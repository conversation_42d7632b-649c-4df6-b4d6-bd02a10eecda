.p-button,
.p-inputtext,
.btn {
  box-shadow: none !important;
  transition: .5s !important;
}

.p-button {
    border-radius: 5px !important;
    &:hover {
        transform: translateY(-2px);
    }
}
.p-button-rounded {
  border-radius: 50% !important;
}

.p-breadcrumb {
  padding: 0 !important;
  margin: 1.5rem;
  border: none;
  .p-breadcrumb-list {
    margin: 0 !important;
    padding-left: 0;
    background: #ff484201 !important;
    .p-menuitem-link {
      text-decoration: none;
    }
  }
}
.p-toolbar {
  padding: 0;
  background: transparent;
  border: none;
}



.p-primary-button button {
  background: #5900ff;
  border-color: #5900ff;
}

.p-confirm-popup {
  z-index: 100000 !important;
}