import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { RootLayoutComponent } from './layouts/root-layout/root-layout.component';
import { HubLayoutComponent } from './layouts/hub-layout/hub-layout.component';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';
import { HubHeaderComponent } from './layouts/hub-header/hub-header.component';
import { HubSidebarComponent } from './layouts/hub-sidebar/hub-sidebar.component';
import { PrimengModule } from './shared/modules/primeng.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { AngularFireModule } from '@angular/fire/compat';
import { AngularFireAuthModule } from '@angular/fire/compat/auth';
import { AngularFireStorageModule } from '@angular/fire/compat/storage';
import { AngularFirestoreModule } from '@angular/fire/compat/firestore';
import { AngularFireDatabaseModule } from '@angular/fire/compat/database';
import { environment } from './environments/environment';
import { PublicLayoutComponent } from './layouts/public-layout/public-layout.component';
import { PublicHeaderComponent } from './layouts/public-header/public-header.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { CreateAccountComponent } from './pages/auth/create-account/create-account.component';

@NgModule({
  declarations: [
    AppComponent,
    RootLayoutComponent,
    HubLayoutComponent,
    AuthLayoutComponent,
    AdminLayoutComponent,
    HubHeaderComponent,
    HubSidebarComponent,
    PublicLayoutComponent,
    PublicHeaderComponent,
    CreateAccountComponent,
  ],
  imports: [
    AngularFireModule.initializeApp(environment.firebaseConfig),
    AngularFireAuthModule,
    AngularFirestoreModule,
    AngularFireStorageModule,
    AngularFireDatabaseModule,
    GoogleMapsModule,
    BrowserModule,
    AppRoutingModule,
    PrimengModule,
    BrowserAnimationsModule
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
