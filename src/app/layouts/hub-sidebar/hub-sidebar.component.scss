.sidebar-container {
  .logo {
    width: 100%;
    text-align: center;
    padding: 14px 0;
    img {
      height: 42px;
    }
  }
  .sidebar-divider-hr {
    margin: 0 0 10px 0;
    border-top: none;
    height: 1px;
    background-image: linear-gradient(
      90deg,
      var(--secondary-bg),
      var(--text-100),
      var(--secondary-bg)
    );
  }
  .menu-box {
    width: calc(100% - 12px);
    margin: 0 6px;
  }
}

:host ::ng-deep {
  .p-menuitem-link-active {
    border-left: 3px solid #5900ff !important;
    .p-menuitem-text,
    .p-menuitem-icon {
      color: #5900ff !important;
    }
  }
  .p-panelmenu-header-content {
    background: var(--secondary-bg);
    border-color: var(--secondary-bg);
    margin: 1px 0;

    .p-panelmenu-header-action {
      color: var(--text-100);
      text-decoration: none;
      font-weight: 500;
      font-size: 16px !important;
      font-family: "Roboto", sans-serif !important;
    }
  }
  .p-highlight .p-panelmenu-header-content {
    background: var(--accent);
    border-color: var(--accent);
    hadow: 0 1.5px 9px rgba(0, 0, 0, 0.08);
    bordbox-ser-radius: 6px;
  }
  .p-panelmenu-content {
    background: transparent;
    border-color: transparent;
  }

  .p-submenu-icon {
    right: 0;
    position: absolute;
    margin-top: -7px;
  }
  .p-menuitem-link {
    text-decoration: none;
  }
  .p-submenu-list {
    padding-left: 0.5rem;
  }

  .single-menu-item {
    position: relative;
    color: var(--text-100);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px !important;
    font-family: "Roboto", sans-serif !important;
    border-radius: 6px;
    height: 66px;
    margin: 1px 0;
    transition: .5s;
    border: 1px solid transparent;
    a {
        display: flex;
        vertical-align: middle;
        align-items: center;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        position: absolute;
        padding: 1.25rem .25rem 1.25rem 1.6rem;
        text-decoration: none;
        color: var(--text-100);
        .bi, .pi {
            margin-right: 10px;
        }
    }
    &:hover {
        box-shadow: 0 1.5px 9px rgba(0, 0, 0, 0.08);
        background: var(--accent);
        border-color: var(--accent);
    }
  }
}

.active-single-link {
  border-left: 3px solid #5900ff !important;
  i,
  span {
    color: #5900ff !important;
  }
}