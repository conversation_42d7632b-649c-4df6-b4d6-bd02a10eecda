import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { MenuItem } from 'primeng/api';
import { ThemeService } from 'src/app/shared/services/theme.service';

@Component({
  selector: 'app-hub-sidebar',
  templateUrl: './hub-sidebar.component.html',
  styleUrls: ['./hub-sidebar.component.scss'],
})
export class HubSidebarComponent implements OnInit {
  HUBMngmtItems: MenuItem[] = [];

  constructor(
    public themeService: ThemeService,
    private afAuth: AngularFireAuth,
    private afs: AngularFirestore,
  ) {
    this.HUBMngmtItems = [
      {
        label: 'Branches',
        icon: 'bi bi-diagram-3',
        routerLink: 'branches',
        routerLinkActiveOptions: 'active-link'
      },
      /*{
        separator: true,
      },*/
      {
        label: 'Offices',
        icon: 'bi bi-door-open',
        routerLink: 'offices',
        routerLinkActiveOptions: 'active-link'
      },
      {
        label: 'Meeting Rooms',
        icon: 'bi bi-door-closed',
        routerLink: 'meeting-rooms',
        routerLinkActiveOptions: 'active-link'
      },
      {
        label: 'Resources',
        icon: 'bi bi-box-seam-fill',
        items: [
          {
            label: 'Resources',
            icon: 'bi bi-easel2-fill',
            routerLink: 'resources',
            routerLinkActiveOptions: 'active-link'
          },
          {
            label: 'Inventory',
            icon: 'bi bi-boxes',
            routerLink: 'inventory',
            routerLinkActiveOptions: 'active-link'
          },
          {
            label: 'Pantry',
            icon: 'bi bi-cup-hot',
            routerLink: 'pantry',
            routerLinkActiveOptions: 'active-link'
          },
          {
            label: 'Supliers',
            icon: 'bi bi-truck',
            routerLink: 'supliers',
            routerLinkActiveOptions: 'active-link'
          }
        ],
      }
    ];
  }

  currentUser: any;

  ngOnInit() {
    this.getCurrentUser();
  }

  getCurrentUser() {
    this.afAuth.onAuthStateChanged((user) => {
      if (user) {
        this.afs.collection('users').doc(user.uid).valueChanges().subscribe((userData) => {
          this.currentUser = userData;
          console.log('User data from Firestore', this.currentUser);
        });
      } else {
        this.currentUser = null;
        this.currentUser = null;
      }
    });
  }
}
