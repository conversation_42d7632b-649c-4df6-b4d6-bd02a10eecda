<div
  class="main-container d-flex"
  [ngClass]="{
    light: themeService.mode === 'light',
    'dark-blue': themeService.mode === 'dark'
  }"
>
  <div class="sidebar shadow-sm">
    <app-hub-sidebar></app-hub-sidebar>
  </div>
  <div class="d-flex col m-0">
    <div class="main-content">
      <div class="hub-header">
        <app-hub-header></app-hub-header>
      </div>
      <div class="hub-outlet">
        <router-outlet></router-outlet>
      </div>
    </div>
    <!--<div class="messages-container shadow">
        <div class="m-headr">

        </div>
        <div class="m-content">

        </div>
    </div>-->

    <div #canvas class="custom-offcanvas">
      <div #handle class="offcanvas-header p-2" style="min-width: 400px; border-bottom: 1px solid #eee; height: 59px;">
        <div class="d-flex">
          <button
            type="button"
            class="btn btn-danger me-3"
            (click)="openCanvas()"
            aria-label="Close"
          >
            <i class="bi bi-chat-right-text"></i>
          </button>
          <h5 class="offcanvas-title">Aymen Salhi</h5>
        </div>

        <button
          type="button"
          class="btn-close"
          (click)="closeCanvas()"
          aria-label="Close"
        ></button>
      </div>
      <div class="offcanvas-body" style="min-width: 400px">
        <div class="contact-list">cdsd ddcsd dcsdc</div>
        <div class="msg-container">dcsdcsdcsdcsdc</div>
      </div>
    </div>

    <!--<div #handle class="canvas-handle" (click)="openCanvas()">
      <button>☰</button>
    </div>-->
  </div>
</div>
