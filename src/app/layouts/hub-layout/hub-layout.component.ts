import {
  Component,
  ElementRef,
  HostListener,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { ThemeService } from 'src/app/shared/services/theme.service';

@Component({
  selector: 'app-hub-layout',
  templateUrl: './hub-layout.component.html',
  styleUrls: ['./hub-layout.component.scss'],
})
export class HubLayoutComponent {
  @ViewChild('canvas') canvasRef!: ElementRef;
  @ViewChild('handle') handleRef!: ElementRef;

  constructor(private renderer: Renderer2, public themeService: ThemeService) {}

  ngOnInit(): void {}

  openCanvas(): void {
    this.renderer.addClass(this.canvasRef.nativeElement, 'expanded');
  }

  closeCanvas(): void {
    this.renderer.removeClass(this.canvasRef.nativeElement, 'expanded');
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const clickedInsideCanvas = this.canvasRef.nativeElement.contains(
      event.target
    );
    const clickedOnHandle = this.handleRef.nativeElement.contains(event.target);
    if (!clickedInsideCanvas && !clickedOnHandle) {
      this.closeCanvas();
    }
  }
}
