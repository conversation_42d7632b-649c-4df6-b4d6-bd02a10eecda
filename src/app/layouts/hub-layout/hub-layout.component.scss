.main-container {
  width: 100vw;
  height: 100vh;
  background: var(--main-bg);
  overflow: hidden;
  .sidebar {
    width: 250px;
    height: 100vh;
    overflow-y: auto;
    margin: 0;
    background: var(--secondary-bg);
    background: hsla(0, 0%, 100%, 0.8) !important;
    backdrop-filter: saturate(200%) blur(30px);
    border-radius: 0;
  }
  .main-content {
    max-width: 100%;
    margin: 0 4px;
    width: 100%;
    min-height: 100vh;
    padding: 0px;
    background: var(--main-bg);
    overflow-y: auto;
    position: relative;
    padding-right: 58px;
    .hub-header {
      top: 0px;
      position: sticky;
      margin: 0;
      z-index: 1042;
    }
    .hub-outlet {
      width: 100%;
    }
  }

  .messages-container {
    width: 58px;
    height: 100vh;
    border-left: 1px solid #eee;
    background: #fff;
    top: 0;
    right: 0;
    bottom: 0;
    position: fixed;

    .m-headr {
      width: 58px;
      height: 48px;
      border-bottom: 1px solid #eee;
    }
  }
}

/******************************/
.custom-offcanvas {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  background-color: #fff;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
  transition: width 0.3s ease;
  overflow: hidden;
  z-index: 1055;
  width: 57px;
}

.custom-offcanvas.expanded {
  width: 400px;
}

.canvas-handle {
  position: fixed;
  top: 0;
  right: 0;
  width: 60px;
  height: 100vh;
  z-index: 1060;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.offcanvas-body {
  padding: 0;
  height: calc(100vh - 58px);
  display: flex;
  flex-direction: row;

  .contact-list {
    height: calc(100vh - 58px);
    width: 58px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #eee;
    max-width: 58px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .msg-container {
    width: 342px;
    height: calc(100vh - 58px);
    overflow-y: auto;
  }
}
