<svg width="1440" height="1024" viewBox="0 0 1440 1024" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g clip-path="url(#clip0_6_900)">
<rect width="1440" height="1024" fill="#f0f2f5"/>
<rect width="1440" height="1024" fill="url(#pattern0)" fill-opacity="0.2"/>
<rect x="794" y="-438.5" width="669" height="872.41" rx="64" transform="rotate(-30 794 -438.5)" fill="url(#paint0_linear_6_900)"/>
<rect x="-201.113" y="479" width="669" height="872.41" rx="64" transform="rotate(45 -201.113 479)" fill="url(#paint1_linear_6_900)"/>
</g>
<defs>
<pattern id="pattern0" patternContentUnits="objectBoundingBox" width="0.01875" height="0.00878906">
<use xlink:href="#image0_6_900" transform="scale(0.000347222 0.000488281)"/>
</pattern>
<linearGradient id="paint0_linear_6_900" x1="1454" y1="466.113" x2="898.521" y2="-289.552" gradientUnits="userSpaceOnUse">
<stop stop-color="#cccccc"/>
<stop offset="1" stop-color="#f7f8ff"/>
</linearGradient>
<linearGradient id="paint1_linear_6_900" x1="458.887" y1="1383.61" x2="-96.5925" y2="627.948" gradientUnits="userSpaceOnUse">
<stop stop-color="#f7f8ff"/>
<stop offset="1" stop-color="#cccccc"/>
</linearGradient>
<clipPath id="clip0_6_900">
<rect width="1440" height="1024" fill="white"/>
</clipPath>
<image id="image0_6_900" width="54" height="18" xlink:href="data:image/png;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/4gHYSUNDX1BST0ZJTEUAAQEAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADb/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/2wBDAQMDAwQDBAgEBAgQCwkLEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBD/wAARCAAkAGwDASIAAhEBAxEB/8QAGAABAQEBAQAAAAAAAAAAAAAAAAgJBgf/xAAqEAAABAUCBgIDAQAAAAAAAAAAAQIDBgcREhMFIQQIFCIjMUJSFWGBFv/EABQBAQAAAAAAAAAAAAAAAAAAAAD/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwDT0AHGzRmnDcqYfVrkQPbqpgZtc8vehKu5CFW0yEe5bgOyATfLTnThuOYkZh/WoY/Ak/djf61ziq2trWfalhP0Ivfy/QpAAAAAAE3zJ51YXgmIHND0KG/9ATVMj3WOcJbVCFF2qYOtbjLY/j+x69KyaMPzWhtuINDVae+ZiizxeRaE9ykJurjM9i2AdkAAAAONmjNOG5Uw+rXIge3VTAza55e9CVdyEKtpkI9y3HkMtOdOG45iRmH9ahj8CT92N/rXOKra2tZ9qWE/Qi9/L9AKQAAABN/OrLaII2hfR9d0NrKUP9RmbuQm7O5w6U7qUVKWmexH/BSAAMr5YSviiZMSNaLoXCXH3ZHMjZWeNai2UtNa2H6MaoAAAAAAyvmhLCJJbRQ/oWtMbptxuXN9/jQo9krVSl5F7FgclktIkgaG9a1qIOGwFr3TYG721UwOcQlW6FK+5eyL+ikAAAAAE386stogjaF9H13Q2spQ/wBRmbuQm7O5w6U7qUVKWmexH/BH8sJXxRMmJGtF0LhLj7sjmRsrPGtRbKWmtbD9GNUAAAAAAAAAAAAAAAAAAAAAAAAAAAAB/9k="/>
</defs>
</svg>
