import { Component } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ThemeService } from 'src/app/shared/services/theme.service';

@Component({
  selector: 'app-hub-header',
  templateUrl: './hub-header.component.html',
  styleUrls: ['./hub-header.component.scss'],
})
export class HubHeaderComponent {
  items: MenuItem[] = [{ label: 'Hub' }, { label: 'Dashboard' }];
  home!: MenuItem;

  constructor(public themeService: ThemeService) {}

  ngOnInit() {
    this.home = { icon: 'pi pi-home', routerLink: '/' };
  }

  switchTheme() {
    if (this.themeService.mode === 'light') {
      this.themeService.mode = 'dark';
    } else {
      this.themeService.mode = 'light';
    }
  }
}
