.hub-header {
    height: 58px;
    display: flex;
    vertical-align: middle;
    align-items: center;
    margin: 0;
    border-radius: 0px;
    width: 100%;
    background: hsla(0,0%,100%,.8)!important;
    backdrop-filter: saturate(200%) blur(30px);
    box-shadow: inset 0 0 1px 1px hsla(0,0%,100%,.9),0 20px 27px 0 rgba(0,0,0,.05)!important;
    display: flex;
    

    .menu-part {
        height: 100%;
        display: flex;
        vertical-align: middle;
        align-items: center;
    }
}