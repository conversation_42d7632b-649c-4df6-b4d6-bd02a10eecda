import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { Router } from '@angular/router';

@Component({
  selector: 'app-branches',
  templateUrl: './branches.component.html',
  styleUrls: ['./branches.component.scss'],
})
export class BranchesComponent implements OnInit {
  branches: any[] = [];
  map!: google.maps.Map;
  markers: google.maps.Marker[] = [];

  constructor(
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth,
    private router: Router
  ) {}

  async ngOnInit() {
    await this.initMap();
    this.getBranches();
  }

  async initMap() {
    const { Map } = (await google.maps.importLibrary(
      'maps'
    )) as google.maps.MapsLibrary;
    this.map = new Map(document.getElementById('map') as HTMLElement, {
      center: { lat: 25.28656798692695, lng: 51.53085437354816 },
      zoom: 12,
    });
  }

  getBranches() {
    this.afAuth.authState.subscribe((user) => {
      if (user || !user) {
        this.afs
          .collection('branches')
          .snapshotChanges()
          .subscribe((changes) => {
            this.branches = [];
            changes.map((change) => {
              const branchData = change.payload.doc.data();
              this.branches.push({
                id: change.payload.doc.id,
                data: change.payload.doc.data(),
              });
              this.addMarker(branchData, change.payload.doc.id);
            });
            console.log(this.branches);
          });
      }
    });
  }

  addMarker(branch: any, branchId: string) {
    const marker = new google.maps.Marker({
      position: { lat: branch.lat, lng: branch.lng },
      map: this.map,
      title: branch.name,
    });

    const infoWindow = new google.maps.InfoWindow({
      content: `<h5>${branch.name}</h5><p>${branch.address}</p> <button id="view-details-${branchId}" class="btn btn-info" routerLink="/branches/${branchId}">View Details</button>`,
    });

    marker.addListener('click', () => {
      infoWindow.open(this.map, marker);

      // Ensure the button click event is registered after the InfoWindow is opened
      setTimeout(() => {
        const detailsButton = document.getElementById(
          `view-details-${branchId}`
        );
        if (detailsButton) {
          detailsButton.addEventListener('click', () => {
            this.router.navigate([`/branches/${branchId}`]);
          });
        }
      }, 100);
    });

    this.markers.push(marker);
  }
}
