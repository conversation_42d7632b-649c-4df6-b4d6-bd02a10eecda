<div class="container-fluid">
  <div class="row mx-0 p-0">
    <div class="col-md-8 px-0">
      <div id="map" style="width: 100%; height: 470px" class="mb-3"></div>
    </div>
    <div class="col branches-list">
      <div class="card card-body border border-secondary mb-3" *ngFor="let branch of branches">
        <h6 class="text-light">{{ branch.data.name }}</h6>
        <small class="text-muted">{{branch.data.address}}</small>
        <div class="text-end">
          <button pButton pRipple type="button" routerLink="/branches/{{branch.id}}" icon="pi pi-chevron-right" iconPos="right" label="Details" class="p-button-sm p-button-raised p-button-text"></button>
        </div>
      </div>
    </div>
  </div>
  F
</div>
