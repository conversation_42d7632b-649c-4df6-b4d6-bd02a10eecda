import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BranchesComponent } from './branches.component';

const routes: Routes = [
  { path: '', component: BranchesComponent },
  {
    path: ':id',
    loadChildren: () =>
      import('./branche-item/branche-item.module').then(
        (m) => m.BrancheItemModule
      ),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BranchesRoutingModule {}
