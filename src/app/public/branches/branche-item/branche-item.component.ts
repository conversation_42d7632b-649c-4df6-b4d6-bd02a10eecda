import { Component, Input } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-branche-item',
  templateUrl: './branche-item.component.html',
  styleUrls: ['./branche-item.component.scss']
})
export class BrancheItemComponent {
  branch: any;

  offices: any[] = [];
  orderedOffices: any[] = [];

  constructor(
    private afs: AngularFirestore,
    private activeRoute: ActivatedRoute
  ) {}

  ngOnInit() {
    this.getOffices();
    this.getBranch();
  }

  async getBranch() {
    await this.activeRoute.params.subscribe((data: any) => {
      this.afs.collection('branches', ref => ref.where('docid', '==', data.id)).valueChanges().subscribe((branches: any) => {
        this.branch = branches[0];
      });
    })
    
  }

  getOffices() {
    this.activeRoute.params.subscribe((data: any) => {
      this.afs
          .collection('offices', (ref) =>
            ref.where('branchId', '==', data.id)
          )
          .valueChanges()
          .subscribe((offices: any) => {
            this.offices = offices.sort((a: any, b: any) => a.officeNumber - b.officeNumber);
            console.log(this.offices);
          });
    })
    
  }
}
