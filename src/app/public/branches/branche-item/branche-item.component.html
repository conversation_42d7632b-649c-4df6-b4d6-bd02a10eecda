
<div class="container">
   <div class="card branch-card card-body">
      <h6 class="text-light">{{branch?.name}}</h6>
      <p class="text-muted m-0">{{branch?.address}}</p>

      <div class="chip-box" *ngIf="branch">
         <p-chip label="{{offices.length}} Offices" styleClass="mr-2 custom-chip"></p-chip>
      </div>
   </div>
   <hr>
   <div class="offices-cards">
      <div class="card m-2 office-card border" routerLink="/branches/{{branch?.docid}}/{{office?.officeId}}" [ngClass]="{'available-card': office.status === 'available', 'occupied-card': office.status === 'occupied'}" *ngFor="let office of offices">
         <h6 class="m-0">
            {{office.officeNumber}}
         </h6>
      </div>
   </div>
</div>