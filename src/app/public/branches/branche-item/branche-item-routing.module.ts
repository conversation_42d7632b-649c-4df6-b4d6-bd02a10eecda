import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BrancheItemComponent } from './branche-item.component';

const routes: Routes = [
  { path: '', component: BrancheItemComponent },
  {
    path: ':id',
    loadChildren: () =>
      import('./office-item/office-item.module').then(
        (m) => m.OfficeItemModule
      ),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BrancheItemRoutingModule {}
