.branch-card {
    background: #1b212b;
    position: relative;

    .chip-box {
        top: 10px;
        right: 10px;
        position: absolute;
    }
}

:host ::ng-deep .p-chip.custom-chip {
    background: var(--primary-color);
    color: var(--primary-color-text);
}

.offices-cards {
    display: flex;
    flex-wrap: wrap;

    .office-card {
        background: transparent;
        width: 100px;
        height: 72px;
        display: flex;
        vertical-align: middle;
        align-items: center;
        justify-content: center;
        text-align: center;
        cursor: pointer;
        h6 {
            font-size: 24px;
        }
    }

    .available-card {
        border-color: #3FB950 !important;
        background: rgba(63, 185, 80, .1);
        color: #3FB950;
        &:hover {
            background: #3FB950;
            color: #fff;
        }
    }
    .occupied-card {
        border-color: red !important;
        background: rgba(255, 0, 0, .1);
        color: #ff0000;
        &:hover {
            background: red;
            color: #fff;
        }
    }
}