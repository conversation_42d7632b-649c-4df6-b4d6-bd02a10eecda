import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RootLayoutComponent } from './layouts/root-layout/root-layout.component';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { HubLayoutComponent } from './layouts/hub-layout/hub-layout.component';
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';
import { PublicLayoutComponent } from './layouts/public-layout/public-layout.component';
import { CreateAccountComponent } from './pages/auth/create-account/create-account.component';

const routes: Routes = [
  {
    path: 'login',
    component: AuthLayoutComponent,
    children: [
      { path: '', loadChildren: () => import('./pages/auth/login/login.module').then(m => m.LoginModule) }
    ]
  },
  {
    path: 'register/:id',
    component: AuthLayoutComponent,
    children: [
      { path: '', loadChildren: () => import('./pages/auth/register-employee/register-employee.module').then(m => m.RegisterEmployeeModule) }
    ]
  },
  {
    path: 'home',
    component: PublicLayoutComponent,
    children: [
      { path: '', loadChildren: () => import('./public/home/<USER>').then(m => m.HomeModule) }
    ]
  },
  {
    path: 'branches',
    component: PublicLayoutComponent,
    children: [
      { path: '', loadChildren: () => import('./public/branches/branches.module').then(m => m.BranchesModule) }
    ]
  },
  { path: 'admin', component: AdminLayoutComponent, children: [ { path: '', loadChildren: () => import('./pages/administrator/administrator.module').then(m => m.AdministratorModule) }]},
  { path: 'hub', component: HubLayoutComponent, children: [ { path: '', loadChildren: () => import('./pages/hub/hub.module').then(m => m.HubModule) }]},
  { path: 'portal', loadChildren: () => import('./pages/portal/portal.module').then(m => m.PortalModule) },
  { path: 'create-account/:token', component: CreateAccountComponent },

  {
    path: '**',
    pathMatch: 'full',
    redirectTo: 'home'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
