import { Component, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { UserService } from './shared/services/user.service';
import { OwnerService } from './shared/services/owner.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnChanges {
  title = 'HUB';
  userId$ = this.ownerService.userId$;

  constructor(
    private userService: UserService,
    public ownerService: OwnerService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes) {
      console.log('0000000000', this.ownerService.userId);
      this.userId$.subscribe((id) => {
        console.log('User ID:', id); // Always reflects current value
      });
    }
  }

  ngOnInit() {
    this.userService.getCurrentUser();
    this.ownerService.initializeUserId(); // Fetch ID once on app load
  }
}
