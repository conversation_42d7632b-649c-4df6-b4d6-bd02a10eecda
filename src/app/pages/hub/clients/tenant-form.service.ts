import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Tenant, Contract, Office } from './tenant.model';

@Injectable({
  providedIn: 'root',
})
export class TenantFormService {
  constructor(private fb: FormBuilder) {}

  // Create form for new tenant/contract creation with office selection
  createTenantContractForm(
    selectedOffice?: Office,
    contract?: Contract
  ): FormGroup {
    return this.fb.group({
      // Basic Info (step 0 - now first step)
      basicInfo: this.fb.group({
        fullName: [
          contract?.tenant?.basicInfo?.fullName || '',
          Validators.required,
        ],
        email: [
          contract?.tenant?.basicInfo?.email || '',
          [Validators.required, Validators.email],
        ],
        phone: [contract?.tenant?.basicInfo?.phone || '', Validators.required],
        companyName: [contract?.tenant?.basicInfo?.companyName || ''],
        jobTitle: [contract?.tenant?.basicInfo?.jobTitle || ''],
        idNumber: [contract?.tenant?.basicInfo?.idNumber || ''],
        taxId: [contract?.tenant?.basicInfo?.taxId || ''],
        registrationLinkMethod: [
          contract?.tenant?.basicInfo?.registrationLinkMethod ||
            contract?.tenant?.legal?.registrationLinkMethod ||
            'Email',
          Validators.required,
        ],
      }),

      // Contact Preferences (step 0 continued)
      contactPreferences: this.fb.group({
        preferredCommunication: [
          contract?.tenant?.contactPreferences?.preferredCommunication ||
            'Email',
        ],
        emergencyContact: this.fb.group({
          name: [
            contract?.tenant?.contactPreferences?.emergencyContact?.name || '',
          ],
          phone: [
            contract?.tenant?.contactPreferences?.emergencyContact?.phone || '',
          ],
        }),
      }),

      // Office selection (step 1 - now second step)
      officeSelection: this.fb.group({
        selectedOfficeId: [
          selectedOffice?.officeId || contract?.officeId || '',
          // Remove required validation for now since office selection is optional for drafts
        ],
        officeNumber: [selectedOffice?.officeNumber || ''],
        branchId: [selectedOffice?.branchId || ''],
        selectedBranchId: [''], // For branch selection
      }),

      // Lease Details (step 2)
      leaseDetails: this.fb.group({
        terms: this.fb.group({
          startDate: [
            contract?.tenant?.leaseDetails?.terms?.startDate ||
              contract?.startDate ||
              '',
            Validators.required,
          ],
          endDate: [
            contract?.tenant?.leaseDetails?.terms?.endDate ||
              contract?.endDate ||
              '',
            Validators.required,
          ],
          rentAmount: [
            contract?.tenant?.leaseDetails?.terms?.rentAmount ||
              contract?.rentAmount ||
              0,
            [Validators.required, Validators.min(1)],
          ],
          currency: [
            contract?.tenant?.leaseDetails?.terms?.currency ||
              contract?.currency ||
              'USD',
          ],
          securityDeposit: [
            contract?.tenant?.leaseDetails?.terms?.securityDeposit ||
              contract?.securityDeposit ||
              0,
          ],
          paymentDueDay: [
            contract?.tenant?.leaseDetails?.terms?.paymentDueDay ||
              contract?.paymentDueDay ||
              1,
          ],
          lateFeePolicy: [
            contract?.tenant?.leaseDetails?.terms?.lateFeePolicy ||
              contract?.lateFeePolicy ||
              '',
          ],
        }),
        paymentMethod: this.fb.group({
          type: [
            contract?.tenant?.leaseDetails?.paymentMethod?.type ||
              contract?.paymentMethod?.type ||
              'Bank Transfer',
          ],
          billingCycle: [
            contract?.tenant?.leaseDetails?.paymentMethod?.billingCycle ||
              contract?.paymentMethod?.billingCycle ||
              'Monthly',
          ],
          autoPay: [
            contract?.tenant?.leaseDetails?.paymentMethod?.autoPay ||
              contract?.paymentMethod?.autoPay ||
              false,
          ],
          chequeImage: [
            contract?.tenant?.leaseDetails?.paymentMethod?.chequeImage ||
              contract?.paymentMethod?.chequeImage ||
              '',
          ],
          chequeImagePath: [
            contract?.tenant?.leaseDetails?.paymentMethod?.chequeImagePath ||
              contract?.paymentMethod?.chequeImagePath ||
              '',
          ],
        }),
      }),

      // Additional Services (step 3)
      additionalServices: this.fb.group({
        internet: [
          contract?.tenant?.additionalServices?.internet ||
            contract?.additionalServices?.internet ||
            false,
        ],
        cleaning: [
          contract?.tenant?.additionalServices?.cleaning ||
            contract?.additionalServices?.cleaning ||
            false,
        ],
        meetingRoom: [
          contract?.tenant?.additionalServices?.meetingRoom ||
            contract?.additionalServices?.meetingRoom ||
            false,
        ],
        parking: [
          contract?.tenant?.additionalServices?.parking ||
            contract?.additionalServices?.parking ||
            false,
        ],
        mailHandling: [
          contract?.tenant?.additionalServices?.mailHandling ||
            contract?.additionalServices?.mailHandling ||
            false,
        ],
      }),

      // Legal (step 4 - Review & Confirmation)
      legal: this.fb.group({
        agreementSigned: [
          contract?.tenant?.legal?.agreementSigned ||
            contract?.legal?.agreementSigned ||
            false,
          Validators.requiredTrue,
        ],
        termsAccepted: [
          contract?.tenant?.legal?.termsAccepted ||
            contract?.legal?.termsAccepted ||
            false,
          Validators.requiredTrue,
        ],
        privacyConsent: [
          contract?.tenant?.legal?.privacyConsent ||
            contract?.legal?.privacyConsent ||
            false,
          Validators.requiredTrue,
        ],
      }),

      // Admin Notes
      adminNotes: this.fb.group({
        specialRequests: [
          contract?.tenant?.adminNotes?.specialRequests ||
            contract?.adminNotes?.specialRequests ||
            '',
        ],
        moveInChecklist: this.fb.array(
          (
            contract?.tenant?.adminNotes?.moveInChecklist ||
            contract?.adminNotes?.moveInChecklist ||
            []
          ).map((item: any) => this.fb.control(item))
        ),
      }),
    });
  }

  // Extract contract data from form
  extractContractFromForm(formValue: any): Partial<Contract> {
    return {
      officeId: formValue.officeSelection?.selectedOfficeId,
      startDate: formValue.leaseDetails?.terms?.startDate,
      endDate: formValue.leaseDetails?.terms?.endDate,
      rentAmount: formValue.leaseDetails?.terms?.rentAmount,
      currency: formValue.leaseDetails?.terms?.currency,
      securityDeposit: formValue.leaseDetails?.terms?.securityDeposit,
      paymentDueDay: formValue.leaseDetails?.terms?.paymentDueDay,
      lateFeePolicy: formValue.leaseDetails?.terms?.lateFeePolicy,
      paymentMethod: formValue.leaseDetails?.paymentMethod,
      additionalServices: formValue.additionalServices,
      legal: formValue.legal,
      adminNotes: formValue.adminNotes,
      // Include tenant data for complete contract storage
      tenant: this.extractTenantFromForm(formValue) as Tenant,
    };
  }

  // Extract tenant data from form
  extractTenantFromForm(formValue: any): Partial<Tenant> {
    const tenantData = {
      basicInfo: formValue.basicInfo,
      contactPreferences: formValue.contactPreferences,
      leaseDetails: {
        property: {
          buildingId: formValue.officeSelection?.branchId || '',
          floor: '', // You can add floor selection if needed
          deskNumber: formValue.officeSelection?.officeNumber?.toString() || '',
        },
        terms: formValue.leaseDetails?.terms,
        paymentMethod: formValue.leaseDetails?.paymentMethod,
      },
      additionalServices: formValue.additionalServices,
      legal: formValue.legal,
      adminNotes: formValue.adminNotes,
    };
    console.log('Extracted tenant data:', tenantData); // Debug log
    return tenantData;
  }

  // Validation methods
  isStepValid(form: FormGroup, step: number): boolean {
    switch (step) {
      case 0: // Basic Info
        return form.get('basicInfo')?.valid || false;
      case 1: // Office Selection
        return true; // Make office selection optional for drafts
      case 2: // Lease Details
        return form.get('leaseDetails')?.valid || false;
      case 3: // Additional Services
        return true; // Optional step
      case 4: // Review & Confirm
        return form.get('legal')?.valid || false;
      default:
        return false;
    }
  }

  // Draft-specific validation (more lenient)
  isDraftSaveValid(form: FormGroup): boolean {
    const basicInfo = form.get('basicInfo');
    const fullName = basicInfo?.get('fullName')?.value;

    // For draft save, we only require full name
    return !!(fullName && fullName.trim());
  }

  // Check if basic tenant info is complete for draft saving
  hasMinimumDraftData(form: FormGroup): boolean {
    const basicInfo = form.get('basicInfo');
    const fullName = basicInfo?.get('fullName')?.value;
    const email = basicInfo?.get('email')?.value;

    return !!(fullName?.trim() && email?.trim());
  }
}
