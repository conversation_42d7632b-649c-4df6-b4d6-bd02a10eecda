// Table improvements
::ng-deep {
  .p-datatable {
    .p-datatable-header {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
    }

    .p-datatable-tbody > tr {
      transition: background-color 0.3s ease;

      &:hover {
        background: #f8f9fa !important;
      }
    }
  }
}

// Button improvements
.p-button-success {
  background: #28a745;
  border-color: #28a745;

  &:hover {
    background: #218838;
    border-color: #1e7e34;
  }
}

// Warning elements
.text-warning {
  color: #856404 !important;
  font-weight: 600;

  i {
    margin-right: 0.5rem;
  }
}
.wheel-button-container {
  position: relative;
  display: inline-block;
}

/* Wheel button animation */
.wheel-button-container button.p-button-rounded {
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.wheel-button-container button.p-button-rounded.rotate {
  transform: rotate(90deg);
  background-color: var(--primary-color) !important;
}

.hidden-buttons {
  display: none;
  position: absolute;
  top: 50%;
  left: -250px;
  transform: translateY(-50%);
  z-index: 1;
  flex-direction: column;
  align-items: flex-start;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  transform: translateY(-50%) translateX(-10px);
}

.hidden-buttons.show {
  display: flex;
  opacity: 1;
  transform: translateY(-50%) translateX(0);
}

.hidden-buttons button {
  display: block;
  margin: 5px 0;
  transform: scale(0.9);
  opacity: 0;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.hidden-buttons.show button {
  transform: scale(1);
  opacity: 1;
}

/* Staggered animation for buttons */
.hidden-buttons.show button:first-child {
  transition-delay: 0.1s;
}

.hidden-buttons.show button:last-child {
  transition-delay: 0.2s;
}

.deleted-contracts-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1055;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
}

.modal-content {
  position: relative;
  width: 80%;
  max-width: 600px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h5 {
  margin: 0;
  font-size: 1.25rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #343a40;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex-grow: 1;
}

.modal-body ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.modal-body li {
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body li:last-child {
  border-bottom: none;
}

.contract-number {
  font-weight: 500;
  color: #495057;
}

.contract-status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-active {
  background-color: #e6f7ee;
  color: #28a745;
}

.status-inactive {
  background-color: #fff3cd;
  color: #d39e00;
}

.status-terminated {
  background-color: #f8d7da;
  color: #dc3545;
}

.modal-footer {
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}
.deleted-contracts-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 10px; /* Adds vertical spacing between rows */
}

.deleted-contracts-table th,
.deleted-contracts-table td {
  padding: 10px 15px; /* Adds padding inside cells */
  text-align: left;
}

.deleted-contracts-table th {
  background-color: #f4f4f4;
  font-weight: bold;
}

.deleted-contracts-table tr {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.deleted-contracts-table tr:hover {
  background-color: #f9f9f9;
}

.deleted-contracts-table td {
  border-bottom: 1px solid #ddd;
}

// Enhanced Payment Status Styling
.status-paid {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  font-weight: 600;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid #c3e6cb;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.85rem;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  box-shadow: 0 2px 4px rgba(21, 87, 36, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: "✓";
    font-weight: bold;
    font-size: 0.9rem;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(21, 87, 36, 0.15);
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.5s;
  }

  &:hover::after {
    left: 100%;
  }
}

.status-soon {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  font-weight: 600;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid #ffeaa7;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.85rem;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  box-shadow: 0 2px 4px rgba(133, 100, 4, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: "⏰";
    font-size: 0.9rem;
    animation: pulse 2s infinite;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(133, 100, 4, 0.15);
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.5s;
  }

  &:hover::after {
    left: 100%;
  }
}

.status-overdue {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  font-weight: 600;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid #f5c6cb;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.85rem;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  box-shadow: 0 2px 4px rgba(114, 28, 36, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: "⚠️";
    font-size: 0.9rem;
    animation: shake 1s infinite;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(114, 28, 36, 0.15);
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.5s;
  }

  &:hover::after {
    left: 100%;
  }
}

.status-future {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  color: #495057;
  font-weight: 500;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.85rem;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  box-shadow: 0 2px 4px rgba(73, 80, 87, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: "📅";
    font-size: 0.9rem;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(73, 80, 87, 0.15);
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.5s;
  }

  &:hover::after {
    left: 100%;
  }
}

.status-na {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  font-weight: 500;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid #e9ecef;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.85rem;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-style: italic;
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: "❓";
    font-size: 0.9rem;
    opacity: 0.7;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.15);
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.5s;
  }

  &:hover::after {
    left: 100%;
  }
}

// Animations for status indicators
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

// Special highlighting for overdue payments
.status-overdue {
  animation: fadeInOut 3s infinite;
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

// Enhanced tooltips for payment status
::ng-deep {
  .p-tooltip {
    .p-tooltip-text {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: #ecf0f1;
      border-radius: 8px;
      padding: 12px 16px;
      font-size: 13px;
      line-height: 1.4;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
      border: 1px solid rgba(255, 255, 255, 0.1);
      max-width: 300px;

      strong {
        color: #fff;
        display: inline-block;
        margin-bottom: 2px;
      }
    }

    .p-tooltip-arrow {
      border-top-color: #2c3e50;
    }

    &.p-tooltip-right .p-tooltip-arrow {
      border-right-color: #2c3e50;
    }

    &.p-tooltip-left .p-tooltip-arrow {
      border-left-color: #2c3e50;
    }

    &.p-tooltip-bottom .p-tooltip-arrow {
      border-bottom-color: #2c3e50;
    }
  }
}

// Status badge hover effects
.status-paid,
.status-soon,
.status-overdue,
.status-future,
.status-na {
  cursor: help;
  user-select: none;
}

// Responsive adjustments for mobile
@media (max-width: 768px) {
  .status-paid,
  .status-soon,
  .status-overdue,
  .status-future,
  .status-na {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;

    &::before {
      font-size: 0.8rem;
    }
  }
}

// Print styles
@media print {
  .status-paid,
  .status-soon,
  .status-overdue,
  .status-future,
  .status-na {
    background: none !important;
    border: 1px solid #000;
    color: #000;

    &::before {
      display: none;
    }
  }
}

// Payment Timeline Modal Styles
.payment-timeline-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1055;
}

.payment-timeline-content {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
}

.timeline-loading {
  text-align: center;
  padding: 2rem;
  color: #6c757d;

  i {
    font-size: 1.5rem;
    margin-right: 0.5rem;
  }
}

.no-timeline {
  text-align: center;
  padding: 2rem;
  color: #6c757d;

  i {
    font-size: 1.5rem;
    margin-right: 0.5rem;
  }
}

// Custom Timeline Styles
::ng-deep .custom-timeline {
  .p-timeline-event-marker {
    border: none !important;
    background: none !important;
    padding: 0 !important;
  }

  .custom-marker {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    &.marker-paid {
      background: linear-gradient(135deg, #28a745, #20c997);
    }

    &.marker-soon {
      background: linear-gradient(135deg, #ffc107, #fd7e14);
      animation: pulse 2s infinite;
    }

    &.marker-overdue {
      background: linear-gradient(135deg, #dc3545, #e74c3c);
      animation: shake 1s infinite;
    }

    &.marker-upcoming {
      background: linear-gradient(135deg, #6c757d, #495057);
    }

    &.marker-default {
      background: linear-gradient(135deg, #6c757d, #495057);
    }
  }

  .p-timeline-event-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
  }

  .p-timeline-event-separator {
    background: #e9ecef !important;
  }
}

.timeline-event-content {
  padding: 1.5rem;
}

.timeline-date {
  margin-bottom: 1rem;

  h6 {
    margin: 0 0 0.25rem 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .timeline-period {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .due-date {
    margin-top: 0.25rem;

    small {
      color: #dc3545;
      font-weight: 500;
    }
  }
}

.timeline-details {
  .payment-status {
    display: inline-block;
    margin-bottom: 0.75rem;

    &.status-paid {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      color: #155724;
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.9rem;
      border: 1px solid #c3e6cb;
    }

    &.status-soon {
      background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      color: #856404;
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.9rem;
      border: 1px solid #ffeaa7;
    }

    &.status-overdue {
      background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
      color: #721c24;
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.9rem;
      border: 1px solid #f5c6cb;
    }

    &.status-upcoming {
      background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
      color: #495057;
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.9rem;
      border: 1px solid #dee2e6;
    }
  }

  .payment-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 0.75rem;
  }

  .payment-method,
  .transaction-info,
  .cheque-info {
    margin-bottom: 0.5rem;

    small {
      color: #6c757d;
      font-size: 0.85rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      i {
        color: #17a2b8;
        font-size: 0.9rem;
      }
    }
  }

  .payment-notes {
    small {
      color: #6c757d;
      font-style: italic;
      font-size: 0.8rem;
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;

      i {
        color: #ffc107;
        font-size: 0.9rem;
        margin-top: 0.1rem;
        flex-shrink: 0;
      }
    }
  }
}

// Responsive design for timeline
@media (max-width: 768px) {
  .payment-timeline-content {
    width: 95%;
    max-height: 95vh;
  }

  .timeline-event-content {
    padding: 1rem;
  }

  ::ng-deep .custom-timeline {
    .custom-marker {
      width: 2rem;
      height: 2rem;
      font-size: 1rem;
    }

    .p-timeline-event-content {
      margin-left: 1rem;
    }
  }

  .timeline-date h6 {
    font-size: 1rem;
  }

  .timeline-details .payment-amount {
    font-size: 1.1rem;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .status-paid {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    color: #d4edda;
    border-color: #28a745;
  }

  .status-soon {
    background: linear-gradient(135deg, #d39e00 0%, #b8860b 100%);
    color: #fff3cd;
    border-color: #ffc107;
  }

  .status-overdue {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    color: #f8d7da;
    border-color: #dc3545;
  }

  .status-future {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    color: #e9ecef;
    border-color: #6c757d;
  }

  .status-na {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    color: #adb5bd;
    border-color: #6c757d;
  }
}
