import { Injectable } from '@angular/core';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { Observable, from } from 'rxjs';
import { finalize, switchMap } from 'rxjs/operators';

export interface ChequeImageUploadResult {
  downloadURL: string;
  filePath: string;
  fileName: string;
}

@Injectable({
  providedIn: 'root',
})
export class ChequeImageService {
  constructor(
    private storage: AngularFireStorage,
    private afAuth: AngularFireAuth
  ) {}

  /**
   * Upload a cheque image to Firebase Storage
   * @param file The image file to upload
   * @param contractId The contract ID (for organizing files)
   * @returns Observable that emits the download URL when upload is complete
   */
  uploadChequeImage(
    file: File,
    contractId: string
  ): Observable<ChequeImageUploadResult> {
    return this.afAuth.authState.pipe(
      switchMap((user) => {
        if (!user) {
          throw new Error('User not authenticated');
        }

        // Validate file
        this.validateImageFile(file);

        // Create unique file path
        const timestamp = Date.now();
        const fileExtension = file.name.split('.').pop();
        const fileName = `cheque_${contractId}_${timestamp}.${fileExtension}`;
        const filePath = `cheque-images/${user.uid}/${fileName}`;

        // Create file reference
        const fileRef = this.storage.ref(filePath);

        // Set metadata
        const metadata = {
          contentType: file.type,
          contentDisposition: `inline; filename="${fileName}"`,
          customMetadata: {
            contractId: contractId,
            uploadedBy: user.uid,
            uploadDate: new Date().toISOString(),
          },
        };

        // Upload file
        const uploadTask = this.storage.upload(filePath, file, metadata);

        // Return download URL when upload completes
        return uploadTask
          .snapshotChanges()
          .pipe(
            finalize(() => {
              // This ensures we get the download URL after upload completes
            })
          )
          .pipe(
            switchMap(() => fileRef.getDownloadURL()),
            switchMap((downloadURL) => {
              return from(
                Promise.resolve({
                  downloadURL,
                  filePath,
                  fileName,
                } as ChequeImageUploadResult)
              );
            })
          );
      })
    );
  }

  /**
   * Delete a cheque image from Firebase Storage
   * @param filePath The storage path of the file to delete
   * @returns Observable that completes when deletion is done
   */
  deleteChequeImage(filePath: string): Observable<void> {
    const fileRef = this.storage.ref(filePath);
    return from(fileRef.delete());
  }

  /**
   * Get the download URL for a cheque image
   * @param filePath The storage path of the file
   * @returns Observable that emits the download URL
   */
  getChequeImageURL(filePath: string): Observable<string> {
    const fileRef = this.storage.ref(filePath);
    return fileRef.getDownloadURL();
  }

  /**
   * Validate that the uploaded file is a valid image
   * @param file The file to validate
   * @throws Error if file is invalid
   */
  private validateImageFile(file: File): void {
    // Check file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error(
        'Invalid file type. Please select a PNG, JPG, or JPEG image.'
      );
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new Error('File too large. Please select a file smaller than 5MB.');
    }

    // Check if it's actually an image by trying to create an image element
    // This is done asynchronously, so we'll do basic validation here
    if (file.size === 0) {
      throw new Error('File is empty.');
    }
  }

  /**
   * Compress an image file before upload (optional utility method)
   * @param file The image file to compress
   * @param quality Compression quality (0-1)
   * @param maxWidth Maximum width in pixels
   * @param maxHeight Maximum height in pixels
   * @returns Promise that resolves to compressed file
   */
  compressImage(
    file: File,
    quality: number = 0.8,
    maxWidth: number = 800,
    maxHeight: number = 600
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        // Set canvas size
        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new Error('Failed to compress image'));
            }
          },
          'image/jpeg',
          quality
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));

      // Create object URL for the image
      img.src = URL.createObjectURL(file);
    });
  }
}
