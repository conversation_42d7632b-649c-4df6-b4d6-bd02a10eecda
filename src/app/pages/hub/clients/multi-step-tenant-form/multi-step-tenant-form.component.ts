import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Observable, Subject, takeUntil, firstValueFrom } from 'rxjs';
import { MessageService } from 'primeng/api';

import { TenantFormService } from '../tenant-form.service';
import { ContractService } from '../contract.service';
import {
  ChequeImageService,
  ChequeImageUploadResult,
} from '../cheque-image.service';
import { Contract, Office } from '../tenant.model';

@Component({
  selector: 'app-multi-step-tenant-form',
  templateUrl: './multi-step-tenant-form.component.html',
  styleUrls: ['./multi-step-tenant-form.component.scss'],
})
export class MultiStepTenantFormComponent
  implements OnInit, OnDestroy, OnChanges
{
  @Input() visible: boolean = false;
  @Input() selectedOffice?: Office; // Pre-selected office (from offices module)
  @Input() contractId?: string; // For editing existing draft
  @Input() tenantData?: any; // For editing existing tenants
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() contractCreated = new EventEmitter<string>();
  @Output() contractFinalized = new EventEmitter<string>();
  @Output() tenantUpdated = new EventEmitter<any>(); // For tenant updates

  form: FormGroup;
  currentStep: number = 0;
  loading: boolean = false;
  saving: boolean = false;

  currentContract?: Contract;
  availableOffices$: Observable<Office[]>;
  branches$: Observable<any[]>;
  selectedBranchId: string = '';

  // Track if we're editing an existing tenant
  isEditingTenant: boolean = false;
  originalTenantId?: string;

  // Cheque image handling
  selectedChequeImage: string | null = null; // Display URL for preview
  selectedChequeFile: File | null = null;
  chequeImageUploadResult: ChequeImageUploadResult | null = null; // Firebase storage result
  isUploadingChequeImage: boolean = false;

  private destroy$ = new Subject<void>();

  steps = [
    { label: 'Basic Info', icon: 'pi pi-user', key: 'basic' },
    { label: 'Select Office', icon: 'pi pi-building', key: 'office' },
    { label: 'Lease Details', icon: 'pi pi-file', key: 'lease' },
    { label: 'Additional Services', icon: 'pi pi-cog', key: 'services' },
    { label: 'Review & Confirm', icon: 'pi pi-check', key: 'review' },
  ];

  constructor(
    private tenantFormService: TenantFormService,
    private contractService: ContractService,
    private chequeImageService: ChequeImageService,
    private messageService: MessageService,
    private cdr: ChangeDetectorRef
  ) {
    this.form = this.tenantFormService.createTenantContractForm();
    this.branches$ = this.contractService.getUserBranches();
    this.availableOffices$ = this.contractService.getAvailableOffices();
  }

  ngOnInit(): void {
    console.log('ngOnInit called');
    console.log('contractId:', this.contractId);
    console.log('tenantData:', this.tenantData);
    console.log('selectedOffice:', this.selectedOffice);

    this.initializeComponent();
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('=== ngOnChanges called ===');
    console.log('Changes:', changes);
    console.log('Current values:', {
      contractId: this.contractId,
      tenantData: this.tenantData,
      selectedOffice: this.selectedOffice,
    });

    // Check if tenantData changed
    if (changes['tenantData'] && changes['tenantData'].currentValue) {
      console.log('tenantData changed to:', changes['tenantData'].currentValue);
      this.loadTenantForEditing();
    }
    // Check if contractId changed
    else if (changes['contractId']) {
      console.log('contractId change detected:');
      console.log('- Previous value:', changes['contractId'].previousValue);
      console.log('- Current value:', changes['contractId'].currentValue);

      if (changes['contractId'].currentValue) {
        console.log(
          'Loading existing contract with ID:',
          changes['contractId'].currentValue
        );
        this.loadExistingContract();
      } else {
        console.log('contractId set to null/undefined, clearing form');
        this.resetForm();
      }
    }
    // Check if selectedOffice changed
    else if (
      changes['selectedOffice'] &&
      changes['selectedOffice'].currentValue
    ) {
      console.log(
        'selectedOffice changed to:',
        changes['selectedOffice'].currentValue
      );
      this.initializeWithOffice();
    }
  }

  private initializeComponent(): void {
    if (this.contractId) {
      console.log('Loading existing contract');
      this.loadExistingContract();
    } else if (this.tenantData) {
      console.log('Loading tenant for editing');
      this.loadTenantForEditing();
    } else if (this.selectedOffice) {
      console.log('Initializing with office');
      this.initializeWithOffice();
    } else {
      console.log('Creating empty form for new tenant');
      // Clear any leftover state when creating new tenant
      this.currentContract = undefined;
      this.contractId = undefined;
      this.currentStep = 0;
      // Create empty form for new tenant
      this.form = this.tenantFormService.createTenantContractForm();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadExistingContract(): void {
    if (!this.contractId) {
      console.error('loadExistingContract called without contractId');
      return;
    }

    this.loading = true;
    console.log('=== LOADING EXISTING CONTRACT ===');
    console.log('Contract ID:', this.contractId);
    console.log('Component state before loading:', {
      currentContract: this.currentContract,
      currentStep: this.currentStep,
      isEditingTenant: this.isEditingTenant,
    });

    // First, let's debug what's in localStorage
    this.contractService.debugLocalStorageData();

    // Use the contract service to get the draft
    this.contractService
      .getContract(this.contractId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (contract) => {
          console.log('=== CONTRACT SERVICE RESPONSE ===');
          console.log('Contract service returned:', contract);

          if (contract) {
            console.log('Contract found, loading data...');
            this.loadContractData(contract);
          } else {
            console.warn(
              'Contract service returned null/undefined for ID:',
              this.contractId
            );
            console.log('Trying direct localStorage access as fallback...');
            this.tryDirectLocalStorageAccess();
          }
        },
        error: (error) => {
          console.error('Error from contract service:', error);
          console.log('Falling back to direct localStorage access...');
          this.tryDirectLocalStorageAccess();
        },
      });
  }

  private tryDirectLocalStorageAccess(): void {
    try {
      const rawData = localStorage.getItem('draft_contracts');
      console.log('Direct localStorage access - raw data:', rawData);

      if (rawData) {
        const contracts = JSON.parse(rawData);
        console.log('Direct localStorage access - all contracts:', contracts);
        console.log('Looking for contract ID:', this.contractId);

        const foundContract = contracts.find(
          (c: any) => c.id === this.contractId
        );
        console.log(
          'Direct localStorage access - found contract:',
          foundContract
        );

        if (foundContract) {
          this.loadContractData(foundContract);
          return;
        } else {
          console.log(
            'Contract not found in localStorage. Available contract IDs:',
            contracts.map((c: any) => c.id)
          );
        }
      } else {
        console.log('No draft_contracts found in localStorage');
      }
    } catch (error) {
      console.error('Direct localStorage access failed:', error);
    }

    // If we get here, no contract was found
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load contract draft',
    });
    this.loading = false;
  }

  private loadContractData(contract: any): void {
    console.log('=== LOADING CONTRACT DATA ===');
    console.log('Contract received:', contract);
    console.log('Contract structure check:', {
      id: contract?.id,
      tenant: contract?.tenant,
      tenantBasicInfo: contract?.tenant?.basicInfo,
      tenantLeaseDetails: contract?.tenant?.leaseDetails,
      currentStep: contract?.currentStep,
    });

    // Convert date strings to Date objects if needed
    if (typeof contract.createdAt === 'string') {
      contract.createdAt = new Date(contract.createdAt);
    }
    if (typeof contract.updatedAt === 'string') {
      contract.updatedAt = new Date(contract.updatedAt);
    }
    // Handle dates that might be nested in tenant.leaseDetails.terms
    if (contract.tenant?.leaseDetails?.terms) {
      const terms = contract.tenant.leaseDetails.terms;
      if (terms.startDate && typeof terms.startDate === 'string') {
        terms.startDate = new Date(terms.startDate);
      }
      if (terms.endDate && typeof terms.endDate === 'string') {
        terms.endDate = new Date(terms.endDate);
      }
    }
    // Also handle dates at contract level (legacy support)
    if (contract.startDate && typeof contract.startDate === 'string') {
      contract.startDate = new Date(contract.startDate);
    }
    if (contract.endDate && typeof contract.endDate === 'string') {
      contract.endDate = new Date(contract.endDate);
    }

    this.currentContract = contract;
    this.currentStep = contract.currentStep || 0;

    console.log('Creating form with processed contract:', contract);
    console.log('Contract tenant data:', contract.tenant);

    // Create new form with contract data
    const newForm = this.tenantFormService.createTenantContractForm(
      undefined,
      contract
    );

    // Replace the current form
    this.form = newForm;

    console.log('Form created with values:', this.form.value);
    console.log('Form controls after creation:', {
      basicInfo: this.form.get('basicInfo')?.value,
      officeSelection: this.form.get('officeSelection')?.value,
      leaseDetails: this.form.get('leaseDetails')?.value,
      additionalServices: this.form.get('additionalServices')?.value,
    });

    // Handle office selection if present
    if (contract.officeId && contract.officeId !== '') {
      this.handleOfficeSelection(contract.officeId);
    }

    // Load cheque image if present
    const chequeImageData =
      contract.tenant?.leaseDetails?.paymentMethod?.chequeImage ||
      contract.paymentMethod?.chequeImage;
    if (chequeImageData) {
      this.loadChequeImageFromData(chequeImageData);
    }

    // Mark form as pristine since we just loaded existing data
    this.form.markAsPristine();

    // Force change detection to ensure the template updates
    this.cdr.detectChanges();

    setTimeout(() => {
      this.loading = false;
      this.cdr.detectChanges();
    }, 100);
  }

  private handleOfficeSelection(officeId: string): void {
    console.log('handleOfficeSelection called with officeId:', officeId);
    this.contractService
      .getAllOffices()
      .pipe(takeUntil(this.destroy$))
      .subscribe((offices) => {
        console.log('Available offices:', offices);
        const selectedOffice = offices.find(
          (office) => office.officeId === officeId
        );
        console.log('Found selected office:', selectedOffice);
        if (selectedOffice) {
          this.selectedBranchId = selectedOffice.branchId;
          this.updateAvailableOffices();

          // Update the form with the office selection data
          this.form.patchValue({
            officeSelection: {
              selectedOfficeId: officeId,
              selectedBranchId: selectedOffice.branchId,
              branchId: selectedOffice.branchId,
              officeNumber: selectedOffice.officeNumber,
            },
          });

          console.log(
            'Form patched with office selection:',
            this.form.get('officeSelection')?.value
          );
          this.cdr.detectChanges();
        }
      });
  }

  private loadTenantForEditing(): void {
    if (!this.tenantData) {
      console.log('No tenant data provided for editing');
      return;
    }

    console.log('Loading tenant for editing:', this.tenantData);
    this.loading = true;

    // Reset any previous state first
    this.currentContract = undefined;
    this.contractId = undefined;

    // Set edit mode
    this.isEditingTenant = true;
    this.originalTenantId = this.tenantData.id;

    console.log('Edit mode set - isEditingTenant:', this.isEditingTenant);
    console.log('Original tenant ID:', this.originalTenantId);

    // First, get the office ID from the tenant's contract
    this.getTenantOfficeId(this.tenantData)
      .then((officeId) => {
        console.log('Found office ID for tenant:', officeId);

        // Create empty form first
        this.form = this.tenantFormService.createTenantContractForm();

        // Set to basic info step
        this.currentStep = 0;

        // Populate form with tenant data
        this.populateFormWithTenantData(this.tenantData, officeId);

        // Handle office selection if we have an office ID
        if (officeId) {
          setTimeout(() => {
            this.handleOfficeSelection(officeId);
          }, 100);
        }

        // Force UI update
        this.cdr.detectChanges();

        setTimeout(() => {
          this.loading = false;
          this.cdr.detectChanges();
          console.log(
            'Tenant editing form ready. Form values:',
            this.form.value
          );
        }, 200);
      })
      .catch((error) => {
        console.error('Error loading tenant for editing:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load tenant data for editing',
        });
        this.loading = false;
      });
  }

  private async getTenantOfficeId(tenant: any): Promise<string | null> {
    try {
      // Get all contracts and find the one for this tenant
      const contracts = await firstValueFrom(
        this.contractService.getAllContracts()
      );
      const tenantContract = contracts?.find(
        (contract) =>
          contract.tenant?.basicInfo?.email === tenant.basicInfo?.email ||
          contract.tenant?.basicInfo?.fullName === tenant.basicInfo?.fullName
      );

      return tenantContract?.officeId || null;
    } catch (error) {
      console.error('Error getting tenant office ID:', error);
      return null;
    }
  }

  private populateFormWithTenantData(
    tenant: any,
    officeId: string | null
  ): void {
    console.log('Populating form with tenant data:', tenant);

    if (!tenant) return;

    // Handle date conversion
    const convertDate = (dateValue: any) => {
      if (!dateValue) return null;
      if (dateValue.toDate && typeof dateValue.toDate === 'function') {
        return dateValue.toDate();
      }
      if (typeof dateValue === 'string') {
        return new Date(dateValue);
      }
      if (dateValue instanceof Date) {
        return dateValue;
      }
      return null;
    };

    // Build complete form value object
    const formValue = {
      basicInfo: {
        fullName: tenant.basicInfo?.fullName || '',
        email: tenant.basicInfo?.email || '',
        phone: tenant.basicInfo?.phone || '',
        companyName: tenant.basicInfo?.companyName || '',
        jobTitle: tenant.basicInfo?.jobTitle || '',
        idNumber: tenant.basicInfo?.idNumber || '',
        taxId: tenant.basicInfo?.taxId || '',
      },
      contactPreferences: {
        preferredCommunication:
          tenant.contactPreferences?.preferredCommunication || 'Email',
        emergencyContact: {
          name: tenant.contactPreferences?.emergencyContact?.name || '',
          phone: tenant.contactPreferences?.emergencyContact?.phone || '',
        },
      },
      officeSelection: {
        selectedOfficeId: officeId || '',
        officeNumber: '',
        branchId: '',
        selectedBranchId: '',
      },
      leaseDetails: {
        terms: {
          startDate: convertDate(tenant.leaseDetails?.terms?.startDate),
          endDate: convertDate(tenant.leaseDetails?.terms?.endDate),
          rentAmount: tenant.leaseDetails?.terms?.rentAmount || 0,
          currency: tenant.leaseDetails?.terms?.currency || 'USD',
          securityDeposit: tenant.leaseDetails?.terms?.securityDeposit || 0,
          paymentDueDay: tenant.leaseDetails?.terms?.paymentDueDay || 1,
          lateFeePolicy: tenant.leaseDetails?.terms?.lateFeePolicy || '',
        },
        paymentMethod: {
          type: tenant.leaseDetails?.paymentMethod?.type || 'Bank Transfer',
          billingCycle:
            tenant.leaseDetails?.paymentMethod?.billingCycle || 'Monthly',
          autoPay: tenant.leaseDetails?.paymentMethod?.autoPay || false,
          chequeImage: tenant.leaseDetails?.paymentMethod?.chequeImage || '',
          chequeImagePath:
            tenant.leaseDetails?.paymentMethod?.chequeImagePath || '',
        },
      },
      additionalServices: {
        internet: tenant.additionalServices?.internet || false,
        cleaning: tenant.additionalServices?.cleaning || false,
        meetingRoom: tenant.additionalServices?.meetingRoom || false,
        parking: tenant.additionalServices?.parking || false,
        mailHandling: tenant.additionalServices?.mailHandling || false,
      },
      legal: {
        agreementSigned: tenant.legal?.agreementSigned || false,
        termsAccepted: tenant.legal?.termsAccepted || false,
        privacyConsent: tenant.legal?.privacyConsent || false,
      },
      adminNotes: {
        specialRequests: tenant.adminNotes?.specialRequests || '',
        moveInChecklist: tenant.adminNotes?.moveInChecklist || [],
      },
    };

    console.log('Setting form value to:', formValue);

    // Set the entire form value at once
    this.form.setValue(formValue);

    // Load cheque image if present
    if (tenant.leaseDetails?.paymentMethod?.chequeImage) {
      this.loadChequeImageFromData(
        tenant.leaseDetails.paymentMethod.chequeImage
      );
    }

    // Mark form as touched and update validity
    this.form.markAllAsTouched();
    this.form.updateValueAndValidity();

    console.log('Form populated successfully');
    console.log('Basic Info:', this.form.get('basicInfo')?.value);
    console.log('Form valid:', this.form.valid);
  }

  private initializeWithOffice(): void {
    if (this.selectedOffice) {
      this.form = this.tenantFormService.createTenantContractForm(
        this.selectedOffice
      );
      this.selectedBranchId = this.selectedOffice.branchId;
      this.updateAvailableOffices();
      // Don't skip any steps - user still needs to fill basic info first
    }
  }

  onBranchSelect(branchId: string): void {
    this.selectedBranchId = branchId;
    this.updateAvailableOffices();
    // Clear office selection when branch changes
    this.form.patchValue({
      officeSelection: {
        selectedOfficeId: '',
        officeNumber: '',
        branchId: branchId,
        selectedBranchId: branchId,
      },
    });
  }

  updateAvailableOffices(): void {
    if (this.selectedBranchId) {
      this.availableOffices$ = this.contractService.getAvailableOffices(
        this.selectedBranchId
      );
    } else {
      this.availableOffices$ = this.contractService.getAvailableOffices();
    }
  }

  onOfficeSelect(office: Office): void {
    this.form.patchValue({
      officeSelection: {
        selectedOfficeId: office.officeId,
        officeNumber: office.officeNumber,
        branchId: office.branchId,
        selectedBranchId: office.branchId,
      },
    });
  }

  onOfficeSelectById(officeId: string): void {
    this.contractService
      .getAllOffices()
      .pipe(takeUntil(this.destroy$))
      .subscribe((offices) => {
        const selectedOffice = offices.find(
          (office) => office.officeId === officeId
        );
        if (selectedOffice) {
          this.onOfficeSelect(selectedOffice);
        }
      });
  }

  nextStep(): void {
    if (!this.isCurrentStepValid()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields before proceeding',
      });
      return;
    }

    if (this.currentStep < this.steps.length - 1) {
      this.currentStep++;
      // Auto-save draft after successful step progression (but not for tenant editing)
      if (!this.isEditingTenant) {
        this.saveDraft();
      }
    }
  }

  prevStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  async saveDraft(): Promise<void> {
    if (this.saving) return;

    // Don't save drafts when editing existing tenants
    if (this.isEditingTenant) {
      console.log('Skipping draft save - editing existing tenant');
      this.messageService.add({
        severity: 'info',
        summary: 'Info',
        detail: 'Draft save is not available when editing existing tenants',
      });
      return;
    }

    this.saving = true;
    try {
      // Use the draft-specific validation method
      if (!this.tenantFormService.isDraftSaveValid(this.form)) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Validation Error',
          detail: 'Please enter a full name before saving draft',
        });
        this.saving = false;
        return;
      }

      const contractData = this.tenantFormService.extractContractFromForm(
        this.form.value
      );
      console.log('Saving contract data:', contractData); // Debug log
      console.log('Form value being saved:', this.form.value); // Debug log
      console.log('Extracted tenant data:', contractData.tenant); // Debug log

      // Check if we have an existing draft (either from currentContract or contractId)
      const existingContractId = this.currentContract?.id || this.contractId;

      console.log('Checking for existing draft:');
      console.log('- this.currentContract?.id:', this.currentContract?.id);
      console.log('- this.contractId:', this.contractId);
      console.log('- existingContractId:', existingContractId);

      if (!existingContractId) {
        // Create new draft contract (office selection is optional)
        const officeId =
          this.form.value.officeSelection?.selectedOfficeId || '';

        const contractId = await this.contractService.createDraftContract(
          officeId, // Can be empty string for initial drafts
          { ...contractData, currentStep: this.currentStep }
        );

        // Set both properties to ensure future saves update this draft
        this.contractId = contractId;
        this.currentContract = {
          id: contractId,
          ...contractData,
          currentStep: this.currentStep,
        } as any;
        this.contractCreated.emit(contractId);

        console.log('Draft contract created with ID:', contractId);
      } else {
        // Update existing draft
        await this.contractService.saveDraftContract(
          existingContractId,
          contractData,
          this.currentStep
        );

        // Update local contract reference (create if it doesn't exist)
        if (!this.currentContract) {
          this.currentContract = {
            id: existingContractId,
            ...contractData,
            currentStep: this.currentStep,
          } as any;
        } else {
          this.currentContract = {
            ...this.currentContract,
            ...contractData,
            currentStep: this.currentStep,
          };
        }

        // Make sure contractId is also set
        this.contractId = existingContractId;

        console.log('Draft contract updated with ID:', existingContractId);
      }

      this.messageService.add({
        severity: 'success',
        summary: 'Saved',
        detail: `Draft ${
          existingContractId ? 'updated' : 'created'
        } successfully`,
      });
    } catch (error) {
      console.error('Error saving draft:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to save draft: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      });
    } finally {
      this.saving = false;
    }
  }

  async finalizeContract(): Promise<void> {
    console.log(
      'finalizeContract called. isEditingTenant:',
      this.isEditingTenant
    );
    console.log('originalTenantId:', this.originalTenantId);
    console.log('tenantData:', this.tenantData);

    // If we're editing an existing tenant, update it instead of creating a contract
    if (this.isEditingTenant) {
      console.log('Routing to updateExistingTenant');
      await this.updateExistingTenant();
      return;
    }

    console.log('Proceeding with contract creation flow');

    if (!this.currentContract || !this.isFormValid()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please complete all required fields',
      });
      return;
    }

    // Check if office is selected before finalizing
    const officeId = this.form.value.officeSelection?.selectedOfficeId;
    if (!officeId) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Office Required',
        detail: 'Please select an office before finalizing the contract',
      });
      return;
    }

    this.loading = true;
    try {
      // Save final data
      const contractData = this.tenantFormService.extractContractFromForm(
        this.form.value
      );

      await this.contractService.saveDraftContract(
        this.currentContract.id!,
        contractData,
        this.currentStep
      );

      // Finalize the contract
      await this.contractService.finalizeContract(this.currentContract.id!);

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Contract finalized successfully',
      });

      this.contractFinalized.emit(this.currentContract.id!);
      this.closeDialog();
    } catch (error) {
      console.error('Error finalizing contract:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to finalize contract',
      });
    } finally {
      this.loading = false;
    }
  }

  async updateExistingTenant(): Promise<void> {
    if (!this.isFormValid()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please complete all required fields',
      });
      return;
    }

    this.loading = true;
    try {
      // Extract tenant data from form
      const formValue = this.form.value;
      const updatedTenantData = {
        id: this.originalTenantId,
        basicInfo: formValue.basicInfo,
        contactPreferences: formValue.contactPreferences,
        leaseDetails: formValue.leaseDetails,
        additionalServices: formValue.additionalServices,
        legal: formValue.legal,
        adminNotes: formValue.adminNotes,
        updatedAt: new Date(),
      };

      console.log('Updating tenant with data:', updatedTenantData);

      // Update the tenant using the contract service
      await this.contractService.updateTenant(
        this.originalTenantId!,
        updatedTenantData
      );

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Tenant updated successfully',
      });

      this.tenantUpdated.emit(updatedTenantData);
      this.closeDialog();
    } catch (error) {
      console.error('Error updating tenant:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update tenant',
      });
    } finally {
      this.loading = false;
    }
  }

  async cancelDraft(): Promise<void> {
    if (!this.currentContract) return;

    this.loading = true;
    try {
      await this.contractService.cancelDraftContract(this.currentContract.id!);
      this.messageService.add({
        severity: 'info',
        summary: 'Cancelled',
        detail: 'Draft contract cancelled',
      });
      this.closeDialog();
    } catch (error) {
      console.error('Error cancelling draft:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to cancel draft',
      });
    } finally {
      this.loading = false;
    }
  }

  closeDialog(): void {
    console.log('=== CLOSING DIALOG ===');
    console.log('Current contract ID before close:', this.contractId);
    console.log('Current contract data before close:', this.currentContract);

    this.visible = false;
    this.visibleChange.emit(false);
    this.resetForm();

    console.log('Dialog closed and form reset');
  }

  private resetForm(): void {
    console.log('=== RESETTING FORM ===');
    console.log('Before reset:', {
      currentStep: this.currentStep,
      currentContract: this.currentContract,
      contractId: this.contractId,
      isEditingTenant: this.isEditingTenant,
    });

    this.currentStep = 0;
    this.currentContract = undefined;
    this.contractId = undefined;
    this.isEditingTenant = false;
    this.originalTenantId = undefined;
    this.form = this.tenantFormService.createTenantContractForm();

    console.log('Form reset completed');
  }

  private isCurrentStepValid(): boolean {
    return this.tenantFormService.isStepValid(this.form, this.currentStep);
  }

  private isFormValid(): boolean {
    return this.form.valid;
  }

  get isFirstStep(): boolean {
    return this.currentStep === 0;
  }

  get isLastStep(): boolean {
    return this.currentStep === this.steps.length - 1;
  }

  get canProceed(): boolean {
    return this.isCurrentStepValid() && !this.loading && !this.saving;
  }

  get canSaveDraft(): boolean {
    return (
      !this.isEditingTenant &&
      !this.loading &&
      !this.saving &&
      this.tenantFormService.isDraftSaveValid(this.form)
    );
  }

  // Debug method - can be called from browser console
  debugDraftData(): void {
    console.log('=== DRAFT DEBUG INFO ===');
    console.log('Current contract:', this.currentContract);
    console.log('Current form value:', this.form.value);
    console.log('Current step:', this.currentStep);
    console.log('Component properties:', {
      contractId: this.contractId,
      tenantData: this.tenantData,
      isEditingTenant: this.isEditingTenant,
      visible: this.visible,
    });

    // Check localStorage directly
    try {
      const draftsJson = localStorage.getItem('draft_contracts');
      console.log('Raw localStorage data:', draftsJson);
      if (draftsJson) {
        const drafts = JSON.parse(draftsJson);
        console.log('Parsed drafts:', drafts);
        drafts.forEach((draft: any, index: number) => {
          console.log(`Draft ${index}:`, draft);
          console.log(`Draft ${index} tenant:`, draft.tenant);
        });
      }
    } catch (error) {
      console.error('Error reading localStorage:', error);
    }
  }

  // Test method to simulate the edit flow
  testEditFlow(contractId: string): void {
    console.log('=== TESTING EDIT FLOW ===');
    console.log('1. Setting contractId:', contractId);
    this.contractId = contractId;

    console.log('2. Loading existing contract...');
    this.loadExistingContract();
  }

  // Cheque image handling methods
  onChequeImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.selectedChequeFile = file;

      // Create preview URL for immediate display
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.selectedChequeImage = e.target.result;
        this.cdr.detectChanges();
      };
      reader.readAsDataURL(file);

      // Upload to Firebase Storage
      this.uploadChequeImageToFirebase(file);
    }
  }

  private uploadChequeImageToFirebase(file: File): void {
    // We need a contract ID for organizing the files
    const contractId = this.contractId || this.generateTemporaryId();

    this.isUploadingChequeImage = true;
    this.messageService.add({
      severity: 'info',
      summary: 'Uploading Image',
      detail: 'Uploading cheque image to secure storage...',
    });

    this.chequeImageService
      .uploadChequeImage(file, contractId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result: ChequeImageUploadResult) => {
          console.log('Cheque image uploaded successfully:', result);
          this.chequeImageUploadResult = result;

          // Update form with the Firebase Storage download URL
          this.form.patchValue({
            leaseDetails: {
              paymentMethod: {
                chequeImage: result.downloadURL,
                chequeImagePath: result.filePath, // Store the storage path for deletion
              },
            },
          });

          // Auto-save if we have a draft contract (but not when editing tenant)
          if (
            !this.isEditingTenant &&
            (this.contractId || this.currentContract)
          ) {
            this.saveDraft().catch((error) => {
              console.error(
                'Failed to auto-save after cheque image upload:',
                error
              );
            });
          }

          this.isUploadingChequeImage = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Image Uploaded',
            detail: 'Cheque image has been uploaded successfully.',
          });

          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error uploading cheque image:', error);
          this.isUploadingChequeImage = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Upload Failed',
            detail: error.message || 'Failed to upload cheque image.',
          });
          this.cdr.detectChanges();
        },
      });
  }

  private generateTemporaryId(): string {
    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  removeChequeImage(): void {
    // Delete from Firebase Storage if it exists
    const chequeImagePath = this.form.get(
      'leaseDetails.paymentMethod.chequeImagePath'
    )?.value;

    if (chequeImagePath && this.chequeImageUploadResult) {
      this.chequeImageService
        .deleteChequeImage(chequeImagePath)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            console.log('Cheque image deleted from Firebase Storage');
          },
          error: (error) => {
            console.error('Failed to delete cheque image from storage:', error);
            // Don't prevent the removal from form even if storage deletion fails
          },
        });
    }

    // Clear local state
    this.selectedChequeImage = null;
    this.selectedChequeFile = null;
    this.chequeImageUploadResult = null;

    // Clear the form fields
    this.form.patchValue({
      leaseDetails: {
        paymentMethod: {
          chequeImage: '',
          chequeImagePath: '', // Clear the storage path too
        },
      },
    });

    // Auto-save if we have a draft contract (but not when editing tenant)
    if (!this.isEditingTenant && (this.contractId || this.currentContract)) {
      this.saveDraft().catch((error) => {
        console.error('Failed to auto-save after cheque image removal:', error);
      });
    }

    // Reset the file input
    const fileInput = document.getElementById(
      'chequeImage'
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }

    this.messageService.add({
      severity: 'info',
      summary: 'Image Removed',
      detail: 'Cheque image has been removed.',
    });

    this.cdr.detectChanges();
  }

  // Method to handle cheque image data when loading existing contracts
  private loadChequeImageFromData(chequeImageData: string): void {
    console.log(
      'Loading cheque image from data:',
      chequeImageData ? 'Image URL present' : 'No image URL'
    );
    if (chequeImageData && chequeImageData.trim() !== '') {
      // If it's a Firebase Storage URL, use it directly for display
      if (
        chequeImageData.includes('firebasestorage.googleapis.com') ||
        chequeImageData.startsWith('https://')
      ) {
        this.selectedChequeImage = chequeImageData;
        console.log('Firebase Storage URL loaded for cheque image');
      }
      // If it's base64 data (legacy support), convert it
      else if (chequeImageData.startsWith('data:image/')) {
        this.selectedChequeImage = chequeImageData;
        console.log('Base64 image data loaded (legacy format)');
      } else {
        console.warn(
          'Unknown cheque image format:',
          chequeImageData.substring(0, 50)
        );
      }

      // Ensure the form is also updated with the loaded image data
      const currentLeaseDetails = this.form.get('leaseDetails')?.value || {};
      const currentPaymentMethod = currentLeaseDetails.paymentMethod || {};

      this.form.patchValue({
        leaseDetails: {
          ...currentLeaseDetails,
          paymentMethod: {
            ...currentPaymentMethod,
            chequeImage: chequeImageData,
          },
        },
      });

      console.log('Cheque image loaded and form updated');
      this.cdr.detectChanges();
    } else {
      console.log('No valid cheque image data to load');
    }
  }

  // Method to get the current cheque image from form
  getCurrentChequeImage(): string | null {
    return (
      this.form.get('leaseDetails.paymentMethod.chequeImage')?.value ||
      this.selectedChequeImage
    );
  }

  // Method to verify cheque image persistence
  verifyChequeImagePersistence(): boolean {
    const formImageData = this.form.get(
      'leaseDetails.paymentMethod.chequeImage'
    )?.value;
    const componentImageData = this.selectedChequeImage;

    console.log('Verifying cheque image persistence:', {
      formHasImage: !!formImageData,
      componentHasImage: !!componentImageData,
      isFirebaseURL: formImageData?.includes('firebasestorage.googleapis.com'),
      bothMatch: formImageData === componentImageData,
    });

    return !!(formImageData || componentImageData);
  }
}
