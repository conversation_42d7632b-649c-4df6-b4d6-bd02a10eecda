.tenant-form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .steps-container {
    margin-bottom: 2rem;

    ::ng-deep .p-steps {
      .p-steps-item {
        .p-steps-number {
          background: #e9ecef;
          color: #6c757d;
          border: 2px solid #e9ecef;
        }

        &.p-highlight .p-steps-number {
          background: var(--primary-color);
          color: white;
          border-color: var(--primary-color);
        }

        .p-steps-title {
          color: #6c757d;
          font-weight: 500;
        }

        &.p-highlight .p-steps-title {
          color: var(--primary-color);
          font-weight: 600;
        }
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .step-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;

    .step-panel {
      h4 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        font-weight: 600;
      }

      .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;

        h5 {
          color: #495057;
          margin-bottom: 1rem;
          font-weight: 600;
          border-bottom: 2px solid var(--primary-color);
          padding-bottom: 0.5rem;
        }

        h6 {
          color: #6c757d;
          margin: 1.5rem 0 1rem 0;
          font-weight: 500;
        }
      }

      // Branch selection specific styles
      .branch-item {
        padding: 0.5rem 0;

        .branch-name {
          font-weight: 600;
          color: var(--primary-color);
          margin-bottom: 0.25rem;
        }

        .branch-address {
          small {
            color: #6c757d;
          }
        }
      }

      // Office selection specific styles
      .office-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;

        .office-number {
          font-weight: 600;
          color: var(--primary-color);
        }

        .office-details {
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          small {
            color: #6c757d;
            margin-bottom: 0.25rem;
          }

          .office-status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;

            &.status-available {
              background: #d4edda;
              color: #155724;
            }

            &.status-occupied {
              background: #f8d7da;
              color: #721c24;
            }

            &.status-under_processing {
              background: #fff3cd;
              color: #856404;
            }

            &.status-maintenance {
              background: #d1ecf1;
              color: #0c5460;
            }
          }
        }
      }

      // Office info display
      .office-info {
        .p-card {
          background: #f8f9fa;
          border: 1px solid #e9ecef;

          .p-card-body {
            padding: 1rem;

            h6 {
              color: var(--primary-color);
              margin-bottom: 0.75rem;
              font-weight: 600;
            }

            p {
              margin: 0.5rem 0;
              color: #495057;

              strong {
                color: #212529;
                margin-right: 0.5rem;
              }
            }
          }
        }
      }

      // Contract summary styles
      .contract-summary {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;

        .summary-section {
          margin-bottom: 1.5rem;

          &:last-child {
            margin-bottom: 0;
          }

          h5 {
            color: var(--primary-color);
            margin-bottom: 0.75rem;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 0.5rem;
          }

          p {
            margin: 0.5rem 0;
            color: #495057;

            strong {
              color: #212529;
              margin-right: 0.5rem;
            }
          }
        }
      }

      // Legal checkboxes
      .legal-checkboxes {
        .field-checkbox {
          margin-bottom: 1rem;
          padding: 1rem;
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 6px;

          label {
            margin-left: 0.5rem;
            font-weight: 500;
            color: #495057;
          }
        }
      }

      // Cheque upload styles
      .cheque-upload-container {
        .upload-area {
          border: 2px dashed #e9ecef;
          border-radius: 8px;
          padding: 2rem;
          text-align: center;
          cursor: pointer;
          transition: all 0.3s ease;
          background: #fafafa;
          position: relative;
          overflow: hidden;

          &:hover {
            border-color: var(--primary-color);
            background: #f0f8ff;
          }

          &.has-image {
            padding: 0;
            border-style: solid;
            border-color: var(--primary-color);
          }

          &.uploading {
            border-color: #007ad9;
            background: #f0f8ff;
            cursor: not-allowed;

            .upload-progress {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;

              p {
                margin: 1rem 0 0.5rem 0;
                font-weight: 500;
                color: #007ad9;
              }

              small {
                color: #6c757d;
                font-size: 0.875rem;
              }
            }
          }

          .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            p {
              margin: 1rem 0 0.5rem 0;
              font-weight: 500;
              color: #495057;
            }

            small {
              color: #6c757d;
              font-size: 0.875rem;
            }
          }

          .upload-preview {
            position: relative;
            width: 100%;
            max-width: 400px;
            margin: 0 auto;

            .cheque-preview-image {
              width: 100%;
              height: auto;
              max-height: 300px;
              object-fit: contain;
              border-radius: 8px;
              display: block;
            }

            .upload-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.7);
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              opacity: 0;
              transition: opacity 0.3s ease;
              border-radius: 8px;
              color: white;

              i {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
              }

              span {
                font-weight: 500;
              }
            }

            &:hover .upload-overlay {
              opacity: 1;
            }
          }
        }

        .upload-actions {
          display: flex;
          justify-content: center;
          gap: 0.5rem;
        }
      }
    }
  }

  .form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0 0 0;
    border-top: 1px solid #e9ecef;
    margin-top: 1rem;

    .nav-left,
    .nav-right {
      flex: 1;
    }

    .nav-center {
      display: flex;
      gap: 0.5rem;
      justify-content: center;
      flex: 2;

      // Draft button styling
      .p-button-info {
        &:not(.p-disabled) {
          background: #17a2b8;
          border-color: #17a2b8;
          color: white;

          &:hover {
            background: #138496;
            border-color: #117a8b;
          }
        }

        &.p-disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    .nav-right {
      display: flex;
      justify-content: flex-end;
    }

    button {
      margin: 0 0.25rem;
    }
  }
}

// Form field improvements
::ng-deep {
  .p-field {
    margin-bottom: 1.5rem;

    label {
      font-weight: 500;
      color: #495057;
      margin-bottom: 0.5rem;
      display: block;
    }

    .p-inputtext,
    .p-dropdown,
    .p-calendar,
    .p-inputnumber {
      width: 100%;
    }

    .p-invalid {
      border-color: #dc3545;
    }
  }

  .field-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;

    .p-checkbox {
      margin-right: 0.5rem;
    }

    label {
      margin: 0;
      cursor: pointer;
      font-weight: 500;
    }
  }

  // Dialog customizations
  .tenant-form-dialog {
    .p-dialog-header {
      background: var(--primary-color);
      color: white;
      border-radius: 6px 6px 0 0;

      .p-dialog-title {
        font-weight: 600;
      }

      .p-dialog-header-icon {
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .p-dialog-content {
      padding: 1.5rem;
      height: calc(90vh - 120px);
      overflow: hidden;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .tenant-form-dialog {
      .p-dialog {
        width: 95vw !important;
        height: 95vh !important;
      }
    }

    .form-navigation {
      flex-direction: column;
      gap: 1rem;

      .nav-left,
      .nav-center,
      .nav-right {
        width: 100%;
        justify-content: center;
      }
    }

    .p-formgrid .p-col-12.p-md-6,
    .p-formgrid .p-col-12.p-md-4 {
      padding: 0.5rem;
    }
  }
}
