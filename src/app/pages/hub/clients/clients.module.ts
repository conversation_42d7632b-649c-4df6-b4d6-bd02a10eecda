import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ClientsRoutingModule } from './clients-routing.module';
import { ClientsComponent } from './clients.component';
import { MultiStepTenantFormComponent } from './multi-step-tenant-form/multi-step-tenant-form.component';
import { PrimengModule } from 'src/app/shared/modules/primeng.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [ClientsComponent, MultiStepTenantFormComponent],
  imports: [
    CommonModule,
    ClientsRoutingModule,
    PrimengModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  exports: [
    MultiStepTenantFormComponent, // Export for use in other modules
  ],
})
export class ClientsModule {}
