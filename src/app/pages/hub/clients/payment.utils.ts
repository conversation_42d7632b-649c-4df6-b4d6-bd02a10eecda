import { Contract } from './tenant.model';

/**
 * Payment status constants
 */
export const PAYMENT_STATUS = {
  PAID: 'Paid',
  NEXT_PAYMENT_SOON: 'Next Payment Soon',
  OVERDUE: 'Overdue',
  NO_PAYMENT_INFO: 'No Payment Info',
} as const;

/**
 * Number of days before next payment to consider it "soon"
 */
export const DAYS_UNTIL_PAYMENT_SOON = 7;

/**
 * Computes the payment status based on contract payment information
 * @param contract The contract to compute payment status for
 * @returns The computed payment status string
 */
export function computePaymentStatus(contract: Contract): string {
  // If no payment dates are set, return no payment info
  if (!contract.nextPaymentAt && !contract.paidAt) {
    return PAYMENT_STATUS.NO_PAYMENT_INFO;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison

  // If nextPaymentAt is set, use it for status calculation
  if (contract.nextPaymentAt) {
    const nextPayment = new Date(contract.nextPaymentAt);
    nextPayment.setHours(0, 0, 0, 0);

    // Check if payment is overdue
    if (nextPayment < today) {
      return PAYMENT_STATUS.OVERDUE;
    }

    // Check if payment is due soon
    const daysDifference = Math.ceil(
      (nextPayment.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
    );
    if (daysDifference <= DAYS_UNTIL_PAYMENT_SOON) {
      return PAYMENT_STATUS.NEXT_PAYMENT_SOON;
    }

    // Payment is not due soon
    return PAYMENT_STATUS.PAID;
  }

  // If only paidAt is available, check if it's recent based on billing cycle
  if (contract.paidAt && contract.paymentMethod?.billingCycle) {
    const lastPayment = new Date(contract.paidAt);
    const daysSincePayment = Math.ceil(
      (today.getTime() - lastPayment.getTime()) / (1000 * 60 * 60 * 24)
    );

    let expectedPaymentInterval: number;
    switch (contract.paymentMethod.billingCycle) {
      case 'Monthly':
        expectedPaymentInterval = 30;
        break;
      case 'Quarterly':
        expectedPaymentInterval = 90;
        break;
      case 'Yearly':
        expectedPaymentInterval = 365;
        break;
      default:
        expectedPaymentInterval = 30; // Default to monthly
    }

    // Add a 7-day grace period
    const gracePeriod = 7;
    if (daysSincePayment > expectedPaymentInterval + gracePeriod) {
      return PAYMENT_STATUS.OVERDUE;
    } else if (
      daysSincePayment >
      expectedPaymentInterval - DAYS_UNTIL_PAYMENT_SOON
    ) {
      return PAYMENT_STATUS.NEXT_PAYMENT_SOON;
    } else {
      return PAYMENT_STATUS.PAID;
    }
  }

  return PAYMENT_STATUS.NO_PAYMENT_INFO;
}

/**
 * Calculates the next payment date based on the current payment date and billing cycle
 * @param currentPaymentDate The current/last payment date
 * @param billingCycle The billing cycle frequency
 * @returns The next scheduled payment date
 */
export function calculateNextPaymentDate(
  currentPaymentDate: Date,
  billingCycle: 'Monthly' | 'Quarterly' | 'Yearly'
): Date {
  const nextPayment = new Date(currentPaymentDate);

  switch (billingCycle) {
    case 'Monthly':
      nextPayment.setMonth(nextPayment.getMonth() + 1);
      break;
    case 'Quarterly':
      nextPayment.setMonth(nextPayment.getMonth() + 3);
      break;
    case 'Yearly':
      nextPayment.setFullYear(nextPayment.getFullYear() + 1);
  }

  return nextPayment;
}

/**
 * Updates payment information for a contract
 * @param contract The contract to update
 * @param paymentDate The date of the payment made
 * @param billingCycle The billing cycle (optional, will use existing if not provided)
 * @returns Updated contract with new payment information
 */
export function updateContractPaymentInfo(
  contract: Contract,
  paymentDate: Date,
  billingCycle?: 'Monthly' | 'Quarterly' | 'Yearly'
): Partial<Contract> {
  const cycle =
    billingCycle || contract.paymentMethod?.billingCycle || 'Monthly';
  const nextPaymentAt = calculateNextPaymentDate(paymentDate, cycle);

  // Update the payment method with the billing cycle
  const updatedPaymentMethod = {
    type: 'Bank Transfer' as const,
    autoPay: false,
    ...contract.paymentMethod,
    billingCycle: cycle,
  };

  const updatedContract: Partial<Contract> = {
    paidAt: paymentDate,
    nextPaymentAt: nextPaymentAt,
    paymentMethod: updatedPaymentMethod,
    paymentStatus: computePaymentStatus({
      ...contract,
      paidAt: paymentDate,
      nextPaymentAt: nextPaymentAt,
      paymentMethod: updatedPaymentMethod,
    } as Contract),
  };

  return updatedContract;
}

/**
 * Refreshes the payment status for a contract (useful for periodic updates)
 * @param contract The contract to refresh status for
 * @returns Updated contract with refreshed payment status
 */
export function refreshContractPaymentStatus(
  contract: Contract
): Partial<Contract> {
  return {
    paymentStatus: computePaymentStatus(contract),
  };
}
