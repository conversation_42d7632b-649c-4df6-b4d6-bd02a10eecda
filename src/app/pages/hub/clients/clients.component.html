<!-- tenant-management.component.html -->
<div class="flex justify-content-between align-items-center mb-4">
  <h5>Tenant Management</h5>
  <div class="d-flex gap-2 justify-content-between">
    <span class="p-input-icon-left">
      <i class="pi pi-search"></i>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchText"
        (input)="filterTenants()"
        placeholder="Search tenants..."
      />
    </span>
    <div class="wheel-button-container">
      <button
        pButton
        icon="pi pi-cog"
        class="p-button-rounded p-button-secondary"
        [class.rotate]="isRotated"
        (click)="toggleButtons()"
      ></button>
      <div class="hidden-buttons" [ngClass]="{ show: showButtons }">
        <button
          pButton
          icon="pi pi-plus"
          label="New Tenant Contract"
          (click)="openNewTenantForm()"
          class="p-button-primary"
        ></button>
        <button
          pButton
          icon="pi pi-trash"
          label="Show Deleted Contracts"
          (click)="showDeletedContracts()"
          class="p-button-secondary"
        ></button>
      </div>
    </div>
  </div>
</div>

<!-- Canvas for Deleted Contracts -->
<div *ngIf="showDeletedContractsCanvas" class="deleted-contracts-modal">
  <div class="modal-overlay" (click)="showDeletedContractsCanvas = false"></div>
  <div class="modal-content">
    <div class="modal-header">
      <h5>Deleted Contracts</h5>
      <button
        type="button"
        class="close-btn"
        (click)="showDeletedContractsCanvas = false"
        aria-label="Close"
      >
        &times;
      </button>
    </div>
    <div class="modal-body">
      <table class="deleted-contracts-table">
        <thead>
          <tr>
            <th>Contract Number</th>
            <th>Tenant Name</th>
            <th>Status</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let contract of deletedContracts">
            <td>{{ contract.contractNumber }}</td>
            <td>
              {{ contract.tenant?.basicInfo?.fullName || "Not specified" }}
            </td>
            <td [ngClass]="'status-' + contract.status.toLowerCase()">
              {{ contract.status }}
            </td>
            <td>
              <button
                pButton
                icon="pi pi-refresh"
                class="p-button-rounded p-button-text"
                (click)="restoreContractAndTenant(contract)"
                pTooltip="Restore Contract"
              ></button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="modal-footer">
      <button
        pButton
        label="Close"
        (click)="showDeletedContractsCanvas = false"
        class="p-button-secondary"
      ></button>
    </div>
  </div>
</div>

<!-- Draft Contracts Section -->
<div class="mb-4" *ngIf="draftContracts.length > 0">
  <h6>Draft Contracts</h6>
  <p-table
    [value]="draftContracts"
    styleClass="p-datatable-sm"
    [paginator]="true"
    [rows]="5"
  >
    <ng-template pTemplate="header">
      <tr>
        <th>Contract ID</th>
        <th>Tenant Name</th>
        <th>Office</th>
        <th>Current Step</th>
        <th>Payment Status</th>
        <th>Last Updated</th>
        <th>Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-draft>
      <tr>
        <td>{{ draft.contractNumber }}</td>
        <td>{{ draft.tenant?.basicInfo?.fullName || "Not specified" }}</td>
        <td>{{ draft.officeId || "Not selected" }}</td>
        <td>{{ draft.currentStep || 0 }}/4</td>
        <td>
          <span
            [ngClass]="getPaymentStatus(draft).class"
            [pTooltip]="getContractPaymentTooltip(draft)"
            tooltipPosition="top"
            [escape]="false"
          >
            {{ getPaymentStatus(draft).text }}
          </span>
        </td>
        <td>{{ draft.updatedAt | date : "short" }}</td>
        <td>
          <button
            pButton
            icon="pi pi-pencil"
            class="p-button-rounded p-button-text p-button-sm"
            (click)="openEditDraftContract(draft.id!)"
            pTooltip="Edit Draft"
          ></button>
          <button
            pButton
            icon="pi pi-trash"
            class="p-button-rounded p-button-text p-button-sm p-button-danger"
            (click)="deleteDraftContract(draft.id!)"
            pTooltip="Delete Draft"
          ></button>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-table
  [value]="filteredTenants"
  [loading]="loading"
  [paginator]="true"
  [rows]="10"
  styleClass="p-datatable-sm"
>
  <ng-template pTemplate="header">
    <tr>
      <th>Name</th>
      <th>Company</th>
      <th>Email</th>
      <th>Phone</th>
      <th>Payment Status</th>
      <th>Actions</th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-tenant>
    <tr>
      <td>{{ tenant.basicInfo.fullName }}</td>
      <td>{{ tenant.basicInfo.companyName || "-" }}</td>
      <td>{{ tenant.basicInfo.email }}</td>
      <td>{{ tenant.basicInfo.phone }}</td>
      <td>
        <span
          [ngClass]="getTenantPaymentStatus(tenant).class"
          [pTooltip]="getTenantPaymentTooltip(tenant)"
          tooltipPosition="top"
          [escape]="false"
        >
          {{ getTenantPaymentStatus(tenant).text }}
        </span>
      </td>
      <td>
        <div class="flex gap-2">
          <button
            pButton
            icon="pi pi-pencil"
            class="p-button-rounded p-button-text p-button-info"
            (click)="openEdit(tenant)"
            pTooltip="Edit Tenant"
          ></button>
          <button
            pButton
            icon="pi pi-chart-line"
            class="p-button-rounded p-button-text p-button-info"
            (click)="trackPayment(tenant)"
            pTooltip="Track Payment"
          ></button>
          <p-toast />
          <p-confirmPopup />

          <p-button
            (click)="deleteTenant($event, tenant)"
            icon="pi pi-trash"
            severity="danger"
            styleClass="p-button-text p-button-danger"
            pTooltip="Delete Tenant"
          />
        </div>
      </td>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="6">No tenants found</td>
    </tr>
  </ng-template>
</p-table>

<!-- New Multi-step Tenant Form -->
<app-multi-step-tenant-form
  [(visible)]="showTenantForm"
  [contractId]="selectedContractId"
  [tenantData]="selectedTenantData"
  (contractCreated)="onContractCreated($event)"
  (contractFinalized)="onContractFinalized($event)"
  (tenantUpdated)="onTenantUpdated($event)"
></app-multi-step-tenant-form>

<!-- Payment Timeline Modal -->
<div *ngIf="showPaymentTimelineModal" class="payment-timeline-modal">
  <div class="modal-overlay" (click)="showPaymentTimelineModal = false"></div>
  <div class="modal-content payment-timeline-content">
    <div class="modal-header">
      <h5>
        Payment Timeline - {{ selectedTenantForTracking?.basicInfo?.fullName }}
      </h5>
      <button
        type="button"
        class="close-btn"
        (click)="showPaymentTimelineModal = false"
        aria-label="Close"
      >
        &times;
      </button>
    </div>
    <div class="modal-body">
      <div *ngIf="timelineLoading" class="timeline-loading">
        <i class="pi pi-spin pi-spinner"></i>
        Loading payment timeline...
      </div>
      <div
        *ngIf="!timelineLoading && paymentTimeline.length === 0"
        class="no-timeline"
      >
        <i class="pi pi-info-circle"></i>
        No payment timeline available for this tenant.
      </div>
      <p-timeline
        *ngIf="!timelineLoading && paymentTimeline.length > 0"
        [value]="paymentTimeline"
        align="alternate"
        styleClass="custom-timeline"
      >
        <ng-template pTemplate="marker" let-event>
          <span
            class="custom-marker"
            [ngClass]="getTimelineMarkerClass(event.status)"
          >
            <i [class]="getTimelineMarkerIcon(event.status)"></i>
          </span>
        </ng-template>
        <ng-template pTemplate="content" let-event let-i="index">
          <div class="timeline-event-content">
            <div class="timeline-date">
              <h6>{{ formatTimelineDate(event.date) }}</h6>
              <span class="timeline-period">{{ event.period }}</span>
              <div
                *ngIf="event.dueDate && event.date !== event.dueDate"
                class="due-date"
              >
                <small>Due: {{ formatTimelineDate(event.dueDate) }}</small>
              </div>
            </div>
            <div class="timeline-details">
              <div
                class="payment-status"
                [ngClass]="'status-' + event.status.toLowerCase()"
              >
                {{ event.status }}
              </div>
              <div class="payment-amount">
                {{ event.amount | currency : event.currency }}
              </div>
              <div class="payment-method" *ngIf="event.paymentMethod">
                <small
                  ><i class="pi pi-credit-card"></i>
                  {{ event.paymentMethod }}</small
                >
              </div>
              <div class="transaction-info" *ngIf="event.transactionId">
                <small
                  ><i class="pi pi-tag"></i> Transaction:
                  {{ event.transactionId }}</small
                >
              </div>
              <div class="cheque-info" *ngIf="event.chequeNumber">
                <small
                  ><i class="pi pi-file"></i> Cheque:
                  {{ event.chequeNumber }}</small
                >
              </div>
              <div class="payment-notes" *ngIf="event.notes">
                <small
                  ><i class="pi pi-info-circle"></i> {{ event.notes }}</small
                >
              </div>
            </div>
          </div>
        </ng-template>
      </p-timeline>
    </div>
    <div class="modal-footer">
      <button
        pButton
        label="Close"
        (click)="showPaymentTimelineModal = false"
        class="p-button-secondary"
      ></button>
    </div>
  </div>
</div>

<!-- Toast Messages -->
<p-toast key="confirm" position="center">
  <ng-template let-message pTemplate="message">
    <div class="p-toast-message-content">
      <div class="p-toast-message-text">
        <span class="p-toast-summary">{{ message.summary }}</span>
        <div class="p-toast-detail">{{ message.detail }}</div>
      </div>
      <div class="p-toast-message-actions mt-3">
        <button
          pButton
          type="button"
          label="Cancel"
          class="p-button-text"
          (click)="cancelDelete()"
        ></button>
        <button
          pButton
          type="button"
          label="Delete"
          class="p-button-danger ml-2"
          (click)="confirmDeleteDraft(message.data); cancelDelete()"
        ></button>
      </div>
    </div>
  </ng-template>
</p-toast>
