import { Component, OnInit, OnDestroy } from '@angular/core';
import { ContractService } from './contract.service';
import { Tenant, Contract, PaymentTimelineEvent } from './tenant.model';
import { MessageService, ConfirmationService } from 'primeng/api';
import { Subject, takeUntil } from 'rxjs';
import { UserService } from 'src/app/shared/services/user.service';
import { AngularFireAuth } from '@angular/fire/compat/auth';

@Component({
  selector: 'app-clients',
  templateUrl: './clients.component.html',
  styleUrls: ['./clients.component.scss'],
  providers: [MessageService, ConfirmationService],
})
export class ClientsComponent implements OnInit, OnDestroy {
  tenants: Tenant[] = [];
  contracts: Contract[] = [];
  draftContracts: Contract[] = [];
  filteredTenants: Tenant[] = [];
  searchText: string = '';

  // Multi-step form dialog
  showTenantForm: boolean = false;
  selectedContractId?: string;
  selectedTenantData?: any; // For editing existing tenants
  showButtons: boolean = false;
  isRotated: boolean = false;

  deletedContracts: Contract[] = [];
  showDeletedContractsCanvas = false;
  loading: boolean = false;

  // Payment Timeline Modal
  showPaymentTimelineModal: boolean = false;
  selectedTenantForTracking?: Tenant;
  paymentTimeline: PaymentTimelineEvent[] = [];
  timelineLoading: boolean = false;

  // Performance optimization: Cache payment status calculations
  private paymentStatusCache = new Map<
    string,
    { text: string; class: string }
  >();
  private contractMap = new Map<string, Contract>();

  private destroy$ = new Subject<void>();

  constructor(
    private contractService: ContractService,
    public messageService: MessageService,
    private confirmationService: ConfirmationService,
    private userService: UserService,
    private afAuth: AngularFireAuth
  ) {}

  ngOnInit(): void {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.loadTenants();
      }
    });
    this.loadContracts();
    this.loadDraftContracts();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleButtons() {
    this.showButtons = !this.showButtons;
    this.isRotated = !this.isRotated;
  }

  async loadTenants(): Promise<void> {
    this.loading = true;
    const currentUser = await this.userService.getCurrentUser();
    if (!currentUser) {
      this.tenants = [];
      this.filteredTenants = [];
      this.loading = false;
      return;
    }
    this.contractService
      .getAllTenantsForUser(currentUser)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tenants) => {
          this.tenants = tenants;
          this.filteredTenants = [...this.tenants];
          this.clearPaymentStatusCache(); // Clear cache when tenants change
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading tenants:', error);
          this.tenants = [];
          this.filteredTenants = [];
          this.loading = false;
        },
      });
  }

  loadContracts(): void {
    this.contractService
      .getAllContracts()
      .pipe(takeUntil(this.destroy$))
      .subscribe((contracts) => {
        this.contracts = contracts;
        this.updateContractMap();
        this.clearPaymentStatusCache();
      });
  }

  loadDraftContracts(): void {
    this.contractService
      .getDraftContracts()
      .pipe(takeUntil(this.destroy$))
      .subscribe((drafts) => {
        this.draftContracts = drafts;
        this.updateContractMap();
        this.clearPaymentStatusCache();
      });
  }

  filterTenants(): void {
    if (!this.searchText) {
      this.filteredTenants = [...this.tenants];
      return;
    }

    this.filteredTenants = this.tenants.filter(
      (tenant) =>
        tenant.basicInfo.fullName
          .toLowerCase()
          .includes(this.searchText.toLowerCase()) ||
        tenant.basicInfo.email
          .toLowerCase()
          .includes(this.searchText.toLowerCase()) ||
        tenant.basicInfo.companyName
          ?.toLowerCase()
          .includes(this.searchText.toLowerCase())
    );
  }

  // New multi-step form methods
  openNewTenantForm(): void {
    this.selectedContractId = undefined;
    this.selectedTenantData = undefined; // Clear tenant data for new form
    this.showTenantForm = true;
  }

  openEditDraftContract(contractId: string): void {
    // Clear previous state first
    this.selectedContractId = undefined;
    this.selectedTenantData = undefined;
    this.showTenantForm = false;

    // Small delay to ensure state is cleared
    setTimeout(() => {
      this.selectedContractId = contractId;
      this.showTenantForm = true;
    }, 50);
  }

  deleteDraftContract(contractId: string): void {
    // Show confirmation dialog
    this.messageService.add({
      key: 'confirm',
      sticky: true,
      severity: 'warn',
      summary: 'Delete Draft Contract',
      detail:
        'Are you sure you want to delete this draft contract? This action cannot be undone.',
      data: contractId,
    });
  }

  confirmDeleteDraft(contractId: string): void {
    this.contractService
      .cancelDraftContract(contractId)
      .then(() => {
        this.loadDraftContracts(); // Refresh the list
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Draft contract deleted successfully',
        });
      })
      .catch((error) => {
        console.error('Error deleting draft contract:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to delete draft contract',
        });
      });
  }

  cancelDelete(): void {
    this.messageService.clear('confirm');
  }

  onContractCreated(contractId: string): void {
    this.loadDraftContracts();
    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Draft contract created successfully',
    });
  }

  onContractFinalized(contractId: string): void {
    this.loadContracts();
    this.loadDraftContracts();
    this.loadTenants(); // Reload tenants to show the newly created tenant
    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Contract finalized and tenant created successfully',
    });
  }

  onTenantUpdated(updatedTenant: any): void {
    this.loadTenants(); // Reload tenants to show the updated data
    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Tenant updated successfully',
    });
  }

  openEdit(tenant: Tenant): void {
    // Clear any previous state first
    this.selectedContractId = undefined;
    this.selectedTenantData = undefined;
    this.showTenantForm = false;

    // Use setTimeout to ensure proper change detection
    setTimeout(() => {
      this.selectedTenantData = tenant; // Set the tenant data for the multi-step form
      this.showTenantForm = true; // Use the multi-step form
    }, 100);
  }

  deleteTenant(event: Event, tenant: any): void {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: `Are you sure you want to delete tenant "${tenant.basicInfo.fullName}"? This will also cancel their contract and free up their office.`,
      header: 'Delete Tenant',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loading = true;
        this.contractService
          .deleteTenant(tenant.id)
          .then(() => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Tenant deleted successfully',
            });
            this.loadTenants();
            this.loadContracts(); // Reload contracts to reflect the cancelled contract
          })
          .catch((error) => {
            console.error('Error deleting tenant:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete tenant',
            });
            this.loading = false;
          });
      },
      reject: () => {
        // User cancelled - do nothing
      },
    });
  }

  showDeletedContracts() {
    this.contractService.getDeletedContracts().subscribe((contracts) => {
      this.deletedContracts = contracts;
      this.showDeletedContractsCanvas = true;
      setTimeout(() => {
        const canvas = document.querySelector('.deleted-contracts-canvas');
        if (canvas) {
          canvas.classList.add('active');
        }
      }, 0);
    });
  }

  // Helper method to convert Firestore Timestamp to Date
  private convertToDate(timestamp: any): Date {
    if (!timestamp) return new Date();

    // If it's already a Date object
    if (timestamp instanceof Date) {
      return timestamp;
    }

    // If it's a Firestore Timestamp with seconds property
    if (timestamp.seconds) {
      return new Date(timestamp.seconds * 1000);
    }

    // If it has a toDate method (Firestore Timestamp)
    if (typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }

    // Fallback: try to parse as regular date
    return new Date(timestamp);
  }

  // Method to compute payment status based on dates
  getPaymentStatus(contract: Contract): { text: string; class: string } {
    if (!contract.nextPaymentAt) {
      return { text: 'N/A', class: 'status-na' };
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to compare only dates

    const nextPayment = this.convertToDate(contract.nextPaymentAt);
    nextPayment.setHours(0, 0, 0, 0);

    const diffTime = nextPayment.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // If payment is overdue
    if (diffDays < 0) {
      return { text: 'Overdue', class: 'status-overdue' };
    }

    // If payment is within 7 days
    if (diffDays <= 7) {
      return { text: 'Soon', class: 'status-soon' };
    }

    // If recently paid and next payment is in the future
    if (contract.paidAt) {
      const paidAt = this.convertToDate(contract.paidAt);
      const daysSincePaid = Math.ceil(
        (today.getTime() - paidAt.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Consider "recent" as within the last 30 days
      if (daysSincePaid <= 30 && diffDays > 7) {
        return { text: 'Paid', class: 'status-paid' };
      }
    }

    // Default case for future payments
    return { text: `Due in ${diffDays} days`, class: 'status-future' };
  }

  // Optimized method to get payment status for a tenant by finding their contract
  getTenantPaymentStatus(tenant: any): { text: string; class: string } {
    const cacheKey = this.getCacheKey(tenant.id, 'tenant');

    // Check cache first
    if (this.paymentStatusCache.has(cacheKey)) {
      return this.paymentStatusCache.get(cacheKey)!;
    }

    let result: { text: string; class: string };
    let tenantContract = null;

    // Use contract map for faster lookup if available
    if (tenant.contractId && this.contractMap.size > 0) {
      tenantContract = this.contractMap.get(tenant.contractId);
    }

    // Fallback to array search if contract map lookup fails
    if (!tenantContract && tenant.contractId) {
      tenantContract = this.contracts.find(
        (contract) => contract.id === tenant.contractId
      );
    }

    if (!tenantContract) {
      // Also try alternative matching methods
      tenantContract = this.contracts.find(
        (contract) =>
          contract.tenantId === tenant.id || contract.tenant?.id === tenant.id
      );
    }

    if (!tenantContract) {
      // Check draft contracts as well
      let draftContract = null;

      if (tenant.contractId) {
        draftContract = this.draftContracts.find(
          (contract) => contract.id === tenant.contractId
        );
      }

      if (!draftContract) {
        draftContract = this.draftContracts.find(
          (contract) =>
            contract.tenantId === tenant.id || contract.tenant?.id === tenant.id
        );
      }

      if (draftContract) {
        result = this.getPaymentStatus(draftContract);
      } else {
        result = { text: 'No Contract', class: 'status-na' };
      }
    } else {
      result = this.getPaymentStatus(tenantContract);
    }

    // Cache the result
    this.paymentStatusCache.set(cacheKey, result);
    return result;
  }

  // Method to get tooltip text for tenant payment status
  getTenantPaymentTooltip(tenant: any): string {
    let tenantContract = null;

    if (tenant.contractId) {
      tenantContract = this.contracts.find(
        (contract) => contract.id === tenant.contractId
      );
    }

    if (!tenantContract) {
      tenantContract = this.contracts.find(
        (contract) =>
          contract.tenantId === tenant.id || contract.tenant?.id === tenant.id
      );
    }

    if (!tenantContract) {
      let draftContract = null;
      if (tenant.contractId) {
        draftContract = this.draftContracts.find(
          (contract) => contract.id === tenant.contractId
        );
      }
      if (!draftContract) {
        draftContract = this.draftContracts.find(
          (contract) =>
            contract.tenantId === tenant.id || contract.tenant?.id === tenant.id
        );
      }
      if (draftContract) {
        return this.getContractPaymentTooltip(draftContract);
      }
      return '<strong>No Contract Found</strong><br/>This tenant has no associated contract.';
    }

    return this.getContractPaymentTooltip(tenantContract);
  }

  // Method to get detailed tooltip for contract payment information
  getContractPaymentTooltip(contract: Contract): string {
    if (!contract.nextPaymentAt) {
      return '<strong>No Payment Schedule</strong><br/>Payment dates not configured for this contract.';
    }

    const nextPayment = this.convertToDate(contract.nextPaymentAt);
    const today = new Date();

    let tooltip = `<strong>Contract: ${
      contract.contractNumber || contract.id
    }</strong><br/>`;

    if (contract.paidAt) {
      const paidAt = this.convertToDate(contract.paidAt);
      tooltip += `<strong>Last Payment:</strong> ${paidAt.toLocaleDateString()}<br/>`;
    }

    tooltip += `<strong>Next Payment Due:</strong> ${nextPayment.toLocaleDateString()}<br/>`;

    if (contract.paymentMethod?.billingCycle) {
      tooltip += `<strong>Payment Plan:</strong> ${contract.paymentMethod?.billingCycle}<br/>`;
    }

    const diffTime = nextPayment.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      tooltip += `<strong style="color: #dc3545;">⚠️ ${Math.abs(
        diffDays
      )} days overdue</strong>`;
    } else if (diffDays <= 7) {
      tooltip += `<strong style="color: #ffc107;">⏰ Due in ${diffDays} days</strong>`;
    } else {
      tooltip += `<strong style="color: #28a745;">📅 ${diffDays} days remaining</strong>`;
    }

    return tooltip;
  }

  restoreContractAndTenant(contract: any): void {
    const contractId = contract.id;
    // Try multiple ways to get the tenant ID
    const tenantId = contract.tenantId || contract.tenant?.id;

    // Check if contractId is defined before proceeding
    if (!contractId) {
      console.error('Contract ID is undefined');
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Contract ID is missing. Cannot restore contract.',
      });
      return;
    }

    if (!tenantId) {
      console.error('Tenant ID is undefined - cannot restore tenant');
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Tenant ID is missing. Cannot restore tenant.',
      });
      return;
    }

    // Restore the contract
    this.contractService.restoreContract(contractId).subscribe(
      (contractResponse) => {
        // Restore the tenant
        this.contractService.restoreTenant(tenantId).subscribe(
          (tenantResponse) => {
            // Refresh the list of deleted contracts and tenants
            this.showDeletedContracts();
            this.loadTenants(); // Refresh active tenants list
            this.loadContracts(); // Refresh active contracts list
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Contract and tenant restored successfully',
            });
          },
          (tenantError) => {
            console.error('Error restoring tenant:', tenantError);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Contract restored but failed to restore tenant',
            });
          }
        );
      },
      (contractError) => {
        console.error('Error restoring contract:', contractError);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to restore contract',
        });
      }
    );
  }

  // Performance optimization: Cache management methods
  private updateContractMap(): void {
    this.contractMap.clear();
    // Add active contracts
    this.contracts.forEach((contract) => {
      if (contract.id) {
        this.contractMap.set(contract.id, contract);
      }
    });
    // Add draft contracts
    this.draftContracts.forEach((contract) => {
      if (contract.id) {
        this.contractMap.set(contract.id, contract);
      }
    });
  }

  private clearPaymentStatusCache(): void {
    this.paymentStatusCache.clear();
  }

  private getCacheKey(identifier: string, type: 'tenant' | 'contract'): string {
    return `${type}-${identifier}`;
  }

  // Payment Timeline Methods
  trackPayment(tenant: Tenant): void {
    this.selectedTenantForTracking = tenant;
    this.showPaymentTimelineModal = true;
    this.loadPaymentTimeline(tenant);
  }

  private loadPaymentTimeline(tenant: Tenant): void {
    this.timelineLoading = true;
    this.paymentTimeline = [];

    // Find the contract for this tenant
    let tenantContract = null;

    if (tenant.id) {
      // First, try to find by contract ID if available
      if ((tenant as any).contractId) {
        tenantContract = this.contracts.find(
          (contract) => contract.id === (tenant as any).contractId
        );
      }

      // Fallback: find by tenant ID
      if (!tenantContract) {
        tenantContract = this.contracts.find(
          (contract) =>
            contract.tenantId === tenant.id || contract.tenant?.id === tenant.id
        );
      }

      // Also check draft contracts
      if (!tenantContract) {
        tenantContract = this.draftContracts.find(
          (contract) =>
            contract.tenantId === tenant.id || contract.tenant?.id === tenant.id
        );
      }
    }

    if (!tenantContract || !tenantContract.id) {
      this.timelineLoading = false;
      return;
    }

    // Use the real API to get payment timeline
    this.contractService
      .getPaymentTimeline(tenantContract.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (timeline) => {
          this.paymentTimeline = timeline;
          this.timelineLoading = false;
        },
        error: (error) => {
          console.error('Error loading payment timeline:', error);
          this.paymentTimeline = [];
          this.timelineLoading = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load payment timeline',
          });
        },
      });
  }

  // Timeline UI Helper Methods
  getTimelineMarkerClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'marker-paid';
      case 'soon':
        return 'marker-soon';
      case 'overdue':
        return 'marker-overdue';
      case 'upcoming':
        return 'marker-upcoming';

      default:
        return 'marker-default';
    }
  }

  getTimelineMarkerIcon(status: string): string {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'pi pi-check-circle';
      case 'soon':
        return 'pi pi-clock';
      case 'overdue':
        return 'pi pi-exclamation-triangle';
      case 'upcoming':
        return 'pi pi-calendar';

      default:
        return 'pi pi-circle';
    }
  }

  formatTimelineDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }
}
