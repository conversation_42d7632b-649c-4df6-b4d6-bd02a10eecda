export interface Tenant {
  id?: string; // For edits
  basicInfo: {
    fullName: string;
    email: string;
    phone: string;
    companyName?: string;
    jobTitle?: string;
    idNumber?: string;
    taxId?: string;
    registrationLinkMethod: 'Email' | 'WhatsApp';
  };
  contactPreferences: {
    preferredCommunication: 'Email' | 'Phone';
    emergencyContact: {
      name: string;
      phone: string;
    };
  };
  leaseDetails: {
    property: {
      buildingId: string; // Dropdown value
      floor: string;
      deskNumber?: string;
    };
    terms: {
      startDate: Date;
      endDate: Date;
      rentAmount: number;
      currency: string;
      securityDeposit: number;
      paymentDueDay: number; // e.g., 1 for 1st of month
      lateFeePolicy: string;
    };
    paymentMethod: {
      type: 'Bank Transfer' | 'Credit Card' | 'Cheque' | 'Other';
      billingCycle: 'Monthly' | 'Quarterly';
      autoPay: boolean;
      chequeImage?: string; // Optional download URL for cheque image
      chequeImagePath?: string; // Optional Firebase Storage path for cheque image
    };
  };
  additionalServices: {
    internet: boolean;
    cleaning: boolean;
    meetingRoom: boolean;
    parking: boolean;
    mailHandling: boolean;
  };
  legal: {
    agreementSigned: boolean;
    termsAccepted: boolean;
    privacyConsent: boolean;
  };
  adminNotes: {
    specialRequests?: string;
    moveInChecklist: string[];
  };
  ownerId?: string;
  managerId?: string;
  isDeleted: boolean;
}

export interface Office {
  officeId: string;
  officeNumber: number;
  branchId: string;
  status: 'available' | 'occupied' | 'under_processing' | 'maintenance';
  capacity?: number;
  amenities?: string[];
  monthlyRate?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum ContractStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  DELETED = 'deleted',
  PAUSED = 'paused', // For later logic to be added
}

export interface Contract {
  id?: string;
  contractNumber: string;
  status: ContractStatus;
  officeId: string;
  tenant?: Tenant;
  tenantId?: string; // Direct reference to tenant document ID for easier access
  startDate?: Date;
  endDate?: Date;
  rentAmount?: number;
  currency: string;
  securityDeposit?: number;
  paymentDueDay?: number;
  lateFeePolicy?: string;
  paymentMethod?: {
    type: 'Bank Transfer' | 'Credit Card' | 'Cheque' | 'Other';
    billingCycle: 'Monthly' | 'Quarterly' | 'Yearly';
    autoPay: boolean;
    chequeImage?: string; // Optional download URL for cheque image
    chequeImagePath?: string; // Optional Firebase Storage path for cheque image
  };
  // Payment tracking fields
  paidAt?: Date; // Date of the most recent payment
  nextPaymentAt?: Date; // The upcoming scheduled payment date
  paymentStatus?: string; // Computed status: 'Paid', 'Next Payment Soon', 'Overdue'
  additionalServices?: {
    internet: boolean;
    cleaning: boolean;
    meetingRoom: boolean;
    parking: boolean;
    mailHandling: boolean;
  };
  legal?: {
    agreementSigned: boolean;
    termsAccepted: boolean;
    privacyConsent: boolean;
    registrationLinkMethod?: 'Email' | 'WhatsApp';
  };
  adminNotes?: {
    specialRequests?: string;
    moveInChecklist: string[];
  };
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: string;
  lastModifiedBy?: string;
  currentStep?: number;
}

// Tenant as stored in database with metadata
export interface TenantFromDatabase extends Tenant {
  id: string;
  contractId: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  ownerId: string;
  managerId?: string;
}

// Payment Record Interface
export interface PaymentRecord {
  id?: string;
  contractId: string;
  tenantId: string;
  amount: number;
  currency: string;
  paymentDate: Date;
  dueDate: Date;
  paymentMethod: 'Bank Transfer' | 'Credit Card' | 'Cheque' | 'Cash' | 'Other';
  status: 'Paid' | 'Overdue' | 'Soon' | 'Upcoming';
  period: string; // e.g., "June 2025", "Q3 2025"
  transactionId?: string;
  notes?: string;
  chequeNumber?: string;
  chequeImagePath?: string;
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: string;
}

// Payment Timeline Event Interface (for display)
export interface PaymentTimelineEvent {
  id?: string;
  date: Date;
  dueDate?: Date;
  period: string; // e.g., "June 2025", "Q3 2025"
  status: 'Paid' | 'Overdue' | 'Soon' | 'Upcoming';
  amount: number;
  currency: string;
  paymentMethod?: string;
  notes?: string;
  transactionId?: string;
  chequeNumber?: string;
}
