<p-toolbar>
  <div class="p-toolbar-group-left">
    <h4 class="ms-3 mt-2">Meeting Rooms</h4>
  </div>

  <div class="p-toolbar-group-right">
    <p-button
      icon="pi pi-plus"
      label="New Meeting Room"
      class="me-2"
      data-bs-toggle="offcanvas"
      data-bs-target="#roomCanvas"
      aria-controls="roomExample"
    ></p-button>
  </div>
</p-toolbar>

<div class="card p-3 mt-3">
  <div class="mb-3 pb-3 border-bottom" *ngFor="let b of branches">
    <h5 class="mb-3">{{b.name}}</h5>
    <app-meting-rooms-list [branchId]="b.docid"></app-meting-rooms-list>
  </div>
</div>

<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="roomCanvas"
  aria-labelledby="roomCanvasLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="roomCanvasLabel">New Meeting Room</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body p-0">
    <app-add-room [branches]="branches"></app-add-room>
  </div>
</div>
