import { Component, Input } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-meting-rooms-list',
  templateUrl: './meting-rooms-list.component.html',
  styleUrls: ['./meting-rooms-list.component.scss'],
  providers: [MessageService]
})
export class MetingRoomsListComponent {
  @Input() branchId!: string;
  meetingRooms: any[] = [];

  constructor(
    private afs: AngularFirestore,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.getMeetingRooms();
  }

  getMeetingRooms() {
    this.afs
      .collection('meetingRooms', ref => ref.where('branchId', '==', this.branchId))
      .snapshotChanges()
      .subscribe((changes) => {
        this.meetingRooms = [];
        changes.map((change) => {
          this.meetingRooms.push({
            id: change.payload.doc.id,
            branchId: this.branchId,
            data: change.payload.doc.data(),
          });
        });
      });
  }
}
