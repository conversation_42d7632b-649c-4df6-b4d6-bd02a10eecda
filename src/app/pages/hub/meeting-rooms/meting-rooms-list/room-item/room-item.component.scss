.room-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(50%, 1fr));
  gap: 0;
  p {
    font-weight: 400 !important;
    font-size: 16px;
  }
}

.card-header {
  background: #fff;
  display: flex;
  vertical-align: middle;
  align-items: center;
  border-radius: 12px 12px 0 0;
  padding-bottom: 0;
}
@media screen and (min-width: 1200px) {
  .card-container {
    grid-template-columns: repeat(4, 1fr);
  }
}

.time-col {
  display: flex;
  flex-direction: column;
}

:host ::ng-deep {
  .p-dropdown {
    width: 100%;
    .p-dropdown-items {
      padding-left: 0;
    }
  }
  .p-calendar {
    width: 100%;
  }
  .dropdown .p-button {
    min-height: 36px;
    min-width: 32px;
    max-height: 36px;
    max-width: 32px;
    border-radius: 50% !important;
    line-height: 32px;
    text-align: center;
  }
  .card-footer {
    .p-button {
      width: 100%;
      border-radius: 0 0 8px 8px !important;
      transform: none !important;
      display: flex;
      justify-content: space-between;
    }
  }

  .times-details {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0;
  }
}

.times-details-item {
  width: 33% !important;
  text-align: center !important;
  border-right: 1px solid #ccc !important;
}
.times-details-item:last-child {
  border: none !important;
}
.booking-item {
  border-bottom: 1px solid #ccc;
  transition: 0.5s;
  padding: 8px;
  &:hover {
    background: #e7e8e9;
  }
}
.booking-item:last-child {
  border: none;
}
.company-details {
  vertical-align: middle;
  align-items: center;
}


.country-item {
  display: flex;
  vertical-align: middle;
  align-items: center;
  img {
    width: 36px;
    margin-right: 14px;
  }
}