<div class="card">
  <div class="card-header d-flex justify-content-between">
    <h6>{{ room.data.name }}</h6>
    <div class="dropdown">
      <button
        id="btn{{ room.id }}"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        pButton
        pRipple
        type="button"
        icon="pi pi-ellipsis-v"
        class="p-button-rounded p-button-secondary p-button-text p-button-sm"
      ></button>
      <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
        <li>
          <a class="dropdown-item" (click)="update()">Update</a>
        </li>
        <li><a class="dropdown-item" (click)="delete()">Delete</a></li>
      </ul>
    </div>
  </div>
  <div class="card-body">
    <div class="room-details">
      <p><i class="bi bi-bounding-box me-2"></i> {{ room.data.size }}</p>
      <p>
        <svg
          width="18"
          height="18"
          class="me-2"
          viewBox="0 0 512 512"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <rect width="512" height="512" fill="url(#pattern0)" />
          <defs>
            <pattern
              id="pattern0"
              patternContentUnits="objectBoundingBox"
              width="1"
              height="1"
            >
              <use xlink:href="#image0_83_3" transform="scale(0.00195312)" />
            </pattern>
            <image
              id="image0_83_3"
              width="512"
              height="512"
              xlink:href="data:image/png;base64,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"
            />
          </defs>
        </svg>
        {{ room.data.sits }} Sits
      </p>
      <p>
        <i class="bi bi-camera-reels me-2"></i> {{ room.data.withDataShow }}
      </p>
    </div>
  </div>
  <div class="card-footer p-0 relative">
    <button
      *ngIf="activeBookings.length === 0"
      (click)="op.toggle($event)"
      pButton
      pRipple
      type="button"
      class="p-button-success"
    >
      <span>Available</span>
      <span *ngIf="futureBookings.length !== 0"
        >Next at: {{ futureBookings[0].start | date : "hh:mm a" }}</span
      >
      <span *ngIf="futureBookings.length === 0">No Booking Later</span>
    </button>

    <p-overlayPanel #op [showCloseIcon]="false" [style]="{ width: '600px' }">
      <div class="col-12 p-0 mb-3 text-end">
        <a
          class="btn btn-primary"
          data-bs-toggle="offcanvas"
          href="#newBooking{{room.id}}"
          role="button"
          aria-controls="newBooking"
        >
          <i class="bi bi-plus me-3"></i> New Booking
      </a>
      </div>
      <p-accordion [multiple]="false">
        <p-accordionTab header="Active Bookings">
          <div
            class="alert m-3 alert-primary"
            *ngIf="activeBookings.length === 0"
            role="alert"
          >
            No Booking available
          </div>
          <div class="booking-item" *ngFor="let booking of activeBookings">
            <div class="company-details d-flex">
              <p-image
                src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/LEGO_logo.svg/2048px-LEGO_logo.svg.png"
                alt="Image"
                width="250"
                [preview]="true"
              ></p-image>
              <h6>Smart Target IT Solutions</h6>
              <!--<app-company-logo-name></app-company-logo-name>-->
            </div>
            <div class="times-details d-flex">
              <div class="times-details-item">
                <strong>Start:</strong> {{ booking.start | date : "hh:mm a" }}
              </div>
              <div class="times-details-item">
                <strong>End:</strong> {{ booking.end | date : "hh:mm a" }}
              </div>
              <div class="times-details-item">
                <strong>Date:</strong>
                {{ booking.start | date : "MMM, dd yyyy" }}
              </div>
            </div>
          </div>
        </p-accordionTab>
        <p-accordionTab header="Future Bookings">
          <div
            class="alert m-3 alert-primary"
            *ngIf="futureBookings.length === 0"
            role="alert"
          >
            No Booking available
          </div>
          <div class="booking-item" *ngFor="let booking of futureBookings">
            <div class="company-details d-flex">
              <p-image
                src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/LEGO_logo.svg/2048px-LEGO_logo.svg.png"
                alt="Image"
                width="250"
                [preview]="true"
              ></p-image>
              <h6>Smart Target IT Solutions</h6>
              <!--<app-company-logo-name></app-company-logo-name>-->
            </div>
            <div class="times-details d-flex">
              <div class="times-details-item">
                <strong>Start:</strong> {{ booking.start | date : "hh:mm a" }}
              </div>
              <div class="times-details-item">
                <strong>End:</strong> {{ booking.end | date : "hh:mm a" }}
              </div>
              <div class="times-details-item">
                <strong>Date:</strong>
                {{ booking.start | date : "MMM, dd yyyy" }}
              </div>
            </div>
          </div>
        </p-accordionTab>
        <p-accordionTab header="Previous Bookings">
          <div
            class="alert m-3 alert-primary"
            *ngIf="pastBookings.length === 0"
            role="alert"
          >
            No Booking available
          </div>
          <div class="booking-item" *ngFor="let booking of pastBookings">
            <div class="company-details d-flex">
              <p-image
                src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/LEGO_logo.svg/2048px-LEGO_logo.svg.png"
                alt="Image"
                width="250"
                [preview]="true"
              ></p-image>
              <h6>Smart Target IT Solutions</h6>
              <!--<app-company-logo-name></app-company-logo-name>-->
            </div>
            <div class="times-details d-flex">
              <div class="times-details-item">
                <strong>Start:</strong> {{ booking.start | date : "hh:mm a" }}
              </div>
              <div class="times-details-item">
                <strong>End:</strong> {{ booking.end | date : "hh:mm a" }}
              </div>
              <div class="times-details-item">
                <strong>Date:</strong>
                {{ booking.start | date : "MMM, dd yyyy" }}
              </div>
            </div>
          </div>
        </p-accordionTab>
      </p-accordion>
    </p-overlayPanel>

    <button
      *ngIf="activeBookings.length !== 0"
      (click)="op.toggle($event)"
      pButton
      pRipple
      type="button"
      class="p-button-danger"
    >
      <span>Busy</span>
      <span>available at: {{ activeBookings[0].end | date : "hh:mm a" }}</span>
    </button>
  </div>
</div>

<div
  class="offcanvas offcanvas-end"
  style="width: 100%; max-width: 800px"
  tabindex="-1"
  id="newBooking{{room.id}}"
  aria-labelledby="newBookingLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="newBookingLabel">{{ room.data.name }}</h5>
    <button
      type="button"
      class="btn-close text-reset"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body">
    <form>
      <p-dropdown
        [options]="clients"
        name="client"
        [(ngModel)]="selectedClient"
        optionLabel="name"
        [filter]="true"
        filterBy="name"
        [showClear]="true"
        placeholder="Select a Country"
      >
        <ng-template pTemplate="selectedItem">
          <div class="country-item country-item-value" *ngIf="selectedClient">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/LEGO_logo.svg/2048px-LEGO_logo.svg.png"
            />
            <div>{{ selectedClient.name }}</div>
          </div>
        </ng-template>
        <ng-template let-client pTemplate="item">
          <div class="country-item">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/LEGO_logo.svg/2048px-LEGO_logo.svg.png"
            />
            <div>{{ client.name }}</div>
          </div>
        </ng-template>
      </p-dropdown>
      <div class="mb-3 mt-4">
        <label for="BookingDate">Booking Date</label>
        <p-calendar [(ngModel)]="BookingDate" name="BookingDate" id="BookingDate" [showIcon]="true" inputId="icon"></p-calendar>
      </div>
      <div class="mb-3 mx-0 px-0 row col-12">
        <div class="col-sm-6 ps-sm-0 time-col">
          <label for="timeonly1">Start at</label>
          <p-calendar [(ngModel)]="startBooking" name="startBooking" [timeOnly]="true" [hourFormat]="'12'" inputId="timeonly1"></p-calendar>
        </div>
        <div class="col-sm-6 pe-sm-0 time-col">
          <label for="timeonly2">Start at</label>
          <p-calendar [(ngModel)]="endBooking" name="endBooking" [timeOnly]="true" [hourFormat]="'12'" inputId="timeonly2"></p-calendar>
        </div>
      </div>

      <div class="d-flex mx-0 justify-content-between">
        <button pButton pRipple type="button" label="Save" class="" (click)="addBooking()"></button>
        <button pButton pRipple type="button" label="Cancel" data-bs-dismiss="offcanvas" aria-label="Close" class="p-button-raised p-button-secondary p-button-text"></button>
      </div>
    </form>
  </div>
</div>
