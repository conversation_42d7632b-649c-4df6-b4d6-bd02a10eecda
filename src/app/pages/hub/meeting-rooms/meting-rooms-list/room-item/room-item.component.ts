import { Component, Input, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';

@Component({
  selector: 'app-room-item',
  templateUrl: './room-item.component.html',
  styleUrls: ['./room-item.component.scss'],
})
export class RoomItemComponent implements OnInit {
  @Input() room: any;
  bookings: any[] = [];
  clients: any[] = [
    {id: '001', name: 'Smart Target IT Solutions'},
    {id: '002', name: 'IT Stackers Solutions'},
    {id: '003', name: 'ELITE Solutions'},
    {id: '004', name: 'Ideal Solutions'},
  ];
  selectedClient: any;
  BookingDate: Date = new Date();
  startBooking: any;
  endBooking: any

  //futureBookings: any[] = [];

  constructor(
    private afAuth: AngularFireAuth, 
    private afs: AngularFirestore,
    ) {}

  ngOnInit() {
    this.bookings = this.room.data.bookings;
    console.log(this.bookings);
    this.BookingDate = new Date()
  }

  get futureBookings(): any[] {
    const now = new Date();
    return this.bookings.filter((booking) => new Date(booking.start) > now);
  }

  get pastBookings(): any[] {
    const now = new Date();
    return this.bookings.filter((booking) => new Date(booking.end) < now);
  }

  get activeBookings(): any[] {
    const now = new Date();
    return this.bookings.filter(
      (booking) => new Date(booking.start) < now && new Date(booking.end) > now
    );
  }

  addBooking() {
    this.afAuth.authState.subscribe(user => {
      if(user) {
        const newBooking = {
          companyId: this.selectedClient.id,
          Date: this.BookingDate.toString(),
          start: this.startBooking.toString(),
          end: this.endBooking.toString()
        }
        this.bookings.push(newBooking);
        this.afs.collection('meetingRooms').doc(this.room.branchId).collection('rooms').doc(this.room.id).update({
          bookings: this.bookings
        }).then(() => {
          console.log('Updated Successfully')
        })
      }
    })
  }

  delete() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        console.log(this.room.id);
        console.log(user.uid)
        this.afs
          .collection('meetingRooms')
          .doc(this.room.branchId)
          .collection('rooms')
          .doc(this.room.id)
          .delete()
          .then(() => {
            console.log('Room deleted successfully');
          })
          .catch((error) => {
            console.error('Error deleting room: ', error);
          });
      }
    });
  }
  update() {}
}
