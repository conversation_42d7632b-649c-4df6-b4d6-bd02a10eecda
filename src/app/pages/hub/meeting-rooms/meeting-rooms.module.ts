import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MeetingRoomsRoutingModule } from './meeting-rooms-routing.module';
import { MeetingRoomsComponent } from './meeting-rooms.component';
import { PrimengModule } from 'src/app/shared/modules/primeng.module';
import { AddRoomComponent } from './add-room/add-room.component';
import { MetingRoomsListComponent } from './meting-rooms-list/meting-rooms-list.component';
import { RoomItemComponent } from './meting-rooms-list/room-item/room-item.component';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    MeetingRoomsComponent,
    AddRoomComponent,
    MetingRoomsListComponent,
    RoomItemComponent
  ],
  imports: [
    CommonModule,
    MeetingRoomsRoutingModule,
    PrimengModule,
    FormsModule
  ]
})
export class MeetingRoomsModule { }
