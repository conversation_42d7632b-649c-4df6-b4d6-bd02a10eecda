<div class="form-container">
    <div class="col-12 p-3">
        <label for="room-name">Select the Branch</label>
        <p-dropdown [options]="branches" [(ngModel)]="selectedBranch" class="col-12" placeholder="Select a Branch" optionLabel="name" [showClear]="true"></p-dropdown>
    </div>
    <form [formGroup]="form" class="p-3" *ngIf="selectedBranch !== undefined && selectedBranch !== null">
        <span class="col-12">
            <label for="room-name">Room Name</label>
            <input id="room-name" type="text" class="col-12 mb-4" pInputText formControlName="name"> 
        </span>

        <div class="row mx-0 col-12 select-3-btns">
            <span class="col-md-6 ps-0 mb-4">
                <label for="minmax-buttons">Number of Sits</label>
                <p-inputNumber mode="decimal" [showButtons]="true" class="col-12" inputId="minmax-buttons" [min]="0" [max]="50" formControlName="sits">
                </p-inputNumber>
            </span>
    
            <div class="col-md-6 mb-4 pe-0 select-btns">
                <label for="minmax-buttons">Room Size</label>
                <p-selectButton [options]="sizeOptions" formControlName="size" class="col-12" optionLabel="label" optionValue="value"></p-selectButton>
            </div>
        </div>

        <div class="row mx-0 select-2-btns col-12">
            <span class="col-md-6 ps-0 mb-4">
                <label for="yesNo">Available</label>
                <p-selectButton [options]="yesNoOptions" id="yesNo" formControlName="available" optionLabel="label" optionValue="value"></p-selectButton>
            </span>
            <span class="col-md-6 pe-0 mb-4">
                <label for="withdatashow">With Datashow/TV</label>
                <p-selectButton [options]="yesNoOptions" id="withdatashow" formControlName="withDataShow" optionLabel="label" optionValue="value"></p-selectButton>
            </span>
        </div>

        <div class="form-footer d-flex justify-content-between px-3 pb-3">
            <div>
                <button pButton pRipple type="button" (click)="addRoom()" label="Save"></button>
            </div>
            <div>
                <button pButton pRipple type="button" data-bs-dismiss="offcanvas" aria-label="Close" label="Cancel" class="p-button-secondary p-button-text"></button>
            </div>
        </div>
    </form>
</div>