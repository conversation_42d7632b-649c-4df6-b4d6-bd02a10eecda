import { Component, Input, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-add-room',
  templateUrl: './add-room.component.html',
  styleUrls: ['./add-room.component.scss'],
})
export class AddRoomComponent implements OnInit {
  @Input() branches!: any[];
  form: FormGroup;
  selectedBranch: any;
  

  sizeOptions: any[] = [
    { label: 'Small', value: 'sm' },
    { label: 'Medium', value: 'md' },
    { label: 'Big', value: 'lg' },
  ];
  yesNoOptions: any[] = [{label: 'Yes', value: true}, {label: 'No', value: false}];

  constructor(private afs: AngularFirestore) {
    this.form = new FormGroup({
      name: new FormControl('', Validators.required),
      size: new FormControl('small', Validators.required),
      available: new FormControl(true),
      sits: new FormControl(4),
      withDataShow: new FormControl(false),
      bookStart: new FormControl(null),
      bookEnd: new FormControl(null)
    });
  }
  ngOnInit() {}

  addRoom() {
    const docid = this.afs.createId();

    const BranchDocRef = this.afs.doc(
      `meetingRooms/${docid}`
    );

    BranchDocRef.set({
      branchId: this.selectedBranch.docid,
      name: this.form.value.name,
      size: this.form.value.size,
      available: this.form.value.available,
      sits: this.form.value.sits,
      withDataShow: this.form.value.withDataShow,
      bookStart:this.form.value.bookStart,
      bookEnd:this.form.value.bookEnd,
      bookings: []
    }).then((res) => {
      console.log(res);
    });
  }
}
