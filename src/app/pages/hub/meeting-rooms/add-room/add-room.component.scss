.p-float-label {
    margin-top: 16px;
}

.form-container {
    width: 100%;
    form {
        height: calc(100vh - 175px);
        width: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        position: relative;
        
        .form-footer {
            height: 62px;
            bottom: 0;
            left: 0;
            right: 0;
            position: absolute;
        }
    }
}

:host ::ng-deep {
    .p-dropdown {
        width: 100%;
    }

    .select-btns {
        .p-button {
            transition: none !important;
            transform: none !important;
            &:hover {
                margin-top: 0;
            }
        }
    }

    .p-buttonset .p-button:first-of-type {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }
    .p-buttonset .p-button:last-of-type {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
    .p-buttonset .p-button:not(:first-of-type):not(:last-of-type) {
        border-radius: 0 !important;
    }

    .p-inputnumber-button-up {
        transform: none !important;
        border-radius: 0 6px 0 0 !important;
    }
    .p-inputnumber-button-down {
        transform: none !important;
        border-radius: 0 0 6px 0 !important;
    }

    .p-inputnumber-buttons-stacked {
        width: 100%;
    }
    .select-3-btns {
        .p-selectbutton {
            .p-button:nth-child(1),
            .p-button:nth-child(3) {
                width: 33%;
            }
            .p-button:nth-child(2) {
                width: 34%;
            }
            .p-button {
                height: 51.55px;
                margin-top: 1px;
            }
        }
    }
    .select-2-btns {
        .p-selectbutton .p-button {
            width: 50%;
            transform: none !important;
        }
    }
}