import { Component } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';

@Component({
  selector: 'app-meeting-rooms',
  templateUrl: './meeting-rooms.component.html',
  styleUrls: ['./meeting-rooms.component.scss'],
})
export class MeetingRoomsComponent {
  branches: any[] = [];

  constructor(private afs: AngularFirestore, private afAuth: AngularFireAuth) {}
  ngOnInit() {
    this.getBranches();

    console.log(new Date());
  }

  getBranches() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        // Query for branches where 'uid' is user.uid
        const query1 = this.afs
          .collection('branches', (ref) => ref.where('uid', '==', user.uid))
          .snapshotChanges();

        // Query for branches where 'managerId' is user.uid
        const query2 = this.afs
          .collection('branches', (ref) =>
            ref.where('managerId', '==', user.uid)
          )
          .snapshotChanges();

        // Combine the results of both queries
        query1.subscribe((changes1) => {
          const branches1 = changes1.map((change) => change.payload.doc.data());

          query2.subscribe((changes2) => {
            const branches2 = changes2.map((change) =>
              change.payload.doc.data()
            );

            // Combine the two arrays and remove duplicates
            const combinedBranches = [...branches1, ...branches2];
            this.branches = this.removeDuplicates(combinedBranches, 'docid'); // Assumes branches have a unique 'id' field
            console.log('ooooopppppp',this.branches);
          });
        });
      }
    });
  }

  removeDuplicates(arr: any[], key: string) {
    const seen = new Set();
    return arr.filter((item) => {
      const k = item[key];
      return seen.has(k) ? false : seen.add(k);
    });
  }
}
