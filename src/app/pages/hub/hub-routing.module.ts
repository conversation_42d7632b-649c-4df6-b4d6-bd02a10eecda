import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './dashboard/dashboard.component';

const routes: Routes = [
  { path: '', component: DashboardComponent },
  {
    path: 'profile',
    loadChildren: () =>
      import('./profile/profile.module').then((m) => m.ProfileModule),
  },
  {
    path: 'dashboard',
    loadChildren: () =>
      import('./dashboard/dashboard.module').then((m) => m.DashboardModule),
  },
  {
    path: 'branches',
    loadChildren: () =>
      import('./branches/branches.module').then((m) => m.BranchesModule),
  },
  {
    path: 'offices',
    loadChildren: () =>
      import('./offices/offices.module').then((m) => m.OfficesModule),
  },
  {
    path: 'meeting-rooms',
    loadChildren: () =>
      import('./meeting-rooms/meeting-rooms.module').then(
        (m) => m.MeetingRoomsModule
      ),
  },
  {
    path: 'resources',
    loadChildren: () =>
      import('./resources/resources.module').then((m) => m.ResourcesModule),
  },
  {
    path: 'inventory',
    loadChildren: () =>
      import('./resources/inventory/inventory.module').then((m) => m.InventoryModule),
  },
  {
    path: 'pantry',
    loadChildren: () =>
      import('./resources/pantry/pantry.module').then((m) => m.PantryModule),
  },
  {
    path: 'supliers',
    loadChildren: () =>
      import('./resources/supliers/supliers.module').then((m) => m.SupliersModule),
  },

  { path: 'clients', loadChildren: () => import('./clients/clients.module').then(m => m.ClientsModule) },
  { path: 'documents', loadChildren: () => import('./documents/documents.module').then(m => m.DocumentsModule) },
  { path: 'hr', loadChildren: () => import('./hr/hr.module').then(m => m.HrModule) },
  { path: 'accounting', loadChildren: () => import('./accounting/accounting.module').then(m => m.AccountingModule) },
  { path: 'reports', loadChildren: () => import('./reports/reports.module').then(m => m.ReportsModule) },
  { path: 'maintenance', loadChildren: () => import('./maintenance/maintenance.module').then(m => m.MaintenanceModule) },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HubRoutingModule {}
