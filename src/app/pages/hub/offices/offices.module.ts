import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { OfficesRoutingModule } from './offices-routing.module';
import { OfficesComponent } from './offices.component';
import { PrimengModule } from 'src/app/shared/modules/primeng.module';
import { BranchesModule } from '../branches/branches.module';
import { TagModule } from 'primeng/tag';

@NgModule({
  declarations: [
    OfficesComponent,

  ],
  imports: [
    CommonModule,
    OfficesRoutingModule,
    PrimengModule,
    BranchesModule,
    TagModule
  ]
})
export class OfficesModule { }
