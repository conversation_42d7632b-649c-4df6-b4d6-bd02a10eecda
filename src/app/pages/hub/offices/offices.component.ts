// offices.component.ts
import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { ConfirmationService, MessageService } from 'primeng/api';
import { FormControl, FormGroup, Validators } from '@angular/forms';

interface OfficeLog {
  action: string;
  officeId: string;
  officeNumber: number;
  branchId: string;
  branchName: string;
  performedBy: string;
  performedByName: string;
  performedAt: Date;
  oldData?: any;
    newData?: any;
  reason?: string;
}
interface FieldConfig {
  key: string;
  label: string;
  format?: (val: any) => string;
}

interface ChangedField {
  label: string;
  oldValue: string;
  newValue: string;
  changed: boolean;
}

@Component({
  selector: 'app-offices',
  templateUrl: './offices.component.html',
  styleUrls: ['./offices.component.scss'],
  providers: [ConfirmationService, MessageService]
})
export class OfficesComponent implements OnInit {
  branches: any[] = [];
  selectedBranch: any;
  officeLogs: OfficeLog[] = [];
  addSectorView: boolean = false;
  addResourceView: boolean = false;
  addFunctionalityView: boolean = false;
  currentUserBranchId: string | null = null;
  isRegularUser: boolean = false;
  userRole: string = '';
  expandedRowKeys: any = {};
  showChangesSidebar: boolean = false;
  selectedBranchForLogs: any;
  sectorSidebarVisible: boolean = false;
  selectedBranchForSectors: any;
  sectors: any[] = [];
  editingSector: any = null;
  showOfficeLogsSidebar: boolean = false;

  // Forms
  sectorForm = new FormGroup({
    name: new FormControl('', Validators.required),
    description: new FormControl('')
  });

  resourceForm = new FormGroup({
    name: new FormControl('', Validators.required),
    type: new FormControl(''),
    quantity: new FormControl(1, Validators.min(1))
  });

  functionalityForm = new FormGroup({
    name: new FormControl('', Validators.required),
    description: new FormControl('')
  });

  constructor(
    private afs: AngularFirestore,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private afAuth: AngularFireAuth
  ) {}

  ngOnInit() {
    this.checkUserType();
  }

  checkUserType() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.afs.collection('users').doc(user.uid).get().subscribe((userDoc) => {
          const userData = userDoc.data() as any;
          this.userRole = userData?.role || '';

          if (userData?.branchId) {
            this.currentUserBranchId = userData.branchId;
            this.isRegularUser = true;
            this.getSingleBranch(userData.branchId);
          } else {
            this.isRegularUser = false;
            this.getBranches(user.uid);
          }
        });
      }
    });
  }

  getBranches(userId: string) {
    const query1 = this.afs.collection('branches', ref => ref.where('uid', '==', userId)).snapshotChanges();
    const query2 = this.afs.collection('branches', ref => ref.where('managerId', '==', userId)).snapshotChanges();

    query1.subscribe((changes1) => {
      const branches1 = changes1.map((change) => ({
        ...change.payload.doc.data(),
        docid: change.payload.doc.id
      }));

      query2.subscribe((changes2) => {
        const branches2 = changes2.map((change) => ({
          ...change.payload.doc.data(),
          docid: change.payload.doc.id
        }));

        this.branches = this.removeDuplicates([...branches1, ...branches2], 'docid');
      });
    });
  }

  getSingleBranch(branchId: string) {
    this.afs.collection('branches').doc(branchId).get().subscribe((branch) => {
      if (branch.exists) {
        const branchData = branch.data();
        if (branchData) {
          this.branches = [{ ...branchData, docid: branch.id }];
          this.selectedBranch = this.branches[0];
        }
      }
    });
  }

async getOfficeLogs(branchId: string) {
  if (!branchId) {
    this.messageService.add({
      severity: 'warn',
      summary: 'Warning',
      detail: 'No branch selected'
    });
    return;
  }

  this.showOfficeLogsSidebar = true;
  this.officeLogs = [];

  try {
    // Load sectors for this specific branch
    await this.loadSectorsforlogs(branchId);

    const logs = await this.afs.collection<OfficeLog>('Offices-Logs', ref =>
      ref.where('branchId', '==', branchId)
         .orderBy('performedAt', 'desc')
         .limit(50)
    ).get().toPromise();

    if (!logs) throw new Error('Failed to load logs');

    this.officeLogs = logs.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        performedAt: (data.performedAt as any).toDate()
      };
    });

    await this.loadReceptionistNames();

  } catch (error) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load office logs',
      life: 5000
    });
    console.error('Error loading office logs:', error);
  }
}

private loadReceptionistNames() {
  this.officeLogs.forEach(log => {
    if (!log.performedByName) {
      this.afs.collection('users').doc(log.performedBy).get().subscribe(userDoc => {
        const userData = userDoc.data() as any;
        log.performedByName = userData?.displayName || 'Unknown';
      });
    }
  });
}
showBranchSelectionDialog: boolean = false;

handleViewChangesClick() {
  if (this.branches.length === 1) {
    this.selectedBranchForLogs = this.branches[0];
    this.getOfficeLogs(this.branches[0].docid);
  } else {
    this.showOfficeLogsSidebar = true;
    if (this.branches.length > 0) {
      this.selectedBranchForLogs = this.branches[0]; // Default to first branch
      this.getOfficeLogs(this.branches[0].docid);
    }
  }
}

onBranchForLogsSelected() {
  if (this.selectedBranchForLogs) {
    this.showBranchSelectionDialog = false;
    this.getOfficeLogs(this.selectedBranchForLogs.docid);
  }
}
// Update the view changes button click handler in your template
showChangesForSelectedBranch() {
  if (this.branches.length > 1 && !this.selectedBranchForLogs) {
    this.messageService.add({
      severity: 'warn',
      summary: 'Select Branch',
      detail: 'Please select a branch to view changes'
    });
    return;
  }
  this.getOfficeLogs(this.selectedBranchForLogs?.docid);
}

getChangedFields(log: OfficeLog): ChangedField[] {
  const fields: ChangedField[] = [];

  const oldData = log.oldData || {};
  const newData = log.newData || {};

  // Common office fields to display, now including sectorId
  const fieldConfig: FieldConfig[] = [
    { key: 'number', label: 'Office Number' },
    { key: 'status', label: 'Status', format: (val: string) => this.formatStatus(val) },
    { key: 'price', label: 'Price', format: (val: number) => val ? `${val} OAR` : 'Not set' },
    { key: 'surface', label: 'Surface Area', format: (val: number) => val ? `${val} m²` : 'Not set' },
    { key: 'renterName', label: 'Renter' },
    { key: 'sectorId', label: 'Sector', format: (val: string) => this.getSectorName(val) }, // Added
    { key: 'imageUrl', label: 'Image', format: (val: string) => val ? 'Image exists' : 'No image' }
  ];

  fieldConfig.forEach(config => {
    const oldVal = oldData[config.key];
    const newVal = newData[config.key];

    if (log.action === 'create') {
      if (newVal !== undefined) {
        fields.push({
          label: config.label,
          oldValue: '',
          newValue: config.format ? config.format(newVal) : String(newVal),
          changed: true
        });
      }
    } else if (log.action === 'edit') {
      const changed = JSON.stringify(oldVal) !== JSON.stringify(newVal);
      if (changed) {
        fields.push({
          label: config.label,
          oldValue: config.format ? config.format(oldVal) : (oldVal !== undefined ? String(oldVal) : 'Empty'),
          newValue: config.format ? config.format(newVal) : (newVal !== undefined ? String(newVal) : 'Empty'),
          changed: true
        });
      }
    }
  });

  return fields;
}
getSectorName(sectorId: string | null | undefined): string {
  if (!sectorId) return 'No sector';

  const sector = this.sectors.find(s => s.id === sectorId);
  if (!sector) {
    console.warn('Sector not found for ID:', sectorId);
    return 'Unknown sector';
  }
  return sector.name;
}
formatStatus(status: string): string {
  if (!status) return "";
  return status === 'under_process' ? 'Under Process' : status.charAt(0).toUpperCase() + status.slice(1);
}


  addResource() {
    if (this.resourceForm.invalid || !this.selectedBranch) return;

    this.afAuth.authState.subscribe(user => {
      if (user) {
        const resources = this.selectedBranch.resources || [];
        const newResource = {
          name: this.resourceForm.value.name,
          type: this.resourceForm.value.type,
          quantity: this.resourceForm.value.quantity,
          createdAt: new Date(),
          createdBy: user.uid
        };

        resources.push(newResource);

        this.afs.collection('branches').doc(this.selectedBranch.docid).update({
          resources: resources
        }).then(() => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Resource added successfully'
          });
          this.resourceForm.reset();
          this.addResourceView = false;
        });
      }
    });
  }

  addFunctionality() {
    if (this.functionalityForm.invalid || !this.selectedBranch) return;

    this.afAuth.authState.subscribe(user => {
      if (user) {
        const functionalities = this.selectedBranch.functionalities || [];
        const newFunctionality = {
          name: this.functionalityForm.value.name,
          description: this.functionalityForm.value.description,
          createdAt: new Date()
        };

        functionalities.push(newFunctionality);

        this.afs.collection('branches').doc(this.selectedBranch.docid).update({
          functionalities: functionalities
        }).then(() => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Functionality added successfully'
          });
          this.functionalityForm.reset();
          this.addFunctionalityView = false;
        });
      }
    });
  }

  removeDuplicates(arr: any[], key: string) {
    const seen = new Set();
    return arr.filter(item => {
      const k = item[key];
      return seen.has(k) ? false : seen.add(k);
    });
  }

  onBranchSelect(branch: any) {
    this.selectedBranch = branch;
    this.getOfficeLogs(branch.docid);
  }
  async addSector() {
  if (this.sectorForm.invalid || !this.selectedBranchForSectors) return;

  try {
    const sectorData = {
      name: this.sectorForm.value.name,
      description: this.sectorForm.value.description,
      branchId: this.selectedBranchForSectors.docid,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add to Firestore
    const sectorRef = await this.afs.collection('sectors').add(sectorData);

    // Update the sectors array
    this.sectors = [...this.sectors, { id: sectorRef.id, ...sectorData }];

    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Sector added successfully'
    });

    this.sectorForm.reset();
  } catch (error) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to add sector'
    });
    console.error('Error adding sector:', error);
  }
}

// Add these new methods
openSectorSidebar() {
  this.sectorSidebarVisible = true;
  this.selectedBranchForSectors = this.branches.length === 1 ? this.branches[0] : null;
  this.sectors = [];
  this.editingSector = null;
  this.sectorForm.reset();

  if (this.branches.length === 1) {
    this.loadSectors();
  }
}
async loadSectorsforlogs(branchId: string): Promise<void> {
  try {
    const sectorsSnapshot = await this.afs.collection('sectors', ref =>
      ref.where('branchId', '==', branchId)
         .orderBy('createdAt', 'desc')
    ).get().toPromise();

    if (sectorsSnapshot) {
      this.sectors = sectorsSnapshot.docs.map(doc => {
        const data = doc.data() as { [key: string]: any };
        return {
          id: doc.id,
          ...data
        };
      });
    }
  } catch (error) {
    console.error('Error loading sectors:', error);
    this.sectors = [];
    throw error;
  }
}
async loadSectors() {
  if (!this.selectedBranchForSectors) return;

  try {
    const sectorsSnapshot = await this.afs.collection('sectors', ref =>
      ref.where('branchId', '==', this.selectedBranchForSectors.docid)
         .orderBy('createdAt', 'desc')
    ).get().toPromise();

    if (!sectorsSnapshot) {
      throw new Error('Failed to get sectors snapshot');
    }

    this.sectors = sectorsSnapshot.docs.map(doc => {
      const data = doc.data() as { [key: string]: any }; // Type assertion
      return {
        id: doc.id,
        ...data
      };
    });
  } catch (error) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load sectors'
    });
    console.error('Error loading sectors:', error);
    this.sectors = []; // Reset sectors on error
  }
}

editSector(sector: any) {
  this.editingSector = sector;
  this.sectorForm.patchValue({
    name: sector.name,
    description: sector.description
  });
}

async updateSector() {
  if (this.sectorForm.invalid || !this.editingSector) return;

  try {
    const updateData = {
      name: this.sectorForm.value.name,
      description: this.sectorForm.value.description,
      updatedAt: new Date()
    };

    await this.afs.collection('sectors').doc(this.editingSector.id).update(updateData);

    // Update local sectors array
    const index = this.sectors.findIndex(s => s.id === this.editingSector.id);
    if (index !== -1) {
      this.sectors[index] = {
        ...this.sectors[index],
        ...updateData
      };
      this.sectors = [...this.sectors]; // Trigger change detection
    }

    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Sector updated successfully'
    });

    this.cancelEdit();
  } catch (error) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to update sector'
    });
    console.error('Error updating sector:', error);
  }
}

cancelEdit() {
  this.editingSector = null;
  this.sectorForm.reset();
}

confirmDeleteSector(sector: any) {
  this.confirmationService.confirm({
    message: `Are you sure you want to delete "${sector.name}" sector?`,
    header: 'Confirm Deletion',
    icon: 'pi pi-exclamation-triangle',
    accept: () => {
      this.deleteSector(sector);
    }
  });
}

async deleteSector(sector: any) {
  try {
    await this.afs.collection('sectors').doc(sector.id).delete();

    // Remove from local sectors array
    this.sectors = this.sectors.filter(s => s.id !== sector.id);

    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Sector deleted successfully'
    });
  } catch (error) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to delete sector'
    });
    console.error('Error deleting sector:', error);
  }
}
getSeverityForAction(action: string): string {
  switch (action.toLowerCase()) {
    case 'create': return 'success';
    case 'edit': return 'info';
    case 'delete': return 'danger';
    default: return 'warning';
  }
}
}
