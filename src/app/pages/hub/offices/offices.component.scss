.resources-list {
    display: flex;
    flex-direction: column;

    .resource-item {
        vertical-align: middle;
        align-items: center;
        margin-bottom: 14px;
        .image-box {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            margin-right: 8px;
            overflow: hidden;
            background: #e7e8e9;
            display: flex;
            vertical-align: middle;
            align-items: center;
            justify-content: center;
            text-align: center;
            img {
                width: 80%;
                height: 80%;
                object-fit: cover;
            }
        }

        .resource-details {
            height: 100%;
            display: flex;
            vertical-align: middle;
            align-items: center;
            .resource-color {
                right: 10px;
                top: 0;
                bottom: 0;
                position: absolute;
                width: 80px;
                height: 80px;
                margin: auto 0;
            }
        }

        &:hover {
            .delete-resource-btn {
                display: block !important;
            }
        }
    }
}

.delete-resource-btn {
    top: 10px;
    right: 10px;
    position: absolute;
    background: #ffeded;
    display: none;
    &::after {
        display: none;
    }
}
:host ::ng-deep {
    .p-dropdown {
        width: 100%;
    }
}

.list-group-item {
    position: relative;
    display: flex;
    vertical-align: middle;
    align-items: center;
}.changes-container {
  max-height: calc(100vh - 120px);
  overflow-y: auto;

  .change-item {
    border-left: 4px solid;

    &.bg-blue-50 {
      border-left-color: var(--blue-500);
    }

    &.bg-red-50 {
      border-left-color: var(--red-500);
    }
  }
}


.change-details {
  border-top: 1px dashed #e5e7eb;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}
.line-through {
  text-decoration: line-through;
  opacity: 0.7;
}

.text-green-500 {
  color: var(--green-500);
}

.change-item {
  transition: all 0.2s;
}

.change-item:hover {
  box-shadow: 0 0 0 1px var(--primary-color);
}
::ng-deep .p-dropdown-panel {
  .p-dropdown-items {
    padding: 0; // Remove default padding that might cause misalignment

    .p-dropdown-item {
      padding:1; // Remove default item padding


  }
}}
