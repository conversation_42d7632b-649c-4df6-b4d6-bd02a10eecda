<p-toolbar>
  <div class="p-toolbar-group-left">
    <h4 class="ms-3 mt-2">Offices</h4>
  </div>

  <div class="p-toolbar-group-right" *ngIf="branches.length > 0">
    <!-- View Changes Button (for owner/manager) -->
<p-button
  icon="pi pi-history"
  label="View Changes"
  styleClass="p-button-help me-2"
  (click)="handleViewChangesClick()"
  *ngIf="userRole === 'owner' || userRole === 'manager'"
></p-button>

    <!-- Add Sector Button -->
<p-button
  icon="pi pi-plus"
  label="Add Sector"
  styleClass="p-button-success me-2"
  (click)="openSectorSidebar()"
  *ngIf="userRole === 'owner' || userRole === 'manager' || userRole === 'receptionist'"
></p-button>

    <!-- Add Resource Button -->
    <p-button
      icon="pi pi-box"
      label="Add Resource"
      styleClass="p-button-warning me-2"
      (click)="addResourceView = true"
                  *ngIf="userRole === 'owner' || userRole === 'manager' || userRole === 'receptionist' "

    ></p-button>

    <!-- Add Functionality Button -->

  </div>
</p-toolbar>
<div class="main-container mt-4">
  <p-table
    [value]="branches"
    selectionMode="single"
    dataKey="docid"
    [tableStyle]="{ 'min-width': '60rem' }"
  >
    <ng-template pTemplate="header">
      <tr>
        <th style="width: 5rem" *ngIf="!isRegularUser"></th>
        <th>Name</th>
        <th>Address</th>
        <th *ngIf="!isRegularUser"></th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-branch let-expanded="expanded">
      <tr>
        <!-- Hide toggle button for regular users -->
        <td *ngIf="!isRegularUser">
          <button
            type="button"
            pButton
            pRipple
            [pRowToggler]="branch"
            class="p-button-text p-button-rounded p-button-plain"
            [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
          ></button>
        </td>
        <td>{{ branch.name }}</td>
        <td>{{ branch.address }}</td>
        <td *ngIf="!isRegularUser">

        </td>
      </tr>
      <!-- Auto-expand row for regular users -->
<tr *ngIf="isRegularUser">
  <td colspan="4">
    <div class="p-3">
      <app-details [branch]="branch" [isRegularUser]="isRegularUser" [userRole]="userRole"></app-details>
    </div>
  </td>
</tr>
    </ng-template>
<ng-template pTemplate="rowexpansion" let-branch *ngIf="!isRegularUser">
  <tr>
    <td colspan="4">
      <div class="p-3">
        <app-details [branch]="branch" [isRegularUser]="isRegularUser"></app-details>
      </div>
    </td>
  </tr>
</ng-template>
  </p-table>
</div>



<p-toast></p-toast>
<p-confirmPopup></p-confirmPopup>

<!-- Only show these offcanvas elements for admin/manager users -->
<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="employeesCanvas"
  aria-labelledby="employeesCanvasLabel"
  *ngIf="!isRegularUser"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="employeesCanvasLabel">
      Branches Employees
    </h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body">
    <div>
      Some text as placeholder. In real life you can have the elements you have
      chosen. Like, text, images, lists, etc.
    </div>
    <div class="dropdown mt-3">
      <button
        class="btn btn-secondary dropdown-toggle"
        type="button"
        data-bs-toggle="dropdown"
      >
        Dropdown button
      </button>
      <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#">Action</a></li>
        <li><a class="dropdown-item" href="#">Another action</a></li>
        <li><a class="dropdown-item" href="#">Something else here</a></li>
      </ul>
    </div>
  </div>
</div>

<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="locationsCanvas"
  aria-labelledby="locationsCanvasLabel"
  *ngIf="!isRegularUser"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="locationsCanvasLabel">Branches Location</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body p-0">
    <iframe
      src="https://www.google.com/maps/embed?pb=!1m16!1m12!1m3!1d145665.***********!2d51.34617190504759!3d25.244170153732828!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!2m1!1shub%20business%20!5e0!3m2!1sen!2sqa!4v1700936005566!5m2!1sen!2sqa"
      height="600"
      style="border: 0; width: 100% !important"
      allowfullscreen=""
      loading="lazy"
      referrerpolicy="no-referrer-when-downgrade"
    ></iframe>
  </div>
</div>
<!-- Add Sector Dialog -->
<p-dialog header="Add New Sector" [(visible)]="addSectorView" [style]="{width: '500px'}" [modal]="true">
  <form [formGroup]="sectorForm" (ngSubmit)="addSector()">
    <div class="field mb-3">
      <label for="sectorName" class="block">Sector Name *</label>
      <input id="sectorName" type="text" pInputText formControlName="name" class="w-full">
    </div>
    <div class="field mb-3">
      <label for="sectorDesc" class="block">Description</label>
      <textarea id="sectorDesc" pInputTextarea formControlName="description" rows="3" class="w-full"></textarea>
    </div>
    <div class="flex justify-content-end">
      <button pButton pRipple type="button" label="Cancel" class="p-button-text" (click)="addSectorView = false"></button>
      <button pButton pRipple type="submit" label="Save" class="ml-2" [disabled]="sectorForm.invalid"></button>
    </div>
  </form>
</p-dialog>

<!-- Add Resource Dialog -->
<p-dialog header="Add New Resource" [(visible)]="addResourceView" [style]="{width: '500px'}" [modal]="true">
  <form [formGroup]="resourceForm" (ngSubmit)="addResource()">
    <div class="field mb-3">
      <label for="resourceName" class="block">Resource Name *</label>
      <input id="resourceName" type="text" pInputText formControlName="name" class="w-full">
    </div>
    <div class="field mb-3">
      <label for="resourceType" class="block">Type</label>
      <input id="resourceType" type="text" pInputText formControlName="type" class="w-full">
    </div>
    <div class="field mb-3">
      <label for="resourceQty" class="block">Quantity *</label>
      <p-inputNumber id="resourceQty" formControlName="quantity" [min]="1" class="w-full"></p-inputNumber>
    </div>
    <div class="flex justify-content-end">
      <button pButton pRipple type="button" label="Cancel" class="p-button-text" (click)="addResourceView = false"></button>
      <button pButton pRipple type="submit" label="Save" class="ml-2" [disabled]="resourceForm.invalid"></button>
    </div>
  </form>
</p-dialog>

<!-- Add Functionality Dialog -->
<p-dialog header="Add New Functionality" [(visible)]="addFunctionalityView" [style]="{width: '500px'}" [modal]="true">
  <form [formGroup]="functionalityForm" (ngSubmit)="addFunctionality()">
    <div class="field mb-3">
      <label for="funcName" class="block">Functionality Name *</label>
      <input id="funcName" type="text" pInputText formControlName="name" class="w-full">
    </div>
    <div class="field mb-3">
      <label for="funcDesc" class="block">Description</label>
      <textarea id="funcDesc" pInputTextarea formControlName="description" rows="3" class="w-full"></textarea>
    </div>
    <div class="flex justify-content-end">
      <button pButton pRipple type="button" label="Cancel" class="p-button-text" (click)="addFunctionalityView = false"></button>
      <button pButton pRipple type="submit" label="Save" class="ml-2" [disabled]="functionalityForm.invalid"></button>
    </div>
  </form>
</p-dialog>

<p-toast></p-toast>
<p-confirmPopup></p-confirmPopup>
<p-sidebar [(visible)]="showOfficeLogsSidebar" position="right" [style]="{width: '700px'}">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-3">
      <h4>Office Changes Log</h4>
      <span class="ml-auto text-500" *ngIf="selectedBranchForLogs">
        {{selectedBranchForLogs.name}}
      </span>
    </div>
  </ng-template>

  <div class="p-3">
    <!-- Branch selection dropdown -->
    <div *ngIf="branches.length > 1" class="mb-4 p-3 surface-card border-round">
      <label class="block mb-2 font-medium">Select Branch</label>
      <p-dropdown [options]="branches" [(ngModel)]="selectedBranchForLogs"
                  optionLabel="name" placeholder="Select a branch"
                  (onChange)="getOfficeLogs(selectedBranchForLogs.docid)"
                  [style]="{width: '100%'}">
      </p-dropdown>
    </div>

    <!-- Log entries -->
    <div class="p-3">
      <div *ngIf="officeLogs.length === 0" class="text-center p-4 surface-ground border-round">
        <i class="pi pi-info-circle text-500 mr-2"></i>
        <span class="text-500"> No changes recorded for this branch</span>
      </div>

      <div *ngFor="let log of officeLogs" class="change-item mb-4 p-3 border-round surface-card">
        <!-- Header with action and timestamp -->
      <div class="flex justify-content-between align-items-center mb-3 pb-2 border-bottom-1 surface-border">
        <div class="flex align-items-center gap-2">
          <p-tag [value]="log.action | titlecase"
                [severity]="getSeverityForAction(log.action)"
                class="mr-2"></p-tag>
          <div>
            <div class="font-medium">Office #{{log.officeNumber}}</div>
            <div class="text-xs text-500">
              {{log.performedAt | date:'mediumDate'}} at {{log.performedAt | date:'shortTime'}}
            </div>
          </div>
        </div>
        <div class="text-sm text-500">
          <i class="pi pi-user mr-1"></i>
          {{log.performedByName || 'System'}}
        </div>
      </div>

        <!-- Changes list -->
    <div *ngIf="log" class="grid">
      <div *ngFor="let field of getChangedFields(log)" class="col-12 mb-3">
        <div class="font-medium mb-1">
          {{field.label}}
          <span *ngIf="!field.changed && log.action === 'edit'" class="text-500 ml-2 text-sm">(No changes)</span>
        </div>

        <div *ngIf="log.action === 'create'" class="p-2 border-round surface-100">
          <span class="text-green-500 font-semibold">{{field.newValue}}</span>
        </div>

        <div *ngIf="log.action === 'delete'" class="p-2 border-round surface-100">
          <span class="text-red-500 font-semibold line-through">{{field.oldValue}}</span>
          <span class="ml-2 text-red-500">→ Deleted</span>
        </div>

        <div *ngIf="log.action === 'edit'" class="flex align-items-center">
          <div *ngIf="field.changed" class="flex align-items-center w-full">
            <div class="old-value p-2 border-round mr-2 surface-100 flex-1">
              <span class="text-500 block text-xs">Before : </span>
              <span class="block line-through">{{field.oldValue}}</span>
            <i class="pi pi-arrow-right mx-2 text-500"></i>
              <span class="text-500 block text-xs">After : </span>
              <span class="block text-green-500 font-semibold">{{field.newValue}}</span>
            </div>
          </div>
    <div *ngIf="!field.changed" class="p-2 border-round surface-100 w-full">
      <span class="text-500 block text-xs">Value</span>
      <span class="block">{{ field.newValue !== undefined ? field.newValue : 'Empty' }}</span>
    </div>
        </div>
      </div>
    </div>

          <!-- Reason (if exists) -->
          <div *ngIf="log.reason" class="mt-3 p-2 border-round surface-ground">
            <div class="font-medium flex align-items-center">
              <i class="pi pi-comment mr-2 text-500"></i>
              <span>Reason for change</span>
            </div>
            <div class="mt-1 text-sm">{{log.reason}}</div>
          </div>
        </div>
      </div>
    </div>
  </p-sidebar>
  <!-- SECTORS  -->
  <p-sidebar [(visible)]="sectorSidebarVisible" position="right" [style]="{width: '650px'}">
    <ng-template pTemplate="header">
      <div class="flex align-items-center">
        <h4>Manage Sectors</h4>
        <span class="ml-2 text-500" *ngIf="branches.length === 1">{{branches[0].name}}</span>
      </div>
    </ng-template>

    <div class="p-3">
      <!-- Branch selection for multiple branches -->
      <div *ngIf="branches.length > 1" class="mb-4">
        <label class="block mb-2">Select Branch</label>
        <p-dropdown [options]="branches" [(ngModel)]="selectedBranchForSectors"
                    optionLabel="name" placeholder="Select a branch"
                    (onChange)="loadSectors()" [style]="{width: '100%'}">
        </p-dropdown>
      </div>

      <!-- Add new sector form -->
      <div class="p-3 border-round surface-card mb-4">
        <h5>{{ editingSector ? 'Edit Sector' : 'Add New Sector' }}</h5>
        <form [formGroup]="sectorForm" (ngSubmit)="editingSector ? updateSector() : addSector()">
          <div class="field mb-3">
            <label for="sectorName" class="block">Sector Name *</label>
            <input id="sectorName" type="text" pInputText formControlName="name" class="w-full">
          </div>
          <div class="field mb-3">
            <label for="sectorDesc" class="block">Description</label>
            <textarea id="sectorDesc" pInputTextarea formControlName="description" rows="3" class="w-full"></textarea>
          </div>
          <div class="flex justify-content-end">
            <button pButton pRipple type="submit"
          [label]="editingSector ? 'Update Sector' : 'Add Sector'"
          class="p-button-sm"
          [disabled]="sectorForm.invalid || !selectedBranchForSectors"></button>

          </div>
        </form>
      </div>

      <!-- Sectors list -->
      <div *ngIf="sectors.length > 0">
        <h5>Sectors List</h5>
        <p-table [value]="sectors" [paginator]="true" [rows]="5" [showCurrentPageReport]="true"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries">
          <ng-template pTemplate="header">
            <tr>
              <th>Name</th>
              <th>Description</th>
              <th style="width: 120px">Actions</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-sector>
            <tr>
              <td>{{sector.name}}</td>
              <td>{{sector.description || '-'}}</td>
              <td>
                <div class="flex gap-2">
                  <button pButton pRipple icon="pi pi-pencil" class="p-button-sm p-button-rounded p-button-text"
                          (click)="editSector(sector)"></button>
                  <button pButton pRipple icon="pi pi-trash" class="p-button-sm p-button-rounded p-button-text p-button-danger"
                          (click)="confirmDeleteSector(sector)"></button>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>

      <div *ngIf="sectors.length === 0 && selectedBranchForSectors" class="text-center p-4 border-round surface-ground">
        <i class="pi pi-info-circle mr-2"></i>
        <span>No sectors found for this branch</span>
      </div>
    </div>
  </p-sidebar>

  <p-dialog header="Select Branch" [(visible)]="showBranchSelectionDialog" [style]="{width: '450px'}" [modal]="true">
    <div class="p-fluid">
      <div class="field">
        <label for="logBranch">Select Branch to View Changes</label>
        <p-dropdown [options]="branches" [(ngModel)]="selectedBranchForLogs"
                    optionLabel="name" placeholder="Select a branch"
                    id="logBranch" [style]="{width: '100%'}">
        </p-dropdown>
      </div>
    </div>
    <ng-template pTemplate="footer">
      <button pButton pRipple type="button" label="Cancel" icon="pi pi-times"
              class="p-button-text" (click)="showBranchSelectionDialog = false"></button>
      <button pButton pRipple type="button" label="View Changes" icon="pi pi-history"
              class="p-button-primary" (click)="onBranchForLogsSelected()"
              [disabled]="!selectedBranchForLogs"></button>
    </ng-template>
  </p-dialog>
