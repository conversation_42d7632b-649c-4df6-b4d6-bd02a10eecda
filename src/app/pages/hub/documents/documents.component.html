<p-toolbar>
  <div class="p-toolbar-group-left mb-3">
    <h4 class="documents-title">Documents</h4>
  </div>

  <div class="p-toolbar-group-right mb-3">
    <button
      pButton
      pRipple
      icon="pi pi-sliders-h"
      label=""
      class="p-button-help me-2"
    ></button>
<p-button
  icon="pi pi-plus"
  [label]="currentFolder === 'allDocs' ? 'Add File' : 'Add File to ' + getFolderName(currentFolder)"
  class="me-2"
  data-bs-toggle="offcanvas"
  data-bs-target="#documentCanvas"
  aria-controls="documentExample">
  </p-button>
<button
  pButton
  pRipple
  icon="pi pi-plus"
  class="p-button-warning me-2"
  [label]="currentFolder === 'allDocs' ? 'Add Folder' : 'Add Folder'"
  data-bs-toggle="offcanvas"
  data-bs-target="#folderCanvas"
  aria-controls="folderExample">
</button>
    <button
      pButton
      pRipple
      icon="pi pi-plus"
      class="p-button-success"
      label="Add Category"
      data-bs-toggle="offcanvas"
      data-bs-target="#categoryCanvas"
      aria-controls="categoryExample"
    ></button>
  </div>

</p-toolbar>
<div class="navigation-container">
  <!-- Breadcrumb Navigation -->
  <nav class="navigation-breadcrumbs" aria-label="Folder Breadcrumbs">

    <!-- Root Folder -->
    <span
      class="breadcrumb-item"
      [class.active]="currentFolder === 'allDocs'"
      (click)="navigateToRoot()"
    >
      <i class="pi pi-folder-open"></i>
      All Documents
    </span>

    <!-- Navigated Folders -->
    <ng-container *ngFor="let folderId of folderNavigationStack.slice(1); let i = index">
      <i class="pi pi-chevron-right separator-icon" aria-hidden="true"></i>
      <span
        class="breadcrumb-item"
        [class.active]="i + 1 === folderNavigationStack.length - 1"
        (click)="navigateToSpecificFolder(i + 1)"
      >
        {{ getFolderName(folderId) }}
      </span>
    </ng-container>

  </nav>

</div>
  <span class="p-input-icon-left me-2">
    <i class="pi pi-search"></i>
    <input
      pInputText
      type="text"
      [(ngModel)]="searchQuery"
      (input)="filterFiles()"
      placeholder="Search files..."
    />
  </span>
<!-- Folders Section -->
<div class="section-container folder-section" *ngIf="currentFolder === 'allDocs'">
<div class="section-header">

  <h5 class="section-title">Folders</h5>
</div>
  <div class="folders-grid">
    <div *ngFor="let folder of folders" class="folder-item" (click)="setCurrentFolder(folder.id)">
  <div class="folder-top" (click)="$event.stopPropagation(); showFolderDetails(folder)">
    <i class="pi pi-info-circle info-icon"></i>
  </div>
      <div class="folder-icon">
        <svg class="folder-svg icon-size-14 opacity-50" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19.5 21a3 3 0 003-3v-4.5a3 3 0 00-3-3h-15a3 3 0 00-3 3V18a3 3 0 003 3h15zM1.5 10.146V6a3 3 0 013-3h5.379a2.25 2.25 0 011.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 013 3v1.146A4.483 4.483 0 0019.5 9h-15a4.483 4.483 0 00-3 1.146z"/>
        </svg>
      </div>
      <div class="folder-name">{{ folder.name }}</div>
        <div class="folder-count">
  {{ folder.fileCount || 0 }} {{ (folder.fileCount || 0) === 1 ? 'file' : 'files' }}
</div>
    </div>
  </div>
</div>

<p-sidebar [(visible)]="showFolderSidebar" position="right" styleClass="w-30rem">
  <div class="sidebar-header p-4 border-bottom-1 surface-border">
    <div class="flex align-items-center gap-3">
      <div class="folder-icon-large">
        <svg class="folder-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19.5 21a3 3 0 003-3v-4.5a3 3 0 00-3-3h-15a3 3 0 00-3 3V18a3 3 0 003 3h15zM1.5 10.146V6a3 3 0 013-3h5.379a2.25 2.25 0 011.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 013 3v1.146A4.483 4.483 0 0019.5 9h-15a4.483 4.483 0 00-3 1.146z"/>
        </svg>
      </div>
      <div>
        <h3 class="m-0">{{ folderMetadata?.name }}</h3>
        <small class="text-color-secondary">Created {{ formatDate(folderMetadata?.createdDate) }}</small>
      </div>
    </div>
  </div>

  <div class="sidebar-content p-4">
    <div class="folder-stats mb-5">
      <div class="grid">
        <div class="col-6">
          <div class="text-color-secondary">Files</div>
          <div class="text-xl font-bold">{{ folderMetadata?.fileCount }}</div>
        </div>
        <div class="col-6">
          <div class="text-color-secondary">Total Size</div>
          <div class="text-xl font-bold">{{ formatFileSize(folderMetadata?.totalSize || 0) }}</div>
        </div>
      </div>
    </div>

    <div class="folder-description">
      <label class="block mb-2">Description</label>
<textarea
  [ngModel]="folderMetadata?.description"
  (ngModelChange)="folderMetadata && (folderMetadata.description = $event)"
  pInputTextarea
  rows="3"
  class="w-full mb-3"
  placeholder="Add a description for this folder">
</textarea>
      <div class="flex justify-content-end gap-2">
        <button
          pButton
          label="Remove"
          *ngIf="folderMetadata?.description"
          (click)="folderMetadata!.description = ''; updateFolderDescription()"
          class="p-button-text">
        </button>
        <button
          pButton
          label="Save"
          (click)="updateFolderDescription()"
          [disabled]="!folderMetadata?.description">
        </button>
      </div>
    </div>
  </div>
</p-sidebar>
<!-- Add this sidebar for file details (similar to folder sidebar) -->
<p-sidebar [(visible)]="showFileSidebar" position="right" styleClass="w-30rem">
  <div class="sidebar-header p-4 border-bottom-1 surface-border">
    <div class="flex align-items-center gap-3">
      <div class="file-icon-large" [ngClass]="getExtensionClass(fileMetadata?.type)">
        <svg class="file-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625z"/>
          <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z"/>
        </svg>
        <span class="file-extension">{{ fileMetadata?.type }}</span>
      </div>
      <div>
        <h3 class="m-0">{{ fileMetadata?.name }}</h3>
        <small class="text-color-secondary">Uploaded {{ formatDate(fileMetadata?.uploadDate) }}</small>
      </div>
    </div>
  </div>
<div class="sidebar-content p-4">
    <div class="file-details">
      <!-- Display Name -->


      <!-- File Info (readonly) -->
      <div class="file-info mb-5 p-3 border-round surface-100">
        <div class="grid">
          <div class="col-6">
            <div class="text-color-secondary">Type</div>
            <div class="font-bold">{{ fileMetadata?.type | uppercase }}</div>
          </div>
          <div class="col-6">
            <div class="text-color-secondary">Size</div>
            <div class="font-bold">{{ formatFileSize(fileMetadata?.size) }}</div>
          </div>
          <div class="col-12 mt-2">
            <div class="text-color-secondary">Uploaded By</div>
            <div>{{ fileMetadata?.uploadedBy }}</div>
          </div>
          <div class="col-12 mt-2">
            <div class="text-color-secondary">Upload Date</div>
            <div>{{ formatDate(fileMetadata?.uploadDate) }}</div>
          </div>
        </div>
      </div>


    </div>
  </div>
</p-sidebar>
<!-- Files Section -->
<div
  class="section-container"
  [ngClass]="{ 'folder-section': currentFolder !== 'allDocs' }"
>
  <h5 class="section-title">
    {{ currentFolder === 'allDocs' ? 'All Files' : getFolderName(currentFolder) + ' Files' }}
    <span *ngIf="searchQuery" class="text-sm text-color-secondary">
      (Search results)
    </span>
  </h5>

  <div *ngIf="filteredDocuments.length === 0" class="no-files-message p-4 text-center">
    <i class="pi pi-search" style="font-size: 2rem"></i>
    <p class="mt-2">No files found{{ searchQuery ? ' matching "' + searchQuery + '"' : '' }}</p>
  </div>
<div class="files-grid">
  <div *ngFor="let doc of filteredDocuments" class="file-item">
    <div class="file-top" (click)="$event.stopPropagation(); showFileDetails(doc)">
      <i class="pi pi-info-circle info-icon"></i>
    </div>
    <div class="file-icon" [ngClass]="getExtensionClass(doc.format)" (click)="downloadFile(doc)">
      <div class="icon-wrapper">
        <svg class="file-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625z"/>
          <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z"/>
        </svg>
        <span class="file-extension">{{ doc.format }}</span>
      </div>
    </div>
    <div class="file-name">{{ doc.filename }}</div>
    <div class="file-meta">
      <small>{{ formatDate(doc.uploadDate) }}</small>
      <small>{{ formatFileSize(doc.size) }}</small>
    </div>
  </div>
</div>


<!-- Add File Offcanvas -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="documentCanvas" aria-labelledby="documentCanvasLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="documentCanvasLabel">Upload File</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <div class="p-fluid">
      <div class="p-field mb-4">
        <label for="fileInput">Select File:</label>
        <input class="form-control" type="file" (change)="onFileSelected($event)" id="fileInput" />
      </div>

      <div class="p-field mb-4" *ngIf="fileUploaded">
        <label for="filename">Display Name:</label>
        <input type="text" id="filename" [(ngModel)]="filename" pInputText />
      </div>

      <div class="p-field mb-4" *ngIf="fileUploaded">
        <label>Category:</label>
        <p-dropdown [options]="categories" [(ngModel)]="selectedCategory" placeholder="Select a Category"
          optionLabel="name" [showClear]="true"></p-dropdown>
      </div>

      <div class="p-field mb-4" *ngIf="fileUploaded">
        <label>Folder:</label>
        <p-dropdown [options]="folders" [(ngModel)]="selectedFolder" placeholder="Select a Folder"
          optionLabel="name" [showClear]="true"></p-dropdown>
      </div>

      <div class="p-field mb-4" style="display: flex; flex-direction: column" *ngIf="fileUploaded">
        <label for="description">Description:</label>
        <textarea id="description" class="form-control" [(ngModel)]="description" pInputTextarea rows="4"></textarea>
      </div>

      <button pButton type="button" [disabled]="disableUploadBtn" *ngIf="fileUploaded" label="Upload"
        (click)="uploadDocument()"></button>
    </div>
  </div>
</div>

<!-- Add Folder Offcanvas -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="folderCanvas" aria-labelledby="folderCanvasLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="folderCanvasLabel">Create New Folder</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <div class="field" style="display: flex; flex-direction: column">
      <label for="folderName" class="block">Folder Name</label>
      <input id="folderName" type="text" pInputText [(ngModel)]="folderName" />
    </div>
    <div class="col-12 text-end">
      <button pButton pRipple icon="pi pi-plus" class="p-button-success ms-auto mt-3" label="Create Folder"
        (click)="addFolder()" [disabled]="folderName === ''"></button>
    </div>
    <hr />
    <ul class="list-group col-12">
      <li class="list-group-item" *ngFor="let folder of folders">
        <div style="vertical-align: middle; align-items: center; display: flex; justify-content: space-between;">
          <p class="m-0">{{ folder.name }}</p>
          <div>
            <button pButton pRipple type="button" icon="pi pi-trash" (click)="deleteFolder(folder.id)"
              class="p-button-rounded p-button-text p-button-danger"></button>
          </div>
        </div>
      </li>
    </ul>
  </div>
</div>

<!-- Add Category Offcanvas (existing one remains the same) -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="categoryCanvas" aria-labelledby="categoryCanvasLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="categoryCanvasLabel">Document Categories</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <div class="field" style="display: flex; flex-direction: column">
      <label for="category" class="block">Category Name</label>
      <input id="category" type="text" pInputText [(ngModel)]="category" />
    </div>
    <div class="col-12 text-end">
      <button pButton pRipple icon="pi pi-plus" class="p-button-success ms-auto mt-3" label="Add Category"
        (click)="addCategory()" [disabled]="category === ''"></button>
    </div>
    <hr />
    <ul class="list-group col-12">
      <li class="list-group-item" *ngFor="let cat of categories">
        <div style="vertical-align: middle; align-items: center; display: flex; justify-content: space-between;">
          <p class="m-0">{{ cat.name }}</p>
          <div>
            <button pButton pRipple type="button" icon="pi pi-trash" (click)="deleteCategory(cat.id)"
              class="p-button-rounded p-button-text p-button-danger"></button>
          </div>
        </div>
      </li>
    </ul>
  </div>
</div>
