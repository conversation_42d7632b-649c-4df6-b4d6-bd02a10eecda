import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireStorage, AngularFireStorageReference } from '@angular/fire/compat/storage';
import { FormGroup } from '@angular/forms';
import { Observable, finalize } from 'rxjs';
import { saveAs } from 'file-saver';

interface DocumentCategory {
  name: string,
  id: string
}

interface DocumentFolder {
  name: string,
  id: string,
  fileCount: number;
  totalSize: number;
}
interface FolderMetadata {
  id: string;
  name: string;
  fileCount: number;
  totalSize: number;
  createdDate: string;
  createdBy: string;
  description?: string;
  parentId?: string;
}

@Component({
  selector: 'app-documents',
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss']
})
export class DocumentsComponent implements OnInit {
  form: FormGroup;
  category: string = '';
  categories: DocumentCategory[] = [];
  folders: DocumentFolder[] = [];
  documents: any[] = [];
  filteredDocuments: any[] = [];
  folderNavigationStack: string[] = ['allDocs'];
  searchQuery: string = '';
  originalFilteredDocuments: any[] = []; // To store the original filtered documents
  currentFolder: string = 'allDocs';
  folderName: string = '';

  filename: string = '';
  description: string = '';
  file: File | null = null;
  fileref: string = '';
  selectedCategory: any;
  selectedFolder: any;

  fileUploaded: boolean = false;
  disableUploadBtn: boolean = true;
  folderMetadata: FolderMetadata | null = null;
  showFolderSidebar = false;
  constructor(
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth,
    private storage: AngularFireStorage
  ) {
    this.form = new FormGroup({})
  }

  ngOnInit() {
    this.getCategories();
    this.getFolders();
    this.getDocuments();

  }
migrateFolderStructure() {
  this.afAuth.authState.subscribe(user => {
    if (user) {
      // First fix root folders (should have parentId null or empty)
      this.afs.collection('documentFolders').doc(user.uid).collection('folders', ref =>
        ref.where('parentId', '==', 'allDocs')
      ).get().subscribe(snapshot => {
        snapshot.docs.forEach(doc => {
          doc.ref.update({ parentId: null });
        });
      });

      // Then fix files that might be incorrectly assigned
      this.afs.collection('documents').doc(user.uid).collection('documents', ref =>
        ref.where('folderId', '==', 'allDocs')
      ).get().subscribe(snapshot => {
        snapshot.docs.forEach(doc => {
          doc.ref.update({ folderId: null });
        });
      });
    }
  });
}
// Update the download function to force download
  async downloadFile(doc: any) {
  try {
    const fileName = `${doc.filename}.${doc.format}`;

    // Extract the correct path from Firebase Storage URL
    const pathStart = doc.filePath.indexOf('/o/') + 3;
    const pathEnd = doc.filePath.indexOf('?');
    const filePath = decodeURIComponent(doc.filePath.substring(pathStart, pathEnd));

    // Get the file reference
    const storageRef = this.storage.ref(filePath);

    // Get the download URL with a token
    const url = await storageRef.getDownloadURL().toPromise();

    // Create a hidden iframe to force download
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = url;
    document.body.appendChild(iframe);

    // Alternative method using XMLHttpRequest to force download
    const xhr = new XMLHttpRequest();
    xhr.responseType = 'blob';
    xhr.onload = () => {
      const blob = xhr.response;
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
      document.body.removeChild(iframe);
    };


  } catch (error) {
    // Fallback to opening in new tab if download fails
    window.open(doc.filePath, '_blank');
  }
}

updateFileMetadata() {
  this.afAuth.authState.subscribe(user => {
    if (user && this.fileMetadata) {
      const updateData = {
        filename: this.fileMetadata.filename,
        description: this.fileMetadata.description,
        category: this.fileMetadata.category?.name || this.fileMetadata.category || '',
        folderId: this.fileMetadata.folderId?.id || this.fileMetadata.folderId || null
      };

      this.afs.collection('documents').doc(user.uid)
        .collection('documents').doc(this.fileMetadata.id)
        .update(updateData)
        .then(() => {
          this.showFileSidebar = false;
          this.getDocuments(); // Refresh the documents list
        })
        .catch(error => {
          console.error('Error updating document:', error);
        });
    }
  });
}
// Add this new function to show file details
showFileDetails(file: any) {
  this.afAuth.authState.subscribe(user => {
    if(user) {
      this.fileMetadata = {
        id: file.id,
        filename: file.filename,
        type: file.format,
        size: file.size,
        uploadDate: file.uploadDate,
        uploadedBy: user.displayName || user.email || 'Unknown',
        description: file.description || '',
        category: this.categories.find(c => c.name === file.category) || null,
        folderId: file.folderId || null
      };
      this.showFileSidebar = true;
    }
  });
}

// Add these new properties to your component class
fileMetadata: any = null;
showFileSidebar = false;
// In getDocuments() method
getDocuments() {
  this.afAuth.authState.subscribe(user => {
    if(user) {
      this.afs.collection('documents').doc(user.uid).collection('documents').valueChanges().subscribe((docs: any[]) => {
        this.documents = docs;

        // Calculate folder statistics without overwriting existing metadata
        this.folders.forEach(folder => {
          const folderDocs = docs.filter(doc => doc.folderId === folder.id);
          const newFileCount = folderDocs.length;
          const newTotalSize = folderDocs.reduce((sum, doc) => sum + (doc.size || 0), 0);

          // Only update if different to prevent unnecessary updates
          if (folder.fileCount !== newFileCount || folder.totalSize !== newTotalSize) {
            folder.fileCount = newFileCount;
            folder.totalSize = newTotalSize;

            // Update in Firestore if needed
            if (user) {
              this.afs.collection('documentFolders').doc(user.uid).collection('folders')
                .doc(folder.id)
                .update({
                  fileCount: newFileCount,
                  totalSize: newTotalSize
                });
            }
          }
        });

        this.filterDocumentsByFolder();
      });
    }
  });
}

showFolderDetails(folder: any) {
  this.afAuth.authState.subscribe(user => {
    if(user) {
      this.folderMetadata = {
        id: folder.id,
        name: folder.name,
        fileCount: folder.fileCount || 0,
        totalSize: folder.totalSize || 0,
        createdDate: folder.createdDate || new Date().toISOString(),
        createdBy: user.displayName || user.email || 'Unknown',
        description: folder.description
      };
      this.showFolderSidebar = true;
    }
  });
}
// Update the updateFolderDescription method in documents.component.ts
updateFolderDescription() {
  if (this.folderMetadata) {
    this.afAuth.authState.subscribe(user => {
      if(user) {
        // Only update the description, not the entire folder metadata
        this.afs.collection('documentFolders').doc(user.uid).collection('folders')
          .doc(this.folderMetadata!.id)
          .update({
            description: this.folderMetadata!.description,
            // Preserve existing counts
            fileCount: this.folderMetadata!.fileCount,
            totalSize: this.folderMetadata!.totalSize
          })
          .then(() => {
            // Success - no need to refresh data
          });
      }
    });
  }
}
getFolders() {
  this.afAuth.authState.subscribe(user => {
    if(user) {
      this.afs.collection('documentFolders').doc(user.uid).collection('folders').valueChanges()
        .subscribe((res: any) => {
          this.folders = res;
        });
    }
  });
}
navigateToSpecificFolder(index: number) {
  if (index < this.folderNavigationStack.length) {
    this.currentFolder = this.folderNavigationStack[index];
    this.folderNavigationStack = this.folderNavigationStack.slice(0, index + 1);

    // If we're back at root, reset completely
    if (this.currentFolder === 'allDocs') {
      this.folderNavigationStack = ['allDocs'];
    }

    this.filterDocumentsByFolder();
  }
}
getCurrentParentFolderId(): string {
  return this.currentFolder === 'allDocs' ? '' : this.currentFolder;
}
getFolderName(folderId: string): string {
  if (folderId === 'allDocs') return 'All Documents';
  const folder = this.folders.find(f => f.id === folderId);
  return folder ? folder.name : 'Unknown';
}
navigateToRoot() {
  this.currentFolder = 'allDocs';
  this.folderNavigationStack = ['allDocs'];
  this.filterDocumentsByFolder();
}
navigateToFolder(folderId: string) {
  this.currentFolder = folderId;
  this.folderNavigationStack.push(folderId);
  this.getFolders();
  this.filterDocumentsByFolder();
}
navigateBack() {
  if (this.folderNavigationStack.length > 1) {
    this.folderNavigationStack.pop();
    this.currentFolder = this.folderNavigationStack[this.folderNavigationStack.length - 1];
    this.getFolders(); // Refresh folders for new context
    this.filterDocumentsByFolder();
  }
}



filterDocumentsByFolder() {
  if (this.currentFolder === 'allDocs') {
    this.filteredDocuments = this.documents.filter(doc => !doc.folderId || doc.folderId === 'allDocs');
  } else {
    this.filteredDocuments = this.documents.filter(doc => doc.folderId === this.currentFolder);
  }
  // Store the original filtered documents for search functionality
  this.originalFilteredDocuments = [...this.filteredDocuments];

  // Apply search filter if there's an active search query
  if (this.searchQuery) {
    this.filterFiles();
  }
}
setCurrentFolder(folderId: string) {
  if (this.currentFolder !== folderId) {
    this.currentFolder = folderId;
    // Only add to navigation stack if it's a new folder
    if (this.folderNavigationStack[this.folderNavigationStack.length - 1] !== folderId) {
      this.folderNavigationStack.push(folderId);
    }
    this.filterDocumentsByFolder();
  }
}
filterFiles() {
  if (!this.searchQuery) {
    // If search is empty, show all files in current folder
    this.filteredDocuments = [...this.originalFilteredDocuments];
    return;
  }

  const query = this.searchQuery.toLowerCase();
  this.filteredDocuments = this.originalFilteredDocuments.filter(doc =>
    doc.filename.toLowerCase().includes(query)
  );
}
addFolder() {
  this.afAuth.authState.subscribe(user => {
    if(user && this.folderName.trim() !== '') {
      const folderId = this.afs.createId();
      const folderData = {
        id: folderId,
        name: this.folderName,
        createdDate: new Date().toISOString(),
        createdBy: user.displayName || user.email || 'Unknown',
        fileCount: 0,
        totalSize: 0
      };

      this.afs.collection('documentFolders').doc(user.uid).collection('folders').doc(folderId).set(folderData)
        .then(() => {
          this.folderName = '';
          this.getFolders();
        });
    }
  });
}

  deleteFolder(folderId: string) {
    this.afAuth.authState.subscribe(user => {
      if(user) {
        this.afs.collection('documentFolders').doc(user.uid).collection('folders').doc(folderId).delete()
          .then(() => {
            console.log('Folder deleted successfully');
          })
          .catch(error => {
            console.log(error);
          });
      }
    });
  }



getFileColor(format: string): string {
  if (!format) return '#607d8b'; // Default

  const formatLower = format.toLowerCase();

  const colorMap: { [key: string]: string } = {
    pdf: '#f44336',
    doc: '#2196f3',
    docx: '#2196f3',
    xls: '#4caf50',
    xlsx: '#4caf50',
    csv: '#4caf50',
    ppt: '#ff9800',
    pptx: '#ff9800',
    jpg: '#ff5722',
    jpeg: '#ff5722',
    png: '#ff5722',
    gif: '#ff5722',
    zip: '#9c27b0',
    rar: '#9c27b0',
    txt: '#607d8b',
    default: '#795548'
  };

  return colorMap[formatLower] || colorMap['default'];
}

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    this.fileUploaded = true;
    if (file) {
      this.file = file;
      this.disableUploadBtn = false;
      this.filename = this.extractFilenameWithoutExtension(file.name);
    }
  }

  private extractFilenameWithoutExtension(fullFilename: string): string {
    const parts = fullFilename.split('.');
    if (parts.length > 1) {
      parts.pop(); // Remove the last part (extension)
    }
    return parts.join('.'); // Rejoin the parts without the extension
  }


uploadDocument() {
  this.disableUploadBtn = true;
  const id = this.afs.createId();
  const format = this.file!.name.split('.').pop();
  const filePath = `documents/${id}`;
  const fileRef = this.storage.ref(filePath);

  const metadata = {
    contentDisposition: `attachment; filename="${this.file!.name}"`
  };

  const uploadTask = this.storage.upload(filePath, this.file, metadata);

  uploadTask.snapshotChanges().pipe(
    finalize(() => {
      fileRef.getDownloadURL().subscribe(downloadURL => {
        this.fileref = downloadURL;
        this.afAuth.authState.subscribe(user => {
          if (user) {
            const documentData: any = {
              id: id,
              filename: this.filename,
              description: this.description,
              format,
              category: this.selectedCategory?.name || 'Uncategorized',
              uploadDate: new Date().toISOString(),
              size: this.file!.size,
              filePath: this.fileref,
              folderId: this.currentFolder === 'allDocs' ? null : this.currentFolder
            };

            this.afs.collection('documents').doc(user.uid).collection('documents').doc(id).set(documentData)
              .then(() => {
                this.fileUploaded = false;
                this.resetForm();
              });
          }
        });
      });
    })
  ).subscribe();
}


  resetForm(): void {
    this.filename = '';
    this.description = '';
    this.selectedCategory = null;
    this.selectedFolder = null;
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  addCategory() {
    this.afAuth.authState.subscribe(user => {
      if(user && this.category.trim() !== '') {
        const docId = this.afs.createId();
        this.afs.collection('documentCategory').doc(user.uid).collection('categories').doc(docId).set({
          name: this.category,
          id: docId
        }).then(() => {
          this.category = '';
        }).catch(error => {
          console.log(error);
        })
      }
    })
  }

  getCategories() {
    this.afAuth.authState.subscribe(user => {
      if(user) {
        this.afs.collection('documentCategory').doc(user.uid).collection('categories').valueChanges().subscribe((res: any) => {
          this.categories = res;
        })
      }
    });
  }

  deleteCategory(id: string) {
    this.afAuth.authState.subscribe(user => {
      if(user) {
        this.afs.collection('documentCategory').doc(user.uid).collection('categories').doc(id).delete().then(() => {
          console.log('Category deleted Successfully');
        }).catch((error) => {
          console.log(error);
        })
      }
    })
  }

  // Helper functions for UI
getExtensionClass(format: string): string {
  if (!format) return 'default';

  const formatLower = format.toLowerCase();

  if (formatLower === 'pdf') return 'pdf';
  if (formatLower === 'doc' || formatLower === 'docx') return 'doc';
  if (formatLower === 'xls' || formatLower === 'xlsx' || formatLower === 'csv') return 'xls';
  if (formatLower === 'ppt' || formatLower === 'pptx') return 'ppt';
  if (formatLower === 'jpg' || formatLower === 'jpeg' || formatLower === 'png' || formatLower === 'gif') return 'jpg';
  if (formatLower === 'zip' || formatLower === 'rar') return 'zip';
  if (formatLower === 'txt') return 'txt';

  return 'default';
}

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

formatDate(dateString?: string): string {
  if (!dateString) return 'Unknown date';
  const date = new Date(dateString);
  return date.toLocaleDateString();
}

}
