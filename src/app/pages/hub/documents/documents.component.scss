// Variables assumed (can be placed in abstracts/_variables.scss)
$color-primary: #2c3e50;
$color-secondary: #64748b;
$color-muted: #94a3b8;
$color-muted-dark: #334155;
$color-bg-light: #f8fafc;

$font-size-sm: 0.875rem;
$font-size-md: 1rem;
$font-size-lg: 1.25rem;

$spacing-xs: 0.5rem;
$spacing-sm: 0.75rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;

// ──────────────────────────────────────────────────────────────────────────────
.section-title {
padding:2.5%;
  color: #5a5a5a;
  margin-bottom: 1.5rem;
  font-weight: 500;
}
.documents-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .documents-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
  }
}

// Variables (optional, for cleaner reuse)
$color-breadcrumb: #64748b;
$color-hover-bg: #f1f5f9;
$color-hover-text: #334155;
$color-active-bg: #e2e8f0;
$color-active-text: #1e293b;
$color-separator: #cbd5e1;

$breadcrumb-padding: 0.25rem 0.5rem;
$breadcrumb-radius: 6px;
$breadcrumb-font-size: 0.95rem;
$icon-size: 0.8rem;

// ──────────────────────────────────────────────────────────────────────────────
// Navigation Container
.navigation-container {
  display: flex;
  align-items: center;
  gap: 1rem;

  // ─────────────────────────────────────────────
  // Breadcrumbs Wrapper
  .navigation-breadcrumbs {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.25rem;
    font-size: $breadcrumb-font-size;

    // ─────────────────────────────────────────
    // Individual Breadcrumb Item
    .breadcrumb-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: $color-breadcrumb;
      padding: $breadcrumb-padding;
      border-radius: $breadcrumb-radius;
      transition: background 0.2s ease, color 0.2s ease;

      &:hover {
        background-color: $color-hover-bg;
        color: $color-hover-text;
      }

      &.active {
        background-color: $color-active-bg;
        color: $color-active-text;
        font-weight: 500;
      }

      // Icon inside breadcrumb
      i {
        font-size: $icon-size;
        margin-right: 0.25rem;

        &.pi-chevron-right {
          margin: 0 0.25rem;
          color: $color-separator;
        }
      }
    }
  }
}
.folder-section {
  margin-top: -30px;
}
/* Add to your component's CSS */
.file-details .field {
  margin-bottom: 1.5rem;
}

.file-details label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.file-info {
  background-color: #f8f9fa;
  border-radius: 6px;
}

.file-info .text-color-secondary {
  color: #6c757d;
  font-size: 0.875rem;
}

.file-info .font-bold {
  font-weight: 600;
}.file-top {
  cursor: pointer;
  color: var(--primary-color);

.info-icon {
  font-size: $font-size-md;
  color: $color-muted;
  opacity: 0.6;
}


    &:hover {
      visibility: 1;
    }
  }



.file-icon-large {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 4px;

  .file-svg {
    width: 100%;
    height: 100%;
  }

  .file-extension {
    position: absolute;
    bottom: 5px;
    font-size: 0.7rem;
    font-weight: bold;
    color: white;
  }
}

.file-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}
.folders-grid {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}
.context-indicator {
  font-size: 0.85rem;
  color: #64748b;
  margin-bottom: 1rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border-radius: 6px;
  display: inline-block;

  i {
    margin-right: 0.5rem;
  }
}
.folder-item {
  width: 140px;
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  position: relative;
  transition: 0.2s ease;

  &:hover {
    background: #f5f5f5;
    transform: translateY(-2px);
  }
}


// Folder Top
.folder-top {
  position: absolute;
  top: $spacing-sm;
  right: $spacing-sm;
}

// Info Icon
.info-icon {
  font-size: $font-size-md;
  color: $color-muted;
  opacity: 0.6;
}

// Sidebar Header
.sidebar-header {
  background: $color-bg-light;
}

// Section Header
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-lg;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

// Section Title
.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $color-primary;
  margin: 0;
}

// Section Subtitle
.section-subtitle {
  font-size: $font-size-sm;
  color: $color-secondary;
  margin-left: auto;
}

// Breadcrumbs
.navigation-breadcrumbs {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-sm;
  color: $color-secondary;

  span {
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: $color-muted-dark;
      text-decoration: underline;
    }
  }
}

// Sidebar Content
.sidebar-content {
  .folder-stats {
    background: $color-bg-light;
    padding: $spacing-md;
    border-radius: 8px;
  }

  .folder-description {
    textarea {
      background: $color-bg-light;
      border: none;
      width: 100%;
      resize: vertical;
      min-height: 5rem;
      padding: $spacing-sm;
      font-size: $font-size-sm;
      color: $color-primary;
    }
  }
}
.folder-icon-large {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 8px;

  .folder-svg {
    width: 1.75rem;
    height: 1.75rem;
    color: #64748b;
  }
}
.folder-icon {
  margin-top: 0.5rem;
  margin-bottom: 0.75rem;
}

.folder-svg {
  fill: currentColor;
  color: #64748b; // Tailwind’s slate-500 (matches example)
  opacity: 0.5;
  width: 3.5rem;
  height: 3.5rem;
}

.folder-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
  text-align: center;
}

.folder-count {
  font-size: 0.8rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}


.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  padding-top: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
}

.files-grid {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.file-item {
  width: 140px;
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  position: relative;
  transition: 0.2s ease;

  &:hover {
    background: #f5f5f5;
    transform: translateY(-2px);
  }
}
.file-icon {
  position: relative;
  width: 64px;
  height: 72px;
  margin-bottom: 1rem;

  .icon-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .file-svg {
    width: 3.5rem;
    height: 3.5rem;
    color: #64748b; // default color, overridden below
    opacity: 0.5;
  }

  .file-extension {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    color: white;
    min-width: 30px;
left: 20px;
    text-align: center;
  }

  // Extension-specific colors
  &.pdf {
    .file-extension { background-color: #f44336; }
    i { color: #f44336; opacity: 0.8; }
  }
  &.doc, &.docx {
    .file-extension { background-color: #2196f3; }
    i { color: #2196f3; opacity: 0.8; }
  }
  &.xls, &.xlsx &.csv{
    .file-extension { background-color: #4caf50; }
    i { color: #4caf50; opacity: 0.8; }
  }
  &.ppt, &.pptx {
    .file-extension { background-color: #ff9800; }
    i { color: #ff9800; opacity: 0.8; }
  }
  &.jpg, &.jpeg, &.png, &.gif {
    .file-extension { background-color: #ff5722; }
    i { color: #ff5722; opacity: 0.8; }
  }
  &.zip, &.rar {
    .file-extension { background-color: #9c27b0; }
    i { color: #9c27b0; opacity: 0.8; }
  }
  &.txt {
    .file-extension { background-color: #607d8b; }
    i { color: #607d8b; opacity: 0.8; }
  }
  &.default {
    .file-extension { background-color: #795548; }
    i { color: #795548; opacity: 0.8; }
  }
}

.file-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  word-break: break-word;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  font-size: 0.75rem;
  color: #7f8c8d;
  display: flex;
  gap: 0.75rem;

  .file-size {
    position: relative;
    padding-right: 0.75rem;

    &::after {
      content: "•";
      position: absolute;
      right: -2px;
    }
  }

}

/* Search input styling */
.p-toolbar .p-inputtext {
  width: 200px;
  transition: width 0.3s ease;
  &:focus {
    width: 250px;
  }
}

/* Make sure the search icon is properly aligned */
.p-input-icon-left > i {
  top: 50%;
  transform: translateY(-10%);
}
.p-input-icon-left {
  left: 80%;
}
.no-files-message {
  color: var(--text-color-secondary);
  background-color: var(--surface-b);
  border-radius: 4px;
  margin-top: 1rem;

  i {
    opacity: 0.5;
  }
}
