<div class="container px-0 col-12">
  <h5>Office {{ office?.officeNumber }} details</h5>
  <div
    class="card mb-4 card-body col-12 relative"
    style="display: flex !important; flex-direction: row"
  >
    <div class="logo-box shadow"></div>
    <div class="contact-box  col">
      <h6 class="m-0">Smart Target IT Solutions</h6>
      <p class="text-muted p-0 m-0">+974 3351 5855</p>
      <p class="text-muted p-0 m-0">{{'<EMAIL>'}}</p>
      <div class="col text-end">
        <button
          pButton
          pRipple
          type="button"
          icon="pi pi-chevron-right"
          iconPos="right"
          label="Profile"
          class="p-button-outlined"
        ></button>
      </div>
    </div>
    <div class="end-data-box">Aug, 04 2025</div>
  </div>

  <div class="flex-middle mb-3 col-12 justify-content-between">
    <h5>Ressources</h5>
    <button
      pButton
      pRipple
      type="button"
      icon="pi pi-plus"
      iconPos="right"
      label="Add More"
      class="p-button-outlined"
    ></button>
  </div>

  <div class="d-flex">
    <div class="card me-3 card-body" *ngFor="let item of ressources">
        <h6>{{item.name}}</h6>
        <p class="text-muted">{{item.value}} item{{item.value === 1? '': 's'}}</p>
    </div>
  </div>

  <h5>Virtual Tour</h5>
  <iframe style="width: 100%; height: 320px;" aria-controls="false" src="https://my.matterport.com/show/?m=jm5WwEA3HUN&log=0&help=0&nt=0&play=0&qs=0&brand=1&dh=1&tour=1&gt=1&hr=1&mls=0&mt=1&tagNav=1&pin=1&portal=1&f=1&fp=1&nozoom=0&search=1&wh=0&kb=1&lp=0&title=0&tourcta=1&vr=1" frameborder="0" allowfullscreen allow='xr-spatial-tracking'></iframe>
</div>
