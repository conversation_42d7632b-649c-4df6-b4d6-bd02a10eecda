.office-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 1rem;
.status-badge {
  display: inline-block;       /* shrink width to text */
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  user-select: none;
  text-align: center;
  line-height: 1.2;
  box-shadow: 0 1px 4px rgba(0,0,0,0.15);
}
  h5 {
    font-weight: 700;
    font-size: 1.5rem;
    color: #222;
    margin-bottom: 1.25rem;
    border-bottom: 2px solid #ddd;
    padding-bottom: 0.3rem;
  }

  // Status Badge
  .text-white {
    font-weight: 600;
    font-size: 1rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border-radius: 0.375rem;
    width: fit-content;
    margin: 0 auto 1.5rem;
    padding: 0.5rem 1.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  }


  // Image Container
  .text-center {
    img {
      max-width: 100%;
      max-height: 250px;
      border-radius: 8px;
      border: 1px solid #ddd;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      object-fit: cover;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }
    }

    p.text-muted {
      font-style: italic;
      color: #888;
      margin-top: 0.5rem;
      font-size: 0.9rem;
    }
  }

  // Details section
  .mb-3 {
    p {
      font-size: 1.05rem;
      color: #333;
      margin-bottom: 0.4rem;
      strong {
        color: #444;
      }
    }
  }

  // Resources header + button container
  .flex-middle {
    display: flex !important; // override Bootstrap if necessary
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 1rem;

    h5 {
      margin: 0;
      font-weight: 600;
      color: #222;
    }

    button.p-button-outlined {
      border-color: #0d6efd;
      color: #0d6efd;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background-color: #0d6efd;
        color: #fff;
        border-color: #0d6efd;
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.5);
      }

      i.pi {
        margin-left: 0.3rem;
      }
    }
  }

  // Resources cards container
  .d-flex {
    flex-wrap: wrap;

    .card {
      min-width: 130px;
      max-width: 180px;
      margin-right: 1rem;
      margin-bottom: 1rem;
      padding: 0.75rem 1rem;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.07);
      background-color: #fafafa;
      border: 1px solid #e3e3e3;
      cursor: default;
      transition: box-shadow 0.3s ease;

      &:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      h6 {
        font-weight: 600;
        margin-bottom: 0.3rem;
        color: #222;
      }

      p.text-muted {
        margin: 0;
        font-size: 0.9rem;
        color: #666;
      }
    }

    p.text-muted {
      margin-top: 1rem;
      font-style: italic;
      color: #999;
      font-size: 1rem;
    }
  }

  // Virtual tour heading
  > h5:last-of-type {
    margin-top: 2.5rem;
    font-weight: 700;
    font-size: 1.4rem;
    color: #222;
    margin-bottom: 1rem;
    border-bottom: 2px solid #ddd;
    padding-bottom: 0.25rem;
  }

  // Iframe styling for virtual tour
  iframe {
    width: 100% !important;
    height: 320px !important;
    border-radius: 12px;
    border: 1px solid #ccc;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    transition: box-shadow 0.3s ease;

    &:hover,
    &:focus {
      box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
      outline: none;
    }
  }
}
