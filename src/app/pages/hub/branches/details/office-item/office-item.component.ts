import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-office-item',
  templateUrl: './office-item.component.html',
  styleUrls: ['./office-item.component.scss']
})

export class OfficeItemComponent {
  @Input() office: any;
  ressources: any[] = [
    {
      name: 'Chairs',
      value: 3
    },
    {
      name: '<PERSON><PERSON>',
      value: 2
    },
    {
      name: 'Cabinets',
      value: 1
    },
    {
      name: 'Telefone',
      value: 1
    }
  ]
dropdownOpen = false;
showVirtualTour = true;

toggleDropdown() {
  this.dropdownOpen = !this.dropdownOpen;
}

// Optionally close dropdown on outside click - you can add that for better UX

onEdit() {
  this.dropdownOpen = false;
  // Your edit logic here
  alert('Edit clicked');
}

onDelete() {
  this.dropdownOpen = false;
  // Your delete logic here
  alert('Delete clicked');
}

toggleVirtualTour() {
  this.dropdownOpen = false;
  this.showVirtualTour = !this.showVirtualTour;
}
  onAddResource() {
    // Trigger modal or emit event to parent
    console.log('Add resource clicked');
  }

  openExternalLink(url: string) {
    window.open(url, '_blank');
  }
}
