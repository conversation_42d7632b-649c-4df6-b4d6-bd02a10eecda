.no-offices {
  min-height: 240px;
  display: flex;
  vertical-align: middle;
  align-items: center;
  justify-content: center;

  .bi {
    font-size: 64px;
  }
}

:host ::ng-deep {
  .normal-sidebar .p-sidebar {
    width: 800px !important;
  }
}

.offices-cards {
  display: flex;
  flex-wrap: wrap;



  .available-card {
    border-color: #3fb950 !important;
    background: rgba(63, 185, 80, 0.1);
    color: #3fb950;
    &:hover {
      background: #3fb950;
      color: #fff;
    }
  }
  .occupied-card {
    border-color: red !important;
    background: rgba(255, 0, 0, 0.1);
    color: #ff0000;
    &:hover {
      background: red;
      color: #fff;
    }
  }
}

.field {
    display: flex;
    flex-direction: column;
}

.row .field {
    width: calc(50% - 7px);
}.office-card-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-5px);
  }
}

.office-card {
  background: white;
  cursor: pointer;
  flex-grow: 1;
}

.office-number-circle {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  background: #6c5ce7;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
  z-index: 2;
}

.status-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  z-index: 2;

  &.available {
    background: #28a745;
  }

  &.rented {
    background: #dc3545;
  }
  &.under_process {
    background: #6c757d; // Gray color for Under Process
  }
}

.office-image-container {
  height: 180px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .office-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .no-image-placeholder {
    color: #adb5bd;
    font-size: 3rem;
  }
}

.office-details {
  padding: 15px;

  .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    .detail-label {
      font-weight: 600;
      color: #495057;
    }

    .detail-value {
      color: #6c757d;
    }
  }
}

.office-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;

  .virtual-tour-text {
    color: #6c5ce7;
    font-size: 0.85rem;
    font-weight: 500;
  }

  .action-buttons {
    display: flex;
    gap: 0.5rem;

    button {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
:host ::ng-deep {
  .p-dialog .p-dialog-header {
    border-bottom: 1px solid #e9ecef;
  }
  .p-dialog .p-dialog-content {
    padding: 1.5rem;
  }
  .confirmation-content {
    padding: 1rem 0;
  }
}
.upload-progress {
  margin-top: 1rem;

  .progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
  }
}

.image-preview {
  position: relative;
  margin-top: 1rem;
  border: 1px dashed #dee2e6;
  border-radius: 6px;
  padding: 0.5rem;
  max-width: 300px;

  img {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    border-radius: 4px;
  }

  .remove-image {
    position: absolute;
    top: -10px;
    right: -10px;
    background: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
}

:host ::ng-deep {
  .p-progressbar {
    height: 6px;
    border-radius: 3px;

    .p-progressbar-value {
      background: #6366f1;
    }
  }
}
@media (max-width: 768px) {
  .offices-cards {
    .col-md-4 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
}
