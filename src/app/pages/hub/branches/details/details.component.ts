import { Component, Input, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { MessageService } from 'primeng/api';
import { finalize } from 'rxjs';
interface Sector {
  id: string;
  name: string;
  description?: string;
  branchId: string;
  createdAt: Date;
  updatedAt: Date;
}

@Component({
  selector: 'app-details',
  templateUrl: './details.component.html',
  styleUrls: ['./details.component.scss'],
})
export class DetailsComponent implements OnInit {
  @Input() branch: any;
  @Input() isRegularUser: boolean = false;
  @Input() userRole: string = "";

  offices: any[] = [];
  addOneOffice: boolean = false;
  addBulkOffice: boolean = false;
  openOfficeDetails: boolean = false;
  file: File | null = null;
  filename: string = '';
  fileUploaded: boolean = false;
  disableUploadBtn: boolean = true;

  selectedOffice: any;
  sectors: any[] = [];
  selectedSector: any = null;
  officeNumber!: number;
  startNumber!: number;
  lastNumber!: number;
  uploadProgress: number | null = null;
  imageUrl: string | null = null;
  dropdownOpenMap: { [key: string]: boolean } = {};
  editMode = false;
  officeToEdit: any = null;
  deleteDialogVisible = false;
  officeToDelete: any = null;
  // Office form models
officeForm = {
  number: null as number | null,
  status: 'available',
  surface: null as number | null,
  renterName: null as string | null,
  price: null as number | null,
  imageFile: null as File | null,
  imageUrl: null as string | null,
  sectorId: null as string | null
};

bulkOfficeForm = {
  startNumber: null as number | null,
  endNumber: null as number | null,
  status: 'available',
  surface: null as number | null,
  price: null as number | null,
  imageFile: null as File | null,
  imageUrl: null as string | null,
  sectorId: null as string | null
};

  statusOptions = [
    { label: 'Available', value: 'available' },
    { label: 'Rented', value: 'rented' },
    { label: 'Under Process', value: 'under_process' }
  ];
  constructor(private afs: AngularFirestore, private afAuth: AngularFireAuth,    private messageService: MessageService , private storage: AngularFireStorage // <-- this is the fix

) {}


ngOnInit() {
      this.getOffices();
        this.loadSectors();

    // Initialize dropdown states
    this.offices.forEach(office => {
      this.dropdownOpenMap[office.officeNumber] = false;
    });
  }
async loadSectors() {
  try {
    const sectorsSnapshot = await this.afs.collection('sectors', ref =>
      ref.where('branchId', '==', this.branch.docid)
    ).get().toPromise();

    if (sectorsSnapshot) {
      this.sectors = sectorsSnapshot.docs.map(doc => {
        const data = doc.data() as Omit<Sector, 'id'>; // Type assertion here
        return {
          id: doc.id,
          ...data
        } as Sector;
      });
    }
  } catch (error) {
    console.error('Error loading sectors:', error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load sectors',
      life: 5000
    });
  }
}
private async logOfficeAction(action: string, officeData: any, changes?: any) {
  try {
    const user = await this.afAuth.currentUser;
    if (!user) return;

    const logData = {
      action: action,
      officeId: officeData.officeId,
      officeNumber: officeData.officeNumber,
      branchId: this.branch.docid,
      branchName: this.branch.name,
      performedBy: user.uid,
      performedAt: new Date(),
      oldData: action === 'edit' ? changes?.oldData : officeData,
      newData: action === 'edit' ? changes?.newData : null
    };

    await this.afs.collection('Offices-Logs').add(logData);
  } catch (error) {
    console.error('Error logging office action:', error);
  }
}
private getOfficeChanges(oldData: any, newData: any) {
  const changes: any = {};
  Object.keys(newData).forEach(key => {
    if (oldData[key] !== newData[key]) {
      changes[key] = {
        oldValue: oldData[key],
        newValue: newData[key]
      };
    }
  });
  return changes;
}
removeImage() {
  this.officeForm.imageUrl = null;
  this.officeForm.imageFile = null;
}
onFileSelected(event: any, isBulk: boolean = false): void {
  const file = event.files[0]; // Note: PrimeNG FileUpload uses event.files
  this.fileUploaded = true;

  if (file) {
    this.file = file;
    this.disableUploadBtn = false;
    this.filename = this.extractFilenameWithoutExtension(file.name);

    // Preview the image
    const reader = new FileReader();
    reader.onload = (e: any) => {
if (isBulk) {
  this.bulkOfficeForm.imageFile = file;
  this.bulkOfficeForm.imageUrl = e.target.result;
} else {
  this.officeForm.imageFile = file;
  this.officeForm.imageUrl = e.target.result;
}
    };
    reader.readAsDataURL(file);
  }
}
  viewOffice(office: any) {
    this.dropdownOpenMap[office.officeNumber] = false;
    this.showOfficeDetails(office);
  }

async editOffice(office: any) {
  this.dropdownOpenMap[office.officeNumber] = false;
  this.editMode = true;
  this.officeToEdit = {...office};

  // Populate the form with office data
  this.officeForm = {
    number: office.officeNumber,
    status: office.status,
    surface: office.surface,
    renterName: office.renterName || null,
    price: office.price || null,
    imageFile: null,
    imageUrl: office.imageUrl || null,
    sectorId: office.sectorId || null
  };

  this.addOneOffice = true;
}
async deleteOffice() {
  if (!this.officeToDelete) return;

  try {
    if (this.userRole === 'receptionist') {
      await this.logOfficeAction('delete', this.officeToDelete);
    }

    await this.afs.collection('offices').doc(this.officeToDelete.officeId).delete();

    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: `Office ${this.officeToDelete.officeNumber} deleted successfully`,
      life: 5000
    });

    // Remove from local array
    this.offices = this.offices.filter(o => o.officeId !== this.officeToDelete.officeId);

    this.deleteDialogVisible = false;
    this.officeToDelete = null;
  } catch (error: any) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to delete office: ' + error.message,
      life: 5000
    });
  }
}
private extractFilenameWithoutExtension(fullFilename: string): string {
  const parts = fullFilename.split('.');
  if (parts.length > 1) {
    parts.pop(); // Remove the last part (extension)
  }
  return parts.join('.'); // Rejoin the parts without the extension
}

async uploadImage(file: File): Promise<string> {
  return new Promise(async (resolve, reject) => {
    this.disableUploadBtn = true;
    this.uploadProgress = 0;

    const id = this.afs.createId();
    const format = file.name.split('.').pop();
    const filePath = `office_images/${id}.${format}`;
    const fileRef = this.storage.ref(filePath);

    const metadata = {
      contentType: file.type,
      customMetadata: {
        originalFilename: file.name,
        uploadedBy: (await this.afAuth.currentUser)?.uid || 'unknown'
      }
    };

    const uploadTask = this.storage.upload(filePath, file, metadata);

    uploadTask.percentageChanges().subscribe((progress) => {
      this.uploadProgress = progress || 0;
    });

    uploadTask.snapshotChanges().pipe(
      finalize(() => {
        fileRef.getDownloadURL().subscribe({
          next: (downloadURL: string | PromiseLike<string>) => {
            this.uploadProgress = null;
            resolve(downloadURL);
          },
          error: (error: any) => {
            this.uploadProgress = null;
            this.messageService.add({
              severity: 'error',
              summary: 'Upload Failed',
              detail: 'Failed to upload image',
              life: 5000
            });
            reject(error);
          }
        });
      })
    ).subscribe();
  });
}
 loadUserRole() {
    this.afAuth.authState.subscribe(user => {
      if (user) {
        this.afs.collection('users').doc(user.uid).valueChanges().subscribe((userData: any) => {
          this.userRole = userData?.role || '';
        });
      }
    });
  }
getOffices() {
  this.afAuth.authState.subscribe((user) => {
    if (user) {
      this.afs
        .collection('offices', (ref) =>
          ref.where('branchId', '==', this.branch.docid)
        )
        .valueChanges()
        .subscribe((offices: any) => {
          this.offices = offices.sort((a: any, b: any) => a.officeNumber - b.officeNumber);
          // Initialize dropdown states
          this.offices.forEach(office => {
            this.dropdownOpenMap[office.officeNumber] = false;
          });
        });
    }
  })
}

  showOfficeDetails(office: any) {
    this.selectedOffice = office;
    this.openOfficeDetails = true;
  }

async saveOneOffice() {
  try {
    // Validate form
    if (!this.officeForm.number) {
      throw new Error('Office number is required');
    }

    const user = await this.afAuth.currentUser;
    if (!user) throw new Error('User not authenticated');

    // Upload image if exists
    let imageUrl = this.officeForm.imageUrl;
    if (this.officeForm.imageFile) {
      imageUrl = await this.uploadImage(this.officeForm.imageFile);
    }

    // Create update object with only defined values
    const officeData: any = {
      branchId: this.branch.docid,
      officeNumber: this.officeForm.number,
      status: this.officeForm.status,
      updatedAt: new Date()

    };

    // Only add fields that have values
    if (this.officeForm.surface !== null && this.officeForm.surface !== undefined) {
      officeData.surface = this.officeForm.surface;
    }

    if (this.officeForm.price !== null && this.officeForm.price !== undefined) {
      officeData.price = this.officeForm.price;
    }

    if (this.officeForm.status === 'rented' && this.officeForm.renterName) {
      officeData.renterName = this.officeForm.renterName;
    } else {
      officeData.renterName = null; // Explicitly set to null if not rented
    }
     if (this.officeForm.sectorId ) {
    officeData.sectorId = this.officeForm.sectorId;
    }
    else{
       officeData.sectorId = "";
    }

    if (imageUrl) {
      officeData.imageUrl = imageUrl;
    }
    else{
      officeData.imageUrl = null;
    }

    if (this.editMode && this.officeToEdit) {
      const oldData = {
        status: this.officeToEdit.status,
        surface: this.officeToEdit.surface,
        renterName: this.officeToEdit.renterName,
        price: this.officeToEdit.price,
        imageUrl: this.officeToEdit.imageUrl,
        sectorId :this.officeToEdit.sectorId
      };

      const newData = {
        status: this.officeForm.status,
        surface: this.officeForm.surface,
        renterName: this.officeForm.status === 'rented' ? this.officeForm.renterName : null,
        price: this.officeForm.price,
        imageUrl: imageUrl,
        sectorId :this.officeForm.sectorId

      };

      const changes = this.getOfficeChanges(oldData, newData);

      // Update existing office
      await this.afs.collection('offices').doc(this.officeToEdit.officeId).update(newData);

      // Log the edit action if user is receptionist
      if (this.userRole === 'receptionist') {
        await this.logOfficeAction('edit', this.officeToEdit, {
          oldData: oldData,
          newData: newData
        });
      }

      // Update local array
      const index = this.offices.findIndex(o => o.officeId === this.officeToEdit.officeId);
      if (index !== -1) {
        this.offices[index] = { ...this.offices[index], ...officeData };
      }

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Office updated successfully!',
        life: 5000
      });
    } else {
      // Check for duplicate
      if (this.offices.some(o => o.officeNumber === this.officeForm.number)) {
        throw new Error(`Office number ${this.officeForm.number} already exists`);
      }

      // Create new office
      const docId = this.afs.createId();
      await this.afs.collection('offices').doc(docId).set({
        ...officeData,
        officeId: docId,
        ressources: [],
        createdAt: new Date(),
        // Ensure all required fields have values
        surface: this.officeForm.surface || 0, // Default value if undefined
        price: this.officeForm.price || 0      // Default value if undefined
      });
            const newOffice = {
        ...officeData,
        officeId: docId,
        ressources: [],
        createdAt: new Date(),
        surface: this.officeForm.surface || 0,
        price: this.officeForm.price || 0
      };
      if (this.userRole === 'receptionist') {
        await this.logOfficeAction('create', newOffice);
      }
      // Refresh the list
      this.getOffices();

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Office added successfully!',
        life: 5000
      });
    }

    this.resetOfficeForm();
    this.addOneOffice = false;
    this.editMode = false;
    this.officeToEdit = null;
  } catch (error: any) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.message,
      life: 5000
    });
  } finally {
    this.resetUploadState();
  }
 }
async saveBulkOffice() {
    try {
      // Validate form
      if (!this.bulkOfficeForm.startNumber || !this.bulkOfficeForm.endNumber) {
        throw new Error('Start and end numbers are required');
      }

      if (this.bulkOfficeForm.startNumber >= this.bulkOfficeForm.endNumber) {
        throw new Error('Start number must be less than end number');
      }

      const user = await this.afAuth.currentUser;
      if (!user) throw new Error('User not authenticated');

      // Find next available sequence
      const highestNumber = this.offices.length > 0
        ? Math.max(...this.offices.map(o => o.officeNumber))
        : 0;
      const nextAvailable = highestNumber + 1;
      const actualStart = Math.max(nextAvailable, this.bulkOfficeForm.startNumber);
      const count = this.bulkOfficeForm.endNumber - this.bulkOfficeForm.startNumber + 1;
      const actualEnd = actualStart + count - 1;

      // Upload image if exists
    let imageUrl = this.officeForm.imageUrl; // Keep the preview URL if no new file
    if (this.file) {
      imageUrl = await this.uploadImage(this.file);
    }


      // Create batch
      const batch = this.afs.firestore.batch();
      const officesRef = this.afs.collection('offices');
      const officesToLog: any[] = []; // Store offices for logging

    for (let i = 0; i < count; i++) {
      const docId = this.afs.createId();
      const officeRef = officesRef.doc(docId).ref;
      const officeNumber = actualStart + i;

      const officeData = {
        branchId: this.branch.docid,
        officeId: docId,
        officeNumber: officeNumber,
        status: this.bulkOfficeForm.status,
        surface: this.bulkOfficeForm.surface,
        renterName: this.bulkOfficeForm.status === 'rented' ? this.officeForm.renterName : null,
        price: this.bulkOfficeForm.price,
        imageUrl: imageUrl,
        sectorId: this.bulkOfficeForm.sectorId,

        ressources: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      batch.set(officeRef, officeData);
      officesToLog.push(officeData); // Add to logging array
    }


      await batch.commit();
    // Log the creation of all offices
    if (this.userRole === 'receptionist') {
      for (const office of officesToLog) {
        await this.logOfficeAction('create', office);
      }
    }
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: `Created ${count} offices (${actualStart}-${actualEnd})`,
        life: 5000
      });

      this.resetBulkOfficeForm();
      this.addBulkOffice = false;
    } catch (error: any) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: error.message,
        life: 5000
      });
    }
  }
confirmDelete(office: any) {
    this.officeToDelete = office;
    this.deleteDialogVisible = true;
  }
resetOfficeForm() {
    this.officeForm = {
      number: null,
      status: 'available',
      surface: null,
      renterName: null,
      price: null,
      imageFile: null,
            imageUrl:null,
              sectorId: null


    };
    this.imageUrl = null;
  }

resetBulkOfficeForm() {
    this.bulkOfficeForm = {
      startNumber: null,
      endNumber: null,
      status: 'available',
      surface: null,
      price:null,
      imageFile: null,
      imageUrl:null,
        sectorId: null

    };
    this.imageUrl = null;
  }
  private resetUploadState() {
  this.file = null;
  this.filename = '';
  this.fileUploaded = false;
  this.disableUploadBtn = true;
  this.uploadProgress = null;
}
getSectorName(sectorId: string): string {
  const sector = this.sectors.find(s => s.id === sectorId);
  return sector ? sector.name : '';
}
}
