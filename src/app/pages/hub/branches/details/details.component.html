<div class="no-offices text-center" *ngIf="offices.length === 0">
  <div>
    <h6>
      No Offices found for
      <strong class="violet-text">{{ branch.name }}</strong>
    </h6>
    <i class="bi bi-journal-x text-muted"></i>
    <p>You can start Add Offices one by one or bulk under your branch!</p>
    <div *ngIf="userRole === 'receptionist' || userRole === 'manager' || userRole === 'owner' || !isRegularUser">
      <button
        pButton
        pRipple
        label="Add One Office"
        class="p-button-sm me-3"
        (click)="addOneOffice = true"
      ></button>
      <button
        pButton
        pRipple
        label="Add Bulk Offices"
        class="p-button-secondary p-button-sm"
        (click)="addBulkOffice = true"
      ></button>
    </div>
  </div>
</div>

<!-- For non-empty offices state -->
<div class="offices-details" *ngIf="offices.length !== 0">
  <div class="col-12 mb-3 text-end" *ngIf="userRole === 'receptionist' || userRole === 'manager' || userRole === 'owner' || !isRegularUser">
    <button
      pButton
      pRipple
      label="Add One Office"
      icon="pi pi-plus"
      class="p-button-sm me-3"
      (click)="addOneOffice = true"
    ></button>
    <button
      pButton
      pRipple
      label="Add Bulk Offices"
      icon="pi pi-plus"
      class="p-button-secondary p-button-sm"
      (click)="addBulkOffice = true"
    ></button>
  </div>
  <div class="offices-cards row">
    <div class="col-md-4 col-sm-6 mb-4" *ngFor="let office of offices">
      <div class="office-card-container">
        <div class="office-card" (click)="showOfficeDetails(office)">
          <!-- Office number circle -->
          <div class="office-number-circle">
            {{ office.officeNumber }}
          </div>

          <!-- Status badge -->
          <div class="status-badge"
               [ngClass]="{
                 'available': office.status === 'available',
                 'rented': office.status === 'rented',
                  'under_process': office.status === 'under_process'

               }">

            {{ office.status === 'under_process' ? 'Under process' : (office.status | titlecase) }}
          </div>

          <!-- Office image -->
          <div class="office-image-container">
            <img *ngIf="office.imageUrl" [src]="office.imageUrl" alt="Office image" class="office-image">
            <div *ngIf="!office.imageUrl" class="no-image-placeholder">
              <i class="bi bi-building"></i>
            </div>
          </div>

          <!-- Details section -->
          <div class="office-details">
            <!-- Surface area -->
                    <div class="detail-item" *ngIf="office.sectorId">
          <span class="detail-label">Sector:</span>
          <span class="detail-value">
            {{ getSectorName(office.sectorId) || 'N/A' }}
          </span>
        </div>
            <div class="detail-item">
              <span class="detail-label">Surface:</span>
              <span class="detail-value">{{ office.surface || 'N/A' }} m²</span>
            </div>

            <!-- Price or renter info -->
            <div class="detail-item" *ngIf="office.status !== 'rented'">
              <span class="detail-label">Price:</span>
              <span class="detail-value">{{ office.price }} OAR/month</span>
            </div>

            <div class="detail-item" *ngIf="office.status === 'rented'">
              <span class="detail-label">Rented by:</span>
              <span class="detail-value">{{ office.renterName || 'N/A' }}</span>
            </div>
          </div>
        </div>

        <!-- Virtual tour and actions -->
    <div class="office-footer">
      <span class="virtual-tour-text">Virtual Tour</span>
      <div class="action-buttons">
        <button pButton pRipple icon="pi pi-eye" class="p-button-rounded p-button-text p-button-sm"
                (click)="viewOffice(office); $event.stopPropagation()"></button>
        <button pButton pRipple icon="pi pi-pencil" class="p-button-rounded p-button-text p-button-sm"
                (click)="editOffice(office); $event.stopPropagation()"></button>
        <button pButton pRipple icon="pi pi-trash" class="p-button-rounded p-button-text p-button-sm p-button-danger"
                (click)="confirmDelete(office); $event.stopPropagation()"></button>
      </div>
    </div>
      </div>
    </div>
  </div>
</div>

<p-sidebar
  [(visible)]="openOfficeDetails"
  class="normal-sidebar"
  position="right"
  [baseZIndex]="10000"
>
  <app-office-item [office]="selectedOffice"></app-office-item>
</p-sidebar>

<p-sidebar
  [(visible)]="addOneOffice"
  class="normal-sidebar"
  position="right"
  [baseZIndex]="10000"
>
  <h3>Office Details</h3>

  <!-- Office Number -->
  <div class="field">
    <label for="officeNumber" class="block">Office Number</label>
    <input
      id="officeNumber"
      type="number"
      aria-describedby="officeNumber-help"
      pInputText
    [(ngModel)]="officeForm.number"
        [disabled]="editMode"

      required
    />
  </div>
  <div class="field mb-3">
  <label for="sector" class="block">Sector</label>
  <p-dropdown
    id="sector"
    [options]="sectors"
    [(ngModel)]="officeForm.sectorId"
    optionLabel="name"
    optionValue="id"
    [showClear]="true"
    placeholder="Select a sector"
    [style]="{width: '100%'}">
  </p-dropdown>
</div>
<div class="field mb-3">
  <label for="price" class="block">Price per Month</label>
  <input
    id="price"
    type="number"
    pInputText
    [(ngModel)]="officeForm.price"
    min="0"
    required
  />
</div>
  <!-- Status -->
  <div class="field mb-3">
    <label for="status" class="block">Status *</label>
    <p-dropdown
      id="status"
      [options]="statusOptions"
      [(ngModel)]="officeForm.status"
      optionLabel="label"
      optionValue="value"
      [style]="{ width: '100%' }"
    ></p-dropdown>
  </div>

  <!-- Surface Area -->
  <div class="field mb-3">
    <label for="surface" class="block">Surface Area (m²)</label>
    <input
      id="surface"
      type="number"
      pInputText
      [(ngModel)]="officeForm.surface"
      min="1"
    />
  </div>

  <!-- Rented By (conditional) -->
  <div class="field mb-3" *ngIf="officeForm.status === 'rented'">
    <label for="renterName" class="block">Rented By *</label>
    <input
      id="renterName"
      type="text"
      pInputText
      [(ngModel)]="officeForm.renterName"
      placeholder="Type the renter name"
      required
    />
  </div>

  <!-- Office Image Upload -->
<div class="field mb-3">
  <label class="block">Office Image</label>
  <p-fileUpload
    mode="basic"
    name="officeImage"
    accept="image/*"
    chooseLabel="Browse"
    (onSelect)="onFileSelected($event)"
    [maxFileSize]="50000000"
    [showUploadButton]="false"
    [showCancelButton]="false"
  ></p-fileUpload>

  <div *ngIf="uploadProgress !== null" class="upload-progress">
    <div class="progress-info">
      <span>Uploading: {{ uploadProgress | number:'1.0-0' }}%</span>
      <span *ngIf="uploadProgress === 100">Processing...</span>
    </div>
    <p-progressBar [value]="uploadProgress" [showValue]="false"></p-progressBar>
  </div>

  <div *ngIf="officeForm.imageUrl" class="image-preview">
    <img [src]="officeForm.imageUrl" alt="Office preview">
    <button pButton pRipple icon="pi pi-times" class="p-button-rounded p-button-text p-button-sm remove-image"
            (click)="removeImage(); $event.stopPropagation()"></button>
  </div>
</div>

  <!-- Buttons -->
  <div class="flex justify-content-end mt-4">
    <button
      pButton
      pRipple
      label="Cancel"
      class="p-button-text mr-2"
      (click)="addOneOffice = false"
    ></button>

<button
  pButton
  pRipple
  [label]="editMode ? 'Update Office' : 'Add One Office'"
  class="p-button-sm me-3 mt-3"
  [disabled]="officeForm.number === null || officeForm.number === undefined || !officeForm.status || (officeForm.status === 'rented' && !officeForm.renterName)"
  (click)="saveOneOffice()"
></button>

  </div>
</p-sidebar>


<p-sidebar [(visible)]="addBulkOffice" class="normal-sidebar" position="right" [baseZIndex]="10000" (onHide)="resetBulkOfficeForm()">
  <h3>Add Bulk Offices</h3>

  <div *ngIf="offices.length > 0" class="mb-3 p-3 border-round" style="background-color: #f8f9fa;">
    <p>Existing offices: {{ offices[0].officeNumber }} to {{ offices[offices.length-1].officeNumber }}</p>
    <p>Next available sequence starts at: {{ (offices[offices.length-1].officeNumber + 1) || 1 }}</p>
  </div>

  <div class="grid">
    <div class="col-6 field mb-3">
      <label for="bulkStartNumber" class="block">Start Number *</label>
      <input
        id="bulkStartNumber"
        type="number"
        pInputText
        [(ngModel)]="bulkOfficeForm.startNumber"
        min="1"
        required
      />
    </div>
    <div class="col-6 field mb-3">
      <label for="bulkEndNumber" class="block">End Number *</label>
      <input
        id="bulkEndNumber"
        type="number"
        pInputText
        [(ngModel)]="bulkOfficeForm.endNumber"
        [min]="bulkOfficeForm.startNumber ? bulkOfficeForm.startNumber + 1 : 2"
        required
      />
    </div>
  </div>
  <div class="field mb-3">
  <label for="sector" class="block">Sector</label>
  <p-dropdown
    id="sector"
    [options]="sectors"
    [(ngModel)]="officeForm.sectorId"
    optionLabel="name"
    optionValue="id"
    [showClear]="true"
    placeholder="Select a sector"
    [style]="{width: '100%'}">
  </p-dropdown>
</div>
<div class="field mb-3">
  <label for="price" class="block">Price per Month</label>
  <input
    id="price"
    type="number"
    pInputText
    [(ngModel)]="officeForm.price"
    min="0"
    required
  />
</div>
  <div class="field mb-3">
    <label for="bulkStatus" class="block">Status *</label>
    <p-dropdown
      id="bulkStatus"
      [options]="statusOptions"
      [(ngModel)]="bulkOfficeForm.status"
      optionLabel="label"
      optionValue="value"
      [style]="{ width: '100%' }"
    ></p-dropdown>
  </div>

  <div class="field mb-3">
    <label for="bulkSurface" class="block">Surface Area (m²)</label>
    <input
      id="bulkSurface"
      type="number"
      pInputText
      [(ngModel)]="bulkOfficeForm.surface"
      min="1"
    />
  </div>

  <div class="field mb-3" *ngIf="bulkOfficeForm.status === 'rented'">
    <label for="bulkCompany" class="block">Rented By *</label>

    <input
      id="bulkCompany"
      type="text"
      pInputText
      [(ngModel)]="officeForm.renterName"
      placeholder="Type the renter name"
    />
  </div>

  <div class="field mb-3">
    <label class="block">Office Image (for all)</label>
  <p-fileUpload
    mode="basic"
    name="officeImage"
    accept="image/*"
    chooseLabel="Browse"
    (onSelect)="onFileSelected($event)"
    [maxFileSize]="50000000"
    [showUploadButton]="false"
    [showCancelButton]="false"
  ></p-fileUpload>
  <div *ngIf="uploadProgress !== null" class="mt-2">
    <p-progressBar [value]="uploadProgress"></p-progressBar>
  </div>

  <img *ngIf="officeForm.imageUrl"
       [src]="officeForm.imageUrl"
       class="mt-2"
       style="max-width: 100%; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;">
</div>


  <div *ngIf="bulkOfficeForm.startNumber && bulkOfficeForm.endNumber" class="mb-3">
    <p>You're creating offices {{ bulkOfficeForm.startNumber }} to {{ bulkOfficeForm.endNumber }}
      ({{ bulkOfficeForm.endNumber - bulkOfficeForm.startNumber + 1 }} offices)</p>
    <p *ngIf="offices.length > 0 && bulkOfficeForm.startNumber <= offices[offices.length-1].officeNumber" class="p-error">
      Warning: Some numbers may already exist. Actual creation will start from {{ offices[offices.length-1].officeNumber + 1 }}.
    </p>
  </div>

  <div class="flex justify-content-end mt-4">
    <button
      pButton
      pRipple
      label="Cancel"
      class="p-button-text mr-2"
      (click)="addBulkOffice = false"
    ></button>
    <button
      pButton
      pRipple
      label="Create Offices"
      class="p-button-sm"
      [disabled]="!bulkOfficeForm.startNumber || !bulkOfficeForm.endNumber || bulkOfficeForm.startNumber >= bulkOfficeForm.endNumber"
      (click)="saveBulkOffice()"
    ></button>
  </div>
</p-sidebar>


<p-dialog header="Confirm Delete" [(visible)]="deleteDialogVisible" [style]="{width: '400px'}" [modal]="true" [draggable]="false">
  <div class="confirmation-content flex align-items-center">
    <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem; color: #f8c102"></i>
    <div>
      <h4>Delete Office {{ officeToDelete?.officeNumber }}?</h4>
      <p>This action cannot be undone. All office data will be permanently deleted.</p>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-outlined p-button-secondary" (click)="deleteDialogVisible = false"></button>
    <button pButton pRipple label="Delete" icon="pi pi-trash" class="p-button-danger" (click)="deleteOffice()"></button>
  </ng-template>
</p-dialog>
