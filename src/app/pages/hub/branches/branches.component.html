<p-toolbar>
  <div class="p-toolbar-group-left">
    <h4 class="ms-3 mt-2">Branches</h4>
  </div>

  <!-- Only show toolbar buttons for admin/manager users with branches -->
  <div class="p-toolbar-group-right" *ngIf="!addBranchView && !isRegularUser && branches.length > 0">
    <p-button
      icon="pi pi-plus"
      label="New Branch"
      class="me-2 p-primary-button"
      (click)="addBranchView = true"
    ></p-button>
    <p-button
      icon="pi pi-map-marker"
      label="Locations"
      styleClass="p-button-danger"
      data-bs-toggle="offcanvas"
      data-bs-target="#locationsCanvas"
      aria-controls="locationsExample"
    ></p-button>
  </div>
  <div class="p-toolbar-group-right" *ngIf="addBranchView && !isRegularUser">
    <button pButton pRipple (click)="addBranchView = false" type="button" icon="pi pi-times" label="Cancel" class="p-button-outlined p-button-secondary"></button>
  </div>
</p-toolbar>

<div class="main-container mt-4" *ngIf="!addBranchView">
  <p-table
    [value]="branches"
    selectionMode="single"
    dataKey="docid"
    [tableStyle]="{ 'min-width': '60rem' }"
  >
    <ng-template pTemplate="header">
      <tr>
        <th style="width: 5rem" *ngIf="!isRegularUser"></th>
        <th>Name</th>
        <th>Address</th>
        <th *ngIf="!isRegularUser"></th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-branch let-expanded="expanded">
      <tr>
        <!-- Hide toggle button for regular users -->
        <td *ngIf="!isRegularUser">
          <button
            type="button"
            pButton
            pRipple
            [pRowToggler]="branch"
            class="p-button-text p-button-rounded p-button-plain"
            [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
          ></button>
        </td>
        <td>{{ branch.name }}</td>
        <td>{{ branch.address }}</td>
        <td *ngIf="!isRegularUser">


        </td>
      </tr>
      <!-- Auto-expand row for regular users -->
<tr *ngIf="isRegularUser">
  <td colspan="4">
    <div class="p-3">
      <app-details [branch]="branch" [isRegularUser]="isRegularUser" [userRole]="userRole"></app-details>
    </div>
  </td>
</tr>
    </ng-template>
<ng-template pTemplate="rowexpansion" let-branch *ngIf="!isRegularUser">
  <tr>
    <td colspan="4">
      <div class="p-3">
        <app-details [branch]="branch" [isRegularUser]="isRegularUser"></app-details>
      </div>
    </td>
  </tr>
</ng-template>
  </p-table>
</div>

<!-- Only show add branch form for admin/manager users -->
<div class="main-container mx-auto mt-4" style="max-width: 800px;" *ngIf="addBranchView && !isRegularUser">
  <app-add-branch></app-add-branch>
</div>

<p-toast></p-toast>
<p-confirmPopup></p-confirmPopup>

<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="locationsCanvas"
  aria-labelledby="locationsCanvasLabel"
  *ngIf="!isRegularUser"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="locationsCanvasLabel">Branches Location</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body p-0">
    <iframe
      src="https://www.google.com/maps/embed?pb=!1m16!1m12!1m3!1d145665.***********!2d51.34617190504759!3d25.244170153732828!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!2m1!1shub%20business%20!5e0!3m2!1sen!2sqa!4v1700936005566!5m2!1sen!2sqa"
      height="600"
      style="border: 0; width: 100% !important"
      allowfullscreen=""
      loading="lazy"
      referrerpolicy="no-referrer-when-downgrade"
    ></iframe>
  </div>
</div>
