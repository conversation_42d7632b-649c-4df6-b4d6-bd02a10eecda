import { AfterViewInit, Component, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireAuth } from '@angular/fire/compat/auth';

@Component({
  selector: 'app-add-branch',
  templateUrl: './add-branch.component.html',
  styleUrls: ['./add-branch.component.scss'],
})
export class AddBranchComponent implements OnInit, AfterViewInit {
  form: FormGroup;
  map!: google.maps.Map;
  autocomplete!: google.maps.places.Autocomplete;
  markers: google.maps.Marker[] = [];

  constructor(
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth
  ) {
    this.form = new FormGroup({
      uid: new FormControl(''),
      name: new FormControl('', Validators.required),
      description: new FormControl(''),
      address: new FormControl('', Validators.required),
      lat: new FormControl(''),
      lng: new FormControl(''),
    });
  }

  ngOnInit() {
    // Removed map initialization from here
  }

  async ngAfterViewInit() {
    await this.initMap();
    this.initAutocomplete();
  }

  async initMap() {
    const { Map } = (await google.maps.importLibrary('maps')) as google.maps.MapsLibrary;
    this.map = new Map(document.getElementById('map') as HTMLElement, {
      center: { lat: 25.28656798692695, lng: 51.53085437354816 },
      zoom: 12,
    });
  }

  initAutocomplete() {
    const input = document.getElementById("addressInput") as HTMLInputElement;
    this.autocomplete = new google.maps.places.Autocomplete(input, {
      fields: ["geometry", "name"]
    });

    this.autocomplete.addListener('place_changed', () => {
      const place = this.autocomplete.getPlace();
      if (place.geometry) {
        const location = place.geometry.location;
        this.form.controls['lat'].setValue(location!.lat());
        this.form.controls['lng'].setValue(location!.lng());
        this.map.setCenter(location!);

        // Clear out the old markers.
        this.markers.forEach((marker) => {
          marker.setMap(null);
        });
        this.markers = [];

        // Create a new marker for the selected place.
        const marker = new google.maps.Marker({
          position: location,
          map: this.map,
        });
        this.markers.push(marker);
      }
    });
  }

  addBranch() {
    this.afAuth.authState.subscribe(user => {
      if(user) {
        const docid = this.afs.createId();
        this.form.controls['uid'].setValue(user.uid);

        const BranchDocRef = this.afs.doc(
          `branches/${docid}`
        );

        BranchDocRef.set({
          uid: user.uid,
          docid: docid,
          name: this.form.value.name,
          address: this.form.value.address,
          lat: this.form.value.lat,
          lng: this.form.value.lng,
        }).then((res) => {
          console.log(res);
          this.form.reset();
        });
      }
    });
  }
}