<p-toolbar>
  <div class="p-toolbar-group-left mb-3">
    <h4 class="ms-3 mt-2">Suppliers</h4>
  </div>

  <div class="p-toolbar-group-right mb-3">
    <p-button
      icon="pi pi-plus"
      label="New Supplier"
      class="me-2"
      data-bs-toggle="offcanvas"
      data-bs-target="#suplierCanvas"
      aria-controls="branchesExample"
    ></p-button>
  </div>
</p-toolbar>

<p-table
  #dt
  [value]="supliers"
  [(selection)]="selectedSuplier"
  dataKey="id"
  [rowHover]="true"
  [rows]="10"
  [showCurrentPageReport]="true"
  [rowsPerPageOptions]="[10, 25, 50]"
  [loading]="loading"
  [paginator]="true"
  currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
  [filterDelay]="0"
  [globalFilterFields]="[
    'name',
    'country.name',
    'representative.name',
    'status'
  ]"
>
  <ng-template pTemplate="caption">
    <div class="table-header d-flex justify-content-between">
      <div>List of Supliers</div>
      <span class="p-input-icon-left ms-auto">
        <i class="pi pi-search"></i>
        <input
          pInputText
          type="text"
          (input)="dt.filterGlobal($event.target, 'contains')"
          placeholder="Global Search"
        />
      </span>
    </div>
  </ng-template>
  <ng-template pTemplate="header">
    <tr>
      <th style="width: 4rem">
        <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
      </th>
      <th pSortableColumn="name" style="min-width: 14rem">
        <div class="flex justify-content-between align-items-center">
          Name
          <p-sortIcon field="name"></p-sortIcon>
          <p-columnFilter
            type="text"
            field="name"
            display="menu"
            class="ml-auto"
          ></p-columnFilter>
        </div>
      </th>
      <th pSortableColumn="phone" style="min-width: 14rem">
        <div class="flex justify-content-between align-items-center">
          Phone
          <p-sortIcon field="phone"></p-sortIcon>
          <p-columnFilter
            type="text"
            field="phone"
            display="menu"
            class="ml-auto"
          ></p-columnFilter>
        </div>
      </th>
      <th pSortableColumn="email" style="min-width: 14rem">
        <div class="flex justify-content-between align-items-center">
          Email
          <p-sortIcon field="email"></p-sortIcon>
        </div>
      </th>
      <th pSortableColumn="whatsapp" style="min-width: 10rem">
        <div class="flex justify-content-between align-items-center">
          Whatsapp
          <p-sortIcon field="whatsapp"></p-sortIcon>
        </div>
      </th>
      <th pSortableColumn="createdBy" style="min-width: 10rem">
        <div class="flex justify-content-between align-items-center">
          Created By
          <p-sortIcon field="createdBy"></p-sortIcon>
        </div>
      </th>
      <th pSortableColumn="address1" style="min-width: 10rem">
        <div class="flex justify-content-between align-items-center">
          Address 1
          <p-sortIcon field="address1"></p-sortIcon>
        </div>
      </th>
      <th pSortableColumn="address2" style="min-width: 10rem">
        <div class="flex justify-content-between align-items-center">
          Address 2
          <p-sortIcon field="address2"></p-sortIcon>
        </div>
      </th>
      <th pSortableColumn="city" style="min-width: 10rem">
        <div class="flex justify-content-between align-items-center">
          City
          <p-sortIcon field="city"></p-sortIcon>
        </div>
      </th>
      <th pSortableColumn="country" style="min-width: 10rem">
        <div class="flex justify-content-between align-items-center">
          Country
          <p-sortIcon field="country"></p-sortIcon>
        </div>
      </th>
      <th pSortableColumn="zipCode" style="min-width: 10rem">
        <div class="flex justify-content-between align-items-center">
          Zip Code
          <p-sortIcon field="zipCode"></p-sortIcon>
        </div>
      </th>
      <th style="width: 5rem"></th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-suplier>
    <tr class="p-selectable-row">
      <td>
        <p-tableCheckbox [value]="suplier"></p-tableCheckbox>
      </td>
      <td>
        <span class="p-column-title">Name</span>
        {{ suplier.name }}
      </td>
      <td>
        <span class="p-column-title">Phone</span>
        {{ suplier.phone }}
      </td>
      <td>
        <span class="p-column-title">Email</span>
        {{ suplier.email }}
      </td>
      <td>
        <span class="p-column-title">Whatsapp</span>
        {{ suplier.whatsapp }}
      </td>

      <td>
        <span class="p-column-title">Created By</span>
        {{ suplier.createdBy }}
      </td>

      <td>
        <span class="p-column-title">Address 1</span>
        {{ suplier.address1 }}
      </td>
      <td>
        <span class="p-column-title">Address 2</span>
        {{ suplier.address2 }}
      </td>
      <td>
        <span class="p-column-title">City</span>
        {{ suplier.city }}
      </td>
      <td>
        <span class="p-column-title">Country</span>
        {{ suplier.country }}
      </td>
      <td>
        <span class="p-column-title">Zip Code</span>
        {{ suplier.zipCode }}
      </td>
      <td>
        <div class="dropdown">
          <button
            pButton
            pRipple
            type="button"
            styleClass="p-button-sm"
            label=""
            class="p-button-help p-button-text"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            <i class="bi bi-three-dots-vertical"></i>
          </button>
          <ul class="dropdown-menu">
            <li>
              <a
                class="dropdown-item"
                data-bs-toggle="offcanvas"
                href="#suplierUpdateCanvas{{ suplier.id }}"
                aria-controls="detailsExample"
                ><i class="bi bi-file-earmark-text"></i> Update</a
              >
            </li>
            <li>
              <a class="dropdown-item" href="#"
                ><i class="bi bi-pencil-square"></i> Delete</a
              >
            </li>
          </ul>

          <div
            class="offcanvas offcanvas-end"
            tabindex="-1"
            id="suplierUpdateCanvas{{ suplier.id }}"
            aria-labelledby="detailsCanvasLabel"
          >
            <div class="offcanvas-header">
              <h5 class="offcanvas-title" id="detailsCanvasLabel">
                {{ suplier.name }} Edit
              </h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="offcanvas"
                aria-label="Close"
              ></button>
            </div>
            <div class="offcanvas-body p-0">
              <app-add-suplier
                [suplier]="suplier"
                [isEdit]="true"
              ></app-add-suplier>
            </div>
          </div>
        </div>
      </td>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="8">No customers found.</td>
    </tr>
  </ng-template>
</p-table>

<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="suplierCanvas"
  aria-labelledby="suplierCanvasLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="suplierCanvasLabel">New Suplier</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body p-0">
    <app-add-suplier [isEdit]="false"></app-add-suplier>
  </div>
</div>
