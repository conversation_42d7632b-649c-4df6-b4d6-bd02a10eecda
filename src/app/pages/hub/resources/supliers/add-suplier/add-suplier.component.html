<div class="form-container">
    <form [formGroup]="form" class="p-3">
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="name"> 
            <label for="float-input">Suplier Name</label>
        </span>
        
        <h4 class="mt-4 mb-4">Contact</h4>
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="phone"> 
            <label for="float-input">Phone Number</label>
        </span>
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="email"> 
            <label for="float-input">Email</label>
        </span>
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="whatsapp"> 
            <label for="float-input">Whatsapp Number</label>
        </span>
        
        <h4 class="mt-4 mb-4">Address</h4>
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="address1"> 
            <label for="float-input">Address 1</label>
        </span>
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="address2"> 
            <label for="float-input">Address 2</label>
        </span>
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="city"> 
            <label for="float-input">City</label>
        </span>
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="country"> 
            <label for="float-input">Country</label>
        </span>
        <span class="p-float-label col-12 mt-4 mb-4">
            <input id="float-input" type="text" class="col-12" pInputText formControlName="zipCode"> 
            <label for="float-input">Zip Code</label>
        </span>

        <div class="mt-4 mb-4 form-footer d-flex justify-content-between px-0 pb-3">
            <div>
                <button pButton pRipple type="button" [disabled]="!form.valid" (click)="addSuplier()" label="Save"></button>
            </div>
            <div>
                <button pButton pRipple type="button" data-bs-dismiss="offcanvas" aria-label="Close" label="Cancel" class="p-button-secondary p-button-text"></button>
            </div>
        </div>
    </form>
</div>