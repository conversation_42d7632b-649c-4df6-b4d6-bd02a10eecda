import { Component, Input, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { UserService } from 'src/app/shared/services/user.service';

@Component({
  selector: 'app-add-suplier',
  templateUrl: './add-suplier.component.html',
  styleUrls: ['./add-suplier.component.scss'],
})
export class AddSuplierComponent implements OnInit {
  @Input() isEdit!: boolean;
  @Input() suplier: any;

  form: FormGroup;

  constructor(
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth,
    private userService: UserService
  ) {
    this.form = new FormGroup({
      ownerId: new FormControl(''),
      createdBy: new FormControl(''),
      name: new FormControl('', Validators.required),
      phone: new FormControl('', Validators.required),
      email: new FormControl(''),
      whatsapp: new FormControl(''),
      address1: new FormControl('', Validators.required),
      address2: new FormControl(''),
      city: new FormControl(''),
      country: new FormControl(''),
      zipCode: new FormControl(''),
    });
  }

  ngOnInit() {
    console.log(this.isEdit);
    /*console.log(this.suplier);
    console.log(this.isEdit);
    if(this.isEdit) {
      this.form.controls['uid'].setValue(this.suplier.uid)
    }*/
  }

  addSuplier() {
    //console.log(this.userService.currentUser, this.userService.userRole);
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        if (this.userService.userRole === 'owner') {
          this.form.controls['ownerId'].setValue(
            this.userService.currentUser.id
          );
        } else {
          this.form.controls['ownerId'].setValue(
            this.userService.currentUser.adminId
          );
        }
        const docid = this.afs.createId();

        this.afs
          .collection('users')
          .doc(user.uid)
          .valueChanges()
          .subscribe((res: any) => {
            this.form.controls['createdBy'].setValue(res.displayName);
            const docId = this.afs.createId();
            this.afs
              .collection('supliers')
              .doc(docId)
              .set(this.form.value)
              .then(() => {
                console.log('Suplier added successfully');
              })
              .catch((error) => {
                console.log(error);
              });
          });
      } else {
        console.log('Unauthenticated');
      }
    });
  }
}
