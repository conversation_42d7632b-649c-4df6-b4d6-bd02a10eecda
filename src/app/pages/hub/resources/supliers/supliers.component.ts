import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { UserService } from 'src/app/shared/services/user.service';

@Component({
  selector: 'app-supliers',
  templateUrl: './supliers.component.html',
  styleUrls: ['./supliers.component.scss'],
})
export class SupliersComponent implements OnInit, OnChanges, AfterViewInit {
  supliers: any[] = [];
  selectedSuplier: any;
  loading: boolean = true;

  constructor(
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth,
    private userService: UserService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.getMySupliers();
  }

  ngAfterViewInit(): void {
    this.getMySupliers();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes) {
      this.getMySupliers();
    }
  }

  async getMySupliers() {
    this.loading = true;

    try {
      const user = await this.userService.getCurrentUser();

      if (!user) {
        this.supliers = [];
        this.loading = false;
        return;
      }
 
      this.afs
        .collection('supliers', (ref) =>
          ref.where('ownerId', '==', user.adminId)
        )
        .valueChanges()
        .subscribe(
          (res: any) => {
            this.supliers = res;
            this.loading = false;
            console.log(this.supliers);
            this.cdr.detectChanges();
          },
          (error) => {
            console.log(error);
            this.loading = false;
            this.cdr.detectChanges();
          }
        );
    } catch (err) {
      console.error('Error getting user or suppliers:', err);
      this.loading = false;
    }
  }
}
