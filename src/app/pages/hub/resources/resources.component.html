<p-toolbar>
    <div class="p-toolbar-group-left">
      <h4 class="ms-3 mt-2">Resources</h4>
    </div>
  
    <div class="p-toolbar-group-right">
      <p-button
        icon="pi pi-plus"
        label="New Resource"
        class="me-2"
        data-bs-toggle="offcanvas"
        data-bs-target="#resourcesCanvas"
        aria-controls="resourcesExample"
      ></p-button>
      <p-button
        icon="bi bi-plus"
        label="New Resource Type"
        styleClass="p-button-success me-2"
        data-bs-toggle="offcanvas"
        data-bs-target="#resourcesTypeCanvas"
        aria-controls="resourcesTypeExample"
      ></p-button>
    </div>
  </p-toolbar>


  <div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="resourcesCanvas"
  aria-labelledby="resourcesCanvasLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="resourcesCanvasLabel">New Resource</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body">
    <app-add-resource></app-add-resource>
  </div>
</div>

<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="resourcesTypeCanvas"
  aria-labelledby="resourcesTypeLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="resourcesTypeLabel">
      New Resource Type
    </h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body">
    <app-add-resource-type></app-add-resource-type>
  </div>
</div>