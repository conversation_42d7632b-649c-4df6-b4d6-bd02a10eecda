<p-toolbar>
  <div class="p-toolbar-group-left mb-3">
    <h4 class="ms-3 mt-2">Inventory Ressources</h4>
  </div>

  <div class="p-toolbar-group-right mb-3">
    <p-button
      icon="pi pi-plus"
      label="New Inventory Entry"
      class="me-2"
      data-bs-toggle="offcanvas"
      data-bs-target="#EntryCanvas"
      aria-controls="EntryExample"
    ></p-button>
  </div>
</p-toolbar>



<div style="width: 100%; height: 400px; background: linear-gradient(90deg, #ffeeee, #ff8888, #ff0000) ;">

</div>


<div
  class="offcanvas offcanvas-end"
  tabindex="-1"
  id="EntryCanvas"
  aria-labelledby="EntryCanvasLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="EntryCanvasLabel">New Inventory Entry</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
    ></button>
  </div>
  <div class="offcanvas-body p-0">
    <app-add-entry [isEdit]="false"></app-add-entry>
  </div>
</div>
