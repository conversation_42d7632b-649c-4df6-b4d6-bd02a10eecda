<div class="p-3">
  <form [formGroup]="form" (ngSubmit)="addEntry()">
    <div class="field mb-3">
      <label for="suplier">Select Suplier</label>
      <p-dropdown
        class="col-12"
        id="suplier"
        [options]="supliers"
        formControlName="suplierId"
        placeholder="Select a City"
        optionValue="docId"
        optionLabel="name"
        [showClear]="true"
      ></p-dropdown>
    </div>
    <div class="field mb-3">
      <label for="ressourceId" class="block">Ressource Item</label>
      <input
        id="ressourceId"
        type="text"
        aria-describedby="ressourceId-help"
        pInputText
        formControlName="ressourceId"
      />
    </div>

    <div class="row col-12 mb-3">
        <div class="col-6">
            <div class="field">
                <label for="unitPrice">Unit Price</label>
                <p-inputNumber inputId="unitPrice" formControlName="unitPrice">
                </p-inputNumber>
            </div>
        </div>
        <div class="col-6">
            <div class="field">
                <label for="totalPrice">Total Price</label>
                <p-inputNumber inputId="totalPrice" formControlName="totalPrice">
                </p-inputNumber>
            </div>
        </div>
    </div>

    <div class="row col-12 mb-3">
        <div class="col-6">
            <div class="field">
                <label for="quantity">Quantity</label>
                <p-inputNumber inputId="quantity" formControlName="quantity">
                </p-inputNumber>
            </div>
        </div>
        <div class="col-6">
            <div class="field">
                <label for="qtyAlert">Min Quantity Alert</label>
                <p-inputNumber inputId="qtyAlert" formControlName="qtyAlert">
                </p-inputNumber>
            </div>
        </div>
    </div>

    <div class="field mb-3">
        <h5>Invoice File Input Later</h5>
    </div>

    <div class="field mb-3">
      <label for="notes" class="block">Notes</label>
      <textarea
        id="notes"
        pInputTextarea
        formControlName="notes"
      ></textarea>
    </div>

    <button pButton pRipple type="submit" label="Save Entry"></button>
  </form>
</div>
