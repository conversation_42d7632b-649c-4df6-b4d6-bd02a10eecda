import { Component, Input, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-add-entry',
  templateUrl: './add-entry.component.html',
  styleUrls: ['./add-entry.component.scss'],
})
export class AddEntryComponent implements OnInit {
  @Input() isEdit: boolean = false;

  supliers: any[] = [];
  form: FormGroup;

  constructor(private afAuth: AngularFireAuth, private afs: AngularFirestore) {
    this.form = new FormGroup({
      docId: new FormControl(''),
      branchId: new FormControl(''),
      createdBy: new FormControl(''),
      ressourceId: new FormControl('cdcdvfdoksdns32nkjb32e', Validators.required),
      suplierId: new FormControl('', Validators.required),
      unitPrice: new FormControl(0, Validators.required),
      totalPrice: new FormControl(0, Validators.required),
      quantity: new FormControl(0, Validators.required),
      invoice: new FormControl(''),
      qtyAlert: new FormControl(10),
      notes: new FormControl(''),
    });
  }

  ngOnInit(): void {
    this.getMySupliers();
  }

  getMySupliers() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.afs
          .collection('supliers', (ref) => ref.where('uid', '==', user.uid))
          .valueChanges()
          .subscribe((data) => {
            console.log(data);
            this.supliers = data;
          });
      }
    });
  }

  addEntry() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        const docId = this.afs.createId();
        this.form.controls['docId'].setValue(docId);
        this.afs
          .collection('users')
          .doc(user.uid)
          .valueChanges()
          .subscribe((res: any) => {
            this.form.controls['branchId'].setValue(res.branch);
            this.form.controls['createdBy'].setValue(res.displayName);
            this.form.controls['docId'].setValue(this.afs.createId());
            this.afs.collection('inventory').doc(docId).set(this.form.value).then(() => {
            console.log('Entry added successfully');
          }).catch((err) => {
            console.log(err);
          });
          });
      }
    });
  }
}
