import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { InventoryRoutingModule } from './inventory-routing.module';
import { InventoryComponent } from './inventory.component';
import { PrimengModule } from 'src/app/shared/modules/primeng.module';
import { AddEntryComponent } from './add-entry/add-entry.component';


@NgModule({
  declarations: [
    InventoryComponent,
    AddEntryComponent
  ],
  imports: [
    CommonModule,
    InventoryRoutingModule,
    PrimengModule
  ]
})
export class InventoryModule { }
