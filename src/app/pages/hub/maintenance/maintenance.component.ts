import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { MessageService } from 'primeng/api';
import { finalize } from 'rxjs';
import { UserService } from 'src/app/shared/services/user.service';

@Component({
  selector: 'app-maintenance',
  templateUrl: './maintenance.component.html',
  styleUrls: ['./maintenance.component.scss'],
  providers: [MessageService],
})
export class MaintenanceComponent {
  displayAddMaintenance = false;
  form: FormGroup;
  isEditMode = false;
  selectedRequest: any = null;
  uploading = false;
  maintenanceRequests: any[] = [];

  maintenanceTypes = [
    { label: 'Electrical', value: 'electrical' },
    { label: 'HVAC', value: 'hvac' },
    { label: 'Cleaning', value: 'cleaning' },
    { label: 'Furniture', value: 'furniture' },
    { label: 'Plumbing', value: 'plumbing' },
    { label: 'Internet/WiFi', value: 'internet' },
    { label: 'Painting', value: 'painting' },
    { label: 'Pest Control', value: 'pest_control' },
    { label: 'Security', value: 'security' },
  ];

  urgencyLevels = [
    { label: 'Low', value: 'low' },
    { label: 'Normal', value: 'normal' },
    { label: 'High', value: 'high' },
  ];

  sectors = [
    { label: 'Sector A', value: 'sector_a' },
    { label: 'Sector B', value: 'sector_b' },
    { label: 'Sector C', value: 'sector_c' },
  ];

  offices = [
    { label: 'Office 101', value: 'office_101' },
    { label: 'Office 202', value: 'office_202' },
    { label: 'Office 303', value: 'office_303' },
  ];

  statuses = [
    'pending',
    'approved',
    'assigned',
    'in_progress',
    'completed',
    'rejected',
  ];

  statusLabels: { [key: string]: string } = {
    pending: 'Pending Approval',
    approved: 'Approved',
    assigned: 'Assigned',
    in_progress: 'In Progress',
    completed: 'Completed',
    rejected: 'Rejected',
  };

  constructor(
    private fb: FormBuilder,
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth,
    private storage: AngularFireStorage,
    private messageService: MessageService,
    public userService: UserService
  ) {
    this.form = this.fb.group({
      type: [null, Validators.required],
      sector: [null, Validators.required],
      office: [null, Validators.required],
      requestedBy: [null],
      urgency: [null, Validators.required],
      preferredTime: [null],
      description: [null],
      imageUrl: [null],
      status: ['pending'],
      createdAt: [new Date()],
      updatedAt: [new Date()],
      ownerId: [''],
      createdBy: [''],
    });
  }

  ngOnInit() {
    this.fetchMaintenanceRequests();
  }

  getRequestsByStatus(status: string) {
    return this.maintenanceRequests?.filter(
      (req) => (req.status || 'pending') === status
    );
  }

  fetchMaintenanceRequests() {
    this.afAuth.authState.subscribe((user) => {
      if (!user) return;

      const checkUserReady = () => {
        const currentUser = this.userService.currentUser;

        if (currentUser?.id || currentUser?.adminId) {
          const ownerId = currentUser.adminId || currentUser.id;

          this.afs
            .collection('maintenanceRequests', (ref) =>
              ref.where('ownerId', '==', ownerId)
            )
            .valueChanges({ idField: 'id' })
            .subscribe((requests: any[]) => {
              this.maintenanceRequests = requests.sort(
                (a, b) => b.createdAt?.seconds - a.createdAt?.seconds
              );

              console.log(this.maintenanceRequests);
            });
        } else {
          setTimeout(checkUserReady, 300);
        }
      };

      checkUserReady();
    });
  }

  showAddMaintenanceDialog() {
    this.displayAddMaintenance = true;
  }

  editRequest(request: any) {
    this.isEditMode = true;
    this.selectedRequest = request;
    this.form.patchValue(request);
    this.displayAddMaintenance = true;
  }

  saveRequest() {
    if (this.form.invalid) return;

    if (this.isEditMode) {
      const updatedData = { ...this.selectedRequest, ...this.form.value };
      this.updateRequest(updatedData);
    } else {
      this.addMaintenanceRequest();
    }

    this.form.reset();
    this.isEditMode = false;
    this.selectedRequest = null;
    this.displayAddMaintenance = false;
  }

  onUpload(event: any) {
    const file = event.files[0];
    if (!file) return;

    const filePath = `maintenance_images/${Date.now()}_${file.name}`;
    const fileRef = this.storage.ref(filePath);
    const task = this.storage.upload(filePath, file);
    this.uploading = true;

    task
      .snapshotChanges()
      .pipe(
        finalize(() => {
          fileRef.getDownloadURL().subscribe((url) => {
            // Delete old image if exists and in edit mode
            if (this.isEditMode && this.selectedRequest?.imageUrl) {
              this.deleteOldImage(this.selectedRequest.imageUrl);
            }

            this.form.patchValue({ imageUrl: url });
            this.uploading = false;
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Image uploaded successfully',
            });
          });
        })
      )
      .subscribe();
  }

  deleteOldImage(imageUrl: string) {
    const storageRef = this.storage.storage.refFromURL(imageUrl);
    storageRef
      .delete()
      .then(() => {
        console.log('Old image deleted from storage.');
      })
      .catch((error) => {
        console.warn('Failed to delete old image:', error);
      });
  }

  addMaintenanceRequest() {
    this.afAuth.authState.subscribe((user) => {
      if (!user) return;

      const ownerId =
        this.userService.userRole === 'owner'
          ? this.userService.currentUser.id
          : this.userService.currentUser.adminId;
      this.form.controls['ownerId'].setValue(ownerId);

      this.afs
        .collection('users')
        .doc(user.uid)
        .valueChanges()
        .subscribe((res: any) => {
          this.form.controls['createdBy'].setValue(res.displayName);

          const requestData = {
            ...this.form.value,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const docId = this.afs.createId();

          this.afs
            .collection('maintenanceRequests')
            .doc(docId)
            .set(requestData)
            .then(() => {
              this.messageService.add({
                severity: 'success',
                summary: 'Request Added',
                detail: 'Maintenance request has been added',
              });
              this.fetchMaintenanceRequests();
            })
            .catch((error) => {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to add maintenance request',
              });
              console.error(error);
            });
        });
    });
  }

  updateRequest(updatedRequest: any) {
    updatedRequest.updatedAt = new Date();

    if (!updatedRequest.id) {
      console.error('Missing document ID for update.');
      return;
    }

    this.afs
      .collection('maintenanceRequests')
      .doc(updatedRequest.id)
      .update(updatedRequest)
      .then(() => {
        this.messageService.add({
          severity: 'success',
          summary: 'Request Updated',
          detail: 'Maintenance request has been updated',
        });
        this.fetchMaintenanceRequests();
      })
      .catch((error) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update maintenance request',
        });
        console.error(error);
      });
  }

  displayReqDetails: boolean = false;
  selectedRequestDetails: any;
  setSelectedRequest(request: any) {
    this.displayReqDetails = true;
    this.selectedRequestDetails = request;
  }
}
