.main-container {
  width: 100%;
  height: calc(100vh - 128px);
  display: flex;
  overflow-x: auto;

  /* Scrollbar customization */
  scrollbar-height: 2px;

  &::-webkit-scrollbar {
    height: 2px !important;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #5900ff;
    border-radius: 1px;
  }
  /* Firefox */
  scrollbar-color: #5900ff transparent;
  scrollbar-width: thin;

  .maintenance-requests-status {
    width: 400px;
    min-width: 400px;
    height: calc(100vh - 150px);
    margin: 0 7px;

    .requests-list {
      height: 100%;
      overflow-y: auto;
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}

.add-request-btn {
  background: #5900ff !important;
  width: 54px;
  height: 54px;
  border-radius: 50% !important;
  color: #fff;
  bottom: 42px;
  right: 24px;
  position: fixed;
  border-color: transparent;
}

.status-tag {
  width: 54px;
  height: 8px;
  border-radius: 20px;
  &:hover {
    opacity: 0.7;
  }
}
.tag-low {
  background: greenyellow;
}
.tag-normal {
  background: orange;
}
.tag-high {
  background: red !important;
}

.bg-pending {
  background: #ffe0b2;
}
.bg-approved {
  background: #c8e6c9;
}
.bg-assigned {
  background: #bbdefb;
}
.bg-in_progress {
  background: #fff9c4;
}
.bg-completed {
  background: #b2dfdb;
}
.bg-rejected {
  background: #ffcdd2;
}

.request-item {
  border-radius: 8px !important;
  overflow: hidden;
}

:host ::ng-deep {
  .edit-btn {
    display: none;
    top: 3px;
    right: 3px;
    position: absolute;
    padding: 0 !important;
    width: 22px !important;
    height: 22px;
  }

  .request-item:hover {
    background-color: #f4f6f8;
    border: 3px solid #5900ff !important;
    .edit-btn {
      display: block !important;
    }
  }
}

.req-img {
  width: 100%;
  height: 96px;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
