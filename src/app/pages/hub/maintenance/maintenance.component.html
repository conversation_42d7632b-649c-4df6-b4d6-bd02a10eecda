<div class="main-container">
  <div class="card maintenance-requests-status" *ngFor="let status of statuses">
    <div class="card-header" [ngClass]="'bg-' + status">
      <div class="d-flex justify-content-between" style="align-items: center">
        <h5>{{ statusLabels[status] }}</h5>
      </div>
    </div>
    <div class="requests-list grid">
      <div class="col-12 md:col-2">
        <div class="status-column p-3">
          <div
            *ngFor="let request of getRequestsByStatus(status)"
            class="request-item p-0 border mb-3"
            style="cursor: pointer; position: relative"
          >
            <div (click)="setSelectedRequest(request)">
              <div class="req-img" *ngIf="request?.imageUrl">
                <img [src]="request?.imageUrl" alt="" />
              </div>
              <div class="p-2">
                <div class="d-flex justify-content-between mb-1">
                  <div>
                    <div
                      class="status-tag"
                      [ngClass]="{
                        'tag-low': request.urgency === 'low',
                        'tag-normal': request.urgency === 'normal',
                        'tag-high': request.urgency === 'high'
                      }"
                    ></div>
                    <strong>{{ request.type | titlecase }}</strong>
                  </div>

                  <div class="d-flex">
                    <small class="text-muted me-5">{{
                      request.createdAt?.seconds * 1000 | date : "MMM, dd YYYY"
                    }}</small>
                  </div>
                </div>
                <div class="d-flex">
                  <div class="text-sm text-muted me-3">
                    <i class="bi bi-door-open text-secondary mr-1"></i>
                    {{ request.office }}
                  </div>
                  <div class="text-sm text-muted me-3 mt-1">
                    <i class="pi pi-user mr-1"></i
                    >{{ request.requestedBy || "Reception" }}
                  </div>
                </div>
              </div>
            </div>

            <p-button
              label=""
              icon="bi bi-pencil-square"
              styleClass="edit-btn"
              (onClick)="editRequest(request)"
            ></p-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="maintenance-item-details"></div>
</div>

<button
  pButton
  label=""
  icon="pi pi-plus"
  severity="secondary"
  class="p-button-secondary p-button-outlined add-request-btn"
  (click)="showAddMaintenanceDialog()"
></button>

<p-dialog
  header="{{
    isEditMode ? 'Edit Maintenance Request' : 'Add New Maintenance Request'
  }}"
  [(visible)]="displayAddMaintenance"
  [position]="'bottom'"
  [modal]="true"
  [style]="{ width: '50vw' }"
  [draggable]="true"
  [resizable]="true"
>
  <form [formGroup]="form">
    <div class="p-fluid p-formgrid p-grid">
      <div class="p-field p-col-12">
        <label>Type</label>
        <p-dropdown
          [options]="maintenanceTypes"
          formControlName="type"
          placeholder="Select Type"
        ></p-dropdown>
      </div>

      <div class="p-field p-col-6">
        <label>Sector</label>
        <p-dropdown
          [options]="sectors"
          formControlName="sector"
          placeholder="Select Sector"
        ></p-dropdown>
      </div>

      <div class="p-field p-col-6">
        <label>Office</label>
        <p-dropdown
          [options]="offices"
          formControlName="office"
          placeholder="Select Office"
        ></p-dropdown>
      </div>

      <div class="p-field p-col-12">
        <label>Requested By</label>
        <input
          pInputText
          formControlName="requestedBy"
          placeholder="Requested By (optional)"
        />
      </div>

      <div class="p-field p-col-12">
        <label>Urgency</label>
        <p-dropdown
          [options]="urgencyLevels"
          formControlName="urgency"
          placeholder="Select Urgency"
        ></p-dropdown>
      </div>

      <div class="p-field p-col-12">
        <label>Preferred Time Slot</label>
        <input
          pInputText
          formControlName="preferredTime"
          placeholder="Preferred Time Slot (optional)"
        />
      </div>

      <div class="p-field p-col-12">
        <label>Description</label>
        <textarea
          pInputTextarea
          rows="4"
          formControlName="description"
        ></textarea>
      </div>

      <div class="p-field p-col-12">
        <label>Upload Image (optional)</label>
        <p-fileUpload
          name="image"
          accept="image/*"
          [customUpload]="true"
          (uploadHandler)="onUpload($event)"
          [auto]="true"
          [maxFileSize]="1000000"
          chooseLabel="Choose"
          uploadLabel="Upload"
          cancelLabel="Cancel"
        ></p-fileUpload>
      </div>
    </div>
  </form>
  <ng-template pTemplate="footer">
    <p-button
      icon="pi pi-check"
      type="submit"
      [disabled]="form.invalid || uploading"
      [label]="isEditMode ? 'Save Changes' : 'Create Request'"
      styleClass="p-button-text"
      [loading]="uploading"
      loadingIcon="pi pi-spinner"
      (click)="saveRequest()"
    ></p-button>
  </ng-template>
</p-dialog>

<p-toast></p-toast>

<p-dialog
  header="TTTTTTTTT"
  [(visible)]="displayReqDetails"
  [modal]="true"
  [style]="{ width: '50vw' }"
  [draggable]="true"
  [resizable]="true"
  [closable]="true"
  [dismissableMask]="true"
></p-dialog>
