<p-toolbar>
  <div class="p-toolbar-group-left mb-3">
    <h4 class="ms-3 mt-2">Employees</h4>
  </div>

  <div class="p-toolbar-group-right mb-3">
    <p-button
      icon="pi pi-plus"
      label="New Employee"
      class="p-primary-button"
      (click)="displayAddUser = true"
    ></p-button>
    <p-button
      label="Invitations"
      icon="bi bi-list-ul"
      styleClass="p-button-outlined p-button-info ms-2"
      data-bs-toggle="offcanvas"
      data-bs-target="#rolesCanvas"
      aria-controls="branchesExample"
    ></p-button>
  </div>
</p-toolbar>

<!-- Simple Employee Table -->
<div class="card mt-3">
  <p-table [value]="employees" [paginator]="true" [rows]="10">
    <!-- Update your table header -->
    <ng-template pTemplate="header">
      <tr>
        <th pSortableColumn="data.displayName" style="position: relative">
          <div class="flex align-items-center justify-content-between">
            Name
            <p-sortIcon field="data.displayName"></p-sortIcon>
            <p-columnFilter
              type="text"
              field="data.displayName"
              display="menu"
            ></p-columnFilter>
          </div>
        </th>
        <th pSortableColumn="data.email" style="position: relative">
          <div class="flex align-items-center justify-content-between">
            Email
            <p-sortIcon field="data.email"></p-sortIcon>
            <p-columnFilter
              type="text"
              field="data.email"
              display="menu"
            ></p-columnFilter>
          </div>
        </th>
        <th pSortableColumn="data.role" style="position: relative">
          <div class="flex align-items-center justify-content-between">
            Role
            <p-sortIcon field="data.role"></p-sortIcon>
            <p-columnFilter
              type="text"
              field="data.role"
              display="menu"
            ></p-columnFilter>
          </div>
        </th>
        <th>Assigned Branch</th>
      </tr>
    </ng-template>

    <!-- Update your table body -->
    <!-- Update your table body template -->
    <ng-template pTemplate="body" let-employee>
      <tr>
        <td>{{ employee.data.displayName }}</td>
        <td>{{ employee.data.email }}</td>
        <td>{{ employee.data.role }}</td>

        <td>
          <p-dropdown
            [options]="branches"
            optionLabel="name"
            optionValue="id"
            [(ngModel)]="employee.data.branchId"
            (onChange)="assignToBranch(employee.id, $event.value)"
            [showClear]="true"
            placeholder="Select Branch"
            appendTo="body"
            [style]="{
              'min-width': '200px',
              width: 'calc(100% - 10px)',
              'margin-right': '10px'
            }"
            [ngModelOptions]="{ standalone: true }"
          >
            <ng-template let-branch pTemplate="item">
              <div
                class="d-flex align-items-center branch-item"
                style="padding: 8px 12px"
              >
                <i class="pi pi-map-marker me-2"></i>
                <div style="flex: 1; min-width: 0">
                  <div class="font-medium text-ellipsis">{{ branch.name }}</div>
                  <div class="text-sm text-color-secondary text-ellipsis">
                    {{ branch.address }}
                  </div>
                  <div
                    *ngIf="
                      isBranchAssigned(branch.id) &&
                      employee.data.branchId !== branch.id
                    "
                    class="text-xs text-warning mt-1 text-ellipsis"
                  >
                    <i class="pi pi-exclamation-triangle mr-1"></i>
                    Already assigned
                  </div>
                </div>
              </div>
            </ng-template>

            <ng-template pTemplate="selectedItem" let-branch>
              <div
                class="d-flex align-items-center"
                *ngIf="branch"
                style="padding-right: 10px"
              >
                <i class="pi pi-map-marker me-2"></i>
                <div class="text-ellipsis">{{ branch.name }}</div>
              </div>
              <span *ngIf="!branch">Not assigned</span>
            </ng-template>
          </p-dropdown>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="4" class="text-center py-5">
          <div class="flex flex-column align-items-center">
            <i class="pi pi-users text-4xl text-400 mb-3"></i>
            <h5>No employees found</h5>
            <p class="text-500">Add new employees to get started</p>
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>
<div
  class="offcanvas offcanvas-end"
  style="width: 100%; max-width: 1100px !important"
  tabindex="-1"
  id="rolesCanvas"
  aria-labelledby="rolesCanvasLabel"
>
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="rolesCanvasLabel">Invitations</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="offcanvas"
      aria-label="Close"
      #closeInvitationCanvas
    ></button>
  </div>
  <div class="offcanvas-body">
    <p-table
      #dt
      [value]="invitations"
      [globalFilterFields]="['status', 'email', 'role']"
      [tableStyle]="{ 'min-width': '60rem' }"
      [paginator]="true"
      [rows]="10"
    >
      <ng-template pTemplate="caption">
        <div class="d-flex justify-content-between table-header">
          List of Customers
          <span class="p-input-icon-left">
            <i class="pi pi-search"></i>
            <input
              pInputText
              type="text"
              (input)="applyGlobalFilter($event)"
              placeholder="Global Search"
            />
          </span>
        </div>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="email" style="position: relative">
            <div class="flex align-items-center justify-content-between">
              Email
              <p-sortIcon field="email"></p-sortIcon>
            </div>
          </th>
          <th pSortableColumn="role" style="position: relative">
            <div class="flex align-items-center justify-content-between">
              Role
              <p-sortIcon field="role"></p-sortIcon>
            </div>
          </th>
          <th pSortableColumn="status" style="position: relative">
            <div class="flex align-items-center justify-content-between">
              Inv. Status
              <p-sortIcon field="status"></p-sortIcon>
            </div>
          </th>
          <th>Sent at</th>
          <th>Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-invite>
        <tr>
          <td>{{ invite.email }}</td>
          <td>{{ invite.role }}</td>
          <td>
            <p-badge
              [value]="invite.status"
              *ngIf="invite.status === 'accepted'"
              severity="success"
              styleClass="mr-2"
            ></p-badge>
            <p-badge
              [value]="invite.status"
              *ngIf="invite.status === 'pending'"
              severity="warning"
              styleClass="mr-2"
            ></p-badge>
            <p-badge
              [value]="invite.status"
              *ngIf="invite.status === 'cancelled'"
              severity="danger"
            ></p-badge>
          </td>
          <td>{{ invite.created_at.toDate() | date : "MMM, dd YYYY" }}</td>
          <td>
            <div class="d-flex" *ngIf="invite.status !== 'accepted'">
              <p-button
                icon="bi bi-pencil-square"
                styleClass="p-button-info"
                (click)="editInvitation(invite)"
              ></p-button>
              <p-button
                icon="pi pi-trash"
                (click)="confirmDelete($event, invite)"
                styleClass="mx-2 p-button-rounded p-button-danger p-button-text p-button-raised"
              ></p-button>
              <p-button
                [style]="{ minWidth: '180px' }"
                label="Resend Email"
                icon="pi pi-envelope"
                (click)="resendEmail(invite)"
              ></p-button>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-confirmPopup></p-confirmPopup>

<p-dialog
  [header]="enableEditInvitation ? 'Edit Invitation' : 'Add New Invitation'"
  [(visible)]="displayAddUser"
  [position]="'top'"
  [style]="{ width: '50vw', minWidth: '800px' }"
>
  <form [formGroup]="inviteForm">
    <div
      class="field"
      style="display: flex; flex-direction: column"
      *ngIf="!addSuccess"
    >
      <label for="email" class="block">Email Address</label>
      <input
        id="email"
        type="text"
        aria-describedby="email-help"
        pInputText
        formControlName="email"
        placeholder="<EMAIL>"
      />
      <small id="email-help" class="block">Please Enter a correct email.</small>
    </div>
    <div class="field">
      <p-selectButton
        [options]="rolesOptions"
        formControlName="role"
        optionLabel="label"
        optionValue="value"
      >
        <ng-template let-item>
          <span>{{ item.label }}</span>
        </ng-template>
      </p-selectButton>
    </div>
  </form>

  <ng-template pTemplate="footer">
    <p-button
      *ngIf="!enableEditInvitation"
      icon="pi pi-check"
      (click)="inviteUser()"
      label="Invite User"
      styleClass="p-button-text"
      [disabled]="!inviteForm.valid"
      [loading]="inviteBtnLoading"
    ></p-button>
    <p-button
      *ngIf="enableEditInvitation"
      icon="pi pi-check"
      (click)="editInvitedUser()"
      label="Save Invitation Changes"
      styleClass="p-button-text"
      [disabled]="!inviteForm.valid"
      [loading]="inviteBtnLoading"
    ></p-button>
  </ng-template>
</p-dialog>

<p-toast></p-toast>
