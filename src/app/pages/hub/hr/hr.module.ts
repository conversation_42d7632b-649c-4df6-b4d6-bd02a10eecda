import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HrRoutingModule } from './hr-routing.module';
import { HrComponent } from './hr.component';
import { PrimengModule } from 'src/app/shared/modules/primeng.module';
import { CodeInputModule } from 'angular-code-input';


@NgModule({
  declarations: [
    HrComponent
  ],
  imports: [
    CommonModule,
    HrRoutingModule,
    PrimengModule,
    CodeInputModule.forRoot({
      codeLength: 6,
      isCharsCode: true,
      inputType: 'number',
      isCodeHidden: false
    }),
  ]
})
export class HrModule { }
