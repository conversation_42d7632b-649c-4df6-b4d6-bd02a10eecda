import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  registerForm: FormGroup;

  constructor(
    private afAuth: AngularFireAuth,
    private afs: AngularFirestore,
    private router: Router
    ) {
    this.loginForm = new FormGroup({
      email: new FormControl('', Validators.required),
      password: new FormControl('', [
        Validators.required,
        Validators.minLength(6),
      ]),
    });

    this.registerForm = new FormGroup({
      email: new FormControl('', Validators.required),
      password: new FormControl('', [
        Validators.required,
        Validators.minLength(6),
      ]),
    });
  }

  ngOnInit() {}

  register() {
    this.afAuth
      .createUserWithEmailAndPassword(
        this.registerForm.value.email,
        this.registerForm.value.password
      )
      .then((userCredential) => {
        // User registered successfully
        console.log('Registration successful', userCredential);

        const user = userCredential.user;
      if (user) {
        const userData = {
          email: this.registerForm.value.email,
          displayName: this.registerForm.value.displayName || '', // Assuming you have a displayName field in the form
          createdAt: new Date(),
          role: 'owner'
          // Add other user data here
        };

        this.afs
          .collection('users')
          .doc(user.uid)
          .set(userData)
          .then(() => {
            console.log('User data saved to Firestore');
          })
          .catch((error) => {
            console.error('Error saving user data to Firestore', error);
          });
      }
      })
      .catch((error) => {
        console.error('Registration error', error);
      });
  }

  login() {
    this.afAuth.signInWithEmailAndPassword(
        this.loginForm.value.email,
        this.loginForm.value.password
      )
      .then((userCredential) => {
        // User registered successfully
        this.router.navigateByUrl('/hub')
        console.log('Registration successful', userCredential);
      })
      .catch((error) => {
        console.error('Registration error', error);
      });
  }
}
