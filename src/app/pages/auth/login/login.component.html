<div class="s-container">
    
    <p-card class="col-sm-12 col-md-8 col-lg-6">
        <div class="d-flex col-12 md:flex-row">
            <form [formGroup]="loginForm" (ngSubmit)="login()" class="col">
                <h5>Login</h5>
                <div class="form-field mb-3">
                    <label class="w-6rem">Username/Email</label>
                    <input pInputText id="usernamel" type="email" formControlName="email" class="w-12rem" />
                </div>
                <div class="form-field mb-3">
                    <label class="w-6rem">Password</label>
                    <input pInputText id="passwordl" type="password" formControlName="password" class="w-12rem" />
                </div>
                <div class="col-12 text-end">
                    <p-button label="Login" type="submit" icon="pi pi-user" class="col" styleClass="mx-auto"></p-button>
                </div>
            </form>
            <div class="w-full md:w-2">
                <p-divider layout="vertical" styleClass="hidden md:flex"><b>OR</b></p-divider>
                <!--<p-divider layout="horizontal" styleClass="flex md:hidden" [align]="'center'"><b>OR</b></p-divider>-->
            </div>
            <form [formGroup]="registerForm" (ngSubmit)="register()" class="col">
                <h5>Register</h5>
                <div class="form-field mb-3">
                    <label class="w-6rem">Username/Email</label>
                    <input pInputText id="usernamer" type="email" formControlName="email" class="w-12rem" />
                </div>
                <div class="form-field mb-3">
                    <label class="w-6rem">Password</label>
                    <input pInputText id="passwordr" type="password" formControlName="password" class="w-12rem" />
                </div>
                <div class="col-12 text-center mt-auto">
                    <p-button label="Request a Demo" type="submit" icon="pi pi-phone" styleClass="p-button-success"></p-button>
                </div>
            </form>
        </div>
    </p-card>

    

</div>