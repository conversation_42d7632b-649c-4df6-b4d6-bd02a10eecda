<div class="s-container">
    
    <p-card class="col-sm-12 col-md-8 col-lg-6" *ngIf="validToken">
        <div class="d-flex col-12 md:flex-row">
            <form [formGroup]="registerForm" (ngSubmit)="register()" class="col">
                <h5>Register</h5>
                <div class="form-field mb-3">
                    <label class="w-6rem" for="displayName">Full Name</label>
                    <input pInputText id="displayName" type="text" formControlName="displayName" class="w-12rem" />
                </div>
                <div class="form-field mb-3">
                    <label class="w-6rem" for="email">Email</label>
                    <input pInputText id="email" type="email" readonly formControlName="email" class="w-12rem" />
                </div>
                <div class="form-field mb-3">
                    <label class="w-6rem" for="password">Password</label>
                    <input pInputText id="password" type="password" formControlName="password" class="w-12rem" />
                </div>
                <div class="form-field mb-3">
                    <label class="w-6rem" for="cpassword">Confrim Password</label>
                    <input pInputText id="cpassword" type="password" formControlName="cpassword" class="w-12rem" />
                </div>
                <div class="col-12 text-center mt-auto">
                    <p-button label="Create my Account" [disabled]="!registerForm.valid || registerForm.value.password !== registerForm.value.cpassword" type="submit" icon="pi pi-phone" styleClass="p-button-success"></p-button>
                </div>
            </form>
        </div>
    </p-card>

    

</div>