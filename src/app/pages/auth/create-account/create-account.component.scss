.container {
  padding: 5rem 2rem; // More vertical padding, less horizontal
  max-width: 1200px; // Limits width on large screens
  margin: 0 auto;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 10rem); // Accounts for padding
}:host {
  display: block;
  min-height: 100vh;
  background-color: #f8f9fa; // Full screen background
}


.p-card {
  width: 100%;
  max-width: 600px;
  margin: 2rem auto; // More vertical spacing
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  background: white; // Ensures card stands out
}

.card-header {
  font-size: 1.5rem;
  font-weight: 600;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 1.5rem;
}

.field {
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
  }

  input {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.3s;

    &:focus {
      border-color: #6366f1;
      box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
      outline: none;
    }

    &[disabled] {
      background-color: #e9ecef;
      opacity: 1;
    }
  }
}

.p-button {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s;

  &:enabled:hover {
    transform: translateY(-1px);
  }
}

.p-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}
