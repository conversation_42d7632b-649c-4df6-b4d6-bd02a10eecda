import { Component } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-create-account',
  templateUrl: './create-account.component.html',
  styleUrls: ['./create-account.component.scss'],
  providers: [MessageService]
})
export class CreateAccountComponent {
  registerForm: FormGroup;
  validToken: boolean = false;
  loading: boolean = false;
  invitation: any;
  token: string = '';

  constructor(
    private afAuth: AngularFireAuth,
    private afs: AngularFirestore,
    private router: Router,
    private route: ActivatedRoute,
    private messageService: MessageService
  ) {
    this.registerForm = new FormGroup({
      email: new FormControl({value: '', disabled: false}, [Validators.required, Validators.email]),
      displayName: new FormControl('', Validators.required),
      password: new FormControl('', [
        Validators.required,
        Validators.minLength(6),
      ]),
      confirmPassword: new FormControl('', [
        Validators.required,
        Validators.minLength(6),
      ]),
    });
  }

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.token = params['token'];
      if (this.token) {
        this.validateToken();
      } else {
        this.router.navigate(['/login']);
      }
    });
  }

  validateToken() {
    this.afs.collection('invites', ref =>
      ref.where('token', '==', this.token)
         .where('status', '==', 'pending')
         .limit(1)
    ).get().subscribe(snapshot => {
      if (!snapshot.empty) {
        this.invitation = snapshot.docs[0].data();
        this.registerForm.patchValue({
          email: this.invitation.email
        });
        this.registerForm.get('email')?.disable();
        this.validToken = true;
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Invalid Token',
          detail: 'The invitation link is invalid or has expired.'
        });
        this.router.navigate(['/login']);
      }
    });
  }

  async register() {
    if (this.registerForm.invalid) return;
    if (this.registerForm.value.password !== this.registerForm.value.confirmPassword) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Passwords do not match'
      });
      return;
    }

    this.loading = true;

    try {
      // Get the email value properly (even if the field is disabled)
      const email = this.invitation.email;
      const password = this.registerForm.value.password;
      const displayName = this.registerForm.value.displayName;

      // Create user account
      const userCredential = await this.afAuth.createUserWithEmailAndPassword(email, password);
      const user = userCredential.user;

      if (!user) {
        throw new Error('User creation failed');
      }

            // Save user data to Firestore
      const userData = {
        id: user.uid,
        email: email,
        displayName: displayName,
        role: this.invitation.role,
        adminId: this.invitation.adminId || "",
        ...(this.invitation.role !== 'manager' && {
          managerId: this.invitation.managerId || this.invitation.adminId
        }),
        createdAt: new Date(),
        status: 'active'
      };

      await this.afs.collection('users').doc(user.uid).set(userData);

      // Update invitation status
      await this.afs.collection('invites').doc(this.invitation.id).update({
        status: 'accepted',
        acceptedAt: new Date()
      });

      // Sign in the new user
      await this.afAuth.signInWithEmailAndPassword(email, password);

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Account created successfully!'
      });

      this.router.navigate(['/hub']);
    } catch (error: any) {
      console.error('Registration error:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Registration Failed',
        detail: this.getErrorMessage(error)
      });
    } finally {
      this.loading = false;
    }
  }

  private getErrorMessage(error: any): string {
    if (error.code) {
      switch (error.code) {
        case 'auth/email-already-in-use':
          return 'This email is already registered.';
        case 'auth/invalid-email':
          return 'The email address is invalid.';
        case 'auth/weak-password':
          return 'The password is too weak.';
        default:
          return error.message || 'An unknown error occurred.';
      }
    }
    return error.message || 'An unknown error occurred.';
  }
}
