<!-- create-account.component.html -->
<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <p-card *ngIf="validToken" header="Create Your Account">
        <form [formGroup]="registerForm" (ngSubmit)="register()">
          <div class="p-fluid">
            <div class="field mb-3">
              <label for="email">Email</label>
              <input
                pInputText
                id="email"
                type="email"
                formControlName="email"
                [disabled]="true"
              />
            </div>

            <div class="field mb-3">
              <label for="displayName">Full Name</label>
              <input
                pInputText
                id="displayName"
                type="text"
                formControlName="displayName"
                [ngClass]="{'p-invalid': registerForm.get('displayName')?.invalid && registerForm.get('displayName')?.touched}"
              />
              <small *ngIf="registerForm.get('displayName')?.invalid && registerForm.get('displayName')?.touched" class="p-error">
                Full name is required
              </small>
            </div>

            <div class="field mb-3">
              <label for="password">Password</label>
              <input
                pInputText
                id="password"
                type="password"
                formControlName="password"
                [ngClass]="{'p-invalid': registerForm.get('password')?.invalid && registerForm.get('password')?.touched}"
              />
              <small *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched" class="p-error">
                Password must be at least 6 characters
              </small>
            </div>

            <div class="field mb-3">
              <label for="confirmPassword">Confirm Password</label>
              <input
                pInputText
                id="confirmPassword"
                type="password"
                formControlName="confirmPassword"
                [ngClass]="{'p-invalid': (registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) || (registerForm.value.password !== registerForm.value.confirmPassword && registerForm.get('confirmPassword')?.touched)}"
              />
              <small *ngIf="registerForm.value.password !== registerForm.value.confirmPassword && registerForm.get('confirmPassword')?.touched" class="p-error">
                Passwords do not match
              </small>
            </div>

            <div class="field mb-3">
              <p-button
                type="submit"
                label="Create Account"
                [disabled]="registerForm.invalid || registerForm.value.password !== registerForm.value.confirmPassword"
                [loading]="loading"
              ></p-button>
            </div>
          </div>
        </form>
      </p-card>

      <p-card *ngIf="!validToken" header="Invalid Invitation">
        <p>The invitation link is invalid or has expired. Please contact your administrator for a new invitation.</p>
        <p-button label="Go to Login" routerLink="/login"></p-button>
      </p-card>
    </div>
  </div>
</div>
