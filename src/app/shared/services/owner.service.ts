import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { BehaviorSubject, Observable, of, switchMap, take } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class OwnerService {
  private _userId = new BehaviorSubject<string | null>(null); // Cached ID (observable)
  userId$ = this._userId.asObservable(); // Expose as observable

  constructor(private afAuth: AngularFireAuth, private afs: AngularFirestore) {}

  // Call this ONCE in AppComponent to initialize the ID
  initializeUserId(): void {
    this.afAuth.authState
      .pipe(
        take(1) // Ensure it runs only once
      )
      .subscribe((user) => {
        if (!user?.uid) {
          this._userId.next(null);
          return;
        }
        this.afs
          .doc(`users/${user.uid}`)
          .valueChanges()
          .pipe(take(1))
          .subscribe((userData: any) => {
            const id =
              userData?.role === 'owner' ? userData.id : userData.adminId;
            console.log('mmmmmmmmmm', this._userId.value);
            this._userId.next(id); // Store the ID
          });
      });
  }

  // Get the cached ID (sync access)
  get userId(): string | null {
    return this._userId.value;
  }
}
