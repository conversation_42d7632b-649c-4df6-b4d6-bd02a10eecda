import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  currentUser: any;
  userRole: string = ''; //<PERSON><PERSON><PERSON><PERSON>, owner, manager, assistant, client, technician, teaBoy, cleaner,

  constructor(private afAuth: AngularFireAuth, private afs: AngularFirestore) {}

  async getCurrentUser(): Promise<any> {
    const user = await this.afAuth.currentUser;
    if (user) {
      const userDoc = await this.afs
        .collection('users')
        .doc(user.uid)
        .get()
        .toPromise();
      const userData: any = userDoc?.data();
      if (userData) {
        userData.uid = user.uid; // Ensure uid is always present
      }
      this.currentUser = userData;
      this.userRole = userData && userData.role ? userData.role : '';
      return userData;
    } else {
      return null;
    }
  }
}
