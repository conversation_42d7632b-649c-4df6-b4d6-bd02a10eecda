import { Injectable } from '@angular/core';
import { init, send } from '@emailjs/browser';

@Injectable({ providedIn: 'root' })
export class EmailService {
  private readonly serviceId = 'service_majb3p7';
  private readonly templateId = 'template_wx3axu7';
  private readonly publicKey = 'qslLKqpInA466iUsj';

  constructor() {
    try {
      init(this.publicKey);
      console.log('EmailJS initialized successfully');
    } catch (e) {
      console.error('EmailJS initialization failed:', e);
    }
  }

  async sendInvitationEmail(email: string, link: string, role: string): Promise<boolean> {
    try {
      const response = await send(
        this.serviceId,
        this.templateId,
        {
          to_email: email,
          registration_link: link,
          role: role,
          subject: 'Hub Invitation',
          reply_to: '<EMAIL>'
        }
      );

      console.log('Email sent successfully:', response);
      return true;
    } catch (error) {
      console.error('Email sending failed:', error);
      return false;
    }
  }
}
