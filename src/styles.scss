/* You can add global styles to this file, and also import other style files */
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");

@import url(theme/form.css);
@import url(theme/primeng.css);
@import url(theme/colors.css);
@import url(theme/utils.css);
@import url(theme/bootstrap-components.css);

html,
body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Roboto", sans-serif;
}

.p-overlaypanel {
  z-index: 1050 !important;
  .p-accordion-content {
    padding: 0 !important;
    .p-image {
      width: 44px;
      height: 44px;
      border-radius: 6px;
      overflow: hidden;
      margin-right: 14px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .p-accordion-header a {
    color: #383838;
    text-decoration: none;
  }
}


.pac-container {
  z-index: 10000000000 !important;
  border: 5px solid red;
}
.violet-text {
  color: #7C72FF;
}

.p-sidebar-header {
  text-align: right;
  justify-content: right;
  top: 14px;
  right: 14px;
  position: absolute;
  z-index: 100000;
}

.p-sidebar-content {
  padding-top: 48px;
}

.flex-middle {
  display: flex;
  vertical-align: middle;
  align-items: center;
}

.active-link {
  color: red !important;
}

.p-inputnumber {
  width: 100% !important;
}