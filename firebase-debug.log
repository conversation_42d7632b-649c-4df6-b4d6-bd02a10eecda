[debug] [2025-05-17T23:46:30.978Z] ----------------------------------------------------------------------
[debug] [2025-05-17T23:46:30.980Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init
[debug] [2025-05-17T23:46:30.980Z] CLI Version:   14.4.0
[debug] [2025-05-17T23:46:30.980Z] Platform:      win32
[debug] [2025-05-17T23:46:30.980Z] Node Version:  v20.16.0
[debug] [2025-05-17T23:46:30.980Z] Time:          Sun May 18 2025 00:46:30 GMT+0100 (heure normale d’Europe centrale)
[debug] [2025-05-17T23:46:30.980Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-17T23:46:30.984Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-17T23:46:30.985Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\Desktop\HubCenter

[debug] [2025-05-17T23:47:13.426Z] ExitPromptError: User force closed the prompt with SIGINT
    at Interface.sigint (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\node_modules\@inquirer\core\dist\commonjs\lib\create-prompt.js:100:37)
    at Interface.emit (node:events:519:28)
    at Interface.emit (node:domain:488:12)
    at [_ttyWrite] [as _ttyWrite] (node:internal/readline/interface:1126:18)
    at ReadStream.onkeypress (node:internal/readline/interface:265:20)
    at ReadStream.emit (node:events:531:35)
    at ReadStream.emit (node:domain:488:12)
    at emitKeys (node:internal/readline/utils:371:14)
    at emitKeys.next (<anonymous>)
    at ReadStream.onData (node:internal/readline/emitKeypressEvents:64:36)
[error] 
[error] Error: An unexpected error has occurred.
