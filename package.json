{"name": "hub", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.12", "@angular/fire": "^7.6.1", "@angular/forms": "^16.2.0", "@angular/google-maps": "^16.2.14", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@emailjs/browser": "^3.11.0", "@matterport/webcomponent": "^0.1.39", "@sendgrid/mail": "^8.1.4", "@types/file-saver": "^2.0.7", "angular-code-input": "^2.0.0", "bootstrap-icons": "^1.11.2", "echarts": "^5.4.3", "file-saver": "^2.0.5", "firebase": "^9.22.2", "primeicons": "^6.0.1", "primeng": "^16.4.2", "rxjs": "~7.8.0", "three": "^0.151.3", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.10", "@angular/cli": "^16.2.10", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}