"use strict";(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[562],{562:(e,s,t)=>{t.r(s),t.d(s,{AcceptMessage:()=>x,AcceptObserver:()=>O,ConnectMessage:()=>b,ConnectMessageObserver:()=>V,ConnectionRefusedError:()=>I,ErrorType:()=>w,ErrorTypeMap:()=>M,HandshakeMessage:()=>L,HandshakeObserver:()=>C,IncomingMessageType:()=>y,InvalidProviderError:()=>T,KeyReferrerMismatchError:()=>N,MP_SDK:()=>m,OutgoingMessageType:()=>f,RejectMessage:()=>B,RejectObserver:()=>R,connect:()=>j,version:()=>k});class n{constructor(e){this.messageReceiver=e}notify(e,s){this.messageReceiver.onMessageReceived(e,s)}}class r{constructor(){this.values={}}add(e,s){this.getValuesAtKey(e).push(s)}remove(e,s){const t=this.values[e];if(t){const e=t.indexOf(s);e>-1&&t.splice(e,1)}}removeKey(e){delete this.values[e]}getValuesAtKey(e){const s=this.values[e]||[];return this.values[e]=s,s}valuesPerKey(e){return this.getValuesAtKey(e).length}find(e,s){return this.values[e]&&this.values[e].find(s)}get keys(){return Object.keys(this.values)}hasKey(e){return e in this.values}has(e,s){return this.hasKey(e)&&this.values[e].includes(s)}*[Symbol.iterator](){for(const e in this.values)for(const s of this.values[e])yield[e,s]}}Symbol.iterator;class i{listen(){}stopListening(){}send(){throw Error("The sdk has been disconnected and can't make any new calls")}}class o{constructor(e,s,t){this.sourceId=e,this.targetId=s,this.messageBridge=t,this.observers=new r}static toFilteredMessenger(e,s,t){const n=new c(e.sourceId,e.targetId,e,s);for(const s of t)for(const t of e.observers.getValuesAtKey(s))n.addObserver(t);return n}init(){const e=new class{constructor(e){this.messenger=e}onMessageReceived(e,s){this.messenger.onMessageReceived(e,s)}}(this),s=new n(e);this.messageBridge.listen(s)}dispose(){this.messageBridge.stopListening(),this.messageBridge=new i}addObserver(e){this.observers.add(e.messageType,e)}removeObserver(e){this.observers.remove(e.messageType,e)}send(e){const s=this.sourceId,t=this.targetId;this.messageBridge.send(Object.assign(Object.assign({},e),{fromId:s,toId:t,timestamp:Date.now()}))}onMessageReceived(e,s){if(!this.filterMessageId(e))return;const t=e.type,n=this.observers.getValuesAtKey(t);if(n)for(const t of n){const n=e.payload||e;t.notify(n,s,e.timestamp)}}filterMessageId(e){const s=e.toId,t=e.fromId;return s instanceof Array?s.indexOf(this.sourceId)>-1:(void 0===s||s===this.sourceId)&&t===this.targetId}}class c extends o{constructor(e,s,t,n){super(e,s,new i),this.messenger=t,this.sendFilter=n,this.errorBridge=new i}send(e){this.sendFilter.includes(e.type)?this.messenger.send(e):this.errorBridge.send()}}class a{constructor(e,s){this.sourceId=e,this.listenerBridge=s,this.observers=new r}init(){const e=new class{constructor(e){this.messenger=e}onMessageReceived(e,s){this.messenger.onMessageReceived(e,s)}}(this),s=new n(e);this.listenerBridge.listen(s)}dispose(){this.listenerBridge.stopListening()}addObserver(e){this.observers.add(e.messageType,e)}removeObserver(e){this.observers.remove(e.messageType,e)}send(e,s,t,n){t.send(Object.assign(Object.assign({},e),{fromId:this.sourceId,toId:s,timestamp:Date.now()}),n)}onMessageReceived(e,s){const t=e.type,n=this.observers.getValuesAtKey(t);if(n)for(const t of n){const n=e.payload||e;t.notify(n,s,e.timestamp)}}}class h{constructor(e){this.targetWindow=e,this.messageObserver=null,this.onMessage=e=>{if(this.messageObserver){const s=e.data;this.messageObserver.notify(Object.assign({},s),{id:s.fromId,origin:e.origin,source:e.source},e.data.timestamp)}}}listen(e){this.messageObserver||(this.messageObserver=e,this.targetWindow.addEventListener("message",this.onMessage))}stopListening(){this.targetWindow.removeEventListener("message",this.onMessage),this.messageObserver=null}}class d{constructor(e,s="*"){this.targetWindow=e,this.targetOrigin=s}send(e,s){this.targetWindow.postMessage(e,this.targetOrigin)}}class l{constructor(e,s,t="*"){this.sourceWindow=e,this.listener=new h(e),this.sender=new d(s,t)}listen(e){this.listener.listen(e)}stopListening(){this.listener.stopListening()}send(e){this.sender.send(e,this.sourceWindow)}}class u{constructor(e,s,t,n,r="*"){this.sourceId=e,this.sourceWindow=s,this.targetId=t,this.targetWindow=n,this.targetOrigin=r}createMessenger(){const e=new l(this.sourceWindow,this.targetWindow,this.targetOrigin);return new o(this.sourceId,this.targetId,e)}}var g=function(e,s,t,n){return new(t||(t=Promise))((function(r,i){function o(e){try{a(n.next(e))}catch(e){i(e)}}function c(e){try{a(n.throw(e))}catch(e){i(e)}}function a(e){var s;e.done?r(e.value):(s=e.value,s instanceof t?s:new t((function(e){e(s)}))).then(o,c)}a((n=n.apply(e,s||[])).next())}))};class v{constructor(){this.libraryCache=new Map}load(e,s){const t=this.libraryCache.get(e)||new p(e,s);return this.libraryCache.set(e,t),t.library}}class p{constructor(e,s){this._libraryPromise=new Promise(((e,s)=>{this.libraryResolver=e,this.libraryRejecter=s})),this.fetch(e,s)}fetch(e,s){return g(this,void 0,void 0,(function*(){try{const t=yield import(e),n=Object.assign({},t);if(s&&s in window){const e=window[s]||{};Object.assign(n,e),delete window[s]}this.libraryResolver(n)}catch(e){this.libraryRejecter(e)}}))}get library(){return this._libraryPromise}}var m,f,y,w,E=function(e,s,t,n){return new(t||(t=Promise))((function(r,i){function o(e){try{a(n.next(e))}catch(e){i(e)}}function c(e){try{a(n.throw(e))}catch(e){i(e)}}function a(e){var s;e.done?r(e.value):(s=e.value,s instanceof t?s:new t((function(e){e(s)}))).then(o,c)}a((n=n.apply(e,s||[])).next())}))};!function(e){const s=new v;e.connect=function(e,t,n){return E(this,void 0,void 0,(function*(){let r;try{r=yield e.connect()}finally{e.cancelConnecting()}const i=yield function(e){return E(this,void 0,void 0,(function*(){if(!e)throw new Error("Unabled to load the sdk");try{const t=yield s.load(e,"sdk-client");if(t&&t.SdkBuilder&&"function"==typeof t.SdkBuilder)return t.SdkBuilder}catch(e){}throw Error(`Could not load the sdk from ${e}`)}))}(r.scriptUrl),o=t.getFactory(r);return function(e,s,t,n){return new s(t,e).build(n)}(n,i,o,r.serializedInterface)}))}}(m||(m={})),function(e){e.CONNECT="postmessage.connect"}(f||(f={})),function(e){e.HANDSHAKE="postmessage.handshake",e.ACCEPT="postmessage.accept",e.REJECT="postmessage.reject"}(y||(y={}));class b{constructor(e,s={}){this.type=f.CONNECT,this.payload={bootstrapVersion:e,options:{auth:s.auth,provider:s.provider,sdkType:s.sdkType}}}}class C{constructor(e){this.receiver=e,this.messageType=y.HANDSHAKE}notify(e,s,t){this.receiver.handshake()}}class O{constructor(e){this.receiver=e,this.messageType=y.ACCEPT}notify(e,s,t){const{sourceId:n,scriptUrl:r,targetId:i,targetOrigin:o}=e,c=e.interface;this.receiver.accept(n,r,c,i,o)}}!function(e){e.CANCELLED="ConnectionCancelled",e.REFUSED="ConnectionRefused",e.INVALID_PROVIDER="InvalidProvider",e.KEY_MISMATCH="KeyReferrerMismatch"}(w||(w={}));class I extends Error{constructor(){super(),this.type=w.REFUSED,this.name="ConnectionRefusedError"}}class T extends Error{constructor(e){super(e),this.type=w.INVALID_PROVIDER,this.name="InvalidProviderError"}}class N extends Error{constructor(e){super(e),this.type=w.KEY_MISMATCH,this.name="KeyReferrerMismatchError"}}const M={[w.REFUSED]:I,[w.KEY_MISMATCH]:N,[w.INVALID_PROVIDER]:T};class R{constructor(e){this.receiver=e,this.messageType=y.REJECT}notify(e,s,t){const n=M[e.errorType];if(n){const s=new n(e.reason);this.receiver.reject(s)}}}class A extends Error{constructor(e){super(e),this.type=w.CANCELLED,this.name="ConnectionCancelledError"}}var D;!function(e){e[e.IDLE=0]="IDLE",e[e.CONNECTING=1]="CONNECTING",e[e.HANDSHAKE=2]="HANDSHAKE",e[e.CONNECTED=3]="CONNECTED",e[e.REJECTED=4]="REJECTED"}(D||(D={}));class P{constructor(e,s,t){this.messenger=e,this.target=s,this.source=t,this.connectionState=D.IDLE,this.connectionPoll=void 0;const n=new class{constructor(e){this.connector=e}handshake(){this.connector.handshake()}accept(e,s,t,n,r){this.connector.accept(e,s,t,n,r)}reject(e){this.connector.reject(e)}}(this);this.handshakeObserver=new C(n),this.acceptObserver=new O(n),this.rejectObserver=new R(n),this.connectionPromise=new Promise(((e,s)=>{this.resolveConnection=e,this.rejectConnection=s}))}connect(e,s={}){return this.connectionState===D.IDLE&&(this.connectionState=D.CONNECTING,this.messenger.addObserver(this.handshakeObserver),this.messenger.addObserver(this.acceptObserver),this.messenger.addObserver(this.rejectObserver),this.connectionPoll=window.setInterval((()=>{this.messenger.send(new b(e,s),-1,this.target,this.source)}),500)),this.connectionPromise}cancelConnecting(){this.connectionState<D.CONNECTED&&(this.stopConnectPolling(),this.rejectConnection(new A("User manually cancelled connection")))}handshake(){this.connectionState<D.HANDSHAKE&&(this.connectionState=D.HANDSHAKE,this.stopConnectPolling(),this.messenger.removeObserver(this.handshakeObserver))}accept(e,s,t,n,r){this.connectionState<D.CONNECTED&&(this.connectionState=D.CONNECTED,this.messenger.removeObserver(this.handshakeObserver),this.messenger.removeObserver(this.acceptObserver),this.messenger.removeObserver(this.rejectObserver),this.stopConnectPolling(),this.resolveConnection({sourceId:e,targetId:n,targetOrigin:r,scriptUrl:s,serializedInterface:t}))}reject(e){this.connectionState<D.CONNECTED&&(this.connectionState=D.REJECTED,this.messenger.removeObserver(this.handshakeObserver),this.messenger.removeObserver(this.acceptObserver),this.messenger.removeObserver(this.rejectObserver),this.stopConnectPolling(),this.rejectConnection(e))}stopConnectPolling(){clearInterval(this.connectionPoll),this.connectionPoll=void 0}}var S=function(e,s,t,n){return new(t||(t=Promise))((function(r,i){function o(e){try{a(n.next(e))}catch(e){i(e)}}function c(e){try{a(n.throw(e))}catch(e){i(e)}}function a(e){var s;e.done?r(e.value):(s=e.value,s instanceof t?s:new t((function(e){e(s)}))).then(o,c)}a((n=n.apply(e,s||[])).next())}))};const k="3.0";function j(e,s={}){return S(this,void 0,void 0,(function*(){const t=(n=e).contentWindow?n.contentWindow:null;var n;if(!t)return Promise.reject("invalid window");const r=Math.floor(1e6*Math.random()),i=new h(window),o=new d(t),c=new a(r,i);c.init();const l=new P(c,o,window);return m.connect(new K(l,c,s),new H(t),window)}))}class K{constructor(e,s,t){this.connector=e,this.postMessage=s,this.options=t}connect(){return this.connector.connect(k,this.options)}cancelConnecting(){this.postMessage.dispose()}}class H{constructor(e){this.target=e}getFactory(e){return new u(e.sourceId,window,e.targetId,this.target,e.targetOrigin)}}class L{constructor(){this.type=y.HANDSHAKE,this.payload={}}}class x{constructor(e,s,t,n,r){this.type=y.ACCEPT,this.payload={scriptUrl:e,interface:s,sourceId:t,targetId:n,targetOrigin:r},this.interface=s}}class B{constructor(e){this.type=y.REJECT,this.payload={reason:e.message,errorType:e.type},this.reason=e.message}}class V{constructor(e){this.receiver=e,this.messageType=f.CONNECT}notify(e,s){this.receiver.onConnectionReceived(e,s)}}}}]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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