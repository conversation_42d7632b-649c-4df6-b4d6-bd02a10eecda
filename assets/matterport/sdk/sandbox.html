<!doctype html><html><head><meta charset="utf-8"><title></title><meta name="viewport" content="width=device-width,initial-scale=1"><script defer="defer">(()=>{"use strict";var e;!function(e){e.SETUP="sandbox.setup",e.TO_SANDBOX="sandbox.to.sandbox",e.TO_CLIENT="sandbox.to.client"}(e||(e={}));class s{constructor(){this.values={}}add(e,s){this.getValuesAtKey(e).push(s)}remove(e,s){const t=this.values[e];if(t){const e=t.indexOf(s);e>-1&&t.splice(e,1)}}remove<PERSON>ey(e){delete this.values[e]}getValuesAtKey(e){const s=this.values[e]||[];return this.values[e]=s,s}values<PERSON><PERSON><PERSON><PERSON>(e){return this.getValuesAt<PERSON>ey(e).length}find(e,s){return this.values[e]&&this.values[e].find(s)}get keys(){return Object.keys(this.values)}has<PERSON>ey(e){return e in this.values}has(e,s){return this.hasKey(e)&&this.values[e].includes(s)}*[Symbol.iterator](){for(const e in this.values)for(const s of this.values[e])yield[e,s]}}Symbol.iterator;class t{constructor(e,t){this.id=e,this.target=t,this.messageHandlers=new s,this.onMessage=e=>{const{type:s,sandboxId:t}=e.data;if(this.id!==t)return;const n=this.messageHandlers.getValuesAtKey(s);for(const s of n)s(...e.data.payload)},window.addEventListener("message",this.onMessage)}dispose(){window.removeEventListener("message",this.onMessage),this.messageHandlers=new s}on(e,s){this.messageHandlers.add(e,s)}off(e,s){this.messageHandlers.remove(e,s)}send(e,...s){const t={sandboxId:this.id,type:e,payload:s};this.target.postMessage(t,"*")}}function n(e,s,t){for(;s.childNodes.length;){const n=s.childNodes[0];if("SCRIPT"===n.nodeName){const e=document.createElement("script"),o=n;o.src?e.setAttribute("src",o.src):e.innerHTML=o.innerHTML||"",s.removeChild(n),t.push(e)}else e.append(n)}}window.addEventListener("message",(function s(o){if(o.source===window.parent&&o.data.type===e.SETUP){window.removeEventListener("message",s),function(e){const s=new t(e.sandboxId,window.parent),n=e.globalVariableMap;window[n.send]=(e,...t)=>s.send(e,...t),window[n.on]=(e,t)=>s.on(e,t),window[n.off]=(e,t)=>s.off(e,t),window[n.tag]=e.parentTag,window[n.docked]=e.docked}(o.data);const e=(new DOMParser).parseFromString(o.data.customHTML,"text/html"),a=[];n(document.head,e.head,a),n(document.body,e.body,a),function e(s){const t=s[0];t&&(t.onload=function(){s.shift(),e(s)},document.head.appendChild(t))}(a)}}))})();</script></head><body></body></html>