var e,t,n,a,o,s,i,r,c,d,l,h,u,p,m,g,w,f,y,I,T,E,v,C={d:(e,t)=>{for(var n in t)C.o(t,n)&&!C.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},A={};C.d(A,{w:()=>v,c:()=>nc}),function(e){let t,n,a;!function(e){e.PHASE_CHANGE="application.phasechange"}(t=e.Event||(e.Event={})),function(e){e.UNINITIALIZED="appphase.uninitialized",e.WAITING="appphase.waiting",e.LOADING="appphase.loading",e.STARTING="appphase.starting",e.PLAYING="appphase.playing",e.ERROR="appphase.error"}(n=e.Phase||(e.Phase={})),function(e){e.UNKNOWN="application.unknown",e.WEBVR="application.webvr",e.SHOWCASE="application.showcase",e.WORKSHOP="application.workshop"}(a=e.Application||(e.Application={}))}(e||(e={})),function(e){let t,n,a;!function(e){e.IMAGE="image",e.PDF="pdf",e.VIDEO="video",e.RICH="rich",e.ZIP="zip",e.TEXT="text",e.AUDIO="audio",e.MODEL="model",e.APPLICATION="application"}(t=e.MediaType||(e.MediaType={})),function(e){e.EXTERNAL="external",e.UPLOAD="upload",e.SANDBOX="sandbox"}(n=e.AttachmentCategory||(e.AttachmentCategory={})),function(e){e.COMMENT="comment",e.MATTERTAG="mattertag"}(a=e.ParentType||(e.ParentType={}))}(t||(t={})),function(e){let t,n;!function(e){e.MOVE="camera.move"}(t=e.Event||(e.Event={})),function(e){e.FORWARD="FORWARD",e.LEFT="LEFT",e.RIGHT="RIGHT",e.BACK="BACK",e.UP="UP",e.DOWN="DOWN"}(n=e.Direction||(e.Direction={}))}(n||(n={})),function(e){let t;!function(e){e.CHANGE_START="floors.changestart",e.CHANGE_END="floors.changeend"}(t=e.Event||(e.Event={}))}(a||(a={})),function(e){let t;!function(e){e.SUCCESS="astar.status.success",e.NO_PATH="astar.status.no_path",e.TIMEOUT="astar.status.timeout",e.NO_START_VERTEX="astar.status.no_start",e.NO_END_VERTEX="astar.status.no_end"}(t=e.AStarStatus||(e.AStarStatus={}))}(o||(o={})),function(e){let t;!function(e){e.POSITION_UPDATED="label.positionupdated"}(t=e.Event||(e.Event={}))}(s||(s={})),function(e){let t,n,a;!function(e){e.WINDOW="link.creationpolicy.window",e.REFERRER="link.creationpolicy.referrer",e.MATTERPORT="link.creationpolicy.matterport"}(t=e.CreationPolicy||(e.CreationPolicy={})),function(e){e.DEFAULT="link.openpolicy.default",e.NEW_WINDOW="link.openpolicy.newwindow",e.SAME_FRAME="link.openpolicy.sameframe",e.CURRENT_WINDOW="link.openpolicy.current"}(n=e.OpenPolicy||(e.OpenPolicy={})),function(e){e.DEFAULT="link.destination.default",e.MATTERPORT="link.destination.matterport"}(a=e.DestinationPolicy||(e.DestinationPolicy={}))}(i||(i={})),function(e){let t,n,a,o,s;!function(e){e.INSTANT="transition.instant",e.FLY="transition.fly",e.FADEOUT="transition.fade"}(t=e.Transition||(e.Transition={})),function(e){e.NAVIGATION="tag.link.nav",e.MODEL="tag.link.model",e.EXT_LINK="tag.link.ext"}(n=e.LinkType||(e.LinkType={})),function(e){e.NONE="tag.chunk.none",e.TEXT="tag.chunk.text",e.LINK="tag.chunk.link"}(a=e.DescriptionChunkType||(e.DescriptionChunkType={})),function(e){e.HOVER="tag.hover",e.CLICK="tag.click",e.LINK_OPEN="tag.linkopen"}(o=e.Event||(e.Event={})),function(e){e.NONE="mattertag.media.none",e.PHOTO="mattertag.media.photo",e.VIDEO="mattertag.media.video",e.RICH="mattertag.media.rich"}(s=e.MediaType||(e.MediaType={}))}(r||(r={})),function(e){let t,n,a;!function(e){e.INSIDE="mode.inside",e.OUTSIDE="mode.outside",e.DOLLHOUSE="mode.dollhouse",e.FLOORPLAN="mode.floorplan",e.TRANSITIONING="mode.transitioning"}(t=e.Mode||(e.Mode={})),function(e){e.CHANGE_START="viewmode.changestart",e.CHANGE_END="viewmode.changeend"}(n=e.Event||(e.Event={})),function(e){e.INSTANT="transition.instant",e.FLY="transition.fly",e.FADEOUT="transition.fade"}(a=e.TransitionType||(e.TransitionType={}))}(c||(c={})),function(e){let t;!function(e){e.MODEL_LOADED="model.loaded"}(t=e.Event||(e.Event={}))}(d||(d={})),function(e){let t;!function(e){e.NONE="intersectedobject.none",e.MODEL="intersectedobject.model",e.TAG="intersectedobject.tag",e.SWEEP="intersectedobject.sweep",e.UNKNOWN="intersectedobject.unknown"}(t=e.Colliders||(e.Colliders={}))}(l||(l={})),h||(h={}),function(e){let t,n;!function(e){e.CAMERA="sensor.sensortype.camera"}(t=e.SensorType||(e.SensorType={})),function(e){e.SPHERE="sensor.sourcetype.sphere",e.BOX="sensor.sourcetype.box",e.CYLINDER="sensor.sourcetype.cylinder"}(n=e.SourceType||(e.SourceType={}))}(u||(u={})),function(e){let t,n,a,o;!function(e){e.ENTER="sweep.enter",e.EXIT="sweep.exit"}(t=e.Event||(e.Event={})),function(e){e.INSTANT="transition.instant",e.FLY="transition.fly",e.FADEOUT="transition.fade"}(n=e.Transition||(e.Transition={})),function(e){e.ALIGNED="aligned",e.UNALIGNED="unaligned"}(a=e.Alignment||(e.Alignment={})),function(e){e.UNPLACED="unplaced",e.AUTO="auto",e.MANUAL="manual"}(o=e.Placement||(e.Placement={}))}(p||(p={})),function(e){let t;!function(e){e.UNKNOWN="tag.attachment.unknown",e.APPLICATION="tag.attachment.application",e.AUDIO="tag.attachment.audio",e.IMAGE="tag.attachment.image",e.MODEL="tag.attachment.model",e.PDF="tag.attachment.pdf",e.RICH="tag.attachment.rich",e.TEXT="tag.attachment.text",e.VIDEO="tag.attachment.video",e.ZIP="tag.attachment.zip",e.SANDBOX="tag.attachment.sandbox"}(t=e.AttachmentType||(e.AttachmentType={}))}(m||(m={})),function(e){let t,n;!function(e){e.STARTED="tour.started",e.STOPPED="tour.stopped",e.ENDED="tour.ended",e.STEPPED="tour.stepped"}(t=e.Event||(e.Event={})),function(e){e.INACTIVE="tour.inactive",e.ACTIVE="tour.active",e.STOP_SCHEDULED="tour.stopscheduled"}(n=e.PlayState||(e.PlayState={}))}(g||(g={})),function(e){let t,n,a;!function(e){e.OBJ_LOADER="mp.objLoader",e.FBX_LOADER="mp.fbxLoader",e.DAE_LOADER="mp.daeLoader",e.GLTF_LOADER="mp.gltfLoader",e.SCROLLING_TUBE="mp.scrollingTube",e.TRANSFORM_CONTROLS="mp.transformControls",e.LIGHTS_COMPONENT="mp.lights",e.POINT_LIGHT="mp.pointLight",e.DIRECTIONAL_LIGHT="mp.directionalLight",e.AMBIENT_LIGHT="mp.ambientLight",e.CAMERA="mp.camera",e.INPUT="mp.input",e.XR="mp.xr"}(t=e.Component||(e.Component={})),function(e){e.CLICK="INTERACTION.CLICK",e.HOVER="INTERACTION.HOVER",e.DRAG="INTERACTION.DRAG",e.DRAG_BEGIN="INTERACTION.DRAG_BEGIN",e.DRAG_END="INTERACTION.DRAG_END",e.POINTER_MOVE="INTERACTION.POINTER_MOVE",e.POINTER_BUTTON="INTERACTION.POINTER_BUTTON",e.SCROLL="INTERACTION.SCROLL",e.KEY="INTERACTION.KEY",e.LONG_PRESS_START="INTERACTION.LONG_PRESS_START",e.LONG_PRESS_END="INTERACTION.LONG_PRESS_END",e.MULTI_SWIPE="INTERACTION.MULTI_SWIPE",e.MULTI_SWIPE_END="INTERACTION.MULTI_SWIPE_END",e.PINCH="INTERACTION.PINCH",e.PINCH_END="INTERACTION.PINCH_END",e.ROTATE="INTERACTION.ROTATE",e.ROTATE_END="INTERACTION.ROTATE_END"}(n=e.InteractionType||(e.InteractionType={})),function(e){e.INPUT="input",e.OUTPUT="output",e.EVENT="event",e.EMIT="emit"}(a=e.PathType||(e.PathType={}))}(w||(w={})),function(e){e[e.BACKSPACE=8]="BACKSPACE",e[e.TAB=9]="TAB",e[e.RETURN=13]="RETURN",e[e.SHIFT=16]="SHIFT",e[e.CONTROL=17]="CONTROL",e[e.ALT=18]="ALT",e[e.ESCAPE=27]="ESCAPE",e[e.SPACE=32]="SPACE",e[e.HASH=35]="HASH",e[e.LEFTARROW=37]="LEFTARROW",e[e.UPARROW=38]="UPARROW",e[e.RIGHTARROW=39]="RIGHTARROW",e[e.DOWNARROW=40]="DOWNARROW",e[e.DELETE=46]="DELETE",e[e.ZERO=48]="ZERO",e[e.ONE=49]="ONE",e[e.TWO=50]="TWO",e[e.THREE=51]="THREE",e[e.FOUR=52]="FOUR",e[e.FIVE=53]="FIVE",e[e.SIX=54]="SIX",e[e.SEVEN=55]="SEVEN",e[e.EIGHT=56]="EIGHT",e[e.NINE=57]="NINE",e[e.AT=64]="AT",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.SEMICOLON=186]="SEMICOLON",e[e.PLUSEQUALS=187]="PLUSEQUALS",e[e.COMMA=188]="COMMA",e[e.DASHUNDERSCORE=189]="DASHUNDERSCORE",e[e.PERIOD=190]="PERIOD",e[e.OPENBRACKET=219]="OPENBRACKET"}(f||(f={})),function(e){e[e.DOWN=0]="DOWN",e[e.PRESSED=1]="PRESSED",e[e.UP=2]="UP"}(y||(y={})),function(e){e[e.PRIMARY=0]="PRIMARY",e[e.MIDDLE=1]="MIDDLE",e[e.SECONDARY=2]="SECONDARY",e[e.BACK=3]="BACK",e[e.FORWARD=4]="FORWARD",e[e.COUNT=5]="COUNT"}(I||(I={})),function(e){e[e.NONE=0]="NONE",e[e.PRIMARY=1]="PRIMARY",e[e.SECONDARY=4]="SECONDARY",e[e.MIDDLE=2]="MIDDLE",e[e.BACK=8]="BACK",e[e.FORWARD=16]="FORWARD",e[e.ALL=31]="ALL"}(T||(T={}));class S{constructor(e){this.directionMap={[n.Direction.FORWARD]:e.FORWARD.clone(),[n.Direction.LEFT]:e.LEFT.clone(),[n.Direction.RIGHT]:e.RIGHT.clone(),[n.Direction.BACK]:e.BACK.clone(),[n.Direction.UP]:e.UP.clone(),[n.Direction.DOWN]:e.DOWN.clone()}}toVector(e){return this.directionMap[e]}}class D{constructor(e){this.CwfViewmode=e}toSdk(e,t){switch(e){case this.CwfViewmode.Panorama:return t?c.Mode.INSIDE:c.Mode.OUTSIDE;case this.CwfViewmode.Dollhouse:return c.Mode.DOLLHOUSE;case this.CwfViewmode.Floorplan:return c.Mode.FLOORPLAN;case this.CwfViewmode.Transition:return c.Mode.TRANSITIONING;case this.CwfViewmode.Outdoor:return c.Mode.OUTSIDE;default:return c.Mode.INSIDE}}fromSdk(e){switch(e){case c.Mode.INSIDE:case c.Mode.OUTSIDE:return this.CwfViewmode.Panorama;case c.Mode.DOLLHOUSE:return this.CwfViewmode.Dollhouse;case c.Mode.FLOORPLAN:return this.CwfViewmode.Floorplan;case c.Mode.TRANSITIONING:return this.CwfViewmode.Transition}}}class b{constructor(e){this.CommandMode=e}toSdk(e){switch(e){case this.CommandMode.INSIDE:return c.Mode.INSIDE;case this.CommandMode.DOLLHOUSE:return c.Mode.DOLLHOUSE;case this.CommandMode.FLOORPLAN:return c.Mode.FLOORPLAN;case this.CommandMode.TRANSITIONING:return c.Mode.TRANSITIONING;case this.CommandMode.OUTSIDE:return c.Mode.OUTSIDE;default:return c.Mode.INSIDE}}fromSdk(e){switch(e){case c.Mode.INSIDE:return this.CommandMode.INSIDE;case c.Mode.OUTSIDE:return this.CommandMode.OUTSIDE;case c.Mode.DOLLHOUSE:return this.CommandMode.DOLLHOUSE;case c.Mode.FLOORPLAN:return this.CommandMode.FLOORPLAN;case c.Mode.TRANSITIONING:return this.CommandMode.TRANSITIONING}}}!function(e){e.INSTANT="transition.instant",e.FLY="transition.fly",e.FADEOUT="transition.fade",e.MOVEFADE="transition.movefade"}(E||(E={}));class O{constructor(e){this.toSdkTransitionMap={[e.Interpolate]:E.FLY,[e.FadeToBlack]:E.FADEOUT,[e.Instant]:E.INSTANT,[e.MoveToBlack]:E.MOVEFADE},this.fromSdkTransitionMap={[E.FLY]:e.Interpolate,[E.FADEOUT]:e.FadeToBlack,[E.INSTANT]:e.Instant,[E.MOVEFADE]:e.MoveToBlack}}toSdkTransition(e){return this.toSdkTransitionMap[e]}fromSdkTransition(e){return this.fromSdkTransitionMap[e]}}class M{constructor(e){this.THREE=e,this.tempEuler=new e.Euler}quaternionToRotation(e,t){const n=this.tempEuler.setFromQuaternion(e,M.eulerOrder),a=t||{};return a.x=this.THREE.MathUtils.radToDeg(n.x),a.y=this.THREE.MathUtils.radToDeg(n.y),a.z=this.THREE.MathUtils.radToDeg(n.z),a}rotationToQuaternion(e,t){const n=t||new this.THREE.Quaternion;return this.tempEuler.x=this.THREE.MathUtils.degToRad(e.x),this.tempEuler.y=this.THREE.MathUtils.degToRad(e.y),this.tempEuler.z=this.THREE.MathUtils.degToRad(e.z||0),this.tempEuler.order=M.eulerOrder,n.setFromEuler(this.tempEuler)}}M.eulerOrder="YXZ";class N{constructor(e){this.CwfSweepAlignmentType=e}isSweepAligned(e,t){if(!e||!t)return!1;const n=t&&e.getSweep(t);return!!n&&n.alignmentType===this.CwfSweepAlignmentType.ALIGNED}isCurrentSweepAligned(e){return this.isSweepAligned(e,e.currentSweep)}}!function(e){e.LEGACY="legacy",e.API="api"}(v||(v={}));class R{constructor(e){this.sweepData=e}getIdForSweep(e){return e.uuid}getSweepForId(e){return this.sweepData.getSweepByUuid(e)}getIdFromCwfId(e){const t=this.sweepData.getSweep(e);return this.getIdForSweep(t)}}class P{constructor(e){this.sweepData=e}getIdForSweep(e){return e.id}getSweepForId(e){return this.sweepData.getSweep(e)}getIdFromCwfId(e){const t=this.sweepData.getSweep(e);return this.getIdForSweep(t)}}class L{constructor(e){this.floorData=e}getIdForFloor(e){return String(e.index)}getFloorForId(e){return this.floorData.getFloorAtIndex(parseInt(e,10))}getIdFromCwfId(e){const t=this.floorData.getFloor(e);return this.getIdForFloor(t)}}class x{constructor(e){this.floorData=e}getIdForFloor(e){return e.id}getFloorForId(e){return this.floorData.getFloor(e)}getIdFromCwfId(e){const t=this.floorData.getFloor(e);return this.getIdForFloor(t)}}class k{constructor(e,t){this.roomData=e,this.floorData=t}getIdForRoom(e){const t=this.floorData.getFloor(e.floorId);return t?String((t.index<<16)+e.meshSubgroup):""}getRoomForId(e){return this.roomData.getByMeshSubgroup(65535&parseInt(e,10))}getIdFromCwfId(e){const t=this.roomData.get(e);return this.getIdForRoom(t)}}class _{constructor(e){this.roomData=e}getIdForRoom(e){return e.id}getRoomForId(e){return this.roomData.get(e)}getIdFromCwfId(e){const t=this.roomData.get(e);return this.getIdForRoom(t)}}class U{constructor(t){this.toSdkPhaseMap={[t.UNINITIALIZED]:e.Phase.UNINITIALIZED,[t.WAITING]:e.Phase.WAITING,[t.LOADING]:e.Phase.LOADING,[t.STARTING]:e.Phase.STARTING,[t.PLAYING]:e.Phase.PLAYING,[t.ERROR]:e.Phase.ERROR},this.fromSdkPhaseMap={[e.Phase.UNINITIALIZED]:t.UNINITIALIZED,[e.Phase.WAITING]:t.WAITING,[e.Phase.LOADING]:t.LOADING,[e.Phase.STARTING]:t.STARTING,[e.Phase.PLAYING]:t.PLAYING,[e.Phase.ERROR]:t.ERROR}}toSdkAppPhase(e){return this.toSdkPhaseMap[e]}fromSdkAppPhase(e){return this.fromSdkPhaseMap[e]}}class V{constructor(t){this.toSdkApplicationMap={[t.SHOWCASE]:e.Application.SHOWCASE,[t.WORKSHOP]:e.Application.WORKSHOP,[t.WEBVR]:e.Application.WEBVR,[t.UNKNOWN]:e.Application.UNKNOWN},this.fromSdkApplicationMap={[e.Application.SHOWCASE]:t.SHOWCASE,[e.Application.WORKSHOP]:t.WORKSHOP,[e.Application.WEBVR]:t.WEBVR,[e.Application.UNKNOWN]:t.UNKNOWN}}toSdkApplication(e){return this.toSdkApplicationMap[e]}fromSdkApplication(e){return this.fromSdkApplicationMap[e]}}class F{constructor(e,t,n){this.dependencies=e,this.subscriptionFactory=t,this.observers=new Set,this.currentData=n.create(),this.freshDataCache=n.create()}static async create(e,t,n){const a=await e.getDependencies();return new F(a,t,n)}getData(){return this.currentData.data}subscribe(e){return this.addObserver(e),new class{constructor(e){this.sdkObservable=e,this.renew()}renew(){this.sdkObservable.addObserver(e)}cancel(){this.sdkObservable.removeObserver(e)}}(this)}addObserver(e){if(!this.observers.has(e)){class t{constructor(e){this.observable=e}onChanged(){this.observable.onChanged()}}this.observers.add(e),1===this.observers.size&&(this.currentData.update(...this.dependencies),this.changeSubscription=this.subscriptionFactory.create(new t(this),...this.dependencies)),e.dirty=!0}}removeObserver(e){0!==this.observers.size&&(this.observers.delete(e),0===this.observers.size&&this.changeSubscription.cancel())}onChanged(){if(this.freshDataCache.update(...this.dependencies),!this.currentData.equals(this.freshDataCache)){this.currentData.copy(this.freshDataCache);for(const e of this.observers)e.dirty=!0}}}class G{constructor(e,...t){this.ctor=e,this.args=t}create(...e){return new this.ctor(...this.args,...e)}}const H=e=>"propertyObservers"===e||"changeObservers"===e||"isObservable"===e||"childChangeFunctions"===e||"isObservableProxy"===e||"diffRoot"===e||"elementChangedHandlers"===e||"knownKeysMap"===e||"knownKeysList"===e||"isVector2"===e||"isVector3"===e||"isQuaternion"===e||"onPropertyChanged"===e||"removeOnPropertyChanged"===e||"onChanged"===e||"target"===e,B=(e,t=[])=>{if(void 0!==e){if(t.includes(e))return Array.isArray(e)||ArrayBuffer.isView(e)?[]:{};if(e instanceof Date)return new Date(e);if(null===e)return null;if(e.deepCopy)return e.deepCopy();if(e.isQuaternion)return{x:e.x,y:e.y,z:e.z,w:e.w};if("object"==typeof e){const n=Array.isArray(e)||ArrayBuffer.isView(e)?[]:{};for(const a in e){if(H(a))continue;const o=e[a];o instanceof Date?n[a]=new Date(o):"function"!=typeof o&&(t.push(e),n[a]="object"==typeof o?B(o,t):o,t.pop())}return n}return e}},z=(e,t)=>{if(null===e||"object"!=typeof e)return e!==t;if(!t)return!0;for(const n in t)if(!(n in e))return!0;for(const n in e)if(z(e[n],t[n]))return!0;return!1};class ${create(e,t){return t.onChanged((()=>e.onChanged()))}}class j{constructor(t,n){this._data={phase:e.Phase.UNINITIALIZED,phaseTimes:{},application:e.Application.SHOWCASE},this.AppPhase=t.AppPhase,this.appPhaseConverter=n.appPhaseConverter,this.applicationConverter=n.applicationConverter}get data(){return this._data}equals(e){return!z(this,e)}copy(e){this._data.phase=e.data.phase,function(e,t,...n){for(const a of n)e[a]=t[a]}(this.data.phaseTimes,e.data.phaseTimes),this._data.application=e.data.application}update(t){this._data.phase=this.appPhaseConverter.toSdkAppPhase(t.phase),this._data.application=this.applicationConverter.toSdkApplication(t.application),this._data.phaseTimes[e.Phase.WAITING]=t.phaseTimes[this.AppPhase.WAITING],this._data.phaseTimes[e.Phase.LOADING]=t.phaseTimes[this.AppPhase.LOADING],this._data.phaseTimes[e.Phase.PLAYING]=t.phaseTimes[this.AppPhase.PLAYING],this._data.phaseTimes[e.Phase.STARTING]=t.phaseTimes[this.AppPhase.STARTING],this._data.phaseTimes[e.Phase.UNINITIALIZED]=t.phaseTimes[this.AppPhase.UNINITIALIZED],this._data.phaseTimes[e.Phase.ERROR]=t.phaseTimes[this.AppPhase.ERROR]}}class W{}class q extends W{constructor(){super(),this.id="GET_APP_STATE"}}class K extends W{constructor(){super(),this.id="GET_APP_PHASE_TIMES"}}var Y,X;!function(e){e[e.LOW=0]="LOW",e[e.MEDIUM=1]="MEDIUM",e[e.HIGH=2]="HIGH",e[e.HIGHEST=3]="HIGHEST"}(Y||(Y={})),function(e){e[e.PENDING=0]="PENDING",e[e.SENDING=1]="SENDING",e[e.FAILED=2]="FAILED",e[e.DONE=3]="DONE"}(X||(X={}));const Z=new WeakMap;class Q{constructor({retries:e=0,concurrency:t=6}={}){this.totalBytesDownloaded=0,this.queue=[],this.retries=e,this.concurrency=t}get(e,t){return this.request("GET",e,t)}head(e,t){return this.request("HEAD",e,t)}options(e,t){return this.request("OPTIONS",e,t)}post(e,t){return this.request("POST",e,t)}put(e,t){return this.request("PUT",e,t)}patch(e,t){return this.request("PATCH",e,t)}delete(e,t){return this.request("DELETE",e,t)}request(e,t,n){const a=new J(e,t,n);if(null==n?void 0:n.signal){const e=()=>{this.queue.includes(a)&&(a.status===X.SENDING&&a.abort(),this.dequeue(a),a.onFail(new DOMException("Aborted","AbortError")))};n.signal.addEventListener("abort",e),Z.set(a,(()=>{var t;null===(t=n.signal)||void 0===t||t.removeEventListener("abort",e),Z.delete(a)}))}return this.enqueue(a),a.promise}update(){let e;for(;e=this.getNextPendingRequest();)this.sendRequest(e);for(;e=this.getNextOverflowingGet();)e.abort(),e.status=X.PENDING;this.updateTimeout=null}enqueue(e){let t=0;for(t=0;t<this.queue.length;t++){if(this.queue[t].priority<e.priority)break}this.queue.splice(t,0,e),this.updateTimeout||(this.updateTimeout=window.setTimeout((()=>{this.update()}),1))}dequeue(e){var t;null===(t=Z.get(e))||void 0===t||t();const n=this.queue.indexOf(e);if(-1===n)throw new Error("Can't dequeue request not in queue");this.queue.splice(n,1),this.update()}getNextPendingRequest(){for(let e=0;e<this.queue.length&&e<this.concurrency;e++){const t=this.queue[e];if(t.status===X.PENDING)return t}return null}getNextOverflowingGet(){for(let e=this.concurrency;e<this.queue.length;e++){const t=this.queue[e];if(t.status===X.SENDING&&t.priority!==Y.HIGHEST&&"GET"===t.method)return t}return null}shouldRetryStatusCode(e){return!Q.doNotRetryStatusCodes[e]}sendRequest(e){e.status=X.SENDING,e.send().then((t=>{e.status=X.DONE,this.dequeue(e),e.contentLength&&e.contentLength>0&&(this.totalBytesDownloaded+=Number(e.contentLength)),e.onDone(t)})).catch((t=>{const n=null!==e.maxRetries?e.maxRetries:this.retries;let a=e.sendAttempts<n;if("object"==typeof t){const e=t.status_code||0;a=a&&this.shouldRetryStatusCode(e)}a?(e.status=X.PENDING,this.update(),console.warn(`Retried ${e.url}`),console.warn(t)):(e.status=X.FAILED,this.dequeue(e),console.warn(`Failed ${e.url}`),e.onFail(t))}))}}Q.doNotRetryStatusCodes={400:!0,401:!0,403:!0,404:!0,405:!0,406:!0,410:!0,411:!0,414:!0,415:!0,421:!0,431:!0,451:!0};class J{constructor(e,t,n={}){this.sendAttempts=0,this.status=X.PENDING,this.contentLength=0,this.isAborting=!1,this.url=t,this.method=e,this.auth=n.auth||null,this.withCredentials=n.withCredentials||!1,this.priority=n.priority||Y.MEDIUM,this.responseType=n.responseType||null,this.body=n.body||null,this.headers=n.headers||{},this.maxRetries=n.maxRetries||null,this.onProgress=n.onProgress,this.promise=new Promise(((e,t)=>{this.onDone=e,this.onFail=t}))}send(){const e=this.xhr=function(e,t,n){let a;if("undefined"!=typeof XMLHttpRequest)a=new XMLHttpRequest,a.withCredentials=n;else{if("undefined"==typeof XDomainRequest)throw new Error("No XMLHTTPRequest or XDomainRequest... are you trying to run me in node? :(");a=new XDomainRequest}return a.open(e,t,!0),a}(this.method,this.url,this.withCredentials);if(this.responseType)if("arraybuffer"===this.responseType||"text"===this.responseType||"json"===this.responseType||"blob"===this.responseType)e.responseType=this.responseType;else{if("image"!==this.responseType)throw new Error('reponseType can only be one of "arraybuffer", "text", "json", "blob", "image"');e.responseType="blob"}"json"===this.responseType&&e.setRequestHeader("Accept","application/json"),this.auth&&"string"==typeof this.auth&&e.setRequestHeader("Authorization",this.auth);for(const t in this.headers)e.setRequestHeader(t,this.headers[t]);return this.body&&"object"==typeof this.body&&(this.body instanceof FormData||(this.body=JSON.stringify(this.body),e.setRequestHeader("Content-Type","application/json"))),this.onProgress&&(e.onprogress=this.onProgress),new Promise(((t,n)=>{e.onreadystatechange=()=>{if(4===e.readyState){if(200===e.status||201===e.status||204===e.status)return this.parseResponse(this.xhr).then((e=>{t(e)}));if(!this.isAborting)return this.parseResponse(this.xhr).then((t=>{n(Object.assign({status_code:e.status},t))})).catch((()=>{n({status_code:e.status})}));this.isAborting=!1}},e.onerror=function(e){n(e)},e.send(this.body),this.sendAttempts++}))}parseResponse(e){return new Promise(((t,n)=>{var a;try{if(!e)throw new Error(`No request received. Trying ${this.method} on ${this.url} and expecting ${this.responseType}, but request was ${this.xhr}`);let n=e.response;if(this.contentLength=parseInt(null!==(a=e.getResponseHeader("Content-Length"))&&void 0!==a?a:"0",10),"json"===this.responseType&&"object"!=typeof n)t(JSON.parse(e.responseText));else if(200!==e.status&&201!==e.status&&204!==e.status||"image"!==this.responseType)t(n);else{const e=URL.createObjectURL(n);n=new Image,n.onload=function(){URL.revokeObjectURL(e),t(n)},n.src=e,n.crossOrigin="Anonymous"}}catch(e){n({error:"Payload was not valid JSON"})}}))}abort(){if(null===this.xhr)throw new Error("Cannot abort unsent Request");this.isAborting=!0,this.xhr.abort()}}const ee=window.navigationStart||Date.now();var te;!function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(te||(te={}));class ne{constructor(e){this.timers={},this.handlers={[te.DEBUG]:console.debug,[te.INFO]:console.info,[te.WARN]:console.warn,[te.ERROR]:console.error};const t=e.split(new RegExp("/|\\\\"));this.prefix="["+t[t.length-1].replace(".js","")+"]"}message(e){if(ne.level>=e&&console){return(this.handlers[e]?this.handlers[e]:console.log).bind(console,this.getPrefix())}return()=>{}}get debug(){return this.message(te.DEBUG)}get devInfo(){return()=>{}}get debugInfo(){return this.debug}get debugWarn(){return this.message(ne.level>=te.DEBUG?te.WARN:te.DEBUG)}get info(){return this.message(te.INFO)}get warn(){return this.message(te.WARN)}get error(){return this.message(te.ERROR)}time(e){ne.level>=te.DEBUG&&(this.timers[e]=Date.now())}timeEnd(e){if(ne.level>=te.DEBUG){const t=this.timers[e];if(!t)return;const n=(Date.now()-t)/1e3;this.debug(e,n+"s")}}getPrefix(){const e=(Date.now()-ee)/1e3+"s";return`${this.prefix} ${e}`}}ne.level=te.INFO;const ae=new ne("sdk command");class oe{constructor(e){this._executor=e}static async create(e,t){const n=await e.getDependencies();return new oe(t.create(...n))}get executor(){return this._executor}validateInput(e,t){return this.executor.validateInput(e,t)}exec(e,t){const n=this.executor.validateInput(e,t);return this.executor.exec(n,t)}}function se(e,t,n){return`'${e}' is expected to be a(n) ${t}; got '${JSON.stringify(n)}'`}function ie(e,t,n){return e.validateInput(t,n)}function re(e,t,n){return e.exec(t,n)}async function ce(e,t){return new class{constructor(e){this.command=e}validateInput(e,t){return ie(this.command,e,t)}exec(e,n){return ae.warn(t),this.command.exec(e,n)}}(await e)}function de(e){return!!e&&"string"==typeof e}function le(e){return"string"==typeof e}class he{constructor(){this.base=new Promise(((e,t)=>{this.resolver=e,this.rejecter=t})),this._promise=new ue(this.base,this),this.progressCallbacks=[]}then(e,t){return this.base.then(e,t)}catch(e){return this.base.catch(e)}resolve(e){return this.resolver(e),this}reject(e){return this.rejecter(e),this}notify(e){for(const t of this.progressCallbacks)t(e)}progress(e){return this.progressCallbacks.push(e),this}promise(){return this._promise}nativePromise(){return this.base}static resolve(){const e=new he;return e.resolve(),e.promise()}static reject(e){const t=new he;return t.reject(e),t.promise()}static all(e){const t=[];for(const n of e)n.nativePromise?t.push(n.nativePromise()):t.push(n);const n=new he;return Promise.all(t).then((e=>n.resolve(e)),(e=>n.reject(e))),n.promise()}}class ue{constructor(e,t){this.basePromise=e,this.baseDeferred=t}then(e,t){return this.baseDeferred.then(e,t),this}catch(e){return this.baseDeferred.catch(e),this}progress(e){return this.baseDeferred.progress(e),this}nativePromise(){return this.basePromise}}const pe=new ne("asset.registerTexture");class me{constructor(e,t,n){this.THREE=e,this.loadImage=t,this.scopedTextures=n,this.svgLoader=new ge(new Q)}validateInput(e,t){const{textureId:n,textureSrc:a}=e;if(!de(n))throw Error(`textureId ${n} is not a valid string`);if(!de(a))throw Error(`textureSrc ${a} is not a valid string`);const o=this.scopedTextures.get(t.client.applicationKey);if(o){if(o.map[n])throw Error(n+" already has a registered texture")}return{textureId:n,textureSrc:a}}async exec(e,t){const{textureId:n,textureSrc:a}=e,o=this.scopedTextures.get(t.client.applicationKey)||{imagePromise:{},map:{}};this.scopedTextures.set(t.client.applicationKey,o);const[s,i]=a.endsWith(".svg")?this.loadSvgTexture(a):this.loadImageTexture(a);o.imagePromise[n]=i;try{const e=await i;s.image=e,s.needsUpdate=!0,o.map[n]=s}catch(t){throw delete o.imagePromise[n],Error(`Failed to load ${e.textureSrc}`)}}loadImageTexture(e){const t=new he,n=this.loadImage(e,(()=>t.resolve(n.image)),(e=>t.reject(e)));return[n,t.nativePromise()]}loadSvgTexture(e){return[new this.THREE.Texture,this.svgLoader.load(e)]}}class ge{constructor(e){this.queue=e}async load(e){const t=await this.queue.get(e,{responseType:"text"}),n=document.createElement("div");n.innerHTML=t;const a=n.querySelector("svg");if(!a)throw Error("Failed trying to load "+e+"as an svg.");const o=a.getAttribute("width"),s=a.getAttribute("height");return o||s?(s&&!o&&(pe.warn(e,"does not have a defined width. Defaulting width equal to height"),a.setAttribute("width",s)),o&&!s&&(pe.warn(e,"does not have a defined height. Defaulting height equal to width"),a.setAttribute("height",o))):(pe.warn(e,"does not have a defined size. Defaulting to a 128x128 resolution"),a.setAttribute("width",ge.defaultResolution),a.setAttribute("height",ge.defaultResolution)),new Promise((e=>{const t=new Image;t.onload=()=>e(t),t.src=URL.createObjectURL(new Blob([a.outerHTML],{type:"image/svg+xml"}))}))}}ge.defaultResolution="128";class we{constructor(...e){this.dependencies=e}static get none(){return this._none}async getDependencies(){return Promise.all(this.dependencies)}static extend(e,...t){return new we(...e.dependencies,...t)}static combine(e,t){return new we(...e.dependencies,...t.dependencies)}}we._none=new we;const fe=function(e){let t="";const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";for(let a=0;a<e;a++)t+=n.charAt(Math.floor(Math.random()*n.length));return t};(()=>{const e={}})();const ye=["dev-app.matterport.com","dev-app.matterportvr.cn","qa3-app.matterport.com","qa3-app.matterportvr.cn","gc1-app.matterport.com","my.matterport.com","my.matterportvr.cn","showcase-next.matterport.com","static.matterport.com","static.matterportvr.cn","static.matterport.us","matterport.github.io","127.0.0.1","localhost"];class Ie{constructor(e,t,n){this.apiPromise=e,this.floorData=t,this.modelData=n}validateInput(e){const{sid:t}=e;if(!t)return{sid:this.modelData.model.sid};if(de(t))return{sid:t};throw Error(`"${t}" is not a valid sid string`)}async exec(e,t){const n=await this.apiPromise,a=`${n.getApi().baseUrl}${`/api/player/models/${e.sid}/files?type=3`}`,o=await n.getApi().get(a).then((e=>JSON.parse(e))).catch((e=>this.throw(e))),s=e=>o.templates[0].replace("{{filename}}",e),i=this.floorData.getFloorCount(),r=n.getApi().get(s("render/vr_colorplan.json")).then((e=>JSON.parse(e))),c=await r,d=new Array(i).fill(void 0).map(((e,t)=>fetch(s(`render/vr_colorplan_${t.toString().padStart(3,"0")}.png`)).then((e=>e.arrayBuffer())))),l=`${s("render/colorplan_alpha_all.png")}&width=${c.width}&height=${c.height}`,h=fetch(l).then((e=>e.arrayBuffer()));return{imageDataUrls:(await Promise.all([...d,h]).catch((e=>this.throw(e)))).map((e=>(e=>{const t=[];for(let n=0;n<e.length;n+=8192)t.push(String.fromCharCode.apply(null,e.subarray(n,n+8192)));return btoa(t.join(""))})(new Uint8Array(e)))),data:{height:c.height,imageOriginX:c.image_origin_x,imageOriginY:c.image_origin_y,resolutionPpm:c.resolution_ppm,width:c.width}}}throw(e){throw Error("Asset.getVrColorplans: "+e)}}class Te{constructor(e){this.attachmentRegistry=e}validateInput(e,t){return de(e.id)||this.throw(se("tagId","string",e.id)),this.attachmentRegistry.get(t.client.applicationKey,e.id)||this.throw(`${e.id} does not map to a valid asset`),e}async exec(e,t){const n=this.attachmentRegistry.get(t.client.applicationKey,e.id);return n||this.throw(`${e.id} does not map to a valid asset`),n}throw(e){throw Error("Asset.getAssetById: "+e)}}class Ee{constructor(e){this.attachmentRegistry=e}validateInput(e,t){return e}exec(){return this.attachmentRegistry.refresh()}}function ve(e,t,n,a,o,s,i){const{floorData:r,modelData:c}=s;return{getVrColorplans:function(e,t,n){const a=oe.create(n,new G(Ie,t));return e.addAsyncCommandToInterface({namespace:"Asset",name:"getVrColorplans",args:["sid"],origins:ye},a),a}(e,o,new we(r,c)),registerTexture:function(e,t,n,a){const o=oe.create(a,new G(me,t,n.loadImage));return e.addAsyncCommandToInterface({namespace:"Asset",name:"registerTexture",args:["textureId","textureSrc"]},o),o}(e,t,n,new we(a)),getAssetById:function(e,t){const n=oe.create(t,new G(Te));return e.addAsyncCommandToInterface({namespace:"Asset",name:"getAssetById",args:["id"],origins:ye},n),n}(e,new we(i)),refreshAssets:function(e,t){const n=oe.create(t,new G(Ee));return e.addAsyncCommandToInterface({namespace:"Asset",name:"refreshAssets",args:[],origins:ye},n),n}(e,new we(i))}}class Ce extends W{constructor(){super(),this.id="GET_POSE"}}const Ae=40*Math.PI/180/1e3;class Se extends W{constructor(e){super(),this.payload=e}}const De=new ne("broadcast.move");function be(e,t){return Object.values(e).includes(t)}class Oe extends W{constructor(e){super(),this.id="MOVE_DIRECTION",this.payload=e}}class Me extends W{constructor(e){super(),this.id="PAN",this.payload={x:e.x,z:e.z}}}class Ne{constructor(...e){this.subs=e}renew(){for(const e of this.subs)e.renew()}cancel(){for(const e of this.subs)e.cancel()}}function Re(e,t){e.x=t.x,e.y=t.y}function Pe(e,t){Re(e,t),e.z=t.z}class Le extends Error{constructor(e,t){var n;super(e instanceof Error?e.message:e),this.name="BaseException",t&&(this.code=t),e instanceof Error&&(this.originalError=e,(n=e)&&n instanceof Error&&n.isMock&&(this.isMock=!0))}}function xe(e,t){const n=t.length;e.length=n;for(let a=0;a<n;++a)e[a]=t[a];return e}class ke extends Le{constructor(){super("Cannot copy into an array of a different size"),this.name="ArraySizeMismatch"}}const _e=(e,t,n=1e-5)=>Math.abs(e-t)<=n,Ue=(e,t,n=1e-5)=>_e(e.x,t.x,n)&&_e(e.y,t.y,n),Ve=(e,t,n=1e-5)=>Ue(e,t,n)&&_e(e.z,t.z,n);class Fe{create(e,t,n,a){const o=()=>e.onChanged();return new Ne(t.onChanged(o),n.onChanged(o),a.onChanged(o))}}class Ge{constructor(e,t){this._data={position:{x:0,y:0,z:0},rotation:{x:0,y:0},projection:new Float32Array(16),sweep:"",mode:c.Mode.TRANSITIONING},this.THREE=e,this.viewmodeConverter=t,this.tempEuler=new e.Euler}get data(){return this._data}equals(e){const t=1e-5;return Ve(e._data.position,this._data.position,t)&&Ue(e._data.rotation,this._data.rotation,t)&&((e,t,n=1e-5)=>e.length===t.length&&!Array.prototype.some.call(e,((e,a)=>!_e(e,t[a],n))))(e._data.projection,this._data.projection,t)&&e._data.sweep===this._data.sweep&&e._data.mode===this._data.mode}update(e,t,n,a){const o=this.tempEuler.setFromQuaternion(e.pose.rotation,"YXZ"),s=t.currentSweep&&t.isSweepUnaligned(t.currentSweep);Pe(this._data.position,e.pose.position),this._data.rotation.x=this.THREE.MathUtils.radToDeg(o.x),this._data.rotation.y=this.THREE.MathUtils.radToDeg(o.y),this._data.projection=Float32Array.from(e.pose.projection.asThreeMatrix4().transpose().elements),this._data.sweep=t.currentSweepObject?a.getIdForSweep(t.currentSweepObject):"",this._data.mode=this.viewmodeConverter.toSdk(n.currentMode,!s)}copy(e){Pe(this._data.position,e.data.position),Re(this._data.rotation,e.data.rotation),function(e,t){if(e.byteLength!==t.byteLength)throw new ke;const n=t.length;for(let a=0;a<n;++a)e[a]=t[a]}(this._data.projection,e.data.projection),this._data.sweep=e.data.sweep,this._data.mode=e.data.mode}}class He extends W{constructor(e){super(),this.id="ROTATE",this.payload={xAngle:e.xAngle,yAngle:e.yAngle,zAngle:e.zAngle,rotationSpeed:e.rotationSpeed}}}function Be(e,t,n){const a=new e.Euler,o=new e.Euler;return async function(e,s,i,r){if(i.currentMode!==t.Panorama)throw Error("Camera.setRotation is only available in Panorama mode");const c=r.xAngle%(.5*Math.PI),d=r.yAngle%(2*Math.PI),l=e.pose.rotation;a.setFromQuaternion(l,"YXZ");let h=d-a.y;return Math.abs(h)>Math.PI&&(h-=2*Math.sign(h)*Math.PI),o.set(c-a.x,h,0,"YXZ"),o.y%=2*Math.PI,n(s,{xAngle:-o.y,yAngle:o.x,zAngle:0,rotationSpeed:r.rotationSpeed})}}class ze extends He{}const $e=.7,je=3,We=function(e,t,n){return Math.max(t,Math.min(e,n))};const qe=new ne("command.zoom");class Ke{constructor(e,t,n,a){this.cameraData=n,this.viewmodeData=a,this.Viewmode=e.Viewmode,this.ZoomSetCommand=e.ZoomSetCommand,this.issueCommand=t.issueCommand}validateInput(e){const t=e.zoomPct-0;if(isNaN(t))throw Error(t+" is not a valid zoom level");return t<$e&&je<t&&(e.zoomPct=We(t,$e,je),qe.warn(t,`is outside the valid zoom range of [${$e}, ${je}]`)),e}async exec(e,t){return Ze(this.Viewmode,this.viewmodeData.currentMode),await this.issueCommand(new this.ZoomSetCommand(e.zoomPct)),this.cameraData.zoom()}}class Ye{constructor(e,t,n,a){this.cameraData=n,this.viewmodeData=a,this.Viewmode=e.Viewmode,this.ZoomInCommand=e.ZoomInCommand,this.issueCommand=t.issueCommand}validateInput(e){const t=e.zoomDelta-0;if(isNaN(t))throw Error(t+" is not a valid zoom delta");return e.zoomDelta=t||0,e}async exec(e,t){return Ze(this.Viewmode,this.viewmodeData.currentMode),await this.issueCommand(new this.ZoomInCommand(e.zoomDelta)),this.cameraData.zoom()}}class Xe{constructor(e,t,n,a){this.viewmodeData=a,this.Viewmode=e.Viewmode,this.ZoomResetCommand=e.ZoomResetCommand,this.issueCommand=t.issueCommand}validateInput(e){return e}async exec(e,t){Ze(this.Viewmode,this.viewmodeData.currentMode),await this.issueCommand(new this.ZoomResetCommand)}}function Ze(e,t){if(t!==e.Panorama)throw Error("Zoom controls are currently only supported in Panorama mode")}class Qe{create(e,t){return t.onChanged((()=>e.onChanged()))}}class Je{constructor(e){this._data={level:1}}get data(){return this._data}equals(e){return this._data.level===e.data.level}copy(e){this._data.level=e.data.level}update(e,t){t.isInside()&&(this._data.level=e.zoom())}}function et(e,t){const n=new e.Euler;return function(a,o,s,i,r){const c=n.setFromQuaternion(o.pose.rotation,"YXZ"),d=s.currentSweep&&s.isSweepUnaligned(s.currentSweep);return a.position.x=o.pose.position.x,a.position.y=o.pose.position.y,a.position.z=o.pose.position.z,a.rotation.x=e.MathUtils.radToDeg(c.x),a.rotation.y=e.MathUtils.radToDeg(c.y),a.projection=Float32Array.from(o.pose.projection.asThreeMatrix4().transpose().elements),a.sweep=s.currentSweepObject?r.getIdForSweep(s.currentSweepObject):"",a.mode=t.toSdk(i.currentMode,!d),a}}class tt{constructor(){this.wasInTransition=!1,this.currentFloor=""}create(e,t,n,a,o){return new Ne(t.makeFloorChangeSubscription((()=>this.throttleCurrentFloorChanges(e,t))),a.onChanged((()=>this.throttleCameraTranstionChanges(e,a,n,o))),n.onChanged((()=>this.throttleCameraTranstionChanges(e,a,n,o))),o.onChanged((()=>this.throttleCameraTranstionChanges(e,a,n,o))))}throttleCameraTranstionChanges(e,t,n,a){const o=t.transition.active||n.transition.active;o!==this.wasInTransition&&(this.wasInTransition=o,e.onChanged())}throttleCurrentFloorChanges(e,t){const n=t.currentFloorId;n!==this.currentFloor&&(this.currentFloor=n,e.onChanged())}}class nt{constructor(e){this._data={id:void 0,sequence:void 0,name:""},this.SweepAlignmentType=e.SweepAlignmentType,this.SweepPlacementType=e.SweepPlacementType}get data(){return this._data}equals(e){const t=this.data,n=e.data;return t.id===n.id&&t.sequence===n.sequence&&t.name===n.name}copy(e){this._data.id=e.data.id,this._data.sequence=e.data.sequence,this._data.name=e.data.name}update(e,t,n,a,o){const s=e.currentFloor,i=t.currentSweep&&t.getSweep(t.currentSweep);!a.isTourActive()&&n.transition.active&&!t.transitionActive||function(){if(t.transitionActive&&t.transition.to&&t.transition.from){const e=t.getSweep(t.transition.to),n=t.getSweep(t.transition.from);return e.floorId!==n.floorId}return!1}()?(this._data.id="",this._data.sequence=void 0,this._data.name=""):i&&i.alignmentType===this.SweepAlignmentType.UNALIGNED&&i.placementType===this.SweepPlacementType.UNPLACED?(this._data.id=void 0,this._data.sequence=void 0,this._data.name=""):s?(this._data.id=o.getIdFromCwfId(s.id),this._data.sequence=s.index,this._data.name=s.name):(this._data.id="",this._data.sequence=-1,this._data.name="all")}}class at{constructor(e,t,n){this.dependencies=e,this.subscriptionFactory=t,this.dataFactory=n,this.dirty=!0,this.observers=new Set,this.currentData=n.create(...e);this.changeSubscription=this.subscriptionFactory.create(new class{constructor(e){this.collection=e}onChanged(){this.collection.dirty=!0;for(const e of this.collection.observers)e.onChanged()}}(this),...this.dependencies),this.changeSubscription.cancel()}static async create(e,t,n){const a=await e.getDependencies();return new at(a,t,n)}getData(){return this.currentData.data}subscribe(e){const t=this.dependencies;const n=new class{constructor(e){this.sdkCollection=e,this.needsUpdate=!0,this.diffRecord={added:new Map,removed:new Map,updated:new Map},this.dataView=e.dataFactory.create(...t)}get dirty(){return this.needsUpdate}readDiff(){const{added:t,removed:n,updated:a}=this.diffRecord;if(t.clear(),n.clear(),a.clear(),!this.dirty)return this.diffRecord;this.sdkCollection.update(e);const o=this.sdkCollection.currentData;for(const e in o.data)this.dataView.data.hasOwnProperty(e)?this.dataView.isItemEqual(o,e)||a.set(e,o.data[e]):t.set(e,o.data[e]);for(const e in this.dataView.data)o.data.hasOwnProperty(e)||n.set(e,this.dataView.data[e]);return this.dataView.update(e),this.needsUpdate=!1,this.diffRecord}onChanged(){this.needsUpdate=!0}renew(){this.sdkCollection.addObserver(this)}cancel(){this.sdkCollection.removeObserver(this)}}(this);return this.addObserver(n),n}update(e){this.dirty&&(this.currentData.update(e),this.dirty=!1)}addObserver(e){0===this.observers.size&&(this.changeSubscription.renew(),this.dirty=!0),this.observers.add(e)}removeObserver(e){this.observers.delete(e),0===this.observers.size&&(this.changeSubscription.cancel(),this.currentData.clear())}}class ot{create(e,t){return t.onChanged((()=>e.onChanged()))}}class st{constructor(e,t){this.floorData=e,this.floorIdMap=t,this._data={}}get data(){return this._data}isItemEqual(e,t){const n=this.data[t],a=e.data[t];return n.id===a.id&&n.name===a.name&&n.sequence===a.sequence}update(){for(const e of this.floorData.getCollection()){const t=this.floorIdMap.getIdFromCwfId(e.id),n=this._data[t]||{};n.id=t,n.sequence=e.index,n.name=e.name,this._data[t]=n}for(const e in this.data){this.floorIdMap.getFloorForId(e)||delete this._data[e]}}clear(){this._data={}}}class it extends W{constructor(){super(...arguments),this.id="GET_FLOORS_DATA"}}async function rt(e){try{const{currentFloor:t,totalFloors:n}=e,a=e.getFloorNames();return{currentFloor:t?t.index:-1,floorNames:a,totalFloors:n}}catch(e){throw Error("no floors currently loaded")}}class ct extends W{constructor(e){super(),this.id="MOVE_TO_FLOOR",this.payload=e}}class dt{constructor(e,t,n){this.floorsViewData=n,this.issueCommand=t.issueCommand,this.ShowAllFloorsCommand=e.ShowAllFloorsCommand}validateInput(e){return e}async exec(e,t){try{await this.issueCommand(new this.ShowAllFloorsCommand({moveCamera:!0}));const e=this.floorsViewData.currentFloor;return e?e.index:-1}catch(e){throw Error("Could not show all floors")}}}class lt{constructor(e){this.FloorsData=e}validateInput(e){return{invert:!!(null==e?void 0:e.invert)||!1}}async exec(e,t){return this.FloorsData.getFloorIdMap(e.invert)}}class ht extends W{constructor(){super(...arguments),this.id="LABEL_GET"}}function ut(e,t){return function(n,a,o,s){const i=o.getCollection(),r=[],c=new e.Vector2;for(const o of i){t(n,o.position,c);const{id:i,index:d}=a.getFloor(o.floorId),l=s.getIdFromCwfId(i),h=B(o);r.push(Object.assign(Object.assign({},h),{position:(new e.Vector3).copy(o.position),screenPosition:c,floor:d,floorInfo:{id:l,sequence:d}}))}return r}}class pt{create(e,t){return t.onChanged((()=>e.onChanged()))}}class mt{constructor(e,t,n){this.labelData=e,this.floorsData=t,this.floorIdMap=n,this._data={}}get data(){return this._data}isItemEqual(e,t){return!z(this.data[t],e.data[t])}update(){var e;for(const t of this.labelData.getCollection()){const n=null!==(e=this.data[t.sid])&&void 0!==e?e:{};n.position=t.position,n.sid=t.sid,n.text=t.text,n.visible=t.visible;const a=this.floorsData.getFloor(t.floorId);n.floorInfo={id:this.floorIdMap.getIdFromCwfId(a.id),sequence:a.index},this.data[n.sid]=n}for(const e in this.data){this.labelData.getLabel(e)||delete this.data[e]}}clear(){this._data={}}}class gt{constructor(e){this.deepLinker=e}validateInput(e){return e}async exec(e,t){const n=this.deepLinker.creator.createLink();return decodeURIComponent(n.href)}}class wt{constructor(e){this.deepLinker=e}validateInput(e){return e}async exec(e,t){const n=this.deepLinker.creator.createDeepLink();return decodeURIComponent(n.href)}}function ft(e){return!!e&&"object"==typeof e}function yt(e){return"boolean"==typeof e}function It(e,t,n,a){switch(e){case i.OpenPolicy.DEFAULT:n.handler.resetPolicy(t);break;case i.OpenPolicy.NEW_WINDOW:n.handler.setPolicy(t,n.handler.HandlingPolicy.NEW_WINDOW);break;case i.OpenPolicy.SAME_FRAME:n.handler.setPolicy(t,n.handler.HandlingPolicy.IN_FRAME);break;case i.OpenPolicy.CURRENT_WINDOW:n.handler.setPolicy(t,n.handler.HandlingPolicy.CUSTOM,(e=>{a.sendPrivateMessage("open link",{href:e})}))}}class Tt{constructor(e,t,n){this.sdk=e,this.deepLinker=t,this.api=n}validateInput(e){if(!be(i.OpenPolicy,e.policy))throw Error(`Link.setModelLinkPolicy: ${e.policy} is not a valid Link.OpenPolicy`);if(!de(e.baseHref))throw Error("Link.setModelLinkPolicy: could not determine window.location");if(be(i.DestinationPolicy,e.destinationPolicy))return{policy:e.policy,baseHref:e.baseHref,destinationPolicy:e.destinationPolicy};const t=ft(e.options)?e.options.templateHref:void 0;if(void 0!==t&&!de(t)||de(t)&&new URL(e.baseHref).origin!==new URL(t).origin)throw Error("Link.setModelLinkPolicy: setting the policy to a different domain is not supported");return{policy:e.policy,baseHref:e.baseHref,options:{templateHref:t}}}async exec(e,t){const n=Et(e,this.api);e.policy!==i.OpenPolicy.NEW_WINDOW&&e.policy!==i.OpenPolicy.CURRENT_WINDOW||!n?It(e.policy,this.deepLinker.LinkType.MODEL,this.deepLinker,this.sdk):this.deepLinker.handler.setPolicy(this.deepLinker.LinkType.MODEL,this.deepLinker.handler.HandlingPolicy.CUSTOM,(t=>{const a=new URLSearchParams(window.location.search),o=new URL(t).searchParams;a.set("m",o.get("m")||o.get("model")||"");const s=new URL(St(n,a));s.searchParams.set("m",o.get("m")||o.get("model")||""),this.sdk.sendPrivateMessage("open link",{newWindow:e.policy===i.OpenPolicy.NEW_WINDOW,href:decodeURIComponent(s.href)})}))}}function Et(e,t){var n;if(function(e){return!e.destinationPolicy}(e))return null===(n=e.options)||void 0===n?void 0:n.templateHref;const a=t.getApi().baseUrl;return{[i.DestinationPolicy.DEFAULT]:"",[i.DestinationPolicy.MATTERPORT]:a+"/show/"}[e.destinationPolicy]}class vt{constructor(e,t,n){this.sdk=e,this.deepLinker=t,this.api=n}validateInput(e){if(!be(i.OpenPolicy,e.policy))throw Error(`${e.policy} is not a valid Link.OpenPolicy`);if(!de(e.baseHref))throw Error("Link.setNavigationLinkPolicy: could not determine window.location");if(be(i.DestinationPolicy,e.destinationPolicy))return{policy:e.policy,baseHref:e.baseHref,destinationPolicy:e.destinationPolicy};const t=ft(e.options)?e.options.templateHref:void 0;if(void 0!==t&&!de(t)||de(t)&&new URL(e.baseHref).origin!==new URL(t).origin)throw Error("Link.setNavigationLinkPolicy: setting the policy to a different domain is not supported");return{policy:e.policy,baseHref:e.baseHref,options:{templateHref:t}}}async exec(e,t){const n=Et(e,this.api);e.policy!==i.OpenPolicy.NEW_WINDOW&&e.policy!==i.OpenPolicy.CURRENT_WINDOW||!n?It(e.policy,this.deepLinker.LinkType.NAVIGATION,this.deepLinker,this.sdk):this.deepLinker.handler.setPolicy(this.deepLinker.LinkType.NAVIGATION,this.deepLinker.handler.HandlingPolicy.CUSTOM,(t=>{const a=new URLSearchParams(window.location.search),o=new URLSearchParams(t),s=new URL(St(n,a));!function(e,t){const[n,a,o,s,i,r,c]=[t.get("sm"),t.get("sp"),t.get("sq"),t.get("sr"),t.get("ss"),t.get("sz"),t.get("start")];de(n)&&e.set("sm",n);de(a)&&e.set("sp",a);de(o)&&e.set("sq",o);de(s)&&e.set("sr",s);de(i)&&e.set("ss",i);de(r)&&e.set("sz",r);de(c)&&e.set("start",c)}(s.searchParams,o),s.searchParams.set("m",o.get("m")||o.get("model")||""),this.sdk.sendPrivateMessage("open link",{newWindow:e.policy===i.OpenPolicy.NEW_WINDOW,href:decodeURIComponent(s.href)})}))}}class Ct{constructor(e,t){this.sdk=e,this.deepLinker=t}validateInput(e){if(!be(i.OpenPolicy,e.policy))throw Error(`${e.policy} is not a valid Link.OpenPolicy`);return{policy:e.policy}}async exec(e,t){It(e.policy,this.deepLinker.LinkType.SAME_ORIGIN,this.deepLinker,this.sdk)}}class At{constructor(e,t){this.sdk=e,this.deepLinker=t}validateInput(e){const{openInNewWindow:t}=e;if(!yt(t))throw Error(`${e.openInNewWindow} is expected to strictly be a boolean to avoid confusion about truth-y and false-y values.`);return{openInNewWindow:t}}async exec(e,t){e.openInNewWindow?this.deepLinker.handler.setPolicy(this.deepLinker.LinkType.EXTERNAL,this.deepLinker.handler.HandlingPolicy.NEW_WINDOW):this.deepLinker.handler.setPolicy(this.deepLinker.LinkType.EXTERNAL,this.deepLinker.handler.HandlingPolicy.CUSTOM,(e=>{this.sdk.sendPrivateMessage("open link",{href:e})}))}}function St(e,t){const n=/\$\{([a-zA-Z]+)\}/,a=new URL(e);let o,s=decodeURI(a.pathname);for(;o=s.match(n);){const e=t.get(o[1]);s=s.replace(o[0],`${e}`)}for(a.pathname=s;o=a.search.match(n);){const e=t.get(o[1]);a.search=null!==e||""===e?a.search.replace(o[0],`${o[1]}=${e}`):a.search.replace(o[0],`${o[1]}`)}return a.href}function Dt(e,t){return!!Array.isArray(e)&&e.every(t)}class bt{constructor(e){this.deepLinks=e}validateInput(e){const{policy:t,baseHref:n}=e;if(!be(i.CreationPolicy,t))throw Error(`Link.setShareLinkPolicy: ${e.policy} is not a valid Link.ShareLinkPolicy`);if(!de(n))throw Error("Link.setShareLinkPolicy: Could not determine window.location");const a=ft(e.options)&&e.options.includeParams?e.options.includeParams:[];if(!Dt(a,de))throw Error("Link.setShareLinkPolicy: 'includeParams' expects an array of strings");return{policy:t,baseHref:this.validateBaseHref(t,n),options:{includeParams:a}}}validateBaseHref(e,t){const n=/^https:\/\/([a-z0-9\-]*\.)*(matterport\.com|matterportvr\.cn)$/,a=/^https:\/\/static\.(matterport\.com|matterportvr\.cn)$/;return{[i.CreationPolicy.MATTERPORT]:function(){if(window.location.origin.match(n)&&!window.location.origin.match(a))return window.location.href;const e=decodeURIComponent(new URLSearchParams(window.location.search.toLowerCase()).get("apihost")||"");return e.match(n)?e+"/show/":"https://my.matterport.com/show/"},[i.CreationPolicy.REFERRER]:function(){return t},[i.CreationPolicy.WINDOW]:function(){return window.location.href}}[e]()}async exec(e,t){this.deepLinks.creator.setDefaultBaseHref(e.baseHref,e.options.includeParams)}}class Ot{constructor(e,t){this.toSdkMediaMap={[e.PHOTO]:r.MediaType.PHOTO,[e.VIDEO]:r.MediaType.VIDEO,[e.RICH]:r.MediaType.RICH},this.toSdkMediaFromChunk={[t.error]:r.MediaType.NONE,[t.link]:r.MediaType.NONE,[t.none]:r.MediaType.NONE,[t.photo]:r.MediaType.PHOTO,[t.rich]:r.MediaType.RICH,[t.text]:r.MediaType.NONE,[t.video]:r.MediaType.VIDEO}}toSdkMedia(e){return this.toSdkMediaMap[e]}fromTagChunkType(e){return this.toSdkMediaFromChunk[e]}}class Mt{constructor(e){this.toSdkChunkTypeMap={[e.error]:r.DescriptionChunkType.NONE,[e.link]:r.DescriptionChunkType.LINK,[e.none]:r.DescriptionChunkType.NONE,[e.photo]:r.DescriptionChunkType.NONE,[e.rich]:r.DescriptionChunkType.NONE,[e.text]:r.DescriptionChunkType.TEXT,[e.video]:r.DescriptionChunkType.NONE}}toSdkChunkType(e){return this.toSdkChunkTypeMap[e]}}class Nt{constructor(e){this.toSdkLinkTypeMap={[e.NAVIGATION]:r.LinkType.NAVIGATION,[e.MODEL]:r.LinkType.MODEL,[e.EXT_LINK]:r.LinkType.EXT_LINK}}toSdkLinkType(e){return this.toSdkLinkTypeMap[e]}}var Rt,Pt,Lt,xt,kt,_t,Ut,Vt,Ft,Gt,Ht,Bt,zt,$t,jt,Wt,qt,Kt,Yt,Xt,Zt,Qt,Jt,en,tn,nn,an,on,sn,rn,cn,dn,ln,hn,un,pn,mn,gn,wn,fn,yn,In,Tn,En,vn,Cn,An,Sn,Dn,bn,On,Mn,Nn,Rn,Pn,Ln,xn,kn,_n,Un,Vn,Fn,Gn,Hn,Bn,zn,$n,jn,Wn,qn,Kn,Yn,Xn,Zn,Qn,Jn,ea,ta,na,aa,oa,sa,ia,ra,ca,da,la,ha,ua,pa,ma,ga,wa,fa,ya,Ia,Ta,Ea,va;!function(e){(Rt||(Rt={})).create=async function(e,n){const a=await e.getDependencies();return new t(n.create(...a))};class t{constructor(e){this.executor=e}validateInput(e,t){return this.executor.validateInput(e,t)}exec(e,t){const n=this.executor.validateInput(e,t);return this.executor.exec(n,t)}}}();class Ca{constructor(){this.observers=new Set}observe(e){this.observers.add(e);const t=this;return{renew(){t.observers.add(e)},cancel(){t.removeObserver(e)}}}removeObserver(e){this.observers.delete(e)}notify(){for(const e of this.observers)e.notify()}}!function(e){e.ROLE="ROLE",e.TYPE="TYPE",e.USER_EMAIL="USER_EMAIL"}(Pt||(Pt={})),function(e){e.EMPTY="empty",e.PARSED="parsed",e.RAW="raw",e.VERIFIED="verified"}(Lt||(Lt={})),function(e){e.FULL="full",e.NONE="none",e.PARTIAL="partial"}(xt||(xt={})),function(e){e.FOREVER="FOREVER",e.LAST_7_DAYS="LAST_7_DAYS",e.LAST_30_DAYS="LAST_30_DAYS"}(kt||(kt={})),function(e){e.COMPLETED="COMPLETED",e.FAILED="FAILED",e.OPENED="OPENED",e.PENDING="PENDING"}(_t||(_t={})),function(e){e.IMPRESSION="impression",e.MODELLOADED="modelLoaded"}(Ut||(Ut={})),function(e){e.LAST7DAYS="last7Days",e.LAST30DAYS="last30Days",e.LIFETIME="lifetime"}(Vt||(Vt={})),function(e){e.CREATED="created",e.IMPRESSIONS="impressions",e.NAME="name",e.UNIQUE_VISITORS="unique_visitors",e.VIEWS="views"}(Ft||(Ft={})),function(e){e.LZMA="lzma",e.NONE="none"}(Gt||(Gt={})),function(e){e.AVAILABLE="available",e.LOCKED="locked",e.UNAVAILABLE="unavailable"}(Ht||(Ht={})),function(e){e.EXTERNAL="external",e.UPLOAD="upload"}(Bt||(Bt={})),function(e){e.IMAGE="image",e.PDF="pdf",e.RICH="rich",e.VIDEO="video",e.ZIP="zip"}(zt||(zt={})),function(e){e.PROVISIONED="provisioned",e.UPLOADED="uploaded"}($t||($t={})),function(e){e.LOCKED="locked",e.UNAVAILABLE="unavailable",e.UNLOCKED="unlocked"}(jt||(jt={})),function(e){e.COMPLETED="completed",e.FAILED="failed",e.INPROGRESS="inprogress",e.ORDERED="ordered",e.PLACED="placed",e.PREVIEWABLE="previewable"}(Wt||(Wt={})),function(e){e.DOLLHOUSE="dollhouse",e.FLOORPLAN="floorplan",e.OUTDOOR="outdoor",e.PANORAMA="panorama",e.TRANSITION="transition",e.UNKNOWN="unknown"}(qt||(qt={})),function(e){e.WALL="wall",e.WINDOW="window"}(Kt||(Kt={})),function(e){e.HANDHELD="handheld",e.OTHER="other",e.PRO="pro",e.SPHERICAL="spherical"}(Yt||(Yt={})),function(e){e.DUPLICATE="duplicate",e.FAIL="fail",e.REPLACE="replace",e.SKIP="skip"}(Xt||(Xt={})),function(e){e.CREATED="created",e.MODIFIED="modified"}(Zt||(Zt={})),function(e){e.FAST="fast",e.NORMAL="normal",e.URGENT="urgent"}(Qt||(Qt={})),function(e){e.KILOMETERS="kilometers",e.MILES="miles"}(Jt||(Jt={})),function(e){e.INVISIBLE="invisible",e.WALL="wall"}(en||(en={})),function(e){e.CSV="CSV"}(tn||(tn={})),function(e){e.MATTERTAGS="Mattertags",e.NOTECOMMENTS="NoteComments",e.NOTES="Notes"}(nn||(nn={})),function(e){e.BLOCKED="blocked",e.DISABLED="disabled",e.GATED="gated",e.UNLOCKED="unlocked"}(an||(an={})),function(e){e.MAY="may",e.MUST="must",e.MUST_NOT="must_not"}(on||(on={})),function(e){e.ALPHA="alpha",e.COLORED_ROOMS="colored_rooms",e.MEASUREMENTS="measurements",e.PHOTOGRAMY="photogramy",e.SCHEMATIC="schematic"}(sn||(sn={})),function(e){e.ADMIN="Admin",e.FOLDERCREATOR="FolderCreator",e.FOLDERSHARES="FolderShares",e.USERGROUPFOLDERSHARES="UserGroupFolderShares"}(rn||(rn={})),function(e){e.USEREMAIL="UserEmail",e.USERID="UserId"}(cn||(cn={})),function(e){e.ALL="all",e.ID="id",e.NAME="name"}(dn||(dn={})),function(e){e.CREATED="created",e.MODIFIED="modified",e.NAME="name"}(ln||(ln={})),function(e){e.COMPLETE="COMPLETE",e.ERROR="ERROR",e.PENDING="PENDING"}(hn||(hn={})),function(e){e.REEL="reel",e.STORY="story"}(un||(un={})),function(e){e.CANCELLED="cancelled",e.COMPLETED="completed",e.CREATED="created",e.FAILED="failed",e.INPROGRESS="inprogress"}(pn||(pn={})),function(e){e.ANCHORLOCATION="AnchorLocation",e.HIGHLIGHTREEL="HighlightReel",e.LABEL="Label",e.MATTERTAG="Mattertag",e.MEASUREMENTPATH="MeasurementPath",e.MODELFLOOR="ModelFloor",e.MODELROOM="ModelRoom",e.NOTE="Note",e.OBJECTANNOTATION="ObjectAnnotation",e.ORDEREDLIST="OrderedList",e.PANORAMICIMAGELOCATION="PanoramicImageLocation",e.PHOTO="Photo"}(mn||(mn={})),function(e){e.SHOWCASE="showcase"}(gn||(gn={})),function(e){e.PHOTO="photo",e.RICH="rich",e.VIDEO="video"}(wn||(wn={})),function(e){e.LINETYPE_2D="linetype_2D",e.LINETYPE_3D="linetype_3D"}(fn||(fn={})),function(e){e.DISABLED="disabled",e.MEASURE="measure",e.MEASUREANDVIEW="measureAndView"}(yn||(yn={})),function(e){e.FLOORS="floors",e.HIGHLIGHTREEL="highlightReel",e.IMAGE="image",e.LABELS="labels",e.MATTERTAGS="mattertags",e.MEASUREMENTPATHS="measurementPaths",e.ORDEREDLISTS="orderedLists",e.PLAYEROPTIONS="playerOptions",e.SWEEPS="sweeps"}(In||(In={})),function(e){e.FULL="full",e.PUBLIC="public",e.VIEWER="viewer"}(Tn||(Tn={})),function(e){e.ADMIN="Admin",e.MODELCREATOR="ModelCreator",e.MODELSHARES="ModelShares",e.USERGROUPMODELSHARES="UserGroupModelShares"}(En||(En={})),function(e){e.PASSWORD="password",e.PRIVATE="private",e.PUBLIC="public",e.UNLISTED="unlisted"}(vn||(vn={})),function(e){e.ACTIVE="active",e.ARCHIVED="archived",e.FLAGGED="flagged",e.PENDING="pending"}(Cn||(Cn={})),function(e){e.BLACK="black",e.GREY="grey",e.WHITE="white"}(An||(An={})),function(e){e.DEFAULT="default",e.MLS="mls",e.UNBRANDED="unbranded"}(Sn||(Sn={})),function(e){e.BUNDLE_COMPLETED="bundle_completed",e.BUNDLE_REQUESTED="bundle_requested",e.DELETED="deleted",e.INSERTED="inserted",e.PROCESSED="processed",e.REPLACED="replaced",e.UPDATED="updated",e.UPLOADED="uploaded"}(Dn||(Dn={})),function(e){e.ACTIVE="active",e.ARCHIVED="archived"}(bn||(bn={})),function(e){e.ID="id",e.INTERNALID="internalId",e.MLSID="mlsId"}(On||(On={})),function(e){e.DEFAULT="default",e.VR="vr"}(Mn||(Mn={})),function(e){e.COMPLETED="completed",e.FAILED="failed",e.PROCESSING="processing",e.STAGING="staging"}(Nn||(Nn={})),function(e){e.USEREMAIL="UserEmail",e.USERID="UserId"}(Rn||(Rn={})),function(e){e.ADDRESS="address",e.CAMERA="camera",e.CAMERAMANUFACTURER="cameraManufacturer",e.CAMERAMODEL="cameraModel",e.EXTENSIONTAG="extensiontag",e.GEODISTANCE="geoDistance",e.GEOREGION="geoRegion",e.ID="id",e.NAME="name",e.STANDARD="standard",e.SUMMARY="summary"}(Pn||(Pn={})),function(e){e.CREATED="created",e.INTERNALID="internalId",e.LAST7IMPRESSIONSEVENTCOUNT="last7ImpressionsEventCount",e.LAST7MODELSLOADEDEVENTCOUNT="last7ModelsLoadedEventCount",e.LAST7MODELSLOADEDUSERCOUNT="last7ModelsLoadedUserCount",e.LAST30IMPRESSIONSEVENTCOUNT="last30ImpressionsEventCount",e.LAST30MODELSLOADEDEVENTCOUNT="last30ModelsLoadedEventCount",e.LAST30MODELSLOADEDUSERCOUNT="last30ModelsLoadedUserCount",e.LIFETIMEIMPRESSIONSEVENTCOUNT="lifetimeImpressionsEventCount",e.LIFETIMEMODELSLOADEDEVENTCOUNT="lifetimeModelsLoadedEventCount",e.LIFETIMEMODELSLOADEDUSERCOUNT="lifetimeModelsLoadedUserCount",e.MLSID="mlsId",e.MODIFIED="modified",e.NAME="name",e.POSTALCODE="postalCode",e.SCORE="score"}(Ln||(Ln={})),function(e){e.COPY="copy",e.DEMO="demo",e.PROCESSING="processing",e.TRANSFER="transfer",e.UNKNOWN="unknown"}(xn||(xn={})),function(e){e.ACTIVATING="activating",e.ACTIVATION_PENDING="activation_pending",e.ACTIVE="active",e.FAILED="failed",e.INACTIVATING="inactivating",e.INACTIVATION_PENDING="inactivation_pending",e.INACTIVE="inactive",e.PENDING="pending",e.PROCESSING="processing",e.STAGING="staging"}(kn||(kn={})),function(e){e.ACTIVE="active",e.INACTIVE="inactive"}(_n||(_n={})),function(e){e.COPY="copy",e.DEMO="demo",e.ORIGINAL="original"}(Un||(Un={})),function(e){e.PRIVATE="private",e.PUBLIC="public"}(Vn||(Vn={})),function(e){e.SERIAL="serial"}(Fn||(Fn={})),function(e){e.DOORWAY="doorway",e.OPENING="opening"}(Gn||(Gn={})),function(e){e.AUTO="auto",e.LEFT="left",e.RIGHT="right"}(Hn||(Hn={})),function(e){e.AUTO="auto",e.MANUAL="manual",e.UNPLACED="unplaced"}(Bn||(Bn={})),function(e){e.UPLOAD="upload",e.VISION="vision"}(zn||(zn={})),function(e){e.CYLINDRICAL="cylindrical",e.EQUIRECTANGULAR="equirectangular",e.SKYBOX="skybox"}($n||($n={})),function(e){e.COMMENT="comment",e.MATTERTAG="mattertag"}(jn||(jn={})),function(e){e.FOLDER="folder",e.GRANT="grant",e.MODEL="model",e.ORGANIZATION="organization",e.USAGE="usage",e.USER="user"}(Wn||(Wn={})),function(e){e.SNAPSHOT="snapshot",e.TOUR="tour"}(qn||(qn={})),function(e){e.USER="user",e.VISION="vision"}(Kn||(Kn={})),function(e){e.ICON="icon",e.ORIGINAL="original",e.PRESENTATION="presentation",e.PREVIEW="preview",e.THUMBNAIL="thumbnail",e.WEB="web"}(Yn||(Yn={})),function(e){e.EQUIRECTANGULAR="equirectangular",e.PHOTO2D="photo2D"}(Xn||(Xn={})),function(e){e.AVAILABILITY="availability",e.FLAG="flag",e.QUOTA="quota",e.VALUE="value"}(Zn||(Zn={})),function(e){e.COMPLETE="complete",e.FAILED="failed",e.INPROGRESS="inprogress",e.STAGING="staging"}(Qn||(Qn={})),function(e){e.OPEN="open",e.RESOLVED="resolved"}(Jn||(Jn={})),function(e){e.BOUNDARYEDGE="boundaryEdge",e.BOUNDARYVERTEX="boundaryVertex",e.ROOM="room"}(ea||(ea={})),function(e){e.ALIGNED="aligned",e.FAILED="failed",e.UNALIGNED="unaligned"}(ta||(ta={})),function(e){e.ANCHOR="anchor",e.ID="id",e.LOCATION="location"}(na||(na={})),function(e){e.FACE_BLURRING="face_blurring",e.FIDUCIAL_DETECTION="fiducial_detection",e.FIDUCIAL_REMOVAL="fiducial_removal"}(aa||(aa={})),function(e){e.ADDRESS="ADDRESS",e.ALL="ALL",e.CREATED_BY="CREATED_BY",e.INTERNAL_ID="INTERNAL_ID",e.MLS_ID="MLS_ID",e.MLS_NAME="MLS_NAME"}(oa||(oa={})),function(e){e.DEMO="demo",e.DISCOVERABLE="discoverable",e.FLAGGED="flagged",e.INACTIVE="inactive"}(sa||(sa={})),function(e){e.DEFAULT="default",e.DISABLED="disabled",e.ENABLED="enabled"}(ia||(ia={})),function(e){e.FOLDER="Folder",e.MODEL="Model",e.ORGANIZATION="Organization"}(ra||(ra={})),function(e){e.USER="USER",e.USER_GROUP="USER_GROUP"}(ca||(ca={})),function(e){e.ASC="asc",e.DESC="desc"}(da||(da={})),function(e){e.ACCEPTED="accepted",e.DISMISSED="dismissed",e.REJECTED="rejected",e.UNREVIEWED="unreviewed"}(la||(la={})),function(e){e.HIGH="high",e.LOW="low"}(ha||(ha={})),function(e){e.AFTERNOON="afternoon",e.DAWN="dawn",e.DUSK="dusk",e.MORNING="morning",e.NIGHT="night"}(ua||(ua={})),function(e){e.FADE_TO_BLACK="fade_to_black",e.INSTANT="instant",e.INTERPOLATE="interpolate"}(pa||(pa={})),function(e){e.IMPERIAL="imperial",e.METRIC="metric"}(ma||(ma={})),function(e){e.CREATED="created",e.EMAIL="email",e.FIRST_NAME="first_name",e.LAST_NAME="last_name",e.MODIFIED="modified"}(ga||(ga={})),function(e){e.DOLLHOUSE="dollhouse",e.FLOORPLAN="floorplan",e.MESH="mesh",e.PANORAMA="panorama"}(wa||(wa={})),function(e){e.BASIC="basic",e.NONE="none",e.TOKEN="token"}(fa||(fa={})),function(e){e.CANCELED="Canceled",e.COMPLETED="Completed",e.CONTINUEDASNEW="ContinuedAsNew",e.FAILED="Failed",e.RUNNING="Running",e.TERMINATED="Terminated",e.TIMEDOUT="TimedOut",e.UNRECOGNIZED="Unrecognized",e.UNSPECIFIED="Unspecified"}(ya||(ya={})),function(e){e.FILE_TOO_LARGE="oversize",e.EMPTY_FILE="empty",e.OVER_QUOTA="overQuota",e.UPLOAD_FAILED="failed",e.PERMISSION_DENIED="permission"}(Ia||(Ia={})),function(e){e.EMBED_FAIL="embedFail",e.EMBED_SUCCESS="success"}(Ta||(Ta={})),function(e){e.IMAGE="image",e.PDF="pdf",e.VIDEO="video",e.RICH="rich",e.ZIP="zip",e.TEXT="text",e.AUDIO="audio",e.MODEL="model",e.APPLICATION="application"}(Ea||(Ea={})),function(e){e.EXTERNAL="external",e.UPLOAD="upload",e.SANDBOX="sandbox"}(va||(va={}));const Aa=new ne("expiring-resource"),Sa=new WeakMap;class Da{constructor(e,t){this.value=e,this.validUntil=t}refreshFrom(e){if(!e)return;this.value=e.value,this.validUntil=e.validUntil;const t=Sa.get(this);t&&(t.resolve(),Sa.delete(this))}async get(){if(this.validUntil&&this.onStale){const e=Date.now();if(e+6e4+1e4>this.validUntil.getTime()){let t=Sa.get(this);t||(t=new he,Sa.set(this,t),this.onStale()),e+6e4+1e3>this.validUntil.getTime()&&(Aa.info("Stale resource, waiting for refresh"),await t.nativePromise(),Aa.info("Refreshed resource"))}}return this.value}getCurrentValue(){return this.value}}function ba(e=11,t="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"){let n="";const a=window.crypto||window.msCrypto;let o;o=a?a.getRandomValues(new Uint8Array(e)):new Uint8Array(e).map((()=>255*Math.random()));for(let a=0;a<e;a++)n+=t.charAt(o[a]%t.length);return n}var Oa,Ma;!function(e){e.Behance="behance",e.DailyMotion="dailymotion",e.FaceBook="facebook",e.Flickr="flickr",e.Giphy="giphy",e.GoogleMaps="google maps",e.Instagram="instagram",e.LinkedIn="linkedin",e.Matterport="matterport",e.MixCloud="mixcloud",e.Pinterest="pinterest",e.Reddit="reddit",e.SoundCloud="soundcloud",e.SketchFab="sketchfab",e.Spotify="spotify",e.Tenor="tenor",e.TikTok="tiktok",e.Twitch="twitch",e.Twitter="twitter",e.Tumblr="tumblr",e.Vimeo="vimeo",e.YouTube="youtube"}(Oa||(Oa={})),function(e){e.PHOTO="photo",e.VIDEO="video",e.LINK="link",e.RICH="rich"}(Ma||(Ma={}));Error;Oa.GoogleMaps;Oa.Behance,Oa.FaceBook,Oa.GoogleMaps,Oa.Instagram,Oa.LinkedIn,Oa.MixCloud,Oa.Pinterest,Oa.Reddit,Oa.TikTok,Oa.Twitter,Oa.Twitch;var Na;!function(e){e.IMAGE="tag.attachment.image",e.RICH="tag.attachment.rich",e.VIDEO="tag.attachment.video"}(Na||(Na={}));class Ra{constructor(e,t,n,a){this.attachmentsModule=t,this.pluginConfigData=a,this.globalAttachments=new Map,this.perClientAttachments=new Map,this.clientAttachments=new Map,this.clientIds=new Set,this.attachmentDescriptors=new Map,this.observable=new Ca,this.oEmbed=n;for(const t of e.getTagList()){const n=e.getTag(t);for(const e of n.fileAttachments)this.globalAttachments.set(e.id,e),this.autoRefreshExpiringAttachment(e);for(const e of n.externalAttachments)this.globalAttachments.set(e.id,e),this.setDescriptorUrl(e,e.src)}null==a||a.getMdsResult().then((e=>e.flatMap((e=>e.attachments)))).then((e=>{e.forEach((e=>{this.globalAttachments.set(e.id,e),this.autoRefreshExpiringAttachment(e)}))}))}async refresh(){const e=await this.attachmentsModule.getAllAttachments();for(const t of e)this.globalAttachments.has(t.id)||(this.globalAttachments.set(t.id,t),this.autoRefreshExpiringAttachment(t));if(this.pluginConfigData){(await this.pluginConfigData.getMdsResult()).flatMap((e=>e.attachments)).forEach((e=>{this.globalAttachments.set(e.id,e),this.autoRefreshExpiringAttachment(e)}))}}get(e,t){return this.globalAttachments.get(t)||this.clientAttachments.get(t)}add(e,t){const n=this.createIAttachment({src:t.src,type:t.type});return this.clientAttachments.set(n.id,n),this.setDescriptorUrl(n,t.src),this.clientIds.add(n.id),this.observable.notify(),n.id}async addBySrc(e,t){const n=await this.createOEmbedIAttachment(t);return this.clientAttachments.set(n.id,n),this.setDescriptorUrl(n,t),this.clientIds.add(n.id),this.observable.notify(),n.id}addSandbox(e,t){const n=this.getUniqueId(),a=t.name||"sandbox-"+n,o={id:n,src:a,srcDoc:t.srcDoc,onLoad:t.sandboxLoadedHandler,category:va.SANDBOX,mediaType:Ea.RICH,parentType:jn.MATTERTAG,thumbnailUrl:new Da("",null),url:new Da("",null),created:new Date,height:t.size.h,width:t.size.w};return this.clientAttachments.set(n,o),this.setDescriptorUrl(o,a),this.clientIds.add(n),this.observable.notify(),[n,o]}has(e){return this.globalAttachments.has(e)||this.clientIds.has(e)}*[Symbol.iterator](e){for(const e of this.globalAttachments)yield e;for(const e of this.clientAttachments)yield e;if(e){const t=this.perClientAttachments.get(e)||[];for(const e of t)yield e}}*descriptors(){for(const e of this.attachmentDescriptors)yield e}onChanged(e){return this.observable.observe(e)}getUniqueId(){const e=ba(10);return this.globalAttachments.has(e)||this.clientIds.has(e)?this.getUniqueId():e}async autoRefreshExpiringAttachment(e){const t=e.url,n=async()=>{if(t.validUntil){const a=t.validUntil.getTime()-Date.now(),o=a<=0;if(o){const n=await t.get();this.setDescriptorUrl(e,n),this.observable.notify()}setTimeout(n,o?0:a)}},a=await t.get();this.setDescriptorUrl(e,a),n(),this.observable.notify()}setDescriptorUrl(e,t){this.attachmentDescriptors.set(e.id,{id:e.id,src:t,type:this.getAttachmentType(e.mediaType)})}getAttachmentType(e){return{[Ea.APPLICATION]:m.AttachmentType.APPLICATION,[Ea.AUDIO]:m.AttachmentType.AUDIO,[Ea.IMAGE]:m.AttachmentType.IMAGE,[Ea.MODEL]:m.AttachmentType.MODEL,[Ea.PDF]:m.AttachmentType.PDF,[Ea.RICH]:m.AttachmentType.RICH,[Ea.TEXT]:m.AttachmentType.TEXT,[Ea.VIDEO]:m.AttachmentType.VIDEO,[Ea.ZIP]:m.AttachmentType.ZIP}[e]}createIAttachment(e){const t={[Na.IMAGE]:Ea.IMAGE,[Na.RICH]:Ea.RICH,[Na.VIDEO]:Ea.VIDEO};return{id:this.getUniqueId(),mediaType:t[e.type],category:va.EXTERNAL,src:e.src,parentType:jn.MATTERTAG,created:new Date,height:0,width:0,thumbnailUrl:new Da("",null),url:new Da("",null),mimeType:""}}async createOEmbedIAttachment(e){const t={[Ma.PHOTO]:Ea.IMAGE,[Ma.RICH]:Ea.RICH,[Ma.VIDEO]:Ea.VIDEO},n=this.getUniqueId(),a=t[(await this.oEmbed.getOEmbedData(e)).type];if(!a)throw Error("unable to determine media type");return{id:n,mediaType:a,category:va.EXTERNAL,src:e,parentType:jn.MATTERTAG,created:new Date,height:0,width:0,thumbnailUrl:new Da("",null),url:new Da("",null),mimeType:""}}}const Pa={[r.MediaType.PHOTO]:Na.IMAGE,[r.MediaType.RICH]:Na.RICH,[r.MediaType.VIDEO]:Na.VIDEO};class La{constructor(e,t,n,a){this.tagAdd=e,this.tagEditIcon=t,this.tagAttach=n,this.attachmentRegistry=a}validateInput(e,t){return{descriptors:this.tagAdd.validateInput({descriptors:Array.isArray(e.descriptors)?e.descriptors:[e.descriptors]},t).descriptors}}async exec(e,t){const n=Array.isArray(e.descriptors)?e.descriptors:[e.descriptors],a=new Map;for(const e of n)if(e.media&&e.media.type!==r.MediaType.NONE){const n=this.attachmentRegistry.add(t.client.applicationKey,{src:e.media.src,type:Pa[e.media.type]});a.set(e.media.src,n)}const o=async e=>{for(let a=0;a<e.length;++a){const o=n[a];o.iconId&&await this.tagEditIcon.exec({id:e[a],iconId:o.iconId},t)}},s=async e=>{for(let o=0;o<e.length;++o){const s=n[o];if(s.media&&s.media.type!==r.MediaType.NONE){const n=a.get(s.media.src);n&&this.tagAttach.exec({tagId:e[o],attachmentIds:[n]},t)}}};return this.tagAdd.exec({descriptors:n},t).then((async e=>(await o(e),await s(e),e)))}}function xa(e,t,n){return(n=n||{}).x=e[t].x,n.y=e[t].y,n.z=e[t].z,n}function ka(e,t,n){return(n=n||{}).r=e[t].r,n.g=e[t].g,n.b=e[t].b,n}function _a(e,t,n,a){a=a||{};try{const o=t.getFloor(e);a.id=n.getIdFromCwfId(o.id),a.sequence=o.index}catch(e){a.id="",a.sequence=-1}return a}class Ua{create(e,t){return t.onChanged((()=>e.onChanged()))}}class Va{constructor(e,t,n,a){this.mattertagData=t,this.floorsData=n,this.floorIdMap=a,this._data={},this.mediaConverter=e.mediaConverter}get data(){return this._data}isItemEqual(e,t){return!z(this.data[t],e.data[t])}update(){for(const e of this.mattertagData.getTagList()){const t=this.mattertagData.getTag(e),n=this.floorsData.getFloor(t.floorId),a=this.data[t.sid]||{};a.sid=t.sid,a.enabled=t.enabled,a.anchorPosition=xa(t,"anchorPosition",a.anchorPosition),a.stemVector=xa(t,"stemVector",a.stemVector),a.stemVisible=t.stemVisible,a.label=t.label,a.description=t.description,a.media=this.getMedia(t,a.media),a.color=ka(t,"color",a.color),a.floorIndex=n?n.index:-1,a.floorInfo=a.floorInfo||{},a.floorInfo.id=this.floorIdMap.getIdFromCwfId(n.id),a.floorInfo.sequence=n.index,this._data[t.sid]=a}for(const e in this.data){this.mattertagData.getTag(e)||delete this._data[e]}}clear(){this._data={}}getMedia(e,t){const n=e.externalAttachments.get(0),a=n?this.mediaConverter.toSdkMedia(n.mediaType):r.MediaType.NONE,o=n&&a!==r.MediaType.NONE?n.src:"";return(t=t||{}).src=o,t.type=a,t}}class Fa{constructor(e,t,n){this.tagEditBillboard=t,this.tagAttach=n,[this.mattertagData,this.attachmentRegistry]=e}validateInput(e,t){if(!ft(e.properties))throw Error("Mattertag.editBillboard: editable properties is not an object");const n=ie(this.tagEditBillboard,e,t),a=e.properties.media;if(a){if(!ft(a)||!be(r.MediaType,a.type))throw Error("Mattertag.editBillboard: media was not a valid media descriptor");if(a.type!==r.MediaType.NONE){if(!de(a.src))throw Error(`Mattertag.editBillboard: media was of type "${a.type}" but has no src value`);return{id:n.id,properties:Object.assign(Object.assign({},n.properties),{media:{src:a.src,type:a.type}})}}n.properties.media={src:"",type:r.MediaType.NONE}}return n}async exec(e,t){const n=this.mattertagData.getTag(e.id),a=(()=>{if(e.properties.media&&e.properties.media.type!==r.MediaType.NONE)return this.attachmentRegistry.add(t.client.applicationKey,{src:e.properties.media.src,type:Pa[e.properties.media.type]})})(),o=this.mattertagData;return re(this.tagEditBillboard,e,t).then((()=>{e.properties.media&&(n.fileAttachments.replace([]),n.externalAttachments.replace([])),a&&re(this.tagAttach,{tagId:n.sid,attachmentIds:[a]},t),o.commit()}))}}function Ga(e,t,n){function a(e){if(!e)return;return{label:e.label,type:n.toSdkLinkType(e.type),url:e.url,navigationData:e.navigationData}}const o=[];for(const n of e)o.push({type:t.toSdkChunkType(n.type),link:a(n.link),text:n.text});return o}class Ha{constructor(e,t,n,a){this.commands=e,this.tagData=t,this.floorData=n,this.floorIdMap=a}validateInput(e,t){return e}async exec(e,t){return function(e,t,n,a){const o=[];return e.iterate((e=>{const{id:s,index:i}=t.getFloor(e.floorId),c=n.getIdFromCwfId(s),d=e.externalAttachments.get(0),l=d?a.mediaConverter.toSdkMedia(d.mediaType):r.MediaType.NONE,h=d&&l!==r.MediaType.NONE?d.src:"";o.push({sid:e.sid,label:e.label,description:e.description,parsedDescription:Ga(e.parsedDescription,a.chunkTypeConverter,a.linkTypeConverter),mediaSrc:h,mediaType:l,media:{type:l,src:h},anchorPosition:{x:e.anchorPosition.x,y:e.anchorPosition.y,z:e.anchorPosition.z},anchorNormal:{x:e.anchorNormal.x,y:e.anchorNormal.y,z:e.anchorNormal.z},color:{r:e.color.r,g:e.color.g,b:e.color.b},enabled:e.enabled,floorId:i,floorIndex:i,floorInfo:{id:c,sequence:i},stemVector:{x:e.stemVector.x,y:e.stemVector.y,z:e.stemVector.z},stemHeight:e.stemHeight,stemVisible:e.stemVisible})})),o}(this.tagData,this.floorData,this.floorIdMap,this.commands)}}class Ba extends Le{constructor(e){super("Expected tagId parameter to be of type string; got "+(Array.isArray(e)?"Array":typeof e))}}class za{constructor(e,t,n){this.mattertagsData=e,this.attachmentRegistry=t,this.registerSandbox=n}validateInput(e,t){var n,a;if(!de(e.tagId)||!this.mattertagsData.getTag(e.tagId))throw new Ba(e.tagId);const o=e.options||{};return"string"==typeof(null===(n=o.size)||void 0===n?void 0:n.w)&&(o.size.w=parseInt(o.size.w,10)),"string"==typeof(null===(a=o.size)||void 0===a?void 0:a.h)&&(o.size.h=parseInt(o.size.h,10)),Object.assign(Object.assign({},this.registerSandbox.validateInput(Object.assign(Object.assign({},e),{options:o}),t)),{tagId:e.tagId})}async exec(e,t){const{clientId:n,html:a,options:o,tagId:s}=e,{sandboxId:i,attachmentId:r}=await this.registerSandbox.exec({clientId:n,html:a,options:o},t),c=this.attachmentRegistry.get(t.client.applicationKey,r);return c&&function(e){return"onLoad"in e}(c)&&this.mattertagsData.getTag(s).sandboxAttachments.replace([c]),{sandboxId:i,attachmentId:r}}}class $a{constructor(e){this.tagRemove=e}validateInput(e,t){return{ids:ie(this.tagRemove,{ids:Array.isArray(e.ids)?e.ids:[e.ids]},t).ids}}async exec(e,t){return re(this.tagRemove,{ids:Array.isArray(e.ids)?e.ids:[e.ids]},t)}}var ja;!function(e){e[e.Instant=0]="Instant",e[e.FadeToBlack=1]="FadeToBlack",e[e.Interpolate=2]="Interpolate",e[e.MoveToBlack=3]="MoveToBlack"}(ja||(ja={}));const Wa={[r.Transition.FLY]:ja.Interpolate,[r.Transition.FADEOUT]:ja.FadeToBlack,[r.Transition.INSTANT]:ja.Instant};class qa{constructor(e,t,n,a,o,s){this.FocusOnPinInsideCommand=e,this.OpenTagCommand=t,this.issueCommand=n,this.mattertagData=a,this.tagsViewData=o,this.annotationsViewData=s}validateInput(e,t){if(!de(e.id)||!this.mattertagData.getTag(e.id))throw Error(`${e.id} does not map to a valid Tag`);if(void 0!==e.transitionType&&!be(r.Transition,e.transitionType))throw Error(`${e.transitionType} does not match valid transition type`);return{id:e.id,transitionType:e.transitionType,force:!!e.force}}async exec(e,t){const n=this.mattertagData.getTag(e.id);let a=Wa[e.transitionType];return void 0===a&&(a=ja.Interpolate),(this.tagsViewData.getCapabilities(e.id).focus||e.force)&&(await this.issueCommand(new this.FocusOnPinInsideCommand({pinPosition:Object.assign(Object.assign({},n),{stemLength:n.stemHeight,stemNormal:n.stemVector}),transition:a})),(this.annotationsViewData.getCapabilities(e.id).preview||e.force)&&await this.issueCommand(new this.OpenTagCommand(e.id))),e.id}}class Ka{constructor(e,t){this.tagAllowAction=e,this.annotationsViewData=t}validateInput(e,t){const n=ie(this.tagAllowAction,{id:e.tagId,allow:{}},t),a={navigating:!1,opening:!1};return ft(e.prevent)&&(a.navigating=!!e.prevent.navigating,a.opening=!!e.prevent.opening),{tagId:n.id,prevent:a}}exec(e,t){const n=this.annotationsViewData.getCapabilities(e.tagId);return re(this.tagAllowAction,{id:e.tagId,allow:{navigating:!e.prevent.navigating,opening:!e.prevent.opening,docking:n.dock}},t)}}function Ya(e,t,n,a,o,s){e.addEnumToInterface({namespace:"Mattertag",name:"DescriptionChunkType",values:r.DescriptionChunkType}),e.addEnumToInterface({namespace:"Mattertag",name:"Event",values:r.Event}),e.addEnumToInterface({namespace:"Mattertag",name:"LinkType",values:r.LinkType}),e.addEnumToInterface({namespace:"Mattertag",name:"MediaType",values:r.MediaType}),e.addEnumToInterface({namespace:"Mattertag",name:"Transition",values:r.Transition});const i=Object.assign(Object.assign({},n),{mediaConverter:new Xa(t.MediaType)}),[c,d,l,h,u,p]=s;!function(e,t,n){const a=["",!1],o=e=>(a[0]=e.id,a[1]=e.hovering,a);n.subscribe(t.PinHoverChangeMessage,(n=>{n.pinType===t.PinType.MATTERTAG&&e.broadcast(r.Event.HOVER,o,n)}));const s=[""],i=e=>(s[0]=e.id,s);n.subscribe(t.PinClickedMessage,(n=>{n.pinType===t.PinType.MATTERTAG&&e.broadcast(r.Event.CLICK,i,n)}))}(e,t,n),function(e,t,n,a){a.getDependencies().then((([a])=>{const o=["",""],s=e=>{var t;return o[0]=(null===(t=a.billboardAnnotation)||void 0===t?void 0:t.id)||"",o[1]=e.url,o};n.subscribe(t.MattertagLinkOpenedMessage,(t=>{e.broadcast(r.Event.LINK_OPEN,s,t)}))}))}(e,t,n,new we(l));const m=function(e,t,n){const a=Rt.create(new we(t.add,t.editIcon,t.attach,n),new G(La));return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"add",args:["descriptors"],options:{replay:!0,deprecated:"Tag.add"}},a),a}(e,o,p),g=function(e,t,n){const a=at.create(n,new Ua,new G(Va,t));return e.addCollectionToInterface({namespace:"Mattertag",name:"data"},a),a}(e,i,new we(c,h,u)),w=function(e,t,n){const a=oe.create(new we(n.getDependencies(),t.editBillboard,t.attach),new G(Fa));return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"editBillboard",args:["id","properties"],options:{replay:!0,deprecated:"`Tag.editBillboard`, or `Tag.registerAttachment` and `Tag.attach` for media"}},a),a}(e,o,new we(c,p)),f=function(e,t){return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"editColor",args:["id","color"],options:{replay:!0,deprecated:"Tag.editColor"}},t),t}(e,o.editColor),y=function(e,t){return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"editIcon",args:["id","iconId"],options:{replay:!0,deprecated:"Tag.editIcon"}},t),t}(e,o.editIcon),I=function(e,t){return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"editOpacity",args:["id","opacity"],options:{replay:!0,deprecated:"Tag.editOpacity"}},t),t}(e,o.editOpacity),T=function(e,t){return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"editPosition",args:["id","options"],options:{replay:!0,deprecated:"Tag.editPosition"}},t),t}(e,o.editPosition),E=function(e,t){return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"editStem",args:["id","stemParams"],options:{replay:!0,deprecated:"Tag.editStem"}},t),t}(e,o.editStem);!function(e,t,n){const a=oe.create(n,new G(Ha,t));e.addAsyncCommandToInterface({namespace:"Mattertag",name:"getData",args:[],options:{deprecated:"Tag.data"}},a)}(e,i,new we(c,h,u));const v=function(e,t,n){const a=Rt.create(we.extend(n,t.registerSandbox),new G(za));return e.addCommandToInterface({namespace:"Mattertag",name:"injectHTML",subRoutine:"mattertag.inject",args:["tagId","html","options"],options:{replay:!0,deprecated:"Tag.registerSandbox and Tag.attaach"}},a),a}(e,o,new we(c,p));!function(e,t,n,a){const o=oe.create(a,new G(qa,t.FocusOnPinInsideCommand,t.OpenTagCommand,n.issueCommand));e.addAsyncCommandToInterface({namespace:"Mattertag",name:"navigateToTag",args:["id","transitionType","force"]},o)}(e,t,n,new we(c,d,l));const C=function(e,t){const n=oe.create(t,new G(Ka));return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"preventAction",args:["tagId","prevent"],options:{replay:!0,deprecated:"Tag.allowAction"}},n),n}(e,new we(o.allowAction,l));!function(e,t){e.addAsyncCommandToInterface({namespace:"Mattertag",name:"registerIcon",args:["textureId","textureSrc"],options:{deprecated:"Asset.registerTexture"}},t)}(e,a.registerTexture);return{data:g,add:m,editBillboard:w,editColor:f,editIcon:y,editOpacity:I,editPosition:T,editStem:E,inject:v,preventAction:C,remove:function(e,t){const n=oe.create(new we(t),new G($a));return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"remove",args:["ids"],options:{replay:!0,deprecated:"Tag.remove"}},n),n}(e,o.remove),resetIcon:function(e,t){return e.addAsyncCommandToInterface({namespace:"Mattertag",name:"resetIcon",args:["id"],options:{replay:!0,deprecated:"Tag.resetIcon"}},t),t}(e,o.resetIcon)}}class Xa{constructor(e){this.toSdkMediaMap={[e.IMAGE]:r.MediaType.PHOTO,[e.VIDEO]:r.MediaType.VIDEO,[e.RICH]:r.MediaType.RICH,[e.APPLICATION]:r.MediaType.NONE,[e.AUDIO]:r.MediaType.NONE,[e.MODEL]:r.MediaType.NONE,[e.PDF]:r.MediaType.NONE,[e.TEXT]:r.MediaType.NONE,[e.ZIP]:r.MediaType.NONE}}toSdkMedia(e){return this.toSdkMediaMap[e]}}class Za extends W{constructor(){super(...arguments),this.id="MEASUREMENTS_GET"}}const Qa=async(e,t)=>{const n=[];for(const a of e.groups()){const e=a.info,o=t.getFloor(e.floorId);for(let t=1;t<a.count;t++){const s=a.get(t-1),i=a.get(t);n.push({sid:e.sid,label:e.text,floor:o.index,start:{x:s.x,y:s.y,z:s.z},end:{x:i.x,y:i.y,z:i.z}})}}return n};class Ja{constructor(e){this.CwfMeasuringPhase=e}create(e,t){let n=t.groupCount,a=t.phase;const o=()=>{const o=t.groupCount,s=t.phase;n===o&&a!==this.CwfMeasuringPhase.EDITING||s!==this.CwfMeasuringPhase.IDLE||(e.onChanged(),n=o),a=s};return new Ne(t.onPhaseChanged(o),t.onSelectedGroupIndexChanged(o))}}class eo{constructor(e){this.measurementModeData=e,this.data={}}update(){for(const e of this.measurementModeData.groups())this.createMeasurement(e);for(const e in this.data){this.measurementModeData.getGroupInfoBySid(e)||delete this.data[e]}}isItemEqual(e,t){return!z(this.data[t],e.data[t])}clear(){this.data={}}createMeasurement(e){const t=e.info.sid,n=this.data[t]||{},a=n.points||[];n.label=e.info.text,n.segmentLengths=e.segmentLengths,n.totalLength=e.length,function(e,t){let n=0;for(const a of t){const t=e[n]||{};t.x=a.x,t.y=a.y,t.z=a.z,e[n]=t,++n}e.length=n}(a,e),n.sid=t,n.points=a,this.data[t]=n}}class to{create(e,t){return t.onChanged((()=>e.onChanged()))}}class no{constructor(){this._data={active:!1}}get data(){return this._data}equals(e){return this._data.active===e.data.active}copy(e){this._data.active=e.data.active}update(e){this._data.active=e.modeActive()}}class ao extends W{constructor(e){super(),this.id="SDK_MEASUREMENT_TOGGLE",this.payload={active:e}}}class oo extends Le{constructor(e="Unhandled Transition Exception"){super(e),this.name="TransitionException"}}class so extends oo{constructor(e="Unhandled Viewmode Exception",t){super(e),this.originalError=t,this.name="ViewmodeException"}}class io extends so{constructor(e="Tried to start view-mode transition while another transition was active"){super(e),this.name="ViewmodeActiveTransition"}}function ro(e){return"number"==typeof e&&!isNaN(e)}function co(e){return e-0}function lo(e,t,n){return Math.max(t,Math.min(e,n))}function ho(e){if(e&&"object"==typeof e&&"x"in e&&"y"in e&&"z"in e){const t=e;return ro(t.x)&&ro(t.y)&&ro(t.z)}return!1}const uo=new ne("move-to-mode-command");class po extends W{constructor(e){super(),this.id="MOVE_TO_MODE",this.payload=e}}class mo{constructor(e,t,n,a){this.THREE=e,this.viewmodeConverter=t,this.conversion=n,this.transitionTypeConverter=a}validateMoveToModeInput(e){if(!e)throw Error("Mode.moveTo -> no arguments provided -- mode is required");e.options||(e.options={});const t=e.mode;if(!be(c.Mode,e.mode))throw Error(`Mode.moveTo -> ${e.mode} is not a valid viewmode`);if(t===c.Mode.TRANSITIONING)throw Error(`Mode.moveTo -> ${e.mode} is not a valid viewmode`);e.options&&e.options.rotation&&(e.options.rotation.z=e.options.rotation.z||0);const n=this.validatePayloadOptions(e.options);return{mode:this.viewmodeConverter.fromSdk(t),options:{transition:n.transition,position:n.position,rotation:n.rotation,zoom:n.zoom}}}validatePayloadOptions(e){let t,n,a,o;if(e){if(e.transition&&(a=this.transitionTypeConverter.fromSdkTransition(e.transition),void 0===a))throw Error(`Mode.moveTo -> ${e.transition} is not a valid transition style`);e.rotation&&ho(e.rotation)&&(t=this.conversion.rotationToQuaternion(e.rotation)),e.position&&ho(e.position)&&(n=new this.THREE.Vector3(parseFloat(e.position.x),parseFloat(e.position.y),parseFloat(e.position.z))),e.zoom&&!isNaN(e.zoom)&&(o=We(e.zoom,1,30))}return{rotation:t,position:n,transition:a,zoom:o}}}class go{create(e,t,n){const a=()=>e.onChanged();return new Ne(n.onChanged(a),t.onChanged(a))}}class wo{constructor(e,t){this._data=c.Mode.INSIDE,this.viewmodeConverter=t.viewmodeConverter}get data(){return this._data}equals(e){return this._data===e.data}copy(e){this._data=e.data}update(e,t){const n=t.currentSweep&&t.isSweepAligned(t.currentSweep);this._data=this.viewmodeConverter.toSdk(e.currentMode,!!n)}}class fo extends W{constructor(){super(...arguments),this.id="GET_MODEL_DATA"}}function yo(e,t,n){t.addBinding(fo,(async()=>{const e=await n.getDependencies();return await async function(e,n,a,o){const s={sid:"",sweeps:[],modelSupportsVr:!1};try{const i=n.getSweepList(),r=e.model;return await t.makeModelData(r.sid,r.options.isVR,i,a,o,s),s}catch(e){throw Error("no model currently loaded")}}(...e)})),e.addCommandCreator({namespace:"Model",name:"getData",args:[]},(()=>new fo))}class Io extends W{constructor(){super(...arguments),this.id="GET_MODEL_DETAILS"}}function To(e,t,n,a){a.getDependencies().then((e=>n.addBinding(Io,(async()=>function(e,n){function a(e,t){if(n.tryGetProperty(e,!0))return t}const o=e.model;return{sid:o.sid,name:a(t.OptionsKey.DetailsModelName,o.details.name),presentedBy:a(t.OptionsKey.PresentedBy,o.details.presentedBy),summary:a(t.OptionsKey.DetailsSummary,o.details.summary),formattedAddress:a(t.OptionsKey.DetailsAddress,o.details.formattedAddress),contactEmail:a(t.OptionsKey.DetailsEmail,o.details.contact.email),contactName:a(t.OptionsKey.DetailsName,o.details.contact.name),phone:a(t.OptionsKey.DetailsPhone,o.details.contact.phone),formattedContactPhone:a(t.OptionsKey.DetailsPhone,o.details.contact.formattedPhone),externalUrl:a(t.OptionsKey.DetailsExternalUrl,o.details.externalUrl)}}(...e))))),e.addCommandCreator({namespace:"Model",name:"getDetails",args:[]},(()=>new Io))}function Eo(e,t){return async function(n,a,o,s,i,r){r.sweeps=[];for(const n of o){const a=n.floorId?s.getFloor(n.floorId).index:-1;r.sweeps.push({uuid:i.getIdForSweep(n),sid:i.getIdForSweep(n),alignmentType:t.toSdkAlignment(n.alignmentType),placementType:t.toSdkPlacement(n.placementType),neighbors:await Promise.all(n.neighbours.map((async e=>await i.getIdFromCwfId(e)))),position:(c=n,null!==c.position?{x:c.position.x,y:c.position.y,z:c.position.z}:null),rotation:e.quaternionToRotation(n.rotation),floor:a})}var c;return r.sid=n,r.modelSupportsVr=a,r}}class vo{constructor(e){this.tokenUpdater=e}validateInput(e){const{token:t}=e;if(!de(t))throw Error();return{token:t}}async exec(e,t){this.tokenUpdater.setToken(e.token)}}var Co,Ao;!function(e){e.POSTMESSAGE="postmessage",e.DIRECT="direct",e.WEBSOCKET="websocket"}(Co||(Co={})),function(e){e[e.None=0]="None",e[e.AnonymousFetch=1]="AnonymousFetch",e[e.UserFetch=2]="UserFetch"}(Ao||(Ao={}));class So{constructor(e){this.pluginRegistry=e}validateInput(e){if(!ft(e.key))throw Error(e.key+" is not a valid object");const t=e.key;if(!le(t.applicationKey))throw Error(t.applicationKey+" is not a valid string");if(!le(t.id))throw Error(t.id+"is not a valid string");if(!le(e.path))throw Error(e.path+" is not a valid string");return e}async exec(e){var t;let n=Ao.AnonymousFetch;void 0!==(null===(t=e.config)||void 0===t?void 0:t.$fetchLevel)&&(n=lo(e.config.$fetchLevel,Ao.None,Ao.UserFetch));const a={};for(const t in e.config)"$fetchLevel"!==t&&(a[t]=e.config[t]);return this.pluginRegistry.load({applicationKey:e.key.applicationKey,id:e.key.id||"default",src:e.path,strict:!1,fetchLevel:n,config:a||{}})}}class Do{constructor(e){this.pluginRegistry=e}validateInput(e){if(!ft(e.key))throw Error(e.key+" is not a valid object");if(!le(e.key.applicationKey))throw Error(e.key+" is not a valid string");return e}async exec(e){return this.pluginRegistry.unload(e.key)}}class bo{create(e,t){return t.onChanged((()=>e.onChanged()))}}class Oo{constructor(e,t){this.colliderTypes=t,this.ZERO=new e.Vector3(0,0,0),this.tempVec=new e.Vector3,this._data={position:{x:0,y:0,z:0},normal:{x:0,y:0,z:0},object:l.Colliders.NONE,floorId:void 0,floorIndex:void 0}}get data(){return this._data}equals(e){const t=1e-5,n=e.data.position,a=this._data.position,o=e.data.normal,s=this._data.normal;return Ve(n,a,t)&&Ve(o,s,t)&&e.data.object===this._data.object}update(e,t,n,a,o){const s=e.hit;s?(Pe(this._data.position,s.point),Pe(this._data.normal,s.face.normal),this._data.floorId=void 0,s.object instanceof this.colliderTypes.ModelColliderTarget?(this._data.object=l.Colliders.MODEL,this._data.floorId=function(e,t,n,a,o,s){let i;const r=o.floorIdFromObject(e.object);if(r)return i=t.getFloor(r),i.index;s.copy(e.point);const c=a.meshSubGroupsFromPoint(s);if(!c.length)return;const d=n.getByMeshSubgroup(c[0]);if(!d)return;return i=t.getFloor(d.floorId),i.index}(s,t,n,a,o,this.tempVec)):s.object instanceof this.colliderTypes.PinHeadMesh||s.object instanceof this.colliderTypes.InstancedPinHeads?this._data.object=l.Colliders.TAG:s.object instanceof this.colliderTypes.PuckCollider?this._data.object=l.Colliders.SWEEP:this._data.object=l.Colliders.UNKNOWN):(Pe(this._data.position,this.ZERO),Pe(this._data.normal,this.ZERO),this._data.object=l.Colliders.NONE,this._data.floorId=void 0),this._data.floorIndex=this._data.floorId}copy(e){Pe(this._data.position,e.data.position),Pe(this._data.normal,e.data.normal),this._data.object=e.data.object,this._data.floorId=e.data.floorId,this._data.floorIndex=e.data.floorIndex}}class Mo{constructor(e,t){this.cursorController=e,this.textureAaps=t}validateInput(e){if(!de(e.textureId))throw Error("textureId is not a valid string");return{textureId:e.textureId}}async exec(e,t){const{textureId:n}=e,a=this.textureAaps.get(t.client.applicationKey).map[n];this.cursorController.setTexture(a)}}class No{constructor(e){this.cursorController=e}validateInput(e){return e}async exec(e,t){this.cursorController.setTexture(null)}}class Ro{constructor(e){this.cursorController=e}validateInput(e){(e=e||{}).props=e.props||{};const t=e.props;if(t.fadeIn){const e=t.fadeIn;if(e.duration&&(!ro(e.duration)||e.duration<=0))throw Error(`Pointer.setFadeProps: fadeIn.duration was specified but was not a valid duration; got ${e.duration}`)}if(t.fadeOut){const e=t.fadeOut;if(e.duration&&(!ro(e.duration)||e.duration<=0))throw Error(`Pointer.setFadeProps: fadeOut.duration was specified but was not a valid duration; got ${e.duration}`);if(e.delay&&(!ro(e.delay)||e.delay<=0))throw Error(`Pointer.setFadeProps: fadeOut.delay was specified but was not a valid delay; got ${e.delay}`)}return e}async exec(e,t){this.cursorController.setFadeProps(e.props)}}class Po{constructor(e){this.cursorController=e,this.visible=!0,this.ruleAdded=!1}isVisible(){return this.visible}validateInput(e){if(!yt(e.visible))throw Error(`Pointer.setVisible: visible was specified but was not a valid boolean; got ${e.visible}`);return e}async exec(e,t){this.ruleAdded||(this.isVisible=this.isVisible.bind(this),this.cursorController.addVisibilityRule(this.isVisible),this.ruleAdded=!0),this.visible=e.visible}}class Lo{constructor(e){this.r3fModule=e}validateInput(e){return e}async exec(e,t){return this.r3fModule.registerExternalR3F(e.callbacks)}}const xo=new ne("mpSdk.R3F.navigationToggle");class ko{constructor(e){this.enabled=!0,this.userNavigationEnabled=()=>this.enabled,e.addNavigationRule(this.userNavigationEnabled.bind(this))}validateInput(e){return e}async exec(e,t){this.enabled=e.enabled,xo.debug(e)}}class _o{constructor(e,t,n,a,o){this.THREE=e,this.cwfTypes=t,this.commands=n,this.navigation=a,this.viewmodeData=o}validateInput(e){if(!e)throw Error('focus: no arguments provided -- "target" point|box is required');const t=e.target;if(!function(e){if(e&&"object"==typeof e&&"min"in e&&"max"in e){const t=e;if(ho(t.min)&&ho(t.max))return!0}return!1}(t)&&!ho(t))throw new Error('focus: invalid "target"');const n=e.options;if(n){const{from:e,mode:t,transition:a}=n;if(e&&!ho(e))throw new Error('focus: "options.from" must be a Vector3|undefined');if(t){if(void 0===this.commands.viewmodeConverter.fromSdk(t))throw Error(`focus: ${t} is not a valid "options.mode"`)}let o=this.cwfTypes.CameraTransitionType.Interpolate;if(a&&(o=this.commands.cameraTransitionConverter.fromSdkTransition(a),void 0===o))throw Error(`focus: ${a} is not a valid "options.transition"`)}return e}async exec(e,t){const n=e.target,a=this.convertOptions(e);try{await this.navigation.focus(n,a)}catch(e){console.warn("focus: unable to focus",e)}}convertOptions(e){var t,n,a,o;const s=null===(t=e.options)||void 0===t?void 0:t.mode,i=s?this.commands.viewmodeConverter.fromSdk(s):null!==(n=this.viewmodeData.currentMode)&&void 0!==n?n:void 0,r=null===(a=e.options)||void 0===a?void 0:a.from,c=r&&ho(r)?new this.THREE.Vector3(r.x,r.y,r.z):void 0,d=null===(o=e.options)||void 0===o?void 0:o.transition;return{from:c,mode:i,transition:d?this.commands.cameraTransitionConverter.fromSdkTransition(d):this.cwfTypes.CameraTransitionType.Interpolate}}}const Uo=new ne("mpSdk.R3F.controlsToggle");class Vo{constructor(e){this.controls=e,this.sub=null}validateInput(e){return e}async exec(e,t){if(Uo.debug(e),this.sub&&e.enabled&&(this.sub.cancel(),this.sub=null),!this.sub&&!e.enabled){const e=this.controls.cameraPoseProxy;this.sub=e.newSession(new Fo)}}}class Fo{constructor(){}onAccessGranted(e){Uo.debug("controls-block granted")}onAccessRevoked(e){Uo.debug("controls-block revoked")}}class Go{constructor(e,t){this.captureCamera=e,this.encodeRenderTarget=t}async capture(e,t,n,a){const{camera:o,scene:s}=t.getScene();return o.getWorldPosition(this.captureCamera.position),o.getWorldQuaternion(this.captureCamera.quaternion),this.captureCamera.projectionMatrix.copy(o.projectionMatrix),this.captureCamera.layers.mask=e.visibleObjects.mask,a.setSize(e.resolution.width,e.resolution.height),n.render(a.target,s,this.captureCamera),await this.encodeRenderTarget(a)}}function Ho(e){return ro(e)&&e%1==0}const Bo=new ne("command.takeScreenshot");var zo,$o;!function(e){e.Base64JPG="screenshot.base64.jpg",e.ArrayBufferJPG="screenshot.arraybuffer.jpg"}(zo||(zo={}));class jo{constructor(e,t,n,a,o,s){this.canvasData=a,this.rendererModule=o,this.renderToTexture=s,this.RenderLayers=t.RenderLayers,this.screenCapturer=e,this.requestTarget=n.requestTarget,this.getRenderLayer=n.getRenderLayer,this.jpegAsBase64=n.jpegAsBase64}validateInput(e){return{resolution:this.validateResolution(e.resolution,this.canvasData,this.rendererModule),visibleObjects:this.buildVisibilityOptions(e.visibleObjects),returnType:e.returnType===zo.ArrayBufferJPG?zo.ArrayBufferJPG:zo.Base64JPG}}async exec(e,t){const n=await this.requestTarget(),a=await this.screenCapturer.capture({resolution:e.resolution,visibleObjects:this.buildVisibilityLayers(e.visibleObjects)},this.rendererModule,this.renderToTexture,n);return e.returnType===zo.ArrayBufferJPG?a.buffer:this.jpegAsBase64(a)}validateResolution(e,t,n){const a={width:0,height:0};if(!e)return a.width=t.width,a.height=t.height,a;if(!function(e){const t=e;return!!t&&("number"==typeof t.width&&"number"==typeof t.height)}(e))throw Error('"resolution" provided was not valid. Expected .width and .height to be numbers.');a.width=e.width,a.height=e.height;const o=n.maxTextureSize;if(e.width<=0||e.width>o)throw Error('"resolution.width" provided was outside the valid range of [0, '+o+"]");if(e.height<=0||e.height>o)throw Error('"resolution.height" provided was outside the valid range of [0, '+o+"]");if(!Ho(a.width)){const e=Math.round(a.width);Bo.info(`Integer width expected. Rounding from ${a.width} to ${e}`),a.width=e}if(!Ho(a.height)){const e=Math.round(a.height);Bo.info(`Integer width expected. Rounding from ${a.height} to ${e}`),a.height=e}return a}buildVisibilityOptions(e){const t=e||{};return{measurements:!!t.measurements,mattertags:!!t.mattertags,sweeps:!!t.sweeps,views:!!t.views}}buildVisibilityLayers(e){const t=this.RenderLayers.ALL;return e.mattertags||t.removeLayers(this.getRenderLayer("pins")),e.measurements||t.removeLayers(this.getRenderLayer("measurements")),e.sweeps||t.removeLayers(this.getRenderLayer("sweep-pucks")),e.views||(t.removeLayers(this.getRenderLayer("sweep-portal-mesh")),t.removeLayers(this.getRenderLayer("sweep-pin-mesh"))),t}}class Wo{constructor(e,t,n,a,o,s,i,r){this.renderer=a,this.renderToTexture=o,this.viewmodeData=s,this.sweepData=i,this.sweepTextureLoader=r,this.Viewmode=t.Viewmode,this.requestTarget=n.requestTarget,this.encodeRenderTarget=n.encodeRenderTarget,this.jpegAsBase64=n.jpegAsBase64,this.getXmp=n.getXmp,this.getOrientedAngleTo=n.getOrientedAngleTo,this.forward=new e.Vector3(0,0,-1),this.sweepForward=new e.Vector3,this.viewForward=new e.Vector3}validateInput(e){return e}async exec(e,t){if(!this.sweepData.currentSweep||this.viewmodeData.currentMode!==this.Viewmode.Panorama)throw new Error("Can only capture equirectangular projections while stationary in a sweep");const n=this.renderer.getScene().camera,a=this.sweepData.getSweep(this.sweepData.currentSweep),o=await this.sweepTextureLoader.load(a);this.sweepForward.copy(this.forward),this.sweepForward.copy(this.forward).applyQuaternion(a.rotation).setY(0);const s=this.getOrientedAngleTo(this.sweepForward,n.getWorldDirection(this.viewForward).setY(0))+Math.PI,i=await this.requestTarget();i.setSize(Wo.equirectangularRes.width,Wo.equirectangularRes.height),this.renderToTexture.renderEquirectangular(o,i.target,s);const r=await this.encodeRenderTarget(i,this.getXmp(i.width,i.height,0,0));return this.jpegAsBase64(r)}}Wo.equirectangularRes={width:4096,height:2048};class qo{constructor(e,t){this.issueCommand=t.issueCommand,this.GetScreenPositionCommand=e.GetScreenPositionCommand}validateInput(e){if(!ho(e.worldPosition))throw Error(`Renderer.getScreenPosition: 'worldPosition' was specified but was not a valid Vector3; got ${e.worldPosition}`);return e}async exec(e,t){return await this.issueCommand(new this.GetScreenPositionCommand(e))}}class Ko{constructor(e,t,n){this.floorsData=n,this.GetFloorIntersectCommand=e.GetFloorIntersectCommand,this.issueCommand=t.issueCommand}validateInput(e){if(!function(e){if(e&&"object"==typeof e&&"x"in e&&"y"in e){const t=e;return ro(t.x)&&ro(t.y)}return!1}(e.screenPosition))throw Error(`Renderer.getWorldPositionData: 'screenPosition' was not a valid Vector2; got ${e.screenPosition}`);return e}async exec(e,t){const n=await this.issueCommand(new this.GetFloorIntersectCommand(e)),a=this.floorsData.getFloorAtIndex(n.floorIndex);return a?{position:n.position,floorInfo:{id:a.id,sequence:a.index},floor:n.floorIndex}:{position:n.position,floorInfo:{id:"",sequence:-1},floor:-1}}}class Yo{constructor(){this.wasTransitioning360=!1,this.cameraPosition={x:0,y:0,z:0}}create(e,t,n,a,o){return new Ne(a.onChanged((()=>this.throttleCameraPositionChanges(e,a))),o.onChanged((()=>this.throttleSweepChanges(e,o))))}throttleCameraPositionChanges(e,t){const n=t.pose.position;n.x===this.cameraPosition.x&&n.y===this.cameraPosition.y&&n.z===this.cameraPosition.z||(this.cameraPosition.x=n.x,this.cameraPosition.y=n.y,this.cameraPosition.z=n.z,e.onChanged())}throttleSweepChanges(e,t){const n=t.transition;t.transitionActive?(t.isSweepUnaligned(n.from)||t.isSweepUnaligned(n.to))&&(this.wasTransitioning360=!0):this.wasTransitioning360&&!t.isSweepUnaligned(t.currentSweep)&&(e.onChanged(),this.wasTransitioning360=!1)}}class Xo{constructor(){this.currentRooms={rooms:[]}}get data(){return this.currentRooms}equals(e){return this.data.rooms.length===e.data.rooms.length&&this.currentRooms.rooms.every((t=>e.data.rooms.findIndex((e=>e.id===t.id))>-1))}copy(e){this.currentRooms.rooms.length=e.data.rooms.length,xe(this.currentRooms.rooms,e.data.rooms)}update(e,t,n,a,o,s,i,r){if(o.isInside()&&a.isSweepUnaligned(a.currentSweep)||a.transitionActive&&(a.isSweepUnaligned(a.transition.to)||a.isSweepUnaligned(a.transition.from)))return void(this.currentRooms.rooms.length=0);const c=s.meshSubGroupsFromPoint(n.pose.position);this.currentRooms.rooms.length=0;for(let n=0;n<c.length;++n){const a=c[n],o=e.getByMeshSubgroup(a),d=s.meshGroups.rooms.get(a);if(!o||!d)continue;const l=this.currentRooms.rooms[n]||{};l.id=i.getIdFromCwfId(o.id),l.bounds=l.bounds||{},l.bounds.min=xa(d.boundingBox,"min",l.bounds.min),l.bounds.max=xa(d.boundingBox,"max",l.bounds.max),l.floorInfo=_a(o.floorId,t,r,l.floorInfo),this.currentRooms.rooms.push(l)}}}class Zo{create(e,t){return{renew(){},cancel(){}}}}class Qo{constructor(e,t,n,a,o){this.roomData=e,this.floorData=t,this.roomIdMap=n,this.floorIdMap=a,this.meshData=o,this._data={}}get data(){return this._data}isItemEqual(e,t){const n=this.data[t],a=e.data[t];return n.id===a.id&&Ve(n.bounds.min,a.bounds.min)&&Ve(n.bounds.max,a.bounds.max)}update(){for(const e of this.roomData.rooms()){const t=this.meshData.meshGroups.rooms.get(e.meshSubgroup);if(!t)continue;const n=this.roomIdMap.getIdFromCwfId(e.id),a=this._data[n]||{};a.id=n,a.bounds=a.bounds||{},a.bounds.min=xa(t.boundingBox,"min",a.bounds.min),a.bounds.max=xa(t.boundingBox,"max",a.bounds.max),a.floorInfo=_a(e.floorId,this.floorData,this.floorIdMap,a.floorInfo),this._data[n]=a}for(const e in this.data){this.roomIdMap.getRoomForId(e)||delete this._data[e]}}clear(){this._data={}}}class Jo{constructor(e,t){this.RoomData=e,this.FloorsData=t}validateInput(e){return{invert:!!(null==e?void 0:e.invert)||!1}}async exec(e,t){return this.RoomData.getRoomIdMap(this.FloorsData,e.invert)}}!function(e){e.OUTPUT="output",e.INPUT="input",e.EVENT="event",e.EMIT="emit"}($o||($o={}));class es{constructor(e){this.sceneModule=e}validateInput(e){if(!de(e.name))throw Error(`Scene.register name ${e.name} is not a string`);if(!e.factory||"function"!=typeof e.factory)throw Error("Scene.register factory is not a function");return e}async exec(e,t){if(this.sceneModule.registerFactory({scope:t.client.applicationKey,name:e.name},e.factory))return{dispose:()=>{this.sceneModule.unregisterFactory(e.name,e.factory)}};throw Error("There was an error registering the scene component")}}class ts{constructor(e){this.sceneModule=e}validateInput(e){return e}async exec(e,t){return this.sceneModule.createNode(t.client.applicationKey).publicFacade()}}class ns extends W{constructor(e){super(),this.id="SCENE_QUERY",this.payload=e}}class as extends W{constructor(e){super(),this.id="SCENE_CONFIGURE",this.payload=e}}class os extends W{constructor(e){super(),this.id="SCENE_GETIMAGE",this.payload=e}}class ss{constructor(e){this.sceneModule=e}validateInput(e){return e}async exec(e,t){return this.sceneModule.deserialize(t.client.applicationKey,e.text).object}}const is=new ne("serialize");class rs{constructor(e){this.sceneModule=e}validateInput(e){return e}async exec(e,t){Array.isArray(e.objects)&&is.warn("Serializing an array of ISceneNodes is deprecated and may produce an unexpected result.                Serialization of the ISceneObject container should be done instead.");return Array.isArray(e.objects)?this.sceneModule.serializeNodeList(e.objects):this.sceneModule.serialize(e.objects)}}class cs{constructor(e){this.sceneModule=e}validateInput(e){if(!ro(e.count))throw Error("count is not a valid number");return e}async exec(e,t){const n=[];for(let a=0;a<e.count;a++){const e=this.sceneModule.createNode(t.client.applicationKey);n.push(e.publicFacade())}return n}}class ds{constructor(e){this.sceneModule=e}validateInput(e){if(!Dt(e.components,ft))throw Error("Scene.registerComponents array elements must be objects");for(const t of e.components){if(!de(t.name))throw Error(`Scene.registerComponents name ${t.name} is not a string`);if(!t.factory||"function"!=typeof t.factory)throw Error("Scene.registerComponents factory is not a function")}return e}async exec(e,t){const n=[];for(const a of e.components){if(!this.sceneModule.registerFactory({scope:t.client.applicationKey,name:a.name},a.factory))throw Error("There was an error registering a scene component");{const e=this.sceneModule,t=function(t,n){return{dispose:function(){e.unregisterFactory(t,n)}}}(a.name,a.factory);n.push(t)}}return n}}class ls{constructor(e){this.sceneModule=e}validateInput(e){if(!ro(e.count))throw Error("count is not a valid number");return e}async exec(e,t){const n=[];for(let a=0;a<e.count;a++)n.push(this.sceneModule.createObject(t.client.applicationKey).publicFacade());return n}}class hs{constructor(e){this.sceneModule=e}validateInput(e){if(e=e||{},!Array.isArray(e.components))throw Error("components must be an array");return e.components=e.components||[],e}async exec(e){for(const t of e.components)this.sceneModule.unregisterFactory(t.name,t.factory)}}class us{constructor(e,t){this.sensorModule=e,this.sensorMap=t}validateInput(e){if(!de(e.sensorId)||!this.sensorMap.has(e.sensorId))throw Error("dev error: this should not occur unless the sdk was not setup properly");if(!yt(e.show))throw Error(`Sensor.showDebug: 'show' was not a boolean; got ${e.show}`);return{sensorId:e.sensorId,show:e.show}}async exec(e){if(e.show){const t=this.sensorMap.get(e.sensorId);this.sensorModule.setDebugSensor(t)}else this.sensorModule.setDebugSensor(null)}}class ps{constructor(e){this.sensor=e,this.sourceMap=new Map,this.reverseSourceLookup=new Map,this.sourceId=0,this._data={}}get data(){return this._data}isItemEqual(e,t){return e.data[t].inRange===this.data[t].inRange&&e.data[t].inView===this.data[t].inView}update(){for(const[e,t]of this.sensor.readings){const n=this.addOrGetSourceId(e),a=this._data[n]||{};a.inRange=t.inRange,a.inView=t.inView,a.distanceSquared=t.distanceSq,a.direction=xa(t,"direction",a.direction),this._data[n]=a}for(const e in this.data){const t=this.reverseSourceLookup.get(e);t&&!this.sensor.readings.get(t)&&(this.sourceMap.delete(t),this.reverseSourceLookup.delete(e),delete this._data[e])}}clear(){this._data={}}addOrGetSourceId(e){const t=this.sourceMap.get(e);if(t)return t;const n="source-"+ ++this.sourceId;return this.sourceMap.set(e,n),this.reverseSourceLookup.set(n,e),n}}class ms{constructor(){this._data={origin:{x:0,y:0,z:0},forward:{x:0,y:0,z:-1}}}get data(){return this._data}equals(e){const t=1e-5;return Ve(this.data.origin,e.data.origin,t)&&Ve(this.data.forward,e.data.forward,t)}copy(e){Pe(this._data.origin,e.data.origin),Pe(this._data.forward,e.data.forward)}update(e){Pe(this._data.origin,e.frustum.origin),Pe(this._data.forward,e.frustum.forward)}}class gs{constructor(e,t,n,a,o){this.sdk=e,this.sensorFactories=t,this.sensors=n,this.sensorMap=a,this.removerMap=o}validateInput(e){if(!be(u.SensorType,e.type))throw Error(e.type+" is not a valid sensor type");return this.sensorFactories[e.type].validateInput(e)}async exec(e,t){const n=this.sensorFactories[e.type].create(e,this.sensors),a="sensor-"+ ++gs.nextSensorId;this.sensorMap.set(a,n);const o=F.create(new we(n),new fs,new G(ms)),s=this.sdk.addObservable(a,o),i=at.create(new we(n),new ys,new G(ps)),r=this.sdk.addCollection(a,i);return this.removerMap.set(a,{dispose(){s.dispose(),r.dispose()}}),{sensorId:a}}}gs.nextSensorId=0;class ws{validateInput(e){return e}create(e,t){return t.createCameraBoundSensor()}}class fs{create(e,t){return t.frustum.observe(new Is(e))}}class ys{create(e,t){return t.onReadingsUpdated(new Is(e))}}class Is{constructor(e){this.dependencyObserver=e}notify(){this.dependencyObserver.onChanged()}}class Ts{constructor(e,t){this.sensorMap=e,this.sourceMap=t}validateInput(e){if(!de(e.sensorId)||!this.sensorMap.has(e.sensorId))throw Error("dev error: this should not occur unless the sdk was not setup properly");if(!Dt(e.sourceIds,de))throw Error("dev error: this should not occur unless the sdk was not setup properly");for(const t of e.sourceIds)if(!this.sourceMap.has(t))throw Error("dev error: this should not occur unless the sdk was not setup properly");return{sensorId:e.sensorId,sourceIds:e.sourceIds}}async exec(e){const t=this.sensorMap.get(e.sensorId),n=e.sourceIds.map(this.sourceMap.get,this.sourceMap);t.addSource(...n)}}class Es{constructor(e,t){this.sensorMap=e,this.removerMap=t}validateInput(e){if(!de(e.sensorId)||!this.sensorMap.has(e.sensorId))throw Error("dev error: this should not occur unless the sdk was not setup properly");return{sensorId:e.sensorId}}async exec(e){this.removerMap.get(e.sensorId).dispose();this.sensorMap.get(e.sensorId).dispose()}}const vs={namespace:"Sensor",name:"createSensor",subRoutine:"sensor.create",args:[]},Cs={namespace:"Sensor",name:"createSource",subRoutine:"source.create",args:["type","options"]};class As{constructor(e,t,n){this.radius=1/0,this.type=e.SPHERE,this._volume=t}get origin(){return this._volume.origin}get volume(){return this._volume}describeVolume(){return{origin:this._volume.origin,radius:this._volume.radius}}}const Ss=Math.PI/180;class Ds{constructor(e,t,n){this.type=e.BOX,this._volume=t,this.eulerCache=new n.Euler,this.quaternionCache=new n.Quaternion}get origin(){return this._volume.origin}get volume(){return this._volume}describeVolume(){return{center:this._volume.center,size:this._volume.size,orientation:this.convertQuaternionToOrientation(this._volume.orientation)}}updateOrientation(e){this.eulerCache.set(e.pitch*Ss,e.yaw*Ss,e.roll*Ss,"YXZ"),this.quaternionCache.setFromEuler(this.eulerCache),this._volume.updateOrientation(this.quaternionCache)}convertQuaternionToOrientation(e){return this.eulerCache.setFromQuaternion(this.quaternionCache.set(e.x,e.y,e.z,e.w)).reorder("YXZ"),{yaw:this.eulerCache.y*Ss,pitch:this.eulerCache.x*Ss,roll:this.eulerCache.z*Ss}}}class bs{constructor(e,t,n){this.type=e.CYLINDER,this._volume=t}get origin(){return this._volume.origin}get volume(){return this._volume}describeVolume(){return{basePoint:this._volume.basePoint,height:this._volume.height,radius:this._volume.radius}}}function Os(e){if(e&&"object"==typeof e&&"yaw"in e&&"pitch"in e&&"roll"in e){const t=e;return ro(t.yaw)&&ro(t.pitch)&&ro(t.roll)}return!1}class Ms{constructor(e,t){this.sourceFactories=e,this.sourceMap=t}validateInput(e){if(!be(u.SourceType,e.type))throw Error(e.type+" is not a valid source type");return this.sourceFactories[e.type].validateInput(e)}async exec(e){const t=this.sourceFactories[e.type].create(e),n="source-"+ ++Ms.nextSourceId;return this.sourceMap.set(n,t),{sourceId:n,type:e.type,volume:t.describeVolume()}}}Ms.nextSourceId=0;class Ns{constructor(e,t,n){this.sourceType=e,this.sphereVolumeFactory=t,this.THREE=n}validateInput(e){if(ft(e.options)){if(e.options.origin&&!ho(e.options.origin))throw Error(`Source.Box: 'origin' was specified but was not a valid Vector3; got ${e.options.origin}`);if(e.options.radius&&(!ro(e.options.radius)||e.options.radius<0))throw Error(`Source.Sphere: 'radius' was specified but was not a positive number or Infinity; got ${e.options.radius}`)}const t=e.options||{};return{type:e.type,options:{origin:t.origin||{x:0,y:0,z:0},radius:t.hasOwnProperty("radius")?t.radius:1/0}}}create(e){const t=new As(this.sourceType,new this.sphereVolumeFactory,this.THREE),n=t.volume;return n.updateOrigin(e.options.origin),n.updateRadius(e.options.radius),t}}class Rs{constructor(e,t,n){this.sourceType=e,this.boxVolumeFactory=t,this.THREE=n}validateInput(e){if(ft(e.options)){if(e.options.center&&!ho(e.options.center))throw Error(`Source.Box: 'center' was specified but was not a valid Vector3; got ${e.options.center}`);if(e.options.size&&(!ho(e.options.size)||e.options.size.x<0||e.options.size.y<0||e.options.size.z<0))throw Error(`Source.Box: 'size' was specified but was not a valid Vector3; got ${e.options.size}`);if(e.options.orientation&&!Os(e.options.orientation))throw Error(`Source.Box: 'orientation' was specified but was not a valid Orientation; got ${e.options.orientation}`)}const t=e.options||{};return{type:e.type,options:{center:t.center||{x:0,y:0,z:0},size:t.size||{x:1/0,y:1/0,z:1/0},orientation:t.orientation||{yaw:0,pitch:0,roll:0}}}}create(e){const t=new Ds(this.sourceType,new this.boxVolumeFactory,this.THREE),n=t.volume;return n.updateCenter(e.options.center),n.updateDimensions(e.options.size),t.updateOrientation(e.options.orientation),t}}class Ps{constructor(e,t,n){this.sourceType=e,this.cylinderVolumeFactory=t,this.THREE=n}validateInput(e){if(ft(e.options)){if(e.options.basePoint&&!ho(e.options.basePoint))throw Error(`Source.Cylinder: 'basePoint' was specified but was not a valid Vector3; got ${e.options.basePoint}`);if(e.options.hasOwnProperty("height")&&(!ro(e.options.height)||e.options.height<0))throw Error(`Source.Cylinder: 'height' was specified but was not a positive number or Infinity; got ${e.options.height}`);if(e.options.hasOwnProperty("radius")&&(!ro(e.options.radius)||e.options.radius<0))throw Error(`Source.Cylinder: 'radius' was specified but was not a positive number or Infinity; got ${e.options.radius}`)}const t=e.options||{};return{type:e.type,options:{basePoint:t.basePoint||{x:0,y:0,z:0},height:t.hasOwnProperty("height")?t.height:1/0,radius:t.hasOwnProperty("radius")?t.radius:1/0}}}create(e){const t=new bs(this.sourceType,new this.cylinderVolumeFactory,this.THREE),n=t.volume;return n.updateBasePoint(e.options.basePoint),n.updateHeight(e.options.height),n.updateRadius(e.options.radius),t}}class Ls{constructor(e,t){this.sourceUpdaters=e,this.sourceMap=t}validateInput(e){if(!de(e.sourceId)||!this.sourceMap.has(e.sourceId))throw Error("dev error: this should not occur unless the sdk was not setup properly");const t=this.sourceMap.get(e.sourceId);return this.sourceUpdaters[t.type].validateInput(e)}async exec(e){const t=this.sourceMap.get(e.sourceId);this.sourceUpdaters[t.type].update(e,t)}}class xs{validateInput(e){if(!ft(e.options))throw Error("Source.Sphere.update: invalid options provided to .update");if(!ho(e.options.origin))throw Error(`Source.Sphere.update: 'origin' was specified but was not a valid Vector3; got ${e.options.origin}`);if(!ro(e.options.radius)||e.options.radius<0)throw Error(`Source.Sphere.update: 'radius' was specified but was not a positive number or Infinity; got ${e.options.radius}`);return e}update(e,t){const n=t.volume;n.updateOrigin(e.options.origin),n.updateRadius(e.options.radius),n.notify()}}class ks{validateInput(e){if(!ft(e.options))throw Error("Source.Box.update: invalid options provided to .update");if(e.options.center&&!ho(e.options.center))throw Error(`Source.Box.update: 'center' was specified but was not a valid Vector3; got ${e.options.center}`);if(e.options.size&&!(ho(e.options.size)&&e.options.size.x>=0&&e.options.size.y>=0&&e.options.size.z>=0))throw Error(`Source.Box.update: 'size' was specified but was not a valid Vector3; got ${JSON.stringify(e.options.size)}\n        ${"object"==typeof e}`);if(e.options.orientation&&!Os(e.options.orientation))throw Error(`Source.Box.update: 'orientation' was specified but was not a valid Orientation; got ${e.options.orientation}`);return e}update(e,t){const n=t.volume;n.updateCenter(e.options.center),n.updateDimensions(e.options.size),t.updateOrientation(e.options.orientation),n.notify()}}class _s{validateInput(e){if(!ft(e.options))throw Error("Source.Cylinder.update: invalid options provided to .update");if(e.options.basePoint&&!ho(e.options.basePoint))throw Error(`Source.Cylinder.update: 'basePoint' was specified but was not a valid Vector3; got ${e.options.basePoint}`);if(e.options.hasOwnProperty("height")&&(!ro(e.options.height)||e.options.height<0))throw Error(`Source.Cylinder.update: 'height' was specified but was not a positive number or Infinity; got ${e.options.height}`);if(e.options.hasOwnProperty("radius")&&(!ro(e.options.radius)||e.options.radius<0))throw Error(`Source.Cylinder: 'radius' was specified but was not a positive number or Infinity; got ${e.options.radius}`);return e}update(e,t){const n=t.volume;n.updateBasePoint(e.options.basePoint),n.updateHeight(e.options.height),n.updateRadius(e.options.radius),n.notify()}}function Us(e,t,n){const a=new Map,o=new Map;return function(s,i){!function(e,t,n,a){const o=new Map,s=oe.create(new we(t,n),new G(Ts)),i=oe.create(new we(t,o),new G(Es)),r=oe.create(we.extend(a,t),new G(us));e.addCommand("Sensor.addSource",s),e.addCommand("sensor.dispose",i),e.addCommand("sensor.showDebug",r);const c={[u.SensorType.CAMERA]:new ws},d=oe.create(we.extend(a,t,o),new G(gs,e,c));e.addCommandToInterface(Object.assign({},vs),d),e.addEnumToInterface({namespace:"Sensor",name:"SensorType",values:u.SensorType})}(s,a,o,i),function(e,t,n,a,o){const s=new we(t),i={[a.SPHERE]:new xs,[a.BOX]:new ks,[a.CYLINDER]:new _s},r=oe.create(s,new G(Ls,i));e.addCommand("Sensor.updateSource",r);const c={[u.SourceType.SPHERE]:new Ns(a,o.sphere,n),[u.SourceType.BOX]:new Rs(a,o.box,n),[u.SourceType.CYLINDER]:new Ps(a,o.cylinder,n)},d=oe.create(s,new G(Ms,c));e.addCommandToInterface(Object.assign({},Cs),d),e.addEnumToInterface({namespace:"Sensor",name:"SourceType",values:u.SourceType})}(s,o,e,t,n)}}var Vs;!function(e){e.FPS="stat.fps"}(Vs||(Vs={}));class Fs{constructor(e,t){this.perfStatInterval=1e3,this.elapsedTime=0,this.frameCount=0,this.lastCalcTime=0,this.fps=[0],this.waitForUpdate=e,this.perfStatInterval=t.tryGetProperty("perfInterval",this.perfStatInterval)}async calcAndBroadcast(e){await this.waitForUpdate();const t=Date.now(),n=t-this.lastCalcTime;this.lastCalcTime=t,this.elapsedTime+=n,this.frameCount++,this.elapsedTime>=this.perfStatInterval&&(this.fps[0]=1e3*this.frameCount/this.elapsedTime,this.elapsedTime=0,this.frameCount=0,e.broadcast(Vs.FPS,(()=>this.fps))),this.calcAndBroadcast(e)}}function Gs(e,t="",n=""){let a="sdkStorage/";return t&&t.length>0&&(a=a+t+"/"),n&&n.length>0&&(a=a+n+"/"),a+e}class Hs{constructor(e){this.modelData=e}validateInput(e){const t=null==e?void 0:e.key;if(!t||"string"!=typeof t)throw Error(t+" is not a valid key");return e}async exec(e){localStorage.setItem(Gs(e.key,this.modelData.model.sid),JSON.stringify(e.data))}}class Bs{constructor(e){this.modelData=e}validateInput(e){const t=null==e?void 0:e.key;if(!t||"string"!=typeof t)throw Error(t+" is not a valid key");return{key:e.key}}async exec(e){const t=localStorage.getItem(Gs(e.key,this.modelData.model.sid));return t?JSON.parse(t):null}}class zs{constructor(e){this.modelData=e}validateInput(e){let t=null==e?void 0:e.modelId;if(null==t)t=this.modelData.model.sid;else if("*"===t)t="";else if("string"!=typeof t)throw Error(t+" is not a valid key");return{modelId:t}}async exec(e){const t=Gs("",e.modelId),n={};for(const[e,a]of Object.entries(localStorage))e.startsWith(t)&&(n[e.slice(t.length)]=JSON.parse(a));return n}}class $s{constructor(e){this.modelData=e}validateInput(e){const t=null==e?void 0:e.key;if(!t||"string"!=typeof t)throw Error(t+" is not a valid key");return{key:e.key}}async exec(e){localStorage.removeItem(Gs(e.key,this.modelData.model.sid))}}class js{constructor(e,t){this.toSdkAlignmentMap={[e.ALIGNED]:p.Alignment.ALIGNED,[e.UNALIGNED]:p.Alignment.UNALIGNED},this.toSdkPlacementMap={[t.UNPLACED]:p.Placement.UNPLACED,[t.AUTO]:p.Placement.AUTO,[t.MANUAL]:p.Placement.MANUAL}}toSdkAlignment(e){return this.toSdkAlignmentMap[e]}toSdkPlacement(e){return this.toSdkPlacementMap[e]}}const Ws=Object.freeze({signedUrlDefaultExpireTime:24e4,signedUrlCheckInterval:1e4,signedUrlRefreshBuffer:15e3,visionTilingStartDate:new Date("8/26/2016"),visionTilingStartVersion:"1.1.407.13667",epsilon:1e-5,skyboxMeshGroup:"_group_skybox_",skysphereMeshGroup:"_group_skysphere_"});class qs{constructor(){this.currentSweep="",this.sweepTransitionActive=!1,this.currentMode=null,this.modeTransitionActive=!1}create(e,t,n){return new Ne(t.onChanged((()=>this.throttleSweepDataChanges(e,t))),n.onChanged((()=>this.throttleViewmodeDataChanges(e,n))))}throttleSweepDataChanges(e,t){const n=t.currentSweep||"",a=t.transition.active&&t.transition.to!==t.transition.from;this.currentSweep===n&&this.sweepTransitionActive===a||(this.currentSweep=n,this.sweepTransitionActive=a,e.onChanged())}throttleViewmodeDataChanges(e,t){const n=t.currentMode,a=t.transition.active;this.currentMode===n&&this.modeTransitionActive===a||(this.currentMode=n,this.modeTransitionActive=a,e.onChanged())}}class Ks{constructor(e,t){this.currentSweep=Object.assign(Object.assign({},Ks.empty),{floorInfo:Object.assign({},Ks.empty.floorInfo),neighbors:[],position:Object.assign({},Ks.empty.position),rotation:Object.assign({},Ks.empty.rotation)}),this.Viewmode=e.Viewmode,this.sweepPlacementConverter=t.sweepPlacementConverter}get data(){return this.currentSweep}equals(e){const t=Ws.epsilon;return this.currentSweep.id===e.data.id&&this.currentSweep.enabled===e.data.enabled&&this.currentSweep.alignmentType===e.data.alignmentType&&this.currentSweep.placementType===e.data.placementType&&this.currentSweep.floorInfo.id===e.data.floorInfo.id&&this.currentSweep.floorInfo.sequence===e.data.floorInfo.sequence&&this.compareNeighbors(e.currentSweep)&&Ve(this.currentSweep.position,e.data.position,t)&&Ve(this.currentSweep.rotation,e.data.rotation,t)}copy(e){this.copySweep(e.data)}update(e,t,n,a,o){const s=e.currentSweepObject;if(!s||e.transition.active||t.transition.active||!t.isInside()&&t.currentMode!==this.Viewmode.Outdoor)return void this.copySweep(Ks.empty);let i;try{i=n.getFloor(s.floorId||"")}catch(e){i={id:void 0,index:void 0}}const r=a.getIdForSweep(s);this.currentSweep.uuid=s.uuid,this.currentSweep.sid=r,this.currentSweep.id=r,this.currentSweep.enabled=s.enabled,this.currentSweep.alignmentType=this.sweepPlacementConverter.toSdkAlignment(s.alignmentType),this.currentSweep.placementType=this.sweepPlacementConverter.toSdkPlacement(s.placementType),this.currentSweep.neighbors=[...s.neighbours],Pe(this.currentSweep.position,s.position),Pe(this.currentSweep.rotation,s.rotation),this.currentSweep.alignmentType===p.Alignment.UNALIGNED&&this.currentSweep.placementType===p.Placement.UNPLACED?(this.currentSweep.floorInfo.id=void 0,this.currentSweep.floorInfo.sequence=void 0):(this.currentSweep.floorInfo.id=i.id?o.getIdFromCwfId(i.id):i.id,this.currentSweep.floorInfo.sequence=i.index)}copySweep(e){this.currentSweep.uuid=e.uuid,this.currentSweep.sid=e.sid,this.currentSweep.id=e.id,this.currentSweep.enabled=e.enabled,this.currentSweep.alignmentType=e.alignmentType,this.currentSweep.placementType=e.placementType,this.currentSweep.floorInfo.id=e.floorInfo.id,this.currentSweep.floorInfo.sequence=e.floorInfo.sequence,this.currentSweep.neighbors=[...e.neighbors],Pe(this.currentSweep.position,e.position),Pe(this.currentSweep.rotation,e.rotation)}compareNeighbors(e){const t=this.currentSweep.neighbors.length;for(let n=0;n<t;++n)if(this.currentSweep.neighbors[n]!==e.neighbors[n])return!1;return t===e.neighbors.length}}Ks.empty={uuid:"",sid:"",id:"",enabled:!1,alignmentType:p.Alignment.ALIGNED,placementType:p.Placement.UNPLACED,floorInfo:{id:void 0,sequence:void 0},neighbors:[],position:{x:0,y:0,z:0},rotation:{x:0,y:0,z:0}};class Ys{constructor(e,t,n,a){this.sweepData=n,this.sweepIdMap=a,this.NavigateToSweepCommand=e.NavigateToSweepCommand,this.issueCommand=t.issueCommand,this.cameraTransitionConverter=t.cameraTransitionConverter,this.conversion=t.conversionUtils}validateInputSweepId(e,t,n){if(!e||!de(e))throw Error(`Sweep.moveTo: 'sweep' was not a valid sweep id; got ${e}`);const a=n.getSweepForId(e);if(!a)throw Error(e+" does not map to a valid sweep in this model");return a.id}validateInputRotation(e){if(!e)return;const t=Error(`Sweep.moveTo: 'options.rotation' was specified but was not a valid Rotation; got ${e}`);if(e&&!ft(e))throw t;const n=e||{},a={x:co(n.x),y:co(n.y)};if(n.z&&(a.z=co(n.z)),!function(e){const t=e;return!!t&&!("object"!=typeof t||"z"in t&&!ro(t.z))&&ro(t.x)&&ro(t.y)}(a))throw t;return a}validateTransitionTime(e){if(!e)return;const t=Error(`Sweep.moveTo: options.transitionTime was specified but was not a valid, positive time; got ${e}`);if(!ro(e)&&!de(e))throw t;const n="number"==typeof e?e:co(e);if(!ro(n)||n<=0)throw t;return n}validateInput(e){const t=this.validateInputSweepId(e.sweep,this.sweepData,this.sweepIdMap),n=e.options||{},a=this.validateInputRotation(n.rotation),o=this.validateTransitionTime(n.transitionTime);if(n.transition&&!be(E,n.transition))throw Error(`Sweep.moveTo: options.transition was specified but was not a valid transition type; got ${n.transition}`);return{sweep:t,options:{rotation:a,transition:n.transition,transitionTime:o}}}async exec(e,t){const n=e.options.rotation?this.conversion.rotationToQuaternion(e.options.rotation):void 0,a=e.options.transition,o=e.options.transition?this.cameraTransitionConverter.fromSdkTransition(a):void 0,s=e.options.transitionTime;return await this.issueCommand(new this.NavigateToSweepCommand({sweep:e.sweep,rotation:n,transition:o,transitionTime:s}))}}class Xs{create(e,t){return t.onChanged((()=>e.onChanged()))}}class Zs{constructor(e,t,n,a,o,s){this.sweepData=n,this.floorsData=a,this.sweepIdMap=o,this.floorIdMap=s,this._data={},this.conversionUtils=e,this.sweepPlacementConverter=t}get data(){return this._data}isItemEqual(e,t){return!z(this.data[t],e.data[t])}update(){for(const e of this.sweepData.sweeps()){const{id:t,index:n}=e.floorId?this.floorsData.getFloor(e.floorId):{id:"",index:-1},a=t?this.floorIdMap.getIdFromCwfId(t):void 0,o=this.sweepIdMap.getIdFromCwfId(e.id),s=this.data[o]||{};s.uuid=e.uuid,s.sid=o,s.id=o,s.enabled=e.enabled,s.alignmentType=this.sweepPlacementConverter.toSdkAlignment(e.alignmentType),s.neighbors=e.neighbours.map((e=>this.sweepIdMap.getIdFromCwfId(e))),s.position=Qs(e,s.position),s.rotation=this.conversionUtils.quaternionToRotation(e.rotation,s.rotation),s.floorInfo=a?{id:a,sequence:n}:{id:"",sequence:-1},this.data[s.id]=s}for(const e in this.data){this.sweepIdMap.getSweepForId(e)||delete this.data[e]}}clear(){this._data={}}}function Qs(e,t){return(t=t||{}).x=e.position.x,t.y=e.position.y,t.z=e.position.z,t}class Js{constructor(e){this.sweepData=e}validateInput(e){return{invert:!!(null==e?void 0:e.invert)||!1}}async exec(e,t){return this.sweepData.getSweepIdMap(e.invert)}}class ei{constructor(e){this.sweepData=e}validateInput(e,t){const n=null==e?void 0:e.id;if(!n)throw Error("Sweep.GetLabelFromIdExecutor: Missing input 'id' field");if(!le(n))throw Error(`Sweep.GetLabelFromIdExecutor: provided ${null==e?void 0:e.id} is not a valid string`);return{id:n}}async exec(e,t){var n,a;let o=null===(n=this.sweepData.getSweep(e.id))||void 0===n?void 0:n.index;return void 0===o&&(o=null===(a=this.sweepData.getSweepByUuid(e.id))||void 0===a?void 0:a.index),void 0!==o?(o+1).toString():null}}class ti{constructor(e,t,n){this.sweepData=e,this.sweepViewData=t,this.sweepIdMap=n}validateInput(e){return{sweepIds:ai(e.sweepIds,this.sweepData,this.sweepIdMap)}}async exec(e,t){for(const t of e.sweepIds)oi(t,!0,this.sweepData,this.sweepViewData)}}class ni{constructor(e,t,n){this.sweepData=e,this.sweepViewData=t,this.sweepIdMap=n}validateInput(e){return{sweepIds:ai(e.sweepIds,this.sweepData,this.sweepIdMap)}}async exec(e,t){for(const t of e.sweepIds)oi(t,!1,this.sweepData,this.sweepViewData)}}function ai(e,t,n){if(!Array.isArray(e))throw Error();return e.map((e=>function(e,t,n){if(!de(e))throw Error(e+" is not a valid string input");const a=n.getSweepForId(e);if(!a)throw Error(e+" does not map to a valid sweep in this model");return a.id}(e,0,n)))}function oi(e,t,n,a){a.setVisible(e,t);const o=n.getSweep(e);o.enabled=t,o.commit()}class si{constructor(e,t,n){this.sweepData=n,this.SweepNeighborModifyCommand=e.SweepNeighborModifyCommand,this.issueCommand=t.issueCommand}validateInput(e,t){const n=null==e?void 0:e.sweepId;if(!n)throw Error("Sweep.AddSweepNeighborsExecutor: Missing input 'sweepId' field");if(!le(n))throw Error(`Sweep.AddSweepNeighborsExecutor: provided "${null==e?void 0:e.sweepId}" is not a valid string`);if(!this.sweepData.containsSweep(e.sweepId))throw Error(`Sweep.AddSweepNeighborsExecutor: provided sweep ID "${null==e?void 0:e.sweepId}" is not a valid V2 ID`);const a=this.sweepData;const o=null==e?void 0:e.toAdd;if(!o||!Dt(o,(function(e){const t=a.containsSweep(e);return le(e)&&t})))throw Error("Sweep.AddSweepNeighborsExecutor: Invalid list of neighbors to add");return{sweepId:n,toAdd:o}}async exec(e,t){const n=this.sweepData.getSweep(e.sweepId);return await this.issueCommand(new this.SweepNeighborModifyCommand(n.id,e.toAdd)),this.sweepData.getSweep(e.sweepId).neighbours}}class ii{constructor(e,t,n){this.sweepData=n,this.SweepNeighborModifyCommand=e.SweepNeighborModifyCommand,this.issueCommand=t.issueCommand}validateInput(e,t){const n=null==e?void 0:e.sweepId;if(!n)throw Error("Sweep.RemoveSweepNeighborsExecutor: Missing input 'sweep' field");if(!le(n))throw Error(`Sweep.RemoveSweepNeighborsExecutor: provided "${null==e?void 0:e.sweepId}" is not a valid string`);if(!this.sweepData.containsSweep(e.sweepId))throw Error(`Sweep.AddSweepNeighborsExecutor: provided sweep ID "${null==e?void 0:e.sweepId}" is not a valid V2 Sweep ID`);const a=this.sweepData;const o=null==e?void 0:e.toRemove;if(!o||!Dt(o,(function(e){const t=a.getSweep(n).neighbours.includes(e);return le(e)&&t})))throw Error("Sweep.RemoveSweepNeighborsExecutor: Invalid list of neighbors to remove.");return{sweepId:n,toRemove:o}}async exec(e,t){const n=this.sweepData.getSweep(e.sweepId);return await this.issueCommand(new this.SweepNeighborModifyCommand(n.id,[],e.toRemove)),this.sweepData.getSweep(e.sweepId).neighbours}}class ri{create(e,t){return t.onChanged({notify(){e.onChanged()}})}}class ci{constructor(e){this.attachmentRegistry=e,this._data={}}get data(){return this._data}isItemEqual(e,t){return this.data[t].id===e.data[t].id&&this.data[t].src===e.data[t].src&&this.data[t].type===e.data[t].type}update(){for(const[,e]of this.attachmentRegistry.descriptors()){const t=this._data[e.id]||{};t.id=e.id,t.src=e.src,t.type=e.type,this._data[e.id]=t}for(const e in this.data){this.attachmentRegistry.has(e)||delete this._data[e]}}clear(){this._data={}}}class di{constructor(e,t){this.mattertagData=e,this.attachmentRegistry=t,this.attachmentCounts=new Map}validateInput(e,t){de(e.tagId)||this.throw(se("tagId","string",e.tagId)),this.mattertagData.getTag(e.tagId)||this.throw(`${e.tagId} does not map to a valid tag`),Dt(e.attachmentIds,de)||this.throw(se("attachmentId","string or strings",e.tagId));for(const n of e.attachmentIds)this.attachmentRegistry.get(t.client.applicationKey,n)||this.throw(`${n} does not map to a valid attachment`);return{tagId:e.tagId,attachmentIds:e.attachmentIds}}async exec(e,t){for(const n of e.attachmentIds){const a=this.attachmentRegistry.get(t.client.applicationKey,n);a||this.throw(`${n} does not map to a valid attachment`);const o=this.attachmentCounts.get(a)||0;this.attachmentCounts.set(a,o+1);const s=this.mattertagData.getTag(e.tagId);a.category===va.UPLOAD?s.fileAttachments.push(a):a.category===va.EXTERNAL?s.externalAttachments.push(a):a.category===va.SANDBOX&&s.sandboxAttachments.push(Object.assign(Object.assign({},a),{src:a.src+`?id=${a.id}&parent=${e.tagId}&${o}`}))}this.mattertagData.commit()}throw(e){throw Error("Tag.attach: "+e)}}class li{constructor(e,t){this.mattertagData=e,this.attachmentRegistry=t}validateInput(e,t){de(e.tagId)||this.throw(se("tagId","string",e.tagId)),this.mattertagData.getTag(e.tagId)||this.throw(`${e.tagId} does not map to a valid tag`),Dt(e.attachmentIds,de)||this.throw(se("attachmentId","string or strings",e.tagId));for(const n of e.attachmentIds)this.attachmentRegistry.get(t.client.applicationKey,n)||this.throw(`${n} does not map to a valid attachment`);return{tagId:e.tagId,attachmentIds:e.attachmentIds}}async exec(e,t){for(const n of e.attachmentIds){const a=this.attachmentRegistry.get(t.client.applicationKey,n);if(!a)return void this.throw(`${n} does not map to a valid attachment`);const o=this.mattertagData.getTag(e.tagId);a.category===va.UPLOAD?hi(o.fileAttachments,a.id):a.category===va.EXTERNAL?hi(o.externalAttachments,a.id):a.category===va.SANDBOX&&hi(o.sandboxAttachments,a.id)}this.mattertagData.commit()}throw(e){throw Error("Tag.detach: "+e)}}function hi(e,t){const n=e.findIndex((e=>e.id===t));n>=0&&e.splice(n,1)}class ui{constructor(e){this.attachmentRegistry=e,this.log=new ne("tag.registerAttachment")}validateInput(e){const t=e.srcs;return Dt(t,le)||(Dt(t,this.validateDescriptor)&&this.log.warn("Using attachment descriptors is deprecated and will be removed when Early Access closes."),this.throw(se("descriptors","Array of AttachmentDescriptors or strings",t))),{srcs:t}}validateDescriptor(e,t){return ft(e)||this.throw(`descriptor #${t} is not an object`),de(e.src)||this.throw(`descriptor[${t}].src is not a string`),be(m.AttachmentType,e.type)&&e.type!==m.AttachmentType.UNKNOWN||this.throw(`descriptor[${t}].type is not a valid attachment type`),e.type===m.AttachmentType.SANDBOX&&this.throw(`descriptor[${t}] has type ${m.AttachmentType.SANDBOX} and should be registered using 'Tag.registerSandbox' instead`),!0}async exec(e,t){const n=[];for(const a of e.srcs){const e="string"==typeof a?await this.attachmentRegistry.addBySrc(t.client.applicationKey,a):this.attachmentRegistry.add(t.client.applicationKey,a);n.push(e)}return n}throw(e){throw Error("Tag.registerAttachment: "+e)}}const pi=new ne("mattertag-util");function mi(e,t,n,a){const o=a.meshSubGroupsFromPoint(e);let s=1/0,i="";for(const t of n.rooms())if(o.includes(t.meshSubgroup)){const n=a.meshGroups.rooms.get(t.meshSubgroup),o=e.y-((null==n?void 0:n.boundingBox.min.y)||-1/0);o<s&&(s=o,i=t.floorId)}try{return t.getFloor(i).index}catch(e){return pi.debug("Unable to deduce floor index from position; defaulting to floor 0"),0}}const gi=new ne("command.importTags");class wi{constructor(e,t,n,a,o,s,i,r,c){this.sdkLayer=e,this.THREE=t,this.commands=a,this.getTagsQuery=o,this.mattertagsData=s,this.floorsData=i,this.roomData=r,this.meshData=c,this.AddMattertagCommand=n.AddMattertagCommand,this.SelectLayerCommand=n.SelectLayerCommand}validateInput(e,t){return de(e.spaceSid)||this.throw(se("spaceSid","string",e.spaceSid)),{spaceSid:e.spaceSid}}async exec(e,t){const n=await this.getTagsQuery(e.spaceSid),a=[];if(!this.sdkLayer)return gi.warn("SDK Layers is not ready to be used."),[];await this.commands.issueCommand(new this.SelectLayerCommand(this.sdkLayer.id,!0));for(const t of n){const n=new this.THREE.Vector3,o=new this.THREE.Vector3;n.set(t.anchorPosition.x,t.anchorPosition.y,t.anchorPosition.z),o.set(t.stemVector.x,t.stemVector.y,t.stemVector.z);const s=o.length();o.normalize();const i=mi(n,this.floorsData,this.roomData,this.meshData),r=this.floorsData.getFloorAtIndex(i);r||this.throw(`Could not add Tag ${t.sid} due to invalid floor position floorIndex: ${i} spaceId ${e.spaceSid}`),a.push({id:t.sid,positionOptions:{position:t.anchorPosition,normal:o,floorId:r.id},standardOptions:{label:t.label,description:t.description,stemVisible:t.stemVisible,stemHeight:s,color:t.color,icon:t.icon}})}const o=await this.commands.issueCommand(new this.AddMattertagCommand(a));for(let e=0;e<n.length;e++){const t=n[e],a=this.mattertagsData.getTag(o[e]);if(a.enabled=t.enabled,t.fileAttachments)for(const e of t.fileAttachments)a.fileAttachments.push(e);if(t.externalAttachments)for(const e of t.externalAttachments)a.externalAttachments.push(e);for(const e of t.keywords)a.keywords.push(e)}return o}throw(e){throw Error("Tag.importTags: "+e)}}class fi{create(e,t){return t.onChanged((()=>e.onChanged()))}}class yi{constructor(e,t,n,a,o,s){this.THREE=e,this.mattertagData=t,this.tagsViewData=n,this.roomIdMap=a,this.roomData=o,this.meshData=s,this._data={}}get data(){return this._data}isItemEqual(e,t){return!z(this.data[t],e.data[t])}update(){const e=e=>{const t=(new this.THREE.Vector3).set(e.x,e.y,e.z);for(const e of this.roomData.rooms()){const n=this.meshData.meshGroups.rooms.get(e.meshSubgroup);if(n&&n.boundingBox.containsPoint(t))return e}};for(const t of this.tagsViewData.getOrderedTags()){const n=this.mattertagData.getTag(t.id),a=this.data[n.sid]||{};if(a.id=n.sid,a.anchorPosition=xa(n,"anchorPosition",a.anchorPosition),a.stemVector=xa(n,"stemVector",a.stemVector),a.stemVisible=n.stemVisible,a.label=n.label,a.description=n.description,a.color=ka(n,"color",a.color),a.keywords=xe(a.keywords||[],n.keywords),a.fontId=n.icon?n.icon.split("_")[2]:"",n.roomId)a.roomId=this.roomIdMap.getIdFromCwfId(n.roomId);else{const t=e(a.anchorPosition);t&&t.id&&(a.roomId=this.roomIdMap.getIdFromCwfId(t.id))}const o=n.fileAttachments.map((e=>e.id)),s=n.externalAttachments.map((e=>e.id)),i=n.sandboxAttachments.map((e=>e.id));a.attachments=[...o,...s,...i],this._data[n.sid]=a}for(const e in this.data){this.mattertagData.getTag(e)||delete this._data[e]}}clear(){this._data={}}}function Ii(e){if(e&&"object"==typeof e&&"r"in e&&"g"in e&&"b"in e){const t=e;return ro(t.r)&&ro(t.g)&&ro(t.b)}return!1}const Ti=new ne("command.add");class Ei{constructor(e,t,n,a,o,s,i,r,c,d,l,h,u){this.THREE=e,this.AddMattertagCommand=t.AddMattertagCommand,this.issueCommand=n.issueCommand,this.mattertagsData=a,this.tagAttach=o,this.tagEditIcon=s,this.tagEditOpacity=i,this.attachmentRegistry=r,this.iconMaps=c,this.floorsData=l,this.roomData=h,this.meshData=u}validateInput(e,t){const{descriptors:n}=e;return Dt(n,((e,n)=>this.validateDescriptor(e,n,t)))||this.throw("descriptors is not an array"),{descriptors:n}}validateDescriptor(e,t,n){var a,o,s;if(ft(e)||this.throw(`descriptor #${t} is not an object`),ho(e.anchorPosition)||this.throw(`descriptor[${t}].anchorPosition is not vector`),ho(e.stemVector)||this.throw(`descriptor[${t}].stemVector is not vector`),void 0===e.id||de(e.id)||this.throw(`descriptor[${t}].id is not a valid string`),void 0===e.stemVisible||yt(e.stemVisible)||this.throw(`descriptor[${t}].stemVisible is not a boolean`),void 0===e.label||le(e.label)||this.throw(`descriptor[${t}].label is not a string`),void 0===e.description||le(e.description)||this.throw(`descriptor[${t}].description is not a string`),void 0!==e.attachments){Dt(e.attachments,le)||this.throw(`descriptor[${t}].attachments is not an array of strings`);for(let a=0;a<e.attachments.length;++a){const o=e.attachments[a];this.attachmentRegistry.get(n.client.applicationKey,o)||this.throw(`descriptor[${t}].attachment[${a}] does not map to a valid attachment`)}}return void 0===e.color||Ii(e.color)||this.throw(`descriptor[${t}].color is not a Color`),void 0!==e.opacity&&(ro(e.opacity)||this.throw(`descriptor[${t}].opacity is not a number`),o=e.opacity,s=1,0<=o&&o<=s||(Ti.warn(`descriptor[${t}].opacity was clamped from ${e.opacity} to the range [0, 1]`),e.opacity=lo(e.opacity,0,1))),void 0===e.iconId||de(e.iconId)||this.throw(`descriptor[${t}].iconId is not a valid string`),e.id=n.client.getOrClaimId(e.id),e.id&&de(e.id)&&this.mattertagsData.getTag(e.id)&&this.throw(`descriptor[${t}].id (${e.id}) is already in use`),e.iconId&&!(null===(a=this.iconMaps.get(n.client.applicationKey))||void 0===a?void 0:a.imagePromise[e.iconId])&&this.throw(`descriptor[${t}].iconId (${e.iconId}) does not map to a registered icon`),!0}async exec(e,t){const n=[],a=[];for(let t=0;t<e.descriptors.length;t++){const o=new this.THREE.Vector3,s=new this.THREE.Vector3,i=e.descriptors[t];o.set(i.anchorPosition.x,i.anchorPosition.y,i.anchorPosition.z),s.set(i.stemVector.x,i.stemVector.y,i.stemVector.z);const r=s.length();s.normalize();const c=mi(o,this.floorsData,this.roomData,this.meshData),d=this.floorsData.getFloorAtIndex(c);d||this.throw(`Could not add Tag on invalid floor with index: ${c}`),n.push({id:i.id,positionOptions:{position:o,normal:s,floorId:d.id},standardOptions:{label:i.label,description:i.description,stemVisible:i.stemVisible,stemHeight:r,color:i.color}}),i.attachments&&a.push(t)}const o=await this.issueCommand(new this.AddMattertagCommand(n));for(let n=0;n<e.descriptors.length;n++){const a=o[n],s=e.descriptors[n];void 0!==s.opacity&&await this.tagEditOpacity.exec({id:a,opacity:s.opacity},t),void 0!==s.iconId&&this.tagEditIcon.exec({id:a,iconId:s.iconId},t),s.attachments&&this.tagAttach.exec({tagId:a,attachmentIds:s.attachments},t)}return o}throw(e){throw Error("Tag.add: "+e)}}function vi(e,t){return de(e)&&!!t.getTag(e)}class Ci{constructor(e,t){this.externals=e,this.mattertagsData=t}validateInput(e,t){var n;if(!vi(e.id,this.mattertagsData))throw Error(`${e.id} does not map to a valid Tag`);if(void 0!==e.options&&!ft(e.options))throw Error(se("options","object",e.options));return{id:e.id,options:{force:!!(null===(n=e.options)||void 0===n?void 0:n.force)}}}async exec(e,t){await this.externals.issueCommand(new this.externals.OpenTagCommand(e.id,{forceOpen:e.options.force}))}}var Ai,Si;!function(e){e.NOTE="note",e.TAG="tag",e.OBJECT="object"}(Ai||(Ai={}));class Di{constructor(e,t){this.externals=e,this.mattertagsData=t}validateInput(e,t){var n;if(!vi(e.id,this.mattertagsData))throw Error(`${e.id} does not map to a valid Tag`);if(void 0!==e.options&&!ft(e.options))throw Error(se("options","object",e.options));return{id:e.id,options:{force:!!(null===(n=e.options)||void 0===n?void 0:n.force)}}}async exec(e,t){await this.externals.issueCommand(new this.externals.DockAnnotationCommand(e.id,Ai.TAG,e.options.force))}}class bi{constructor(e,t,n){this.externals=e,this.mattertagsData=t,this.tagsViewData=n}validateInput(e,t){if(!vi(e.id,this.mattertagsData))throw Error(`${e.id} does not map to a valid Tag`);return{id:e.id}}async exec(e,t){var n;(null===(n=this.tagsViewData.openTagView)||void 0===n?void 0:n.id)===e.id&&await this.externals.issueCommand(new this.externals.CloseTagCommand)}}!function(e){e.MATTERTAG="mattertag",e.NOTE="note",e.OBJECT="object"}(Si||(Si={}));Si.MATTERTAG,Si.NOTE,Si.OBJECT;var Oi,Mi,Ni,Ri;!function(e){e.DEFAULT="default",e.HIGHLIGHTED="highlighted",e.DIMMED="dimmed"}(Oi||(Oi={})),function(e){e.IDLE="idle",e.CREATING="creating",e.PRESSING="pressing",e.PLACING="placing",e.PLACED="placed"}(Mi||(Mi={})),function(e){e.UP="up",e.UP_LEFT="up-left",e.UP_RIGHT="up-right",e.DOWN="down",e.DOWN_LEFT="down-left",e.DOWN_RIGHT="down-right",e.LEFT="left",e.RIGHT="right"}(Ni||(Ni={}));class Pi{constructor(){this.focusedPin=null,this.selectedPin=null,this.dockedAnnotation=null}create(e,t,n){return new Ne(t.onChanged((()=>this.throttlePinChanges(e,t))),n.onChanged((()=>this.throttleAnnotationChanges(e,n))))}throttlePinChanges(e,t){var n,a;const o=null!==(a=null===(n=t.focusedPin)||void 0===n?void 0:n.id)&&void 0!==a?a:null,s=t.selectedPinId;this.focusedPin===o&&this.selectedPin===s||(this.focusedPin=o,this.selectedPin=s,e.onChanged())}throttleAnnotationChanges(e,t){var n,a;const o=null!==(a=null===(n=t.dockedAnnotation)||void 0===n?void 0:n.id)&&void 0!==a?a:null;this.dockedAnnotation!==o&&(this.dockedAnnotation=o,e.onChanged())}}class Li{constructor(){this._data={hovered:null,selected:new Set,docked:null}}get data(){return this._data}equals(e){return this.data.hovered===e.data.hovered&&this.data.docked===e.data.docked&&(t=this.data.selected,n=e.data.selected,t.size===n.size&&[...t].every((e=>n.has(e))));var t,n}update(e,t){var n,a;const o=e.focusedPin,s=e.selectedPinId?e.getPin(e.selectedPinId):null,i=null!==(a=null===(n=t.dockedAnnotation)||void 0===n?void 0:n.id)&&void 0!==a?a:null;this._data.hovered=(null==o?void 0:o.pinType)===Si.MATTERTAG?o.id:null,this._data.selected.clear(),s&&s.pinType===Si.MATTERTAG&&this._data.selected.add(s.id),this._data.docked=i}copy(e){this._data.hovered=e.data.hovered,this._data.docked=e.data.docked,function(e,t){e.clear();for(const n of t)e.add(n)}(this._data.selected,e.data.selected)}}!function(e){e.SETUP="sandbox.setup",e.TO_SANDBOX="sandbox.to.sandbox",e.TO_CLIENT="sandbox.to.client"}(Ri||(Ri={}));Symbol.iterator;class xi{constructor(){this.values=new Map}add(e,t){this.getValuesAtKey(e).add(t)}remove(e,t){const n=this.values.get(e);null==n||n.delete(t)}removeKey(e){this.values.delete(e)}getValuesAtKey(e){const t=this.values.get(e)||new Set;return this.values.set(e,t),t}valuesPerKey(e){return this.getValuesAtKey(e).size}get keys(){return this.values.keys()}hasKey(e){return e in this.values}has(e,t){var n;return!!(null===(n=this.values.get(e))||void 0===n?void 0:n.has(t))}*[Symbol.iterator](){for(const[e,t]of this.values)for(const n of t)yield[e,n]}}class ki{constructor(){this.usedIds=new Set,this.usedIds.add(0)}generate(){let e;do{e=Math.floor(1e6*Math.random())}while(this.usedIds.has(e));return this.usedIds.add(e),e}isInUse(e){return this.usedIds.has(e)}}class _i{constructor(e,t,n,a){this.sdk=e,this.attachmentRegistry=t,this.tagViewData=n,this.iframeSrcDoc=a,this.idGenerator=new ki,this.sandboxSubs=new Set}validateInput(e){if(!e.html||"string"!=typeof e.html)throw Error(e.html+" is not valid html");if(!ro(e.clientId))throw Error("Unexpected error: unable to create iframe");const t=e.options||{},n={on:"on",off:"off",send:"send",tag:"tag",docked:"docked"};t.globalVariableMap&&(de(t.globalVariableMap.on)&&(n.on=t.globalVariableMap.on),de(t.globalVariableMap.off)&&(n.off=t.globalVariableMap.off),de(t.globalVariableMap.send)&&(n.send=t.globalVariableMap.send),de(t.globalVariableMap.tag)&&(n.tag=t.globalVariableMap.tag),de(t.globalVariableMap.docked)&&(n.docked=t.globalVariableMap.docked));const a={w:0,h:0};if(t.size){if(!(ro(t.size.w)&&t.size.w>=0&&ro(t.size.h)&&t.size.h>=0))throw Error(se("options.size","Size",t.size));a.w=t.size.w,a.h=t.size.h}return{clientId:e.clientId,html:e.html,options:{name:t.name||"",globalVariableMap:n,size:a}}}async exec(e,t){const n=this.idGenerator.generate(),a=e=>{const a=a=>{a.source===e&&a.data.sandboxId===n&&this.sdk.sendPrivateMessage(Ri.TO_CLIENT,a.data,t.client.id)};window.addEventListener("message",a),this.sandboxSubs.add({dispose(){window.removeEventListener("message",a)}})},o=e=>{const a=this.sdk.addPrivateMessageHandler({clientId:t.client.id,messageType:Ri.TO_SANDBOX,onMessage(t){t.sandboxId===n&&e.postMessage(t,"*")}});this.sandboxSubs.add(a)},[s]=this.attachmentRegistry.addSandbox(t.client.applicationKey,{name:e.options.name,srcDoc:this.iframeSrcDoc,sandboxLoadedHandler:(t,s)=>{var i;const r=t.contentWindow;for(const e of this.sandboxSubs)e.dispose();this.sandboxSubs.clear(),a(r),o(r),r.postMessage({type:Ri.SETUP,sandboxId:n,customHTML:e.html,parentTag:s,docked:(null===(i=this.tagViewData.dockedAnnotation)||void 0===i?void 0:i.id)===s,globalVariableMap:e.options.globalVariableMap},"*")},size:e.options.size});return{sandboxId:n,attachmentId:s}}}class Ui{constructor(e,t,n,a,o){this.externalTypes=e,this.externalCommands=t,this.pinViewData=n,this.tagsViewData=a,this.annotationsViewData=o}validateInput(e,t){const n=e.id;de(n)&&this.pinViewData.getPin(n)||this.throw(`${e.id} does not map to a valid tag`);const a={opening:!1,navigating:!1,docking:!1,sharing:!1};return ft(e.allow)&&(a.opening=!!e.allow.opening,a.navigating=!!e.allow.navigating,a.docking=!!e.allow.docking,a.sharing=!!e.allow.sharing),{id:n,allow:a}}async exec(e,t){var n,a;const o=this.pinViewData.getPin(e.id);o?(this.annotationsViewData.updateCapabilities(o.id,{dock:e.allow.docking,preview:e.allow.opening,share:e.allow.sharing}),this.tagsViewData.updateCapabilities(o.id,{focus:!!e.allow.navigating}),(!e.allow.docking&&(null===(n=this.annotationsViewData.dockedAnnotation)||void 0===n?void 0:n.id)===e.id||!e.allow.opening&&(null===(a=this.annotationsViewData.selectedAnnotation)||void 0===a?void 0:a.id)===e.id)&&this.externalCommands.issueCommand(new this.externalTypes.CloseAnnotationCommand(o.id,Ai.TAG))):this.throw(`${e.id} does not map to a valid tag`)}throw(e){throw Error("Tag.allowAction: "+e)}}class Vi{constructor(e,t,n){this.mattertagData=n,this.EditMattertagCommand=e.EditMattertagCommand,this.issueCommand=t.issueCommand}validateInput(e,t){const{id:n}=e;return de(n)||this.throw(se("id","string",n)),this.mattertagData.getTag(n)||this.throw(`${n} does not map to a valid tag`),{id:n,properties:this.validateProperties(e.properties)}}validateProperties(e){ft(e)||this.throw("'properties' input is not an object");const{label:t,description:n}=e;return void 0===t||le(t)||this.throw(se("label","string (or undefined)",t)),void 0===n||le(n)||this.throw(se("description","string (or undefined)",n)),{label:t,description:n}}async exec(e,t){try{await this.issueCommand(new this.EditMattertagCommand(e.id,{label:e.properties.label,description:e.properties.description}))}catch(e){this.throw(e)}}throw(e){throw Error("Tag.editBillboard: "+e)}}class Fi{constructor(e,t,n,a){this.THREE=e,this.EditMattertagCommand=t.EditMattertagCommand,this.issueCommand=n.issueCommand,this.mattertagData=a}validateInput(e,t){return de(e.id)||this.throw(se("id","string",e.id)),this.mattertagData.getTag(e.id)||this.throw(`${e.id} does not map to a valid tag`),Ii(e.color)||this.throw(se("color","Color",e.color)),{id:e.id,color:e.color}}async exec(e,t){try{const t=(new this.THREE.Color).setRGB(e.color.r,e.color.g,e.color.b);await this.issueCommand(new this.EditMattertagCommand(e.id,{color:t}))}catch(e){this.throw(e)}}throw(e){throw Error("Tag.editColor: "+e)}}class Gi{constructor(e,t,n,a,o,s,i){this.THREE=n,this.isMpFontId=a,this.mattertagData=o,this.pinsModule=s,this.iconMaps=i,this.UpdatePinCommand=e.UpdatePinCommand,this.issueCommand=t.issueCommand}validateInput(e,t){var n;de(e.id)||this.throw(se("id","string",e.id));this.mattertagData.getTag(e.id)||this.throw(`${e.id} does not map to a valid tag`),de(e.iconId)||this.throw(se("iconId","string",e.iconId));const a=this.isMpFontId(e.iconId);return(null===(n=this.iconMaps.get(t.client.applicationKey))||void 0===n?void 0:n.imagePromise[e.iconId])||a||this.throw(`${e.iconId} does not map to a registered icon or font id`),{id:e.id,iconId:e.iconId}}async exec(e,t){const{id:n,iconId:a}=e,o=this.iconMaps.get(t.client.applicationKey);if(e.iconId in o.imagePromise){let e;try{e=await o.imagePromise[a]}catch(e){this.throw(e)}const t=o.map[a],s=e.naturalWidth/e.naturalHeight,i=new this.THREE.Vector3;s>1?i.set(s,1,1):i.set(1,1/s,1),this.pinsModule.pinRenderer.setPinRenderOverrides(n,t,i)}else{this.mattertagData.getTag(e.id).icon=e.iconId,await this.issueCommand(new this.UpdatePinCommand(e.id,Si.MATTERTAG,{icon:e.iconId}))}}throw(e){throw Error("Tag.editIcon: "+e)}}class Hi{constructor(e,t,n,a,o){this.cwfTypes=e,this.closeTag=n,this.mattertagData=a,this.annotationsViewData=o,this.issueCommand=t.issueCommand}validateInput(e,t){le(e.id)&&this.mattertagData.getTag(e.id)||this.throw(`${e.id} does not map to a valid tag`),e.stemParams&&ft(e.stemParams)||this.throw("invalid stem parameters objeect");const{stemHeight:n,stemVisible:a}=e.stemParams;return void 0===n||ro(n)||this.throw(se("stemHeight","number",e.id)),void 0===a||yt(a)||this.throw(se("stemVisibility","boolean",e.id)),{id:e.id,stemParams:{stemHeight:n,stemVisible:a}}}async exec(e,t){var n;const{stemHeight:a,stemVisible:o}=e.stemParams,s={};void 0!==a&&(s.stemHeight=a,(null===(n=this.annotationsViewData.dockedAnnotation)||void 0===n?void 0:n.id)!==e.id&&this.closeTag.exec({id:e.id},t)),void 0!==o&&(s.stemVisible=o),await this.issueCommand(new this.cwfTypes.EditMattertagCommand(e.id,s))}throw(e){throw Error("Tag.editStem: "+e)}}class Bi{constructor(e,t,n,a){this.ChangePinOpacityScaleCommand=e.ChangePinOpacityScaleCommand,this.PinType=e.PinType,this.issueCommand=t.issueCommand,this.mattertagData=n,this.pinsViewData=a}validateInput(e,t){return de(e.id)||this.throw(se("id","string",e.id)),this.mattertagData.getTag(e.id)||this.throw(`${e.id} does not map to a valid tag`),(!ro(e.opacity)||e.opacity<0||e.opacity>1)&&this.throw("'opacity' should be a number in the range of [0, 1]"),{id:e.id,opacity:e.opacity}}async exec(e,t){await this.waitForTagView(e.id),await this.issueCommand(new this.ChangePinOpacityScaleCommand(e.id,this.PinType.MATTERTAG,e.opacity))}async waitForTagView(e){if(!this.pinsViewData.getPin(e))return new Promise((t=>{const n=this.pinsViewData.onPinUpdate({onAdded(a,o){o===e&&(n.cancel(),t())}})}))}throw(e){throw Error("Tag.editOpacity: "+e)}}class zi{constructor(e,t,n,a,o,s){this.closeTag=n,this.mattertagData=a,this.annotationsViewData=o,this.roomIdMap=s,this.cwfTypes=e,this.issueCommand=t.issueCommand}validateInput(e){return de(e.id)&&this.mattertagData.getTag(e.id)||this.throw(`${e.id} does not map to a valid tag`),{id:e.id,options:this.validateOptions(e.options)}}validateOptions(e){return e&&ft(e)||this.throw("invalid options provided"),{anchorPosition:this.validateVector3(e.anchorPosition,"`anchorPosition` was provided but not a valid Vector3"),stemVector:this.validateVector3(e.stemVector,"`stemVector` was provided, but not a valid Vector3"),roomId:this.validateRoomId(e.roomId)}}validateVector3(e,t){return void 0===e||ho(e)||this.throw(t),e}validateRoomId(e){return void 0===e||de(e)||this.throw(se("roomId","string (or undefined)",e)),e&&!this.roomIdMap.getRoomForId(e)&&this.throw(`${e} does not map to a valid room`),e}async exec(e,t){var n;const{anchorPosition:a,stemVector:o}=e.options,s=this.mattertagData.getTag(e.id),i=e.options.roomId&&this.roomIdMap.getRoomForId(e.options.roomId),r=i?i.floorId:s.floorId,c=a?s.anchorPosition.clone().set(a.x,a.y,a.z):s.anchorPosition,d=o?s.stemVector.clone().set(o.x,o.y,o.z):s.stemVector,l=o?d.length():s.stemHeight;d.normalize();const h={stemHeight:l},u={position:c,normal:d,floorId:r};(a||o)&&(null===(n=this.annotationsViewData.dockedAnnotation)||void 0===n?void 0:n.id)!==e.id&&await this.closeTag.exec({id:e.id},t),await this.issueCommand(new this.cwfTypes.EditMattertagCommand(e.id,h,void 0,u))}throw(e){throw Error("Tag.editPosition: "+e)}}class $i{constructor(e,t){this.cwfTypes=e,this.issueCommand=t.issueCommand}validateInput(e){const t=e.ids;return Dt(t,de)||this.throw("'ids' is expected to be a series of strings"),{ids:t}}async exec(e){const t=[];for(const n of e.ids)await this.issueCommand(new this.cwfTypes.CloseAnnotationCommand(n,Ai.TAG)),await this.issueCommand(new this.cwfTypes.RemoveMattertagCommand(n)),t.push(n);return t}throw(e){throw Error("Tag.remove: "+e)}}class ji{constructor(e,t){this.mattertagData=e,this.pinsModule=t}validateInput(e,t){return de(e.id)||this.throw(se("id","string",e.id)),this.mattertagData.getTag(e.id)||this.throw(`${e.id} does not map to a valid tag`),{id:e.id}}async exec(e,t){const{id:n}=e;this.pinsModule.pinRenderer.setPinRenderOverrides(n,null,null)}throw(e){throw Error(`Tag.resetIcon: ${e}`)}}Object.freeze({colors:["#d44441","#f44336","#e91e63","#f78da7","#9c4b92","#673ab7","#03687d","#03a9f4","#00bcd4","#417505","#51a868","#37d67a","#cddc39","#fbcd00","#ffac17","#ff6900","#abb8c3","#607d8b"]});class Wi{constructor(e,t){this.setting=e,this.settingsData=t}validateInput(e){if(void 0===e.enable&&(e.enable=!this.settingsData.tryGetProperty(this.setting,!0)),!yt(e.enable))throw Error(se("enable","boolean or undefined",e.enable));return{enable:e.enable}}async exec(e){this.settingsData.setProperty(this.setting,e.enable)}}const qi=new ne("command.saveToLayer");class Ki{constructor(e,t,n,a,o){this.sdkLayer=e,this.remove=a,this.mattertagsData=o,this.AddUserLayerCommand=t.AddUserLayerCommand,this.SaveNewMattertagCommand=t.SaveNewMattertagCommand,this.UploadAttachmentsCommand=t.UploadAttachmentsCommand,this.EmbedMediaCommand=t.EmbedMediaCommand,this.issueCommand=n.issueCommand}validateInput(e,t){return{}}async exec(e,t){if(null===this.sdkLayer)return qi.warn("Layers must be enabled to use Tag.saveToLayer."),[];const n=[];for(const e of this.mattertagsData)e.layerId===this.sdkLayer.id&&n.push(e);if(0===n.length)return[];await this.issueCommand(new this.AddUserLayerCommand(`Imported ${Date.now()}`,!1));const a=[];for(const e of n)if(e.layerId===this.sdkLayer.id){let t=fe(11);for(;this.mattertagsData.getTag(t);)t=fe(11);const n=[];if(e.fileAttachments)for(const a of e.fileAttachments){const e=await fetch(a.url.getCurrentValue()),o=await e.blob(),s=new File([o],a.filename||"image.jpg",{type:o.type}),i=await this.issueCommand(new this.UploadAttachmentsCommand(t,jn.MATTERTAG,[s]));i[0].attachment&&n.push(i[0].attachment)}e.fileAttachments.length=0,n.forEach((t=>{e.fileAttachments.push(t)}));let o=null;e.externalAttachments&&e.externalAttachments.length>0&&(o=await this.issueCommand(new this.EmbedMediaCommand(t,jn.MATTERTAG,e.externalAttachments.get(0).src)));try{await this.issueCommand(new this.SaveNewMattertagCommand(t,{position:e.anchorPosition,normal:e.anchorNormal,roomId:e.roomId,floorId:e.floorId,color:Object.assign({},e.color),description:e.description,label:e.label,stemHeight:e.stemHeight,stemVisible:e.stemVisible,enabled:e.enabled,keywords:e.keywords,icon:e.icon},n,o)),a.push(e)}catch(n){qi.warn(`Could not save tag sid:${t} label: ${e.label}}, skipping it.`)}}return await this.remove.exec({ids:a.map((e=>e.sid))},t),a.map((e=>e.sid))}}function Yi(e,t,n,a,o,s,i,r,c,d,l){const{mattertagData:h,pinsModule:u,meshData:p,floorData:g,pinViewData:w,annotationsViewData:f,tagsViewData:y,roomData:I,roomIdMap:T,settingsData:E}=l;e.addEnumToInterface({namespace:"Tag",name:"AttachmentType",values:m.AttachmentType});const v=function(e,t){const n=at.create(t,new ri,new G(ci));return e.addCollectionToInterface({namespace:"Tag",name:"attachments"},n),n}(e,new we(s)),C=function(e,t){const n=oe.create(t,new G(ui));return e.addAsyncCommandToInterface({namespace:"Tag",name:"registerAttachment",args:["srcs"],varArg:!0},n),n}(e,new we(s)),A=function(e,t){const n=oe.create(t,new G(_i,e));return e.addAsyncCommandToInterface({namespace:"Tag",name:"registerSandbox",args:["html","options"],subRoutine:"tag.registerSandbox"},n),n}(e,new we(s,f,i)),S=function(e,t){const n=oe.create(t,new G(di));return e.addAsyncCommandToInterface({namespace:"Tag",name:"attach",args:["tagId","attachmentIds"],varArg:!0,options:{replay:!0}},n),n}(e,new we(h,s)),D=function(e,t){const n=oe.create(t,new G(li));return e.addAsyncCommandToInterface({namespace:"Tag",name:"detach",args:["tagId","attachmentIds"],varArg:!0,options:{replay:!0}},n),n}(e,new we(h,s)),b=function(e,t){const n=F.create(t,new Pi,new G(Li));return e.addObservableToInterface({namespace:"Tag",name:"openTags"},n),n}(e,new we(w,f)),O=function(e,t,n){const a=at.create(n,new fi,new G(yi,t));return e.addCollectionToInterface({namespace:"Tag",name:"data",elementFactory:"tag.data"},a),a}(e,t,new we(h,y,T,I,p)),M=function(e,t,n,a){const o=oe.create(a,new G(Ci,Object.assign(Object.assign({},t),n)));return e.addAsyncCommandToInterface({namespace:"Tag",name:"open",args:["id","options"]},o),o}(e,n,a,new we(h,y)),N=function(e,t,n,a){const o=oe.create(a,new G(Di,Object.assign(Object.assign({},t),n)));return e.addAsyncCommandToInterface({namespace:"Tag",name:"dock",args:["id","options"]},o),o}(e,n,a,new we(h,y)),R=function(e,t,n,a){const o=oe.create(a,new G(bi,Object.assign(Object.assign({},t),n)));return e.addAsyncCommandToInterface({namespace:"Tag",name:"close",args:["id"]},o),o}(e,n,a,new we(h,y)),P=function(e,t,n,a){const o=oe.create(a,new G(Ui,t,n));return e.addAsyncCommandToInterface({namespace:"Tag",name:"allowAction",args:["id","allow"],options:{replay:!0}},o),o}(e,n,a,new we(w,y,f)),L=function(e,t,n,a){const o=oe.create(a,new G(Vi,t,n));return e.addAsyncCommandToInterface({namespace:"Tag",name:"editBillboard",args:["id","properties"],options:{replay:!0}},o),o}(e,n,a,new we(h)),x=function(e,t,n,a,o){const s=oe.create(o,new G(Fi,t,n,a));return e.addAsyncCommandToInterface({namespace:"Tag",name:"editColor",args:["id","color"],options:{replay:!0}},s),s}(e,t,n,a,new we(h)),k=function(e,t,n,a,o,s){const i=oe.create(s,new G(Gi,t,n,a,o));return e.addAsyncCommandToInterface({namespace:"Tag",name:"editIcon",args:["id","iconId"],options:{replay:!0}},i),i}(e,n,a,t,d,new we(h,u,o,y)),_=function(e,t,n,a){const o=oe.create(a,new G(Bi,t,n));return e.addAsyncCommandToInterface({namespace:"Tag",name:"editOpacity",args:["id","opacity"],options:{replay:!0}},o),o}(e,n,a,new we(h,w)),U=function(e,t,n,a){const o=oe.create(a,new G(Hi,t,n));return e.addAsyncCommandToInterface({namespace:"Tag",name:"editStem",args:["id","stemParams"],options:{replay:!0}},o),o}(e,n,a,new we(R,h,f)),V=function(e,t,n,a){const o=oe.create(a,new G(zi,t,n));return e.addAsyncCommandToInterface({namespace:"Tag",name:"editPosition",args:["id","options"],options:{replay:!0}},o),o}(e,n,a,new we(R,h,f,T)),H=function(e,t,n,a){const o=oe.create(a,new G($i,t,n));return e.addAsyncCommandToInterface({namespace:"Tag",name:"remove",args:["ids"],varArg:!0,options:{replay:!0}},o),o}(e,n,a,new we),B=function(e,t){const n=oe.create(t,new G(ji));return e.addAsyncCommandToInterface({namespace:"Tag",name:"resetIcon",args:["id"],options:{replay:!0}},n),n}(e,new we(h,u)),z=function(e,t,n,a,o){const s=oe.create(o,new G(Ei,t,n,a));return e.addAsyncCommandToInterface({namespace:"Tag",name:"add",args:["descriptors"],varArg:!0,options:{replay:!0}},s),s}(e,t,n,a,new we(h,S,k,_,s,o,y,g,I,p)),$=function(e,t,n,a,o,s,i){const r=oe.create(s,new G(wi,t,n,a,o,i));return e.addAsyncCommandToInterface({namespace:"Tag",name:"importTags",args:["spaceSid"],varArg:!1,origins:ye,options:{replay:!1}},r),r}(e,c,t,n,a,new we(h,g,I,p),r),j=function(e,t,n,a,o,s){const i=oe.create(s,new G(Ki,t,a,o));return e.addAsyncCommandToInterface({namespace:"Tag",name:"saveToLayer",args:[],varArg:!1,origins:ye,options:{replay:!0}},i),i}(e,c,0,n,a,new we(H,h)),W=new we(E);return{attachments:v,registerAttachment:C,registerSandbox:A,attach:S,importTags:$,detach:D,data:O,openTags:b,add:z,open:M,dock:N,close:R,allowAction:P,editBillboard:L,editColor:x,editIcon:k,editOpacity:_,editStem:U,editPosition:V,remove:H,resetIcon:B,toggleDocking:function(e,t){const n=oe.create(t,new G(Wi,"TAG_BILLBOARD_DOCK"));return e.addAsyncCommandToInterface({namespace:"Tag",name:"toggleDocking",args:["enable"]},n),n}(e,W),toggleNavControls:function(e,t){const n=oe.create(t,new G(Wi,"TAG_NAV_OVERLAY"));return e.addAsyncCommandToInterface({namespace:"Tag",name:"toggleNavControls",args:["enable"]},n),n}(e,W),toggleSharing:function(e,t){const n=oe.create(t,new G(Wi,"TAG_BILLBOARD_SHARE"));return e.addAsyncCommandToInterface({namespace:"Tag",name:"toggleSharing",args:["enable"]},n),n}(e,W),saveToLayer:j}}class Xi{constructor(e,t){this.tagsViewData=e,this.layersData=t}validateInput(e,t){return{}}async exec(e,t){const n={};for(const e of this.tagsViewData.getCollection()){const t=this.layersData.getLayer(e.layerId);n[e.id]=!!(null==t?void 0:t.toggled)||!e.layerId}return n}}class Zi{validateInput(e){if(!de(e.arg0))throw Error(`Stat.test: arg0 was specified but was not a valid string; got ${e.arg0}`);return e}async exec(e,t){return e.arg0}}const Qi=new ne("Subscription");class Ji{constructor(e,t,n=!1,a=""){this.startSubscription=e,this.endSubscription=t,this.id=a,this.isSubbed=!1,n&&this.renew()}renew(){this.isSubbed?Qi.debugWarn(`Duplicate subscription renew ${this.id}`):(this.startSubscription(),this.isSubbed=!0)}cancel(){this.isSubbed&&(this.endSubscription(),this.isSubbed=!1)}get active(){return this.isSubbed}}function er(e,t,n=!0,a=""){return new Ji(e,t,n,a)}class tr{create(){return er((()=>{}),(()=>{}))}}class nr{constructor(){this._data={}}get data(){return this._data}isItemEqual(e,t){const n=this.data[t],a=e.data[t];return n.test===a.test}update(){}clear(){this._data={}}}class ar{validateInput(e){return e}async exec(e,t){}}class or{create(e){return er((()=>{}),(()=>{}))}}class sr{constructor(){this._data={testString:"hello"}}get data(){return this._data}equals(e){return this._data.testString===e.data.testString}copy(e){this._data.testString=e.data.testString}update(){}}function ir(e,t){const{tagsViewData:n,layersData:a}=t,o=function(e,t){const n=at.create(t,new tr,new G(nr));return e.addCollectionToInterface({namespace:"Test",name:"directCollection",sdkTypes:[Co.DIRECT],origins:ye},n),e.addCollectionToInterface({namespace:"Test",name:"postMessageCollection",sdkTypes:[Co.POSTMESSAGE],origins:ye},n),n}(e,we.none),s=function(e,t){const n=F.create(t,new or,new G(sr));return e.addObservableToInterface({namespace:"Test",name:"directObservable",sdkTypes:[Co.DIRECT],origins:ye},n),e.addObservableToInterface({namespace:"Test",name:"postMessageObservable",sdkTypes:[Co.POSTMESSAGE],origins:ye},n),n}(e,we.none),i=function(e,t){const n=oe.create(t,new G(Xi));return e.addAsyncCommandToInterface({namespace:"Test",name:"getTagVisibility",args:[],origins:ye},n),n}(e,new we(n,a));return function(e,t){const n=oe.create(t,new G(Zi));e.addCommandToInterface({namespace:"Test",name:"echo",args:["arg0"],origins:ye},n),e.addAsyncCommandToInterface({namespace:"Test",name:"echoAsync",args:["arg0"],origins:ye},n),e.addCommandToInterface({namespace:"Test.Sub",name:"echo",args:["arg0"],origins:ye},n),e.addAsyncCommandToInterface({namespace:"Test.Sub",name:"echoAsync",args:["arg0"],origins:ye},n),e.addCommandToInterface({namespace:"Test.Sub.Sub2",name:"echo",args:["arg0"],origins:ye},n),e.addAsyncCommandToInterface({namespace:"Test.Sub.Sub2",name:"echoAsync",args:["arg0"],origins:ye},n)}(e,we.none),function(e,t){const n=oe.create(t,new G(ar));e.addCommandToInterface({namespace:"Test",name:"directCommand",args:[],sdkTypes:[Co.DIRECT],varArg:!1,origins:ye},n),e.addCommandToInterface({namespace:"Test",name:"postMessageCommand",args:[],sdkTypes:[Co.POSTMESSAGE],varArg:!1,origins:ye},n)}(e,we.none),{collection:o,observable:s,visibleTagsCheckCommand:i}}const rr=new ne("sdk: tours");class cr{constructor(e,t){this.tourControls=e,this.tourData=t}validateInput(e){return void 0!==e.index&&isNaN(e.index)&&(rr.warn(e.index,"is not a valid number"),e.index=void 0),void 0!==e.steps&&isNaN(e.steps)&&(rr.warn(e.steps,"is not a valid number"),e.steps=void 0),e}async exec(e){if(0===this.tourData.getSnapshotCount())throw Error("No tour data found");if(!this.tourControls.canChangeTourLocation())throw Error("TourStart ignored, cannot change location at this time, another transition is active");try{this.tourControls.startTour(e.index,e.steps,e.loop)}catch(e){throw Error(`Error occurred while starting tour - ${e}`)}}}class dr{constructor(e){this.tourControls=e}validateInput(e){return e}async exec(e){try{await this.tourControls.stopTour()}catch(e){throw Error(`Error occurred while stopping tour - ${e}`)}}}class lr{constructor(e,t){this.tourControls=e,this.tourData=t}validateInput(e){const t=e.index;if(!ro(t))throw Error(t+" is not a valid tour snapshot index");if(t<0||t>this.tourData.getSnapshotCount())throw Error(`${t} is outside of the range of tour snapshots: [0 , ${this.tourData.getSnapshotCount()-1}]`);return{index:t}}async exec(e){if(0===this.tourData.getSnapshotCount())throw Error("No tour data found");if(!this.tourControls.canChangeTourLocation())throw Error("TourStep ignored, cannot change location at this time, another transition is active");try{this.tourControls.tourGoTo(e.index)}catch(e){throw Error(`Error occurred while jumping to new tour location - ${e}`)}}}class hr{constructor(e,t,n){this.forward=e,this.tourControls=t,this.tourData=n}validateInput(e){return e}async exec(e){if(0===this.tourData.getSnapshotCount())throw Error("No tour data found");if(!this.tourControls.canChangeTourLocation())throw Error("TourStep ignored, cannot change location at this time, another transition is active");try{this.forward?this.tourControls.tourGoNext(!1):this.tourControls.tourGoPrevious(!1)}catch(e){throw Error(`Error while trying to travel to the next tour snapshot - ${e}`)}}}function ur(e,t,n,a){!function(e,t,n){t.addBinding(pr,(async()=>n.getDependencies().then((async([n,a])=>{const o=await Promise.all(a.getEnabledSnapshots().map((n=>async function(e,t,n){const a=!e.is360,o=n.viewmodeConverter.toSdk(e.metadata.cameraMode,a);if(!o||o===n.commandModeConverter.toSdk(t.CommandViewmode.TRANSITIONING))throw Error("Failed to convert snapshot, invalid viewmode");return{sid:e.sid,thumbnailUrl:await e.thumbnailUrl.get(),imageUrl:await e.imageUrl.get(),is360:e.is360,name:e.name,mode:o,zoom:e.metadata.ssZoom,position:e.metadata.cameraPosition,rotation:n.conversionUtils.quaternionToRotation(e.metadata.cameraQuaternion)}}(n,e,t))));if(0===o.length)throw Error("No tour data found");return o}))))}(t,n,a),e.addCommandCreator({namespace:"Tour",name:"getData",args:[]},(()=>new pr));const o=a,s=oe.create(o,new G(cr)),i=oe.create(o,new G(dr)),r=oe.create(o,new G(lr)),c=oe.create(o,new G(hr,!0)),d=oe.create(o,new G(hr,!1));e.addAsyncCommandToInterface({namespace:"Tour",name:"start",args:["index"]},s),e.addCommandToInterface({namespace:"Tour",name:"stop",args:[]},i),e.addCommandToInterface({namespace:"Tour",name:"step",args:["index"]},r),e.addCommandToInterface({namespace:"Tour",name:"prev",args:[]},c),e.addCommandToInterface({namespace:"Tour",name:"next",args:[]},d)}class pr extends W{constructor(){super(...arguments),this.id="GET_TOUR_DATA"}}class mr{create(e,t){return t.onChanged((()=>e.onChanged()))}}class gr{constructor(){this._data={step:null}}get data(){return this._data}equals(e){return this._data.step===e.data.step}copy(e){this._data.step=e.data.step}update(e){var t;this._data.step=e.isTourActive()&&null!==(t=e.getTourCurrentSnapshotSid())&&void 0!==t?t:null}}class wr{create(e,t){return t.onChanged((()=>e.onChanged()))}}class fr{constructor(e){this._data={current:g.PlayState.INACTIVE},this.tourStateConverter=e.tourStateConverter}get data(){return this._data}equals(e){return this._data.current===e.data.current}copy(e){this._data.current=e.data.current}update(e){this._data.current=this.tourStateConverter.toSdkTourState(e.getTourState())}}class yr{constructor(e){this.toSdkTourStateMap={[e.Inactive]:g.PlayState.INACTIVE,[e.Active]:g.PlayState.ACTIVE,[e.StopScheduled]:g.PlayState.STOP_SCHEDULED}}toSdkTourState(e){return this.toSdkTourStateMap[e]}}class Ir{create(e,t){return t.onChanged((()=>e.onChanged()))}}class Tr{constructor(){this._data={from:null,to:null}}get data(){return this._data}equals(e){return this._data.from===e.data.from&&this._data.to===e.data.to}copy(e){this._data.from=e.data.from,this._data.to=e.data.to}update(e){const t=this.hasEnded;if(this.hasEnded=e.tourEnded,!t&&this.hasEnded)return this._data.from=null,void(this._data.to=null);const n=-1===e.tourCurrentSnapshot?null:e.getTourSnapshotSid(e.tourCurrentSnapshot),a=-1===e.transition.toIndex?null:e.getTourSnapshotSid(e.transition.toIndex);null!==a&&(n===a?(this._data.from=null,this._data.to=null):(this._data.from=n,this._data.to=a))}}class Er{constructor(){this.layerSubscriptions=new Map}create(e,t){const n=t.onCurrentLayersChanged((()=>{this.subscribeToLayers(e,t),e.onChanged()}));return{renew:()=>{n.renew();for(const[,e]of this.layerSubscriptions)e.renew()},cancel:()=>{n.cancel();for(const[,e]of this.layerSubscriptions)e.cancel()}}}subscribeToLayers(e,t){for(const n of t.getOrderedModelViews()){if(n.enabled)for(const a of t.filterUserFacingLayers(n.layers))this.layerSubscriptions.has(a)||this.layerSubscriptions.set(a,a.onChanged((()=>e.onChanged())));for(const n of t.inMemoryLayers)this.layerSubscriptions.has(n)||this.layerSubscriptions.set(n,n.onChanged((()=>e.onChanged())))}}}class vr{constructor(e){this.layersData=e,this._data={}}get data(){return this._data}isItemEqual(e,t){return this.isLayerEqual(this.data[t],e.data[t])}isLayerEqual(e,t){return e.id===t.id&&e.name===t.name&&e.toggled===t.toggled}update(){const e=new Set;for(const t of this.layersData.getOrderedModelViews())if(t.enabled)for(const n of this.layersData.filterUserFacingLayers(t.layers))this._data[n.id]={id:n.id,name:n.name,toggled:n.toggled},e.add(n.id);for(const t of this.layersData.inMemoryLayers)this._data[t.id]={id:t.id,name:t.name,toggled:t.toggled},e.add(t.id);for(const t in this.data)e.has(t)||delete this._data[t]}clear(){this._data={}}}class Cr{create(e,t){return new Ne(t.onModelViewsChanged((()=>e.onChanged())),t.onCurrentLayersChanged((()=>e.onChanged())))}}class Ar{constructor(e){this.layersData=e,this._data={}}get data(){return this._data}isItemEqual(e,t){return this.isViewEqual(this.data[t],e.data[t])}isViewEqual(e,t){return e.id===t.id&&e.name===t.name&&e.layers.length===t.layers.length&&e.active===t.active&&e.layers.every(((e,n)=>{const a=t.layers[n];return e.id===a.id&&e.name===a.name&&e.toggled===a.toggled}))}update(){for(const e of this.layersData.getOrderedModelViews())if(e.enabled){const t=this._data[e.id]||{};t.id=e.id,t.name=e.name,t.active=this.layersData.currentViewId===e.id;const n=this.layersData.filterUserFacingLayers(e.layers).map((e=>({id:e.id,name:e.name,toggled:e.toggled})));t.layers=xe(t.layers||[],n),this._data[e.id]=t}for(const e in this.data)try{this.layersData.getView(e)||delete this._data[e]}catch(t){delete this._data[e]}}clear(){this._data={}}}class Sr{constructor(e){this.commands=e}validateInput(e,t){if(!de(e.name))throw Error("an empty name is not allowed");return{name:e.name}}async exec(e,t){const n=await this.commands.issueCommand(new this.commands.AddInMemoryLayerCommand({name:e.name,toggled:!0,visible:!0}));return{id:n.id,name:n.name,toggled:!0}}}var Dr;!function(e){e.ADD="view.add.layer",e.REMOVE="view.remove.layer"}(Dr||(Dr={}));const br="View.update",Or="View.activate",Mr="Layer.toggle";class Nr{constructor(e){this.layersData=e}validateInput(e,t){if(!be(Dr,e.op))throw Error(`${e.op} is an unsupported operation`);if(!de(e.viewId))throw Error(`${e.viewId} is not a valid view id`);const n=this.layersData.getView(e.viewId);if(!n)throw Error(`${e.viewId} does not map to a valid View`);if(!de(e.layerId))throw Error(`${e.layerId} is not a valid layer id`);const a=this.layersData.inMemoryLayers.find((t=>t.id===e.layerId));if(!a)throw Error(`Only SDK created layers can be manipulated. ${e.layerId} does not map to an SDK-owned layer`);return{op:e.op,view:n,layer:a}}async exec(e,t){({[Dr.ADD]:(e,t)=>{e.layers.push(t)},[Dr.REMOVE]:(e,t)=>{const n=e.layers.findIndex((e=>e.id===t.id));n>=0&&e.layers.remove(n)}})[e.op](e.view,e.layer)}}class Rr{constructor(e,t){this.commands=e,this.layersData=t}validateInput(e,t){if(!de(e.viewId)||!this.layersData.getView(e.viewId))throw Error(`${e.viewId} does not map to a valid View`);return{viewId:e.viewId,returnToStart:!!e.returnToStart}}async exec(e,t){e.returnToStart?await this.commands.issueCommand(new this.commands.SetActiveModelViewCommand(e.viewId)):(await this.commands.issueCommand(new this.commands.SetMoveCameraOnViewChange(!1)),await this.commands.issueCommand(new this.commands.SetActiveModelViewCommand(e.viewId)),await this.commands.issueCommand(new this.commands.SetMoveCameraOnViewChange(!0)))}}class Pr{create(e,t){return t.onPropertyChanged("currentViewId",(()=>e.onChanged()))}}class Lr{constructor(){this._data={id:"",name:"",active:!1,layers:[]}}get data(){return this._data}copy(e){this._data.id=e.data.id,this._data.name=e.data.name,this._data.active=e.data.active,xe(this._data.layers,e.data.layers)}equals(e){return this.data.id===e.data.id&&this.data.name===e.data.name&&this.data.active===e.data.active&&this.data.layers.length===e.data.layers.length&&this._data.layers.every(((e,t)=>{const n=this.data.layers[t];return e.id===n.id&&e.name===n.name&&e.toggled===n.toggled}))}update(e){const t=e.getView(e.currentViewId);this._data.id=t.id,this._data.name=t.name,this._data.active=e.currentViewId===this._data.id,xe(this._data.layers,e.filterUserFacingLayers(t.layers).map((e=>({id:e.id,name:e.name}))))}}class xr{constructor(e){this.matcher=e}static isValue(e){return new xr(new kr(e))}static isType(e){return new xr(new _r(e))}static isInstanceOf(e){return new xr(new Ur(e))}static is(e){return new xr(new Vr(e))}static isAny(){return new xr(new Fr)}compare(e){return this.matcher.matches(e)}}class kr{constructor(e){this.value=e}matches(e){return this.value===e}}class _r{constructor(e){this.type=e}matches(e){return Object.getPrototypeOf(e).constructor===this.type}}class Ur{constructor(e){this.type=e}matches(e){if("object"==typeof e)return e instanceof this.type;const t=this.type;switch(typeof e){case"number":return t===Number;case"string":return t===String;case"boolean":return t===Boolean;case"function":return t===Function}return!1}}class Vr{constructor(e){this.predicate=e}matches(e){return this.predicate(e)}}class Fr{constructor(){}matches(){return!0}}class Gr{constructor(e,t){this.sdk=e,this.commandFilter=t,this._data=new Map}validateInput(){return{}}async exec(){return this.data}get data(){return this._data=new Map(this.sdk.registeredCommands(this.commandFilter)),this._data}}class Hr{constructor(e,t){this.commands=e,this.layersData=t}validateInput(e,t){if(!de(e.layerId))throw Error(`${e.layerId} does not map to a valid Layer`);const n=this.layersData.getLayer(e.layerId);if(!n)throw Error(`${e.layerId} does not map to a valid Layer`);return{layer:n,activate:void 0===e.activate?void 0:!!e.activate}}exec(e,t){return void 0!==e.activate?this.commands.issueCommand(new this.commands.ToggleLayerCommand(e.layer.id,e.activate)):this.commands.issueCommand(new this.commands.ToggleLayerCommand(e.layer.id,!e.layer.toggled))}}function Br(e,t,n){const a=new we(t.layersData),o=function(e,t){const n=F.create(t,new Pr,new G(Lr));return e.addObservableToInterface({namespace:"View",name:"current",objectFactory:"View"},n),n}(e,a),s=function(e,t){const n=at.create(t,new Cr,new G(Ar));return e.addCollectionToInterface({namespace:"View",name:"views",elementFactory:"View"},n),n}(e,a),i=function(e,t){const n=at.create(t,new Er,new G(vr));return e.addCollectionToInterface({namespace:"View",name:"layers",elementFactory:"Layer"},n),n}(e,a);!function(e){const t=xr.is((e=>{var t,n;return!(null===(t=e.options)||void 0===t?void 0:t.deprecated)&&!!(null===(n=e.options)||void 0===n?void 0:n.replay)})),n=Rt.create(we.none,new G(Gr,e,t));e.addCommand("Layer.interface",n)}(e);return{current:o,views:s,layers:i,updateView:function(e,t){const n=Rt.create(t,new G(Nr));return e.addCommand(br,n),n}(e,a),activateView:function(e,t,n){const a=Rt.create(n,new G(Rr,t));return e.addCommand(Or,a),a}(e,n,a),createLayer:function(e,t){const n=Rt.create(we.none,new G(Sr,t));return e.addCommandToInterface({namespace:"View",name:"createLayer",args:["name"],subRoutine:"Layer"},n),n}(e,n),toggleLayer:function(e,t,n){const a=Rt.create(n,new G(Hr,t));return e.addCommand(Mr,a),a}(e,n,a)}}class zr{constructor(e,t,n,a){this.layersData=e,this.commonLayer=t,this.getCurrentLayer=n,this.selectLayer=a,this.layerToReset=null}async intercept(e,t){var n;this.layerToReset=null!==(n=this.getCurrentLayer())&&void 0!==n?n:null;const a=t.layerId&&this.layersData.findLayer(t.layerId)||this.commonLayer;await this.selectLayer(a)}async postExec(){this.layerToReset&&await this.selectLayer(this.layerToReset)}}var $r;!function(e){e.DISABLED="disabled",e.VIEWS_OPTIONAL_OPT_IN="user_views",e.VIEWS_ENABLED="views_enabled",e.VIEWS_AND_LAYERS="views_layers",e.LAYERS_ONLY="layers"}($r||($r={}));var jr;!function(e){e.DISABLED="0",e.VIEWS_ENABLED="2",e.VIEWS_AND_LAYERS="3",e.LAYERS_ONLY="4"}(jr||(jr={}));class Wr{create(e,t,n){const a=()=>e.onChanged();return new Ne(n.onChanged(a),t.onChanged(a))}}class qr{constructor(e){this._data={from:null,to:null},this.viewmodeConverter=e.viewmodeConverter}get data(){return this._data}equals(e){return this._data.from===e._data.from&&this._data.to===e._data.to}copy(e){this._data={from:e._data.from,to:e._data.to}}update(e,t){const n=t.currentSweep&&t.isSweepAligned(t.currentSweep),a=t.transition.to&&t.isSweepAligned(t.transition.to);this._data=e.transition.active?{from:this.viewmodeConverter.toSdk(e.transition.from,!!n),to:this.viewmodeConverter.toSdk(e.transition.to,!!a)}:{from:null,to:null}}}class Kr{create(e,t){return t.onChanged((()=>e.onChanged()))}}class Yr{constructor(e){this.pluginData=e,this._data={}}get data(){return this._data}isItemEqual(e,t){const n=this.data[t],a=e.data[t];return n.applicationKey===a.applicationKey&&n.id===a.id}update(){for(const e of this.pluginData.plugins.entries())this._data[e[0]]={applicationKey:e[1].applicationKey,id:e[1].id};for(const e in this.data)this.pluginData.plugins.has(e)||delete this._data[e]}clear(){this._data={}}}function*Xr(e){const t=[...e];for(let e=t.length-1;e>=0;--e)yield t[e]}const Zr=new ne("sdk history");class Qr{constructor(e,t,n){this.sdk=e,this.layersData=t,this.commonLayer=n,this.claimedIds=new Set,this.allRegisteredCommands=new Map,this.commandObservers=new Set,this.interceptors=new Set,this.commandInterceptors=new xi}getOrClaimId(e,t){const n=t||this.generateUniqueId();return this.claimedIds.add(n),n}generateUniqueId(){let e="";do{e=ba(12)}while(this.claimedIds.has(e));return e}connect(e,t){return this.sdk.connect(e,t)}connectPlugin(e,t){return this.sdk.connectPlugin(e,t)}getClientAuth(e){return this.sdk.getClientAuth(e)}addEnumToInterface(e){return this.sdk.addEnumToInterface(e)}addCommand(e,t,n){return this.sdk.addCommand(e,t,n)}addCommandToInterface(e,t){return this.sdk.addCommandToInterface(e,this.createCommandProxy(e,t))}addAsyncCommandToInterface(e,t){return this.sdk.addAsyncCommandToInterface(e,this.createCommandProxy(e,t))}addObservable(e,t){return this.sdk.addObservable(e,t)}addObservableToInterface(e,t){return this.sdk.addObservableToInterface(e,t)}addCollection(e,t){return this.sdk.addCollection(e,t)}addCollectionToInterface(e,t){return this.sdk.addCollectionToInterface(e,t)}broadcast(e,t,n){return this.sdk.broadcast(e,t,n)}sendPrivateMessage(e,t){return this.sdk.sendPrivateMessage(e,t)}addPrivateMessageHandler(e){return this.sdk.addPrivateMessageHandler(e)}addCommandCreator(e,t){return this.sdk.addCommandCreator(e,t)}interceptCommands(e,...t){if(0===t.length)this.interceptors.add(e);else for(const n of t)this.commandInterceptors.add(n,e)}async createCommandProxy(e,t){var n;const a=new ec,o=new Set,s=new Set,i={visitor:a,beforeExec:async(n,s,i,r)=>{var c,d;if(o.clear(),a.reset(),null==n?void 0:n.layerId){const e=this.layersData.findLayer(n.layerId);if(!e)throw Error(n.layerId+"does not map to a valid layer");this.layersData.getCurrentView().layers.find((t=>t===e))&&!this.layersData.commonInMemoryLayers.find((t=>t===e))||(a.stopPropagation("not ready to execute"),s.validateInput(i,n))}for(const t of this.interceptors){if(!a.propagate)break;await(null===(c=t.intercept)||void 0===c?void 0:c.call(t,e,n)),o.add(t)}for(const s of this.commandInterceptors.getValuesAtKey(t)){if(!a.propagate)break;await(null===(d=s.intercept)||void 0===d?void 0:d.call(s,e,n)),o.add(s)}},afterExec:async(n,i,r,c)=>{var d,l,h,u,p;const m=async()=>{Zr.debug("executing",`${e.namespace}.${e.name}`,r);try{await c.exec(r,n)}catch(e){Zr.debugWarn(e)}};if((null===(d=e.options)||void 0===d?void 0:d.replay)&&!s.has(n.executionId))if(s.add(n.executionId),n.layerId){const e=this.layersData.findLayer(n.layerId);if(!e)throw Error(n.layerId+"does not map to a valid layer");e.onApply(m)}else null===(l=this.commonLayer)||void 0===l||l.onApply(m);if(a.propagate){for(const e of Xr(this.commandInterceptors.getValuesAtKey(t)))await(null===(h=e.postExec)||void 0===h?void 0:h.call(e));for(const e of Xr(this.interceptors))await(null===(u=e.postExec)||void 0===u?void 0:u.call(e))}else for(const e of Xr(o))null===(p=e.postExec)||void 0===p||p.call(e)}},r=(e,t)=>this.getOrClaimId(e,t);if(this.allRegisteredCommands.set(`${e.namespace}.${e.name}`,e),this.triggerCommandObservers(e),null===(n=e.options)||void 0===n?void 0:n.deprecated){const n=`"${e.namespace}.${e.name}" is deprecated`+(le(e.options.deprecated)?`; use ${e.options.deprecated}`:"");return new Jr(i,r,await ce(t,n))}return new Jr(i,r,await t)}*registeredCommands(e){for(const t of this.allRegisteredCommands){const[,n]=t;e.compare(n)&&(yield t)}}onCommandsUpdated(e,t){const n=[e,t],a={renew:()=>this.commandObservers.add(n),cancel:()=>this.commandObservers.delete(n)};return a.renew(),a}triggerCommandObservers(e){for(const[t,n]of this.commandObservers)n.compare(e)&&t(e)}}class Jr{constructor(e,t,n){this.hooks=e,this.claimId=t,this.command=n,this.clientCache=new Map,this.contextCache=new Map}async exec(e,t){const n=this.getContextWithIdLookup(t);let a;return await this.hooks.beforeExec(n,this.command,e,this),this.hooks.visitor.propagate&&(a=await this.command.exec(e,n)),this.hooks.afterExec(n,this.command,e,this),a}validateInput(e,t){const n=this.getContextWithIdLookup(t);return this.command.validateInput(e,n)}getContextWithIdLookup(e){const t=e.client,n=this.clientCache.get(t)||Object.assign(Object.assign({},t),{getOrClaimId:e=>this.claimId(n,e)}),a=this.contextCache.get(e.executionId)||Object.assign(Object.assign({},e),{client:n});return this.clientCache.set(t,n),this.contextCache.set(e.executionId,a),a}}class ec{constructor(){this.reason="",this.propagate=!0}stopPropagation(e){this.propagate=!1,this.reason=e}reset(){this.reason="",this.propagate=!0}}const tc=new ne("SDK interface");async function nc(t,o,r,h,u,m,f){ne.level=f.logLevel||te.INFO;const{THREE:y}=o,{appPhaseModule:I,applicationData:T,cameraData:C,canvasData:A,deepLinks:H,floorData:B,floorViewData:z,labelData:W,layersData:Y,mattertagData:X,tagsViewData:Z,measurementModeData:Q,meshData:J,modelData:ee,annotationsViewData:ae,raycasterData:se,roomData:ie,settingsData:re,sweepData:de,sweepViewData:le,tourData:he,viewmodeData:ue,attachmentsModule:pe,commonControlsModule:me,floorCasterModule:ge,meshQuery:fe,modelDataModule:Ie,modelMeshModule:Te,navigationModule:Ee,plugin:Ne,pluginConfigData:Re,renderToTextureModule:Pe,sceneModule:Le,sensorModule:xe,settingsModule:ke,tourControlsModule:_e,webglRendererModule:Ue,zoomControlModule:Ve,sweepTextureLoader:$e,cursorController:je,pluginData:We,externalR3FModule:qe}=u;f.idOption=f.idOption||v.LEGACY,f.idOption===v.LEGACY&&tc.warn("\n      Using legacy IDs is deprecated. Please add the &useLegacyIds=0 URL parameter to use modern IDs.\n      Modern IDs will become the new default soon.\n      To convert between the different ID schemes use the Conversion namespace under the Sweep, Floor, or Room namespaces.\n    ");const[Ze,ft]=await Promise.all([Y,re]);let yt=ft.tryGetProperty("data-layers-feature",!1)||ft.tryGetProperty("model-views-feature",!1),It=null;try{It=await h.issueCommand(new r.AddInMemoryLayerCommand({name:"SDK Layer (Temporary)",visible:!0,toggled:!0,common:!0}))}catch(e){yt=!1,It=null,tc.info("Error setting up layers. Views and Layers features will be disabled. -- ",e)}const Et=new Qr(t,Ze,It),St=new M(y),Dt=new D(r.Viewmode),Rt=function(e){return async function(t,n){const a=n.rotationSpeed;let o=0;const s=Math.abs(n.xAngle),i=Math.abs(n.yAngle),r=Math.max(s,i);if(r>=Math.PI){const e=r/Math.PI,t=Math.floor(e),n=s/e,a=i/e,c=s-n*t,d=i-a*t;o=Math.acos(Math.cos(n)*Math.cos(a))*t+Math.acos(Math.cos(c)*Math.cos(d))}else o=Math.acos(Math.cos(Math.abs(n.xAngle))*Math.cos(Math.abs(n.yAngle)));if(o){const s=new e.Vector2(-n.xAngle,n.yAngle);return s.multiplyScalar(a/o),t.startRotateTransition(o/a,s,!1)}}}(y),Pt=new js(r.SweepAlignmentType,r.SweepPlacementType),Lt=Object.assign(Object.assign({},h),{conversionUtils:new M(y),directionConverter:new S(r.Vectors),getPose:et(y,Dt),rotateCamera:Rt,orientCamera:Be(y,r.Viewmode,Rt),appPhaseConverter:new U(r.AppPhase),applicationConverter:new V(r.Application),mediaConverter:new Ot(r.MattertagMediaType,r.TagDescriptionChunkType),chunkTypeConverter:new Mt(r.TagDescriptionChunkType),linkTypeConverter:new Nt(r.TagLinkType),sweepPlacementConverter:Pt,viewmodeConverter:Dt,commandModeConverter:new b(r.CommandViewmode),cameraTransitionConverter:new O(r.CameraTransitionType),makeLabelData:ut(y,h.worldToScreenPosition),makeModelData:Eo(St,Pt),sweepUtils:new N(r.SweepAlignmentType),tourStateConverter:new yr(r.TourState)}),xt=async function(e,t){const n=await e;return t===v.LEGACY?new R(n):new P(n)}(de,f.idOption),kt=async function(e,t){const n=await e;return t===v.LEGACY?new L(n):new x(n)}(B,f.idOption),_t=async function(e,t,n){const a=await e,o=await t;return n===v.LEGACY?new k(a,o):new _(a)}(ie,B,f.idOption),Ut=async function(e,t,n,a){const[o,s,i]=await Promise.all([e,t,a]);return new Ra(o,s,n,i)}(X,pe,o.oEmbed,void 0),Vt=new Map;!function(t){t.addEnumToInterface({namespace:"App",name:"Phase",values:e.Phase}),t.addEnumToInterface({namespace:"App",name:"Event",values:e.Event}),t.addEnumToInterface({namespace:"App",name:"Application",values:e.Application})}(Et),function(t,n,a){const o=[void 0],s=e=>(o[0]=a.appPhaseConverter.toSdkAppPhase(e.phase),o);a.subscribe(n.AppPhaseChangeMessage,(n=>{t.broadcast(e.Event.PHASE_CHANGE,s,n)}))}(Et,r,Lt),function(e,t,n){n.getDependencies().then((([e])=>{t.addBinding(q,(async()=>{try{const n=e.getData();return{phase:t.appPhaseConverter.toSdkAppPhase(n.phase),application:t.applicationConverter.toSdkApplication(n.application)}}catch(e){throw Error("Error: Can't get application data at this time")}}))})),e.addCommandCreator({namespace:"App",name:"getState",args:[]},(()=>new q))}(Et,Lt,new we(I)),function(t,n,a){n.addBinding(K,(async()=>{const t={[e.Phase.WAITING]:null,[e.Phase.LOADING]:null,[e.Phase.STARTING]:null,[e.Phase.PLAYING]:null,[e.Phase.ERROR]:null},[o]=await a.getDependencies();for(const e in o.phaseTimes){const a=Number(e);t[n.appPhaseConverter.toSdkAppPhase(a)]=o.phaseTimes[a]}return t})),t.addCommandCreator({namespace:"App",name:"getLoadTimes",args:[]},(()=>new K))}(Et,Lt,new we(T)),function(e,t,n,a){const o=F.create(a,new $,new G(j,t,n));e.addObservableToInterface({namespace:"App",name:"state"},o)}(Et,r,Lt,new we(T));const Ft=ve(Et,y,Lt,Vt,m,u,Ut);!function(e,t,n){n.getDependencies().then((([e,n,a,o])=>{const s={position:{x:0,y:0,z:0},rotation:{x:0,y:0},projection:new Float32Array(16),sweep:"",mode:c.Mode.TRANSITIONING};t.addBinding(Ce,(async()=>t.getPose(s,e,n,a,o)))})),e.addCommandCreator({namespace:"Camera",name:"getPose",args:[]},(()=>new Ce))}(Et,Lt,new we(C,de,ue,xt)),function(e,t,n,a){a.getDependencies().then((([e,a,o])=>{n.addBinding(Se,(async function(s={}){if(!o.isInside())throw Error("Camera.lookAtScreenCoords must be called from Inside mode");const i=n.screenPositionToNDC(s.x,s.y,a.width,a.height),r=new t.Vector3(0,0,-1).applyQuaternion(a.pose.rotation),c=n.ndcToWorldPosition(a,new t.Vector2(i.x,i.y),1).normalize(),d=r.clone().setY(0).angleTo(c.clone().setY(0)),l=Math.asin(c.y-r.y),h=d*Math.sign(i.x),u=l;return n.rotateCamera(e,{xAngle:h,yAngle:u,zAngle:0,rotationSpeed:Ae})}))})),e.addCommandCreator({namespace:"Camera",name:"lookAtScreenCoords",args:["x","y"]},(function(e={}){if(e.x=e.x||0,e.y=e.y||0,isNaN(e.x)||isNaN(e.y))throw new Error(`${JSON.stringify(e)} does not contain valid screen coordinates`);return new Se({x:e.x,y:e.y})}))}(Et,y,Lt,new we(me,C,ue)),function(e,t,a){a.getDependencies().then((([a,o,s,i])=>{const r=[{position:{x:0,y:0,z:0},rotation:{x:0,y:0},projection:new Float32Array(16),sweep:"",mode:c.Mode.TRANSITIONING}],d=(()=>{let c=0,l=!1;const h=()=>(t.getPose(r[0],a,o,s,i),r);return()=>{const t=Date.now(),a=c+100;if(t>a){l=!1,c=t;try{e.broadcast(n.Event.MOVE,h)}catch(e){De.debug("failed to broadcast pose, one of the module dependencies are probably not loaded yet")}}else l||(setTimeout((()=>d()),a-t),l=!0)}})();a.onChanged(d),s.onChanged(d)})),e.addEnumToInterface({namespace:"Camera",name:"Event",values:n.Event})}(Et,Lt,new we(C,de,ue,xt)),function(e,t,a){a.getDependencies().then((([e])=>t.addBinding(Oe,(async a=>{const o=a.direction;if(!be(n.Direction,a.direction))throw new Error(`${a.direction} is not a valid direction`);await e.navigateInLocalDirection(t.directionConverter.toVector(o))})))),e.addEnumToInterface({namespace:"Camera",name:"Direction",values:n.Direction}),e.addCommandCreator({namespace:"Camera",name:"moveInDirection",args:["direction"]},(e=>new Oe(e||{})))}(Et,Lt,new we(Ee)),function(e,t,n,a){a.getDependencies().then((([e,a])=>{n.addBinding(Me,(async n=>{const o=a.pose.position,s=n.x-o.x,i=n.z-o.z,r=new t.Vector2(s,i),c=r.length();r.setLength(.005),await e.startTranslateTransition(c/.005,r,!1)}))})),e.addCommandCreator({namespace:"Camera",name:"pan",args:["position"]},(e=>{if((e=e||{}).position=e.position||{},e.position.x=e.position.x-0,e.position.z=e.position.z-0,isNaN(e.position.x)||isNaN(e.position.z))throw new Error(`${JSON.stringify(e)} does not contain a valid position to pan`);return new Me({x:e.position.x,z:e.position.z})}))}(Et,y,Lt,new we(me,C)),function(e,t,n,a){const o=F.create(a,new Fe,new G(Ge,t,n.viewmodeConverter));e.addObservableToInterface({namespace:"Camera",name:"pose"},o)}(Et,y,Lt,new we(C,de,ue,xt)),function(e,t,n,a){const o=t.MathUtils.degToRad(80)/1e3;a.getDependencies().then((([e,t,a])=>{n.addBinding(He,(async t=>await n.rotateCamera(e,t))),n.addBinding(ze,(async o=>await n.orientCamera(t,e,a,o)))}));const s=e=>{const n=(e=e||{}).options||{},a=t.MathUtils.degToRad(e.xAngle||0),s=t.MathUtils.degToRad(e.yAngle||0),i=t.MathUtils.degToRad(e.zAngle||0);let r=o;if(n.speed){if(isNaN(n.speed)||n.speed<=0)throw new Error(`${JSON.stringify(n)} does not contain valid rotation speed`);r=t.MathUtils.degToRad(n.speed)/1e3}if(isNaN(a)||isNaN(s)||isNaN(i))throw new Error(`${JSON.stringify(e)} does not contain valid rotation angles`);return{xAngle:a,yAngle:s,zAngle:i,rotationSpeed:r}};e.addCommandCreator({namespace:"Camera",name:"rotate",args:["xAngle","yAngle","options"]},(e=>new He(s(e)))),e.addCommandCreator({namespace:"Camera",name:"setRotation",args:["rotation","options"]},(e=>{const t=(e=e||{}).rotation||{};return new ze(s({xAngle:t.x,yAngle:t.y,zAngle:t.z,options:e.options}))}))}(Et,y,Lt,new we(me,C,ue)),function(e,t,n,a){const o=a,s=oe.create(o,new G(Ke,t,n)),i=oe.create(o,new G(Ye,t,n)),r=oe.create(o,new G(Xe,t,n));e.addCommandToInterface({namespace:"Camera",name:"zoomTo",args:["zoomPct"]},s),e.addCommandToInterface({namespace:"Camera",name:"zoomBy",args:["zoomDelta"]},i),e.addCommandToInterface({namespace:"Camera",name:"zoomReset",args:[]},r)}(Et,r,Lt,new we(C,ue,Ve)),function(e,t,n){const a=F.create(n,new Qe,new G(Je,t));e.addObservableToInterface({namespace:"Camera",name:"zoom"},a)}(Et,r,new we(C,ue)),function(e,t,n){const a=F.create(n,new tt,new G(nt,t));e.addObservableToInterface({namespace:"Floor",name:"current"},a)}(Et,r,new we(z,de,C,he,kt)),function(e,t,n,o){e.addEnumToInterface({namespace:"Floor",name:"Event",values:a.Event}),o.getDependencies().then((([o])=>{const s=[-1,-1];function i(e){if(o){const t=e.to?o.getFloor(e.to):null,n=e.from?o.getFloor(e.from):null;t&&(s[0]=t.index),n&&(s[1]=n.index)}return s}const r=[-1,""];function c(e){if(o){const t=e.floorId?o.getFloor(e.floorId):null;t&&(r[0]=t.index)}return r[1]=e.floorName,r}n.subscribe(t.StartMoveToFloorMessage,(t=>e.broadcast(a.Event.CHANGE_START,i,t))),n.subscribe(t.EndMoveToFloorMessage,(t=>e.broadcast(a.Event.CHANGE_END,c,t)))}))}(Et,r,Lt,new we(B)),function(e,t){const n=at.create(t,new ot,new G(st));e.addCollectionToInterface({namespace:"Floor",name:"data"},n)}(Et,new we(B,kt)),function(e,t,n){t.addBinding(it,(async()=>{const[e]=await n.getDependencies();return rt(e)})),e.addCommandCreator({namespace:"Floor",name:"getData",args:[]},(()=>new it))}(Et,Lt,new we(z)),function(e,t,n,a){a.getDependencies().then((([e])=>n.addBinding(ct,(async a=>{if("number"!=typeof a.floorIndex||a.floorIndex<0)throw Error("floor index must be a non-negative number");try{const o="boolean"==typeof a.moveCamera&&!a.moveCamera,s=o?250:void 0;await n.issueCommand(new t.MoveToFloorIndexCommand(a.floorIndex,o,s));const i=e.currentFloor;return i?i.index:-1}catch(e){throw Error(`Could not move to floor at index ${a.floorIndex}`)}})))),e.addCommandCreator({namespace:"Floor",name:"moveTo",args:["floorIndex","moveCamera"]},(e=>new ct(e)))}(Et,r,Lt,new we(z)),function(e,t,n,a){const o=oe.create(a,new G(dt,t,n));e.addCommandToInterface({namespace:"Floor",name:"showAll",args:["moveCamera"]},o)}(Et,r,Lt,new we(z)),function(e,t){const n=oe.create(t,new G(lt));e.addCommandToInterface({namespace:"Floor.Conversion",name:"createIdMap",args:["invert"]},n)}(Et,new we(B)),function(e,t,n){t.addBinding(ht,(async()=>t.makeLabelData(...await n.getDependencies()))),e.addCommandCreator({namespace:"Label",name:"getData",args:[]},(()=>new ht))}(Et,Lt,new we(C,B,W,kt)),function(e,t,n,a){e.addEnumToInterface({namespace:"Label",name:"Event",values:s.Event}),a.getDependencies().then((([a,o,i,r,c])=>{const d=()=>[n.makeLabelData(a,i,o,c)];a.onChanged((n=>{r.currentMode===t.Viewmode.Floorplan&&e.broadcast(s.Event.POSITION_UPDATED,d,n)}))}))}(Et,r,Lt,new we(C,W,B,ue,kt)),function(e,t){const n=at.create(t,new pt,new G(mt));e.addCollectionToInterface({namespace:"Label",name:"data"},n)}(Et,new we(W,B,kt)),function(e,t){const n=oe.create(t,new G(gt));e.addAsyncCommandToInterface({namespace:"Link",name:"createLink",args:[]},n)}(Et,new we(H)),function(e,t){const n=oe.create(t,new G(wt));e.addAsyncCommandToInterface({namespace:"Link",name:"createDeepLink",args:[]},n)}(Et,new we(H)),function(e,t){const n=oe.create(t,new G(Tt,e)),a=oe.create(t,new G(vt,e)),o=oe.create(t,new G(Ct,e)),s=oe.create(t,new G(At,e));e.addEnumToInterface({namespace:"Link",name:"OpenPolicy",values:i.OpenPolicy}),e.addEnumToInterface({namespace:"Link",name:"DestinationPolicy",values:i.DestinationPolicy}),e.addAsyncCommandToInterface({namespace:"Link",name:"setModelLinkPolicy",args:["policy","baseHref"],subRoutine:"link.setopenpolicy"},n),e.addAsyncCommandToInterface({namespace:"Link",name:"setNavigationLinkPolicy",args:["policy","baseHref"],subRoutine:"link.setopenpolicy"},a),e.addAsyncCommandToInterface({namespace:"Link",name:"setSameOriginLinkPolicy",args:["policy"]},o),e.addAsyncCommandToInterface({namespace:"Link",name:"setExternalLinkPolicy",args:["openInNewWindow"]},s)}(Et,new we(H,m)),function(e,t){const n=oe.create(t,new G(bt));e.addEnumToInterface({namespace:"Link",name:"CreationPolicy",values:i.CreationPolicy}),e.addAsyncCommandToInterface({namespace:"Link",name:"setShareLinkPolicy",args:["policy","baseHref","options"],subRoutine:"link.setsharepolicy"},n)}(Et,new we(H));const Gt=async function(){const e=f.sandboxPath;return(await fetch(e)).text()}(),Ht=Yi(Et,y,r,Lt,Vt,Ut,Gt,Lt.getTagsQuery,It,Lt.isMpFontId,Object.assign(Object.assign({},u),{roomIdMap:_t})),Bt=Ya(Et,r,Object.assign(Object.assign({},Lt),{getIframeSrcDoc:Gt}),Ft,Ht,[X,Z,ae,B,kt,Ut]);!function(e,t,n){t.addBinding(Za,(async()=>{const[e,t]=await n.getDependencies();return Qa(e,t)})),e.addCommandCreator({namespace:"Measurements",name:"getData",args:[]},(()=>new Za))}(Et,Lt,new we(Q,B)),function(e,t,n){const a=at.create(n,new Ja(t.MeasuringPhase),new G(eo));e.addCollectionToInterface({namespace:"Measurements",name:"data"},a)}(Et,r,new we(Q)),function(e,t){const n=F.create(t,new to,new G(no));e.addObservableToInterface({namespace:"Measurements",name:"mode"},n)}(Et,new we(Q)),function(e,t,n){n.addBinding(ao,(async e=>n.issueCommand(new t.ToggleToolCommand(t.Tools.MEASUREMENTS,e.active)))),e.addCommandCreator({namespace:"Measurements",name:"toggleMode",args:["active"]},(e=>new ao(e.active)))}(Et,r,Lt),function(e,t,n,a){e.addEnumToInterface({namespace:"Mode",name:"Event",values:c.Event}),a.getDependencies().then((([a,o])=>{const s=[void 0,void 0],i=e=>{const t=o.currentMode,i=n.sweepUtils.isSweepAligned(a,e.fromSweep),r=n.sweepUtils.isSweepAligned(a,e.toSweep);return s[0]=n.viewmodeConverter.toSdk(t,i),s[1]=n.viewmodeConverter.toSdk(t,r),s};n.subscribe(t.EndMoveToSweepMessage,(t=>{n.sweepUtils.isSweepAligned(a,t.fromSweep)!==n.sweepUtils.isSweepAligned(a,t.toSweep)&&e.broadcast(c.Event.CHANGE_END,i,t)}));const r=[void 0,void 0],d=e=>{const t=n.sweepUtils.isCurrentSweepAligned(a);return r[0]=n.viewmodeConverter.toSdk(e.fromMode,t),r[1]=n.viewmodeConverter.toSdk(e.toMode,t),r};n.subscribe(t.BeginSwitchViewmodeMessage,(t=>e.broadcast(c.Event.CHANGE_START,d,t)));const l=[void 0,void 0],h=e=>{const t=n.sweepUtils.isCurrentSweepAligned(a);return l[0]=n.viewmodeConverter.toSdk(e.fromMode,t),l[1]=n.viewmodeConverter.toSdk(e.toMode,t),l};n.subscribe(t.EndSwitchViewmodeMessage,(t=>e.broadcast(c.Event.CHANGE_END,h,t)))}))}(Et,r,Lt,new we(de,ue)),function(e,t,n,a,o){a.addBinding(po,(async e=>{try{e.options=e.options||{};let t=n.CameraTransitionType.Interpolate;const o=e.options.transition;return void 0===o||isNaN(o)||(t=o),await a.issueCommand(new n.ChangeViewmodeCommand(e.mode,t,{position:e.options.position,rotation:e.options.rotation,zoom:e.options.zoom})),e.mode}catch(t){const n=t instanceof io?`Mode.moveTo -> Cannot move to ${e.mode} during a mode transition`:`Mode.moveTo -> Could not move to ${e.mode}`;if(uo.info(t,n),t instanceof io)throw Error(`Mode.moveTo -> Cannot move to ${e.mode} during a mode transition`);throw Error(`Mode.moveTo -> Could not move to ${e.mode}`)}}));const s=new mo(t,a.commandModeConverter,a.conversionUtils,a.cameraTransitionConverter);e.addCommandCreator({namespace:"Mode",name:"moveTo",args:["mode","options"]},(e=>new po(s.validateMoveToModeInput(e)))),e.addEnumToInterface({namespace:"Mode",name:"Mode",values:c.Mode}),e.addEnumToInterface({namespace:"Mode",name:"TransitionType",values:E})}(Et,y,r,Lt,new we),function(e,t,n,a){const o=F.create(a,new go,new G(wo,t,n));e.addObservableToInterface({namespace:"Mode",name:"current"},o)}(Et,r,Lt,new we(ue,de)),function(e,t,n){const a=F.create(n,new Wr,new G(qr,t));e.addObservableToInterface({namespace:"Mode",name:"transition"},a)}(Et,Lt,new we(ue,de)),yo(Et,Lt,new we(ee,de,B,xt)),To(Et,r,Lt,new we(ee,re)),function(e,t,n,a){e.addEnumToInterface({namespace:"Model",name:"Event",values:d.Event});const o={sid:"",sweeps:[],modelSupportsVr:!1},s=[o];a.getDependencies().then((a=>{n.subscribe(t.ModelDataLoadedMessage,(async t=>{const[i,r,c]=a;await n.makeModelData(t.sid,t.vrSupported,i.getSweepList(),r,c,o),e.broadcast(d.Event.MODEL_LOADED,(()=>s))}))}))}(Et,r,Lt,new we(de,B,xt)),function(e,t){const n=oe.create(t,new G(vo));e.addCommand("OAuth.updateToken",n)}(Et,new we(r.MpSdkAuthentication)),function(e,t){const n=at.create(t,new Kr,new G(Yr));e.addCollectionToInterface({namespace:"Plugin",name:"data",sdkTypes:[Co.POSTMESSAGE],origins:ye},n)}(Et,new we(We)),function(e,t){const n=oe.create(t,new G(So));e.addAsyncCommandToInterface({namespace:"Plugin",name:"load",args:["key","path","config"],varArg:!1,sdkTypes:[Co.POSTMESSAGE],origins:ye},n)}(Et,new we(Ne)),function(e,t){const n=oe.create(t,new G(Do));e.addAsyncCommandToInterface({namespace:"Plugin",name:"unload",args:["key"],varArg:!1,sdkTypes:[Co.POSTMESSAGE],origins:ye},n)}(Et,new we(Ne)),function(e,t,n,a){const o=F.create(a,new bo,new G(Oo,t,n));e.addEnumToInterface({namespace:"Pointer",name:"Colliders",values:l.Colliders}),e.addObservableToInterface({namespace:"Pointer",name:"intersection"},o)}(Et,y,r,new we(se,B,ie,J,fe)),function(e,t,n){const a=oe.create(n,new G(Mo)),o=oe.create(n,new G(No));e.addCommandToInterface({namespace:"Pointer",name:"registerTexture",args:["textureId","textureSrc"],varArg:!1},t),e.addCommandToInterface({namespace:"Pointer",name:"editTexture",args:["textureId"],varArg:!1},a),e.addCommandToInterface({namespace:"Pointer",name:"resetTexture",args:[],varArg:!1},o)}(Et,Ft.registerTexture,new we(je,Vt)),function(e,t){const n=oe.create(t,new G(Ro));e.addCommandToInterface({namespace:"Pointer",name:"setFadeProps",args:["props"],varArg:!1},n)}(Et,new we(je)),function(e,t){const n=oe.create(t,new G(Po));e.addCommandToInterface({namespace:"Pointer",name:"setVisible",args:["visible"],varArg:!1},n)}(Et,new we(je)),function(e,t,n,a,o){const s=new Go(new t.PerspectiveCamera,a.encodeRenderTarget),i=new G(jo,s,n,a),r=oe.create(o,i),c=oe.create(o,i);e.addCommandToInterface({namespace:"Renderer",name:"takeScreenShot",args:["resolution","visibleObjects","returnType"]},r),e.addCommandToInterface({namespace:"Camera",name:"takeScreenShot",args:["resolution","visibleObjects","returnType"]},ce(c,"`Camera.takeScreenshot` is deprecated; use `Renderer.takeScreenshot` instead"))}(Et,y,r,Lt,new we(A,Ue,Pe)),function(e,t,n,a,o){const s=oe.create(o,new G(Wo,t,n,a));e.addCommandToInterface({namespace:"Renderer",name:"takeEquirectangular",args:[]},s)}(Et,y,r,Lt,new we(Ue,Pe,ue,de,$e)),function(e,t,n,a){const o=oe.create(a,new G(qo,t,n));e.addCommandToInterface({namespace:"Renderer",name:"getScreenPosition",args:["worldPosition"]},o)}(Et,r,Lt,new we(Ue)),function(e,t,n,a){const o=oe.create(a,new G(Ko,t,n));e.addCommandToInterface({namespace:"Renderer",name:"getWorldPositionData",args:["screenPosition","height","includeHiddenFloors"]},o)}(Et,r,Lt,new we(B,ge)),function(e,t){const n=oe.create(t,new G(Lo));e.addAsyncCommandToInterface({namespace:"R3F",name:"registerR3F",args:["callbacks"],sdkTypes:[Co.DIRECT]},n)}(Et,new we(qe)),function(e,t,n,a,o){e.addAsyncCommandToInterface({namespace:"R3F",name:"focus",args:["target","options"],sdkTypes:[Co.DIRECT]},oe.create(o,new G(_o,t,n,a)))}(Et,y,r,Lt,new we(Ee,ue)),function(e,t){const n=oe.create(t,new G(Vo));e.addAsyncCommandToInterface({namespace:"R3F",name:"controlsToggle",args:["enabled"],sdkTypes:[Co.DIRECT]},n)}(Et,new we(me)),function(e,t){const n=oe.create(t,new G(ko));e.addAsyncCommandToInterface({namespace:"R3F",name:"navigationToggle",args:["enabled"],sdkTypes:[Co.DIRECT]},n)}(Et,new we(Ee)),function(e,t){const n=F.create(t,new Yo,new G(Xo));e.addObservableToInterface({namespace:"Room",name:"current",objectFactory:"current.room"},n)}(Et,new we(ie,B,C,de,ue,J,_t,kt)),function(e,t){const n=at.create(t,new Zo,new G(Qo));e.addCollectionToInterface({namespace:"Room",name:"data",elementFactory:"collection.room"},n)}(Et,new we(ie,B,_t,kt,J)),function(e,t){const n=oe.create(t,new G(Jo));e.addCommandToInterface({namespace:"Room.Conversion",name:"createIdMap",args:["invert"]},n)}(Et,new we(ie,B)),function(e,t,n,a,o,s,i){(function(e){e.addEnumToInterface({namespace:"Scene",name:"InteractionType",values:w.InteractionType,sdkTypes:[Co.DIRECT]}),e.addEnumToInterface({namespace:"Scene",name:"Component",values:w.Component,sdkTypes:[Co.DIRECT]}),e.addEnumToInterface({namespace:"Scene",name:"PathType",values:$o,sdkTypes:[Co.DIRECT]})})(e),function(e,t){const n=oe.create(t,new G(es));e.addAsyncCommandToInterface({namespace:"Scene",name:"register",args:["name","factory"],sdkTypes:[Co.DIRECT]},n)}(e,new we(a)),function(e,t){const n=oe.create(t,new G(ts));e.addAsyncCommandToInterface({namespace:"Scene",name:"createNode",args:[],varArg:!1,sdkTypes:[Co.DIRECT]},n)}(e,new we(a)),function(e,t,n){t.addBinding(ns,(async e=>{const[t]=await n.getDependencies();return t.sceneTags().getObjects(e.tags)})),e.addCommandCreator({namespace:"Scene",name:"query",args:["tags"],sdkTypes:[Co.DIRECT]},(e=>new ns(e)))}(e,t,new we(a,o)),function(e,t,n,a){a.getDependencies().then((([e])=>{n.addBinding(as,(async n=>{n.callback(e.threeRenderer,t,e.getScene().effectComposer)}))})),e.addCommandCreator({namespace:"Scene",name:"configure",args:["callback"],sdkTypes:[Co.DIRECT]},(e=>new as(e)))}(e,n,t,new we(s)),function(e,t,n){n.getDependencies().then((([e])=>{t.addBinding(os,(async t=>e.getSignedUrls().getImageBitmap(t.path,t.width,t.height,t.options)))})),e.addCommandCreator({namespace:"Scene",name:"getImage",args:["path","width","height","options"],sdkTypes:[Co.DIRECT]},(e=>new os(e)))}(e,t,new we(i)),function(e,t){const n=oe.create(t,new G(ss));e.addAsyncCommandToInterface({namespace:"Scene",name:"deserialize",args:["text"],varArg:!1,sdkTypes:[Co.DIRECT]},n)}(e,new we(a)),function(e,t){const n=oe.create(t,new G(rs));e.addCommandToInterface({namespace:"Scene",name:"serialize",args:["objects"],varArg:!1,sdkTypes:[Co.DIRECT]},n)}(e,new we(a)),function(e,t){const n=oe.create(t,new G(cs));e.addAsyncCommandToInterface({namespace:"Scene",name:"createNodes",args:["count","userContext"],varArg:!1,sdkTypes:[Co.DIRECT]},n)}(e,new we(a)),function(e,t){const n=oe.create(t,new G(ds));e.addAsyncCommandToInterface({namespace:"Scene",name:"registerComponents",args:["components"],sdkTypes:[Co.DIRECT]},n)}(e,new we(a)),function(e,t){const n=oe.create(t,new G(ls));e.addAsyncCommandToInterface({namespace:"Scene",name:"createObjects",args:["count"],varArg:!1,sdkTypes:[Co.DIRECT]},n)}(e,new we(a)),function(e,t,n){const a=oe.create(n,new G(hs));e.addCommandToInterface({namespace:"Scene",name:"unregisterComponents",args:["components"],varArg:!1,sdkTypes:[Co.DIRECT]},a)}(e,0,new we(a))}(Et,Lt,y,Le,Te,Ue,Ie),Us(y,r.SourceType,r.sensorVolumes)(Et,new we(xe)),function(e,t,n,a){n.addBinding(t.SettingGetCommand,(async e=>{const[t]=await a.getDependencies();return t.tryGetProperty(e.key,t.settingsData.getOverrideParam(e.key,void 0))})),e.addCommandCreator({namespace:"Settings",name:"get",args:["key"]},(e=>new t.SettingGetCommand(e.key)))}(Et,r,Lt,new we(ke)),function(e,t,n,a){n.addBinding(t.SettingUpdateCommand,(async e=>{if(!e.key||"string"!=typeof e.key)throw Error("Settings: invalid key");const[t]=await a.getDependencies();return t.updateSetting(e.key,e.value),t.tryGetProperty(e.key,void 0)})),e.addCommandCreator({namespace:"Settings",name:"update",args:["key","value"]},(e=>new t.SettingUpdateCommand(e.key,e.value)))}(Et,r,Lt,new we(ke)),function(e,t,n){n.getDependencies().then((([n])=>{new Fs(t.waitForUpdate,n).calcAndBroadcast(e)})),e.addEnumToInterface({namespace:"Stat",name:"Event",values:Vs})}(Et,Lt,new we(re)),function(e,t){const n=oe.create(t,new G(Hs));e.addAsyncCommandToInterface({namespace:"XStorage",name:"setItem",args:["key","data"],origins:ye},n)}(Et,new we(ee)),function(e,t){const n=oe.create(t,new G(Bs));e.addAsyncCommandToInterface({namespace:"XStorage",name:"getItem",args:["key"],origins:ye},n)}(Et,new we(ee)),function(e,t){const n=oe.create(t,new G(zs));e.addAsyncCommandToInterface({namespace:"XStorage",name:"getAllItems",args:["modelId"],origins:ye},n)}(Et,new we(ee)),function(e,t){const n=oe.create(t,new G($s));e.addAsyncCommandToInterface({namespace:"XStorage",name:"removeItem",args:["key"],origins:ye},n)}(Et,new we(ee)),function(e){e.addEnumToInterface({namespace:"Sweep",name:"Transition",values:E}),e.addEnumToInterface({namespace:"Sweep",name:"Alignment",values:p.Alignment}),e.addEnumToInterface({namespace:"Sweep",name:"Placement",values:p.Placement})}(Et),function(e,t,n,a){const o=F.create(a,new qs,new G(Ks,t,n));e.addObservableToInterface({namespace:"Sweep",name:"current"},o)}(Et,r,Lt,new we(de,ue,B,xt,kt)),function(e,t,n,a){e.addEnumToInterface({namespace:"Sweep",name:"Event",values:p.Event});const o=["",""],s=()=>o;n.subscribe(t.BeginMoveToSweepMessage,(async t=>{const n=await a;t.fromSweep&&t.fromSweep!==t.toSweep&&(o[0]=t.fromSweep?n.getIdFromCwfId(t.fromSweep):t.fromSweep,o[1]=n.getIdFromCwfId(t.toSweep),e.broadcast(p.Event.EXIT,s,t))})),n.subscribe(t.BeginSwitchViewmodeMessage,(n=>{n.fromMode===t.Viewmode.Panorama&&n.toMode!==t.Viewmode.Panorama&&(o[0]=i[1],o[1]=void 0,e.broadcast(p.Event.EXIT,s))}));const i=["",""],r=()=>i;n.subscribe(t.EndMoveToSweepMessage,(async t=>{const n=await a;i[0]=t.fromSweep?n.getIdFromCwfId(t.fromSweep):t.fromSweep,i[1]=n.getIdFromCwfId(t.toSweep),e.broadcast(p.Event.ENTER,r)}))}(Et,r,Lt,xt),function(e,t,n,a){const o=oe.create(a,new G(Ys,t,n));e.addEnumToInterface({namespace:"Sweep",name:"Transition",values:E}),e.addCommandToInterface({namespace:"Sweep",name:"moveTo",args:["sweep","options"]},o)}(Et,r,Lt,new we(de,xt,Ee)),function(e,t){const n=oe.create(t,new G(Js));e.addCommandToInterface({namespace:"Sweep.Conversion",name:"createIdMap",args:["invert"]},n);const a=oe.create(t,new G(ei));e.addCommandToInterface({namespace:"Sweep.Conversion",name:"getLabelFromId",args:["id"]},a)}(Et,new we(de)),function(e,t,n){const a=at.create(n,new Xs,new G(Zs,t.conversionUtils,t.sweepPlacementConverter));e.addCollectionToInterface({namespace:"Sweep",name:"data"},a)}(Et,Lt,new we(de,B,xt,kt)),function(e,t){const n=oe.create(t,new G(ti)),a=oe.create(t,new G(ni));e.addCommandToInterface({namespace:"Sweep",name:"enable",args:["sweepIds"],varArg:!0},n),e.addCommandToInterface({namespace:"Sweep",name:"disable",args:["sweepIds"],varArg:!0},a)}(Et,new we(de,le,xt)),function(e,t,n,a){const o=oe.create(a,new G(si,t,n));e.addCommandToInterface({namespace:"Sweep",name:"addNeighbors",args:["sweepId","toAdd"]},o);const s=oe.create(a,new G(ii,t,n));e.addCommandToInterface({namespace:"Sweep",name:"removeNeighbors",args:["sweepId","toRemove"]},s)}(Et,r,Lt,new we(de)),ir(Et,u),function(e){e.addEnumToInterface({namespace:"Tour",name:"PlayState",values:g.PlayState})}(Et),function(e,t,n){e.addEnumToInterface({namespace:"Tour",name:"Event",values:g.Event}),n.subscribe(t.TourStartedMessage,(()=>e.broadcast(g.Event.STARTED))),n.subscribe(t.TourStoppedMessage,(()=>e.broadcast(g.Event.STOPPED))),n.subscribe(t.TourEndedMessage,(()=>e.broadcast(g.Event.ENDED)));const a=[-1],o=e=>(a[0]=e.index,a);n.subscribe(t.TourSteppedMessage,(t=>e.broadcast(g.Event.STEPPED,o,t)))}(Et,r,Lt),ur(Et,r,Lt,new we(_e,he)),function(e,t){const n=F.create(t,new mr,new G(gr));e.addObservableToInterface({namespace:"Tour",name:"currentStep"},n)}(Et,new we(he)),function(e,t,n){const a=F.create(n,new wr,new G(fr,t));e.addObservableToInterface({namespace:"Tour",name:"state"},a)}(Et,Lt,new we(he)),function(e,t){const n=F.create(t,new Ir,new G(Tr));e.addObservableToInterface({namespace:"Tour",name:"transition"},n)}(Et,new we(he)),yt&&It&&(Br(Et,u,{issueCommand:Lt.issueCommand,SetActiveModelViewCommand:r.SetActiveModelViewCommand,AddInMemoryLayerCommand:r.AddInMemoryLayerCommand,ToggleLayerCommand:r.ToggleLayerCommand,SetMoveCameraOnViewChange:r.SetMoveCameraOnViewChange}),yt&&Et.interceptCommands(new zr(Ze,It,(()=>Ze.getActiveLayer()),(e=>h.issueCommand(new r.SelectLayerCommand(e.id,!0)))),Ht.add,Bt.add))}tc.info("Showcase SDK interface version","24.3.1_webgl-2-g390c0e0ad5");var ac=A.w,oc=A.c;export{ac as IdOption,oc as setup};