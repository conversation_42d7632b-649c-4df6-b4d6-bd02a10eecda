<!doctype html>
<html lang="en">
<head>
  <title></title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0, shrink-to-fit=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="mp:graph-query" content="src=graphql/prefetch.gql, query=GetModelPrefetch">
  <style>
    body {
      width: 100%;
      height: 100%;
      color: #fff;
      margin: 0;
      padding: 0;
      overflow: hidden;
      font-family: Roboto;
    }

    #app-container {
      display: flex;
      flex-direction: column;

      /* without this positioning, descendent positions may have undefined behvavior in browsers like Safari that need positioning up the entire hierarchy */
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    main {
      flex: 1;
      position: relative;
      width: 100%;
      background-color: #222;
    }

    #canvas-container {
      position: absolute;
      height: 100%;
      width: 100%;
      transition: width 500ms, height 500ms; /* CANVAS_RESIZE_ANIMATION_TIME */
    }

    canvas:focus {
      outline: none;
    }
  </style>
  <link rel='stylesheet' href='css/showcase.css'>
  <link rel='stylesheet' href='css/unsupported_browser.css'>
</head>

<body class='showcase'>
<div id='unsupported-browser' class='hidden'>
</div>
<section id='app-container'>
  <main aria-labelledby='loading-header'>
    <div id='canvas-container'>
      <div id='control-kit-wrapper'></div>
    </div>
    <div id='loading-gui' class='hidden' data-testid='loading-gui'>
      <div id='loading-background'></div>
      <div id='tint'></div>
      <h1 id='loading-header'></h1>
      <div id='circleLoader' class='circle-loader'>
        <div id='loader-cont'>
          <svg id='svg' class='circle-loader-svg' width='96' height='96' viewPort='0 0 96 96' version='1.1' xmlns='http://www.w3.org/2000/svg'>
            <circle r='44' cx='48' cy='48'></circle>
            <circle id='bar' r='44' cx='48' cy='48'></circle>
          </svg>
          <button id='showcase-play' class='icon-play-unicode hidden' />
        </div>
        <div id='play-prompt' class='hidden'></div>
      </div>
      <h2 id='loading-presented-by' class='hidden'>
        <div class='loading-label'>Presented by</div>
        <div class='subheader'></div>
      </h2>
      <div id='loading-powered-by'>
        <div class='loading-label'>Powered by</div>
        <img id='loading-mp-logo'>
      </div>
    </div>
    <div id="react-render-root"></div>
    <div id="react-overlay-root"></div>
  </main>
</section>

<script src='vendors/three/0.151.3/three.min.js'></script>
<script src='js/browser-check.js'></script>
<script src='js/showcase.js'></script>
<script>
  var detailObject = {
    config: {"apiHost":"https://my.matterport.com","pointerPreventDefault":false,"disableMobileRedirect":true}
  };

  if (mpBrowserCheck.supported()) {
    window.dispatchEvent(new CustomEvent('app-start', { detail: detailObject }));
  } else {
    mpBrowserCheck.displayErrorPage('showcase');
  }
</script>
</body>
</html>
