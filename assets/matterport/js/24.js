/*! For license information please see 24.js.LICENSE.txt */
"use strict";(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[24],{35221:(e,t,s)=>{var n;function r(e){const t=new Date;t.setHours(0,0,0,0),e.setHours(0,0,0,0);const s=(t.getTime()-e.getTime())/864e5;if(0===s)return n.TODAY;if(1===s)return n.YESTERDAY;if(s<7)return n.THIS_WEEK;const r=Math.floor(s/7);return 1===r?n.ONE_WEEK_AGO:2===r?n.TWO_WEEKS_AGO:3===r?n.THREE_WEEKS_AGO:n.OLDER}s.d(t,{Z:()=>n,f:()=>r}),function(e){e.TODAY="TODAY",e.YESTERDAY="YESTERDAY",e.THIS_WEEK="THIS_WEEK",e.ONE_WEEK_AGO="ONE_WEEK_AGO",e.TWO_WEEKS_AGO="TWO_WEEKS_AGO",e.THREE_WEEKS_AGO="THREE_WEEKS_AGO",e.OLDER="OLDER"}(n||(n={}))},13310:(e,t,s)=>{s.d(t,{Y:()=>a});var n=s(89557),r=s(67992);class a extends r.V{constructor(){super(),this.name="cursor",this.opacity=new n.z(0),this.texture=null}}},85042:(e,t,s)=>{var n;s.d(t,{L:()=>n}),function(e){e[e.Reticle=0]="Reticle",e[e.GridPlane=1]="GridPlane"}(n||(n={}))},56843:(e,t,s)=>{s.d(t,{J:()=>g});var n=s(85893),r=s(67294),a=s(94184),i=s.n(a),c=s(76631),l=s(40216),o=s(61531),u=s(31362),d=s(40505),h=s(97593),p=s(44523);const g=(0,r.forwardRef)((({children:e,open:t,className:s,onClose:a,scrollingDisabled:g=!1},k)=>{const f=(0,r.useRef)(null),m=(0,r.useRef)(null),E=(0,r.useMemo)((()=>(0,u.Jm)()),[]),b=(0,l.T)(),[x,y]=(0,r.useState)(!1),T=(0,p.O)();(0,o.U)(d.SN,(e=>{y(e.focused)})),(0,r.useImperativeHandle)(k,(()=>({resetScrollTop:()=>{m.current&&m.current.resetScrollTop()},scrollToSelector:e=>{m.current&&m.current.scrollToSelector(e)}})),[m]);const R=b===c.wS.BOTTOM_PANEL,v=E&&x&&R,S=v&&!function(){var e;if(!m.current)return!1;const t=m.current.getScrollHeight(),s=(null===(e=m.current.getScroller())||void 0===e?void 0:e.getBoundingClientRect().top)||0;return t<=T.height-s}(),C=R,A={open:t,"sticky-header":!v,"detail-panel-align-top":S};return(0,n.jsx)("div",Object.assign({className:i()("detail-panel",A,s),onKeyDown:function(e){a&&"Escape"===e.code&&(e.stopPropagation(),a())},ref:f,tabIndex:t?0:void 0,onTransitionEnd:e=>{f.current&&e.target===f.current&&t&&f.current.focus()}},{children:(0,n.jsx)(h.T,Object.assign({ref:m,hideThumb:C,disabled:g},{children:e}))}))}))},6608:(e,t,s)=>{s.d(t,{C:()=>o});var n=s(85893),r=s(67294),a=s(38772),i=s(94184),c=s.n(i),l=function(e,t,s,n){var r,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,s):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,s,n);else for(var c=e.length-1;c>=0;c--)(r=e[c])&&(i=(a<3?r(i):a>3?r(t,s,i):r(t,s))||i);return a>3&&i&&Object.defineProperty(t,s,i),i};let o=class extends r.Component{constructor(e){super(e)}render(){const{iconClass:e,label:t,badgeStyle:s,onClick:r,className:a,imageUrl:i}=this.props;return(0,n.jsxs)("span",Object.assign({className:c()("badge",a,{clickable:!!r}),style:s,onClick:r},{children:[e&&(0,n.jsx)("span",{className:`icon badge-icon ${e}`}),t&&(0,n.jsx)("span",Object.assign({className:"badge-label"},{children:t})),i&&(0,n.jsx)("span",Object.assign({className:"badge-img"},{children:(0,n.jsx)("img",{src:i})}))]}))}};o=l([a.Z],o)},44472:(e,t,s)=>{s.d(t,{S:()=>g});var n=s(85893),r=s(94184),a=s.n(r),i=s(95160),c=s(52528),l=s(67294);function o(e){const{text:t,keyPrefix:s,markers:r}=e,[a,i]=r,c=[];let o=t,u=o.search(`${a}|${i}`),d=o.search(a);for(;u>-1;){const e=u===d,r=o.slice(0,u),h=t.length-o.length,p=`${s},${h}-${h+u}`;e?c.push((0,n.jsx)(l.Fragment,{children:r},p)):c.push((0,n.jsx)("span",Object.assign({"data-blocktype":"marker"},{children:r}),p)),o=o.slice(u+(e?a.length:i.length),o.length),u=o.search(`${a}|${i}`),d=o.search(a)}if(o.length){const e=`${s},${t.length-o.length}-${t.length}`;c.push((0,n.jsx)(l.Fragment,{children:o},e))}return 0===c.length?(0,n.jsx)(n.Fragment,{children:t}):(0,n.jsx)(n.Fragment,{children:c})}function u(e){const{block:t,children:s,onClick:r}=e;return r?(0,n.jsx)("a",Object.assign({className:`link-annotation ${c.o.USER}`,onClick:r,"data-id":t.id||void 0,"data-value":t.value||void 0,"data-blocktype":t.blockType},{children:s})):(0,n.jsx)("span",Object.assign({"data-blocktype":t.blockType},{children:s}))}function d(e){const{block:t,children:s,onClick:r}=e;return r?(0,n.jsx)("a",Object.assign({className:`link-annotation ${c.o.USER}`,onClick:r,"data-blocktype":t.blockType},{children:s})):(0,n.jsx)("span",Object.assign({"data-blocktype":t.blockType},{children:s}))}function h(e){const{block:t,children:s,linksActive:r,onClick:a}=e;return a||r?(0,n.jsx)("a",Object.assign({href:`${t.value}`,target:"_blank",className:`link-annotation ${c.o.LINK}`,onClick:a,"data-blocktype":t.blockType,"data-value":t.value},{children:s})):(0,n.jsx)("span",Object.assign({"data-blocktype":t.blockType,"data-value":t.value},{children:s}))}function p(e){const{block:t,keyPrefix:s,markers:r}=e,{blockType:a,text:c}=t,l=r?(0,n.jsx)(o,{keyPrefix:s,text:c,markers:r}):(0,n.jsx)(n.Fragment,{children:c});return a===i.C.USER?(0,n.jsx)(u,Object.assign({},e,{children:l})):a===i.C.HASH?(0,n.jsx)(d,Object.assign({},e,{children:l})):a===i.C.LINK&&t.value?(0,n.jsx)(h,Object.assign({},e,{children:l})):l}function g(e){const t=e.textParser.parse(e.text),s=a()("text-box-text",e.className);return(0,n.jsx)("div",Object.assign({className:s},{children:t.map(((t,s)=>(0,n.jsx)(p,{keyPrefix:`${t.blockType}${s}`,block:t,linksActive:!!e.linksActive,onClick:e.onClick,markers:e.markers},`${t.blockType}${s}`)))}))}},52528:(e,t,s)=>{s.d(t,{o:()=>n,v:()=>i});var n,r=s(95160),a=s(31362);!function(e){e.TEXT="",e.LINK="link-url",e.HASH="link-hashtag",e.USER="link-user"}(n||(n={}));class i{constructor(e){this.serialize=e=>{const t=[],s=[].slice.call(e.childNodes);for(const e of s)switch(e.nodeName){case"#text":t.push(this.serializeText(e));break;case"A":t.push(this.serializeAnchor(e));break;case"P":case"DIV":t.push(this.serializeElement(e,!0));break;case"SPAN":t.push(this.serializeElement(e,!1));break;case"BR":break;default:t.push(this.serialize(e))}return t.join("")},this.deserialize=(e,t,s,n=this.markers)=>{const r=this.parse(e).map((e=>this.createNode(e,t,s)));return n?this.parseMarkers(r,n):r},this.serializeText=e=>{let t=this.sanitizeText(e.textContent||"");if(this.links){const e=new RegExp(/https?:\/\/(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?/gi),s=document.createElement("a");t=t.replace(e,(e=>(s.href=e,`[${e}](${s.href})`))),s.remove()}return this.hashtags&&(t=t.replace(/(#\w*)/,(e=>`[${e}]`))),t.replace(/\<\s*br\s*\/?>/gi,"\n")},this.serializeAnchor=e=>{const t=e.dataset.blocktype,s=this.sanitizeText(e.innerText||e.textContent||"");let n=s;switch(t){case r.C.HASH:n=`[${s}]`;break;case r.C.USER:if(this.users){let t=e.dataset.id||"",s=e.dataset.value||"";if(!t&&!s&&(s="@"===n[0]?n.substr(1):n,s)){const e=this.users.getUserInfoByEmail(s);e&&(t=e.id)}(t||this.validateEmail(s))&&(n=`[@${t}](${s})`)}break;case r.C.LINK:const t=e.dataset.value||e.href;t&&(n=`[${s}](${t})`);break;default:e.href&&(n=`[${s}](${e.href})`)}return n},this.serializeElement=(e,t)=>{let s=t?"\n":" ";return e.childNodes.length>0&&(s+=this.serialize(e)),s},this.validateEmail=(()=>{const e=/\S+@\S+\.\S+/,t=document.createElement("input");return t.type="email",t.required=!0,s=>(t.value=s,"function"==typeof t.checkValidity?t.checkValidity():e.test(s))})();const{links:t,hashtags:s,users:n,markers:a}=e;this.links=t||!1,this.hashtags=s||!1,this.users=n,this.markers=a}parseMarkers(e,t){const[s,n]=t;let r=!1;const a=new RegExp(s+"|"+n);for(let t=0;t<e.length;t++){const s=e[t],{nodeType:n,textContent:i}=s,c=3===n;if(!i)continue;c?e.splice(t,1):s.textContent=null;const l=i.split(a);l.forEach(((n,a)=>{if(""!==n){let a;if(r){const e=document.createElement("span");e.setAttribute("data-blocktype","marker"),a=e,a.textContent=n}else a=document.createTextNode(n);c?e.splice(t++,0,a):s.appendChild(a)}a<l.length-1&&(r=!r)})),c&&t--}return e}createNode(e,t,s){switch(e.blockType){case r.C.USER:const a=document.createElement(s?"a":"span");if(a.setAttribute("data-blocktype",e.blockType),s){const t=a;t.className=`link-annotation ${n.USER}`,t.removeAttribute("href"),s&&(t.onclick=s);const r=e.id||"",i=e.value||"";r&&t.setAttribute("data-id",r),i&&t.setAttribute("data-value",i)}return a.textContent=`${e.text}`,a;case r.C.HASH:const i=document.createElement(s?"a":"span");if(i.setAttribute("data-blocktype",e.blockType),s){const e=i;e.className=`link-annotation ${n.HASH}`,e.removeAttribute("href"),s&&(e.onclick=s)}return i.textContent=`${e.text}`,i;case r.C.LINK:if(e.value){const r=document.createElement(s||t?"a":"span");if(r.setAttribute("data-blocktype",e.blockType),r.setAttribute("data-value",e.value),s||t){const a=r;t&&(a.href=`${e.value}`),a.target="_blank",a.className=`link-annotation ${n.LINK}`,s&&(a.onclick=s)}return r.textContent=`${e.text}`,r}}return document.createTextNode(""+e.text)}parse(e){const t=[];if(!e)return[];const s=e.split("[").map(((e,t)=>0===t?e:"["+e));let n=0;for(;n<s.length;){const e=this.parseIntoBlocks(s[n],t);e&&s.splice(n+1,0,e),n++}return t}getPlainText(e){return this.parse(e).map((e=>e.text)).join("")}searchMarkdown(e,t){let s=-1,n=0,r=0;const a=this.parse(e).map((e=>{let a;return this.matchTextBlock(e,t)?(r++,-1===s&&(s=n),a=e.markdown):a=e.text,n+=e.text.length,a}));return-1===s?null:{markdown:a.join(""),matchIndex:s,textLength:n,numMatches:r}}getUserMentions(e){return this.parse(e).filter((e=>e.blockType===r.C.USER&&e.value)).map((e=>e.value||""))}matchTextBlock(e,t){if(e.blockType===t.blockType)if(e.blockType===r.C.USER){if(e.id===t.id)return!0;if(e.value===t.value)return!0}else if(e.blockType===r.C.HASH)return e.text===t.text;return!1}parseIntoBlocks(e,t){if(this.users){const s=this.getUserBlock(e);if(s)return t.push(s),e.slice(s.markdown.length)}if(this.links){const s=this.getLinkBlock(e);if(s&&s.value&&-1===s.value.indexOf("javascript:"))return t.push(s),e.slice(s.markdown.length)}if(this.hashtags){const s=this.getHashBlock(e);if(s)return t.push(s),e.slice(s.markdown.length)}const s=this.getTextBlock(e);return s&&t.push(s),null}getTextBlock(e){return 0===e.length?null:{blockType:r.C.TEXT,text:e,markdown:e}}getHashBlock(e){const t=e.match(i.hashRegex);return!t||t.length<2?null:{blockType:r.C.HASH,markdown:t[0],text:t[1]}}getUserBlock(e){if(!this.users)return null;const t=e.match(i.userRegex);if(!t||t.length<3)return null;let s=t[1].substr(1),n=t[2];const a=n?this.users.getUserInfoByEmail(n):this.users.getUserInfoById(s),c=a?a.name:"";return s=a?a.id:s,n=a?a.email:n,{blockType:r.C.USER,markdown:t[0],id:s,value:n,text:`@${c||n}`}}getLinkBlock(e){const t=e.match(i.linkRegEx);if(!t)return null;const s=t[2];let n=1,a=0;for(;a<s.length&&("("===s[a]?n++:")"===s[a]&&n--,0!==n);)a++;const c=s.length-a;let l=this.conditionallyEncode(t[2].substring(0,a));return-1===l.indexOf("://")&&(l="http://"+l),{blockType:r.C.LINK,markdown:t[0].substring(0,t[0].length-c),text:t[1],value:l}}conditionallyEncode(e){const t=e.trim();return this.needsEncoding(t)?encodeURI(t):t}needsEncoding(e){if(e.match(a.jx))return!0;let t=e;try{t=decodeURI(e)}catch(e){}return!1}sanitizeText(e){return e.replace(/\]\(/g,"]&zwnj;(").replace(/[\u200B]/g,"").replace(/[\u2018\u2019]/g,"'").replace(/[\u201C\u201D]/g,'"')}}i.hashRegex=/\[(#(\w|-)*)\]/,i.userRegex=/\[(@\w*)\]\(([^\)\(\[\]]*)\)/,i.linkRegEx=/\[([^\]]*)\]\((.*)\)/},57370:(e,t,s)=>{s.d(t,{q:()=>i});var n=s(67294),r=s(1358);function a(e){return e?Array.from(e.rooms.values()):[]}function i(){const e=(0,r.S)(),[t,s]=(0,n.useState)(a(e));return(0,n.useEffect)((()=>{if(!e)return()=>{};const t=()=>s(a(e)),n=e.onRoomsChanged({onRemoved:e=>t,onUpdated:e=>t,onChildUpdated:e=>t,onAdded:e=>t});return t(),()=>{n.cancel()}}),[e]),t}},1358:(e,t,s)=>{s.d(t,{S:()=>a});var n=s(45755),r=s(77161);const a=(0,n.u)(r.Z)},56163:(e,t,s)=>{s.d(t,{K:()=>c});var n=s(52528),r=s(71166),a=s(63319);const i=new n.v({});class c{constructor(e,t,s){this.commandBinder=e,this.layersData=t,this.dataTypeGroup=s,this.textParser=i,this.enabled=!0,this.bindings=[]}getGroupingId(e){switch(e){case a.HH.TYPE:return this.getTypeId();case a.HH.FLOOR:return this.getFloorId();case a.HH.ROOM:return this.getRoomId();case a.HH.LAYER:return this.getLayerGroupId();case a.HH.DATE:return this.dateBucket}}getFloorId(){return this.floorId}getRoomId(){return this.roomId}getDateBucket(){return this.dateBucket}getTypeId(){return this.typeId}supportsBatchDelete(){return!1}supportsLayeredCopyMove(){return!1}getLayerGroupId(){var e,t;const s=null===(e=this.layersData)||void 0===e?void 0:e.getBaseLayerId(),n=null===(t=this.layersData)||void 0===t?void 0:t.getViewLayerId();return this.layerId&&n&&this.layerId===s?n:this.layerId}isLayerVisible(){return!this.layersData||!this.layerId||this.layersData.layerVisible(this.layerId)}onSelect(e,t,s){this.commandBinder.issueCommand(new r.IL(this.id,this.typeId))}registerBindings(){}cancelBindings(){this.bindings.forEach((e=>e.cancel()))}}},71166:(e,t,s)=>{s.d(t,{FZ:()=>a,H1:()=>r,Hf:()=>l,IL:()=>u,M8:()=>i,Mp:()=>o,Pe:()=>h,SN:()=>c,c6:()=>d});var n=s(19663);class r extends n.m{constructor(e){super(),this.id="UPDATE_SEARCH_QUERY",this.payload={query:e}}}class a extends n.m{constructor(e){super(),this.id="UPDATE_SEARCH_QUERY_KEYWORDS",this.payload={keywords:e}}}class i extends n.m{constructor(e){super(),this.id="TOGGLE_SEARCH_QUERY_KEYWORD",this.payload={keywordId:e}}}class c extends n.m{constructor(e){super(),this.id="CHANGE_SEARCH_GROUPING",this.payload={grouping:e}}}class l extends n.m{constructor(){super(),this.id="SEARCH_FILTER_CLEAR"}}class o extends n.m{constructor(e,t){super(),this.id="SEARCH_FILTER_TOGGLE",this.payload={groupId:e,enabled:t}}}class u extends n.m{constructor(e,t){super(),this.id="SELECT_SEARCH_RESULT",this.payload={id:e,typeId:t}}}class d extends n.m{constructor(e){super(),this.payload=e,this.id="SEARCH_GROUP_REGISTER"}}class h extends n.m{constructor(e){super(),this.id="SEARCH_GROUP_DEREGISTER",this.payload={id:e}}}},60543:(e,t,s)=>{s.d(t,{PP:()=>n,vr:()=>i,zf:()=>c});const n=["<mark>","</mark>"];function r(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}function a(e,t){const[s,a]=n,i=`${s}|${a}`;let c=!1;if(e.match(new RegExp(i))&&(e=e.replace(new RegExp(`(${i})`,"g"),((e,t)=>t.split("").join("‌"))),c=!0),t){let n;n=c?t.split("").map(r).join("[‌]?"):r(t),e=e.replace(new RegExp(`(${n})`,"gi"),((e,t)=>`${s}${t}${a}`))}return e}const i=a;function c(e,t){return a(function(e,t){var s;const n=e.toLowerCase(),[a,i,c]=[n.indexOf(t),e.length,(null===(s=n.match(new RegExp(`${r(t)}`,"g")))||void 0===s?void 0:s.length)||1];let l=e,o=a;if(i>20&&o>0){if(1===c){const e=Math.floor((20-t.length)/2);e>0&&o>e&&(o-=e)}l=`...${l.substr(o)}`,l.length<20&&(o-=20-l.length,o>0&&(l=`...${e.substr(o)}`))}return l}(e,t),t)}},77230:(e,t,s)=>{s.d(t,{A:()=>a});var n=s(73085),r=s(29883);const a=(0,n.M)(r.T,"query","")},65428:(e,t,s)=>{s.d(t,{A:()=>a});var n=s(73085),r=s(91380);const a=(0,n.M)(r.t,"previousToolName",null)}}]);