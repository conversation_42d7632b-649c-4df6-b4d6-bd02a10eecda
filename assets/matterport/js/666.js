/*! For license information please see 666.js.LICENSE.txt */
(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[666],{43517:(t,e,n)=>{"use strict";function r(t,e=!1){let n=0;for(let e=0,r=t.length;e<r;e++){const i=t[e],s=t[e===r-1?0:e+1];n+=i[0]*s[1],n-=s[0]*i[1]}return e?n/2:Math.abs(n/2)}n.d(e,{m:()=>r})},5696:(t,e,n)=>{"use strict";function r(t){return Math.sqrt(Math.pow(t[1][0]-t[0][0],2)+Math.pow(t[1][1]-t[0][1],2))}function i(t,e,n=0){const i=r(e);return function(t,e,n=0){return Math.abs((r=t,i=e[0],s=e[1],(r[0]-s[0])*(i[1]-s[1])-(r[1]-s[1])*(i[0]-s[0])))<=n;var r,i,s}(t,e,n)&&r([e[0],t])<=i&&r([e[1],t])<=i}n.d(e,{s8:()=>i})},65661:(t,e,n)=>{"use strict";function r(t){return function(t){const e=t[0],n=t[t.length-1];return e[0]===n[0]&&e[1]===n[1]}(t)?t:[...t,t[0]]}n.d(e,{D:()=>c});var i=n(5696);function s(t,e){const[[n,r],[s,o]]=t,[[a,c],[l,h]]=e;if(n===a&&r===c)return!0;if(s===l&&o===h)return!0;if((0,i.s8)(t[0],e)||(0,i.s8)(t[1],e))return!0;if((0,i.s8)(e[0],t)||(0,i.s8)(e[1],t))return!0;const u=(h-c)*(s-n)-(l-a)*(o-r);if(0===u)return!1;const d=r-c,f=n-a,p=((l-a)*d-(h-c)*f)/u,g=((s-n)*d-(o-r)*f)/u;return p>0&&p<1&&g>0&&g<1}function o(t,e){let n=!1;const o=r(e);for(let e=0,r=o.length-1;e<r;e++){const r=o[e],a=o[e+1];if(s(t,[r,a])||(0,i.s8)(r,t)&&(0,i.s8)(a,t)){n=!0;break}}return n}function a(t,e){let n=t[0],r=t[1],i=!1;for(let t=0,s=e.length-1;t<e.length;s=t++){const o=e[t][0],a=e[t][1],c=e[s][0],l=e[s][1];a>r!=l>r&&n<(c-o)*(r-a)/(l-a)+o&&(i=!i)}return i}function c(t,e){let n=!0;const i=r(t);for(let t=0,r=i.length-1;t<r;t++){const r=i[t];if(!a(r,e)){n=!1;break}if(o([r,i[t+1]],e)){n=!1;break}}return n}},26470:t=>{"use strict";function e(t){if("string"!=typeof t)throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function n(t,e){for(var n,r="",i=0,s=-1,o=0,a=0;a<=t.length;++a){if(a<t.length)n=t.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(s===a-1||1===o);else if(s!==a-1&&2===o){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var c=r.lastIndexOf("/");if(c!==r.length-1){-1===c?(r="",i=0):i=(r=r.slice(0,c)).length-1-r.lastIndexOf("/"),s=a,o=0;continue}}else if(2===r.length||1===r.length){r="",i=0,s=a,o=0;continue}e&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+t.slice(s+1,a):r=t.slice(s+1,a),i=a-s-1;s=a,o=0}else 46===n&&-1!==o?++o:o=-1}return r}var r={resolve:function(){for(var t,r="",i=!1,s=arguments.length-1;s>=-1&&!i;s--){var o;s>=0?o=arguments[s]:(void 0===t&&(t=process.cwd()),o=t),e(o),0!==o.length&&(r=o+"/"+r,i=47===o.charCodeAt(0))}return r=n(r,!i),i?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(t){if(e(t),0===t.length)return".";var r=47===t.charCodeAt(0),i=47===t.charCodeAt(t.length-1);return 0!==(t=n(t,!r)).length||r||(t="."),t.length>0&&i&&(t+="/"),r?"/"+t:t},isAbsolute:function(t){return e(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,n=0;n<arguments.length;++n){var i=arguments[n];e(i),i.length>0&&(void 0===t?t=i:t+="/"+i)}return void 0===t?".":r.normalize(t)},relative:function(t,n){if(e(t),e(n),t===n)return"";if((t=r.resolve(t))===(n=r.resolve(n)))return"";for(var i=1;i<t.length&&47===t.charCodeAt(i);++i);for(var s=t.length,o=s-i,a=1;a<n.length&&47===n.charCodeAt(a);++a);for(var c=n.length-a,l=o<c?o:c,h=-1,u=0;u<=l;++u){if(u===l){if(c>l){if(47===n.charCodeAt(a+u))return n.slice(a+u+1);if(0===u)return n.slice(a+u)}else o>l&&(47===t.charCodeAt(i+u)?h=u:0===u&&(h=0));break}var d=t.charCodeAt(i+u);if(d!==n.charCodeAt(a+u))break;47===d&&(h=u)}var f="";for(u=i+h+1;u<=s;++u)u!==s&&47!==t.charCodeAt(u)||(0===f.length?f+="..":f+="/..");return f.length>0?f+n.slice(a+h):(a+=h,47===n.charCodeAt(a)&&++a,n.slice(a))},_makeLong:function(t){return t},dirname:function(t){if(e(t),0===t.length)return".";for(var n=t.charCodeAt(0),r=47===n,i=-1,s=!0,o=t.length-1;o>=1;--o)if(47===(n=t.charCodeAt(o))){if(!s){i=o;break}}else s=!1;return-1===i?r?"/":".":r&&1===i?"//":t.slice(0,i)},basename:function(t,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');e(t);var r,i=0,s=-1,o=!0;if(void 0!==n&&n.length>0&&n.length<=t.length){if(n.length===t.length&&n===t)return"";var a=n.length-1,c=-1;for(r=t.length-1;r>=0;--r){var l=t.charCodeAt(r);if(47===l){if(!o){i=r+1;break}}else-1===c&&(o=!1,c=r+1),a>=0&&(l===n.charCodeAt(a)?-1==--a&&(s=r):(a=-1,s=c))}return i===s?s=c:-1===s&&(s=t.length),t.slice(i,s)}for(r=t.length-1;r>=0;--r)if(47===t.charCodeAt(r)){if(!o){i=r+1;break}}else-1===s&&(o=!1,s=r+1);return-1===s?"":t.slice(i,s)},extname:function(t){e(t);for(var n=-1,r=0,i=-1,s=!0,o=0,a=t.length-1;a>=0;--a){var c=t.charCodeAt(a);if(47!==c)-1===i&&(s=!1,i=a+1),46===c?-1===n?n=a:1!==o&&(o=1):-1!==n&&(o=-1);else if(!s){r=a+1;break}}return-1===n||-1===i||0===o||1===o&&n===i-1&&n===r+1?"":t.slice(n,i)},format:function(t){if(null===t||"object"!=typeof t)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return function(t,e){var n=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||"");return n?n===e.root?n+r:n+t+r:r}("/",t)},parse:function(t){e(t);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return n;var r,i=t.charCodeAt(0),s=47===i;s?(n.root="/",r=1):r=0;for(var o=-1,a=0,c=-1,l=!0,h=t.length-1,u=0;h>=r;--h)if(47!==(i=t.charCodeAt(h)))-1===c&&(l=!1,c=h+1),46===i?-1===o?o=h:1!==u&&(u=1):-1!==o&&(u=-1);else if(!l){a=h+1;break}return-1===o||-1===c||0===u||1===u&&o===c-1&&o===a+1?-1!==c&&(n.base=n.name=0===a&&s?t.slice(1,c):t.slice(a,c)):(0===a&&s?(n.name=t.slice(1,o),n.base=t.slice(1,c)):(n.name=t.slice(a,o),n.base=t.slice(a,c)),n.ext=t.slice(o,c)),a>0?n.dir=t.slice(0,a-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,t.exports=r},3614:(t,e,n)=>{"use strict";t.exports=i;var r=n(80645);function i(t){this.buf=ArrayBuffer.isView&&ArrayBuffer.isView(t)?t:new Uint8Array(t||0),this.pos=0,this.type=0,this.length=this.buf.length}i.Varint=0,i.Fixed64=1,i.Bytes=2,i.Fixed32=5;var s=4294967296,o=1/s;function a(t){return t.type===i.Bytes?t.readVarint()+t.pos:t.pos+1}function c(t,e,n){return n?4294967296*e+(t>>>0):4294967296*(e>>>0)+(t>>>0)}function l(t,e,n){var r=e<=16383?1:e<=2097151?2:e<=268435455?3:Math.ceil(Math.log(e)/(7*Math.LN2));n.realloc(r);for(var i=n.pos-1;i>=t;i--)n.buf[i+r]=n.buf[i]}function h(t,e){for(var n=0;n<t.length;n++)e.writeVarint(t[n])}function u(t,e){for(var n=0;n<t.length;n++)e.writeSVarint(t[n])}function d(t,e){for(var n=0;n<t.length;n++)e.writeFloat(t[n])}function f(t,e){for(var n=0;n<t.length;n++)e.writeDouble(t[n])}function p(t,e){for(var n=0;n<t.length;n++)e.writeBoolean(t[n])}function g(t,e){for(var n=0;n<t.length;n++)e.writeFixed32(t[n])}function m(t,e){for(var n=0;n<t.length;n++)e.writeSFixed32(t[n])}function x(t,e){for(var n=0;n<t.length;n++)e.writeFixed64(t[n])}function y(t,e){for(var n=0;n<t.length;n++)e.writeSFixed64(t[n])}function w(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16)+16777216*t[e+3]}function b(t,e,n){t[n]=e,t[n+1]=e>>>8,t[n+2]=e>>>16,t[n+3]=e>>>24}function _(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16)+(t[e+3]<<24)}i.prototype={destroy:function(){this.buf=null},readFields:function(t,e,n){for(n=n||this.length;this.pos<n;){var r=this.readVarint(),i=r>>3,s=this.pos;this.type=7&r,t(i,e,this),this.pos===s&&this.skip(r)}return e},readMessage:function(t,e){return this.readFields(t,e,this.readVarint()+this.pos)},readFixed32:function(){var t=w(this.buf,this.pos);return this.pos+=4,t},readSFixed32:function(){var t=_(this.buf,this.pos);return this.pos+=4,t},readFixed64:function(){var t=w(this.buf,this.pos)+w(this.buf,this.pos+4)*s;return this.pos+=8,t},readSFixed64:function(){var t=w(this.buf,this.pos)+_(this.buf,this.pos+4)*s;return this.pos+=8,t},readFloat:function(){var t=r.read(this.buf,this.pos,!0,23,4);return this.pos+=4,t},readDouble:function(){var t=r.read(this.buf,this.pos,!0,52,8);return this.pos+=8,t},readVarint:function(t){var e,n,r=this.buf;return e=127&(n=r[this.pos++]),n<128?e:(e|=(127&(n=r[this.pos++]))<<7,n<128?e:(e|=(127&(n=r[this.pos++]))<<14,n<128?e:(e|=(127&(n=r[this.pos++]))<<21,n<128?e:function(t,e,n){var r,i,s=n.buf;if(i=s[n.pos++],r=(112&i)>>4,i<128)return c(t,r,e);if(i=s[n.pos++],r|=(127&i)<<3,i<128)return c(t,r,e);if(i=s[n.pos++],r|=(127&i)<<10,i<128)return c(t,r,e);if(i=s[n.pos++],r|=(127&i)<<17,i<128)return c(t,r,e);if(i=s[n.pos++],r|=(127&i)<<24,i<128)return c(t,r,e);if(i=s[n.pos++],r|=(1&i)<<31,i<128)return c(t,r,e);throw new Error("Expected varint not more than 10 bytes")}(e|=(15&(n=r[this.pos]))<<28,t,this))))},readVarint64:function(){return this.readVarint(!0)},readSVarint:function(){var t=this.readVarint();return t%2==1?(t+1)/-2:t/2},readBoolean:function(){return Boolean(this.readVarint())},readString:function(){var t=this.readVarint()+this.pos,e=function(t,e,n){var r="",i=e;for(;i<n;){var s,o,a,c=t[i],l=null,h=c>239?4:c>223?3:c>191?2:1;if(i+h>n)break;1===h?c<128&&(l=c):2===h?128==(192&(s=t[i+1]))&&(l=(31&c)<<6|63&s)<=127&&(l=null):3===h?(s=t[i+1],o=t[i+2],128==(192&s)&&128==(192&o)&&((l=(15&c)<<12|(63&s)<<6|63&o)<=2047||l>=55296&&l<=57343)&&(l=null)):4===h&&(s=t[i+1],o=t[i+2],a=t[i+3],128==(192&s)&&128==(192&o)&&128==(192&a)&&((l=(15&c)<<18|(63&s)<<12|(63&o)<<6|63&a)<=65535||l>=1114112)&&(l=null)),null===l?(l=65533,h=1):l>65535&&(l-=65536,r+=String.fromCharCode(l>>>10&1023|55296),l=56320|1023&l),r+=String.fromCharCode(l),i+=h}return r}(this.buf,this.pos,t);return this.pos=t,e},readBytes:function(){var t=this.readVarint()+this.pos,e=this.buf.subarray(this.pos,t);return this.pos=t,e},readPackedVarint:function(t,e){var n=a(this);for(t=t||[];this.pos<n;)t.push(this.readVarint(e));return t},readPackedSVarint:function(t){var e=a(this);for(t=t||[];this.pos<e;)t.push(this.readSVarint());return t},readPackedBoolean:function(t){var e=a(this);for(t=t||[];this.pos<e;)t.push(this.readBoolean());return t},readPackedFloat:function(t){var e=a(this);for(t=t||[];this.pos<e;)t.push(this.readFloat());return t},readPackedDouble:function(t){var e=a(this);for(t=t||[];this.pos<e;)t.push(this.readDouble());return t},readPackedFixed32:function(t){var e=a(this);for(t=t||[];this.pos<e;)t.push(this.readFixed32());return t},readPackedSFixed32:function(t){var e=a(this);for(t=t||[];this.pos<e;)t.push(this.readSFixed32());return t},readPackedFixed64:function(t){var e=a(this);for(t=t||[];this.pos<e;)t.push(this.readFixed64());return t},readPackedSFixed64:function(t){var e=a(this);for(t=t||[];this.pos<e;)t.push(this.readSFixed64());return t},skip:function(t){var e=7&t;if(e===i.Varint)for(;this.buf[this.pos++]>127;);else if(e===i.Bytes)this.pos=this.readVarint()+this.pos;else if(e===i.Fixed32)this.pos+=4;else{if(e!==i.Fixed64)throw new Error("Unimplemented type: "+e);this.pos+=8}},writeTag:function(t,e){this.writeVarint(t<<3|e)},realloc:function(t){for(var e=this.length||16;e<this.pos+t;)e*=2;if(e!==this.length){var n=new Uint8Array(e);n.set(this.buf),this.buf=n,this.length=e}},finish:function(){return this.length=this.pos,this.pos=0,this.buf.subarray(0,this.length)},writeFixed32:function(t){this.realloc(4),b(this.buf,t,this.pos),this.pos+=4},writeSFixed32:function(t){this.realloc(4),b(this.buf,t,this.pos),this.pos+=4},writeFixed64:function(t){this.realloc(8),b(this.buf,-1&t,this.pos),b(this.buf,Math.floor(t*o),this.pos+4),this.pos+=8},writeSFixed64:function(t){this.realloc(8),b(this.buf,-1&t,this.pos),b(this.buf,Math.floor(t*o),this.pos+4),this.pos+=8},writeVarint:function(t){(t=+t||0)>268435455||t<0?function(t,e){var n,r;t>=0?(n=t%4294967296|0,r=t/4294967296|0):(r=~(-t/4294967296),4294967295^(n=~(-t%4294967296))?n=n+1|0:(n=0,r=r+1|0));if(t>=0x10000000000000000||t<-0x10000000000000000)throw new Error("Given varint doesn't fit into 10 bytes");e.realloc(10),function(t,e,n){n.buf[n.pos++]=127&t|128,t>>>=7,n.buf[n.pos++]=127&t|128,t>>>=7,n.buf[n.pos++]=127&t|128,t>>>=7,n.buf[n.pos++]=127&t|128,t>>>=7,n.buf[n.pos]=127&t}(n,0,e),function(t,e){var n=(7&t)<<4;if(e.buf[e.pos++]|=n|((t>>>=3)?128:0),!t)return;if(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),!t)return;e.buf[e.pos++]=127&t}(r,e)}(t,this):(this.realloc(4),this.buf[this.pos++]=127&t|(t>127?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),t<=127||(this.buf[this.pos++]=t>>>7&127))))},writeSVarint:function(t){this.writeVarint(t<0?2*-t-1:2*t)},writeBoolean:function(t){this.writeVarint(Boolean(t))},writeString:function(t){t=String(t),this.realloc(4*t.length),this.pos++;var e=this.pos;this.pos=function(t,e,n){for(var r,i,s=0;s<e.length;s++){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){r>56319||s+1===e.length?(t[n++]=239,t[n++]=191,t[n++]=189):i=r;continue}if(r<56320){t[n++]=239,t[n++]=191,t[n++]=189,i=r;continue}r=i-55296<<10|r-56320|65536,i=null}else i&&(t[n++]=239,t[n++]=191,t[n++]=189,i=null);r<128?t[n++]=r:(r<2048?t[n++]=r>>6|192:(r<65536?t[n++]=r>>12|224:(t[n++]=r>>18|240,t[n++]=r>>12&63|128),t[n++]=r>>6&63|128),t[n++]=63&r|128)}return n}(this.buf,t,this.pos);var n=this.pos-e;n>=128&&l(e,n,this),this.pos=e-1,this.writeVarint(n),this.pos+=n},writeFloat:function(t){this.realloc(4),r.write(this.buf,t,this.pos,!0,23,4),this.pos+=4},writeDouble:function(t){this.realloc(8),r.write(this.buf,t,this.pos,!0,52,8),this.pos+=8},writeBytes:function(t){var e=t.length;this.writeVarint(e),this.realloc(e);for(var n=0;n<e;n++)this.buf[this.pos++]=t[n]},writeRawMessage:function(t,e){this.pos++;var n=this.pos;t(e,this);var r=this.pos-n;r>=128&&l(n,r,this),this.pos=n-1,this.writeVarint(r),this.pos+=r},writeMessage:function(t,e,n){this.writeTag(t,i.Bytes),this.writeRawMessage(e,n)},writePackedVarint:function(t,e){this.writeMessage(t,h,e)},writePackedSVarint:function(t,e){this.writeMessage(t,u,e)},writePackedBoolean:function(t,e){this.writeMessage(t,p,e)},writePackedFloat:function(t,e){this.writeMessage(t,d,e)},writePackedDouble:function(t,e){this.writeMessage(t,f,e)},writePackedFixed32:function(t,e){this.writeMessage(t,g,e)},writePackedSFixed32:function(t,e){this.writeMessage(t,m,e)},writePackedFixed64:function(t,e){this.writeMessage(t,x,e)},writePackedSFixed64:function(t,e){this.writeMessage(t,y,e)},writeBytesField:function(t,e){this.writeTag(t,i.Bytes),this.writeBytes(e)},writeFixed32Field:function(t,e){this.writeTag(t,i.Fixed32),this.writeFixed32(e)},writeSFixed32Field:function(t,e){this.writeTag(t,i.Fixed32),this.writeSFixed32(e)},writeFixed64Field:function(t,e){this.writeTag(t,i.Fixed64),this.writeFixed64(e)},writeSFixed64Field:function(t,e){this.writeTag(t,i.Fixed64),this.writeSFixed64(e)},writeVarintField:function(t,e){this.writeTag(t,i.Varint),this.writeVarint(e)},writeSVarintField:function(t,e){this.writeTag(t,i.Varint),this.writeSVarint(e)},writeStringField:function(t,e){this.writeTag(t,i.Bytes),this.writeString(e)},writeFloatField:function(t,e){this.writeTag(t,i.Fixed32),this.writeFloat(e)},writeDoubleField:function(t,e){this.writeTag(t,i.Fixed64),this.writeDouble(e)},writeBooleanField:function(t,e){this.writeVarintField(t,Boolean(e))}}},4061:(t,e,n)=>{"use strict";var r=n(43842);function i(t,e,n){var i,a,c,l;e=e||1;for(var h=0;h<t[0].length;h++){var u=t[0][h];(!h||u[0]<i)&&(i=u[0]),(!h||u[1]<a)&&(a=u[1]),(!h||u[0]>c)&&(c=u[0]),(!h||u[1]>l)&&(l=u[1])}var d=c-i,f=l-a,p=Math.min(d,f),g=p/2;if(0===p){var m=[i,a];return m.distance=0,m}for(var x=new r(void 0,s),y=i;y<c;y+=p)for(var w=a;w<l;w+=p)x.push(new o(y+g,w+g,g,t));var b=function(t){for(var e=0,n=0,r=0,i=t[0],s=0,a=i.length,c=a-1;s<a;c=s++){var l=i[s],h=i[c],u=l[0]*h[1]-h[0]*l[1];n+=(l[0]+h[0])*u,r+=(l[1]+h[1])*u,e+=3*u}return 0===e?new o(i[0][0],i[0][1],0,t):new o(n/e,r/e,0,t)}(t),_=new o(i+d/2,a+f/2,0,t);_.d>b.d&&(b=_);for(var v=x.length;x.length;){var T=x.pop();T.d>b.d&&(b=T,n&&console.log("found best %d after %d probes",Math.round(1e4*T.d)/1e4,v)),T.max-b.d<=e||(g=T.h/2,x.push(new o(T.x-g,T.y-g,g,t)),x.push(new o(T.x+g,T.y-g,g,t)),x.push(new o(T.x-g,T.y+g,g,t)),x.push(new o(T.x+g,T.y+g,g,t)),v+=4)}n&&(console.log("num probes: "+v),console.log("best distance: "+b.d));var S=[b.x,b.y];return S.distance=b.d,S}function s(t,e){return e.max-t.max}function o(t,e,n,r){this.x=t,this.y=e,this.h=n,this.d=function(t,e,n){for(var r=!1,i=1/0,s=0;s<n.length;s++)for(var o=n[s],c=0,l=o.length,h=l-1;c<l;h=c++){var u=o[c],d=o[h];u[1]>e!=d[1]>e&&t<(d[0]-u[0])*(e-u[1])/(d[1]-u[1])+u[0]&&(r=!r),i=Math.min(i,a(t,e,u,d))}return 0===i?0:(r?1:-1)*Math.sqrt(i)}(t,e,r),this.max=this.d+this.h*Math.SQRT2}function a(t,e,n,r){var i=n[0],s=n[1],o=r[0]-i,a=r[1]-s;if(0!==o||0!==a){var c=((t-i)*o+(e-s)*a)/(o*o+a*a);c>1?(i=r[0],s=r[1]):c>0&&(i+=o*c,s+=a*c)}return(o=t-i)*o+(a=e-s)*a}r.default&&(r=r.default),t.exports=i,t.exports.default=i},82582:function(t){t.exports=function(){"use strict";function t(t,r,i,s,o){!function t(n,r,i,s,o){for(;s>i;){if(s-i>600){var a=s-i+1,c=r-i+1,l=Math.log(a),h=.5*Math.exp(2*l/3),u=.5*Math.sqrt(l*h*(a-h)/a)*(c-a/2<0?-1:1);t(n,r,Math.max(i,Math.floor(r-c*h/a+u)),Math.min(s,Math.floor(r+(a-c)*h/a+u)),o)}var d=n[r],f=i,p=s;for(e(n,i,r),o(n[s],d)>0&&e(n,i,s);f<p;){for(e(n,f,p),f++,p--;o(n[f],d)<0;)f++;for(;o(n[p],d)>0;)p--}0===o(n[i],d)?e(n,i,p):e(n,++p,s),p<=r&&(i=p+1),r<=p&&(s=p-1)}}(t,r,i||0,s||t.length-1,o||n)}function e(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function n(t,e){return t<e?-1:t>e?1:0}var r=function(t){void 0===t&&(t=9),this._maxEntries=Math.max(4,t),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function i(t,e,n){if(!n)return e.indexOf(t);for(var r=0;r<e.length;r++)if(n(t,e[r]))return r;return-1}function s(t,e){o(t,0,t.children.length,e,t)}function o(t,e,n,r,i){i||(i=p(null)),i.minX=1/0,i.minY=1/0,i.maxX=-1/0,i.maxY=-1/0;for(var s=e;s<n;s++){var o=t.children[s];a(i,t.leaf?r(o):o)}return i}function a(t,e){return t.minX=Math.min(t.minX,e.minX),t.minY=Math.min(t.minY,e.minY),t.maxX=Math.max(t.maxX,e.maxX),t.maxY=Math.max(t.maxY,e.maxY),t}function c(t,e){return t.minX-e.minX}function l(t,e){return t.minY-e.minY}function h(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function u(t){return t.maxX-t.minX+(t.maxY-t.minY)}function d(t,e){return t.minX<=e.minX&&t.minY<=e.minY&&e.maxX<=t.maxX&&e.maxY<=t.maxY}function f(t,e){return e.minX<=t.maxX&&e.minY<=t.maxY&&e.maxX>=t.minX&&e.maxY>=t.minY}function p(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function g(e,n,r,i,s){for(var o=[n,r];o.length;)if(!((r=o.pop())-(n=o.pop())<=i)){var a=n+Math.ceil((r-n)/i/2)*i;t(e,a,n,r,s),o.push(n,a,a,r)}}return r.prototype.all=function(){return this._all(this.data,[])},r.prototype.search=function(t){var e=this.data,n=[];if(!f(t,e))return n;for(var r=this.toBBox,i=[];e;){for(var s=0;s<e.children.length;s++){var o=e.children[s],a=e.leaf?r(o):o;f(t,a)&&(e.leaf?n.push(o):d(t,a)?this._all(o,n):i.push(o))}e=i.pop()}return n},r.prototype.collides=function(t){var e=this.data;if(!f(t,e))return!1;for(var n=[];e;){for(var r=0;r<e.children.length;r++){var i=e.children[r],s=e.leaf?this.toBBox(i):i;if(f(t,s)){if(e.leaf||d(t,s))return!0;n.push(i)}}e=n.pop()}return!1},r.prototype.load=function(t){if(!t||!t.length)return this;if(t.length<this._minEntries){for(var e=0;e<t.length;e++)this.insert(t[e]);return this}var n=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===n.height)this._splitRoot(this.data,n);else{if(this.data.height<n.height){var r=this.data;this.data=n,n=r}this._insert(n,this.data.height-n.height-1,!0)}else this.data=n;return this},r.prototype.insert=function(t){return t&&this._insert(t,this.data.height-1),this},r.prototype.clear=function(){return this.data=p([]),this},r.prototype.remove=function(t,e){if(!t)return this;for(var n,r,s,o=this.data,a=this.toBBox(t),c=[],l=[];o||c.length;){if(o||(o=c.pop(),r=c[c.length-1],n=l.pop(),s=!0),o.leaf){var h=i(t,o.children,e);if(-1!==h)return o.children.splice(h,1),c.push(o),this._condense(c),this}s||o.leaf||!d(o,a)?r?(n++,o=r.children[n],s=!1):o=null:(c.push(o),l.push(n),n=0,r=o,o=o.children[0])}return this},r.prototype.toBBox=function(t){return t},r.prototype.compareMinX=function(t,e){return t.minX-e.minX},r.prototype.compareMinY=function(t,e){return t.minY-e.minY},r.prototype.toJSON=function(){return this.data},r.prototype.fromJSON=function(t){return this.data=t,this},r.prototype._all=function(t,e){for(var n=[];t;)t.leaf?e.push.apply(e,t.children):n.push.apply(n,t.children),t=n.pop();return e},r.prototype._build=function(t,e,n,r){var i,o=n-e+1,a=this._maxEntries;if(o<=a)return s(i=p(t.slice(e,n+1)),this.toBBox),i;r||(r=Math.ceil(Math.log(o)/Math.log(a)),a=Math.ceil(o/Math.pow(a,r-1))),(i=p([])).leaf=!1,i.height=r;var c=Math.ceil(o/a),l=c*Math.ceil(Math.sqrt(a));g(t,e,n,l,this.compareMinX);for(var h=e;h<=n;h+=l){var u=Math.min(h+l-1,n);g(t,h,u,c,this.compareMinY);for(var d=h;d<=u;d+=c){var f=Math.min(d+c-1,u);i.children.push(this._build(t,d,f,r-1))}}return s(i,this.toBBox),i},r.prototype._chooseSubtree=function(t,e,n,r){for(;r.push(e),!e.leaf&&r.length-1!==n;){for(var i=1/0,s=1/0,o=void 0,a=0;a<e.children.length;a++){var c=e.children[a],l=h(c),u=(d=t,f=c,(Math.max(f.maxX,d.maxX)-Math.min(f.minX,d.minX))*(Math.max(f.maxY,d.maxY)-Math.min(f.minY,d.minY))-l);u<s?(s=u,i=l<i?l:i,o=c):u===s&&l<i&&(i=l,o=c)}e=o||e.children[0]}var d,f;return e},r.prototype._insert=function(t,e,n){var r=n?t:this.toBBox(t),i=[],s=this._chooseSubtree(r,this.data,e,i);for(s.children.push(t),a(s,r);e>=0&&i[e].children.length>this._maxEntries;)this._split(i,e),e--;this._adjustParentBBoxes(r,i,e)},r.prototype._split=function(t,e){var n=t[e],r=n.children.length,i=this._minEntries;this._chooseSplitAxis(n,i,r);var o=this._chooseSplitIndex(n,i,r),a=p(n.children.splice(o,n.children.length-o));a.height=n.height,a.leaf=n.leaf,s(n,this.toBBox),s(a,this.toBBox),e?t[e-1].children.push(a):this._splitRoot(n,a)},r.prototype._splitRoot=function(t,e){this.data=p([t,e]),this.data.height=t.height+1,this.data.leaf=!1,s(this.data,this.toBBox)},r.prototype._chooseSplitIndex=function(t,e,n){for(var r,i,s,a,c,l,u,d=1/0,f=1/0,p=e;p<=n-e;p++){var g=o(t,0,p,this.toBBox),m=o(t,p,n,this.toBBox),x=(i=g,s=m,a=void 0,c=void 0,l=void 0,u=void 0,a=Math.max(i.minX,s.minX),c=Math.max(i.minY,s.minY),l=Math.min(i.maxX,s.maxX),u=Math.min(i.maxY,s.maxY),Math.max(0,l-a)*Math.max(0,u-c)),y=h(g)+h(m);x<d?(d=x,r=p,f=y<f?y:f):x===d&&y<f&&(f=y,r=p)}return r||n-e},r.prototype._chooseSplitAxis=function(t,e,n){var r=t.leaf?this.compareMinX:c,i=t.leaf?this.compareMinY:l;this._allDistMargin(t,e,n,r)<this._allDistMargin(t,e,n,i)&&t.children.sort(r)},r.prototype._allDistMargin=function(t,e,n,r){t.children.sort(r);for(var i=this.toBBox,s=o(t,0,e,i),c=o(t,n-e,n,i),l=u(s)+u(c),h=e;h<n-e;h++){var d=t.children[h];a(s,t.leaf?i(d):d),l+=u(s)}for(var f=n-e-1;f>=e;f--){var p=t.children[f];a(c,t.leaf?i(p):p),l+=u(c)}return l},r.prototype._adjustParentBBoxes=function(t,e,n){for(var r=n;r>=0;r--)a(e[r],t)},r.prototype._condense=function(t){for(var e=t.length-1,n=void 0;e>=0;e--)0===t[e].children.length?e>0?(n=t[e-1].children).splice(n.indexOf(t[e]),1):this.clear():s(t[e],this.toBBox)},r}()},43842:(t,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>r});class r{constructor(t=[],e=i){if(this.data=t,this.length=this.data.length,this.compare=e,this.length>0)for(let t=(this.length>>1)-1;t>=0;t--)this._down(t)}push(t){this.data.push(t),this.length++,this._up(this.length-1)}pop(){if(0===this.length)return;const t=this.data[0],e=this.data.pop();return this.length--,this.length>0&&(this.data[0]=e,this._down(0)),t}peek(){return this.data[0]}_up(t){const{data:e,compare:n}=this,r=e[t];for(;t>0;){const i=t-1>>1,s=e[i];if(n(r,s)>=0)break;e[t]=s,t=i}e[t]=r}_down(t){const{data:e,compare:n}=this,r=this.length>>1,i=e[t];for(;t<r;){let r=1+(t<<1),s=e[r];const o=r+1;if(o<this.length&&n(e[o],s)<0&&(r=o,s=e[o]),n(s,i)>=0)break;e[t]=s,t=r}e[t]=i}}function i(t,e){return t<e?-1:t>e?1:0}},35048:(t,e,n)=>{"use strict";n.d(e,{I:()=>lt});var r=n(26470);function i(t){let e;try{e=new URL(t,"http://fakehost.com/")}catch(t){return null}const n=e.pathname.split("/").pop(),r=n.lastIndexOf(".");if(-1===r||r===n.length-1)return null;return n.substring(r+1)}class s{constructor(){this.maxSize=800,this.minSize=600,this.unloadPercent=.05,this.itemSet=new Map,this.itemList=[],this.usedSet=new Set,this.callbacks=new Map,this.unloadPriorityCallback=null;const t=this.itemSet;this.defaultPriorityCallback=e=>t.get(e)}isFull(){return this.itemSet.size>=this.maxSize}add(t,e){const n=this.itemSet;if(n.has(t))return!1;if(this.isFull())return!1;const r=this.usedSet,i=this.itemList,s=this.callbacks;return i.push(t),r.add(t),n.set(t,Date.now()),s.set(t,e),!0}remove(t){const e=this.usedSet,n=this.itemSet,r=this.itemList,i=this.callbacks;if(n.has(t)){i.get(t)(t);const s=r.indexOf(t);return r.splice(s,1),e.delete(t),n.delete(t),i.delete(t),!0}return!1}markUsed(t){const e=this.itemSet,n=this.usedSet;e.has(t)&&!n.has(t)&&(e.set(t,Date.now()),n.add(t))}markAllUnused(){this.usedSet.clear()}unloadUnusedContent(){const t=this.unloadPercent,e=this.minSize,n=this.itemList,r=this.itemSet,i=this.usedSet,s=this.callbacks,o=n.length-i.size,a=n.length-e,c=this.unloadPriorityCallback||this.defaultPriorityCallback;if(a>0&&o>0){n.sort(((t,e)=>{const n=i.has(t),r=i.has(e);return n&&r?0:n||r?n?1:-1:c(e)-c(t)}));const l=Math.min(a,o),h=Math.max(e*t,l*t);let u=Math.min(h,o);u=Math.ceil(u);const d=n.splice(0,u);for(let t=0,e=d.length;t<e;t++){const e=d[t];s.get(e)(e),r.delete(e),s.delete(e)}}}scheduleUnload(t=!0){var e;this.scheduled||(this.scheduled=!0,e=()=>{this.scheduled=!1,this.unloadUnusedContent(),t&&this.markAllUnused()},Promise.resolve().then(e))}}var o=n(17099);function a(t){return 3===t||4===t}function c(t,e){return t.__lastFrameVisited===e&&t.__used}function l(t,e){t.__lastFrameVisited!==e&&(t.__lastFrameVisited=e,t.__used=!1,t.__inFrustum=!1,t.__isLeaf=!1,t.__visible=!1,t.__active=!1,t.__error=1/0,t.__distanceFromCamera=1/0,t.__childrenWereVisible=!1,t.__allChildrenLoaded=!1)}function h(t,e,n){if(l(t,e),t.__used=!0,n.markUsed(t),t.__contentEmpty){const r=t.children;for(let t=0,i=r.length;t<i;t++)h(r[t],e,n)}}function u(t,e,n){if(t.__contentEmpty&&(!t.__externalTileSet||a(t.__loadingState))){const r=t.children;for(let t=0,i=r.length;t<i;t++){const i=r[t];i.__depthFromRenderedParent=e,u(i,e,n)}}else n.requestTileContents(t)}function d(t,e=null,n=null,r=null,i=0){if(e&&e(t,r,i))return void(n&&n(t,r,i));const s=t.children;for(let r=0,o=s.length;r<o;r++)d(s[r],e,n,t,i+1);n&&n(t,r,i)}function f(t,e){const n=e.stats,r=e.frameCount,i=e.errorTarget,s=e.maxDepth,o=e.loadSiblings,a=e.lruCache,c=e.stopAtEmptyTiles;l(t,r);if(!1===e.tileInView(t))return!1;if(t.__used=!0,a.markUsed(t),t.__inFrustum=!0,n.inFrustum++,(c||!t.__contentEmpty)&&!t.__externalTileSet){e.calculateError(t);if(t.__error<=i)return!0;if(e.maxDepth>0&&t.__depth+1>=s)return!0}let u=!1;const d=t.children;for(let t=0,n=d.length;t<n;t++){const n=f(d[t],e);u=u||n}if(u&&o)for(let t=0,e=d.length;t<e;t++){h(d[t],r,a)}return!0}function p(t,e){const n=e.stats,r=e.frameCount;if(!c(t,r))return;n.used++;const i=t.children;let s=!1;for(let t=0,e=i.length;t<e;t++){const e=i[t];s=s||c(e,r)}if(s){let n=!1,s=!0;for(let t=0,o=i.length;t<o;t++){const o=i[t];if(p(o,e),n=n||o.__wasSetVisible||o.__childrenWereVisible,c(o,r)){const t=o.__allChildrenLoaded||!o.__contentEmpty&&a(o.__loadingState)||o.__externalTileSet&&4===o.__loadingState;s=s&&t}}t.__childrenWereVisible=n,t.__allChildrenLoaded=s}else t.__isLeaf=!0}function g(t,e){const n=e.stats,r=e.frameCount;if(!c(t,r))return;const i=t.parent,s=i?i.__depthFromRenderedParent:-1;t.__depthFromRenderedParent=s;const o=e.lruCache;if(t.__isLeaf)return t.__depthFromRenderedParent++,void(3===t.__loadingState?(t.__inFrustum&&(t.__visible=!0,n.visible++),t.__active=!0,n.active++):o.isFull()||t.__contentEmpty&&!t.__externalTileSet||e.requestTileContents(t));const l=(e.errorTarget+1)*e.errorThreshold,h=t.__error<=l,d=h||"ADD"===t.refine,f=!t.__contentEmpty,p=f||t.__externalTileSet,m=a(t.__loadingState)&&p,x=t.__childrenWereVisible,y=t.children;let w=t.__allChildrenLoaded;if(d&&f&&t.__depthFromRenderedParent++,d&&!m&&!o.isFull()&&p&&e.requestTileContents(t),(h&&!w&&!x&&m||"ADD"===t.refine&&m)&&(t.__inFrustum&&(t.__visible=!0,n.visible++),t.__active=!0,n.active++),"ADD"!==t.refine&&h&&!w&&m)for(let n=0,i=y.length;n<i;n++){const i=y[n];c(i,r)&&!o.isFull()&&(i.__depthFromRenderedParent=t.__depthFromRenderedParent+1,u(i,i.__depthFromRenderedParent,e))}else for(let t=0,n=y.length;t<n;t++){const n=y[t];c(n,r)&&g(n,e)}}function m(t,e){const n=c(t,e.frameCount);if(n||t.__usedLastFrame){let r=!1,i=!1;n&&(r=t.__active,i=e.displayActiveTiles&&t.__active||t.__visible),t.__contentEmpty||3!==t.__loadingState||(t.__wasSetActive!==r&&e.setTileActive(t,r),t.__wasSetVisible!==i&&e.setTileVisible(t,i)),t.__wasSetActive=r,t.__wasSetVisible=i,t.__usedLastFrame=n;const s=t.children;for(let t=0,n=s.length;t<n;t++){m(s[t],e)}}}const x=(t,e)=>t.__depth!==e.__depth?t.__depth>e.__depth?-1:1:t.__inFrustum!==e.__inFrustum?t.__inFrustum?1:-1:t.__used!==e.__used?t.__used?1:-1:t.__error!==e.__error?t.__error>e.__error?1:-1:t.__distanceFromCamera!==e.__distanceFromCamera?t.__distanceFromCamera>e.__distanceFromCamera?-1:1:0,y=t=>1/(t.__depthFromRenderedParent+1);function w(t){return(new TextDecoder).decode(t)}class b{constructor(t,e,n,r){this.buffer=t,this.binOffset=e+n,this.binLength=r;let i=null;if(0!==n){const r=new Uint8Array(t,e,n);i=JSON.parse(w(r))}else i={};this.header=i}getKeys(){return Object.keys(this.header)}getData(t,e,n=null,r=null){const i=this.header;if(!(t in i))return null;const s=i[t];if(s instanceof Object){if(Array.isArray(s))return s;{const{buffer:i,binOffset:o,binLength:a}=this,c=s.byteOffset||0,l=s.type||r,h=s.componentType||n;if("type"in s&&r&&s.type!==r)throw new Error("FeatureTable: Specified type does not match expected type.");let u,d;switch(l){case"SCALAR":u=1;break;case"VEC2":u=2;break;case"VEC3":u=3;break;case"VEC4":u=4;break;default:throw new Error(`FeatureTable : Feature type not provided for "${t}".`)}const f=o+c,p=e*u;switch(h){case"BYTE":d=new Int8Array(i,f,p);break;case"UNSIGNED_BYTE":d=new Uint8Array(i,f,p);break;case"SHORT":d=new Int16Array(i,f,p);break;case"UNSIGNED_SHORT":d=new Uint16Array(i,f,p);break;case"INT":d=new Int32Array(i,f,p);break;case"UNSIGNED_INT":d=new Uint32Array(i,f,p);break;case"FLOAT":d=new Float32Array(i,f,p);break;case"DOUBLE":d=new Float64Array(i,f,p);break;default:throw new Error(`FeatureTable : Feature component type not provided for "${t}".`)}if(f+p*d.BYTES_PER_ELEMENT>o+a)throw new Error("FeatureTable: Feature data read outside binary body length.");return d}}return s}}class _ extends b{constructor(t,e,n,r,i){super(t,n,r,i),this.batchSize=e}getData(t,e=null,n=null){return super.getData(t,this.batchSize,e,n)}}class v{constructor(){this.fetchOptions={},this.workingPath=""}load(t){return fetch(t,this.fetchOptions).then((e=>{if(!e.ok)throw new Error(`Failed to load file "${t}" with status ${e.status} : ${e.statusText}`);return e.arrayBuffer()})).then((e=>(""===this.workingPath&&(this.workingPath=this.workingPathForURL(t)),this.parse(e))))}resolveExternalURL(t){return/^[^\\/]/.test(t)?this.workingPath+"/"+t:t}workingPathForURL(t){const e=t.split(/[\\/]/g);e.pop();return e.join("/")+"/"}parse(t){throw new Error("LoaderBase: Parse not implemented.")}}class T extends v{parse(t){const e=new DataView(t),n=String.fromCharCode(e.getUint8(0))+String.fromCharCode(e.getUint8(1))+String.fromCharCode(e.getUint8(2))+String.fromCharCode(e.getUint8(3));console.assert("b3dm"===n);const r=e.getUint32(4,!0);console.assert(1===r);const i=e.getUint32(8,!0);console.assert(i===t.byteLength);const s=e.getUint32(12,!0),o=e.getUint32(16,!0),a=e.getUint32(20,!0),c=e.getUint32(24,!0),l=t.slice(28,28+s+o),h=new b(l,0,s,o),u=28+s+o,d=t.slice(u,u+a+c),f=new _(d,h.getData("BATCH_LENGTH"),0,a,c),p=u+a+c;return{version:r,featureTable:h,batchTable:f,glbBytes:new Uint8Array(t,p,i-p)}}}var S=n(81396),M=n(1217);class B extends T{constructor(t=S.DefaultLoadingManager){super(),this.manager=t}parse(t){const e=super.parse(t),n=e.glbBytes.slice().buffer;return new Promise(((t,r)=>{const i=this.manager,s=this.fetchOptions,o=i.getHandler("path.gltf")||new M.GLTFLoader(i);"include"===s.credentials&&"cors"===s.mode&&o.setCrossOrigin("use-credentials"),"credentials"in s&&o.setWithCredentials("include"===s.credentials),s.headers&&o.setRequestHeader(s.headers);let a=this.workingPath;!/[\\/]$/.test(a)&&a.length&&(a+="/"),o.parse(n,a,(n=>{const{batchTable:r,featureTable:i}=e,{scene:s}=n,o=i.getData("RTC_CENTER");o&&(s.position.x+=o[0],s.position.y+=o[1],s.position.z+=o[2]),n.batchTable=r,n.featureTable=i,s.batchTable=r,s.featureTable=i,t(n)}),r)}))}}class P extends v{parse(t){const e=new DataView(t),n=String.fromCharCode(e.getUint8(0))+String.fromCharCode(e.getUint8(1))+String.fromCharCode(e.getUint8(2))+String.fromCharCode(e.getUint8(3));console.assert("pnts"===n);const r=e.getUint32(4,!0);console.assert(1===r);const i=e.getUint32(8,!0);console.assert(i===t.byteLength);const s=e.getUint32(12,!0),o=e.getUint32(16,!0),a=e.getUint32(20,!0),c=e.getUint32(24,!0),l=t.slice(28,28+s+o),h=new b(l,0,s,o),u=28+s+o,d=t.slice(u,u+a+c),f=new _(d,h.getData("BATCH_LENGTH")||h.getData("POINTS_LENGTH"),0,a,c);return Promise.resolve({version:r,featureTable:h,batchTable:f})}}class A extends P{constructor(t=S.DefaultLoadingManager){super(),this.manager=t}parse(t){return super.parse(t).then((t=>{const{featureTable:e}=t,n=e.getData("POINTS_LENGTH"),r=e.getData("POSITION",n,"FLOAT","VEC3"),i=e.getData("RGB",n,"UNSIGNED_BYTE","VEC3");["RTC_CENTER","QUANTIZED_VOLUME_OFFSET","QUANTIZED_VOLUME_SCALE","CONSTANT_RGBA","BATCH_LENGTH","POSITION_QUANTIZED","RGBA","RGB565","NORMAL","NORMAL_OCT16P"].forEach((t=>{t in e.header&&console.warn(`PNTSLoader: Unsupported FeatureTable feature "${t}" detected.`)}));const s=new S.BufferGeometry;s.setAttribute("position",new S.BufferAttribute(r,3,!1));const o=new S.PointsMaterial;o.size=2,o.sizeAttenuation=!1,null!==i&&(s.setAttribute("color",new S.BufferAttribute(i,3,!0)),o.vertexColors=!0);const a=new S.Points(s,o);t.scene=a,t.scene.featureTable=e;const c=e.getData("RTC_CENTER");return c&&(t.scene.position.x+=c[0],t.scene.position.y+=c[1],t.scene.position.z+=c[2]),t}))}}class F extends v{parse(t){const e=new DataView(t),n=String.fromCharCode(e.getUint8(0))+String.fromCharCode(e.getUint8(1))+String.fromCharCode(e.getUint8(2))+String.fromCharCode(e.getUint8(3));console.assert("i3dm"===n);const r=e.getUint32(4,!0);console.assert(1===r);const i=e.getUint32(8,!0);console.assert(i===t.byteLength);const s=e.getUint32(12,!0),o=e.getUint32(16,!0),a=e.getUint32(20,!0),c=e.getUint32(24,!0),l=e.getUint32(28,!0),h=t.slice(32,32+s+o),u=new b(h,0,s,o),d=32+s+o,f=t.slice(d,d+a+c),p=new _(f,u.getData("INSTANCES_LENGTH"),0,a,c),g=d+a+c,m=new Uint8Array(t,g,i-g);let x=null,y=null;if(l)x=m,y=Promise.resolve();else{const t=this.resolveExternalURL(w(m));y=fetch(t,this.fetchOptions).then((e=>{if(!e.ok)throw new Error(`I3DMLoaderBase : Failed to load file "${t}" with status ${e.status} : ${e.statusText}`);return e.arrayBuffer()})).then((t=>{x=new Uint8Array(t)}))}return y.then((()=>({version:r,featureTable:u,batchTable:p,glbBytes:x})))}}const C=new S.Vector3,V=new S.Vector3,U=new S.Vector3,k=new S.Vector3,L=new S.Quaternion,E=new S.Vector3,R=new S.Matrix4;class O extends F{constructor(t=S.DefaultLoadingManager){super(),this.manager=t}resolveExternalURL(t){return this.manager.resolveURL(super.resolveExternalURL(t))}parse(t){return super.parse(t).then((t=>{const{featureTable:e,batchTable:n}=t,r=t.glbBytes.slice().buffer;return new Promise(((t,i)=>{const s=this.fetchOptions,o=this.manager,a=o.getHandler("path.gltf")||new M.GLTFLoader(o);"include"===s.credentials&&"cors"===s.mode&&a.setCrossOrigin("use-credentials"),"credentials"in s&&a.setWithCredentials("include"===s.credentials),s.headers&&a.setRequestHeader(s.headers);let c=this.workingPath;/[\\/]$/.test(c)||(c+="/"),a.parse(r,c,(r=>{const i=e.getData("INSTANCES_LENGTH"),s=e.getData("POSITION",i,"FLOAT","VEC3"),o=e.getData("NORMAL_UP",i,"FLOAT","VEC3"),a=e.getData("NORMAL_RIGHT",i,"FLOAT","VEC3"),c=e.getData("SCALE_NON_UNIFORM",i,"FLOAT","VEC3"),l=e.getData("SCALE",i,"FLOAT","SCALAR");["RTC_CENTER","QUANTIZED_VOLUME_OFFSET","QUANTIZED_VOLUME_SCALE","EAST_NORTH_UP","POSITION_QUANTIZED","NORMAL_UP_OCT32P","NORMAL_RIGHT_OCT32P"].forEach((t=>{t in e.header&&console.warn(`I3DMLoader: Unsupported FeatureTable feature "${t}" detected.`)}));const h=new Map,u=[];r.scene.traverse((t=>{if(t.isMesh){const{geometry:e,material:n}=t,r=new S.InstancedMesh(e,n,i);r.position.copy(t.position),r.rotation.copy(t.rotation),r.scale.copy(t.scale),u.push(r),h.set(t,r)}}));const d=new S.Vector3;for(let t=0;t<i;t++)d.x+=s[3*t+0]/i,d.y+=s[3*t+1]/i,d.z+=s[3*t+2]/i;h.forEach(((t,e)=>{const n=e.parent;n&&(n.remove(e),n.add(t),t.updateMatrixWorld(),t.position.copy(d).applyMatrix4(t.matrixWorld))}));for(let t=0;t<i;t++){k.set(s[3*t+0]-d.x,s[3*t+1]-d.y,s[3*t+2]-d.z),o?(V.set(o[3*t+0],o[3*t+1],o[3*t+2]),U.set(a[3*t+0],a[3*t+1],a[3*t+2]),C.crossVectors(U,V).normalize(),R.makeBasis(U,V,C),L.setFromRotationMatrix(R)):L.set(0,0,0,1),l?E.setScalar(l[t]):c?E.set(c[3*t+0],c[3*t+1],c[3*t+2]):E.set(1,1,1),R.compose(k,L,E);for(let e=0,n=u.length;e<n;e++){u[e].setMatrixAt(t,R)}}r.batchTable=n,r.featureTable=e,r.scene.batchTable=n,r.scene.featureTable=e,t(r)}),i)}))}))}}class I extends v{parse(t){const e=new DataView(t),n=String.fromCharCode(e.getUint8(0))+String.fromCharCode(e.getUint8(1))+String.fromCharCode(e.getUint8(2))+String.fromCharCode(e.getUint8(3));console.assert("cmpt"===n,'CMPTLoader: The magic bytes equal "cmpt".');const r=e.getUint32(4,!0);console.assert(1===r,'CMPTLoader: The version listed in the header is "1".');const i=e.getUint32(8,!0);console.assert(i===t.byteLength,"CMPTLoader: The contents buffer length listed in the header matches the file.");const s=e.getUint32(12,!0),o=[];let a=16;for(let e=0;e<s;e++){const e=new DataView(t,a,12),n=String.fromCharCode(e.getUint8(0))+String.fromCharCode(e.getUint8(1))+String.fromCharCode(e.getUint8(2))+String.fromCharCode(e.getUint8(3)),r=e.getUint32(4,!0),i=e.getUint32(8,!0),s=new Uint8Array(t,a,i);o.push({type:n,buffer:s,version:r}),a+=i}return{version:r,tiles:o}}}class D extends I{constructor(t=S.DefaultLoadingManager){super(),this.manager=t}parse(t){const e=super.parse(t),n=this.manager,r=[];for(const t in e.tiles){const{type:i,buffer:s}=e.tiles[t];switch(i){case"b3dm":{const t=s.slice(),e=new B(n);e.workingPath=this.workingPath,e.fetchOptions=this.fetchOptions;const i=e.parse(t.buffer);r.push(i);break}case"pnts":{const t=s.slice(),e=new A(n);e.workingPath=this.workingPath,e.fetchOptions=this.fetchOptions;const i=e.parse(t.buffer);r.push(i);break}case"i3dm":{const t=s.slice(),e=new O(n);e.workingPath=this.workingPath,e.fetchOptions=this.fetchOptions;const i=e.parse(t.buffer);r.push(i);break}}}return Promise.all(r).then((t=>{const e=new S.Group;return t.forEach((t=>{e.add(t.scene)})),{tiles:t,scene:e}}))}}class N extends v{constructor(t=S.DefaultLoadingManager){super(),this.manager=t}parse(t){return new Promise(((e,n)=>{const r=this.manager,i=this.fetchOptions;let s=r.getHandler("path.gltf")||r.getHandler("path.glb");s||(s=new M.GLTFLoader(r),"include"===i.credentials&&"cors"===i.mode&&s.setCrossOrigin("use-credentials"),"credentials"in i&&s.setWithCredentials("include"===i.credentials),i.headers&&s.setRequestHeader(i.headers));let o=s.resourcePath||s.path||this.workingPath;!/[\\/]$/.test(o)&&o.length&&(o+="/"),s.parse(t,o,(t=>{e(t)}),n)}))}}const z=new S.Matrix4;class X extends S.Group{constructor(t){super(),this.name="TilesRenderer.TilesGroup",this.tilesRenderer=t}raycast(t,e){this.tilesRenderer.optimizeRaycast&&this.tilesRenderer.raycast(t,e)}updateMatrixWorld(t){if(this.matrixAutoUpdate&&this.updateMatrix(),this.matrixWorldNeedsUpdate||t){null===this.parent?z.copy(this.matrix):z.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorldNeedsUpdate=!1;const t=z.elements,e=this.matrixWorld.elements;let n=!1;for(let r=0;r<16;r++){const i=t[r],s=e[r];if(Math.abs(i-s)>Number.EPSILON){n=!0;break}}if(n){this.matrixWorld.copy(z);const t=this.children;for(let e=0,n=t.length;e<n;e++)t[e].updateMatrixWorld()}}}}const Y=new S.Sphere,H=new S.Matrix4,q=new S.Vector3,G=new S.Vector3,j=new S.Ray,W=[];function $(t,e){return t.distance-e.distance}function J(t,e,n){t.traverse((t=>{Object.getPrototypeOf(t).raycast.call(t,e,n)}))}function Q(t,e,n,r){if(n.has(t)){if(J(t.cached.scene,r,W),W.length>0){W.length>1&&W.sort($);const t=W[0];return W.length=0,t}return null}const i=[],s=t.children;for(let t=0,n=s.length;t<n;t++){const n=s[t],o=n.cached,a=e.matrixWorld;H.copy(a);const c=o.sphere;if(c&&(Y.copy(c),Y.applyMatrix4(H),!r.ray.intersectsSphere(Y)))continue;const l=o.box,h=o.boxTransform;if(l){if(H.multiply(h).invert(),j.copy(r.ray),j.applyMatrix4(H),!j.intersectBox(l,q))continue;{let t;G.setFromMatrixScale(H),t=G.x,Math.abs(Math.max(G.x-G.y,G.x-G.z))>1e-6&&console.warn("ThreeTilesRenderer : Non uniform scale used for tile which may cause issues when raycasting.");let e={distance:1/0,tile:null};i.push(e),e.distance=q.distanceToSquared(j.origin)*t*t,e.tile=n}}}i.sort($);let o=1/0,a=null;for(let t=0,s=i.length;t<s;t++){const s=i[t];if(s.distance>o)break;{const t=s.tile,i=t.cached.scene;let c=null;if(n.has(t)?(J(i,r,W),W.length>0&&(W.length>1&&W.sort($),c=W[0])):c=Q(t,e,n,r),c){const t=c.distance*c.distance;t<o&&(o=t,a=c),W.length=0}}}return a}function Z(t,e,n,r,i){const s=t.cached,o=e.matrixWorld;H.copy(o);const a=s.sphere;if(a&&(Y.copy(a),Y.applyMatrix4(H),!r.ray.intersectsSphere(Y)))return;const c=s.box,l=s.boxTransform;if(c&&(H.multiply(l).invert(),j.copy(r.ray).applyMatrix4(H),!j.intersectsBox(c)))return;const h=s.scene;if(n.has(t))return void J(h,r,i);const u=t.children;for(let t=0,s=u.length;t<s;t++)Z(u[t],e,n,r,i)}const K=Symbol("INITIAL_FRUSTUM_CULLED"),tt=new S.Matrix4,et=new S.Matrix4,nt=new S.Vector3,rt=new S.Vector3,it=new S.Vector3,st=new S.Vector3,ot=new S.Vector3(1,0,0),at=new S.Vector3(0,1,0);function ct(t,e){t.traverse((t=>{t.frustumCulled=t[K]&&e}))}class lt extends class{get rootTileSet(){const t=this.tileSets[this.rootURL];return!t||t instanceof Promise?null:t}get root(){const t=this.rootTileSet;return t?t.root:null}constructor(t){this.tileSets={},this.rootURL=t,this.fetchOptions={},this.preprocessURL=null;const e=new s;e.unloadPriorityCallback=y;const n=new o.Z;n.maxJobs=4,n.priorityCallback=x;const r=new o.Z;r.maxJobs=1,r.priorityCallback=x,this.lruCache=e,this.downloadQueue=n,this.parseQueue=r,this.stats={parsing:0,downloading:0,failed:0,inFrustum:0,used:0,active:0,visible:0},this.frameCount=0,this.errorTarget=6,this.errorThreshold=1/0,this.loadSiblings=!0,this.displayActiveTiles=!1,this.maxDepth=1/0,this.stopAtEmptyTiles=!0}traverse(t,e){const n=this.tileSets[this.rootURL];n&&n.root&&d(n.root,t,e)}update(){const t=this.stats,e=this.lruCache,n=this.tileSets,r=n[this.rootURL];if(!(this.rootURL in n))return void this.loadRootTileSet(this.rootURL);if(!r||!r.root)return;const i=r.root;t.inFrustum=0,t.used=0,t.active=0,t.visible=0,this.frameCount++,f(i,this),p(i,this),g(i,this),m(i,this),e.scheduleUnload()}parseTile(t,e,n){return null}disposeTile(t){}preprocessNode(t,e,n){t.content&&(!("uri"in t.content)&&"url"in t.content&&(t.content.uri=t.content.url,delete t.content.url),t.content.uri&&(t.content.uri=function(...t){const e=/^[a-zA-Z]+:\/\//;let n=-1;for(let r=0,i=t.length;r<i;r++)e.test(t[r])&&(n=r);if(-1===n)return r.join(...t).replace(/\\/g,"/");{const i=n<=0?t:t.slice(n),s=i[0].match(e)[0];return i[0]=i[0].substring(s.length),(s+r.join(...i)).replace(/\\/g,"/")}}(n,t.content.uri)),t.content.boundingVolume&&!("box"in t.content.boundingVolume||"sphere"in t.content.boundingVolume||"region"in t.content.boundingVolume)&&delete t.content.boundingVolume),t.parent=e,t.children=t.children||[];if(t.content&&t.content.uri){const e=i(t.content.uri),n=Boolean(e&&"json"===e.toLowerCase());t.__externalTileSet=n,t.__contentEmpty=n}else t.__externalTileSet=!1,t.__contentEmpty=!0;t.__distanceFromCamera=1/0,t.__error=1/0,t.__inFrustum=!1,t.__isLeaf=!1,t.__usedLastFrame=!1,t.__used=!1,t.__wasSetVisible=!1,t.__visible=!1,t.__childrenWereVisible=!1,t.__allChildrenLoaded=!1,t.__wasSetActive=!1,t.__active=!1,t.__loadingState=0,t.__loadIndex=0,t.__loadAbort=null,t.__depthFromRenderedParent=-1,null===e?(t.__depth=0,t.refine=t.refine||"REPLACE"):(t.__depth=e.__depth+1,t.refine=t.refine||e.refine)}setTileActive(t,e){}setTileVisible(t,e){}calculateError(t){return 0}tileInView(t){return!0}fetchTileSet(t,e,n=null){return fetch(t,e).then((e=>{if(e.ok)return e.json();throw new Error(`TilesRenderer: Failed to load tileset "${t}" with status ${e.status} : ${e.statusText}`)})).then((e=>{const i=e.asset.version;console.assert("1.0"===i||"0.0"===i,'asset.version is expected to be a string of "1.0" or "0.0"');const s=r.dirname(t);return d(e.root,((t,e)=>this.preprocessNode(t,e,s)),null,n,n?n.__depth:0),e}))}loadRootTileSet(t){const e=this.tileSets;if(t in e)return e[t]instanceof Error?Promise.reject(e[t]):Promise.resolve(e[t]);{const n=this.fetchTileSet(this.preprocessURL?this.preprocessURL(t):t,this.fetchOptions).then((n=>{e[t]=n}));return n.catch((n=>{console.error(n),e[t]=n})),e[t]=n,n}}requestTileContents(t){if(0!==t.__loadingState)return;const e=this.stats,n=this.lruCache,r=this.downloadQueue,s=this.parseQueue,o=t.__externalTileSet;n.add(t,(t=>{1===t.__loadingState?(t.__loadAbort.abort(),t.__loadAbort=null):o?t.children.length=0:this.disposeTile(t),1===t.__loadingState?e.downloading--:2===t.__loadingState&&e.parsing--,t.__loadingState=0,t.__loadIndex++,s.remove(t),r.remove(t)})),t.__loadIndex++;const a=t.__loadIndex,c=new AbortController,l=c.signal;e.downloading++,t.__loadAbort=c,t.__loadingState=1;const h=i=>{t.__loadIndex===a&&("AbortError"!==i.name?(s.remove(t),r.remove(t),2===t.__loadingState?e.parsing--:1===t.__loadingState&&e.downloading--,e.failed++,console.error(`TilesRenderer : Failed to load tile at url "${t.content.uri}".`),console.error(i),t.__loadingState=4):n.remove(t))};o?r.add(t,(t=>{if(t.__loadIndex!==a)return Promise.resolve();const e=this.preprocessURL?this.preprocessURL(t.content.uri):t.content.uri;return this.fetchTileSet(e,Object.assign({signal:l},this.fetchOptions),t)})).then((n=>{t.__loadIndex===a&&(e.downloading--,t.__loadAbort=null,t.__loadingState=3,t.children.push(n.root))})).catch(h):r.add(t,(t=>{if(t.__loadIndex!==a)return Promise.resolve();const e=this.preprocessURL?this.preprocessURL(t.content.uri):t.content.uri;return fetch(e,Object.assign({signal:l},this.fetchOptions))})).then((e=>{if(t.__loadIndex===a){if(e.ok)return e.arrayBuffer();throw new Error(`Failed to load model with error code ${e.status}`)}})).then((n=>{if(t.__loadIndex===a)return e.downloading--,e.parsing++,t.__loadAbort=null,t.__loadingState=2,s.add(t,(t=>{if(t.__loadIndex!==a)return Promise.resolve();const e=i(t.content.uri);return this.parseTile(n,t,e)}))})).then((()=>{t.__loadIndex===a&&(e.parsing--,t.__loadingState=3,t.__wasSetVisible&&this.setTileVisible(t,!0),t.__wasSetActive&&this.setTileActive(t,!0))})).catch(h)}dispose(){const t=this.lruCache;this.traverse((e=>{t.remove(e)}))}}{get autoDisableRendererCulling(){return this._autoDisableRendererCulling}set autoDisableRendererCulling(t){this._autoDisableRendererCulling!==t&&(super._autoDisableRendererCulling=t,this.forEachLoadedModel((e=>{ct(e,!t)})))}constructor(...t){super(...t),this.group=new X(this),this.cameras=[],this.cameraMap=new Map,this.cameraInfo=[],this.activeTiles=new Set,this.visibleTiles=new Set,this._autoDisableRendererCulling=!0,this.optimizeRaycast=!0,this.onLoadTileSet=null,this.onLoadModel=null,this.onDisposeModel=null,this.onTileVisibilityChange=null;const e=new S.LoadingManager;e.setURLModifier((t=>this.preprocessURL?this.preprocessURL(t):t)),this.manager=e;const n=this;this._overridenRaycast=function(t,e){n.optimizeRaycast||Object.getPrototypeOf(this).raycast.call(this,t,e)}}getBounds(t){if(!this.root)return!1;const e=this.root.cached,n=e.box,r=e.boxTransform;return!!n&&(t.copy(n),t.applyMatrix4(r),!0)}getOrientedBounds(t,e){if(!this.root)return!1;const n=this.root.cached,r=n.box,i=n.boxTransform;return!!r&&(t.copy(r),e.copy(i),!0)}getBoundingSphere(t){if(!this.root)return!1;const e=this.root.cached.sphere;return!!e&&(t.copy(e),!0)}forEachLoadedModel(t){this.traverse((e=>{const n=e.cached.scene;n&&t(n,e)}))}raycast(t,e){if(this.root)if(t.firstHitOnly){const n=Q(this.root,this.group,this.activeTiles,t);n&&e.push(n)}else Z(this.root,this.group,this.activeTiles,t,e)}hasCamera(t){return this.cameraMap.has(t)}setCamera(t){const e=this.cameras,n=this.cameraMap;return!n.has(t)&&(n.set(t,new S.Vector2),e.push(t),!0)}setResolution(t,e,n){const r=this.cameraMap;return!!r.has(t)&&(e instanceof S.Vector2?r.get(t).copy(e):r.get(t).set(e,n),!0)}setResolutionFromRenderer(t,e){const n=this.cameraMap;if(!n.has(t))return!1;const r=n.get(t);return e.getSize(r),r.multiplyScalar(e.getPixelRatio()),!0}deleteCamera(t){const e=this.cameras,n=this.cameraMap;if(n.has(t)){const r=e.indexOf(t);return e.splice(r,1),n.delete(t),!0}return!1}fetchTileSet(t,...e){const n=super.fetchTileSet(t,...e);return n.then((e=>{this.onLoadTileSet&&Promise.resolve().then((()=>{this.onLoadTileSet(e,t)}))})),n}update(){const t=this.group,e=this.cameras,n=this.cameraMap,r=this.cameraInfo;if(0===e.length)return void console.warn("TilesRenderer: no cameras defined. Cannot update 3d tiles.");for(;r.length>e.length;)r.pop();for(;r.length<e.length;)r.push({frustum:new S.Frustum,isOrthographic:!1,sseDenominator:-1,position:new S.Vector3,invScale:-1,pixelSize:0});let i;et.copy(t.matrixWorld).invert(),nt.setFromMatrixScale(et),i=nt.x,Math.abs(Math.max(nt.x-nt.y,nt.x-nt.z))>1e-6&&console.warn("ThreeTilesRenderer : Non uniform scale used for tile which may cause issues when calculating screen space error.");for(let s=0,o=r.length;s<o;s++){const o=e[s],a=r[s],c=a.frustum,l=a.position,h=n.get(o);0!==h.width&&0!==h.height||console.warn("TilesRenderer: resolution for camera error calculation is not set.");const u=o.projectionMatrix.elements;if(a.isOrthographic=1===u[15],a.isOrthographic){const t=2/u[0],e=2/u[5];a.pixelSize=Math.max(e/h.height,t/h.width)}else a.sseDenominator=2/u[5]/h.height;a.invScale=i,tt.copy(t.matrixWorld),tt.premultiply(o.matrixWorldInverse),tt.premultiply(o.projectionMatrix),c.setFromProjectionMatrix(tt),l.set(0,0,0),l.applyMatrix4(o.matrixWorld),l.applyMatrix4(et)}super.update()}preprocessNode(t,e,n){super.preprocessNode(t,e,n);const r=new S.Matrix4;if(t.transform){const e=t.transform;for(let t=0;t<16;t++)r.elements[t]=e[t]}else r.identity();e&&r.premultiply(e.cached.transform);const i=(new S.Matrix4).copy(r).invert();let s=null,o=null,a=null;if("box"in t.boundingVolume){const e=t.boundingVolume.box;s=new S.Box3,o=new S.Matrix4,a=new S.Matrix4,rt.set(e[3],e[4],e[5]),it.set(e[6],e[7],e[8]),st.set(e[9],e[10],e[11]);const n=rt.length(),i=it.length(),c=st.length();rt.normalize(),it.normalize(),st.normalize(),0===n&&rt.crossVectors(it,st),0===i&&it.crossVectors(rt,st),0===c&&st.crossVectors(rt,it),o.set(rt.x,it.x,st.x,e[0],rt.y,it.y,st.y,e[1],rt.z,it.z,st.z,e[2],0,0,0,1),o.premultiply(r),a.copy(o).invert(),s.min.set(-n,-i,-c),s.max.set(n,i,c)}let c=null;if("sphere"in t.boundingVolume){const e=t.boundingVolume.sphere;c=new S.Sphere,c.center.set(e[0],e[1],e[2]),c.radius=e[3],c.applyMatrix4(r)}else if("box"in t.boundingVolume){const e=t.boundingVolume.box;c=new S.Sphere,s.getBoundingSphere(c),c.center.set(e[0],e[1],e[2]),c.applyMatrix4(r)}"region"in t.boundingVolume&&console.warn("ThreeTilesRenderer: region bounding volume not supported."),t.cached={loadIndex:0,transform:r,transformInverse:i,active:!1,inFrustum:[],box:s,boxTransform:o,boxTransformInverse:a,sphere:c,region:null,scene:null,geometry:null,material:null}}parseTile(t,e,n){e._loadIndex=e._loadIndex||0,e._loadIndex++;const r=e.content.uri.split(/[\\\/]/g);r.pop();const i=r.join("/"),s=this.fetchOptions,o=this.manager,a=e._loadIndex;let c=null;switch(n){case"b3dm":{const e=new B(o);e.workingPath=i,e.fetchOptions=s,c=e.parse(t).then((t=>t.scene));break}case"pnts":{const e=new A(o);e.workingPath=i,e.fetchOptions=s,c=e.parse(t).then((t=>t.scene));break}case"i3dm":{const e=new O(o);e.workingPath=i,e.fetchOptions=s,c=e.parse(t).then((t=>t.scene));break}case"cmpt":{const e=new D(o);e.workingPath=i,e.fetchOptions=s,c=e.parse(t).then((t=>t.scene));break}case"gltf":case"glb":const e=new N(o);e.workingPath=i,e.fetchOptions=s,c=e.parse(t).then((t=>t.scene));break;default:console.warn(`TilesRenderer: Content type "${n}" not supported.`),c=Promise.resolve(null)}return c.then((t=>{if(e._loadIndex!==a)return;const r=this.rootTileSet.asset&&this.rootTileSet.asset.gltfUpAxis||"y",i=e.cached,s=i.transform;switch(r.toLowerCase()){case"x":tt.makeRotationAxis(at,-Math.PI/2);break;case"y":tt.makeRotationAxis(ot,Math.PI/2);break;case"z":tt.identity()}t.updateMatrix(),"pnts"!==n&&t.matrix.multiply(tt),t.matrix.premultiply(s),t.matrix.decompose(t.position,t.quaternion,t.scale),t.traverse((t=>{t[K]=t.frustumCulled})),ct(t,!this.autoDisableRendererCulling),i.scene=t,t.traverse((t=>{t.raycast=this._overridenRaycast}));const o=[],c=[],l=[];t.traverse((t=>{if(t.geometry&&c.push(t.geometry),t.material){const e=t.material;o.push(t.material);for(const t in e){const n=e[t];n&&n.isTexture&&l.push(n)}}})),i.materials=o,i.geometry=c,i.textures=l,this.onLoadModel&&this.onLoadModel(t,e)}))}disposeTile(t){const e=t.cached;if(e.scene){const n=e.materials,r=e.geometry,i=e.textures;for(let t=0,e=r.length;t<e;t++)r[t].dispose();for(let t=0,e=n.length;t<e;t++)n[t].dispose();for(let t=0,e=i.length;t<e;t++){i[t].dispose()}this.onDisposeModel&&this.onDisposeModel(e.scene,t),e.scene=null,e.materials=null,e.textures=null,e.geometry=null}t._loadIndex++}setTileVisible(t,e){const n=t.cached.scene,r=this.visibleTiles,i=this.group;e?(i.add(n),r.add(t),n.updateMatrixWorld(!0)):(i.remove(n),r.delete(t)),this.onTileVisibilityChange&&this.onTileVisibilityChange(n,t,e)}setTileActive(t,e){const n=this.activeTiles;e?n.add(t):n.delete(t)}calculateError(t){const e=t.cached,n=e.inFrustum,r=this.cameras,i=this.cameraInfo,s=t.boundingVolume;if("box"in s||"sphere"in s){const s=e.sphere,o=e.box,a=e.boxTransformInverse,c=e.transformInverse,l=o&&a;let h=-1/0,u=1/0;for(let e=0,d=r.length;e<d;e++){if(!n[e])continue;const r=i[e],d=r.invScale;let f;if(r.isOrthographic){const e=r.pixelSize;f=t.geometricError/(e*d)}else{let e;nt.copy(r.position),l?(nt.applyMatrix4(a),e=o.distanceToPoint(nt)):(nt.applyMatrix4(c),e=Math.max(s.distanceToPoint(nt),0));const n=e*d,i=r.sseDenominator;f=t.geometricError/(n*i),u=Math.min(u,n)}h=Math.max(h,f)}t.__distanceFromCamera=u,t.__error=h}else"region"in s&&console.warn("ThreeTilesRenderer : Region bounds not supported.")}tileInView(t){const e=t.cached,n=e.sphere,r=e.inFrustum;if(n){const t=this.cameraInfo;let e=!1;for(let i=0,s=t.length;i<s;i++){t[i].frustum.intersectsSphere(n)?(e=!0,r[i]=!0):r[i]=!1}return e}return!0}}},17099:(t,e,n)=>{"use strict";n.d(e,{Z:()=>r});class r{constructor(){this.maxJobs=6,this.items=[],this.callbacks=new Map,this.currJobs=0,this.scheduled=!1,this.autoUpdate=!0,this.priorityCallback=()=>{throw new Error("PriorityQueue: PriorityCallback function not defined.")},this.schedulingCallback=t=>{requestAnimationFrame(t)},this._runjobs=()=>{this.tryRunJobs(),this.scheduled=!1}}sort(){const t=this.priorityCallback;this.items.sort(t)}add(t,e){return new Promise(((n,r)=>{const i=this.items,s=this.callbacks;i.push(t),s.set(t,((...t)=>e(...t).then(n).catch(r))),this.autoUpdate&&this.scheduleJobRun()}))}remove(t){const e=this.items,n=this.callbacks,r=e.indexOf(t);-1!==r&&(e.splice(r,1),n.delete(t))}tryRunJobs(){this.sort();const t=this.items,e=this.callbacks,n=this.maxJobs;let r=this.currJobs;for(;n>r&&t.length>0;){r++;const n=t.pop(),i=e.get(n);e.delete(n),i(n).then((()=>{this.currJobs--,this.autoUpdate&&this.scheduleJobRun()})).catch((()=>{this.currJobs--,this.autoUpdate&&this.scheduleJobRun()}))}this.currJobs=r}scheduleJobRun(){this.scheduled||(this.schedulingCallback(this._runjobs),this.scheduled=!0)}}},27896:(t,e,n)=>{"use strict";n.d(e,{r:()=>lt});var r=n(81396);const i=1.25,s=65535,o=Math.pow(2,-24);class a{constructor(){}}function c(t,e,n){return n.min.x=e[t],n.min.y=e[t+1],n.min.z=e[t+2],n.max.x=e[t+3],n.max.y=e[t+4],n.max.z=e[t+5],n}function l(t){let e=-1,n=-1/0;for(let r=0;r<3;r++){const i=t[r+3]-t[r];i>n&&(n=i,e=r)}return e}function h(t,e){e.set(t)}function u(t,e,n){let r,i;for(let s=0;s<3;s++){const o=s+3;r=t[s],i=e[s],n[s]=r<i?r:i,r=t[o],i=e[o],n[o]=r>i?r:i}}function d(t,e,n){for(let r=0;r<3;r++){const i=e[t+2*r],s=e[t+2*r+1],o=i-s,a=i+s;o<n[r]&&(n[r]=o),a>n[r+3]&&(n[r+3]=a)}}function f(t){const e=t[3]-t[0],n=t[4]-t[1],r=t[5]-t[2];return 2*(e*n+n*r+r*e)}function p(t,e,n,r,i=null){let s=1/0,o=1/0,a=1/0,c=-1/0,l=-1/0,h=-1/0,u=1/0,d=1/0,f=1/0,p=-1/0,g=-1/0,m=-1/0;const x=null!==i;for(let r=6*e,i=6*(e+n);r<i;r+=6){const e=t[r+0],n=t[r+1],i=e-n,y=e+n;i<s&&(s=i),y>c&&(c=y),x&&e<u&&(u=e),x&&e>p&&(p=e);const w=t[r+2],b=t[r+3],_=w-b,v=w+b;_<o&&(o=_),v>l&&(l=v),x&&w<d&&(d=w),x&&w>g&&(g=w);const T=t[r+4],S=t[r+5],M=T-S,B=T+S;M<a&&(a=M),B>h&&(h=B),x&&T<f&&(f=T),x&&T>m&&(m=T)}r[0]=s,r[1]=o,r[2]=a,r[3]=c,r[4]=l,r[5]=h,x&&(i[0]=u,i[1]=d,i[2]=f,i[3]=p,i[4]=g,i[5]=m)}const g=32,m=(t,e)=>t.candidate-e.candidate,x=new Array(g).fill().map((()=>({count:0,bounds:new Float32Array(6),rightCacheBounds:new Float32Array(6),leftCacheBounds:new Float32Array(6),candidate:0}))),y=new Float32Array(6);function w(t,e){function n(t){B&&B(t/P)}function s(e,r,o,c=null,B=0){if(!A&&B>=v&&(A=!0,T&&(console.warn(`MeshBVH: Max depth of ${v} reached when generating BVH. Consider increasing maxDepth.`),console.warn(t))),o<=S||B>=v)return n(r+o),e.offset=r,e.count=o,e;const P=function(t,e,n,r,s,o){let a=-1,c=0;if(0===o)a=l(e),-1!==a&&(c=(e[a]+e[a+3])/2);else if(1===o)a=l(t),-1!==a&&(c=function(t,e,n,r){let i=0;for(let s=e,o=e+n;s<o;s++)i+=t[6*s+2*r];return i/n}(n,r,s,a));else if(2===o){const o=f(t);let l=i*s;const p=6*r,w=6*(r+s);for(let t=0;t<3;t++){const r=e[t],b=(e[t+3]-r)/g;if(s<8){const e=[...x];e.length=s;let r=0;for(let i=p;i<w;i+=6,r++){const s=e[r];s.candidate=n[i+2*t],s.count=0;const{bounds:o,leftCacheBounds:a,rightCacheBounds:c}=s;for(let t=0;t<3;t++)c[t]=1/0,c[t+3]=-1/0,a[t]=1/0,a[t+3]=-1/0,o[t]=1/0,o[t+3]=-1/0;d(i,n,o)}e.sort(m);let h=s;for(let t=0;t<h;t++){const n=e[t];for(;t+1<h&&e[t+1].candidate===n.candidate;)e.splice(t+1,1),h--}for(let r=p;r<w;r+=6){const i=n[r+2*t];for(let t=0;t<h;t++){const s=e[t];i>=s.candidate?d(r,n,s.rightCacheBounds):(d(r,n,s.leftCacheBounds),s.count++)}}for(let n=0;n<h;n++){const r=e[n],h=r.count,u=s-r.count,d=r.leftCacheBounds,p=r.rightCacheBounds;let g=0;0!==h&&(g=f(d)/o);let m=0;0!==u&&(m=f(p)/o);const x=1+i*(g*h+m*u);x<l&&(a=t,l=x,c=r.candidate)}}else{for(let t=0;t<g;t++){const e=x[t];e.count=0,e.candidate=r+b+t*b;const n=e.bounds;for(let t=0;t<3;t++)n[t]=1/0,n[t+3]=-1/0}for(let e=p;e<w;e+=6){let i=~~((n[e+2*t]-r)/b);i>=g&&(i=31);const s=x[i];s.count++,d(e,n,s.bounds)}const e=x[31];h(e.bounds,e.rightCacheBounds);for(let t=30;t>=0;t--){const e=x[t],n=x[t+1];u(e.bounds,n.rightCacheBounds,e.rightCacheBounds)}let m=0;for(let e=0;e<31;e++){const n=x[e],r=n.count,d=n.bounds,p=x[e+1].rightCacheBounds;0!==r&&(0===m?h(d,y):u(d,y,y)),m+=r;let g=0,w=0;0!==m&&(g=f(y)/o);const b=s-m;0!==b&&(w=f(p)/o);const _=1+i*(g*m+w*b);_<l&&(a=t,l=_,c=n.candidate)}}}}else console.warn(`MeshBVH: Invalid build strategy value ${o} used.`);return{axis:a,pos:c}}(e.boundingData,c,b,r,o,M);if(-1===P.axis)return n(r+o),e.offset=r,e.count=o,e;const F=function(t,e,n,r,i){let s=n,o=n+r-1;const a=i.pos,c=2*i.axis;for(;;){for(;s<=o&&e[6*s+c]<a;)s++;for(;s<=o&&e[6*o+c]>=a;)o--;if(!(s<o))return s;for(let n=0;n<3;n++){let r=t[3*s+n];t[3*s+n]=t[3*o+n],t[3*o+n]=r;let i=e[6*s+2*n+0];e[6*s+2*n+0]=e[6*o+2*n+0],e[6*o+2*n+0]=i;let a=e[6*s+2*n+1];e[6*s+2*n+1]=e[6*o+2*n+1],e[6*o+2*n+1]=a}s++,o--}}(_,b,r,o,P);if(F===r||F===r+o)n(r+o),e.offset=r,e.count=o;else{e.splitAxis=P.axis;const t=new a,n=r,i=F-r;e.left=t,t.boundingData=new Float32Array(6),p(b,n,i,t.boundingData,w),s(t,n,i,w,B+1);const c=new a,l=F,h=o-i;e.right=c,c.boundingData=new Float32Array(6),p(b,l,h,c.boundingData,w),s(c,l,h,w,B+1)}return e}!function(t,e){if(!t.index){const n=t.attributes.position.count,i=e.useSharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;let s;s=n>65535?new Uint32Array(new i(4*n)):new Uint16Array(new i(2*n)),t.setIndex(new r.BufferAttribute(s,1));for(let t=0;t<n;t++)s[t]=t}}(t,e);const c=new Float32Array(6),w=new Float32Array(6),b=function(t,e){const n=t.attributes.position,r=n.array,i=t.index.array,s=i.length/3,a=new Float32Array(6*s),c=n.offset||0;let l=3;n.isInterleavedBufferAttribute&&(l=n.data.stride);for(let t=0;t<s;t++){const n=3*t,s=6*t,h=i[n+0]*l+c,u=i[n+1]*l+c,d=i[n+2]*l+c;for(let t=0;t<3;t++){const n=r[h+t],i=r[u+t],c=r[d+t];let l=n;i<l&&(l=i),c<l&&(l=c);let f=n;i>f&&(f=i),c>f&&(f=c);const p=(f-l)/2,g=2*t;a[s+g+0]=l+p,a[s+g+1]=p+(Math.abs(l)+p)*o,l<e[t]&&(e[t]=l),f>e[t+3]&&(e[t+3]=f)}}return a}(t,c),_=t.index.array,v=e.maxDepth,T=e.verbose,S=e.maxLeafTris,M=e.strategy,B=e.onProgress,P=t.index.count/3;let A=!1;const F=[],C=function(t){if(!t.groups||!t.groups.length)return[{offset:0,count:t.index.count/3}];const e=[],n=new Set;for(const e of t.groups)n.add(e.start),n.add(e.start+e.count);const r=Array.from(n.values()).sort(((t,e)=>t-e));for(let t=0;t<r.length-1;t++){const n=r[t],i=r[t+1];e.push({offset:n/3,count:(i-n)/3})}return e}(t);if(1===C.length){const t=C[0],e=new a;e.boundingData=c,function(t,e,n,r){let i=1/0,s=1/0,o=1/0,a=-1/0,c=-1/0,l=-1/0;for(let r=6*e,h=6*(e+n);r<h;r+=6){const e=t[r+0];e<i&&(i=e),e>a&&(a=e);const n=t[r+2];n<s&&(s=n),n>c&&(c=n);const h=t[r+4];h<o&&(o=h),h>l&&(l=h)}r[0]=i,r[1]=s,r[2]=o,r[3]=a,r[4]=c,r[5]=l}(b,t.offset,t.count,w),s(e,t.offset,t.count,w),F.push(e)}else for(let t of C){const e=new a;e.boundingData=new Float32Array(6),p(b,t.offset,t.count,e.boundingData,w),s(e,t.offset,t.count,w),F.push(e)}return F}class b{constructor(){this.min=1/0,this.max=-1/0}setFromPointsField(t,e){let n=1/0,r=-1/0;for(let i=0,s=t.length;i<s;i++){const s=t[i][e];n=s<n?s:n,r=s>r?s:r}this.min=n,this.max=r}setFromPoints(t,e){let n=1/0,r=-1/0;for(let i=0,s=e.length;i<s;i++){const s=e[i],o=t.dot(s);n=o<n?o:n,r=o>r?o:r}this.min=n,this.max=r}isSeparated(t){return this.min>t.max||t.min>this.max}}b.prototype.setFromBox=function(){const t=new r.Vector3;return function(e,n){const r=n.min,i=n.max;let s=1/0,o=-1/0;for(let n=0;n<=1;n++)for(let a=0;a<=1;a++)for(let c=0;c<=1;c++){t.x=r.x*n+i.x*(1-n),t.y=r.y*a+i.y*(1-a),t.z=r.z*c+i.z*(1-c);const l=e.dot(t);s=Math.min(l,s),o=Math.max(l,o)}this.min=s,this.max=o}}();!function(){const t=new b}();const _=function(){const t=new r.Vector3,e=new r.Vector3,n=new r.Vector3;return function(r,i,s){const o=r.start,a=t,c=i.start,l=e;n.subVectors(o,c),t.subVectors(r.end,i.start),e.subVectors(i.end,i.start);const h=n.dot(l),u=l.dot(a),d=l.dot(l),f=n.dot(a),p=a.dot(a)*d-u*u;let g,m;g=0!==p?(h*u-f*d)/p:0,m=(h+g*u)/d,s.x=g,s.y=m}}(),v=function(){const t=new r.Vector2,e=new r.Vector3,n=new r.Vector3;return function(r,i,s,o){_(r,i,t);let a=t.x,c=t.y;if(a>=0&&a<=1&&c>=0&&c<=1)return r.at(a,s),void i.at(c,o);if(a>=0&&a<=1)return c<0?i.at(0,o):i.at(1,o),void r.closestPointToPoint(o,!0,s);if(c>=0&&c<=1)return a<0?r.at(0,s):r.at(1,s),void i.closestPointToPoint(s,!0,o);{let t,l;t=a<0?r.start:r.end,l=c<0?i.start:i.end;const h=e,u=n;return r.closestPointToPoint(l,!0,e),i.closestPointToPoint(t,!0,n),h.distanceToSquared(l)<=u.distanceToSquared(t)?(s.copy(h),void o.copy(l)):(s.copy(t),void o.copy(u))}}}(),T=function(){const t=new r.Vector3,e=new r.Vector3,n=new r.Plane,i=new r.Line3;return function(r,s){const{radius:o,center:a}=r,{a:c,b:l,c:h}=s;i.start=c,i.end=l;if(i.closestPointToPoint(a,!0,t).distanceTo(a)<=o)return!0;i.start=c,i.end=h;if(i.closestPointToPoint(a,!0,t).distanceTo(a)<=o)return!0;i.start=l,i.end=h;if(i.closestPointToPoint(a,!0,t).distanceTo(a)<=o)return!0;const u=s.getPlane(n);if(Math.abs(u.distanceToPoint(a))<=o){const t=u.projectPoint(a,e);if(s.containsPoint(t))return!0}return!1}}();class S extends r.Triangle{constructor(...t){super(...t),this.isExtendedTriangle=!0,this.satAxes=new Array(4).fill().map((()=>new r.Vector3)),this.satBounds=new Array(4).fill().map((()=>new b)),this.points=[this.a,this.b,this.c],this.sphere=new r.Sphere,this.plane=new r.Plane,this.needsUpdate=!1}intersectsSphere(t){return T(t,this)}update(){const t=this.a,e=this.b,n=this.c,r=this.points,i=this.satAxes,s=this.satBounds,o=i[0],a=s[0];this.getNormal(o),a.setFromPoints(o,r);const c=i[1],l=s[1];c.subVectors(t,e),l.setFromPoints(c,r);const h=i[2],u=s[2];h.subVectors(e,n),u.setFromPoints(h,r);const d=i[3],f=s[3];d.subVectors(n,t),f.setFromPoints(d,r),this.sphere.setFromPoints(this.points),this.plane.setFromNormalAndCoplanarPoint(o,t),this.needsUpdate=!1}}S.prototype.closestPointToSegment=function(){const t=new r.Vector3,e=new r.Vector3,n=new r.Line3;return function(r,i=null,s=null){const{start:o,end:a}=r,c=this.points;let l,h=1/0;for(let o=0;o<3;o++){const a=(o+1)%3;n.start.copy(c[o]),n.end.copy(c[a]),v(n,r,t,e),l=t.distanceToSquared(e),l<h&&(h=l,i&&i.copy(t),s&&s.copy(e))}return this.closestPointToPoint(o,t),l=o.distanceToSquared(t),l<h&&(h=l,i&&i.copy(t),s&&s.copy(o)),this.closestPointToPoint(a,t),l=a.distanceToSquared(t),l<h&&(h=l,i&&i.copy(t),s&&s.copy(a)),Math.sqrt(h)}}(),S.prototype.intersectsTriangle=function(){const t=new S,e=new Array(3),n=new Array(3),i=new b,s=new b,o=new r.Vector3,a=new r.Vector3,c=new r.Vector3,l=new r.Vector3,h=new r.Line3,u=new r.Line3,d=new r.Line3;return function(r,f=null){this.needsUpdate&&this.update(),r.isExtendedTriangle?r.needsUpdate&&r.update():(t.copy(r),t.update(),r=t);const p=this.plane,g=r.plane;if(Math.abs(p.normal.dot(g.normal))>1-1e-10){const t=this.satBounds,a=this.satAxes;n[0]=r.a,n[1]=r.b,n[2]=r.c;for(let e=0;e<4;e++){const r=t[e],s=a[e];if(i.setFromPoints(s,n),r.isSeparated(i))return!1}const c=r.satBounds,l=r.satAxes;e[0]=this.a,e[1]=this.b,e[2]=this.c;for(let t=0;t<4;t++){const n=c[t],r=l[t];if(i.setFromPoints(r,e),n.isSeparated(i))return!1}for(let t=0;t<4;t++){const r=a[t];for(let t=0;t<4;t++){const a=l[t];if(o.crossVectors(r,a),i.setFromPoints(o,e),s.setFromPoints(o,n),i.isSeparated(s))return!1}}return f&&(console.warn("ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0."),f.start.set(0,0,0),f.end.set(0,0,0)),!0}{const t=this.points;let e=!1,n=0;for(let r=0;r<3;r++){const i=t[r],s=t[(r+1)%3];if(h.start.copy(i),h.end.copy(s),h.delta(a),0===g.normal.dot(a)&&0===g.distanceToPoint(h.start)){u.copy(h),n=2;break}if(g.intersectLine(h,e?u.start:u.end)){if(n++,e)break;e=!0}}if(2!==n)return!1;const i=r.points;let s=!1,o=0;for(let t=0;t<3;t++){const e=i[t],n=i[(t+1)%3];if(h.start.copy(e),h.end.copy(n),h.delta(c),0===p.normal.dot(c)&&0===p.distanceToPoint(h.start)){d.copy(h),o=2;break}if(p.intersectLine(h,s?d.start:d.end)){if(o++,s)break;s=!0}}if(2!==o)return!1;if(u.delta(a),d.delta(c),a.dot(c)<0){let t=d.start;d.start=d.end,d.end=t}const m=u.start.dot(a),x=u.end.dot(a),y=d.start.dot(a),w=d.end.dot(a),b=x<y,_=m<w;return(m===w||y===x||b!==_)&&(f&&(l.subVectors(u.start,d.start),l.dot(a)>0?f.start.copy(u.start):f.start.copy(d.start),l.subVectors(u.end,d.end),l.dot(a)<0?f.end.copy(u.end):f.end.copy(d.end)),!0)}}}(),S.prototype.distanceToPoint=function(){const t=new r.Vector3;return function(e){return this.closestPointToPoint(e,t),e.distanceTo(t)}}(),S.prototype.distanceToTriangle=function(){const t=new r.Vector3,e=new r.Vector3,n=["a","b","c"],i=new r.Line3,s=new r.Line3;return function(r,o=null,a=null){const c=o||a?i:null;if(this.intersectsTriangle(r,c))return(o||a)&&(o&&c.getCenter(o),a&&c.getCenter(a)),0;let l=1/0;for(let e=0;e<3;e++){let i;const s=n[e],c=r[s];this.closestPointToPoint(c,t),i=c.distanceToSquared(t),i<l&&(l=i,o&&o.copy(t),a&&a.copy(c));const h=this[s];r.closestPointToPoint(h,t),i=h.distanceToSquared(t),i<l&&(l=i,o&&o.copy(h),a&&a.copy(t))}for(let c=0;c<3;c++){const h=n[c],u=n[(c+1)%3];i.set(this[h],this[u]);for(let c=0;c<3;c++){const h=n[c],u=n[(c+1)%3];s.set(r[h],r[u]),v(i,s,t,e);const d=t.distanceToSquared(e);d<l&&(l=d,o&&o.copy(t),a&&a.copy(e))}}return Math.sqrt(l)}}();class M extends r.Box3{constructor(...t){super(...t),this.isOrientedBox=!0,this.matrix=new r.Matrix4,this.invMatrix=new r.Matrix4,this.points=new Array(8).fill().map((()=>new r.Vector3)),this.satAxes=new Array(3).fill().map((()=>new r.Vector3)),this.satBounds=new Array(3).fill().map((()=>new b)),this.alignedSatBounds=new Array(3).fill().map((()=>new b)),this.needsUpdate=!1}set(t,e,n){super.set(t,e),this.matrix.copy(n),this.needsUpdate=!0}copy(t){super.copy(t),this.matrix.copy(t.matrix),this.needsUpdate=!0}}M.prototype.update=function(){const t=this.matrix,e=this.min,n=this.max,r=this.points;for(let i=0;i<=1;i++)for(let s=0;s<=1;s++)for(let o=0;o<=1;o++){const a=r[1*i|2*s|4*o];a.x=i?n.x:e.x,a.y=s?n.y:e.y,a.z=o?n.z:e.z,a.applyMatrix4(t)}const i=this.satBounds,s=this.satAxes,o=r[0];for(let t=0;t<3;t++){const e=s[t],n=i[t],a=r[1<<t];e.subVectors(o,a),n.setFromPoints(e,r)}const a=this.alignedSatBounds;a[0].setFromPointsField(r,"x"),a[1].setFromPointsField(r,"y"),a[2].setFromPointsField(r,"z"),this.invMatrix.copy(this.matrix).invert(),this.needsUpdate=!1},M.prototype.intersectsBox=function(){const t=new b;return function(e){this.needsUpdate&&this.update();const n=e.min,r=e.max,i=this.satBounds,s=this.satAxes,o=this.alignedSatBounds;if(t.min=n.x,t.max=r.x,o[0].isSeparated(t))return!1;if(t.min=n.y,t.max=r.y,o[1].isSeparated(t))return!1;if(t.min=n.z,t.max=r.z,o[2].isSeparated(t))return!1;for(let n=0;n<3;n++){const r=s[n],o=i[n];if(t.setFromBox(r,e),o.isSeparated(t))return!1}return!0}}(),M.prototype.intersectsTriangle=function(){const t=new S,e=new Array(3),n=new b,i=new b,s=new r.Vector3;return function(r){this.needsUpdate&&this.update(),r.isExtendedTriangle?r.needsUpdate&&r.update():(t.copy(r),t.update(),r=t);const o=this.satBounds,a=this.satAxes;e[0]=r.a,e[1]=r.b,e[2]=r.c;for(let t=0;t<3;t++){const r=o[t],i=a[t];if(n.setFromPoints(i,e),r.isSeparated(n))return!1}const c=r.satBounds,l=r.satAxes,h=this.points;for(let t=0;t<3;t++){const e=c[t],r=l[t];if(n.setFromPoints(r,h),e.isSeparated(n))return!1}for(let t=0;t<3;t++){const r=a[t];for(let t=0;t<4;t++){const o=l[t];if(s.crossVectors(r,o),n.setFromPoints(s,e),i.setFromPoints(s,h),n.isSeparated(i))return!1}}return!0}}(),M.prototype.closestPointToPoint=function(t,e){return this.needsUpdate&&this.update(),e.copy(t).applyMatrix4(this.invMatrix).clamp(this.min,this.max).applyMatrix4(this.matrix),e},M.prototype.distanceToPoint=function(){const t=new r.Vector3;return function(e){return this.closestPointToPoint(e,t),e.distanceTo(t)}}(),M.prototype.distanceToBox=function(){const t=["x","y","z"],e=new Array(12).fill().map((()=>new r.Line3)),n=new Array(12).fill().map((()=>new r.Line3)),i=new r.Vector3,s=new r.Vector3;return function(r,o=0,a=null,c=null){if(this.needsUpdate&&this.update(),this.intersectsBox(r))return(a||c)&&(r.getCenter(s),this.closestPointToPoint(s,i),r.closestPointToPoint(i,s),a&&a.copy(i),c&&c.copy(s)),0;const l=o*o,h=r.min,u=r.max,d=this.points;let f=1/0;for(let t=0;t<8;t++){const e=d[t];s.copy(e).clamp(h,u);const n=e.distanceToSquared(s);if(n<f&&(f=n,a&&a.copy(e),c&&c.copy(s),n<l))return Math.sqrt(n)}let p=0;for(let r=0;r<3;r++)for(let i=0;i<=1;i++)for(let s=0;s<=1;s++){const o=(r+1)%3,a=(r+2)%3,c=1<<r|i<<o|s<<a,l=d[i<<o|s<<a],f=d[c];e[p].set(l,f);const g=t[r],m=t[o],x=t[a],y=n[p],w=y.start,b=y.end;w[g]=h[g],w[m]=i?h[m]:u[m],w[x]=s?h[x]:u[m],b[g]=u[g],b[m]=i?h[m]:u[m],b[x]=s?h[x]:u[m],p++}for(let t=0;t<=1;t++)for(let e=0;e<=1;e++)for(let n=0;n<=1;n++){s.x=t?u.x:h.x,s.y=e?u.y:h.y,s.z=n?u.z:h.z,this.closestPointToPoint(s,i);const r=s.distanceToSquared(i);if(r<f&&(f=r,a&&a.copy(i),c&&c.copy(s),r<l))return Math.sqrt(r)}for(let t=0;t<12;t++){const r=e[t];for(let t=0;t<12;t++){const e=n[t];v(r,e,i,s);const o=i.distanceToSquared(s);if(o<f&&(f=o,a&&a.copy(i),c&&c.copy(s),o<l))return Math.sqrt(o)}}return Math.sqrt(f)}}();var B=n(78129);function P(t,e,n,r){const i=t.a,s=t.b,o=t.c;let a=e,c=e+1,l=e+2;n&&(a=n.getX(e),c=n.getX(e+1),l=n.getX(e+2)),i.x=r.getX(a),i.y=r.getY(a),i.z=r.getZ(a),s.x=r.getX(c),s.y=r.getY(c),s.z=r.getZ(c),o.x=r.getX(l),o.y=r.getY(l),o.z=r.getZ(l)}function A(t,e,n,r,i,s,o){const a=n.index,c=n.attributes.position;for(let n=t,l=e+t;n<l;n++)if(P(o,3*n,a,c),o.needsUpdate=!0,r(o,n,i,s))return!0;return!1}class F{constructor(t){this._getNewPrimitive=t,this._primitives=[]}getPrimitive(){const t=this._primitives;return 0===t.length?this._getNewPrimitive():t.pop()}releasePrimitive(t){this._primitives.push(t)}}function C(t,e){return 65535===e[t+15]}function V(t,e){return e[t+6]}function U(t,e){return e[t+14]}function k(t){return t+8}function L(t,e){return e[t+6]}const E=new r.Box3,R=new r.Vector3,O=["x","y","z"];function I(t,e,n,r,i){let s=2*t,o=q,a=G,c=j;if(C(s,a)){const o=V(t,c),l=U(s,a);(0,B.U$)(e,n,r,o,l,i)}else{const s=k(t);X(s,o,r,R)&&I(s,e,n,r,i);const a=L(t,c);X(a,o,r,R)&&I(a,e,n,r,i)}}function D(t,e,n,r){let i=2*t,s=q,o=G,a=j;if(C(i,o)){const s=V(t,a),c=U(i,o);return(0,B.rM)(e,n,r,s,c)}{const i=function(t,e){return e[t+7]}(t,a),o=O[i],c=r.direction[o]>=0;let l,h;c?(l=k(t),h=L(t,a)):(l=L(t,a),h=k(t));const u=X(l,s,r,R)?D(l,e,n,r):null;if(u){const t=u.point[o];if(c?t<=s[h+i]:t>=s[h+i+3])return u}const d=X(h,s,r,R)?D(h,e,n,r):null;return u&&d?u.distance<=d.distance?u:d:u||d||null}}const N=function(){let t,e;const n=[],i=new F((()=>new r.Box3));return function(...r){t=i.getPrimitive(),e=i.getPrimitive(),n.push(t,e);const o=s(...r);i.releasePrimitive(t),i.releasePrimitive(e),n.pop(),n.pop();const a=n.length;return a>0&&(e=n[a-1],t=n[a-2]),o};function s(n,r,i,o,a=null,l=0,h=0){function u(t){let e=2*t,n=G,r=j;for(;!C(e,n);)e=2*(t=k(t));return V(t,r)}function d(t){let e=2*t,n=G,r=j;for(;!C(e,n);)e=2*(t=L(t,r));return V(t,r)+U(e,n)}let f=2*n,p=q,g=G,m=j;if(C(f,g)){const e=V(n,m),r=U(f,g);return c(n,p,t),o(e,r,!1,h,l+n,t)}{const f=k(n),x=L(n,m);let y,w,b,_,v=f,T=x;if(a&&(b=t,_=e,c(v,p,b),c(T,p,_),y=a(b),w=a(_),w<y)){v=x,T=f;const t=y;y=w,w=t,b=_}b||(b=t,c(v,p,b));const S=i(b,C(2*v,g),y,h+1,l+v);let M;if(2===S){const t=u(v);M=o(t,d(v)-t,!0,h+1,l+v,b)}else M=S&&s(v,r,i,o,a,l,h+1);if(M)return!0;_=e,c(T,p,_);const B=i(_,C(2*T,g),w,h+1,l+T);let P;if(2===B){const t=u(T);P=o(t,d(T)-t,!0,h+1,l+T,_)}else P=B&&s(T,r,i,o,a,l,h+1);return!!P}}}(),z=function(){const t=new S,e=new S,n=new r.Matrix4,i=new M,s=new M;return function r(o,a,l,h,u=null){let d=2*o,f=q,p=G,g=j;null===u&&(l.boundingBox||l.computeBoundingBox(),i.set(l.boundingBox.min,l.boundingBox.max,h),u=i);if(!C(d,p)){const t=o+8,e=g[o+6];c(t,f,E);if(u.intersectsBox(E)&&r(t,a,l,h,u))return!0;c(e,f,E);return!!(u.intersectsBox(E)&&r(e,a,l,h,u))}{const r=a,i=r.index,u=r.attributes.position,m=l.index,x=l.attributes.position,y=V(o,g),w=U(d,p);if(n.copy(h).invert(),l.boundsTree){c(o,f,s),s.matrix.copy(n),s.needsUpdate=!0;return l.boundsTree.shapecast({intersectsBounds:t=>s.intersectsBox(t),intersectsTriangle:t=>{t.a.applyMatrix4(h),t.b.applyMatrix4(h),t.c.applyMatrix4(h),t.needsUpdate=!0;for(let n=3*y,r=3*(w+y);n<r;n+=3)if(P(e,n,i,u),e.needsUpdate=!0,t.intersectsTriangle(e))return!0;return!1}})}for(let r=3*y,s=w+3*y;r<s;r+=3){P(t,r,i,u),t.a.applyMatrix4(n),t.b.applyMatrix4(n),t.c.applyMatrix4(n),t.needsUpdate=!0;for(let n=0,r=m.count;n<r;n+=3)if(P(e,n,m,x),e.needsUpdate=!0,t.intersectsTriangle(e))return!0}}}}();function X(t,e,n,r){return c(t,e,E),n.intersectBox(E,r)}const Y=[];let H,q,G,j;function W(t){H&&Y.push(H),H=t,q=new Float32Array(t),G=new Uint16Array(t),j=new Uint32Array(t)}function $(){H=null,q=null,G=null,j=null,Y.length&&W(Y.pop())}const J=Symbol("skip tree generation"),Q=new r.Box3,Z=new r.Box3,K=new r.Matrix4,tt=new M,et=new M,nt=new r.Vector3,rt=new r.Vector3,it=new r.Vector3,st=new r.Vector3,ot=new r.Vector3,at=new r.Box3,ct=new F((()=>new S));class lt{static serialize(t,e={}){if(e.isBufferGeometry)return console.warn("MeshBVH.serialize: The arguments for the function have changed. See documentation for new signature."),lt.serialize(arguments[0],{cloneBuffers:void 0===arguments[2]||arguments[2]});e={cloneBuffers:!0,...e};const n=t.geometry,r=t._roots,i=n.getIndex();let s;return s=e.cloneBuffers?{roots:r.map((t=>t.slice())),index:i.array.slice()}:{roots:r,index:i.array},s}static deserialize(t,e,n={}){if("boolean"==typeof n)return console.warn("MeshBVH.deserialize: The arguments for the function have changed. See documentation for new signature."),lt.deserialize(arguments[0],arguments[1],{setIndex:void 0===arguments[2]||arguments[2]});n={setIndex:!0,...n};const{index:i,roots:s}=t,o=new lt(e,{...n,[J]:!0});if(o._roots=s,n.setIndex){const n=e.getIndex();if(null===n){const n=new r.BufferAttribute(t.index,1,!1);e.setIndex(n)}else n.array!==i&&(n.array.set(i),n.needsUpdate=!0)}return o}constructor(t,e={}){if(!t.isBufferGeometry)throw new Error("MeshBVH: Only BufferGeometries are supported.");if(t.index&&t.index.isInterleavedBufferAttribute)throw new Error("MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.");if((e=Object.assign({strategy:0,maxDepth:40,maxLeafTris:10,verbose:!0,useSharedArrayBuffer:!1,setBoundingBox:!0,onProgress:null,[J]:!1},e)).useSharedArrayBuffer&&"undefined"==typeof SharedArrayBuffer)throw new Error("MeshBVH: SharedArrayBuffer is not available.");this._roots=null,e[J]||(this._roots=function(t,e){const n=w(t,e);let r,i,o;const a=[],c=e.useSharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;for(let t=0;t<n.length;t++){const e=n[t],s=new c(32*l(e));r=new Float32Array(s),i=new Uint32Array(s),o=new Uint16Array(s),h(0,e),a.push(s)}return a;function l(t){return t.count?1:1+l(t.left)+l(t.right)}function h(t,e){const n=t/4,a=t/2,c=!!e.count,l=e.boundingData;for(let t=0;t<6;t++)r[n+t]=l[t];if(c){const r=e.offset,c=e.count;return i[n+6]=r,o[a+14]=c,o[a+15]=s,t+32}{const r=e.left,s=e.right,o=e.splitAxis;let a;if(a=h(t+32,r),a/4>Math.pow(2,32))throw new Error("MeshBVH: Cannot store child pointer greater than 32 bits.");return i[n+6]=a/4,a=h(a,s),i[n+7]=o,a}}}(t,e),!t.boundingBox&&e.setBoundingBox&&(t.boundingBox=this.getBoundingBox(new r.Box3))),this.geometry=t}refit(t=null){t&&Array.isArray(t)&&(t=new Set(t));const e=this.geometry,n=e.index.array,r=e.attributes.position,i=r.array,o=r.offset||0;let a,c,l,h,u=3;r.isInterleavedBufferAttribute&&(u=r.data.stride);let d=0;const f=this._roots;for(let t=0,e=f.length;t<e;t++)a=f[t],c=new Uint32Array(a),l=new Uint16Array(a),h=new Float32Array(a),p(0,d),d+=a.byteLength;function p(e,r,a=!1){const d=2*e;if(l[d+15]===s){const t=c[e+6];let r=1/0,s=1/0,a=1/0,f=-1/0,p=-1/0,g=-1/0;for(let e=3*t,c=3*(t+l[d+14]);e<c;e++){const t=n[e]*u+o,c=i[t+0],l=i[t+1],h=i[t+2];c<r&&(r=c),c>f&&(f=c),l<s&&(s=l),l>p&&(p=l),h<a&&(a=h),h>g&&(g=h)}return(h[e+0]!==r||h[e+1]!==s||h[e+2]!==a||h[e+3]!==f||h[e+4]!==p||h[e+5]!==g)&&(h[e+0]=r,h[e+1]=s,h[e+2]=a,h[e+3]=f,h[e+4]=p,h[e+5]=g,!0)}{const n=e+8,i=c[e+6],s=n+r,o=i+r;let l=a,u=!1,d=!1;t?l||(u=t.has(s),d=t.has(o),l=!u&&!d):(u=!0,d=!0);const f=l||d;let g=!1;(l||u)&&(g=p(n,r,l));let m=!1;f&&(m=p(i,r,l));const x=g||m;if(x)for(let t=0;t<3;t++){const r=n+t,s=i+t,o=h[r],a=h[r+3],c=h[s],l=h[s+3];h[e+t]=o<c?o:c,h[e+t+3]=a>l?a:l}return x}}}traverse(t,e=0){const n=this._roots[e],r=new Uint32Array(n),i=new Uint16Array(n);!function e(o,a=0){const c=2*o,l=i[c+15]===s;if(l){const e=r[o+6],s=i[c+14];t(a,l,new Float32Array(n,4*o,6),e,s)}else{const i=o+8,s=r[o+6],c=r[o+7];t(a,l,new Float32Array(n,4*o,6),c)||(e(i,a+1),e(s,a+1))}}(0)}raycast(t,e=r.FrontSide){const n=this._roots,i=this.geometry,s=[],o=e.isMaterial,a=Array.isArray(e),c=i.groups,l=o?e.side:e;for(let r=0,o=n.length;r<o;r++){const o=a?e[c[r].materialIndex].side:l,h=s.length;if(W(n[r]),I(0,i,o,t,s),$(),a){const t=c[r].materialIndex;for(let e=h,n=s.length;e<n;e++)s[e].face.materialIndex=t}}return s}raycastFirst(t,e=r.FrontSide){const n=this._roots,i=this.geometry,s=e.isMaterial,o=Array.isArray(e);let a=null;const c=i.groups,l=s?e.side:e;for(let r=0,s=n.length;r<s;r++){const s=o?e[c[r].materialIndex].side:l;W(n[r]);const h=D(0,i,s,t);$(),null!=h&&(null==a||h.distance<a.distance)&&(a=h,o&&(h.face.materialIndex=c[r].materialIndex))}return a}intersectsGeometry(t,e){const n=this.geometry;let r=!1;for(const i of this._roots)if(W(i),r=z(0,n,t,e),$(),r)break;return r}shapecast(t,e,n){const r=this.geometry;if(t instanceof Function){if(e){const t=e;e=(e,n,r,i)=>{const s=3*n;return t(e,s,s+1,s+2,r,i)}}t={boundsTraverseOrder:n,intersectsBounds:t,intersectsTriangle:e,intersectsRange:null},console.warn("MeshBVH: Shapecast function signature has changed and now takes an object of callbacks as a second argument. See docs for new signature.")}const i=ct.getPrimitive();let{boundsTraverseOrder:s,intersectsBounds:o,intersectsRange:a,intersectsTriangle:c}=t;if(a&&c){const t=a;a=(e,n,s,o,a)=>!!t(e,n,s,o,a)||A(e,n,r,c,s,o,i)}else a||(a=c?(t,e,n,s)=>A(t,e,r,c,n,s,i):(t,e,n)=>n);let l=!1,h=0;for(const t of this._roots){if(W(t),l=N(0,r,o,a,s,h),$(),l)break;h+=t.byteLength}return ct.releasePrimitive(i),l}bvhcast(t,e,n){let{intersectsRanges:r,intersectsTriangles:i}=n;const s=this.geometry.index,o=this.geometry.attributes.position,a=t.geometry.index,c=t.geometry.attributes.position;K.copy(e).invert();const l=ct.getPrimitive(),h=ct.getPrimitive();if(i){function u(t,n,r,u,d,f,p,g){for(let m=r,x=r+u;m<x;m++){P(h,3*m,a,c),h.a.applyMatrix4(e),h.b.applyMatrix4(e),h.c.applyMatrix4(e),h.needsUpdate=!0;for(let e=t,r=t+n;e<r;e++)if(P(l,3*e,s,o),l.needsUpdate=!0,i(l,h,e,m,d,f,p,g))return!0}return!1}if(r){const t=r;r=function(e,n,r,i,s,o,a,c){return!!t(e,n,r,i,s,o,a,c)||u(e,n,r,i,s,o,a,c)}}else r=u}this.getBoundingBox(Z),Z.applyMatrix4(e);const d=this.shapecast({intersectsBounds:t=>Z.intersectsBox(t),intersectsRange:(e,n,i,s,o,a)=>(Q.copy(a),Q.applyMatrix4(K),t.shapecast({intersectsBounds:t=>Q.intersectsBox(t),intersectsRange:(t,i,a,c,l)=>r(e,n,t,i,s,o,c,l)}))});return ct.releasePrimitive(l),ct.releasePrimitive(h),d}intersectsBox(t,e){return tt.set(t.min,t.max,e),tt.needsUpdate=!0,this.shapecast({intersectsBounds:t=>tt.intersectsBox(t),intersectsTriangle:t=>tt.intersectsTriangle(t)})}intersectsSphere(t){return this.shapecast({intersectsBounds:e=>t.intersectsBox(e),intersectsTriangle:e=>e.intersectsSphere(t)})}closestPointToGeometry(t,e,n={},r={},i=0,s=1/0){t.boundingBox||t.computeBoundingBox(),tt.set(t.boundingBox.min,t.boundingBox.max,e),tt.needsUpdate=!0;const o=this.geometry,a=o.attributes.position,c=o.index,l=t.attributes.position,h=t.index,u=ct.getPrimitive(),d=ct.getPrimitive();let f=rt,p=it,g=null,m=null;r&&(g=st,m=ot);let x=1/0,y=null,w=null;return K.copy(e).invert(),et.matrix.copy(K),this.shapecast({boundsTraverseOrder:t=>tt.distanceToBox(t),intersectsBounds:(t,e,n)=>n<x&&n<s&&(e&&(et.min.copy(t.min),et.max.copy(t.max),et.needsUpdate=!0),!0),intersectsRange:(n,r)=>{if(t.boundsTree)return t.boundsTree.shapecast({boundsTraverseOrder:t=>et.distanceToBox(t),intersectsBounds:(t,e,n)=>n<x&&n<s,intersectsRange:(t,s)=>{for(let o=3*t,b=3*(t+s);o<b;o+=3){P(d,o,h,l),d.a.applyMatrix4(e),d.b.applyMatrix4(e),d.c.applyMatrix4(e),d.needsUpdate=!0;for(let t=3*n,e=3*(n+r);t<e;t+=3){P(u,t,c,a),u.needsUpdate=!0;const e=u.distanceToTriangle(d,f,g);if(e<x&&(p.copy(f),m&&m.copy(g),x=e,y=t/3,w=o/3),e<i)return!0}}}});for(let t=0,s=h?h.count:l.count;t<s;t+=3){P(d,t,h,l),d.a.applyMatrix4(e),d.b.applyMatrix4(e),d.c.applyMatrix4(e),d.needsUpdate=!0;for(let e=3*n,s=3*(n+r);e<s;e+=3){P(u,e,c,a),u.needsUpdate=!0;const n=u.distanceToTriangle(d,f,g);if(n<x&&(p.copy(f),m&&m.copy(g),x=n,y=e/3,w=t/3),n<i)return!0}}}}),ct.releasePrimitive(u),ct.releasePrimitive(d),x===1/0?null:(n.point?n.point.copy(p):n.point=p.clone(),n.distance=x,n.faceIndex=y,r&&(r.point?r.point.copy(m):r.point=m.clone(),r.point.applyMatrix4(K),p.applyMatrix4(K),r.distance=p.sub(r.point).length(),r.faceIndex=w),n)}closestPointToPoint(t,e={},n=0,r=1/0){const i=n*n,s=r*r;let o=1/0,a=null;if(this.shapecast({boundsTraverseOrder:e=>(nt.copy(t).clamp(e.min,e.max),nt.distanceToSquared(t)),intersectsBounds:(t,e,n)=>n<o&&n<s,intersectsTriangle:(e,n)=>{e.closestPointToPoint(t,nt);const r=t.distanceToSquared(nt);return r<o&&(rt.copy(nt),o=r,a=n),r<i}}),o===1/0)return null;const c=Math.sqrt(o);return e.point?e.point.copy(rt):e.point=rt.clone(),e.distance=c,e.faceIndex=a,e}getBoundingBox(t){t.makeEmpty();return this._roots.forEach((e=>{c(0,new Float32Array(e),at),t.union(at)})),t}}const ht=lt.prototype.raycast;lt.prototype.raycast=function(...t){if(t[0].isMesh){console.warn('MeshBVH: The function signature and results frame for "raycast" has changed. See docs for new signature.');const[e,n,r,i]=t;return ht.call(this,r,e.material).forEach((t=>{(t=(0,B.O)(t,e,n))&&i.push(t)})),i}return ht.apply(this,t)};const ut=lt.prototype.raycastFirst;lt.prototype.raycastFirst=function(...t){if(t[0].isMesh){console.warn('MeshBVH: The function signature and results frame for "raycastFirst" has changed. See docs for new signature.');const[e,n,r]=t;return(0,B.O)(ut.call(this,r,e.material),e,n)}return ut.apply(this,t)};const dt=lt.prototype.closestPointToPoint;lt.prototype.closestPointToPoint=function(...t){if(t[0].isMesh){console.warn('MeshBVH: The function signature and results frame for "closestPointToPoint" has changed. See docs for new signature.'),t.unshift();const e=t[1],n={};return t[1]=n,dt.apply(this,t),e&&e.copy(n.point),n.distance}return dt.apply(this,t)};const ft=lt.prototype.closestPointToGeometry;lt.prototype.closestPointToGeometry=function(...t){const e=t[2],n=t[3];if(e&&e.isVector3||n&&n.isVector3){console.warn('MeshBVH: The function signature and results frame for "closestPointToGeometry" has changed. See docs for new signature.');const r={},i={},s=t[1];return t[2]=r,t[3]=i,ft.apply(this,t),e&&e.copy(r.point),n&&n.copy(i.point).applyMatrix4(s),r.distance}return ft.apply(this,t)};const pt=lt.prototype.refit;lt.prototype.refit=function(...t){const e=t[0],n=t[1];if(n&&(n instanceof Set||Array.isArray(n))){console.warn('MeshBVH: The function signature for "refit" has changed. See docs for new signature.');const t=new Set;n.forEach((e=>t.add(e))),e&&e.forEach((e=>t.add(e))),pt.call(this,t)}else pt.apply(this,t)},["intersectsGeometry","shapecast","intersectsBox","intersectsSphere"].forEach((t=>{const e=lt.prototype[t];lt.prototype[t]=function(...n){return(null===n[0]||n[0].isMesh)&&(n.shift(),console.warn(`MeshBVH: The function signature for "${t}" has changed and no longer takes Mesh. See docs for new signature.`)),e.apply(this,n)}}))},33320:(t,e,n)=>{"use strict";n.d(e,{Xy:()=>h,sn:()=>u,uL:()=>l});var r=n(81396),i=n(78129),s=n(27896);const o=new r.Ray,a=new r.Matrix4,c=r.Mesh.prototype.raycast;function l(t,e){if(this.geometry.boundsTree){if(void 0===this.material)return;a.copy(this.matrixWorld).invert(),o.copy(t.ray).applyMatrix4(a);const n=this.geometry.boundsTree;if(!0===t.firstHitOnly){const r=(0,i.O)(n.raycastFirst(o,this.material),this,t);r&&e.push(r)}else{const r=n.raycast(o,this.material);for(let n=0,s=r.length;n<s;n++){const s=(0,i.O)(r[n],this,t);s&&e.push(s)}}}else c.call(this,t,e)}function h(t){return this.boundsTree=new s.r(this,t),this.boundsTree}function u(){this.boundsTree=null}},78129:(t,e,n)=>{"use strict";n.d(e,{O:()=>g,rM:()=>p,U$:()=>f});var r=n(81396);const i=new r.Vector3,s=new r.Vector3,o=new r.Vector3,a=new r.Vector2,c=new r.Vector2,l=new r.Vector2,h=new r.Vector3;function u(t,e,n,u,d,f,p){i.fromBufferAttribute(e,u),s.fromBufferAttribute(e,d),o.fromBufferAttribute(e,f);const g=function(t,e,n,i,s,o){let a;return a=o===r.BackSide?t.intersectTriangle(i,n,e,!0,s):t.intersectTriangle(e,n,i,o!==r.DoubleSide,s),null===a?null:{distance:t.origin.distanceTo(s),point:s.clone()}}(t,i,s,o,h,p);if(g){n&&(a.fromBufferAttribute(n,u),c.fromBufferAttribute(n,d),l.fromBufferAttribute(n,f),g.uv=r.Triangle.getUV(h,i,s,o,a,c,l,new r.Vector2));const t={a:u,b:d,c:f,normal:new r.Vector3,materialIndex:0};r.Triangle.getNormal(i,s,o,t.normal),g.face=t,g.faceIndex=u}return g}function d(t,e,n,r,i){const s=3*r,o=t.index.getX(s),a=t.index.getX(s+1),c=t.index.getX(s+2),l=u(n,t.attributes.position,t.attributes.uv,o,a,c,e);return l?(l.faceIndex=r,i&&i.push(l),l):null}function f(t,e,n,r,i,s){for(let o=r,a=r+i;o<a;o++)d(t,e,n,o,s)}function p(t,e,n,r,i){let s=1/0,o=null;for(let a=r,c=r+i;a<c;a++){const r=d(t,e,n,a);r&&r.distance<s&&(o=r,s=r.distance)}return o}function g(t,e,n){return null===t?null:(t.point.applyMatrix4(e.matrixWorld),t.distance=t.point.distanceTo(n.ray.origin),t.object=e,t.distance<n.near||t.distance>n.far?null:t)}}}]);