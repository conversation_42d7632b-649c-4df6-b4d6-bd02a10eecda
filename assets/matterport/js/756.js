/*! For license information please see 756.js.LICENSE.txt */
"use strict";(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[756],{7402:(e,t,o)=>{o.d(t,{g:()=>d});var s=o(81396),n=o(17878),i=o(56512),r=o(82814),a=o(12529),l=o(53203);class d extends r.S{constructor(e,t,o=n.o.ALL){super(),this._opacity=1,this._chunks=[],this.size=new s.Vector3,this.center=new s.Vector3,this.built=!1,this.layers.mask=o.mask,this.name=`RoomMesh:${e}-${t}`,this.meshGroup=e,this.meshSubgroup=t,this.renderOrder=a.z.default,this.onBeforeRender=(e,t,o,s,n,i)=>{this.updateUniforms(n,i)}}dispose(){this.reset()}reset(){this._chunks.length=0,this.geometry.dispose(),delete this.onBuild,delete this.onOpacityUpdate,this.built=!1}addChunk(e){-1===this._chunks.indexOf(e)&&this._chunks.push(e)}getChunk(e){return this._chunks[e]}build(){if(this.built)throw new Error("build() should only be called once");if(!this._chunks.length)return;const e=(0,i.qf)(this._chunks.map((e=>e.geometry)));e.clearGroups();let t=0;this.material=[],this._chunks.forEach(((o,s)=>{o.geometry&&o.geometry.index&&(e.addGroup(t,o.geometry.index.count,s),t+=o.geometry.index.count,o.geometry.dispose(),o.geometry=e,o.notifyOnMaterialUpdated((e=>{Array.isArray(this.material)&&(this.material[s]=e),this.onMaterialUpdate&&this.onMaterialUpdate()})),o.onOpacityUpdate=e=>{this.opacity=e})})),this.geometry=e,this.geometry.computeBoundingBox(),this.geometry.computeBoundingSphere(),this.material=this._chunks.map((e=>e.material)),this.size=this.boundingBox.getSize(this.size),this.center=this.boundingBox.getCenter(this.center),this.built=!0,this.onBuild&&this.onBuild()}buildWithTileChunk(e){if(this.built)return;const{meshGroup:t,meshSubgroup:o,lod:s}=e;this.name=`RoomMesh:${s}-${t}-${o}-${e.chunkIndex}`,this.meshGroup=t,this.meshSubgroup=o,this._chunks.push(e),e.notifyOnMaterialUpdated((e=>{this.material=e,this.onMaterialUpdate&&this.onMaterialUpdate()})),e.onOpacityUpdate=e=>{this.opacity=e},this.size=this.boundingBox.getSize(this.size),this.center=this.boundingBox.getCenter(this.center),this.built=!0,this.onBuild&&this.onBuild()}updateUniforms(e,t){e instanceof s.RawShaderMaterial&&(t?this.chunks[t.materialIndex].onBeforeDraw(e):this.chunks.length&&this.chunks[0].onBeforeDraw(e))}get boundingBox(){return(0,i.A5)(this.geometry)}set opacity(e){e!==this.opacity&&(this._opacity=e,this.raycastEnabled=e>l.xx.FADE_CLICKABLE_THRESHOLD,this.renderOrder=e<l.xx.FADE_OPAQUE?a.z.ghostFloor:a.z.default,this.onOpacityUpdate&&this.onOpacityUpdate(e))}get opacity(){return this._opacity}get chunks(){return this._chunks}getSortKey(){return this.chunks.length?this._chunks[0].getSortKey():0}}},38987:(e,t,o)=>{o.d(t,{u:()=>n});var s=o(19663);class n extends s.m{constructor(e=null){super(),this.id="SET_MOUSE_CURSOR",this.payload={cursor:e}}}},34956:(e,t,o)=>{var s;o.d(t,{C:()=>s}),function(e){e.NONE="none",e.DEFAULT="default",e.MOVE="move",e.MOVE_LF="col-resize",e.MOVE_UD="row-resize",e.XHAIR="crosshair",e.PLUS="cell",e.QUESTION="help",e.NOPE="not-allowed",e.FINGER="pointer",e.TEXT="text",e.TEXT_VERT="vertical-text",e.ZOOM_IN="zoom-in",e.ZOOM_OUT="zoom-in",e.GRAB="grab",e.GRABBING="grabbing",e.ARROW_R="e-resize",e.ARROW_L="w-resize",e.ARROW_U="n-resize",e.ARROW_D="s-resize",e.ARROW_UR="ne-resize",e.ARROW_UL="nw-resize",e.ARROW_DR="se-resize",e.ARROW_DL="sw-resize",e.ARROW_LR="ew-resize",e.ARROW_UD="ns-resize",e.ARROW_URDL="nesw-resize",e.ARROW_ULDR="nwse-resize",e.ROOMBOUNDS_DEFAULT="rbe-default",e.ROOMBOUNDS_MOVING="rbe-moving",e.ROOMBOUNDS_PLACE_NODE="rbe-place-node",e.ROOMBOUNDS_FINISH_ROOM="rbe-finish-room"}(s||(s={}))},77161:(e,t,o)=>{o.d(t,{Z:()=>ge});var s=o(32683),n=o(92558),i=o(16747),r=o(81396),a=o(75668);class l{constructor(e,t,o,s,n){this.rbushBbox={minX:0,minY:0,maxX:1,maxY:1},this.overlapsCircle=(()=>{const e=new r.Vector3;return(t,o)=>{const s=this.getVec3(e).distanceTo(t);return s<=o+.3?s:null}})(),this.overlapsLine=(()=>{const e=new r.Vector3,t=new r.Vector3,o=new r.Vector3;return(s,n)=>{const i=e.subVectors(n,s),r=this.getVec3(o),a=t.subVectors(r,s),l=a.dot(i)/i.length(),d=a.length();return Math.sqrt(d*d-l*l)<.3?[{entity:this,t:l/i.length()}]:[]}})(),this.id=e,this.floorId=t,this.x=o,this.z=s,this.layerId=n}getEntityAnalytic(){return"node"}getPoint(){return{x:this.x,z:this.z}}getVec3(e){const t=e||new r.Vector3;return t.set(this.x,0,this.z),t}getVec2(e){const t=e||new r.Vector2;return t.set(this.x,this.z),t}getViewCenter(e=new r.Vector3){return this.getVec3(e)}getSnapshot(){return{x:this.x,y:-this.z,layerId:this.layerId}}distanceTo(e){const t=this.x-e.x,o=this.z-e.z;return Math.sqrt(t*t+o*o)}updateRBushBBox(){this.rbushBbox={minX:this.x-a.dt,minY:this.z-a.dt,maxX:this.x+a.dt,maxY:this.z+a.dt}}get minX(){return this.rbushBbox.minX}get maxX(){return this.rbushBbox.maxX}get minY(){return this.rbushBbox.minY}get maxY(){return this.rbushBbox.maxY}}var d=o(83021);class h{constructor(e){this.afterAction=e,this.actions=[]}run(){for(const e of this.actions)e.run(),this.afterAction()}invert(){for(let e=this.actions.length-1;e>=0;e--){this.actions[e].invert(),this.afterAction()}}merge(e){if(0===this.actions.length)this.actions.push(e);else{const t=this.actions[this.actions.length-1];t&&t.merge(e)||this.actions.push(e)}return!0}logInfo(){return this.actions.map((e=>Object.assign({undo:!0},e.logInfo())))}lastAction(){return this.actions.length?this.actions[this.actions.length-1]:null}}class c{constructor(e){this.afterAction=e,this.undoBuffer=new d.P(50),this.finalized=!1}push(e){(this.undoBuffer.isEmpty()||this.finalized)&&(this.undoBuffer.push(new h(this.afterAction)),this.finalized=!1);const t=this.undoBuffer.peek();t&&t.merge(e)}pop(){return this.undoBuffer.pop()}peek(){return this.undoBuffer.peek()}finalize(){this.finalized=!0}availableUndos(){return this.undoBuffer.count}clear(){this.undoBuffer.clear()}}var u=o(35895),g=o(97998),p=o(15004),f=o(80978);class m{constructor(){this.data=new Map,this.addedObservers=new Set,this.updatedObservers=new Set,this.childUpdatedObservers=new Set,this.deletedObservers=new Set}clear(){this.data.clear()}get(e){return this.data.get(e)}set(e,t){this.data.set(e,t)}delete(e){this.data.delete(e)}has(e){return this.data.has(e)}get size(){return this.data.size}onChanged(e){return(0,u.k1)((()=>{e.onAdded&&this.addedObservers.add(e.onAdded),e.onUpdated&&this.updatedObservers.add(e.onUpdated),e.onChildUpdated&&this.childUpdatedObservers.add(e.onChildUpdated),e.onRemoved&&this.deletedObservers.add(e.onRemoved)}),(()=>{e.onAdded&&this.addedObservers.delete(e.onAdded),e.onUpdated&&this.updatedObservers.delete(e.onUpdated),e.onChildUpdated&&this.childUpdatedObservers.delete(e.onChildUpdated),e.onRemoved&&this.deletedObservers.delete(e.onRemoved)}),!0)}}var w=o(43517),y=o(82582),_=o.n(y);class O{constructor(e,t){this.data=e,this.inputs=t,this._outputCache=null}onRun(e){throw new Error("Method not implemented.")}onInvert(e,t){throw new Error("Method not implemented.")}run(){this._outputCache=this.onRun(this.inputs)}invert(){if(!this._outputCache)throw new Error("Attempted to inverse a data action before running it");this.onInvert(this._outputCache,this.inputs),this._outputCache=null}merge(e){return!1}get output(){if(this._outputCache)return this._outputCache;throw new Error("Tried to read output of an action before it was run")}logInfo(){return Object.assign({name:this.name()},this.inputs)}}class I extends O{onRun(e){const t=this.data.getNode(e.nodeId),o={x:t.x,z:t.z};return this.data._updateNode(e.nodeId,e.newPos),this.data._updateDependentsForNodes(t),{prevPos:o}}onInvert(e,t){this.data._updateNode(t.nodeId,e.prevPos);const o=this.data.getNode(t.nodeId);this.data._updateDependentsForNodes(o)}merge(e){return e instanceof I&&e.inputs.nodeId===this.inputs.nodeId&&(this.inputs.newPos=e.inputs.newPos,!0)}name(){return"MoveNode"}}class W extends O{onRun(e){const t=this.data._createNode(e.from,e.floorId),o=this.data._createNode(e.to,e.floorId),s=this.data._createWall(e.type,t,o,e.width,[],.5);return{fromId:t.id,toId:o.id,wall:s.id}}onInvert(e,t){this.data._deleteWall(e.wall),this.data._deleteNode(e.fromId),this.data._deleteNode(e.toId)}merge(e){return!!(e instanceof I&&this._outputCache&&e.inputs.nodeId===this._outputCache.toId)&&(this.inputs.to=e.inputs.newPos,!0)}name(){return"AddFloatingEdge"}}class v extends O{onRun(e){const t=this.data.getWall(e.wallId),o=t.from.getPoint(),s=t.to.getPoint();return this.data._updateNode(t.from.id,e.newFromPos),this.data._updateNode(t.to.id,e.newToPos),this.data._updateDependentsForNodes(t.from,t.to),{prevFromPos:o,prevToPos:s}}onInvert(e,t){const o=this.data.getWall(t.wallId);this.data._updateNode(o.from.id,e.prevFromPos),this.data._updateNode(o.to.id,e.prevToPos),this.data._updateDependentsForNodes(o.from,o.to)}merge(e){return e instanceof v&&e.inputs.wallId===this.inputs.wallId&&(this.inputs.newFromPos=e.inputs.newFromPos,this.inputs.newToPos=e.inputs.newToPos,!0)}name(){return"MoveEdge"}}class b extends O{onRun(e){const t=this.data.getNode(e.fromId),o=this.data._createNode(e.to,t.floorId),s=this.data._createWall(e.type,t,o,e.width,[],.5);return{toId:o.id,wall:s.id}}onInvert(e,t){this.data._deleteWall(e.wall),this.data._deleteNode(e.toId)}merge(e){return!!(e instanceof I&&this._outputCache&&e.inputs.nodeId===this._outputCache.toId)&&(this.inputs.to=e.inputs.newPos,!0)}name(){return"AddTrailingEdgeToNode"}}var N=o(58353),S=o(65661);class R extends O{run(){super.run(),this.computeRooms(),this._outputCache&&this._outputCache.updateNodeDependencies.length&&this.data._updateDependentsForNodes(...this._outputCache.updateNodeDependencies.map((e=>this.data.getNode(e))))}onInvert(e,t){throw new Error("Method not implemented.")}invert(){let e;if(!this._outputCache)throw new Error("Attempted to inverse a data action before running it");e=this.onInvert(this._outputCache,this.inputs),this._outputCache=null;for(const e of this.modificationRecord.createdRooms)this.data._deleteRoom(e.id);const t=e=>({points:e.points.map((e=>this.data.getNode(e.id))),walls:new Set(Array.from(e.walls.values()).map((e=>this.data.getWall(e.id)))),holesCW:e.holesCW.map((e=>e.map((e=>this.data.getNode(e.id))))),holes:e.holes.map((e=>new Set(Array.from(e.values()).map((e=>this.data.getWall(e.id))))))});for(const e of this.modificationRecord.updatedRooms){const{points:o,walls:n,holesCW:i,holes:r}=t(e),a=new s.JJ({id:e.id,layerId:e.layerId,name:e.name,points:o,walls:n,holesCW:i,holes:r,location:e.location,includeInAreaCalc:e.includeInAreaCalc,hide:e.hide,keywords:e.keywords.slice(),classifications:e.classifications.slice(),height:e.height});this.data._updateRoom(e.id,a)}for(const e of this.modificationRecord.deletedRooms){const{points:o,walls:s,holesCW:n,holes:i}=t(e);this.data._createRoom({id:e.id,layerId:this.data.defaultLayerId,name:"",points:o,walls:s,holesCW:n,holes:i,location:e.location,includeInAreaCalc:e.includeInAreaCalc,hide:e.hide,keywords:e.keywords.slice(),classifications:e.classifications.slice(),height:e.height})}if(e){const t=e.map((e=>this.data.getNode(e)));this.data._updateDependentsForNodes(...t)}}wallsForLoop(e){const t=new Set;for(let o=0;o<e.length;o++)t.add(this.data.getWallForNodes(e[o].id,e[(o+1)%e.length].id));return t}computeRooms(){var e;this.modificationRecord={createdRooms:[],updatedRooms:[],deletedRooms:[]};const{regions:t,holes:o}=this.findEnclosedRegionsAndHoles(),i=this.mapHolesToRegions(o,t),r=i.map((e=>e.map((e=>this.wallsForLoop(e))))),a=t.map((e=>new Set(this.wallsForLoop(e)))),l=new Array;for(const e of this.data.rooms.values())for(let t=0;t<a.length;t++){const o=a[t],s=Array.from(e.walls.values()).reduce(((e,t)=>o.has(t)?e+1:e),0);s>0&&l.push({score:s/Math.max(o.size,e.walls.size),newWalls:o.size-s,oldRoomId:e.id,oldRoomClassifications:e.classifications,newRoomIndex:t})}const d=new Set,h=new Array(t.length),c=new Array(t.length);l.sort(((e,t)=>{var o,s;return t.score-e.score||((null===(o=this.data.rooms.get(t.oldRoomId))||void 0===o?void 0:o.area)||0)-((null===(s=this.data.rooms.get(e.oldRoomId))||void 0===s?void 0:s.area)||0)}));for(const{oldRoomId:e,newRoomIndex:t,oldRoomClassifications:o,newWalls:s}of l)d.has(e)||h[t]||(h[t]=e,d.add(e)),!c[t]&&s<=2&&(c[t]=o);for(let e=0;e<t.length;e++)h[e]||(h[e]=(0,n._r)());for(let o=0;o<t.length;o++){const n=h[o],a=t[o],l=new Set;for(let e=0;e<a.length;e++){const t=a[e],o=a[(e+1)%a.length],s=this.data.getWallForNodes(t.id,o.id);l.add(s)}const d=this.data.rooms.get(n);if(d){const e=new s.JJ({id:n,layerId:d.layerId,name:"",points:t[o],walls:l,holesCW:i[o],holes:r[o],location:d.location,includeInAreaCalc:d.includeInAreaCalc,hide:d.hide,keywords:d.keywords.slice(),classifications:d.classifications.slice(),height:d.height});(0,N.nb)(d.walls,e.walls)&&(0,N.nb)((0,N.dW)(...d.holes),(0,N.dW)(...e.holes))||(this.data._updateRoom(n,e),this.modificationRecord.updatedRooms.push(d))}else{const a=null!==(e=c[o])&&void 0!==e?e:[],d=(0,s.Rd)(a,s.GS.OUTDOOR)?s.Lh.OUTDOOR:s.Lh.INDOOR,h=!(0,s.Rd)(a,s.GS.NON_AREA),u=(0,s.Rd)(a,s.GS.HIDE),g=this.data._createRoom({id:n,layerId:this.data.defaultLayerId,name:"",points:t[o],walls:l,holesCW:i[o],holes:r[o],classifications:a.slice(),location:d,includeInAreaCalc:h,hide:u,keywords:[],height:NaN});this.modificationRecord.createdRooms.push(g)}}const u=new Set(h);for(const e of this.data.rooms.values())u.has(e.id)||(this.data._deleteRoom(e.id),this.modificationRecord.deletedRooms.push(e))}findEnclosedRegionsAndHoles(){const e=new Array,t=new Array,o=new Set,s=new Set,n=(n,i)=>{const r=`${n.id}/${i.id}`,a=this.data.traceWallsFromTo(n,i),l=e=>{if(e.has(r))return!0;for(let t=0;t<a.length;t++){const o=a[t],s=a[(t+1)%a.length],n=`${o.id}/${s.id}`;e.add(n)}return!1},d=(0,p.SV)(a,!0);d>=.01?l(o)||e.push((0,p.xS)(a)):d<=-.01&&(l(s)||t.push((0,p.xS)(a).reverse()))};for(const e of this.data.walls.values())n(e.from,e.to),n(e.to,e.from);return{regions:e,holes:t}}mapHolesToRegions(e,t){const o=t.map((e=>[])),s=t.map((e=>(0,p.SV)(e))),n=t.map((e=>e.map((e=>[e.x,e.z]))));for(const i of e){const e=(0,p.SV)(i);let r=9999999,a=-1;const l=i.map((e=>[e.x,e.z]));for(let o=0;o<t.length;o++){const d=s[o];e>=d||i[0].floorId!==t[o][0].floorId||(0,S.D)(l,n[o])&&d<r&&(r=d,a=o)}a>=0&&o[a].push(i)}return o}}function x(e,t,o){if(0===e.openings.length)return;const s=new r.Line3(e.from.getVec3(),e.to.getVec3()),n=t.map((e=>({wall:e,line:new r.Line3(e.from.getVec3(),e.to.getVec3())}))),i=(e,t)=>{const o=e.closestPointToPointParameter(t,!1);if(!(o<0||o>1))return o};for(const t of e.openings){const{type:e,width:a,relativePos:l,id:d}=t,h=s.at(l,new r.Vector3);for(const{wall:t,line:s}of n){const n=i(s,h);if(void 0!==n){o._createWallOpening(t,e,n,a,d);break}}}}function E(e,t){return A(e.openings,t.defaultLayerId),t._createWall(e.type,e.from,e.to,e.width,e.openings,e.bias,e.id)}function A(e,t){for(const o of e)o.layerId=t}function V(e,t){const o=e.getWall(t.wallId).clone(),s=e.getEdgeCountForNode(o.from),n=e.getEdgeCountForNode(o.to);e._deleteWall(t.wallId);let i="none";1===s&&1===n?(i="both",e._deleteNode(o.from.id),e._deleteNode(o.to.id)):1===s?(i="from",e._deleteNode(o.from.id)):1===n?(i="to",e._deleteNode(o.to.id)):i="none";const r=[];switch(i){case"from":r.push(o.to.id);break;case"to":r.push(o.from.id);break;case"none":r.push(o.from.id,o.to.id)}return{deletedWall:o,deletedNodes:i,updateNodeDependencies:r}}function T(e,t){const o=t.deletedWall;let s,n;switch(A(o.openings,e.defaultLayerId),t.deletedNodes){case"both":s=e._createNode({x:o.from.x,z:o.from.z},o.floorId,o.from.id),n=e._createNode({x:o.to.x,z:o.to.z},o.floorId,o.to.id),e._createWall(o.type,s,n,o.width,o.openings,o.bias,o.id);break;case"none":e._createWall(o.type,o.from,o.to,o.width,o.openings,o.bias,o.id);break;case"from":s=e._createNode({x:o.from.x,z:o.from.z},o.floorId,o.from.id),e._createWall(o.type,s,o.to,o.width,o.openings,o.bias,o.id);break;case"to":n=e._createNode({x:o.to.x,z:o.to.z},o.floorId,o.to.id),e._createWall(o.type,o.from,n,o.width,o.openings,o.bias,o.id);break;default:throw new Error(`${t.deletedNodes} is an invalid value`)}switch(t.deletedNodes){case"from":return[o.to.id];case"to":return[o.from.id];case"none":return[o.from.id,o.to.id];case"both":return[]}}class z extends R{onRun(e){return V(this.data,e)}onInvert(e,t){return T(this.data,e)}name(){return"DeleteEdge"}}class L extends R{onRun(e){const t=this.data.getWall(e.fromWallId).clone(),o=t.getDirection().normalize().multiplyScalar(e.along),s=t.from.getVec3().add(o);this.data._deleteWall(e.fromWallId);const n=this.data._createNode({x:s.x,z:s.z},t.floorId),i=this.data._createNode(e.to,t.floorId),r=this.data._createWall(e.type,n,i,e.width,[],.5),a=this.data._createWall(t.type,t.from,n,t.width,[],t.bias),l=this.data._createWall(t.type,n,t.to,t.width,[],t.bias);return x(t,[a,l],this.data),{deletedWall:t,newTrailingWall:r,newLeftWall:a,newRightWall:l,newNodeToId:i.id,updateNodeDependencies:[]}}onInvert(e,t){const{deletedWall:o,newTrailingWall:s,newLeftWall:n,newRightWall:i}=e;return this.data._deleteWall(s.id),this.data._deleteWall(n.id),this.data._deleteWall(i.id),this.data._deleteNode(s.from.id),this.data._deleteNode(s.to.id),E(o,this.data),[]}merge(e){return!!(e instanceof I&&this._outputCache&&e.inputs.nodeId===this._outputCache.newTrailingWall.to.id)&&(this.inputs.to=e.inputs.newPos,!0)}name(){return"AddTrailingEdgeToEdge"}}class C extends O{onRun({wallId:e,props:t}){const o=this.data.getWall(e),s={};for(const e of Object.keys(t))s[e]=o[e];return this.data._setEdgeProps(e,t),{wallId:e,props:s}}onInvert({wallId:e,props:t},o){this.data._setEdgeProps(e,t)}merge(e){return e instanceof C&&e.inputs.wallId===this.inputs.wallId&&(this.inputs.props.width=e.inputs.props.width,this.inputs.props.bias=e.inputs.props.bias,!0)}name(){return"SetEdgeProps"}}class M extends R{onRun(e){const t=this.data.getWall(e.wallId),o=[],s=[],n=[],i={originalFromPos:t.from.getPoint(),originalToPos:t.to.getPoint(),deletedWalls:s,createdWalls:o,originalWallId:e.wallId,createdNodes:n,updateNodeDependencies:[]};return e.fromNode&&this.createJoint(t.from,t,s,o,n),e.toNode&&this.createJoint(t.to,t,s,o,n),i}onInvert(e,t){for(const t of e.createdWalls)this.data._deleteWall(t.id);for(const t of e.deletedWalls)E(t,this.data);for(const t of e.createdNodes)this.data._deleteNode(t.id);const o=this.data.getWall(this.inputs.wallId);return this.data._updateNode(o.from.id,e.originalFromPos),this.data._updateNode(o.to.id,e.originalToPos),[o.from.id,o.to.id]}merge(e){return e instanceof v&&e.inputs.wallId===this.inputs.wallId&&(this.inputs.fromPos=e.inputs.newFromPos,this.inputs.toPos=e.inputs.newToPos,!0)}createJoint(e,t,o,s,n){const i=this.data._createNode({x:e.x,z:e.z},e.floorId),r=this.data.getWallsForNode(e);for(const n of r){const r=n.clone();if(r.id!==t.id){const t=r.getOtherNode(e);o.push(r),this.data._deleteWall(r.id);const n=r.from===t?this.data._createWall(r.type,t,i,r.width,[],r.bias):this.data._createWall(r.type,i,t,r.width,[],r.bias);x(r,[n],this.data),s.push(n)}}n.push(i);const a=t.from===e?this.data._createWall(t.type,i,t.from,t.width,[],t.bias):this.data._createWall(t.type,t.to,i,t.width,[],t.bias);s.push(a)}name(){return"AddJointToNode"}}class D extends R{onRun(e){const t=this.data.getNode(e.nodeId),o=Array.from(this.data.getWallsForNode(t).values());if(2!==o.length)throw new Error("Can only delete nodes with two edges on it");const s=o[0].clone(),n=s.getOtherNode(t),i=o[1].clone(),r=i.getOtherNode(t);this.data._deleteWall(s.id),this.data._deleteWall(i.id),this.data._deleteNode(t.id);const a=this.data.newWallWouldIntersect({n0:n,n1:r})?null:this.data._createWall(s.type,n,r,(s.width+i.width)/2,[],(s.bias+i.bias)/2);return a&&(x(s,[a],this.data),x(i,[a],this.data)),{deletedNode:t,deletedWall1:s,deletedWall2:i,createdWall:a,updateNodeDependencies:[]}}onInvert(e,t){const{createdWall:o,deletedNode:s,deletedWall1:n,deletedWall2:i}=e;o&&this.data._deleteWall(o.id),this.data._createNode(s.getPoint(),s.floorId,s.id);for(const e of[n,i])E(e,this.data);return[]}name(){return"DeleteNode"}}class P extends R{onRun(e){const t=this.data.getNode(e.fromId),o=this.data.getNode(e.toId);return{wall:this.data._createWall(e.type,t,o,e.width,[],.5).id,updateNodeDependencies:[]}}onInvert(e,t){return this.data._deleteWall(e.wall),[]}name(){return"AddBridgingEdge"}}class B extends O{onRun(e){const{id:t}=e,o=this.data.getOpening(t),{type:s,relativePos:n,width:i}=o;return this.data._setOpeningDetails(t,e),{id:t,type:s,relativePos:n,width:i}}onInvert(e,t){const{id:o}=e;this.data._setOpeningDetails(o,e)}name(){return"EditOpeningDetails"}}class U extends O{onRun(e){const{openingId:t}=e,o=this.data.getOpening(t);return this.data._deleteWallOpening(t),{deletedOpening:o}}onInvert(e,t){const{id:o,wallId:s,type:n,relativePos:i,width:r}=e.deletedOpening,a=this.data.getWall(s);this.data._createWallOpening(a,n,i,r,o)}name(){return"DeleteWallOpening"}}class F extends O{onRun(e){const{wallId:t,type:o,relativePos:s,width:n}=e,i=this.data.getWall(t);return{openingId:this.data._createWallOpening(i,o,s,n).id}}onInvert(e,t){const{openingId:o}=e;this.data._deleteWallOpening(o)}name(){return"AddOpeningAction"}}function k(e,t,o,s){const n=t.getOtherNode(o);return n!==s&&!e.hasWallBetween(n,s)}function G(e,t){const o=[],s=[],n=[],i=[],r=new Set;for(const a of t){const t=[],{mergeId:l,keepId:d}=a;r.add(d);const h=e.getNode(l),c=e.getNode(d),u=e.getWallsForNode(c),g=c.getPoint();s.push(h),n.push(d),i.push(g),e._updateNode(c.id,h.getPoint());const p=e.getWallsForNode(h);for(const o of p){const s=o.clone(),n=o.getOtherNode(h),i=o.width;e._deleteWall(o.id);let r=null;k(e,o,h,c)&&(e.hasWallBetween(n,c)||(r=e._createWall(o.type,n,c,i,[],o.bias),x(s,[r],e),u.add(r))),t.push({deleted:s,created:r}),u.delete(o)}e._deleteNode(h.id),0===u.size&&(e._deleteNode(c.id),s.push(c)),o.push(t)}return{deletedNodes:s,wallDiffs:o,oldKeepNodeIds:n,oldKeepNodePositions:i,updateNodeDependencies:r}}function H(e,t){const o=[];for(let s=t.deletedNodes.length-1;s>=0;s--){const n=t.deletedNodes[s],i=t.wallDiffs[s],r=e._createNode(n.getPoint(),n.floorId,n.id);o.push(r.id);for(const t of i){t.created&&e._deleteWall(t.created.id);let o=r,s=t.deleted.getOtherNode(n);if(r.id!==t.deleted.from.id){const e=o;o=s,s=e}A(t.deleted.openings,e.defaultLayerId),e._createWall(t.deleted.type,o,s,t.deleted.width,t.deleted.openings,t.deleted.bias,t.deleted.id)}e._updateNode(t.oldKeepNodeIds[s],t.oldKeepNodePositions[s])}return t.oldKeepNodeIds}function K(e,t){const o=[],s=new Set;for(const n of t){const t=e.getNode(n),r=e.getAttachedEntities(t),a=e.findNodeOrWallForPosition(t.getVec3(),t.floorId,r);if(a&&a instanceof i.c){const i=a.getProjection(t.getVec3()),r=a.clone(),l={x:t.x,z:t.z},d=r.getDirection().normalize().multiplyScalar(i),h=r.from.getVec3().add(d);e._deleteWall(r.id),e._updateNode(n,{x:h.x,z:h.z});const c=e.hasWallBetween(r.from,t)?null:e._createWall(r.type,r.from,t,r.width,[],r.bias),u=e.hasWallBetween(t,r.to)?null:e._createWall(r.type,t,r.to,r.width,[],r.bias);x(r,[c,u].filter((e=>!!e)),e),o.push({deletedWall:r,newLeftWall:c,newRightWall:u,prevNodePos:l,nodeId:t.id}),s.add(t.id)}}return{splitDiffs:o,updateNodeDependencies:s}}function Z(e,t){for(let o=t.splitDiffs.length-1;o>=0;o--){const{newLeftWall:s,newRightWall:n,deletedWall:i,prevNodePos:r,nodeId:a}=t.splitDiffs[o];s&&e._deleteWall(s.id),n&&e._deleteWall(n.id),e._updateNode(a,r),E(i,e)}return t.splitDiffs.map((e=>e.nodeId))}function X(e,t){const o=[],s=function(e){const t=[];for(const o of e){if(2!==o.length)throw new Error("Can only merge co-linear overlaps");if(0===t.length){t.push(o);continue}const e=t[t.length-1],s=e[1];if(o[0].t<s.t+.001){const n=o[1];n.t>=s.t&&(t[t.length-1]=[e[0],n])}else t.push(o)}return t}(t);if(s[0][0].t>0){const t=s[0][0].entity;o.push([e.from,t])}for(let e=0;e<s.length-1;e++){const t=s[e],n=s[e+1],i=t[1].entity,r=n[0].entity;o.push([i,r])}if(s[s.length-1][1].t<1){const t=s[s.length-1][1].entity;o.push([t,e.to])}return o}class q extends R{onRun(e){return Y(e,this.data)}onInvert(e,t){return $(e,this.data)}name(){return"MergeOverlappingEntities"}}class j extends R{onRun(e){const t={},o=[];for(const e of this.data.getFloorsWithNodes()){const s=this.data.getNodesByFloor(e);if(s.size>0){const n=Array.from(s.values())[0],i=this.data.findOverlappingEntities(n);if(i){const s=Y({movedEntity:n,nodeOverlaps:i.nodeOverlaps,nodeWallOverlaps:i.nodeWallOverlaps,collinearWall:i.collinearWall,intersectingWall:i.intersectingWall},this.data);t[e]=s,o.push(...s.updateNodeDependencies)}}}return{overlapsByFloor:t,updateNodeDependencies:o}}onInvert(e,t){const o=[];for(const t in e.overlapsByFloor){const s=$(e.overlapsByFloor[t],this.data);o.push(...s)}return o}name(){return"ValidateGraphAndComputeRooms"}}function Y(e,t){const o=function(e,t,o){const s=[],n=new Set;let i=t,r=0;for(;(i.nodeOverlaps.length>0||i.nodeWallOverlaps.length>0)&&r<10;){const t=G(e,i.nodeOverlaps);for(const e of t.updateNodeDependencies)n.add(e);const a=K(e,i.nodeWallOverlaps);for(const e of a.updateNodeDependencies)n.add(e);s.push({nodeMergeInvertInfo:t,nodeWallInvertInfo:a}),i=e.findOverlappingNodePairs(o),r++}for(const e of s){const t=e.nodeMergeInvertInfo.deletedNodes;for(const e of t)n.delete(e.id)}return{nodeMergeBatches:s,updateNodeDependencies:Array.from(n)}}(t,{nodeOverlaps:e.nodeOverlaps,nodeWallOverlaps:e.nodeWallOverlaps},e.movedEntity);let s=[];e.collinearWall&&(s=function(e,t){const o=[];let s=e.findWallWithWorstOverlaps(t,"colinear"),n=0;for(;null!=s&&n<10;){const i=s.wall;e._deleteWall(i.id);const r=X(s.wall,s.overlaps).map((t=>e._createWall(i.type,t[0],t[1],i.width,[],i.bias)));o.push({deletedWall:i,createdWalls:r}),s=e.findWallWithWorstOverlaps(t,"colinear"),n++}return o}(t,e.collinearWall.floorId));let n=[];return e.intersectingWall&&(n=function(e,t){const o=[];let s=e.findWallWithWorstOverlaps(t,"intersection"),n=0;for(;null!=s&&n<10;){const i=[],r=[],a=[],l=s.wall.clone();if(i.push(l),e._deleteWall(s.wall.id),null!=s){let n=s.wall.from;const d=s.wall.getDirection();for(const o of s.overlaps){if(1!==o.length)throw new Error("possible bug in RoomBoundData.findWallWithWorstOverlaps, it should only return walls intersecting at ONE point");const h=o[0],c=h.entity.clone();i.push(c),e._deleteWall(h.entity.id);const u=s.wall.from.getVec3().addScaledVector(d,o[0].t),g=o[0].entity,p=e._createNode(u,t);r.push(p);const f=e._createWall(s.wall.type,n,p,s.wall.width,[],s.wall.bias);n=p,a.push(f),x(l,[f],e);const m=e._createWall(g.type,g.from,p,g.width,[],g.bias);a.push(m);const w=e._createWall(g.type,g.to,p,g.width,[],g.bias);a.push(w),x(c,[m,w],e)}const h=e._createWall(s.wall.type,n,s.wall.to,s.wall.width,[],s.wall.bias);a.push(h),x(l,[h],e),o.push({createdWalls:a,createdNodes:r,deletedWalls:i})}s=e.findWallWithWorstOverlaps(t,"intersection"),n++}return o}(t,e.intersectingWall.floorId)),{nodeOverlapInvertInfo:o,intersectionSplitInvertInfo:n,collinearOverlapInvertInfo:s,updateNodeDependencies:o.updateNodeDependencies}}function $(e,t){!function(e,t){for(let o=t.length-1;o>=0;o--){const s=t[o];for(const t of s.createdWalls)e._deleteWall(t.id);for(const t of s.createdNodes)e._deleteNode(t.id);for(const t of s.deletedWalls)E(t,e)}}(t,e.intersectionSplitInvertInfo),function(e,t){for(let o=t.length-1;o>=0;o--){const s=t[o],n=s.deletedWall;s.createdWalls.forEach((t=>e._deleteWall(t.id))),E(n,e)}}(t,e.collinearOverlapInvertInfo);return function(e,t){const o=[];for(let s=t.nodeMergeBatches.length-1;s>=0;s--){const{nodeMergeInvertInfo:n,nodeWallInvertInfo:i}=t.nodeMergeBatches[s],r=Z(e,i),a=H(e,n);o.push(...r,...a)}return o}(t,e.nodeOverlapInvertInfo)}var J=o(69877),Q=o(5823);class ee extends O{onRun({roomId:e,name:t,roomTypeIds:o,location:s,includeInAreaCalc:n,hide:i,keywords:r,showDimensions:a,showHeight:l}){const d=this.data.getRoom(e),h={roomId:e,name:d.name,roomTypeIds:d.roomTypeIds.slice(),includeInAreaCalc:d.includeInAreaCalc,hide:d.hide,keywords:d.keywords.slice(),showDimensions:d.showDimensions,showHeight:d.showHeight};return this.data._updateRoomDetails(e,{name:t,roomTypeIds:o,location:s,includeInAreaCalc:n,hide:i,keywords:r,showDimensions:a,showHeight:l}),h}onInvert({roomId:e,name:t,roomTypeIds:o,location:s,includeInAreaCalc:n,hide:i,showDimensions:r,showHeight:a},l){this.data._updateRoomDetails(e,{name:t,roomTypeIds:o,location:s,includeInAreaCalc:n,hide:i,showDimensions:r,showHeight:a})}name(){return"SetRoomDetails"}}var te=o(63422),oe=o(69505),se=o(49827),ne=o(84784),ie=o(19098),re=o(65241),ae=o(90304);class le extends R{onRun({roomId:e}){const t={deletedEdges:[],updateNodeDependencies:[]},o=this.data.getRoom(e);for(const e of o.walls){if(1===Array.from(this.data.getRoomsForWall(e)).filter((t=>t.walls.has(e))).length){const o=V(this.data,{wallId:e.id});t.deletedEdges.push(o),t.updateNodeDependencies.push(...o.updateNodeDependencies)}}for(const e of t.deletedEdges){const o=[];switch(e.deletedNodes){case"from":o.push(e.deletedWall.from.id);break;case"to":o.push(e.deletedWall.to.id);break;case"both":o.push(e.deletedWall.from.id,e.deletedWall.to.id)}t.updateNodeDependencies=t.updateNodeDependencies.filter((e=>-1===o.indexOf(e)))}return t}onInvert(e,t){let o=[];for(const t of e.deletedEdges.reverse())o=o.concat(T(this.data,t));return o}name(){return"DeleteRoom"}}var de=o(62944),he=o(39880);const ce=new Set;var ue;!function(e){e[e.CREATE=0]="CREATE",e[e.UPDATE=1]="UPDATE",e[e.DELETE=2]="DELETE"}(ue||(ue={}));class ge{constructor(e,t={},o=(()=>{})){this.roomClassifications=t,this.broadcast=o,this.name="wall-graph",this.version=0,this.legacyRoomIds=[],this.defaultLayerId=te.gi,this.raycast=null,this.onActionError=e=>{this.logger.error(e)},this._nodes=new m,this._walls=new m,this._rooms=new m,this._wallOpenings=new m,this.undoBuffer=new c(this.commit.bind(this)),this._nodeToWallMap=new Map,this._nodesByFloor=new Map,this._wallToRoomMap=new Map,this._wallsByFloor=new Map,this._wallsByCompositeKey=new Map,this._observerQueue=[],this._deleteSet=new Set,this._anythingChangeObservers=new Set,this._afterFinalizeObservers=new Set,this.logger=new g.Z("wall-graph"),this.isLoading=!1,this.spatialIndex=new Map,this.actionList=[],this.getRelativeAngle=(()=>{const e=new r.Vector2,t=new r.Vector2,o=new r.Vector2,s=(t,o)=>{if(0===t.lengthSq()){const s=this.getNodeNeighbors(o);if(0===s.size)return;const n=Array.from(s.values())[0];t.set(n.x,n.z).sub(e)}};return(n,i,r)=>{if(i===r)return 2*Math.PI;e.set(n.x,n.z),t.set(i.x,i.z).sub(e),s(t,i),o.set(r.x,r.z).sub(e),s(o,r);const a=Math.atan2(t.y,t.x);let l=-(Math.atan2(o.y,o.x)-a);return l<0&&(l+=2*Math.PI),l}})(),this.newWallWouldIntersect=(()=>{const e=new r.Vector2,t=new r.Vector2,o=new r.Vector2,s=new r.Vector2,n=new r.Vector2,i=new r.Vector2,a=new r.Vector2,l=new r.Vector2;return({n0:r,n1:d,n0OverridePos:h,n1OverridePos:c,wallToIgnore:u})=>{if(this.hasWallBetween(r,d)){if(!u)return!0;if(this.getWallForNodes(r.id,d.id)!==u)return!0}const g=e=>Math.round(1e3*e)/1e3,f=(e,t)=>{e===r&&h?t.copy(h):e===d&&c?t.copy(c):e.getVec2(t)};if(f(r,e),f(d,t),o.subVectors(e,t).length()<1e-4)return!1;const m=[[g(e.x),g(e.y)],[g(t.x),g(t.y)]],w=[[0,0],[0,0]];for(const e of this._walls.data.values()){if(r.floorId!==e.floorId||e===u)continue;if(f(e.from,s),f(e.to,n),o.subVectors(s,n).length()<1e-4)continue;if(w[0][0]=g(s.x),w[0][1]=g(s.y),w[1][0]=g(n.x),w[1][1]=g(n.y),(0,p.OH)(m,w))return!0;const t=e.to===r||e.from===r?r:e.to===d||e.from===d?d:void 0;if(t){const o=e.getOtherNode(t),s=t===r?d:r;f(t,i),f(o,a),f(s,l),a.sub(i),l.sub(i);const n=Math.atan2(a.y,a.x);let h=-(Math.atan2(l.y,l.x)-n);h<0&&(h+=2*Math.PI);const c=h,u=2*Math.PI/180;if(c<u||c>2*Math.PI-u)return!0}}return!1}})(),this._updateDependentsForNodes=(()=>{const e=new Set,t=new Set;return(...o)=>{e.clear(),t.clear();const s=o=>{if(!e.has(o)){this.removeFromSpatialIndex(o),this.insertIntoSpatialIndex(o),this._scheduleUpdate(this._walls,o,!0);const s=this._wallToRoomMap.get(o);if(s)for(const e of s.values())t.has(e)||(this._scheduleUpdate(this._rooms,e,!0),e.pointsMoved(),this.calculateRoomInsights(e),t.add(e));for(const e of o.openings)this._scheduleUpdate(this._wallOpenings,e,!0);e.add(o)}};for(const e of o){const t=this._nodeToWallMap.get(e);if(null!=t)for(const o of t){s(o);const t=o.getOtherNode(e),n=this._nodeToWallMap.get(t);if(null!=n)for(const e of n)s(e)}}}})(),this.lowDiscrepancySampleTriangle=(()=>{const e=new r.Vector2(1,0),t=new r.Vector2(0,1),o=new r.Vector2(0,0),s=new r.Vector2,n=new r.Vector2,i=new r.Vector2,a=new r.Vector2,l=new r.Vector3;return(r,d)=>{e.set(1,0),t.set(0,1),o.set(0,0);for(let a=0;a<d;a++){switch(r>>2*a&3){case 0:s.addVectors(t,o).multiplyScalar(.5),n.addVectors(e,o).multiplyScalar(.5),i.addVectors(e,t).multiplyScalar(.5);break;case 1:s.copy(e),n.addVectors(e,t).multiplyScalar(.5),i.addVectors(e,o).multiplyScalar(.5);break;case 2:s.addVectors(t,e).multiplyScalar(.5),n.copy(t),i.addVectors(t,o).multiplyScalar(.5);break;case 3:s.addVectors(o,e).multiplyScalar(.5),n.addVectors(o,t).multiplyScalar(.5),i.copy(o)}e.copy(s),t.copy(n),o.copy(i)}return a.addVectors(e,t).add(o).multiplyScalar(1/3),l.set(a.x,a.y,1-a.x-a.y),l}})(),e&&(this.version=e.version,this.load(e))}load(e){var t,o;this.isLoading=!0;for(const e of Array.from(this.rooms.values()))this._deleteRoom(e.id);for(const e of Array.from(this.walls.values()))this._deleteWall(e.id);for(const e of Array.from(this.nodes.values()))this._deleteNode(e.id);for(const[e,t]of this.spatialIndex)t.clear();this.commit();for(const n in e.floors){const r=e.floors[n],l=r.vertices,d=new(_());this.spatialIndex.set(n,d);const h=[];for(const e in l){const t=l[e],o=this._createNode({x:t.x,z:-t.y},n,e,t.layerId);o.updateRBushBBox(),h.push(o)}d.load(h);const c=[];for(const e in r.edges){const s=r.edges[e],[n,l]=null!==(t=s.vertices)&&void 0!==t?t:e.split(":"),d=s.thickness,h=this.getNode(n),u=this.getNode(l),g=s.type===Q.Pb.INVISIBLE,p=this._createWall(g?i.d.DIVIDER:i.d.SOLID,h,u,g?a.kM:d,[],null!==(o=s.bias)&&void 0!==o?o:.5,e,s.layerId);if(p.bias=1-p.bias,p.updateRBushBBox(),c.push(p),!g)for(const e in s.openings){const{relativePos:t,type:o,width:n,layerId:i}=s.openings[e];this._createWallOpening(p,o,t,n,e,i)}}if(d.load(c),r.rooms)for(const e in r.rooms){const t=r.rooms[e],o=t.edges||[],n=t.holes||[];if(!o.length&&t.vertices)for(let e=0;e<t.vertices.length;e++){const s=t.vertices[e],n=t.vertices[(e+1)%t.vertices.length];o.push(this.getWallForNodes(s,n).id)}if(0===o.length){this.legacyRoomIds.push(e),this.logger.info("Skipping room with zero vertices",e);continue}const i=this.getWall(o[0]),a=new Set(o.map((e=>this.getWall(e)))),l=this.traceWallsFromTo(i.from,i.to,a),d=this.traceWallsFromTo(i.to,i.from,a),h=l.length>d.length?l:d;if(0===h.length){this.logger.warn("No node loop found for room: ",e);continue}(0,p.xS)(h);const c=new Set;for(const e of a)c.add(e.from),c.add(e.to);(0,N.nb)(new Set(h),c)||this.logger.warn("Traced room does not match edge list!",e,h,c);const u=[],g=[];for(const e of n){if(0===e.length)continue;const t=new Set(e.map((e=>this.getWall(e))));g.push(t);const o=this.getWall(e[0]),s=this.traceWallsFromTo(o.from,o.to,t);(0,p.xS)(s).reverse(),u.push(s)}const{location:f,includeInAreaCalc:m,hide:w,keywords:y}=(0,s.Ci)(t.keywords||[]),_=this._createRoom({id:e,layerId:t.layerId,name:t.label||"",points:h,walls:a,holesCW:u,holes:g,classifications:this.translateClassificationLabels(t.classifications),location:f,includeInAreaCalc:m,hide:w,keywords:y,height:t.height}),O=null!=t.width&&0===t.width;null!=t.length&&0===t.length&&O&&(_.showDimensions=!1),_.showHeight=null===t.height||0!==t.height,t.width&&!Number.isNaN(t.width)&&Math.abs(t.width-_.width)>.001&&this.logger.warn(`Room ${e} has width of ${t.width} on MDS but calculated as ${_.width} locally`),t.length&&!Number.isNaN(t.length)&&Math.abs(t.length-_.length)>.001&&this.logger.warn(`Room ${e} has length of ${t.length} on MDS but calculated as ${_.length} locally`),t.area&&!Number.isNaN(t.area)&&Math.abs(t.area-_.area)>=.01&&this.logger.warn(`Room ${e} has area of ${t.area} on MDS but calculated as ${_.area} locally`)}}this.lastSolidWallWidth=void 0,this.isLoading=!1}get walls(){return this._walls.data}get nodes(){return this._nodes.data}get rooms(){return this._rooms.data}get wallOpenings(){return this._wallOpenings.data}getNode(e){const t=this._nodes.get(e);if(t)return t;this.logAndThrow(new Error("WallNode does not exist in WallGraphData"))}getWall(e){const t=this._walls.get(e);if(t)return t;this.logAndThrow(new Error("Wall does not exist in WallGraphData"))}hasRoom(e){return!!this._rooms.get(e)}hasRooms(){return this._rooms.size>0}getRoom(e){const t=this._rooms.get(e);return t||this.logAndThrow(new Error("Room id is invalid!")),t}getOpening(e){const t=this._wallOpenings.get(e);return t||this.logAndThrow(new Error("Opening id is invalid")),t}getEntity(e){const t=this._walls.get(e)||this._nodes.get(e)||this._rooms.get(e)||this._wallOpenings.get(e);return t||this.logAndThrow(new Error("No entity for id.")),t}tryGetEntity(e){return this._walls.get(e)||this._nodes.get(e)||this._rooms.get(e)||this._wallOpenings.get(e)||null}getEdgeCountForNode(e){var t;return(null===(t=this._nodeToWallMap.get(e))||void 0===t?void 0:t.size)||0}getWallsForNode(e){const t=this._nodeToWallMap.get(e);if(t&&t.size>0)return t;this.logAndThrow(new Error("WallNode has no associate walls, this should not happen unless inside an action"))}getFloorsWithNodes(){return Array.from(this._nodesByFloor.keys())}getWallsForFloor(e){const t=this._wallsByFloor.get(e);return t&&t.size>0?t:null}getNodesByFloor(e){const t=this._nodesByFloor.get(e),o=new Set;return t||(this._nodesByFloor.set(e,o),o)}getRoomsForWall(e){return this._wallToRoomMap.get(e)||new Set}getWallNeighbors(e,t){const o=e[t],s=this.getEdgeCountForNode(o);if(1===s)return null;if(2!==s){const s=(()=>{const e=new r.Vector2;return(t,o)=>{const s=o.getOtherNode(t);e.set(s.x-t.x,s.z-t.z).normalize();return e.angle()}})(),n=this._nodeToWallMap.get(o);if(!n||0===n.size)return null;const i=Array.from(n).sort(((e,t)=>s(o,e)-s(o,t))),a=i.indexOf(e);let l=(a+1)%i.length,d=a-1<0?i.length-1:a-1;if("from"===t){const e=d;d=l,l=e}return{left:i[l],right:i[d]}}{const t=this._nodeToWallMap.get(o);if(!t||0===t.size)return null;for(const o of t)if(o.id!==e.id)return{left:o,right:o}}this.logAndThrow(new Error("Not finding both neighbors should never happen"))}getLastWallWidth(e){return e===i.d.SOLID&&this.lastSolidWallWidth?this.lastSolidWallWidth:this.getMostCommonWallWidth(e)}getMostCommonWallWidth(e){if(e===i.d.DIVIDER)return a.kM;const t={};for(const e of this._walls.data.values()){const o=e.width.toFixed(2);t[o]=t[o]?t[o]+1:1}let o=a.Oz,s=0;for(const[e,n]of Object.entries(t))n>s&&Number(e)>a.kM&&(s=n,o=Number(e));return o}findRoomIdForPosition(e,t,o){const s={x:e.x,y:e.z},n=this._rooms.get(o||"");if(n&&o){const e=n.points.map((e=>({x:e.x,y:e.z})));if((0,J.L)(s,e))return o}for(const e of this._rooms.data.values()){if(e.floorId!==t)continue;const o=e.points.map((e=>({x:e.x,y:e.z})));if((0,J.L)(s,o))return e.id}return null}findNodeOrWallForPosition(e,t,o,s=.1){const n=this.spatialIndex.get(t);if(n){const t=n.search({minX:e.x-s,minY:e.z-s,maxX:e.x+s,maxY:e.z+s});let r=null,a=null,d=Number.MAX_VALUE,h=Number.MAX_VALUE;for(const n of t){const t=n.overlapsCircle(e,s);null==t||o.has(n.id)||(n instanceof i.c&&t<d&&(r=n,d=t),n instanceof l&&t<h&&(a=n,h=t))}return a||r}return null}findNodeAndWallsOverlappingLine(e,t,o,s=(()=>!0)){const n=this.spatialIndex.get(o),i=.05,r=[];if(n){const o=n.search({minX:Math.min(e.x,t.x)-i,minY:Math.min(e.z,t.z)-i,maxX:Math.max(e.x,t.x)+i,maxY:Math.max(e.z,t.z)+i});for(const n of o)if(s(n)){const o=n.overlapsLine(e,t);o.length>0&&r.push(o)}}return r}findOverlappingNodePairs(e){const t=e.floorId,o=[],s=new Set,n=new Set,r=[],a=this.getNodesByFloor(t);for(const d of a){const a=this.getAttachedEntities(d),h=this.findNodeOrWallForPosition(d.getVec3(),t,a);if(h)if(h instanceof l){const t=[d.id,h.id].sort().join(":");if(!s.has(t)){let i=d.id,r=h.id;if(r===e.id){const e=r;r=i,i=e}n.has(r)||n.has(i)||(o.push({keepId:i,mergeId:r}),s.add(t),n.add(r))}}else h instanceof i.c&&(n.has(d.id)||r.push(d.id))}return{nodeOverlaps:o,nodeWallOverlaps:r}}findWallWithWorstOverlaps(e,t){let o=[],s=null;const n=this.getWallsForFloor(e),r="colinear"===t?2:1;if(n)for(const e of n){const n="intersection"===t?this.getAttachedEntities(e.from):ce,a="intersection"===t?this.getAttachedEntities(e.to):ce,l=this.findNodeAndWallsOverlappingLine(e.from.getVec3(),e.to.getVec3(),e.floorId,(t=>t instanceof i.c&&t.id!==e.id&&!n.has(t.id)&&!a.has(t.id))).filter((e=>e.length===r));l.length>0&&l.length>o.length&&(o=l,s=e)}return null!=s?{wall:s,overlaps:o.sort(((e,t)=>e[0].t-t[0].t))}:null}findOverlappingEntities(e){const t=e.floorId,{nodeOverlaps:o,nodeWallOverlaps:s}=this.findOverlappingNodePairs(e),n=this.findWallWithWorstOverlaps(t,"colinear"),i=this.findWallWithWorstOverlaps(t,"intersection");if(o.length>0||s.length>0||i||n){return{nodeOverlaps:o,nodeWallOverlaps:s,intersectingWall:i?i.wall:void 0,collinearWall:n?n.wall:void 0}}return null}getSortedRoomClassifications(){return Object.values(this.roomClassifications).sort(((e,t)=>e.label.localeCompare(t.label)))}hasWallBetween(e,t){const o=i.c.getCompositeKey(e.id,t.id);return this._wallsByCompositeKey.has(o)}getWallForNodes(e,t){const o=i.c.getCompositeKey(e,t),s=this._wallsByCompositeKey.get(o);return s||this.logAndThrow(new Error("getWallForNodes: No wall exists for these nodes!")),s}canDeleteEntity(e){if(null!=e){const t=this.getEntity(e);if(t instanceof l){if(2!==this.getEdgeCountForNode(t))return!1;let e;for(const o of this.getWallsForNode(t).values()){if(void 0!==e&&o.type!==e)return!1;e=o.type}return!0}if(t instanceof s.JJ){return!!Array.from(t.walls).find((e=>1===Array.from(this.getRoomsForWall(e)).filter((t=>t.walls.has(e))).length))}return t instanceof i.c||t instanceof f.E}return!1}undo(){const e=this.undoBuffer.pop();if(e){try{this.actionList.push(e.logInfo()),e.invert()}catch(e){return void this.onActionError(e)}this.commit(),this._triggerFinalizeObservers()}}availableUndos(){return this.undoBuffer.availableUndos()}clearUndoBuffer(){this.undoBuffer.clear()}resetHistory(){this.clearUndoBuffer(),this._observerQueue.length=0,this._deleteSet.clear()}finalizeHistory(){this.undoBuffer.finalize(),this._triggerFinalizeObservers()}getAndClearActionList(){const e=this.actionList.slice();return this.actionList.length=0,e}triggerAction(e){var t,o;try{e.run()}catch(e){return this._observerQueue.length=0,this._deleteSet.clear(),void this.onActionError(e)}const s=null===(t=this.undoBuffer.peek())||void 0===t?void 0:t.lastAction();this.undoBuffer.push(e),s!==(null===(o=this.undoBuffer.peek())||void 0===o?void 0:o.lastAction())&&this.actionList.push(e.logInfo()),this.commit()}onNodesChanged(e){return this._nodes.onChanged(e)}onWallsChanged(e){return this._walls.onChanged(e)}onRoomsChanged(e){return this._rooms.onChanged(e)}onOpeningsChanged(e){return this._wallOpenings.onChanged(e)}validateGraph(){this._recomputeRooms()&&(this.commit(),this.finalizeHistory())}commit(){const e=this._observerQueue.length;if(this._flushObserverQueue(),e>0)for(const e of this._anythingChangeObservers)e(void 0)}onPropertyChanged(e,t){const o=this["_"+e];if(o instanceof m){const s=o,n=()=>{s.addedObservers.add(t),s.updatedObservers.add(t),s.deletedObservers.add(t)},i=()=>this.removeOnPropertyChanged(e,t);return(0,u.k1)(n,i,!0,e)}this.logAndThrow(new Error(`Property: ${e} does not exist on wall-data`))}removeOnPropertyChanged(e,t){const o=this["_"+e];if(o instanceof m){const e=o;e.addedObservers.delete(t),e.updatedObservers.delete(t),e.deletedObservers.delete(t)}else this.logAndThrow(new Error(`Property: ${e} does not exist on wall-data`))}onChanged(e){if(this._anythingChangeObservers.has(e))throw new Error("This observer function is already observing this Observable, and double subscriptions are not supported.");return(0,u.k1)((()=>this._anythingChangeObservers.add(e)),(()=>this.removeOnChanged(e)),!0)}removeOnChanged(e){this._anythingChangeObservers.delete(e)}afterFinalize(e){this._afterFinalizeObservers.has(e)&&this.logAndThrow(new Error("Already subscribed!"));return(0,u.k1)((()=>this._afterFinalizeObservers.add(e)),(()=>this._afterFinalizeObservers.delete(e)),!0)}addFloatingEdge(e,t,o,s,n){const i=new W(this,{from:t,to:o,width:s,floorId:n,type:e});return this.triggerAction(i),i.output}addBridgingEdge(e,t,o,s){const n=new P(this,{fromId:t,toId:o,width:s,type:e});return this.triggerAction(n),n.output}addTrailingEdgeToNode(e,t,o,s){const n=new b(this,{fromId:t,to:o,width:s,type:e});return this.triggerAction(n),n.output}addTrailingEdgeToEdge(e,t,o,s,n){const i=new L(this,{fromWallId:t,along:o,to:s,width:n,type:e});return this.triggerAction(i),i.output}canMoveNode(e,t){const o=this.getNode(e),s=this.getWallsForNode(o);for(const e of s){if(0===e.openings.length)continue;const s=e.getOtherNode(o),n=s.x-t.x,i=s.z-t.z,r=Math.sqrt(n*n+i*i);if(!(r>e.length))for(const t of e.openings){const e=t.relativePos*r,o=.5*t.width;if(e+o>r||e-o<0)return!1}}return!0}moveNode(e,t){const o={nodeId:e,newPos:t};this.triggerAction(new I(this,o))}moveWall(e,t,o){const s={wallId:e,newFromPos:t,newToPos:o};this.triggerAction(new v(this,s))}deleteEntity(e){if(this.canDeleteEntity(e)){const t=this.getEntity(e);if(t instanceof l){const t={nodeId:e};this.triggerAction(new D(this,t))}else if(t instanceof i.c){const t={wallId:e};this.triggerAction(new z(this,t))}else if(t instanceof f.E){const t={openingId:e};this.triggerAction(new U(this,t))}else if(t instanceof s.JJ){const t={roomId:e};this.triggerAction(new le(this,t))}}}setEdgeProperties(e,t){const o={wallId:e,props:t};this.triggerAction(new C(this,o))}addWallOpening(e){const t=new F(this,e);return this.triggerAction(t),t.output.openingId}editWallOpeningDetails(e,t){this.triggerAction(new B(this,Object.assign(Object.assign({},t),{id:e})))}addWallJoint(e,t,o){const s={wallId:e,fromNode:t,toNode:o};this.triggerAction(new M(this,s))}setRoomDetails(e,t){this.triggerAction(new ee(this,Object.assign({roomId:e},t)))}mergeOverlappingEntities(e,t,o,s,n){const i={movedEntity:e,nodeOverlaps:t,nodeWallOverlaps:o,collinearWall:s,intersectingWall:n};this.triggerAction(new q(this,i))}getRoomTypeName(e,t){var o;const s=this.getRoom(e);if(s||this.logAndThrow(new Error("Room does not exist")),s.classifications&&s.classifications.length>0){return(0,ne.ZJ)(s.classifications.map((e=>e.id))).map((e=>{var o;return(null===(o=this.roomClassifications[e])||void 0===o?void 0:o.label)||t})).join(a.X9)}return(null===(o=this.roomClassifications[a.ub])||void 0===o?void 0:o.label)||t}getRoomLabel(e,t){const o=this.getRoom(e);return o||this.logAndThrow(new Error("Room does not exist")),o.name?o.name:this.getRoomTypeName(e,t)}getPotentialRoomCanvasLabels(e,t,o){const s=(...e)=>e.filter((e=>e.length>0)).join("\n"),n=this.getRoom(e),i=!n.isOther()||n.name?this.getRoomLabel(e,t):"",r=n.hide?"":(0,de.dO)(o,n.getArea(o)).area,a=n.hide?"":n.getMeasurementText(o),l=[];for(let e=i.length;e>=0;e--)l.push(s((0,he.aS)(i,e),r,a),s((0,he.aS)(i,e),r));for(let e=i.length;e>=0&&i.length>0;e--)l.push((0,he.aS)(i,e));return 0===i.length&&l.push("..."),l}getAttachedEntities(e){const t=new Set;t.add(e.id);const o=this.getNode(e.id),s=this.getWallsForNode(o);for(const e of s){t.add(e.id);const s=e.getOtherNode(o);1===this.getEdgeCountForNode(o)&&1===this.getEdgeCountForNode(s)&&t.add(s.id)}return t}translateClassificationLabels(e){return e?e.map((e=>{const t=this.roomClassifications[e.id];return t?Object.assign(Object.assign({},e),{label:t.label}):e})):[]}traceWallsFromTo(e,t,o){const s=[e,t];let n=!1;for(;n=!this.hasLoop(s);){const e=s[s.length-1],t=s[s.length-2],n=this.getNodeNeighbors(e,o);let i,r=4*Math.PI;for(const o of n){const s=this.getRelativeAngle(e,t,o);s<r&&(r=s,i=o)}if(i||this.logAndThrow(new Error("Expected a neighbor")),e===s[0]&&i===s[1]){s.pop();break}s.push(i)}return n?s:[]}getNodeNeighbors(e,t){const o=new Set,s=this.getWallsForNode(e);s||this.logAndThrow(new Error("Expecting node to wall map to be valid"));for(const n of s.values())t&&!t.has(n)||o.add(n.getOtherNode(e));return o}hasLoop(e){if(0===e.length||e.length%100!=0)return!1;const t=[];for(let o=e.length-1;o>=0;o--){t.unshift(e[o]);const s=e.length-2*t.length;if(s<0)return!1;let n=!0;for(let o=0;o<t.length;o++)e[s+o]!==t[o]&&(n=!1);if(n)return this.logger.error("Found infinite loop!",t.map((e=>e.id)),"in sequence",e.map((e=>e.id))),!0}return!1}_createNode(e,t,o,s){const i=new l(o||(0,n._r)(),t,e.x,e.z,s||this.defaultLayerId);return this._nodes.set(i.id,i),this._addToDictHelper(t,i,this._nodesByFloor),this.insertIntoSpatialIndex(i),this._scheduleObserver({observers:this._nodes.addedObservers,param:i,type:ue.CREATE}),i}_updateNode(e,t){const o=this._nodes.get(e);o?(this.removeFromSpatialIndex(o),o.x=t.x,o.z=t.z,this.insertIntoSpatialIndex(o),this._scheduleUpdate(this._nodes,o)):this.logAndThrow(new Error("Attempted to update WallNode that does not exist"))}_deleteNode(e){const t=this._nodes.get(e);t?(this.getEdgeCountForNode(t)>0&&this.logAndThrow(new Error("Attempted to delete a node which has wall references still!")),this._nodes.delete(e),this._removeFromDictHelper(t.floorId,t,this._nodesByFloor),this.removeFromSpatialIndex(t),this._scheduleDelete(this._nodes,t)):this.logAndThrow(new Error("Attempted to delete WallNode that does not exist"))}_createRoom(e){this._validateRoomDetails(e.points,e.holesCW,e.walls,e.holes);const t=new s.JJ(e);this.rooms.set(t.id,t);for(const e of t.allWalls()){const o=this._wallToRoomMap.get(e)||new Set;o.add(t),this._wallToRoomMap.set(e,o)}return this._scheduleObserver({observers:this._rooms.addedObservers,param:t,type:ue.CREATE}),this.calculateRoomInsights(t),t}_updateRoom(e,t){this._validateRoomDetails(t.points,t.holesCW,t.walls,t.holes);const o=this._rooms.get(e);if(o){for(const e of o.allWalls()){const t=this._wallToRoomMap.get(e);t&&t.delete(o)}t.name=o.name,t.classifications=o.classifications?[...o.classifications]:[],this.rooms.set(e,t);for(const e of t.allWalls().values()){const o=this._wallToRoomMap.get(e)||new Set;o.add(t),this._wallToRoomMap.set(e,o)}this._scheduleUpdate(this._rooms,t),this.calculateRoomInsights(t)}else this.logAndThrow(new Error("Attempted to update Room that does not exist"))}_validateRoomDetails(e,t,o,s){const n=e[0].floorId;for(const o of e.concat(t.flat()))o.floorId!==n&&this.logAndThrow(new Error("Room nodes have mismatching floor ids!"))}logAndThrow(e){throw this.broadcast(new re.x(e)),e}_updateRoomDetails(e,t){const{name:o,roomTypeIds:s,location:n,includeInAreaCalc:i,hide:r,keywords:a,showDimensions:l,showHeight:d}=t,h=this.getRoom(e);h.name=null!=o?o:h.name,s&&(h.classifications=s.map((e=>({id:e,label:this.roomClassifications[e].label})))),h.location=null!=n?n:h.location,h.includeInAreaCalc=null!=i?i:h.includeInAreaCalc,h.hide=null!=r?r:h.hide,h.keywords=null!=a?a:h.keywords,h.showDimensions=null!=l?l:h.showDimensions,h.showHeight=null!=d?d:h.showHeight,this._scheduleUpdate(this._rooms,h)}_deleteRoom(e){const t=this._rooms.get(e);if(t){this._rooms.delete(t.id);for(const e of t.allWalls().values()){const o=this._wallToRoomMap.get(e);null==o||o.delete(t)}this._scheduleDelete(this._rooms,t)}else this.logAndThrow(new Error("Attempted to delete Room that does not exist"))}_createWall(e,t,o,s,r,a,l=(0,n._r)(),d=this.defaultLayerId){t.id===o.id&&this.logAndThrow(new Error("Cannot create wall where the from node is the same as the to node.")),t.floorId!==o.floorId&&this.logAndThrow(new Error("Cannot create a wall between nodes on different floors.")),this.hasWallBetween(t,o)&&this.logAndThrow(new Error("Wall already exists between start and end nodes.")),this._walls.has(l)&&this.logAndThrow(new Error("Wall already exists!"));const h=this.getNode(t.id),c=this.getNode(o.id),u=new i.c(l,d,e,h,c,s,a);this._walls.set(u.id,u),this._addToWallDicts(u),this._scheduleObserver({observers:this._walls.addedObservers,param:u,type:ue.CREATE});for(const e of r){const{type:t,relativePos:o,width:s,id:n,layerId:i}=e;this._createWallOpening(u,t,o,s,n,i)}return this.insertIntoSpatialIndex(u),u}_deleteWall(e){const t=this.getWall(e);for(const e of t.openings.slice())this._deleteWallOpening(e.id);this._walls.delete(e),this._removeFromWallDicts(t),this.removeFromSpatialIndex(t),this._scheduleDelete(this._walls,t)}_setEdgeProps(e,t){const{width:o,bias:s,type:n}=t,r=this.getWall(e);void 0!==n&&(r.type=n),void 0!==o&&(r.width=o,r.type===i.d.SOLID&&(this.lastSolidWallWidth=o)),void 0!==s&&(r.bias=s),this._scheduleUpdate(this._walls,r),this._updateDependentsForNodes(r.from,r.to)}_createWallOpening(e,t,o,s,i=(0,n._r)(),r=this.defaultLayerId){const a=new f.E(i,r,e.id,t,o,s);return this._wallOpenings.set(a.id,a),e.openings.push(a),this._scheduleObserver({observers:this._wallOpenings.addedObservers,param:a,type:ue.CREATE}),a}_setOpeningDetails(e,t){var o,s,n;const i=this.getOpening(e);i.type=null!==(o=t.type)&&void 0!==o?o:i.type,i.relativePos=null!==(s=t.relativePos)&&void 0!==s?s:i.relativePos,i.width=null!==(n=t.width)&&void 0!==n?n:i.width,this._scheduleUpdate(this._wallOpenings,i)}_deleteWallOpening(e){const t=this.getOpening(e),o=this.getWall(t.wallId),s=o.openings.findIndex((t=>t.id===e));-1!==s?o.openings.splice(s,1):this.logAndThrow(new Error("Expected opening to exist in wall array!")),this._wallOpenings.delete(e),this._scheduleDelete(this._wallOpenings,t)}_addToWallDicts(e){this._addToDictHelper(e.from,e,this._nodeToWallMap),this._addToDictHelper(e.to,e,this._nodeToWallMap),this._addToDictHelper(e.floorId,e,this._wallsByFloor),this._wallsByCompositeKey.set(i.c.getCompositeKey(e.from.id,e.to.id),e)}_addToDictHelper(e,t,o){let s=o.get(e);null!=s?s.add(t):(s=new Set,s.add(t),o.set(e,s))}_removeFromDictHelper(e,t,o){const s=o.get(e);null!=s&&(s.delete(t),0===s.size&&o.delete(e))}_removeFromWallDicts(e){this._removeFromDictHelper(e.from,e,this._nodeToWallMap),this._removeFromDictHelper(e.to,e,this._nodeToWallMap),this._removeFromDictHelper(e.floorId,e,this._wallsByFloor),this._wallToRoomMap.delete(e),this._wallsByCompositeKey.delete(i.c.getCompositeKey(e.from.id,e.to.id))}_scheduleObserver(e){this._observerQueue.push(e)}_scheduleUpdate(e,t,o=!1){this._observerQueue.find((o=>o.observers===e.addedObservers&&o.param===t))||this._scheduleObserver({observers:o?e.childUpdatedObservers:e.updatedObservers,param:t,type:ue.UPDATE})}_scheduleDelete(e,t){this._scheduleObserver({observers:e.deletedObservers,param:t,type:ue.DELETE}),this._deleteSet.add(t)}_flushObserverQueue(){const e=new Set;for(const t of this._observerQueue)if(t.type!==ue.UPDATE||!this._deleteSet.has(t.param))if(t.type===ue.CREATE&&this._deleteSet.has(t.param))e.add(t.param);else if(t.type!==ue.DELETE||!e.has(t.param))for(const e of t.observers){const o=t.param;e(o,o.id)}this._observerQueue.length=0,this._deleteSet.clear()}_triggerFinalizeObservers(){for(const e of this._afterFinalizeObservers.values())e()}getSnapshot(){const e={version:this.version,floors:{}},t=t=>{e.floors[t]||(e.floors[t]={edges:{},vertices:{},rooms:{}})};for(const[o,s]of this._nodes.data){t(s.floorId);e.floors[s.floorId].vertices[s.id]=s.getSnapshot()}for(const[o,s]of this._walls.data){t(s.floorId);e.floors[s.floorId].edges[s.id]=s.getSnapshot()}for(const[o,s]of this._rooms.data){t(s.floorId);e.floors[s.floorId].rooms[s.id]=s.getSnapshot()}return e}insertIntoSpatialIndex(e){if(this.isLoading)return;this.spatialIndex.has(e.floorId)||this.spatialIndex.set(e.floorId,new(_()));const t=this.spatialIndex.get(e.floorId);e.updateRBushBBox(),t.insert(e)}removeFromSpatialIndex(e){if(this.isLoading)return;const t=this.spatialIndex.get(e.floorId);null!=t&&t.remove(e)}_recomputeRooms(){return new j(this,{}).run(),this._observerQueue.length>0}calculateRoomInsights(e){this.calculateRoomArea(e),this.calculateRoomPerimeter(e),this.calculateRoomMeasurements(e),this.isLoading||this.calculateRoomHeight(e)}calculateRoomArea(e){const t=6*oe.Ue,o=new r.Vector3,s=new r.Vector3,n=(e,n,i,r)=>{const a=e[n],l=e[(n+1)%e.length],d=this.getWallForNodes(a.id,l.id).getEdgeWidth(a,l),h=i?(n+1)%e.length:n,c=(h+1)%e.length,u=(h+e.length-1)%e.length,g=this.getWallForNodes(e[u].id,e[h].id).getEdgeNormal(e[u],e[h]),p=this.getWallForNodes(e[h].id,e[c].id).getEdgeNormal(e[h],e[c]);if(o.copy(i?g:p),g.dot(p)>Math.cos(Math.PI-t)){s.addVectors(g,p).normalize();const e=s.dot(g);Math.abs(e)>1e-6&&(s.multiplyScalar(1/e),o.copy(s))}e[h].getVec3(r).addScaledVector(o,d)},i=new Array(new r.Vector3,new r.Vector3,new r.Vector3,new r.Vector3);i.push(i[0]);const a=e=>{let t=0;for(let o=0;o<e.length;o++){e[o].getVec3(i[0]),e[(o+1)%e.length].getVec3(i[1]),n(e,o,!0,i[2]),n(e,o,!1,i[3]);t+=(0,w.m)(i.map((e=>[e.x,e.z])))}return t},l=a(e.getCWPoints());let d=0;for(const t of e.holesCW)d+=(0,p.SV)(t),d+=a(t.slice().reverse());const h=(0,p.SV)(e.points);e.area=Math.max(h-l-d,0)}calculateRoomPerimeter(e){const t=[],o=e.wallsCCW.length;for(let s=0;s<o;s++){const n=e.wallsCCW[(s+o-1)%o],i=e.wallsCCW[s],r=e.wallsCCW[(s+1)%o],a=(0,ie.T)(i.wall,n.wall,r.wall,i.flipped);t.push({start:a.start.clone(),end:a.end.clone(),width:i.wall.width})}const s=[];s.push(t[0]);const n=new r.Vector3,i=new r.Vector3,a=(e,t)=>{const o=e.end.distanceTo(t.start)<.01;n.subVectors(e.start,e.end).normalize(),i.subVectors(t.end,t.start).normalize();const s=Math.abs(Math.abs(n.dot(i))-1)<.01;return o&&s},l=t.length;for(let e=1;e<l;e++){const o=t[(e+l-1)%l],n=t[e];if(a(o,n)){const e=s[s.length-1];e.end.copy(n.end),e.width=Math.max(e.width,n.width)}else s.push(n)}const d=s[0],h=s[s.length-1];a(h,d)&&(d.start.copy(h.start),d.width=Math.max(d.width,h.width),s.pop()),e.minimalInnerLoop=s;let c=0;for(const e of s)c+=e.start.distanceTo(e.end);e.perimeter=c}calculateRoomMeasurements(e){const{edges:t,thickness:o}=(0,p.B7)(e,this);let s=0,n=[new r.Vector2,new r.Vector2],i=[new r.Vector2,new r.Vector2],l=new r.Vector2(0,0);for(let e=0;e<2;e++){let d=0;for(let h=0;h<t.length;h++){const c=t[h],u=t[(h+1)%t.length];for(let g=0;g<t.length;g++){if(h===g)continue;const f=t[g],m=t[(g+1)%t.length],w=(0,p.bX)([c,u],new r.Vector2),y=(0,p.bX)([f,m],new r.Vector2);if(w.dot(y)>Math.cos(Math.PI-a.S2))continue;const _=(new r.Vector2).subVectors(w,y).normalize();if(e>0&&Math.abs(_.dot(l))>Math.cos(Math.PI/2-a.LW))continue;const O=(new r.Vector2).addVectors(f.getVec2(),m.getVec2()).multiplyScalar(.5);if((0,p.jH)(w,c.getVec2(),O)<0)continue;const I=new r.Vector2,W=new r.Vector2,v=(0,p.IQ)([c.getVec2().addScaledVector(w,o[h]),u.getVec2().addScaledVector(w,o[h])],[f.getVec2().addScaledVector(y,o[g]),m.getVec2().addScaledVector(y,o[g])],I,W);v>d&&(d=v,0===e?(n=[I,W],l=w,s=1):(i=[I,W],s=2))}}}if(2===s){const e=(n[1].y-n[0].y)/(n[1].x-n[0].x),t=(i[1].y-i[0].y)/(i[1].x-i[0].x);if(Math.abs(e)<Math.abs(t)){const e=n;n=i,i=e}}e.length=NaN,e.width=NaN,s>=2&&(e.length=(new r.Vector2).subVectors(n[0],n[1]).length(),e.l1=n[0].clone(),e.l2=n[1].clone(),e.width=(new r.Vector2).subVectors(i[0],i[1]).length(),e.w1=i[0].clone(),e.w2=i[1].clone(),e.isRectangularIsh()||(e.length=NaN,e.width=NaN))}calculateRoomHeight(e){if(!this.raycast||e.area<=1e-4)return;const t=(0,oe.Id)(10),o=.01;let s=0;const n=new r.Vector3,i=[],a=new r.Vector3,l=new r.Vector3,d=this.getRoomSamplePoints(e);for(const o of d){n.copy(o.position),n.y=1e5,s+=o.area;const r=this.raycast(n,e.floorId);let d=1e5,h=-1e5;for(const{face:e,point:t}of r){const o=null==e?void 0:e.normal;if(!o)continue;const s=o.dot(ae.fU.UP);s<0&&t.y>h&&(h=t.y,l.copy(o)),s>0&&t.y<d&&(d=t.y,a.copy(o))}if(d>=h)continue;const c=Math.acos(a.dot(ae.fU.UP)),u=Math.acos(l.dot(ae.fU.DOWN));if(c<t&&u<t){const e=(1-c/t)*(1-u/t)/(1/o.area);i.push({height:h-d,weight:e})}}if(i.length<10)return void(e.height=NaN);const h=i.reduce(((e,t)=>Math.max(e,t.height)),0),c=1+Math.floor((h+.005)/o),u=new Array(c).fill(0);for(const{height:e,weight:t}of i){u[Math.floor((e+.005)/o)]+=t}const g=new Array(c).fill(0);for(let e=0;e<u.length;e++)0===e?g[e]=(u[e]+u[e+1])/2:e===u.length-1?g[e]=(u[e]+u[e-1])/2:g[e]=(u[e-1]+u[e]+u[e+1])/3;const p=g.reduce(((e,t,o)=>g[e]<t?o:e),0)*o;let f=0;for(const{weight:e,height:t}of i)Math.abs(t-p)<.05&&(f+=e);f+=.5,e.height=p>0&&s>0&&f/s>=.3?p:NaN}*getRoomSamplePoints(e){const{points:t,faces:o}=e.getGeometry(),s=[];for(const e of o){const o=e.map((e=>new r.Vector3(t[e].x,0,t[e].y)));s.push(new r.Triangle(o[0],o[1],o[2]))}const n=s.reduce(((e,t)=>e+t.getArea()),0),i=(0,se.uZ)(1*n,10,200),a=new r.Vector3;for(const e of s){const t=e.getArea(),o=Math.ceil(i*(t/n)),s=Math.ceil(Math.log2(o)/2);for(let n=0;n<o;n++){const i=this.lowDiscrepancySampleTriangle(n,s);a.set(0,0,0),a.addScaledVector(e.a,i.x).addScaledVector(e.b,i.y).addScaledVector(e.c,i.z),yield{position:a,area:t/o}}}}}},32683:(e,t,o)=>{o.d(t,{Ci:()=>y,GS:()=>n,Hc:()=>_,JJ:()=>f,Lh:()=>s,Rd:()=>m});var s,n,i=o(4061),r=o.n(i),a=o(81396),l=o(46391),d=o(78283),h=o(62944),c=o(58353),u=o(16747),g=o(75668),p=o(19098);!function(e){e.INDOOR="indoor",e.OUTDOOR="outdoor"}(s||(s={})),function(e){e.INDOOR="indoor",e.OUTDOOR="outdoor",e.NON_AREA="nonArea",e.HIDE="hide"}(n||(n={}));class f{constructor(e){this.area=NaN,this.length=NaN,this.l1=new a.Vector2,this.l2=new a.Vector2,this.width=NaN,this.w1=new a.Vector2,this.w2=new a.Vector2,this.height=NaN,this.minimalInnerLoop=[],this.classifications=[],this.showDimensions=!0,this.showHeight=!0,this.wallsCCW=[],this.holeWallsCW=[],Object.assign(this,e),this.initCCWWalls()}getEntityAnalytic(){return"room"}get roomTypeIds(){return 0===this.classifications.length?[g.ub]:this.classifications.map((e=>e.id))}accessible(){return!(this.hide&&!this.includeInAreaCalc)}isOther(){const e=this.roomTypeIds;return 1===e.length&&e[0]===g.ub}get floorId(){return this.points[0].floorId}getSnapshot(){return{height:this.height,source:"frontend",vertices:this.points.map((e=>e.id)),layerId:this.layerId,holes:this.holesCW.map((e=>e.map((e=>e.id)))),label:this.name}}pointsMoved(){this._bbox=null,this._geometry=null}get bbox(){return this._bbox||(this._bbox=(new a.Box2).setFromPoints(this.points.map((e=>e.getVec2())))),this._bbox}getViewCenter(e=new a.Vector3,t=1){const o=this.points.map((e=>[e.x/t,e.z]));o.push([this.points[0].x/t,this.points[0].z]);const s=[o].concat(this.holesCW.map((e=>e.map((e=>[e.x/t,e.z])).reverse()))),n=r()(s,.1);return e.set(n[0]*t,0,n[1]),e}getArea(e){let t=this.area,o=1;return e===l.M.IMPERIAL?t=(0,d.Nv)(t):e===l.M.METRIC&&(o=10),Math.round(t*o)/o}getMeasurementText(e){return this.canDisplayDimensions()?[(0,h.up)(this.width,e),(0,h.up)(this.length,e)].join(` ${h.RQ} `):""}canDisplayDimensions(){return!(isNaN(this.length)||isNaN(this.width)||this.length<=0||this.width<=0||!this.showDimensions)}canDisplayHeight(){return!isNaN(this.height)&&this.showHeight}getPerimeterText(e){return(0,h.up)(this.perimeter,e)}getCWPoints(){return this.points.slice().reverse()}allWalls(){return(0,c.dW)(this.walls,...this.holes)}allKeywords(){const e=this.keywords.slice();return this.hide&&e.push(n.HIDE),this.includeInAreaCalc||e.push(n.NON_AREA),e.push(this.location===s.OUTDOOR?n.OUTDOOR:n.INDOOR),e}initCCWWalls(){const e=new Map;for(const t of this.allWalls())e.set(u.c.getCompositeKey(t.from.id,t.to.id),t);const t=t=>{const o=[],s=t.length;for(let n=0;n<s;n++){const i=t[n],r=t[(n+1)%s],a=u.c.getCompositeKey(i.id,r.id),l=e.get(a);if(l){const e=i.id===l.to.id;o.push({wall:l,flipped:e})}}return o};this.wallsCCW.length=0,this.wallsCCW.push(...t(this.points)),this.holeWallsCW.length=0;for(const e of this.holesCW)this.holeWallsCW.push(t(e))}isRectangularIsh(){if(isNaN(this.length)||isNaN(this.width))return!1;const e=(new a.Vector2).subVectors(this.l2,this.l1).normalize(),t=(new a.Vector2).subVectors(this.w2,this.w1).normalize();let o=Number.MAX_VALUE,s=Number.MAX_VALUE,n=-Number.MAX_VALUE,i=-Number.MAX_VALUE;const r=new a.Vector2,l=a=>{r.set(a.x,a.z);const l=e.dot(r),d=t.dot(r);o=Math.min(o,l),s=Math.min(s,d),n=Math.max(n,l),i=Math.max(i,d)};for(const e of this.minimalInnerLoop)l(e.start),l(e.end);const d=(i-s)*(n-o);return this.length*this.width/d>.6}getGeometry(){if(!this._geometry){const e=this.points.map((e=>e.getVec2())),t=this.holesCW.map((e=>e.map((e=>e.getVec2())))),o=a.ShapeUtils.triangulateShape(e,t);this._geometry={faces:o,points:e.concat(t.flat(1))}}return this._geometry}getInnerLoops(){const e=[],t=t=>{e.push([]);const o=t.length;for(let s=0;s<o;s++){const n=t[(s+o-1)%o],i=t[s],r=t[(s+1)%o],a=(0,p.T)(i.wall,n.wall,r.wall,i.flipped);e[e.length-1].push(a.start.clone())}};t(this.wallsCCW);for(const e of this.holeWallsCW)t(e);return e}}function m(e,t){var o;return e.length>0&&(null!==(o=e[0].defaultKeywords)&&void 0!==o?o:[]).includes(t)}function w(e){return e.filter((e=>!Object.values(n).includes(e)))}function y(e){return{location:e.includes(n.OUTDOOR)?s.OUTDOOR:s.INDOOR,includeInAreaCalc:!e.includes(n.NON_AREA),hide:e.includes(n.HIDE),keywords:w(e)}}function _(e){const t=new Set;for(const o of e)(0,c.Jt)(t,o.defaultKeywords||[]);return t.has(n.INDOOR)&&t.has(n.OUTDOOR)&&t.delete(n.OUTDOOR),Array.from(t.values())}},16747:(e,t,o)=>{o.d(t,{c:()=>l,d:()=>s});var s,n=o(81396),i=o(5823),r=o(75668),a=o(2569);!function(e){e.SOLID="solid-wall",e.DIVIDER="invisible-wall"}(s||(s={}));class l{constructor(e,t,o,s,i,r,l){this.bias=.5,this.openings=[],this._t1=new n.Vector3,this._t2=new n.Vector3,this._rbushBbox={minX:0,minY:0,maxX:1,maxY:1},this.overlapsCircle=(()=>{const e=new n.Vector3,t=new n.Vector3,o=new n.Vector3,s=new n.Vector3,i=new n.Vector3;return(n,r)=>{const a=this.getBiasAdjustmentVec(e),l=this.from.getVec3(t).add(a),d=this.to.getVec3(o).add(a),h=s.subVectors(d,l),c=h.length(),u=i.subVectors(n,l),g=u.length(),p=h.dot(u)/c,f=Math.sqrt(Math.max(g*g-p*p,0));return f<=this.width/2+r&&p>=-r&&p<=c+r?Math.min(p,f):null}})(),this.overlapsLine=(()=>{const e=new n.Vector3,t=new n.Vector3,o=new n.Vector3,s=new n.Vector3,i=new n.Vector3,r=new n.Vector3,l=new n.Vector3,d=new n.Vector3,h=new n.Vector3,c=1e-4;function u(e,t,o){return h.subVectors(t,e).dot(o)/o.lengthSq()}return(n,h)=>{const g=this.getBiasAdjustmentVec(e),p=this.from.getVec3(t).add(g),f=this.to.getVec3(o).add(g),m=s.subVectors(f,p),w=r.copy(m).normalize(),y=i.subVectors(h,n),_=l.copy(y).normalize(),O=w.dot(_);if(Math.abs(Math.abs(O)-1)<c){const e=d.subVectors(n,p),t=e.length(),o=e.dot(w);if(Math.sqrt(Math.abs(t*t-o*o))<.05){const e=c,t=.9999,o=u(n,p,y),s=u(n,f,y),i=o>t&&s>t;if(!(o<e&&s<e)&&!i)return[{entity:this.from,t:o},{entity:this.to,t:s}].sort(((e,t)=>e.t-t.t))}}else{const e=(0,a._B)(n.x,n.z,h.x,h.z,p.x,p.z,f.x,f.z,c);if(null!=e)return[{entity:this,t:e}]}return[]}})(),this.id=e,this.layerId=t,this.type=o,this.from=s,this.to=i,this.width=r,this.bias=l}static getCompositeKey(e,t){const o=[e,t].sort();return`${o[0]}:${o[1]}`}getEntityAnalytic(){return this.type}get floorId(){return this.from.floorId}get compositeKey(){return l.getCompositeKey(this.from.id,this.to.id)}getOtherNode(e){if(e===this.from)return this.to;if(e===this.to)return this.from;throw new Error("WallNode does not belong to edge.")}hasNodes(e,t){const{from:o,to:s}=this;return!(e!==o&&e!==s||t!==o&&t!==s)}getBiasAdjustmentVec(e=new n.Vector3){this.getNormal(e);const t=(this.bias-.5)*this.width;return e.multiplyScalar(t),e}getNormal(e=new n.Vector3){return this.getDirection(e),e.normalize(),e.set(-e.z,0,e.x),e}getDirection(e){const t=e||new n.Vector3;return t.set(this.to.x-this.from.x,0,this.to.z-this.from.z),t}getEdgeWidth(e,t){const o=this.from===e&&this.to===t?this.bias:1-this.bias;return this.width*o}getEdgeNormal(e,t,o=new n.Vector3){return this.getNormal(o),this.from===e&&this.to===t||o.multiplyScalar(-1),o}getLine3(e){const t=e||new n.Line3;return this.from.getVec3(t.start),this.to.getVec3(t.end),t}getProjection(e){const t=this._t1.copy(e).sub(this.from.getVec3(this._t2)),o=this.getDirection(this._t2);return t.dot(o)/o.length()}getSnapshot(){const e={thickness:this.width,vertices:[this.from.id,this.to.id],type:this.type===s.SOLID?i.Pb.WALL:i.Pb.INVISIBLE,layerId:this.layerId};if(this.openings.length){const t={};for(const e of this.openings)t[e.id]=e.getSnapshot();e.openings=t}return e}getViewCenter(e=new n.Vector3){return e.addVectors(this.to.getVec3(),this.from.getVec3()).multiplyScalar(.5)}get length(){return this.from.distanceTo(this.to)}get minX(){return this._rbushBbox.minX}get maxX(){return this._rbushBbox.maxX}get minY(){return this._rbushBbox.minY}get maxY(){return this._rbushBbox.maxY}updateRBushBBox(){this._rbushBbox={minX:Math.min(this.from.x,this.to.x)-2*this.width-r.dt,minY:Math.min(this.from.z,this.to.z)-2*this.width-r.dt,maxX:Math.max(this.from.x,this.to.x)+2*this.width+r.dt,maxY:Math.max(this.from.z,this.to.z)+2*this.width+r.dt}}clone(){const e=new l(this.id,this.layerId,this.type,this.from,this.to,this.width,this.bias);return e.openings.push(...this.openings),e}}},80978:(e,t,o)=>{o.d(t,{E:()=>i,u:()=>s});var s,n=o(81396);!function(e){e.DOOR="doorway",e.OPENING="opening"}(s||(s={}));class i{constructor(e,t,o,s,n,i){this.id=e,this.layerId=t,this.wallId=o,this.type=s,this.relativePos=n,this.width=i}getEntityAnalytic(){return this.type}get floorId(){return""}getViewCenter(e=new n.Vector3){return e.set(0,0,0)}getSnapshot(){const{type:e,relativePos:t,width:o,layerId:s}=this;return{lowerElevation:0,height:0,type:e,relativePos:t,width:o,layerId:s}}}},65241:(e,t,o)=>{o.d(t,{$:()=>i,x:()=>n});var s=o(98010);class n extends s.v0{constructor(e){super(),this.error=e}}class i extends s.v0{constructor(e,t=(new Error).stack){super(),this.message=e,this.stack=t}}},19098:(e,t,o)=>{o.d(t,{T:()=>d,b:()=>l});var s=o(90304),n=o(81396),i=o(2569),r=o(69505);class a{constructor(){this.primary=new n.Vector3,this.bevel=new n.Vector3}set(e,t,o){this.primary.set(e,t,o),this.bevel.set(e,t,o)}copy(e,t){this.primary.copy(e),t?this.bevel.copy(t):this.bevel.copy(e)}}const l=(()=>{const e={fromLeft:new a,fromRight:new a,toLeft:new a,toRight:new a};return(t,o)=>{u(t,e);const s=o.getWallNeighbors(t,"from"),n=o.getWallNeighbors(t,"to");return s&&(h(t,s.left,"from","left",e.fromLeft),h(t,s.right,"from","right",e.fromRight)),n&&(h(t,n.left,"to","left",e.toLeft),h(t,n.right,"to","right",e.toRight)),e}})(),d=(()=>{const e={start:new n.Vector3,end:new n.Vector3},t={fromLeft:new a,fromRight:new a,toLeft:new a,toRight:new a};return(o,s,n,i)=>{let r,a,l,d,c;return i?(r="right",a="to",l="from",d=t.toRight,c=t.fromRight):(r="left",a="from",l="to",d=t.fromLeft,c=t.toLeft),u(o,t),h(o,s,a,r,d),h(o,n,l,r,c),e.start.copy(d.primary),e.end.copy(c.primary),e}})(),h=(()=>{const e=new n.Vector3,t=new n.Vector3,o=new n.Vector3,a=new n.Vector3,l=new n.Vector3,d=new n.Vector3,h=new n.Vector3,u=new n.Vector2,p=new n.Vector2,f=new n.Vector2,m=new n.Vector3,w=new n.Vector3;return(n,y,_,O,I)=>{const W=n.getDirection(e).normalize(),v=n[_],b="from"===_?-1:1,N=y.getOtherNode(v).getVec3(t).sub(v.getVec3(o)).normalize().multiplyScalar(b),S=W.dot(N);if(Math.acos(S)*r.MN<5)return;n.getBiasAdjustmentVec(m),y.getBiasAdjustmentVec(w);const R="left"===O?Math.PI/2:-Math.PI/2,x=W.applyAxisAngle(s.fU.UP,R).multiplyScalar(n.width/2).add(m),E=N.applyAxisAngle(s.fU.UP,R).multiplyScalar(y.width/2).add(w);n.from.getVec3(a).add(x),n.to.getVec3(l).add(x),y.from.getVec3(d).add(E),y.to.getVec3(h).add(E);if((0,i.cB)(a.x,a.z,l.x,l.z,d.x,d.z,h.x,h.z,u)){const e=c(v,n,y,u,f),t=e.x>0&&e.y>0,o=e.x<0&&e.y<0;if(e.x<n.length&&e.y<y.length)if(t)I.set(u.x,0,u.y);else{p.set(v.x+m.x+w.x,v.z+m.z+w.z).distanceTo(u)>1.2*Math.max(n.width,y.width)&&o?g(v,n,y,x,E,I):I.set(u.x,0,u.y)}}}})(),c=(()=>{const e=new n.Vector3,t=new n.Vector3,o=new n.Vector3,s=new n.Vector3;return(n,i,r,a,l)=>{let d=i.getOtherNode(n);return n.getVec3(o),d.getVec3(e).sub(o),d=r.getOtherNode(n),d.getVec3(t).sub(o),s.set(a.x,0,a.y).sub(o),l.set(e.dot(s)/e.length(),t.dot(s)/t.length()),l}})(),u=(()=>{const e=new n.Vector3,t=new n.Vector3,o=new n.Vector3,s=new n.Vector3;return(n,i)=>{const r=n.width;n.getBiasAdjustmentVec(o),n.getNormal(e).multiplyScalar(-r/2).add(o),n.getNormal(t).multiplyScalar(r/2).add(o);const a=n.from.getVec3(s).add(t);i.fromRight.copy(a);const l=n.to.getVec3(s).add(t);i.toRight.copy(l);const d=n.to.getVec3(s).add(e);i.toLeft.copy(d);const h=n.from.getVec3(s).add(e);i.fromLeft.copy(h)}})(),g=(()=>{const e=new n.Vector3,t=new n.Vector3,o=new n.Vector3,s=new n.Vector3,i=new n.Vector3;return(n,r,a,l,d,h)=>{const c=r.getOtherNode(n),u=a.getOtherNode(n);n.getVec3(t),c.getVec3(e).sub(t).normalize().multiplyScalar(-r.width/2),u.getVec3(o).sub(t).normalize().multiplyScalar(-a.width/2),s.copy(t).add(l).add(e),i.copy(t).add(d).add(o),h.copy(s,i)}})()},84784:(e,t,o)=>{o.d(t,{LN:()=>r,Nw:()=>a,ZJ:()=>d});var s=o(38399),n=o(75668);const i={[n.Vm]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.BALCONY,[n.YN]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.BASEMENT,[n.yn]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.BATHROOM,[n.sc]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.BEDROOM,[n.xi]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.BONUS_ROOM,[n.e$]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.CLOSET,[n.gk]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.DEN,[n.O4]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.DINING_ROOM,[n.y5]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.DRIVEWAY,[n.jt]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.ENTRANCE,[n.tI]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.EXERCISE_ROOM,[n.Zd]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.EXTERIOR,[n.yg]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.FACADE,[n.KT]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.FAMILY_ROOM,[n.x1]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.GAME_ROOM,[n.kA]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.GARAGE,[n.v6]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.GARDEN,[n.QM]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.GREAT_ROOM,[n.HT]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.GYM,[n.xu]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.HALLWAY,[n.Z2]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.KITCHEN,[n.aj]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.LAUNDRY,[n.mN]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.LIBRARY,[n.I3]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.LIVING_ROOM,[n.er]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.LOFT,[n.Nk]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.MEDIA_ROOM,[n.no]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.OFFICE,[n.ub]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.OTHER,[n.CF]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.PANTRY,[n.oF]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.PATIO,[n.qH]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.POOL,[n.pM]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.SAUNA,[n.a6]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.STAIRCASE,[n.X$]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.UTILITY_ROOM,[n.Os]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.VOID,[n.DQ]:s.Z.WORKSHOP.LABEL_SUGGESTIONS.WORKSHOP_ROOM};function r(e,t,o){const n=t.t(s.Z.SHOWCASE.ROOMS.DEFAULT_NAME);return o&&e?o.getRoomLabel(e,n):n}function a(e,t){const{id:o,label:i}=e;if(!o)return i||t.t(s.Z.WORKSHOP.LABEL_SUGGESTIONS.OTHER);return o.indexOf(n.Rt)>-1?o.split(n.Rt).map((e=>t.t(l({id:o,label:i})))).join(n.X9):t.t(l({id:o,label:i}))}function l(e){const{id:t,label:o}=e,n=i[t];return n||(o||s.Z.WORKSHOP.LABEL_SUGGESTIONS.OTHER)}function d(e){if(e.length<2)return e;for(const t of n.Ds)if(t.length===e.length&&t.every((t=>-1!==e.indexOf(t))))return t.slice();return e}},75668:(e,t,o)=>{o.d(t,{CF:()=>B,DQ:()=>Z,Ds:()=>X,HT:()=>E,I3:()=>L,KT:()=>b,LW:()=>i,Nk:()=>M,O4:()=>y,Os:()=>K,Oz:()=>a,QM:()=>x,Rt:()=>h,S2:()=>n,Vm:()=>c,X$:()=>H,X9:()=>d,YN:()=>u,Z2:()=>V,Zd:()=>I,a6:()=>G,aj:()=>T,dt:()=>r,e$:()=>m,er:()=>C,gk:()=>w,jt:()=>O,kA:()=>S,kM:()=>l,mN:()=>z,no:()=>D,oF:()=>U,pM:()=>k,qH:()=>F,sc:()=>p,tI:()=>W,ub:()=>P,v6:()=>R,x1:()=>N,xi:()=>f,xu:()=>A,y5:()=>_,yg:()=>v,yn:()=>g});var s=o(69505);const n=6*s.Ue,i=6*s.Ue,r=.01,a=.1,l=.02,d=", ",h="-",c="ge0tcan3iztta5mtgsfx99xpb",u="4by2d70b54srbin43t5m0yh9c",g="ua1m0u8pm3yngrhzwec1ue5zc",p="pnf0xzq61xcegack4iai7xppd",f="y3xuprmywgrprnm0273u3pphd",m="zp189tx2hdrry8k5nhaq5x45d",w="4gp8x21ni4i8y19r9pm4rb35a",y="00258k0mqu8750du151be805c",_="91naa1k3hiim9z4mk8352enta",O="rmu8pikcye0n7n9fsu5e4acda",I="cqig2yw0p8q99t5n02w1kf40b",W="66psa4tqgazbnffr9huy2dnpd",v="4qq4e5hz84hcur7ry87uhtmua",b="mx6e2yfk0tg0zbkbzuw5mhkda",N="0gnuunpwx63dmmqeq64aqfiyd",S="izt39gytcbymc0qtd37nrb7xb",R="ei3kmtun2xpspbg3d5ff3t3zc",x="xwhhz63a0hw735qf81knp63ha",E="ywtddu9hagraxhznwinn8t4zb",A="yeh1aa32fwgm72bh0g92ebruc",V="zgh2304w5anu0t25ia06ytfxd",T="pr9h4zy7eak91f3b7un97u42c",z="gqqw1zac1hzg1p8622ducg3zb",L="w4tdggk5i5u2zpiryugktghgb",C="e6md2spaqemc448pmau3azkhd",M="a11d2gi43h0etwedw0mx9pgwd",D="ag5uphha72sak65qazgsp09yd",P="byp7pz2tmt4ff47nhfx0uwzec",B="2nx6maxxpaeut7kx96fuacdzc",U="n07hhs9gky4cgrh7q65s9rp0d",F="s1h0hb8tpcc9i35dmupknsega",k="gba8cq0s2rdfg1zdb1epwty7d",G="0an2rky24gb91fdnpzmbwadib",H="qa21tqcsinnxde9gdmiphacza",K="k4ww3sk359ntmwgr8s75hq22a",Z="nqw75004dcg2bcr41047rhg6c",X=[[g,T],[V,y],[V,L],[V,L,y],[L,y]]},15004:(e,t,o)=>{o.d(t,{B7:()=>c,IQ:()=>f,OH:()=>O,SV:()=>d,bX:()=>u,jH:()=>g,mX:()=>_,xS:()=>h});var s=o(43517),n=o(5696),i=o(81396),r=o(75182),a=o(69505),l=o(75668);function d(e,t=!1){if(0===e.length)return 0;const o=e.map((e=>[e.x,e.z]));return o.push(o[0]),(0,s.m)(o,t)}function h(e){return d(e,!0)>0&&e.reverse(),e}const c=(e,t)=>{const o=e.getCWPoints(),s=o.length,n={edges:[],thickness:[]};for(let e=0;e<s;e++){const i=o[(e+s-1)%s],r=o[e],a=o[(e+1)%s],l=t.getWallForNodes(i.id,r.id).getEdgeWidth(i,r),d=t.getWallForNodes(r.id,a.id).getEdgeWidth(r,a);(!w([i,r],[r,a],!1)||Math.abs(d-l)>.01)&&(n.edges.push(o[e]),n.thickness.push(d))}return n},u=(e,t)=>{t.set(e[1].x-e[0].x,e[1].z-e[0].z).normalize();const o=t.x,s=t.y;return t.set(-s,o),t},g=(()=>{const e=new i.Vector2;return(t,o,s)=>(e.subVectors(s,o),t.dot(e))})(),p=(()=>{const e=new i.Vector2,t=new i.Vector2,o=new i.Vector2;return(s,n,i,r)=>(e.subVectors(i,n),t.copy(s).multiplyScalar(s.dot(e)),o.subVectors(e,t),r.addVectors(n,o),r)})(),f=(()=>{const e=new i.Vector2,t=new i.Vector2,o=new i.Vector2,s=new i.Vector2,n=new i.Vector2;return(i,r,a,l)=>{e.subVectors(i[1],i[0]).normalize(),t.subVectors(r[1],r[0]).normalize(),o.subVectors(e,t).normalize();const d=g(o,i[0],i[1]);let h=g(o,i[0],r[0]),c=g(o,i[0],r[1]);if(c<h){const e=h;h=c,c=e}const u=Math.min(d,c)-Math.max(0,h);s.subVectors(e,t).multiplyScalar(.5).multiplyScalar(u/2+Math.max(0,h)),a.copy(i[0]).add(s);const f=p(o,i[0],r[0],n);return l.copy(f).add(s),u}})(),m=(()=>{const e=new i.Vector2,t=new i.Vector2,o=Math.cos(5*a.Ue);return(s,n,i)=>{e.set(s[1].x-s[0].x,s[1].z-s[0].z).normalize(),t.set(n[1].x-n[0].x,n[1].z-n[0].z).normalize();const r=e.dot(t);return r>=o||i&&r<=-o}})(),w=(()=>{const e=new i.Vector2;return(t,o,s)=>{if(!m(t,o,s))return!1;e.set(-(o[1].z-t[0].z),o[1].x-t[0].x).normalize();return!(Math.abs(g(e,t[0].getVec2(),t[1].getVec2()))>.05)}})();function y(e){return`room-${e}`}function _(e){const t=l.ub;return 0===e.length?y(t):e.length>1?y("multi-use"):r.g.includes(y(e[0]))?y(e[0]):y(t)}function O(e,t){const[[o,s],[i,r]]=e,[[a,l],[d,h]]=t,c=((0,n.s8)(e[0],t)?1:0)+((0,n.s8)(e[1],t)?1:0)+((0,n.s8)(t[0],e)?1:0)+((0,n.s8)(t[1],e)?1:0),u=(h-l)*(i-o)-(d-a)*(r-s);if(Math.abs(u)<1e-4)return c>2;const g=s-l,p=o-a,f=((d-a)*g-(h-l)*p)/u,m=((i-o)*g-(r-s)*p)/u,w=1e-8,y=.99999999;return f>w&&f<y&&m>w&&m<y}},94046:(e,t,o)=>{o.d(t,{i:()=>n});var s=o(81396);class n extends s.Mesh{}},81248:(e,t,o)=>{o.d(t,{Bv:()=>u,Dv:()=>l,TE:()=>d,l0:()=>c,o7:()=>h});var s=o(81396);const n=-1,i=10,r=5,a=-5,l=(e,t=n)=>o=>e.distanceToSquared(o.position)*t,d=(e,t=n)=>o=>e.distanceTo(o.position)*t,h=(e,t,o=i)=>{const n=new s.Vector3;return s=>n.copy(s.position).sub(e).normalize().dot(t)*o},c=(e,t=n)=>o=>e.distanceToSquared(o.floorPosition)*t,u=(e,t=r,o=a)=>s=>e===s.floorId?t:o},87928:(e,t,o)=>{o.d(t,{E:()=>n});var s=o(81396);class n extends s.Mesh{constructor(e,t){super(e,t)}}},69877:(e,t,o)=>{function s(e,t){let o,s,n=t[t.length-1],i=0;for(let r=0;r<t.length;++r)o=n,n=t[r],o.y<=e.y&&n.y<=e.y||o.y>e.y&&n.y>e.y||o.x<e.x&&n.x<e.x||(s=(e.y-o.y)*(n.x-o.x)-(e.x-o.x)*(n.y-o.y),n.y<o.y&&(s=-s),i+=s>0?1:0);return i%2==1}o.d(t,{L:()=>s})},58353:(e,t,o)=>{function s(e,t){return e.size===t.size&&[...e].every((e=>t.has(e)))}function n(...e){const t=new Set;for(const o of e)for(const e of o.values())t.add(e);return t}function i(e,t){for(const o of t)e.add(o);return e}o.d(t,{Jt:()=>i,dW:()=>n,nb:()=>s})},75182:(e,t,o)=>{o.d(t,{g:()=>n});var s=o(81346);const n=Object.keys(s.f).sort(((e,t)=>e.localeCompare(t)))}}]);