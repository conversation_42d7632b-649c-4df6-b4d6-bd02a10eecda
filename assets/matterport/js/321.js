/*! For license information please see 321.js.LICENSE.txt */
(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[321],{92011:(e,t)=>{"use strict";var i=t.binary_mesh={};i.read=function(e,t){return e.readFields(i._readField,{chunk:[],quantized_chunk:[]},t)},i._readField=function(e,t,i){1===e?t.chunk.push(r.read(i,i.readVarint()+i.pos)):2===e&&t.quantized_chunk.push(l.read(i,i.readVarint()+i.pos))},i.write=function(e,t){if(e.chunk)for(var i=0;i<e.chunk.length;i++)t.writeMessage(1,r.write,e.chunk[i]);if(e.quantized_chunk)for(i=0;i<e.quantized_chunk.length;i++)t.writeMessage(2,l.write,e.quantized_chunk[i])};var s=t.vertices_simple={};s.read=function(e,t){return e.readFields(s._readField,{xyz:[],uv:[]},t)},s._readField=function(e,t,i){1===e?i.readPackedFloat(t.xyz):2===e&&i.readPackedFloat(t.uv)},s.write=function(e,t){e.xyz&&t.writePackedFloat(1,e.xyz),e.uv&&t.writePackedFloat(2,e.uv)};var o=t.faces_simple={};o.read=function(e,t){return e.readFields(o._readField,{faces:[]},t)},o._readField=function(e,t,i){1===e&&i.readPackedVarint(t.faces)},o.write=function(e,t){e.faces&&t.writePackedVarint(1,e.faces)};var r=t.chunk_simple={};r.read=function(e,t){return e.readFields(r._readField,{vertices:null,faces:null,chunk_name:"",material_name:""},t)},r._readField=function(e,t,i){1===e?t.vertices=s.read(i,i.readVarint()+i.pos):2===e?t.faces=o.read(i,i.readVarint()+i.pos):3===e?t.chunk_name=i.readString():4===e&&(t.material_name=i.readString())},r.write=function(e,t){e.vertices&&t.writeMessage(1,s.write,e.vertices),e.faces&&t.writeMessage(2,o.write,e.faces),e.chunk_name&&t.writeStringField(3,e.chunk_name),e.material_name&&t.writeStringField(4,e.material_name)};var n=t.vertices_quantized={};n.read=function(e,t){return e.readFields(n._readField,{quantization:0,translation:[],x:[],y:[],z:[]},t)},n._readField=function(e,t,i){1===e?t.quantization=i.readFloat():2===e?i.readPackedFloat(t.translation):3===e?i.readPackedSVarint(t.x):4===e?i.readPackedSVarint(t.y):5===e&&i.readPackedSVarint(t.z)},n.write=function(e,t){e.quantization&&t.writeFloatField(1,e.quantization),e.translation&&t.writePackedFloat(2,e.translation),e.x&&t.writePackedSVarint(3,e.x),e.y&&t.writePackedSVarint(4,e.y),e.z&&t.writePackedSVarint(5,e.z)};var a=t.uv_quantized={};a.read=function(e,t){return e.readFields(a._readField,{name:"",quantization:0,u:[],v:[]},t)},a._readField=function(e,t,i){1===e?t.name=i.readString():2===e?t.quantization=i.readFloat():3===e?i.readPackedSVarint(t.u):4===e&&i.readPackedSVarint(t.v)},a.write=function(e,t){e.name&&t.writeStringField(1,e.name),e.quantization&&t.writeFloatField(2,e.quantization),e.u&&t.writePackedSVarint(3,e.u),e.v&&t.writePackedSVarint(4,e.v)};var h=t.faces_compressed={};h.read=function(e,t){return e.readFields(h._readField,{faces:[]},t)},h._readField=function(e,t,i){1===e&&i.readPackedSVarint(t.faces)},h.write=function(e,t){e.faces&&t.writePackedSVarint(1,e.faces)};var l=t.chunk_quantized={};l.read=function(e,t){return e.readFields(l._readField,{chunk_name:"",material_name:"",vertices:null,uvs:[],faces:null},t)},l._readField=function(e,t,i){1===e?t.chunk_name=i.readString():2===e?t.material_name=i.readString():3===e?t.vertices=n.read(i,i.readVarint()+i.pos):4===e?t.uvs.push(a.read(i,i.readVarint()+i.pos)):5===e&&(t.faces=o.read(i,i.readVarint()+i.pos))},l.write=function(e,t){if(e.chunk_name&&t.writeStringField(1,e.chunk_name),e.material_name&&t.writeStringField(2,e.material_name),e.vertices&&t.writeMessage(3,n.write,e.vertices),e.uvs)for(var i=0;i<e.uvs.length;i++)t.writeMessage(4,a.write,e.uvs[i]);e.faces&&t.writeMessage(5,o.write,e.faces)}},17734:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>S});var s=i(97542),o=i(92810),r=i(54244),n=i(76532),a=i(53954),h=i(23254),l=i(92294),d=i(64773),c=i(18923),u=i(66563),m=i(14630),p=i(96154),g=i(5090),f=i(10699),y=i(23885),w=i(57053),v=i(99935),b=i(80626),M=i(24048),T=i(95882),D=i(5135);class x extends s.Y{constructor(){super(...arguments),this.name="mesh-quality",this.measurementModeData=null,this.updateMaxQuality=(()=>{let e,t,i;return({modeChange:s,criticalChange:o,interactionChange:r})=>{void 0!==o&&(t=o),void 0!==s&&(e=s),void 0!==r&&(i=r);const n=i===w.s.VrOrientOnly||i===w.s.VrWithController||i===w.s.VrWithTrackedController,a=this.modelMeshModule.stats(),h=a.textureCount<=this.config.textureLODThreshold?Math.max(v.S.ULTRA,this.config.maxQuality):v.S.MEDIUM,l=Math.max(h,e===T.Ey.Panorama&&0===this.meshData.meshTextureOpacity.value?h:this.config.maxQuality);this.modelMeshModule.setTextureLimits(h,l);const d=this.viewmodeData.currentMode!==T.Ey.Dollhouse&&this.viewmodeData.currentMode!==T.Ey.Floorplan,c=this.viewmodeData.transition.active&&(0,T.Bw)(this.viewmodeData.transition.to);n||c||(a.streaming||t)&&d?this.modelMeshModule.setTextureStreamMode(b.l.NONE):this.modelMeshModule.setTextureStreamMode(this.config.textureLOD)}})()}async init(e,t){this.config=e,this.engine=t,[this.modelMeshModule,this.meshData,this.viewmodeData,this.sweepData,this.appData,this.interactionModeData]=await Promise.all([t.getModuleBySymbol(o.Ve),t.market.waitForData(n._),t.market.waitForData(h.O),t.market.waitForData(a.Z),t.market.waitForData(r.pu),t.market.waitForData(D.Z)]),this.bindAppEventsToTextureQuality(),this.bindAppEventsToTextureVisibility(),this.updateMaxQuality({}),this.showcaseMeshDetailRules()}bindAppEventsToTextureQuality(){let e=0;this.bindings.push(this.meshData.onChanged((t=>{t.meshTextureOpacity.value!==e&&this.updateMaxQuality({}),e=t.meshTextureOpacity.value})),this.appData.onPhase((()=>{this.updateMaxQuality({})})),this.engine.subscribe(g.Z,(e=>{this.updateMaxQuality({criticalChange:!0,modeChange:e.toMode})})),this.engine.subscribe(f.Z,(e=>{this.updateMaxQuality({criticalChange:!1,modeChange:e.toMode})})),this.engine.subscribe(p.oR,(()=>{this.updateMaxQuality({criticalChange:!0})})),this.engine.subscribe(p.NR,(()=>{this.updateMaxQuality({criticalChange:!1})})),this.engine.subscribe(c.Z,(()=>{this.updateMaxQuality({criticalChange:!0})})),this.engine.subscribe(u.Z,(()=>{this.updateMaxQuality({criticalChange:!1})})),this.engine.subscribe(d.m,(e=>{this.updateMaxQuality({interactionChange:e.mode})})))}showcaseMeshDetailRules(){const{modelMeshModule:e,log:t}=this,i=()=>this.appData.phase<=this.appData.phases.LOADING,s=()=>this.appData.phase===this.appData.phases.STARTING,o=()=>this.appData.phase>=this.appData.phases.PLAYING&&this.appData.phase!==this.appData.phases.ERROR,r=()=>this.appData.phase===this.appData.phases.ERROR,n=e=>this.viewmodeData.closestMode===e,a=()=>n(T.Ey.Panorama),h=()=>this.viewmodeData.transition.active,d=e=>h()&&this.viewmodeData.transition.to===e,c=()=>{var e,t;return null!==(t=null===(e=this.measurementModeData)||void 0===e?void 0:e.isEditingOrCreating())&&void 0!==t&&t},u=()=>this.interactionModeData.isVR(),m=()=>void 0!==this.sweepData.currentAlignedSweepObject;function p(){const l=e.getMeshDetail();let p=l;o()?h()?(d(T.Ey.Dollhouse)||d(T.Ey.Floorplan)||d(T.Ey.Mesh))&&(p="max"):(p="default",a()&&u()&&(p="minimal"),a()&&!m()&&(p="minimal"),a()&&c()&&(p="max"),(n(T.Ey.Dollhouse)||n(T.Ey.Floorplan)||n(T.Ey.Mesh))&&(p="max")):(i()&&(p="minimal"),r()&&(p="minimal"),s()&&(p="default")),l!==p&&(t.debug(`overrideMaxDetail from ${l} to ${p}`),e.setMeshOptions({overrideMaxDetail:p}))}this.bindings.push(this.appData.onPhase(p),this.viewmodeData.onChanged(p),this.meshData.meshTextureOpacity.onComplete(p),this.interactionModeData.onChanged(p)),this.engine.market.waitForData(l.X).then((e=>{this.bindings.push(e.onPhaseChanged(p)),this.measurementModeData=e})),p()}bindAppEventsToTextureVisibility(){this.bindings.push(this.engine.subscribe(g.Z,(e=>{this.updateRenderMode()})),this.engine.subscribe(m.Z,(e=>{const t=this.sweepData.isSweepUnaligned(e.toSweep);t!==this.sweepData.isSweepUnaligned(e.fromSweep)&&this.modelMeshModule.setRenderMode(t?M.k.PanoramaCube:M.k.PanoramaMesh)})))}updateRenderMode(){const e=this.viewmodeData.currentMode===T.Ey.Panorama||this.viewmodeData.transition.to===T.Ey.Panorama,t=e?M.k.PanoramaMesh:M.k.Mesh,i=e?y.Q9:y.w2;this.modelMeshModule.setRenderMode(t,i)}}const S=x},10059:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>p});var s=i(92810),o=i(89553),r=i(32597),n=i(41326),a=i(97542),h=i(6667),l=i(23254),d=i(95882),c=i(34029),u=i(53203),m=i(9037);class p extends a.Y{constructor(){super(...arguments),this.name="showcase-hotkeys",this.inputCommandMap={[h.M.PRESSED]:{[r.R.ONE]:()=>(this.viewmodeData.currentMode===d.Ey.Panorama&&(this.settings.setProperty(u.Lp,!1),this.currentView=0),this.switchToMode(n.BD.INSIDE)),[r.R.TWO]:()=>this.switchToMode(n.BD.DOLLHOUSE),[r.R.THREE]:()=>this.switchToMode(n.BD.FLOORPLAN),[r.R.ZERO]:()=>{const e=this.currentView++%this.meshViews.length;return this.meshViews[e]()}}},this.currentView=0,this.meshViews=[()=>(this.settings.setProperty(u.Lp,!1),this.switchToMode(n.BD.MESH)),()=>(this.settings.setProperty(u.Lp,!0),this.switchToMode(n.BD.MESH)),()=>(this.settings.setProperty(u.Lp,!0),this.switchToMode(n.BD.INSIDE)),()=>(this.settings.setProperty(u.Lp,!1),this.switchToMode(n.BD.INSIDE))]}async init(e,t){const i=await t.getModuleBySymbol(s.PZ);[this.viewmodeData,this.settings,this.cameraData]=await Promise.all([t.market.waitForData(l.O),t.market.waitForData(c.e),t.market.waitForData(m.M)]),this.issueCommand=t.commandBinder.issueCommand.bind(t.commandBinder),i.registerHandler(o.e,(async e=>{this.inputCommandMap[e.state]&&this.inputCommandMap[e.state][e.key]&&await this.inputCommandMap[e.state][e.key]()}))}async switchToMode(e){const{currentMode:t}=this.viewmodeData;if(t!==d.Ey.Transition){const i=(0,d.Bw)(t)&&(e===n.BD.MESH||e===n.BD.INSIDE)?this.cameraData.pose.rotation:void 0;try{await this.issueCommand(new n._i(e,void 0,{rotation:i}))}catch(e){this.log.debug("Unable to switchToMode",e)}}}}},94292:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>F});var s=i(97542),o=i(61864),r=i(92810),n=i(34029),a=i(59625),h=i(14715),l=i(64918),d=i(17788),c=i(97998),u=i(46391),m=i(17686),p=i(28361),g=i(5823);const f=new c.Z("mds-player-options-deserializer"),y={[g.Y6.LEFT]:m.kw.Left,[g.Y6.RIGHT]:m.kw.Right,[g.Y6.AUTO]:m.kw.Auto},w={[g.rq.BLACK]:p.z.black,[g.rq.GREY]:p.z.grey,[g.rq.WHITE]:p.z.white};class v{deserialize(e){var t;if(!e||!this.validate(e))return f.debug("Deserialized invalid options data from MDS",e),null;const i=(null===(t=e.publication)||void 0===t?void 0:t.options)||{},s=e.options||{},o=(null==s?void 0:s.tourPanDirection)?y[s.tourPanDirection]:m.kw.Auto,r={address:i.address,contact_email:i.contactEmail,contact_name:i.contactName,contact_phone:i.contactPhone,dollhouse:s.dollhouseEnabled,external_url:i.externalUrl,floor_plan:s.floorplanEnabled,floor_select:s.floorSelectEnabled,highlight_reel:s.highlightReelEnabled,labels:s.labelsEnabled,labels_dh:s.dollhouseLabelsEnabled,model_name:i.modelName,model_summary:i.modelSummary,presented_by:i.presentedBy,measurements:s.measurements!==g.XR.DISABLED,measurements_saved:s.measurements===g.XR.MEASUREANDVIEW,room_bounds:s.roomBoundsEnabled,unit_type:s.unitType===g.nL.IMPERIAL?u.M.IMPERIAL:u.M.METRIC,background_color:(null==s?void 0:s.backgroundColor)?w[s.backgroundColor]:p.z.black,tour_buttons:s.tourButtonsEnabled,fast_transitions:s.tourFastTransitionsEnabled,transition_speed:s.tourTransitionSpeed,transition_time:s.tourTransitionTime,pan_speed:s.tourPanSpeed,dollhouse_pan_speed:s.tourDollhousePanSpeed,zoom_duration:s.tourZoomDuration,pan_angle:s.tourPanAngle,pan_direction:o,space_search:s.spaceSearchEnabled};return new a.af(r)}validate(e){if(!e)return!1;return["id","options","publication"].every((t=>t in e))}}var b=i(18448);const M=(0,b.S)(y),T=(0,b.S)(w);class D{serialize(e){const t={},i=e.options||{};if(void 0!==i.dollhouse&&(t.dollhouseOverride=i.dollhouse?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.floor_plan&&(t.floorplanOverride=i.floor_plan?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.floor_select&&(t.floorSelectOverride=i.floor_select?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.room_bounds&&(t.roomBoundsOverride=i.room_bounds?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.space_search&&(t.spaceSearchOverride=i.space_search?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.labels&&(t.labelsOverride=i.labels?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.labels_dh&&(t.dollhouseLabelsOverride=i.labels_dh?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.highlight_reel&&(t.highlightReelOverride=i.highlight_reel?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.background_color&&(t.backgroundColor={set:T[i.background_color]||g.rq.BLACK}),void 0!==i.unit_type){const e=i.unit_type===u.M.METRIC?g.nL.METRIC:g.nL.IMPERIAL;t.unitType={set:e}}if(void 0!==i.measurements||void 0!==i.measurements_saved){const e=i.measurements&&i.measurements_saved?g.XR.MEASUREANDVIEW:i.measurements?g.XR.MEASURE:g.XR.DISABLED;t.measurements={set:e}}void 0!==i.tour_buttons&&(t.tourButtonsOverride=i.tour_buttons?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.fast_transitions&&(t.tourFastTransitionsOverride=i.fast_transitions?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.pan_angle&&(t.tourPanAngle={set:i.pan_angle}),void 0!==i.pan_direction&&(t.tourPanDirection={set:M[i.pan_direction]}),void 0!==i.pan_speed&&(t.tourPanSpeed={set:i.pan_speed}),void 0!==i.transition_speed&&(t.tourTransitionSpeed={set:i.transition_speed}),void 0!==i.transition_time&&(t.tourTransitionTime={set:i.transition_time}),void 0!==i.zoom_duration&&(t.tourZoomDuration={set:i.zoom_duration}),void 0!==i.dollhouse_pan_speed&&(t.tourDollhousePanSpeed={set:i.dollhouse_pan_speed});const s={};void 0!==i.presented_by&&(s.presentedByOverride=i.presented_by?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.contact_email&&(s.contactEmailOverride=i.contact_email?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.contact_name&&(s.contactNameOverride=i.contact_name?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.contact_phone&&(s.contactPhoneOverride=i.contact_phone?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.external_url&&(s.externalUrlOverride=i.external_url?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.model_name&&(s.modelNameOverride=i.model_name?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.model_summary&&(s.modelSummaryOverride=i.model_summary?g.J1.ENABLED:g.J1.DISABLED),void 0!==i.address&&(s.addressOverride=i.address?g.J1.ENABLED:g.J1.DISABLED);return{options:t,publication:Object.keys(s).length>0?{options:s}:void 0}}}var x=i(64927);const S=new c.Z("mds-player-options-store");class C extends d.u{constructor(){super(...arguments),this.serializer=new D,this.deserializer=new v,this.prefetchKey="data.model.publication.options"}async read(e={}){const t={modelId:this.getViewId()};return this.query(x.GetModelOptions,t,e).then((e=>{var t;return this.deserializer.deserialize(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.model)}))}async update(e){const t=this.getViewId(),i=this.serializer.serialize(e);if(0===Object.keys(i).length)throw new Error("No data to update?");return this.mutate(x.PatchModel,{modelId:t,patch:i}).then((e=>{S.debug(e)}))}}var P=i(86090),O=i(2541),k=i(79728),B=i(635),A=i(80742);class F extends s.Y{constructor(){super(...arguments),this.name="showcase-settings",this.updateTransitionType=(e,t,i)=>{const s=e.options.fast_transitions?l.n.FadeToBlack:l.n.Interpolate;t.hasProperty(h.gj)?t.setProperty(h.gj,s):i.registerSetting("player_options",h.gj,s)}}async init(e,t){const{baseModelId:i,readonly:s,baseUrl:h}=e;this.engine=t;const l=await t.market.waitForData(A.R);this.store=new C({context:l.mdsContext,readonly:s,baseUrl:h,viewId:i});const[d,c,u,m]=await Promise.all([t.getModuleBySymbol(o.Ak),t.market.waitForData(n.e),t.getModuleBySymbol(r.Lx),this.store.read()]);this.playerOptionsData=m||new a.af,t.market.register(this,a.af,this.playerOptionsData);for(const e in a.gx){const t=a.gx[e],i=this.playerOptionsData.options[t];if(d.hasProperty(t)){const e=d.getProperty(t);d.updateSetting(t,i&&e),this.log.debug(`Updated player_options setting ${t} = ${i&&e}`)}else d.registerSetting("player_options",t,i),this.log.debug(`Registered player_options setting ${t} = ${i}`)}this.updateTransitionType(this.playerOptionsData,c,d),this.bindings.push(c.onPropertyChanged(a.gx.InstantTransitions,(()=>this.updateTransitionType(this.playerOptionsData,c,d)))),s||(this.bindings.push(u.onSave((()=>this.save()),{dataType:k.g.SETTINGS})),this.monitor=new P.u(this.playerOptionsData,{aggregationType:O.E.NextFrame},this.engine),this.monitor.onChanged((()=>this.engine.commandBinder.issueCommand(new B.V({dataTypes:[k.g.SETTINGS]})))))}async save(){if(!this.store||!this.monitor)return void this.log.warn("Settings changes will NOT be saved");const e=this.monitor.getDiffRecord(),t=e.options;return t&&(t&&void 0===t.measurements&&void 0!==t.measurements_saved&&(t.measurements=this.playerOptionsData.options.measurements),t&&void 0!==t.measurements&&void 0===t.measurements_saved&&(t.measurements_saved=this.playerOptionsData.options.measurements_saved),void 0!==(null==t?void 0:t.floor_select)&&(t.floor_select=this.playerOptionsData.options.floor_select),void 0!==(null==t?void 0:t.labels_dh)&&(t.labels_dh=this.playerOptionsData.options.labels_dh),void 0===t.presented_by&&void 0===t.contact_email&&void 0===t.contact_name&&void 0===t.contact_phone&&void 0===t.external_url&&void 0===t.model_name&&void 0===t.model_summary&&void 0===t.address||(t.presented_by=this.playerOptionsData.options.presented_by,t.contact_email=this.playerOptionsData.options.contact_email,t.contact_name=this.playerOptionsData.options.contact_name,t.contact_phone=this.playerOptionsData.options.contact_phone,t.external_url=this.playerOptionsData.options.external_url,t.model_name=this.playerOptionsData.options.model_name,t.model_summary=this.playerOptionsData.options.model_summary,t.address=this.playerOptionsData.options.address)),this.monitor.clearDiffRecord(),this.store.update(e)}}},32170:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>S});var s=i(81396),o=i(97542),r=i(92810),n=i(53954),a=i(95882),h=i(55450),l=i(65019),d=i(55318),c=i(64918),u=i(63252),m=i(39880),p=i(34029),g=i(90197),f=i(2569),y=i(46950),w=i(33324),v=i(40505),b=i(44237),M=i(28022),T=i(54244),D=i(61681),x=i(59625);class S extends o.Y{constructor(){super(...arguments),this.name="showcase-start",this.firstRenderPromise=new y.Q,this.flyInPromise=new y.Q,this.handleFlyIn=async(e,t,i)=>{this.renderer.startRender(!1);try{await this.doStandardStart(e,t,i,!0)}catch(e){this.log.error(e),this.renderer.startRender(!0)}}}async init(e,t){const[i,s,o]=await Promise.all([t.market.waitForData(p.e),t.market.waitForData(d.O),t.market.waitForData(x.af)]);[this.sweepData,this.sweepModule,this.cameraModule,this.renderer,this.appData]=await Promise.all([t.market.waitForData(n.Z),t.getModuleBySymbol(r.l),t.getModuleBySymbol(r.kg),t.getModuleBySymbol(r.Aj),t.market.waitForData(T.pu)]);const a=i.tryGetProperty("quickstart",!1),h=this.getStartingPose(s,o);return(a?this.doQuickStart(h,t,c.n.Instant):this.doStandardStart(h,t,i,!1)).catch((e=>{this.log.error("Handling error during initial fly-in, attempting recover",{startingPose:h,error:e}),this.doQuickStart(h,t,c.n.FadeToBlack),this.renderBeforeStarting(t)})).finally((()=>this.flyInPromise.resolve())),this.bindings.push(t.commandBinder.addBinding(g.b,(async e=>{this.handleFlyIn(e.pose||s.getStartingPose(),t,i)})),t.commandBinder.addBinding(g.Q,(async e=>{this.fadeToStartLocation(s,t)})),t.subscribe(M.YB,(async()=>{this.appData.application!==T.Mx.WORKSHOP&&s.moveCameraOnViewChange&&this.fadeToStartLocation(s,t)}))),this.waitForFirstRender}getStartingPose(e,t){const i=e.getStartingPose(),s=!t.options.dollhouse,o=!t.options.floor_plan;return s&&i.mode===a.Ey.Dollhouse||o&&i.mode===a.Ey.Floorplan?new d.B:i}fadeToStartLocation(e,t){const i=e.getStartingPose();this.doQuickStart(i,t,c.n.FadeToBlack)}get waitForFirstRender(){return this.firstRenderPromise.nativePromise()}get waitForFlyin(){return this.flyInPromise.nativePromise()}async doQuickStart(e,t,i){const s=this.sweepData.getStartSweep(e),o=s&&s.id,n=await t.getModuleBySymbol(r.XT);return await n.switchToMode(e.mode,i,{sweepID:o,position:e.camera.position||s&&s.position,rotation:e.camera.rotation,zoom:-1!==e.camera.zoom?e.camera.zoom:void 0}),t.broadcast(new v.bN(!1)),e.mode!==a.Ey.Dollhouse&&e.mode!==a.Ey.Floorplan||e.floorVisibility&&await t.commandBinder.issueCommandWhenBound(new w.h9(e.floorVisibility.lastIndexOf(1),!0,0)),this.renderBeforeStarting(t)}async doStandardStart(e,t,i,o){var n;const[d]=await Promise.all([t.getModuleBySymbol(r.XT)]);await t.getModuleBySymbol(D.My);const u=!i.tryGetProperty(l.wY,!1),p=!i.tryGetProperty(l.dF,!1);e&&(e.mode===a.Ey.Dollhouse&&u||e.mode===a.Ey.Floorplan&&p||e.mode===a.Ey.Panorama&&!e.pano)&&(e=null);const g=!e||e&&(e.mode===a.Ey.Dollhouse||e.mode===a.Ey.Floorplan),y=!e||e&&!this.is360Pano(e)&&(0,a.Bw)(e.mode)&&!u,M=e?e.mode:a.Ey.Panorama,T=this.sweepData.getStartSweep(e),x=T&&T.id,S=this.sweepData.getFirstSweep();let C=S&&S.rotation;if((null===(n=null==e?void 0:e.camera)||void 0===n?void 0:n.rotation)&&!(0,b.mB)(e.camera.rotation)&&(C=e.camera.rotation),C&&M!==a.Ey.Floorplan&&(C=(0,f.Z)(C)),y){await t.getModuleBySymbol(r.Ve);const i=d.getFlyinEndPose({sweepID:x,position:e?e.camera.position:void 0,rotation:C}),n=d.getFlyinStartPose(i,o?new s.Vector3(0,0,0):void 0);t.broadcast(new v.bN(!0));const l=x?this.sweepModule.activateSweepUnsafe({sweepId:x}):Promise.resolve();await d.switchToMode(a.Ey.Dollhouse,c.n.Instant,n),await this.renderBeforeStarting(t),await(0,m.gw)(750),await this.cameraModule.moveTo({transitionType:c.n.Interpolate,pose:i}).nativePromise(),await l,t.broadcast(new v.bN(!1)),await d.switchToMode(M,c.n.Interpolate,{sweepID:x,rotation:C},h.DEFAULT_TRANSITION_TIME)}else await d.switchToMode(M,c.n.Instant,{sweepID:x,position:e?e.camera.position:void 0,rotation:C,zoom:e&&-1!==e.camera.zoom?e.camera.zoom:void 0}),g&&e&&(e.floorVisibility?await t.commandBinder.issueCommandWhenBound(new w.h9(e.floorVisibility.lastIndexOf(1),!0,0)):await t.commandBinder.issueCommandWhenBound(new w.Vw(null))),await this.renderBeforeStarting(t)}is360Pano(e){if(e.pano.uuid){const t=this.sweepData.getSweep(e.pano.uuid);if(t)return t.alignmentType!==u.z9.ALIGNED}else{const e=this.sweepData.getFirstSweep();if(void 0!==e)return e.alignmentType!==u.z9.ALIGNED}return!1}async renderBeforeStarting(e){await this.renderer.renderOnce(),await e.getModuleBySymbol(D.$1),this.renderer.startRender(!0),this.firstRenderPromise.resolve()}}},66211:(e,t,i)=>{"use strict";i.d(t,{Z:()=>o});var s=i(97542);class o extends s.Y{constructor(){super(...arguments),this.name="base-controls",this.inputBindings=[]}registerActiveStateChangeBinding(){this.bindings.push(this.controls.onActiveStateChanged((()=>this.onActiveStateChanged())))}updateInputBindings(){this.controls.isActive?this.inputBindings.forEach((e=>e.renew())):this.inputBindings.forEach((e=>e.cancel()))}onActiveStateChanged(){this.controls.stop(),this.updateInputBindings()}dispose(e){super.dispose(e);for(const e of this.inputBindings)e.cancel();this.inputBindings=[]}}},41835:(e,t,i)=>{"use strict";i.d(t,{Z:()=>o});var s=i(37082);class o{constructor(){this.poseControllerObservable=(0,s.Y)(null)}get poseController(){return this.poseControllerObservable.value}setController(e){return this.poseControllerObservable.value=e,this}get isActive(){return null!=this.poseController}onActiveStateChanged(e){return this.poseControllerObservable.onChanged(e)}}},86384:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n});var s=i(97542),o=i(23254),r=i(92810);class n extends s.Y{constructor(){super(...arguments),this.name="common-controls",this.modeControls=new Map,this.poseController=null}async init(e,t){this.modeControls=new Map,t.market.waitForData(o.O).then((e=>{this.viewmodeData=e,this.bindings.push(this.viewmodeData.onChanged((()=>this.setControllerForCurrViewmode()))),this.setControllerForCurrViewmode()})),this.cameraPoseProxy=(await t.getModuleBySymbol(r.kg)).cameraPoseProxy,this.cameraPoseProxy.newSession(this)}onAccessGranted(e){this.poseController=e,this.setControllerForCurrViewmode(),this.bindings.forEach((e=>e.renew()))}onAccessRevoked(e){this.stop(),this.bindings.forEach((e=>e.cancel())),this.poseController=null;for(const e of this.modeControls.values())e.controls.setController(null)}startRotateTransition(e,t,i=!0){return this.checkControlsForAction((s=>s.startRotateTransition(e,t,i)))}startZoomTransition(e,t,i=!0){return this.checkControlsForAction((s=>s.startZoomTransition(e,t,i)))}startTranslateTransition(e,t,i=!0){return this.checkControlsForAction((s=>s.startTranslateTransition(e,t,i)))}stop(){return this.checkControlsForAction((e=>(e.stop(),Promise.resolve())))}setControllerForCurrViewmode(){var e;if(this.viewmodeData&&this.viewmodeData.currentMode){const t=null===(e=this.modeControls.get(this.viewmodeData.currentMode))||void 0===e?void 0:e.controls;if(t){t.setController(this.poseController);for(const e of this.modeControls.values()){const i=e.controls;i!==t&&i.setController(null)}}}}checkControlsForAction(e){if(this.viewmodeData&&null!==this.viewmodeData.currentMode){const t=this.modeControls.get(this.viewmodeData.currentMode);if(t){return e(t.controls)}}return Promise.reject("checkControlsForAction() -> Current view mode is null")}addControls(e,t,i){this.modeControls.get(e)&&!i||(this.modeControls.set(e,{mode:e,controls:t}),this.setControllerForCurrViewmode())}}},78403:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>X});var s=i(92810),o=i(90304),r=i(12250),n=i(80592),a=i(89553),h=i(32597),l=i(6667),d=i(95882),c=i(69505),u=i(81396),m=i(96955),p=i(16782),g=i(21646),f=i(41835),y=i(49827),w=i(46950),v=i(64918);const b=1e3/60,M=89*c.Ue,T=90*c.Ue,D=.001*c.Ue,x=10*c.Ue,S=20*c.Ue,C=30*c.Ue,P=Math.PI/1e3;var O,k=i(47047),B=i(59370);!function(e){e[e.NONE=0]="NONE",e[e.ROTATE=1]="ROTATE",e[e.PAN=2]="PAN",e[e.ZOOM=3]="ZOOM"}(O||(O={}));const A={[p.r.NONE]:O.NONE,[p.r.PRIMARY]:O.ROTATE,[p.r.SECONDARY]:O.PAN,[p.r.MIDDLE]:O.ZOOM,[p.r.BACK]:O.NONE,[p.r.FORWARD]:O.NONE,[p.r.ALL]:O.NONE},F={[p.r.NONE]:O.NONE,[p.r.PRIMARY]:O.PAN,[p.r.SECONDARY]:O.ROTATE,[p.r.MIDDLE]:O.ZOOM,[p.r.BACK]:O.NONE,[p.r.FORWARD]:O.NONE,[p.r.ALL]:O.NONE};var E;!function(e){e[e.NONE=0]="NONE",e[e.MOUSE=1]="MOUSE",e[e.KEYBOARD=2]="KEYBOARD",e[e.TOUCH=3]="TOUCH"}(E||(E={}));class R{constructor(e,t,i,s,r,n){this.poseProxy=e,this.poseConstrainer=t,this.getRayPoint=i,this.getFocusPoint=s,this.invalidateParentOrbitCache=r,this.movingCamera=!1,this.smoothedTouch0=new u.Vector2,this.smoothedTouch1=new u.Vector2,this.origTouchAxis=new u.Vector2,this.origCenterPt=new u.Vector2,this.plane=new u.Plane(o.fU.UP.clone(),0),this.focusPlane=new u.Plane(o.fU.UP.clone(),0),this.originalPinchLength=0,this.pointersMoved=(()=>{const e=new u.Vector2,t=new u.Vector3,i=new u.Vector3,s=new u.Vector2,r=new k.B(1),n=new u.Ray,a=new u.Vector3;return h=>{if(!this.poseController||!this.movingCamera)return;r.copy(this.poseProxy.pose);this.smoothedTouch0.lerp(h[0].position,.2),this.smoothedTouch1.lerp(h[1].position,.2),s.copy(this.smoothedTouch1).sub(this.smoothedTouch0).normalize();const l=this.origTouchAxis.angle()-s.angle();t.copy(this.initialPose.fovCorrectedPosition());const d=new u.Vector3(-this.intersectionPt.x,0,-this.intersectionPt.z);t.add(d),t.applyAxisAngle(o.fU.UP,l),d.negate(),t.add(d);const c=(new u.Quaternion).copy(this.initialPose.rotation),m=(new u.Quaternion).setFromAxisAngle(o.fU.UP,l);c.premultiply(m);const p=this.smoothedTouch0.clone().lerp(this.smoothedTouch1,.5),g=new u.Vector3;if(i.copy(o.fU.FORWARD).applyQuaternion(c),this.castFrom(p.x,p.y,g),isNaN(g.x))return void(this.movingCamera=!1);g.sub(this.intersectionPt),g.negate(),g.applyAxisAngle(o.fU.UP,l);const f=1-1/(e.subVectors(this.smoothedTouch0,this.smoothedTouch1).length()/this.originalPinchLength),y=t.add(g).lerp(this.intersectionPt,f).clone();if(n.set(y,i),n.intersectPlane(this.focusPlane,a)){const e=a.distanceTo(y);r.position.copy(y),r.rotation.copy(c),r.focalDistance=e,r.resetProjMatrix(),r.applyPhiBasedFovSquish(),this.poseConstrainer.constrain(r),this.poseController.updateCameraPose(r),this.invalidateParentOrbitCache()}}})(),this.initMove=(()=>{const e=new u.Vector2,t=new u.Ray;return i=>{const s=i[0].position,r=i[1].position;this.smoothedTouch0.copy(s),this.smoothedTouch1.copy(r),this.originalPinchLength=e.subVectors(s,r).length(),this.origTouchAxis.copy(r).sub(s).normalize(),this.origCenterPt.copy(s).lerp(r,.5);const{pose:n}=this.poseProxy;this.initialPose=n.clone();const a=new u.Vector3,h=new u.Vector3;(0,B.Kh)(this.initialPose,a.set(this.origCenterPt.x,this.origCenterPt.y,-1),a),(0,B.Kh)(this.initialPose,h.set(this.origCenterPt.x,this.origCenterPt.y,1),h),h.sub(a).normalize(),t.set(a,h),this.intersectionPt=this.getRayPoint(t);const l=this.getFocusPoint();n.focalDistance=n.position.distanceTo(l),this.initialPose.focalDistance=n.position.distanceTo(l),this.poseConstrainer.setStartPose(this.initialPose);const d=n.phi()<S?n.forward().clone().multiplyScalar(-1):o.fU.UP;this.plane.setFromNormalAndCoplanarPoint(d,this.intersectionPt),this.focusPlane.setFromNormalAndCoplanarPoint(d,l)}})(),this.castFrom=(()=>{const e=new u.Ray,t=new u.Vector3,i=new u.Vector3;return(s,o,r)=>{(0,B.Kh)(this.initialPose,t.set(s,o,-1),t),(0,B.Kh)(this.initialPose,i.set(s,o,1),i),i.sub(t).normalize(),e.set(t,i),e.intersectPlane(this.plane,r)&&!r.equals(t)||(r.x=NaN)}})(),n.onGrabStart((e=>{this.movingCamera=!0,this.initMove(e.pointers)})),n.onGrabEnd((()=>{this.movingCamera=!1}))}isMovingCamera(){return this.movingCamera}rawPointerUpdate(e){this.pointersMoved(e.pointers)}setController(e){e?this.poseController=e:(this.poseController=void 0,this.movingCamera=!1)}}var I=i(93411),V=i(35895);var _;!function(e){e.NONE="none",e.PITCH="pitch",e.GRAB="grab"}(_||(_={}));class L{constructor(){this.t1=new u.Vector2,this.t2=new u.Vector2,this.initT1=new u.Vector2,this.initT2=new u.Vector2,this.twoFingerEventCt=0,this.currGesture=_.NONE,this.contiguousGrabEventCt=0,this.grabStartObservers=new Set,this.grabEndObservers=new Set,this.pitchStartObservers=new Set,this.pitchEndObservers=new Set,this.gesturePointerIds=[null,null]}rawPointerUpdate(e){e.pointers.length>=2?(0===this.twoFingerEventCt?(this.t1.copy(e.pointers[0].clientPosition),this.t2.copy(e.pointers[1].clientPosition),this.initT1.copy(e.pointers[0].clientPosition),this.initT2.copy(e.pointers[1].clientPosition),this.gestureStartPointers=e,this.gesturePointerIds[0]=e.pointers[0].id,this.gesturePointerIds[1]=e.pointers[1].id,this.setCurrGesureAndNotifyObservers(_.GRAB)):(this.t1.lerp(e.pointers[0].clientPosition,.2),this.t2.lerp(e.pointers[1].clientPosition,.2),(this.currGesture===_.NONE||this.currGesture===_.GRAB&&this.contiguousGrabEventCt<180)&&this.setCurrGesureAndNotifyObservers(this.nextGesture())),this.twoFingerEventCt++):(this.twoFingerEventCt=0,this.setCurrGesureAndNotifyObservers(_.NONE)),this.currGesture===_.GRAB&&this.contiguousGrabEventCt++}onGrabStart(e){return(0,V.k1)((()=>this.grabStartObservers.add(e)),(()=>this.grabStartObservers.delete(e)),!0)}onGrabEnd(e){return(0,V.k1)((()=>this.grabEndObservers.add(e)),(()=>this.grabEndObservers.delete(e)),!0)}onPitchStart(e){return(0,V.k1)((()=>this.pitchStartObservers.add(e)),(()=>this.pitchStartObservers.delete(e)),!0)}onPitchEnd(e){return(0,V.k1)((()=>this.pitchEndObservers.add(e)),(()=>this.pitchEndObservers.delete(e)),!0)}setCurrGesureAndNotifyObservers(e){const t=this.currGesture;if(this.currGesture=e,t===_.NONE)e===_.GRAB?this.notifyGrabStartObservers():e===_.PITCH&&this.notifyPitchStartObservers();else if(t===_.GRAB)e===_.PITCH?(this.notifyGrabEndObservers(),this.notifyPitchStartObservers()):e===_.NONE&&this.notifyGrabEndObservers();else if(t===_.PITCH)if(e===_.NONE)this.notifyPitchEndObservers();else if(e===_.GRAB)throw new Error("Gesture recognizer should never switch from PITCH -> GRAB")}notifyGrabStartObservers(){for(const e of this.grabStartObservers)e(this.gestureStartPointers)}notifyPitchStartObservers(){for(const e of this.pitchStartObservers)e(this.gestureStartPointers)}notifyGrabEndObservers(){this.contiguousGrabEventCt=0;for(const e of this.grabEndObservers)e()}notifyPitchEndObservers(){for(const e of this.pitchEndObservers)e()}nextGesture(){if(this.twoFingerEventCt>10){const e=this.t1.y-this.initT1.y,t=this.t2.y-this.initT2.y,i=this.t1.x-this.initT1.x,s=this.t2.x-this.initT2.x,o=Math.abs(e/i),r=Math.abs(t/s);if(Math.abs(e)>1&&Math.abs(t)>1&&o>3&&r>3&&Math.sign(e)===Math.sign(t))return _.PITCH}return this.twoFingerEventCt>0?_.GRAB:_.NONE}}var N=i(31362),U=i(76587);const z=g.Z.epsilon,G=T;class H extends f.Z{constructor(e,t,i,s,r,n,a){super(),this.cameraPoseProxy=e,this.poseConstrainer=t,this.getOrbitPoint=i,this.getGrabPoint=s,this.constrolsData=n,this.messageBus=a,this.tempOrientation=new u.Quaternion,this.tempAxis=new u.Vector3,this.nextPosition=new u.Vector3,this.nextOrientation=new u.Quaternion,this.currentPose=new k.B(1),this.positionDelta=new u.Vector3,this.angularAccel=new u.Vector2,this.angularVelocity=new u.Vector2,this.linearAccel=new u.Vector2,this.linearVelocity=new u.Vector2,this.grabPlane=(new u.Plane).setFromNormalAndCoplanarPoint(o.fU.UP,o.fU.ZERO),this.grabPt=new u.Vector3,this.ndcPos=new u.Vector2,this.zoomAccel=0,this.zoomVelocity=0,this.orbitPoint=new u.Vector3,this.orbitDistance=0,this.orbitPlane=new u.Plane,this.needsOrbitDataInit=!0,this.autoOrbitStartPhi=0,this.aimTarget=new u.Vector3,this.currentPhiLowerLimit=D,this.currentPhiUpperLimit=M,this.activeAction=O.NONE,this.activeDevice=E.NONE,this.touchGestureRecognizer=new L,this.isTouchPitching=!1,this.prevTouchY=0,this.zoomDirection=new u.Vector3,this.castFromNdc=(()=>{const e=new u.Vector3,t=new u.Vector3(0,0,-1),i=new u.Ray(e,t),s=new u.Vector3;return()=>{(0,B.Kh)(this.grabCameraPose,e.set(this.ndcPos.x,this.ndcPos.y,-1),e),(0,B.Kh)(this.grabCameraPose,t.set(this.ndcPos.x,this.ndcPos.y,1),t),t.sub(e).normalize(),i.set(e,t);return i.intersectPlane(this.grabPlane,s)}})(),this.dampedZoomDir=(()=>{const e=new u.Vector3,t=new u.Quaternion,i=new u.Vector3,s=new u.Vector3,o=new u.Quaternion;return(r=b)=>{const n=this.cameraPoseProxy.pose,a=n.forward().clone(),h=this.getGrabPoint(),l=h.clone().sub(this.cameraPoseProxy.pose.fovCorrectedPosition()).normalize(),d=this.poseConstrainer.modelBoundingBox.containsPoint(h)&&n.phi()>x?l:a,u=o.angleTo(n.rotation)>1e-8;if(o.copy(n.rotation),e.length()<.1||u)e.copy(d),this.zoomDirection.copy(d);else{const o=e.angleTo(d);s.crossVectors(e,d);const n=(0,c.Id)(1*r),a=Math.min(o,n);t.setFromAxisAngle(s,a);const h=i.copy(e).applyQuaternion(t);e.copy(h),this.zoomDirection.copy(h)}}})(),this.touchControls=new R(e,t,r,i,(()=>this.invalidateOrbitMetadata()),this.touchGestureRecognizer),e.pose.autoOrtho&&(this.currentPhiUpperLimit=T),this.transition={active:!1,startTime:0,elapsed:0,duration:0,angularVelocity:new u.Vector2,linearVelocity:new u.Vector2,zoomVelocity:0,easeOut:!1},this.touchGestureRecognizer.onGrabStart((()=>{this.messageBus.broadcast(new U.O),this.stopAndClearState()})),this.touchGestureRecognizer.onGrabEnd((()=>this.stopAndClearState())),this.touchGestureRecognizer.onPitchStart((e=>{this.isTouchPitching=!0,this.prevTouchY=(e.pointers[0].position.y+e.pointers[1].position.y)/2,this.initMove(O.ROTATE,E.MOUSE)})),this.touchGestureRecognizer.onPitchEnd((()=>{this.isTouchPitching=!1,this.endMove()}))}touchGestureIds(){return this.touchGestureRecognizer.gesturePointerIds}rawPointerUpdate(e){if(this.touchGestureRecognizer.rawPointerUpdate(e),this.touchControls.rawPointerUpdate(e),this.isTouchPitching){const t=(e.pointers[0].position.y+e.pointers[1].position.y)/2,i=this.prevTouchY-t,s=Math.abs(i)>.001?i:0;this.setOrbitalAcceleration({x:0,y:Math.PI*s}),this.prevTouchY=t}}setController(e){return super.setController(e),this.touchControls.setController(e),null===e&&this.activeAction!==O.NONE&&this.endMove(),this}setOrbitalAcceleration(e,t=!1){this.transition.active||(t&&this.haltVelocity(e,this.angularVelocity),this.angularAccel.x=void 0!==e.x?e.x:this.angularAccel.x,this.angularAccel.y=void 0!==e.y?e.y:this.angularAccel.y)}setPanAcceleration(e,t=!1){this.transition.active||(t&&this.haltVelocity(e,this.linearVelocity),this.linearAccel.x=void 0!==e.x?e.x:this.linearAccel.x,this.linearAccel.y=void 0!==e.y?e.y:this.linearAccel.y)}setNdcPos(e){this.ndcPos.copy(e)}initMove(e,t,i){if(t!==E.KEYBOARD&&(this.setPanAcceleration({x:0,y:0}),this.stop()),i&&this.setNdcPos(i),e!==this.activeAction||t!==this.activeDevice){this.updateOrbitState(),this.activeDevice=t,this.activeAction=e;const i=this.cameraPoseProxy.pose;this.poseConstrainer.setStartPose(i);const s=this.getGrabPoint(),r=i.phi()<S?i.forward().clone().multiplyScalar(-1):o.fU.UP;this.grabPlane.setFromNormalAndCoplanarPoint(r,s),this.grabCameraPose=this.cameraPoseProxy.pose.clone(),this.grabPt.copy(s),e===O.ROTATE&&this.messageBus.broadcast(new U.O)}}stopAndClearState(e=!1){((0,N.tq)()||e)&&this.stop(),this.updateOrbitState(),this.activeDevice=E.NONE,this.activeAction=O.NONE}endMove(e){e&&this.setNdcPos(e),this.stopAndClearState();const t=this.cameraPoseProxy.pose.pitchFactor();!this.isAutoOrbitting&&t<1&&t>1e-10&&this.cameraPoseProxy.pose.autoOrtho&&this.startAutoOrbitTo()}startAutoOrbitTo(e="top"){const t="top"===e?G:C;if(Math.abs(this.cameraPoseProxy.pose.phi()-t)<1e-10)return Promise.resolve();const i=this.constrolsData.startAutoOrbit(e);return this.autoOrbitStartPhi=this.cameraPoseProxy.pose.phi(),i}stopAutoOrbit(){this.stopAndClearState(!0),this.constrolsData.stopAutoOrbit(),this.invalidateOrbitMetadata()}invalidateOrbitMetadata(){this.needsOrbitDataInit=!0}get isAutoOrbitting(){return this.constrolsData.isAutoOrbitting}get mouseDown(){return this.activeDevice===E.MOUSE||this.activeDevice===E.TOUCH}updateOrbitState(){if(this.activeAction!==O.ROTATE||this.needsOrbitDataInit){const e=this.cameraPoseProxy.pose,t=this.getOrbitPoint();this.orbitPoint.copy(t);const i=e.position.distanceTo(this.orbitPoint);this.orbitDistance=i/e.fovDistanceScale(),this.orbitPlane.setFromNormalAndCoplanarPoint(o.fU.UP,this.orbitPoint),this.cameraPoseProxy.pose.focalDistance=i,this.needsOrbitDataInit=!1}}setZoomAcceleration(e){this.transition.active||(this.zoomAccel=e)}async setPhiLimits(e,t,i){this.currentPhiLowerLimit=e,this.currentPhiUpperLimit=t;const s=this.cameraPoseProxy.pose.phi(),o=(0,y.uZ)(0,this.currentPhiLowerLimit-s,this.currentPhiUpperLimit-s);Math.abs(o)>z&&i&&await this.orbit(new u.Vector2,!0)}haltVelocity(e,t){e.x&&t.x&&Math.sign(e.x)!==Math.sign(t.x)&&(t.x=0),e.y&&t.y&&Math.sign(e.y)!==Math.sign(t.y)&&(t.y=0)}startTransition(e,t,i,s,o){if(null===this.poseController)return w.Q.reject("Unable to start transition, since controller is unavailable.");const r=new w.Q;return this.transition.active=!0,this.transition.duration=e,this.transition.elapsed=0,this.transition.startTime=Date.now(),this.transition.deferred=r,this.transition.angularVelocity.copy(t),this.transition.linearVelocity.copy(i),this.transition.zoomVelocity=s,this.transition.easeOut=o,this.angularAccel.set(0,0),this.linearAccel.set(0,0),this.zoomAccel=0,this.angularVelocity.copy(t),this.linearVelocity.copy(i),this.zoomVelocity=s,this.poseController.beginExternalTransition(),r.promise()}stopTransition(){this.transition.active&&(this.poseController&&this.poseController.endExternalTransition(),this.transition.active=!1),this.transition.deferred&&(this.transition.deferred.resolve(),this.transition.deferred=void 0)}updateTransition(e,t,i,s){let o=1,r=e/b;if(this.transition.elapsed+=e,this.transition.elapsed>=this.transition.duration){o=(this.transition.duration-(this.transition.elapsed-e))/e,r=1}t&&(this.angularVelocity.copy(this.transition.angularVelocity).multiplyScalar(o*r),this.orbit(this.angularVelocity)),i&&(this.linearVelocity.copy(this.transition.linearVelocity).multiplyScalar(o*r),this.pan(this.linearVelocity)),s&&(this.zoomVelocity=this.transition.zoomVelocity*o*r,this.zoom(this.zoomVelocity)),this.transition.elapsed>=this.transition.duration&&(this.stop(this.transition.easeOut),this.transition.active=!1)}updateDefault(e,t,i,s){if(this.touchControls.isMovingCamera())return;const o=e/b;if(t&&!s){this.angularVelocity.addScaledVector(this.angularAccel,o);const e=this.isTouchPitching?1.75:1;this.orbit(this.angularVelocity.clone().multiplyScalar(o/e)),this.angularVelocity.multiplyScalar(Math.pow(.92,o)/e)}i&&(this.linearVelocity.addScaledVector(this.linearAccel,o),this.pan(this.linearVelocity),this.linearVelocity.multiplyScalar(Math.pow(.92,o))),s&&(this.zoomVelocity+=this.zoomAccel*o,this.zoom(this.zoomVelocity),this.zoomVelocity*=Math.pow(.84,o),this.angularVelocity.set(0,0),this.angularAccel.set(0,0))}startRotateTransition(e,t,i){return t.x*=-1,this.orbitDistance=this.cameraPoseProxy.pose.focalDistance,this.startTransition(e,t.clone().multiplyScalar(b),new u.Vector2,0,i).nativePromise()}startTranslateTransition(e,t,i=!0){return this.startTransition(e,new u.Vector2,t.clone().multiplyScalar(b),0,i).nativePromise()}startZoomTransition(e,t,i){return this.startTransition(e,new u.Vector2(0,0),new u.Vector2(0,0),t,i).nativePromise()}update(e){const t=this.linearAccel.length()>z||this.linearVelocity.length()>z,i=this.angularAccel.length()>z||this.angularVelocity.length()>z,s=Math.abs(this.zoomAccel)>z||Math.abs(this.zoomVelocity)>z,o=i||this.isAutoOrbitting,r=t||this.mouseDown&&this.activeAction===O.PAN,n=s;this.dampedZoomDir(e),this.activeDevice!==E.KEYBOARD||t||i||s?this.transition.active?this.updateTransition(e,o,r,n):this.updateDefault(e,o,r,n):this.endMove()}stopMomentum(){this.transition.active||(this.angularVelocity.set(0,0),this.linearVelocity.set(0,0),this.zoomVelocity=0)}stopAcceleration(){this.transition.active||(this.setOrbitalAcceleration({x:0,y:0}),this.setPanAcceleration({x:0,y:0}),this.setZoomAcceleration(0))}stop(e=!1){this.stopTransition(),this.stopAcceleration(),e||this.stopMomentum()}pan(e){if(!this.poseController)return;this.setupCurrentPose();const t=this.cameraPoseProxy.pose;if(this.mouseDown){const e=this.castFromNdc();if(e){const t=e.sub(this.grabPt);this.nextPosition.copy(this.grabCameraPose.fovCorrectedPosition()).addScaledVector(t,-1),this.currentPose.position.copy(this.nextPosition),this.currentPose.applyPhiBasedFovSquish()}}else this.positionDelta.x=e.x,this.positionDelta.z=e.y,this.positionDelta.y=0,this.nextPosition.copy(t.fovCorrectedPosition()).add(this.positionDelta),this.currentPose.position.copy(this.nextPosition),this.currentPose.applyPhiBasedFovSquish();this.poseConstrainer.constrain(this.currentPose),this.poseController.updateCameraPose(this.currentPose)}orbit(e,t=!1){if(!this.poseController)return;this.setupCurrentPose();const i=this.cameraPoseProxy.pose,s=i.phi(),r=this.isAutoOrbitting?this.calcAutoOrbitVelocity(s):e,n=this.isAutoOrbitting?G:this.currentPhiUpperLimit,a=this.isAutoOrbitting?C:this.currentPhiLowerLimit,h=(0,y.uZ)(r.y,a-s,n-s),l=n-s<1e-10,d=s-a<1e-10,c="top"===this.constrolsData.autoOrbitTarget?l:d;if(this.isAutoOrbitting&&c)return void this.stopAutoOrbit();if(l&&h>0)return this.angularVelocity.y=0,void(this.angularAccel.y=0);const u=this.orbitDistance;if(this.aimTarget.copy(o.fU.FORWARD).applyQuaternion(i.rotation),this.aimTarget.setLength(u),this.aimTarget.addVectors(i.fovCorrectedPosition(),this.aimTarget),this.tempAxis.copy(o.fU.RIGHT),this.tempOrientation.setFromAxisAngle(this.tempAxis.applyQuaternion(i.rotation),-h),this.nextPosition.copy(i.fovCorrectedPosition()).sub(this.aimTarget).applyQuaternion(this.tempOrientation),this.nextOrientation.copy(i.rotation).premultiply(this.tempOrientation),this.tempOrientation.setFromAxisAngle(o.fU.UP,r.x),this.nextPosition.applyQuaternion(this.tempOrientation),this.nextOrientation.premultiply(this.tempOrientation),this.nextPosition=this.nextPosition.add(this.aimTarget),this.nextOrientation.normalize(),this.currentPose.position.copy(this.nextPosition),this.currentPose.rotation.copy(this.nextOrientation),this.currentPose.focalDistance=u,this.currentPose.applyPhiBasedFovSquish(),t)return this.poseController.moveTo({transitionType:v.n.Interpolate,pose:{position:this.currentPose.position.clone(),rotation:this.currentPose.rotation.clone()},transitionTime:500}).nativePromise();this.poseController.updateCameraPose(this.currentPose)}zoom(e){if(!this.poseController)return;this.setupCurrentPose(),this.updateOrbitState();const t=this.cameraPoseProxy.pose;this.poseConstrainer.setStartPose(t);const i=t.forward().clone(),s=.1*e/(1/(this.getGrabPoint().distanceTo(t.position)/t.fovDistanceScale())*2*this.poseConstrainer.modelSize*500),o=this.zoomDirection,r=this.orbitPoint,n=this.poseConstrainer.maxZoomDistance-this.poseConstrainer.minZoomDistance,a=r.distanceTo(t.position)/t.fovDistanceScale(),h=(0,y.uZ)(s*n+a,this.poseConstrainer.minZoomDistance,this.poseConstrainer.maxZoomDistance)-a,l=o.setLength(-h);this.nextPosition.copy(t.fovCorrectedPosition()).add(l);const d=new u.Ray(this.nextPosition,i),c=new u.Vector3,m=d.intersectPlane(this.orbitPlane,c)||r;this.currentPose.position.copy(this.nextPosition),this.currentPose.focalDistance=m.distanceTo(this.nextPosition),this.currentPose.resetProjMatrix(),this.currentPose.applyPhiBasedFovSquish(),this.poseConstrainer.constrain(this.currentPose),this.poseController.updateCameraPose(this.currentPose)}setupCurrentPose(){const e=this.cameraPoseProxy.pose;this.currentPose.copy(e),this.currentPose.unapplyPhiBasedFovSquish()}calcAutoOrbitVelocity(e){const t="top"===this.constrolsData.autoOrbitTarget?1:-1,i="top"===this.constrolsData.autoOrbitTarget?G:C,s=Math.abs((i-e)/(i-this.autoOrbitStartPhi)),o=(0,I.t)(.002,.08,s);return new u.Vector2(0,t*o)}getDebugState(){return{plane:this.orbitPlane.clone(),orbitPt:this.orbitPoint.clone(),zoomDir:this.zoomDirection.clone().normalize()}}}var j=i(20217),Q=i(18596);class Z{constructor(e){this.idealOrbitCenter=new u.Vector3,this.gestureStartPose=new k.B(1),this.constrain=(()=>{const e=new u.Vector3,t=new u.Vector3,i=new u.Vector3,s=new u.Vector3,r=new u.Ray,n=new u.Plane;return a=>{const h=this.gestureStartPose.focalPoint(),l=a.focalPoint();if(l.distanceTo(h)<1e-10)return;const d=i.subVectors(a.position,this.gestureStartPose.position);r.set(this.gestureStartPose.position,d);const c=this.maxDeviationOfOrbitPoint.clampPoint(l,e),u=a.fovDistanceScale(),m=this.minOrbitDistance*u,p=this.maxOrbitDistance*u,g=(0,y.uZ)(a.focalDistance,m,p),f=t.copy(c).addScaledVector(a.forward(),-1*g);Math.abs(d.y)>1e-8&&(n.setFromNormalAndCoplanarPoint(o.fU.UP,f),r.intersectPlane(n,s)&&f.copy(s)),a.focalDistance=g,a.position.copy(f),a.commit()}})(),this.updateConstraints(e)}updateConstraints(e){const t=e.getSize(new u.Vector3);this.modelSize=Math.max(t.length(),1),this.modelBoundingBox=e,this.idealOrbitCenter=e.getCenter(this.idealOrbitCenter);const i=e.max.distanceTo(this.idealOrbitCenter);this.minOrbitDistance=2;const s=2*i;this.maxOrbitDistance=s/Math.tan(Q.oR.fov/2*c.Ue);const o=2*i;this.maxDeviationOfOrbitPoint=new u.Box3(this.idealOrbitCenter.clone().add(new u.Vector3(-o,0,-o)),this.idealOrbitCenter.clone().add(new u.Vector3(o,0,o))),this.maxDeviationOfOrbitPoint.min.y=this.modelBoundingBox.min.y,this.maxDeviationOfOrbitPoint.max.y=this.modelBoundingBox.max.y}get minZoomDistance(){return this.minOrbitDistance}get maxZoomDistance(){return this.maxOrbitDistance}setStartPose(e){this.gestureStartPose.copy(e)}containsPoint(e){return this.maxDeviationOfOrbitPoint.containsPoint(e)}}var W=i(41927),$=i(66211),q=i(34029),K=i(66777),Y=i(61601),J=i(61864);class X extends $.Z{constructor(){super(...arguments),this.name="dollhouse-controls",this.controlState=O.NONE,this.movementKeys=new u.Vector2,this.didDrag=!1,this.peekabooActive=!1,this.swapMouseBoutton=!1,this.displayDollhouseMetadata=!1,this.resetControlState=()=>{this.controlState=O.NONE},this.convertDeltaToLocal=(()=>{const e=new u.Vector3,t=new u.Vector3;return i=>{const s=i.x||0,r=i.y||0,n=this.cameraPoseProxy.pose.phi()>(0,c.Id)(85)?o.fU.UP:o.fU.FORWARD,a=2*s*this.cameraPoseProxy.pose.fovCorrectedFocalDistance(),h=2*r*this.cameraPoseProxy.pose.fovCorrectedFocalDistance()/this.cameraPoseProxy.pose.aspect();return e.copy(o.fU.RIGHT).applyQuaternion(this.cameraPoseProxy.pose.rotation).setY(0).setLength(a),t.copy(n).applyQuaternion(this.cameraPoseProxy.pose.rotation),e.add(t.setY(0).setLength(h)),e}})()}async init(e,t){[this.visibleMeshBounds,this.commonControlsModule,this.inputIni,this.raycaster,this.renderer,this.settings]=await Promise.all([t.getModuleBySymbol(s.ep),t.getModuleBySymbol(s.Ng),t.getModuleBySymbol(s.PZ),t.getModuleBySymbol(s.fQ),t.getModuleBySymbol(s.Aj),t.getModuleBySymbol(J.Ak)]);const i=await t.market.waitForData(q.e);this.peekabooActive=i.tryGetProperty(K.eC,!1),this.bindings.push(i.onPropertyChanged(K.eC,(e=>{this.peekabooActive=e}))),this.settings.registerMenuButton({header:"Dollhouse",buttonName:"Toggle Orbit point display",callback:()=>{this.displayDollhouseMetadata=!this.displayDollhouseMetadata,this.displayDollhouseMetadata?this.createDebugObjects():this.removeDebugObjects()}}),this.cameraPoseProxy=this.commonControlsModule.cameraPoseProxy,this.poseConstrainer=new Z(this.visibleMeshBounds.getFullBounds()),this.controlsData=new Y.x,t.market.register(this,Y.x,this.controlsData),this.controls=new H(this.cameraPoseProxy,this.poseConstrainer,(()=>this.computeOrbitPoint()),(()=>this.computeGrabPoint()),(e=>this.focusPointHelper(e)),this.controlsData,t.msgBus),this.bindings.push(this.visibleMeshBounds.onFullBoundsChanged((e=>{this.poseConstrainer.updateConstraints(e),this.controls.invalidateOrbitMetadata()})),this.visibleMeshBounds.onVisibleBoundsChanged((e=>{this.controls.invalidateOrbitMetadata()})),t.commandBinder.addBinding(j.Cy,(async e=>{const t=this.cameraPoseProxy.pose.autoOrtho?T:M;return this.controls.setPhiLimits(void 0!==e.phiLowerLimitDegrees?e.phiLowerLimitDegrees*c.Ue:D,void 0!==e.phiUpperLimitDegrees?e.phiUpperLimitDegrees*c.Ue:t,void 0!==e.noTransition?!e.noTransition:this.controls.isActive)})),t.commandBinder.addBinding(j.ho,(async()=>{if(this.controls.isActive){const e=this.cameraPoseProxy.pose.phi();Math.abs(e-T)>1e-8&&(this.controls.stop(),this.controls.invalidateOrbitMetadata(),this.controls.initMove(O.ROTATE,E.KEYBOARD),await this.controls.startAutoOrbitTo("top"))}else this.log.debugWarn("SnapDollhouseToUpperPhiLimit command received while not active. Ignoring.")})),t.commandBinder.addBinding(j.i6,(async()=>{if(this.controls.isActive){const e=this.cameraPoseProxy.pose.phi();Math.abs(e-C)>1e-8&&(this.controls.stop(),this.controls.invalidateOrbitMetadata(),this.controls.initMove(O.ROTATE,E.KEYBOARD),await this.controls.startAutoOrbitTo("side"))}else this.log.debugWarn("SnapDollhouseToLowerPhiLimit command received while not active. Ignoring.")})),t.commandBinder.addBinding(j.EG,(async()=>{this.peekabooActive||(this.swapMouseBoutton=!0)})),t.commandBinder.addBinding(j.HF,(async()=>{this.peekabooActive||(this.swapMouseBoutton=!1)}))),this.registerActiveStateChangeBinding();const o=this.inputIni;this.commonControlsModule.addControls(d.Ey.Dollhouse,this.controls);const h=e=>{this.didDrag=!1,this.onDragBegin(e.buttons,e.position,e.device,e.ctrlKey)},u=e=>{e.timeSinceLastMove<100&&this.didDrag&&(this.onDrag(e.delta,e.position),this.controls.update(b),this.controls.stopAcceleration()),this.onDragEnd(e.delta,e.buttons,e.position)},p=e=>{this.didDrag=!0,this.onDrag(e.delta,e.position),this.controls.update(b),this.controls.stop()};this.inputBindings.push(o.registerHandler(r.a,(e=>{this.onScrollWheel(e)}))),this.inputBindings.push(o.registerHandler(n.E0,(e=>{this.isTouchDrag(e.pointerId)||h(e)}))),this.inputBindings.push(o.registerHandler(n._R,(e=>{this.isTouchDrag(e.pointerId)||u(e)}))),this.inputBindings.push(o.registerHandler(n._t,(e=>{this.isTouchDrag(e.pointerId)||p(e)}))),this.inputBindings.push(o.registerHandler(m.h,(e=>{this.controls.rawPointerUpdate(e)}))),this.inputBindings.push(o.registerHandler(a.e,(e=>{e.state!==l.M.PRESSED&&this.onKey(e.key,e.state)}))),this.updateInputBindings(),this.peekabooActive&&(this.controls.setPhiLimits(D,T,!1),this.swapMouseBoutton=!0)}onUpdate(e){this.controls.isActive&&(this.controls.update(e),this.updateDebugObjects())}isTouchDrag(e){const t=this.controls.touchGestureIds();return t[0]===e||t[1]===e}onActiveStateChanged(){super.onActiveStateChanged(),this.resetControlState()}onScrollWheel(e){0!==e.delta.y&&(this.controls.setZoomAcceleration(e.delta.y),this.controls.update(b),this.controls.setZoomAcceleration(0))}onDragBegin(e,t,i,s=!1){if(this.controlState===O.NONE){const t=this.swapMouseBoutton?F:A;if(i===W._.MOUSE){const i=this.applyKeyboardModifier(e,s);this.controlState=t[i]}else this.controlState=this.peekabooActive?O.PAN:O.ROTATE}const o=i===W._.TOUCH?E.TOUCH:E.MOUSE;this.controls.initMove(this.controlState,o,t)}onDrag(e,t){switch(this.controls.setNdcPos(t),this.controlState){case O.ROTATE:this.controls.setOrbitalAcceleration({x:-Math.PI*e.x,y:-Math.PI*e.y});break;case O.ZOOM:0!==e.y&&this.controls.setZoomAcceleration(-e.y)}}onDragEnd(e,t,i){t&this.controlState||(this.controlState=O.NONE,this.controls.endMove(i))}onKey(e,t){const i=t===l.M.DOWN;let s=!1;switch(e){case h.R.LEFTARROW:case h.R.J:this.controls.setOrbitalAcceleration({x:i?P:0},!0),this.controls.initMove(O.ROTATE,E.KEYBOARD);break;case h.R.RIGHTARROW:case h.R.L:this.controls.setOrbitalAcceleration({x:i?-P:0},!0),this.controls.initMove(O.ROTATE,E.KEYBOARD);break;case h.R.UPARROW:case h.R.I:this.controls.setOrbitalAcceleration({y:i?-P:0},!0),this.controls.initMove(O.ROTATE,E.KEYBOARD);break;case h.R.DOWNARROW:case h.R.K:this.controls.setOrbitalAcceleration({y:i?P:0},!0),this.controls.initMove(O.ROTATE,E.KEYBOARD);break;case h.R.W:this.movementKeys.y=i?1:0,s=!0;break;case h.R.S:this.movementKeys.y=i?-1:0,s=!0;break;case h.R.D:this.movementKeys.x=i?1:0,s=!0;break;case h.R.A:this.movementKeys.x=i?-1:0,s=!0}if(s){const e=this.convertDeltaToLocal(this.movementKeys).setLength(.02);this.controls.setPanAcceleration({x:e.x,y:e.z}),this.controls.initMove(O.PAN,E.KEYBOARD)}}computeGrabPoint(){const e=this.inputIni.getCurrentPointerRay();return this.focusPointHelper(e,!0)}computeOrbitPoint(){const e=this.cameraPoseProxy.pose,t=new u.Ray(e.position.clone(),e.forward().clone());return this.focusPointHelper(t)}focusPointHelper(e,t=!1){const i=()=>{const t=this.visibleMeshBounds.getVisibleBounds(),i=this.visibleMeshBounds.getCenterOfMass(),s=Math.ceil(Math.abs(i.y-t.min.y)/3);let r=Number.MAX_VALUE;const n=new u.Vector3,a=new u.Vector3;let h=!1;if(Number.isFinite(s))for(let l=0;l<s;l++){const s=t.min.y+3*l,d=(new u.Plane).setFromNormalAndCoplanarPoint(o.fU.UP,new u.Vector3(t.min.x,s,t.min.z));h=!!e.intersectPlane(d,n);const c=i.distanceTo(n);h&&c<r&&(r=c,a.copy(n))}const l=!this.poseConstrainer.containsPoint(a);return h&&!l||(e.closestPointToPoint(i,a),e.origin.distanceTo(a)<.25&&e.at(.25,a)),a};if(this.cameraPoseProxy.pose.autoOrtho&&!t)return i();{const t=this.raycaster.picking.pick(e.origin,e.direction);return t?t.point.clone():i()}}applyKeyboardModifier(e,t){let i=e;return t&&e===p.r.PRIMARY&&(i=p.r.SECONDARY),i}createDebugObjects(){if(null==this.debugPlane){const e=new u.PlaneHelper(new u.Plane,100,16711680);e.material.depthTest=!1,this.debugPlane=e}if(null==this.debugOrbit){const e=new u.SphereGeometry(.1,2,2),t=new u.MeshBasicMaterial({color:16711680,side:u.DoubleSide,depthTest:!1}),i=new u.Mesh(e,t);this.debugOrbit=i}if(null==this.debugZoom){const e=new u.SphereGeometry(.2,2,2),t=new u.MeshBasicMaterial({color:65280,side:u.DoubleSide,depthTest:!1}),i=new u.Mesh(e,t);this.debugZoom=i}this.renderer.getScene().add(this.debugPlane,this.debugOrbit,this.debugZoom)}removeDebugObjects(){null!=this.debugPlane&&(this.renderer.getScene().remove(this.debugPlane),this.debugPlane=null),null!=this.debugOrbit&&(this.renderer.getScene().remove(this.debugOrbit),this.debugOrbit=null),null!=this.debugZoom&&(this.renderer.getScene().remove(this.debugZoom),this.debugZoom=null)}updateDebugObjects(){if(this.displayDollhouseMetadata){const{plane:e,orbitPt:t,zoomDir:i}=this.controls.getDebugState();if(null!=this.debugPlane&&(this.debugPlane.plane=e,this.debugPlane.updateMatrixWorld(!0)),null!=this.debugOrbit&&this.debugOrbit.position.copy(t),null!=this.debugZoom){const e=this.cameraPoseProxy.pose.fovCorrectedPosition().clone().addScaledVector(i,this.cameraPoseProxy.pose.fovCorrectedFocalDistance());this.debugZoom.position.copy(e)}}}}},88675:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>v});var s=i(81396),o=i(90304),r=i(41602),n=i(21646),a=i(44649);class h extends a.B{constructor(e,t,i,s,o=!1){super(e,t,i,s,o),this.cameraPoseProxy=e,this.angularAccel=0,this.angularVelocity=0,this.transition.angularVelocity=0}setRollAcceleration(e,t=!1){this.transition.active||(t&&e&&this.angularVelocity&&Math.sign(e)!==Math.sign(this.angularVelocity)&&(this.angularVelocity=0),this.angularAccel=e)}startRotateTransition(e,t,i){return this.startTransition(e,t.x*r.SI,new s.Vector2,0,i).nativePromise()}startTransition(e,t,i,s,o){return this.transition.angularVelocity=t,this.angularVelocity=t,this.angularAccel=0,super.startTransition(e,t,i,s,o)}update(e){super.update(e);(Math.abs(this.angularAccel)>n.Z.epsilon||Math.abs(this.angularVelocity)>n.Z.epsilon)&&(this.transition.active?this.updateAngularTransition(e):this.updateAngularDefault(e))}stopMomentum(){super.stopMomentum(),this.transition.active||(this.angularVelocity=0)}stopAcceleration(){super.stopAcceleration(),this.transition.active||this.setRollAcceleration(0)}updateAngularTransition(e){const t=this.getTransitionScale(e);this.angularVelocity=this.transition.angularVelocity*t,this.roll(this.angularVelocity)}updateAngularDefault(e){const t=e/r.SI;this.angularVelocity=this.angularVelocity+this.angularAccel*t,this.roll(this.angularVelocity),this.angularVelocity*=Math.pow(1-r.O8,t)}roll(e){var t;const i=this.cameraPoseProxy.pose;this.currentOrientation.setFromAxisAngle(o.fU.FORWARD,e),this.currentOrientation.multiplyQuaternions(i.rotation,this.currentOrientation),this.currentOrientation.normalize(),this.checkBounds&&!this.insideBounds(i.position,this.currentOrientation,i.projection)||i.rotation.equals(this.currentOrientation)||null===(t=this.poseController)||void 0===t||t.updateCameraRotation(this.currentOrientation)}}var l=i(66445),d=i(10082),c=i(92810),u=i(32597),m=i(6667),p=i(95882),g=i(16782);const f=Math.PI/2/1e3;var y,w=i(66777);!function(e){e[e.NONE=g.r.NONE]="NONE",e[e.PAN=g.r.PRIMARY]="PAN",e[e.ROTATE=g.r.SECONDARY]="ROTATE",e[e.ZOOM=g.r.MIDDLE]="ZOOM"}(y||(y={}));class v extends l.default{constructor(){super(...arguments),this.name="floorplan-controls"}async init(e,t){await super.init(e,t),t.getModuleBySymbol(c.PZ).then((e=>{this.inputBindings.push(e.registerHandler(d.D,(e=>{this.controls.isActive&&(this.controls.setRollAcceleration(e.rotateDelta),this.controls.update(r.SI),this.controls.stop())})),e.registerHandler(d.u,this.resetControlState)),this.updateInputBindings()}))}createCameraControls(e,t,i){const s=this.commonControlsModule.cameraPoseProxy,o=t.defaultZoom.bind(t);this.controls=new h(s,o,e.extendedBounds,e.meshCenter,!0),i.tryGetProperty(w.eC,!1)||this.commonControlsModule.addControls(p.Ey.Floorplan,this.controls)}onDrag(e){switch(super.onDrag(e),this.controlState){case y.ROTATE:this.controls.setRollAcceleration(e.x*Math.PI)}}onKey(e){super.onKey(e);const{key:t,state:i,modifiers:s}=e,o=i===m.M.DOWN;switch(t){case u.R.J:case u.R.LEFTARROW:this.controls.setRollAcceleration(o?f:0,!0);break;case u.R.L:case u.R.RIGHTARROW:this.controls.setRollAcceleration(o?-f:0,!0);break;case u.R.DOWNARROW:s.shiftKey||this.controls.setZoomAcceleration(o?r.Gu:0);break;case u.R.UPARROW:s.shiftKey||this.controls.setZoomAcceleration(o?-r.Gu:0)}}}},45717:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>P});var s=i(97542),o=i(92810),r=i(2541),n=i(71954),a=i(64567),h=i(76957),l=i(79728),d=i(635),c=i(8699),u=i(17788),m=i(97998),p=i(81396);class g{constructor(){this.list=[]}insort(e){const t=this.binarySearch(e);let i=t.index;if(!t.success)for(;this.compare(e,this.list[i])>=0&&i<this.list.length;)i++;for(let e=this.list.length;e>i;e--)this.list[e]=this.list[e-1];this.list[i]=e}removeIndex(e){if(e>this.list.length)throw Error("OrderList.removeIndex() -> Invalid index: "+e);this.list.splice(e,1)}getElement(e){if(e>=0&&e<this.list.length)return this.list[e];throw new Error("OrderList.getElement() -> Invalid index: "+e)}get length(){return this.list.length}find(e){const t=this.binarySearch(e);return t.success?t.index:-1}compare(e,t){return void 0===t?1:"number"==typeof e?e===t?0:e<t?-1:1:e.compare(t)}binarySearch(e){let t,i=0,s=this.list.length-1,o=-1,r=0;for(;i<=s;){if(o=Math.floor((i+s)/2),t=this.list[o],r=this.compare(e,t),0===r)return{success:!0,index:o};r<0?s=o-1:i=o+1}return{success:!1,index:o}}}var f=i(37262),y=i(75287),w=i(90304),v=i(37082);class b extends y.T{constructor(e={}){super(),this.name="",this.center=new p.Vector3,this.centerOfMass=new p.Vector3,this._boundingBox=new v.f(new p.Box3),this.size=new p.Vector3,this.sweepHeights=new g,this.sweepFloorHeights=new g,this._groundPlane=new p.Plane,this._groundPt=new p.Vector3,this.medianSweepHeight=()=>this.sweepHeights.length>0?this.sweepHeights.getElement(Math.floor(this.sweepHeights.length/2)):this.center.y,this.minSweepFloorHeight=()=>this.sweepFloorHeights.length>0?this.sweepFloorHeights.getElement(0):this.boundingBox.min.y,Object.assign(this,e)}get boundingBox(){return this._boundingBox.value}set boundingBox(e){this.setBounds(e)}setBounds(e){this._boundingBox.value.copy(e),this._boundingBox.value.getSize(this.size),this._boundingBox.value.getCenter(this.center),this._boundingBox.setDirty(!0)}setCenterOfMass(e){this.centerOfMass.copy(e)}onBoundsChanged(e){return this._boundingBox.onChanged(e)}addSweep(e,t){this.sweepHeights.insort(e.y),this.sweepFloorHeights.insort(t.y)}medianSweepFloorHeight(){return this.sweepFloorHeights.length>0?this.sweepFloorHeights.getElement(Math.floor(this.sweepFloorHeights.length/2)):this.bottom}get bottom(){return this.minSweepFloorHeight()}get groundPlane(){return this._groundPt.set(0,this.bottom,0),this._groundPlane.setFromNormalAndCoplanarPoint(w.fU.UP,this._groundPt),this._groundPlane}get top(){return this.sweepFloorHeights.length>0?this.sweepFloorHeights.getElement(this.sweepFloorHeights.length-1):this.boundingBox.max.y}deepCopy(){return(0,f.p$)({id:this.id,meshGroup:this.meshGroup,index:this.index,name:this.name,center:this.center,boundingBox:this.boundingBox,size:this.size,medianSweepHeight:this.medianSweepHeight(),bottom:this.bottom})}}const M=new m.Z("mds-floor-deserializer");class T{deserialize(e){if(!e||!this.validate(e))return M.debug("Deserialized invalid floor data from MDS",e),null;const t=e.label||"",i=e.dimensions||{areaFloor:-1},{areaFloor:s}=i;return new b({id:e.id,index:e.sequence,meshGroup:e.meshId,name:t,areaFloor:s||-1})}validate(e){return["id","sequence","meshId"].every((t=>t in e))}}class D{serialize(e){return{label:e.name}}}class x extends u.u{constructor(){super(...arguments),this.deserializer=new T,this.prefetchKey="data.model.floors"}async read(e={}){const t={modelId:this.getViewId()};return this.query(c.GetFloors,t,e).then((e=>{var t,i;const s=null===(i=null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.model)||void 0===i?void 0:i.floors;if(!s||!Array.isArray(s))return null;return s.reduce(((e,t)=>{const i=this.deserializer.deserialize(t);return i&&(e[t.id]=i),e}),{})}))}async update(e,t){const i=this.getViewId(),s=(new D).serialize(t);if(!s)throw new Error("Could not update Floor");return this.mutate(c.PatchFloor,{modelId:i,floorId:e,data:s})}}var S=i(67238),C=i(80742);class P extends s.Y{constructor(){super(...arguments),this.name="floors"}async init(e,t){const{readonly:i,baseUrl:s,baseModelId:n}=e;this.engine=t;const{mdsContext:c}=await t.market.waitForData(C.R);this.store=new x({context:c,readonly:i,baseUrl:s,viewId:n});const[u]=await Promise.all([this.store.read()]);if(this.data=new h.i(u||{}),t.market.register(this,h.i,this.data),!1===i){const e=this.data.getCollection();this.monitor=new a.c(e,{aggregationType:r.E.NextFrame,shallow:!0},t),this.monitor.onChanged((()=>this.engine.commandBinder.issueCommand(new d.V({dataTypes:[l.g.FLOORS]}))));const[i]=await Promise.all([t.getModuleBySymbol(o.Lx)]);this.bindings.push(i.onSave((()=>this.save()),{dataType:l.g.FLOORS}))}}onUpdate(){}async save(){const e=this.monitor.getDiffRecord();this.monitor.clearDiffRecord();const t=[];for(const i of e){const e=i.index;if(!e)throw new S.H(`Invalid floor '${i.index}'`);i.action===n.KI.updated&&t.push(this.store.update(e,i.diff))}return Promise.all(t)}}},62226:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>re});var s=i(97542),o=i(92810),r=i(89553),n=i(23612),a=i(32597),h=i(6667),l=i(53954),d=i(23254),c=i(9037),u=i(76532),m=i(69739),p=i(66563),g=i(5090),f=i(85992),y=i(95882),w=i(2473),v=i(54244),b=i(10374),M=i(76957),T=i(49827),D=i(93411),x=i(53203);class S{constructor(e,t,i,s,o,r,n){this.getAngleModifier=e,this.floorsViewData=t,this.meshQuery=i,this.viewmodeData=s,this.raycasterData=o,this.meshData=r,this.touchDevice=n}activate(){}init(){}dispose(){}deactivate(){const{floorOpacity:e}=this.meshData.meshGroupVisuals;for(const t of this.meshData.meshGroups.floors.keys())e.set(t,0)}beforeRender(){const e=this.floorsViewData.nearestFloor,t=this.floorsViewData.transition.progress.active,i=!(!e||this.viewmodeData.isInside()),s=x.xx.FADE_OPAQUE;let o=s,r=s;const n=this.getAngleModifier(),a=this.floorsViewData.onlyShowActiveFloor.value?0:1;i&&(this.viewmodeData.isDollhouse()||this.viewmodeData.isOrthographic()?(o=s*n*a*x.xx.FADE_IN_VALUE,r=o*x.xx.FADE_BELOW_MULT+x.xx.FADE_BELOW_START):(o=x.xx.FADE_ABOVE,r=x.xx.FADE_BELOW));const{floorOpacity:h}=this.meshData.meshGroupVisuals;for(const e of this.meshData.meshGroups.floors.keys()){const i=this.floorsViewData.floors.getFloorByMeshGroup(e);let n=s;const a=this.isBelowSelectedFloor(i)?r:o;let l=(0,T.uZ)(a+x.xx.FADE_IN_HOVER_BOOST_VALUE,0,1);this.floorsViewData.roomSelectModeActive&&(l=a),n=this.isHoveredFloor(null==i?void 0:i.id)&&!t&&a>0?l:a,n=this.isFloorTransitioningOut(i)?a:n;const d=this.isSelectedFloor(null==i?void 0:i.id)||this.isFloorTransitioningIn(i);if(n=d?s:n,n=this.clampForViewmodeTransitions(n),d)h.set(e,n);else{const t=h.get(e);if(void 0===t)h.set(e,n);else if(t!==n){let i=(0,D.t)(t,n,.2);Math.abs(n-i)<1e-4&&(i=n),h.set(e,i)}}}}render(){}clampForViewmodeTransitions(e){if(this.viewmodeData.currentMode===y.Ey.Transition){const{to:t}=this.viewmodeData.transition;if(t===y.Ey.Panorama)return Math.max(1-this.meshData.meshTextureOpacity.value,e)}return e}isHoveredFloor(e){if(void 0!==e&&this.raycasterData.hit&&(this.viewmodeData.isDollhouse()||this.viewmodeData.isOrthographic())&&!this.touchDevice&&this.floorsViewData.isNavigable(e)){const t=this.raycasterData.hit.object;return e===this.meshQuery.floorIdFromObject(t)}return!1}isSelectedFloor(e){const t=this.floorsViewData.nearestFloor;return!t||!(!e||t.id!==e)}isBelowSelectedFloor(e){const t=this.floorsViewData.nearestFloor;return!!(e&&t&&t.index>e.index)}isFloorTransitioningIn(e){return!(!e||!this.floorsViewData.transition.progress.active||this.floorsViewData.transition.to&&this.floorsViewData.transition.to!==e.id)}isFloorTransitioningOut(e){return!(!e||!this.floorsViewData.transition.progress.active||this.floorsViewData.transition.from&&this.floorsViewData.transition.from!==e.id&&this.floorsViewData.transition.to===e.id)}}var C=i(46950),P=i(56159),O=i(26256),k=i(64918),B=i(12039),A=i(81396),F=i(90304),E=i(73121),R=i(98169),I=i(81248),V=i(67238),_=i(35826),L=i(52498),N=i(59370),U=i(66777);class z{constructor(e,t,i,s,o,r,n,a,h,l){this.engine=e,this.floorsData=t,this.floorsViewData=i,this.sweepData=s,this.cameraData=o,this.cameraModule=r,this.viewmodeData=n,this.updateCurrentFloor=a,this.meshData=h,this.settingsData=l,this.MOVE_TO_BLACK_TRANSITION_TIME=2e3,this.moveFloorUp=(e=!1,t)=>{const i=this.floorsViewData.currentFloorId,s=this.floorsViewData.getNavigableFloorIds(),o=i?s.indexOf(i):-1;let r=s[o+1];return void 0===r&&(r=this.viewmodeData.isInside()?s[s.length-1]:s[0]),this.moveToFloor(r,e,t)},this.moveFloorDown=(e=!1,t)=>{const i=this.floorsViewData.currentFloorId,s=this.floorsViewData.getNavigableFloorIds(),o=i?s.indexOf(i):s.length;let r=s[o-1];return void 0===r&&(r=this.viewmodeData.isInside()?s[0]:s[s.length-1]),this.moveToFloor(r,e,t)},this.moveToFloor=(e,t=!1,i,s)=>{const o=this.floorsViewData.currentFloorId,r=o===e,n=this.floorsViewData.totalFloors<2&&!e,a=e?this.floorsViewData.floors.getFloor(e):null;if(r||n||!this.floorsViewData.floorsEnabled)return this.updateCurrentFloor(e),C.Q.resolve();if(a&&!this.canMoveToFloor(a,t))return C.Q.reject(new V.H("Invalid floor ID"));this.engine.broadcast(new P.S(o,e)),void 0===i&&(i=L.cw);const h=new C.Q;let l;this.floorsViewData.transitionToFloor(o,e,i,h.nativePromise()),this.floorsViewData.commit();let d,c=s;if(this.viewmodeData.isInside()){const t=e=>e=>this.floorsViewData.isCurrentOrAllFloors(e.floorId),i=!!e?R.nK:t,o=[(0,R._T)(),(0,R._k)(),i(e)],r=[(0,I.l0)(s||this.cameraData.pose.position)],n=this.sweepData.sortByScore(o,r).shift();n&&(c=n.sweep.position,l=n.sweep.id)}else null!==e&&this.engine.commandBinder.issueCommand(new _.aK);d=t?Promise.resolve():this.getCameraTransition(e,i,l,c);const u=this,m=C.Q.all([h,d]);return this.engine.startGenerator((function*(){let e=Date.now();for(;u.floorsViewData.transition.progress.active;){yield new O.Jj;const t=Date.now(),i=t-e;u.floorsViewData.transition.progress.tick(i),u.floorsViewData.commit(),e=t}u.updateCurrentFloor(u.floorsViewData.transition.to),h.resolve()})),m}}moveToFloorIndex(e,t=!1,i,s){let o=null;if(e!==L.qE){const t=this.floorsViewData.floors.getFloorAtIndex(e);if(!t)return C.Q.reject(new V.H(`Invalid floor index ${e}`));o=t.id}return this.moveToFloor(o,t,i,s)}canMoveToFloor(e,t=!1){if(!e)return!1;const i=this.floorsViewData.transition.progress.active,s=this.cameraData.canTransition()||t,o=e.index<=this.floorsViewData.totalFloors-1,r=e.index>=-1;return!i&&s&&o&&r}getCameraTransition(e,t,i,s){if(this.settingsData.tryGetProperty(U.eC,!1)?this.viewmodeData.currentMode===y.Ey.Dollhouse&&!(0,N.Eb)(this.cameraData.pose.pitchFactor()):this.viewmodeData.currentMode===y.Ey.Dollhouse||this.viewmodeData.currentMode===y.Ey.Floorplan){const i=this.getPoseForFloor(e,s);return this.cameraModule.moveTo({transitionType:k.n.Interpolate,pose:i.pose,focalDistance:i.focalDistance,transitionTime:t,autoOrtho:this.cameraData.pose.autoOrtho}).nativePromise()}return this.viewmodeData.isInside()&&i?this.engine.commandBinder.issueCommand(new B.ju({transition:k.n.MoveToBlack,sweep:i,transitionTime:this.MOVE_TO_BLACK_TRANSITION_TIME})):Promise.resolve()}getPoseForFloor(e,t){const i=this.cameraData.pose.fovCorrectedPosition(),s=this.cameraData.pose.rotation,o=e?this.floorsData.getFloor(e):null,r=o?o.medianSweepHeight():this.meshData.meshCenter.y,n=o?o.center:this.meshData.meshCenter;n.setY(r);const a=F.fU.FORWARD.clone().applyQuaternion(s),h=i.clone();let l,d;if(t){const e=i.clone().addScaledVector(a,this.cameraData.pose.fovCorrectedFocalDistance()),s=(new A.Vector3).set(0,i.y,0),o=(new A.Vector3).set(0,e.y,0),n=s.distanceTo(o);h.setY(r+n);const c=e.clone().setY(t.y);d=c.distanceTo(h),this.viewmodeData.isDollhouse()&&(l=(0,E.n0)(h,c))}else{const e=(o?o.size.y:G)/2+G*-a.y;h.setY(r+e),d=h.distanceTo(n)}return{focalDistance:d,pose:{position:h,rotation:l}}}}const G=8;var H=i(79727),j=i(33324),Q=i(23885),Z=i(2569),W=i(69505);class ${constructor(e,t,i){this.floorsViewData=e,this.viewmodeData=t,this.pose=i,this.vectors={lookDir:new A.Vector3,flattenedLookDir:new A.Vector3},this.getAngleModifier=this.getAngleModifier.bind(this)}update(){const e=this.getAngleModifier(),t=1===this.floorsViewData.totalFloors,i=!!this.floorsViewData.nearestFloor,s=!this.viewmodeData.isInside()&&!this.viewmodeData.transitionActive();this.floorsViewData.roomSelectModeActive=s&&(t||i&&e<1||this.viewmodeData.isFloorplan()),this.floorsViewData.floorSelectModeActive=s&&1===e&&!t,this.floorsViewData.showFloorSelection=!this.viewmodeData.transitionActive()&&(this.viewmodeData.isDollhouse()||this.viewmodeData.isFloorplan())&&i&&!t,this.floorsViewData.floorSelectable=this.viewmodeData.isDollhouse()||this.viewmodeData.isOrthographic()}getAngleModifier(){const e=this.vectors.lookDir.copy(F.fU.FORWARD).applyQuaternion(this.pose.rotation),t=this.vectors.flattenedLookDir.copy(e).setY(0),i=e.angleTo(t)*W.MN,s=x.xx.FADE_IN_START_ANGLE,o=x.xx.FADE_IN_END_ANGLE,r=1-(0,Z.dS)(i,s,o,0,1);return(0,Q.FG)(r,0,1,1)}}var q=i(31362),K=i(89570),Y=i(72996),J=i(26269),X=i(38987),ee=i(34956),te=i(34029),ie=i(39642),se=i(10699),oe=i(59625);class re extends s.Y{constructor(){super(),this.name="floors-viewdata",this.onEndMoveToSweepMessage=e=>{const t=this.sweepData.getSweep(e.toSweep);t&&this.updateCurrentFloor(t.floorId)},this.onMoveToFloorIndexCommand=async e=>this.floorNavigation.moveToFloorIndex(e.floorIndex,e.suppressCameraMovement,e.transitionTime,e.focusPoint),this.onShowAllFloorsCommand=async e=>{const t="boolean"==typeof e.moveCamera&&!e.moveCamera;await this.engine.commandBinder.issueCommand(new j.Vw(null,t))},this.onEnableAllFloorsOptionCommand=async()=>{this.floorsViewData.showAllFloorsOption=!0,this.floorsViewData.commit()},this.onDisableAllFloorsOptionCommand=async()=>{this.floorsViewData.currentFloorIndex===L.qE&&await this.floorNavigation.moveToFloorIndex(0,!0),this.floorsViewData.showAllFloorsOption=!1,this.floorsViewData.commit()},this.applicationChanged=()=>{this.floorsViewData.updateViewData()},this.updateCurrentFloor=e=>{this.config.allowFloorChanges&&this.floorsViewData.currentFloorId!==e&&(this.floorsViewData.transitionToFloorInstant(e),this.engine.broadcast(new m.P(e,this.floorsViewData.getFloorName(e))))},this.updateFloorSelectMode=()=>{var e;null===(e=this.floorsSelectModeHelper)||void 0===e||e.update()},this.floorSelectFeatureEnabledCheck=()=>{const e=this.settingsData.tryGetProperty(ie.w,!0),t=this.settingsData.tryGetProperty(oe.gx.FloorSelect,!0),i=this.applicationData.application===v.Mx.WORKSHOP;this.toggleFloors(i||e&&t)},this.onMoveToFloorCommand=this.onMoveToFloorCommand.bind(this)}async init(e,t){var i,s,r;this.engine=t,this.config=e,[this.floorsData,this.settingsData,this.applicationData,this.sweepData,this.viewmodeData]=await Promise.all([t.market.waitForData(M.i),t.market.waitForData(te.e),t.market.waitForData(v.pu),t.market.waitForData(l.Z),t.market.waitForData(d.O)]);const n=await t.getModuleBySymbol(o.e9),a={floorChangesEnabled:()=>this.config.allowFloorChanges};if(this.floorsViewData=new H.c(this.floorsData,this.viewmodeData,this.sweepData,this.applicationData,n.t.bind(n),a),this.floorSelectFeatureEnabledCheck(),t.market.register(this,H.c,this.floorsViewData),[this.cameraData,this.input,this.raycasterData]=await Promise.all([t.market.waitForData(c.M),t.getModuleBySymbol(o.PZ),t.market.waitForData(f.P)]),this.config.allowFloorChanges){const e=null===(i=this.config.startingFloorsVisibility)||void 0===i?void 0:i.lastIndexOf(1),t=null===(s=this.floorsData.getFloorAtIndex(e))||void 0===s?void 0:s.id,o=null===(r=this.sweepData.currentSweepObject)||void 0===r?void 0:r.floorId,n=o||t||null;(o||t)&&(this.log.debug(`Set initial floor to ${n} from current pose`),this.updateCurrentFloor(n))}const[h,m]=await Promise.all([t.getModuleBySymbol(o.kg),t.market.waitForData(u._)]);this.floorNavigation=new z(t,this.floorsData,this.floorsViewData,this.sweepData,this.cameraData,h,this.viewmodeData,this.updateCurrentFloor,m,this.settingsData),this.floorsSelectModeHelper=new $(this.floorsViewData,this.viewmodeData,this.cameraData.pose);const T=await t.getModuleBySymbol(o.hi),D=new S(this.floorsSelectModeHelper.getAngleModifier,this.floorsViewData,T,this.viewmodeData,this.raycasterData,m,(0,q.Jm)());t.addComponent(this,D),this.config.allowFloorChanges&&this.registerFloorHoverCursorEffect(),this.bindings.push(this.registerKeys(),t.subscribe(b.bS,this.applicationChanged),this.floorsData.onChanged(this.floorsViewData.updateViewData),t.subscribe(p.Z,this.onEndMoveToSweepMessage),t.subscribe(g.Z,function(e,t,i,s,o){const r=s.tryGetProperty(U.eC,!1);let n=r;return s=>{const a=e.tryGetData(w.k);if(!a||a&&a.isTourActive())return;!(0,y.Bw)(s.toMode)||r&&o.phase!==v.nh.PLAYING||(n=null!==i.currentFloorId);const h=(0,y.Bw)(s.fromMode),l=s.toMode===y.Ey.Floorplan,d=s.toMode===y.Ey.Dollhouse;if(!h||!d&&!l)return;const c=n||l?i.currentFloorId:null;t(new j.Vw(c,!0,0))}}(t.market,t.commandBinder.issueCommand,this.floorsViewData,this.settingsData,this.applicationData)),t.commandBinder.addBinding(j.Vw,this.onMoveToFloorCommand),t.commandBinder.addBinding(j.h9,this.onMoveToFloorIndexCommand),t.commandBinder.addBinding(j.EU,this.onShowAllFloorsCommand),t.commandBinder.addBinding(j.LW,this.onEnableAllFloorsOptionCommand),t.commandBinder.addBinding(j.tR,this.onDisableAllFloorsOptionCommand),t.subscribe(se.Z,this.updateFloorSelectMode),this.cameraData.pose.onChanged(this.updateFloorSelectMode),this.floorsViewData.onChanged(this.updateFloorSelectMode),this.settingsData.onPropertyChanged(ie.w,this.floorSelectFeatureEnabledCheck),this.settingsData.onPropertyChanged(oe.gx.FloorSelect,this.floorSelectFeatureEnabledCheck),this.applicationData.onPropertyChanged("application",this.floorSelectFeatureEnabledCheck)),this.updateFloorSelectMode(),this.sweepData.currentSweepObject&&this.updateCurrentFloor(this.sweepData.currentSweepObject.floorId)}onUpdate(){}async onMoveToFloorCommand(e){return this.floorNavigation.moveToFloor(e.floorId,e.suppressCameraMovement,e.transitionTime,e.focusPoint).nativePromise()}toggleFloors(e){e||null===this.floorsViewData.currentFloorId||this.updateCurrentFloor(null),this.config.allowFloorChanges=e,this.updateFloorSelectMode()}registerKeys(){return this.input.registerHandler(r.e,(async e=>{if(!this.cameraData.canTransition())return;const t=e.modifiers.shiftKey;if(e.state===h.M.PRESSED)switch(e.key){case a.R.UPARROW:t&&this.floorNavigation.moveFloorUp();break;case a.R.DOWNARROW:t&&this.floorNavigation.moveFloorDown();break;case a.R.R:this.floorNavigation.moveFloorUp();break;case a.R.F:this.floorNavigation.moveFloorDown();break;case a.R.Y:this.floorNavigation.moveToFloor(null)}}))}registerFloorHoverCursorEffect(){const e=Y.s.is((e=>J.$4.isRoomMesh(e)&&e.raycastEnabled&&this.floorsViewData.floorSelectable)),t=new K.V(this.input.registerMeshHandler(n.z,e,(e=>{this.engine.commandBinder.issueCommand(new X.u(ee.C.FINGER))})),this.input.registerMeshHandler(n.A,e,(e=>{this.engine.commandBinder.issueCommand(new X.u(ee.C.DEFAULT))})));t.cancel();const i=(()=>{let e=!1;return()=>{const i=this.floorsViewData.floorSelectHoverEnabled;i!==e&&(e=i,e?t.renew():(t.cancel(),this.engine.commandBinder.issueCommand(new X.u(ee.C.DEFAULT))))}})();i();const s=()=>{this.floorsViewData.floorSelectHoverEnabled=!1,i()},o=()=>{this.floorsViewData.floorSelectHoverEnabled=!0,i()};this.bindings.push(this.viewmodeData.onChanged(i),this.floorsViewData.onFloorSelectModeChange(i),this.floorsViewData.makeFloorChangeSubscription(i),this.engine.commandBinder.addBinding(j.TS,(async()=>s())),this.engine.commandBinder.addBinding(j.Md,(async()=>o())),t)}}},19280:(e,t,i)=>{"use strict";i.d(t,{p:()=>o});var s=i(25071);function o(e){return e.width<=s.MN}},82780:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var s=i(97542),o=i(92810),r=i(67678),n=i(41927),a=i(89553),h=i(5135),l=i(57053),d=i(64773),c=i(31362);class u extends s.Y{constructor(){super(...arguments),this.name="interactionmode",this.onPointerButtonEvent=e=>{let t=this.data.source;switch(e.device){case n._.TOUCH:t=l.f.Touch;break;case n._.MOUSE:t=l.f.Mouse;break;case n._.PEN:t=l.f.Pen;break;case n._.GAMEPAD:this.renderer.xr.enabled&&this.renderer.xr.isPresenting&&(t=l.f.XRController);break;default:this.log.debug("source:",e.device,e)}this.updateSource(t)},this.onKeyEvent=()=>{this.updateSource(l.f.Key)}}async init(e,t){this.engine=t,this.data=new h.Z,this.mobileBrowser=(0,c.tq)();const i=await t.getModuleBySymbol(o.Aj);this.renderer=i.threeRenderer,this.updateMode(this.getInteractionMode(),this.data.mode),this.engine.market.register(this,h.Z,this.data),t.getModuleBySymbol(o.PZ).then((e=>{this.bindings.push(e.registerHandler(r.er,this.onPointerButtonEvent),e.registerHandler(a.e,this.onKeyEvent))}))}onUpdate(e){const t=this.getInteractionMode();this.updateMode(t,this.data.mode)}getInteractionMode(){if(this.renderer.xr.enabled&&this.renderer.xr.isPresenting){const e=this.renderer.xr.getSession();if(null!==e&&e.inputSources.length>0)switch(e.inputSources[0].targetRayMode){case"gaze":case"screen":return l.s.VrOrientOnly;case"tracked-pointer":return l.s.VrWithTrackedController}return l.s.VrOrientOnly}return this.mobileBrowser?l.s.Mobile:l.s.Desktop}updateMode(e,t){e!==this.data.mode&&(this.data.updateMode(e),this.data.commit(),this.engine.broadcast(new d.m(this.data.mode,t)))}updateSource(e){e!==this.data.source&&(this.data.updateSource(e),this.data.commit(),this.engine.broadcast(new d.a(this.data.source)))}}},83977:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>h});var s=i(97542),o=i(92810),r=i(76957),n=i(76532),a=i(53954);class h extends s.Y{constructor(){super(...arguments),this.name="mesh-api-data-fixups"}async init(e,t){const{market:i}=t;[this.floorData,this.sweepData,this.meshData,this.meshQuery]=await Promise.all([i.waitForData(r.i),i.waitForData(a.Z),i.waitForData(n._),t.getModuleBySymbol(o.hi),t.getModuleBySymbol(o.Ve)]),this.assignSweepToFloors(),this.assignBoundingBoxesToFloors(),this.assignMissingSweepFloors()}assignBoundingBoxesToFloors(){const e=this.meshData.meshGroups.floors;this.floorData.iterate(((t,i)=>{const s=e.get(t.meshGroup);s&&(t.setBounds(s.boundingBox),t.setCenterOfMass(s.centerOfMass))})),this.floorData.commit()}assignMissingSweepFloors(){this.sweepData.getSweepList().forEach((e=>{var t;if(e.isUnplaced())return;let i;if(null===e.floorId||!this.floorData.hasFloor(e.floorId)){const s=this.meshQuery.floorIdFromObject(null===(t=this.meshQuery.nearestMeshInfo(e.position))||void 0===t?void 0:t.object);s&&this.floorData.hasFloor(s)&&(i=this.floorData.getFloor(s)),(null==i?void 0:i.id)&&i.id!==e.floorId&&(this.log.debug(`Setting ${e.alignmentType} sweep ${e.id} from floor ${e.floorId} to ${i.id}`),e.floorId=i.id,e.commit())}}))}assignSweepToFloors(){this.sweepData.getSweepList().forEach((e=>{var t;if(!e.isUnplaced()&&e.isAligned()){let i;if(e.floorId&&this.floorData.hasFloor(e.floorId))i=this.floorData.getFloor(e.floorId);else{const s=this.meshQuery.floorIdFromObject(null===(t=this.meshQuery.nearestMeshInfo(e.position))||void 0===t?void 0:t.object);s&&this.floorData.hasFloor(s)&&(i=this.floorData.getFloor(s))}i&&i.addSweep(e.position,e.floorPosition)}})),this.floorData.commit()}}},96782:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>m});var s=i(97542),o=i(92810),r=i(28269),n=i(76957);const a=(...e)=>function(t){return e.every((e=>e(t)))};var h=i(26269),l=i(81396),d=i(77161),c=i(80742),u=i(49416);class m extends s.Y{constructor(){super(...arguments),this.name="mesh-query",this.roomBoundData=null}async init(e,t){const{market:i}=t;[this.raycaster,this.floorData,this.roomData,this.layersData]=await Promise.all([t.getModuleBySymbol(o.fQ),i.waitForData(n.i),i.waitForData(r.Z),i.waitForData(c.R)]),i.waitForData(d.Z).then((e=>this.roomBoundData=e))}nearestMeshInfoOnFloor(e,t){const i=a(h.$4.isRoomMesh,this.matchesFloorId(t));return this.raycaster.picking.nearest((new l.Vector3).set(e.x,e.y,e.z),i)}nearestMeshInfo(e){return this.raycaster.picking.nearest((new l.Vector3).set(e.x,e.y,e.z),h.$4.isRoomMesh)}inferMeshIdsFromPoint(e,t,i=!0){const s=i&&(0,u.O)(this.roomBoundData,this.layersData,e.layerId),o=!!this.roomBoundData&&this.roomBoundData.hasRooms()&&s;let r=!s||!e.roomId||!this.roomData.get(e.roomId);const n=!e.floorId||!this.floorData.hasFloor(e.floorId);if(!n&&r&&o&&this.roomBoundData){const i=this.roomBoundData.findRoomIdForPosition(t,e.floorId);i&&(e.roomId=i,r=!1)}if(r||n){const i=n?h.$4.isRoomMesh:a(h.$4.isRoomMesh,this.matchesFloorId(e.floorId)),s=this.raycaster.picking.nearest((new l.Vector3).set(t.x,t.y,t.z),i);let d;if(this.roomBoundData&&this.roomBoundData.hasRooms()){if(s){const e=s.object.meshGroup;if(void 0!==e){const i=this.floorData.getFloorByMeshGroup(e);i&&(d={floorId:i.id,roomId:o&&this.roomBoundData.findRoomIdForPosition(t,i.id)||void 0})}}}else d=s&&this.roomIdFloorIdFromObject(s.object);if(d){const{roomId:t,floorId:i}=d;this.log.debug("data-fixup:",r?{roomId:t,prev:e.roomId}:"",n?{floorId:i,prev:e.floorId}:"",{data:e}),r&&(e.roomId=t),n&&(e.floorId=i)}else this.log.warn("Nearest Room/Floor not found for:",{point:t,data:e,invalidRoomId:r,invalidFloorId:n})}return e}roomIdFloorIdFromObject(e){if(!h.$4.hasMeshGroup(e))return;const t=this.floorData.getFloorByMeshGroup(e.meshGroup);if(void 0===t)return;const i=(0,u.O)(this.roomBoundData,this.layersData,null)&&h.$4.hasMeshSubgroup(e)?this.roomData.getByMeshSubgroup(e.meshSubgroup):void 0;return{floorId:t.id,roomId:null==i?void 0:i.id}}floorIdFromObject(e){if(h.$4.hasMeshGroup(e)){const t=this.floorData.getFloorByMeshGroup(e.meshGroup);return null==t?void 0:t.id}}mdsRoomIdFromObject(e){if(!(0,u.O)(this.roomBoundData,this.layersData,null)||!h.$4.hasMeshSubgroup(e)||!h.$4.hasMeshGroup(e))return;const t=this.roomData.getByMeshSubgroup(e.meshSubgroup);if(!t)return;const i=this.floorData.getFloorByMeshGroup(e.meshGroup);return t.floorId===(null==i?void 0:i.id)?t.id:void 0}mdsFloorIdFromObject(e){if(!h.$4.hasMeshGroup(e))return;const t=this.floorData.getFloorByMeshGroup(e.meshGroup);return t?t.id:void 0}matchesFloorId(e){return t=>{const i=this.roomIdFloorIdFromObject(t);return(null==i?void 0:i.floorId)===e}}}},70950:(e,t,i)=>{"use strict";i.d(t,{Z:()=>u});var s=i(67992),o=i(10385),r=i(15637),n=i(52498),a=i(97178),h=i(97998),l=i(98766),d=i(35895);const c=new h.Z("MeshTrimData");class u extends s.V{constructor(e,t){super(),this.maxTrimsPerFloor=a.t,this.maxAllFloorsTrims=a.t,this.numberOfTrims=0,this.meshTrimsByMeshGroup=new r.v,this.meshTrimsById=new r.v,this.onMeshGroupChangedCallbacks=new Set,this.onMeshTrimChangedCallbacks=new Set,this.updateMeshTrim=e=>{for(const t of this.onMeshTrimChangedCallbacks)t(e)},this.notifyMeshGroupChanges=e=>{for(const t of this.onMeshGroupChangedCallbacks)t(e)};for(const e of t){const t=new o.d;this.meshTrimsByMeshGroup.set(`${e}`,t)}1===t.length&&(this.singleFloorMeshGroup=t[0]),this.meshTrimsByMeshGroup.set(`${n.eh}`,new o.d);try{this.add(...Object.values(e))}catch(e){}}add(...e){const t=new Set;let i=!1;if(this.meshTrimsById.atomic((()=>{this.meshTrimsByMeshGroup.atomic((()=>{for(const s of e){void 0!==this.singleFloorMeshGroup&&(s.meshGroup=this.singleFloorMeshGroup);const e=s.meshGroup===n.eh,r=`${s.meshGroup}`;this.meshTrimsByMeshGroup.has(r)||this.meshTrimsByMeshGroup.set(r,new o.d);const a=this.meshTrimsByMeshGroup.get(r);a.length<(e?this.maxAllFloorsTrims:this.maxTrimsPerFloor)?(a.push(s),this.meshTrimsById.set(s.id,s),t.add(s.meshGroup),s.onChanged(this.updateMeshTrim)):(c.debugWarn("Trims exceed floor limit (trimId, meshGroup):",s.id,s.meshGroup),i=!0)}}))})),t.forEach((e=>{const t=this.meshTrimsByMeshGroup.get(`${e}`);this.sortList(t),this.reassignIndexes(t),this.notifyMeshGroupChanges(e)})),this.updateDerivedProperties(),i)throw new l.M("Exceeding max trims")}delete(...e){const t=new Set;this.meshTrimsByMeshGroup.atomic((()=>{for(const i of e){const e=this.meshTrimsByMeshGroup.get(`${i.meshGroup}`),s=e.indexOf(i);if(s>=0){e.splice(s,1)[0].enabled=!1,t.add(i.meshGroup),i.removeOnChanged(this.updateMeshTrim)}else c.error("Could not delete mesh trim:"+i.id)}})),t.forEach((e=>{const t=this.meshTrimsByMeshGroup.get(`${e}`);this.reassignIndexes(t),this.notifyMeshGroupChanges(e)})),this.meshTrimsById.atomic((()=>{e.forEach((e=>{this.meshTrimsById.delete(e.id)}))})),this.updateDerivedProperties()}updateDerivedProperties(){this.numberOfTrims=this.meshTrimsById.length,this.maxTrimsPerFloor=a.t-this.meshTrimsByMeshGroup.get(`${n.eh}`).length,this.maxAllFloorsTrims=a.t-this.getLongestTrimListLength(),this.commit()}onMeshGroupChanged(e){return(0,d.k1)((()=>this.onMeshGroupChangedCallbacks.add(e)),(()=>this.onMeshGroupChangedCallbacks.delete(e)))}onMeshTrimChanged(e){return(0,d.k1)((()=>this.onMeshTrimChangedCallbacks.add(e)),(()=>this.onMeshTrimChangedCallbacks.delete(e)))}getTrimById(e){return this.meshTrimsById.get(e)}getTrim(e,t){return this.meshTrimsByMeshGroup.has(`${e}`)?this.meshTrimsByMeshGroup.get(`${e}`).get(t):null}getTrimsForMeshGroup(e){return this.meshTrimsByMeshGroup.has(`${e}`)?this.meshTrimsByMeshGroup.get(`${e}`).values():[]}*activeTrimsForMeshGroup(e){if(this.meshTrimsByMeshGroup.has(`${e}`))for(const t of this.meshTrimsByMeshGroup.get(`${e}`))t.enabled&&(yield t);if(this.meshTrimsByMeshGroup.has(`${n.eh}`))for(const e of this.meshTrimsByMeshGroup.get(`${n.eh}`))e.enabled&&(yield e)}reassignIndexes(e){e.forEach(((e,t)=>{e.index=t}))}sortList(e){e.sort(((e,t)=>e.index-t.index))}getLongestTrimListLength(){let e=0;return this.meshTrimsByMeshGroup.keys.forEach((t=>{if(t===`${n.eh}`)return;const i=this.meshTrimsByMeshGroup.get(t);e=Math.max(i.length,e)})),e}}},98766:(e,t,i)=>{"use strict";i.d(t,{M:()=>o});var s=i(2224);class o extends s.y{constructor(e){super(e),this.name="TooManyTrimsError"}}},92256:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>_});var s=i(97542),o=i(2541),r=i(64567),n=i(71954),a=i(73698),h=i(50540),l=i(95912),d=i(38399),c=i(70950);class u{constructor(e){this.config=e}serialize(e){const{serializer:t}=this.config;if(!e||!t)return null;const i={};for(const s in e){const o=t.serialize(e[s]);o&&(i[s]=o)}return i}deserialize(e){const{deserializer:t}=this.config;if(!e||!t)return{};const i={};for(const s in e){const o=t.deserialize(e[s]);o&&(i[s]=o)}return i}}var m=i(3907),p=i(2569),g=i(9133),f=i(75287),y=i(39880),w=i(81396);const v=new w.Vector3;class b extends f.T{constructor(e,t,i,s,o,r,n=new Date,a=new Date,h,l,d,c){super(),this.position=e,this.scale=t,this.rotation=i,this.enabled=o,this.meshGroup=r,this.created=n,this.modified=a,this.discardContents=!0,this.activeInPanoMode=!0,this._rotationMatrix=new w.Matrix4,this._matrix=new w.Matrix4,this.id=h||`${this.meshGroup}`+(0,y.O1)(11),this.index=s,this.name=l,this.discardContents=void 0===d||d,this.activeInPanoMode=void 0===c||c}get sid(){return this.id}isPointTrimmed(e,t){return!!this.enabled&&(!(t&&!this.activeInPanoMode)&&(this.discardContents?this.isPointInside(e):!this.isPointInside(e)))}isPointInside(e){this._matrix.compose(this.position,this.rotation,this.scale),this._matrix.invert();const t=v.copy(e).applyMatrix4(this._matrix);return t.x<.5&&t.x>-.5&&t.y<.5&&t.y>-.5&&t.z<.5&&t.z>-.5}updateRotationMatrix(){const e=this.rotation,t=this.rotation.clone().set(e.x,e.y,e.z,-e.w);this._rotationMatrix.makeRotationFromQuaternion(t.normalize())}get rotationMatrix(){return this.updateRotationMatrix(),this._rotationMatrix}}var M=i(97998),T=i(29282);const D=new M.Z("JsonStoreMeshTrimDeserializer"),x=[{path:["position","x"],type:"number"},{path:["position","y"],type:"number"},{path:["position","z"],type:"number"},{path:["scale","x"],type:"number"},{path:["scale","y"],type:"number"},{path:["scale","z"],type:"number"},{path:["rotation","x"],type:"number"},{path:["rotation","y"],type:"number"},{path:["rotation","z"],type:"number"},{path:["rotation","w"],type:"number"},{path:["index"],type:"number"},{path:["enabled"],type:"boolean"},{path:["meshGroup"],type:"number"},{path:["id"],type:"string"},{path:["created"],type:"string"},{path:["modified"],type:"string"}];class S{constructor(){this.deserialize=e=>{if(!this.isValid(e))return D.debug("Unable to deserialize invalid mesh trim data",e),null;const{position:t,scale:i,rotation:s,index:o,enabled:r,meshGroup:n,created:a,modified:h,id:l,name:d,discardContents:c,activeInPanoMode:u}=e;return new b(p.ep.fromVisionVector(t),p.ep.fromVisionVector(i),p.ep.fromVisionQuaternion(s),o,r,n,(0,g.p)(a),(0,g.p)(h),l,d,c,u)}}isValid(e){if(!e||"object"!=typeof e)return!1;const t=e;return(0,T.r)(t.meshGroup)||(t.meshGroup=t.floorIndex),x.every((t=>this.hasRequiredField(e,t)))}hasRequiredField(e,t){try{return typeof t.path.reduce(((i,s)=>{if("object"==typeof i&&null!==i)return i[s];throw new Error(`data ${JSON.stringify(e)} must be addressable by ${t.path.join(".")} with a value of type ${t.type}`)}),e)===t.type}catch(e){return D.debug(e),!1}}}var C=i(59296);class P{serialize(e){if(!e)return null;const{position:t,scale:i,rotation:s,index:o,enabled:r,meshGroup:n,created:a,modified:h,id:l,name:d,discardContents:c,activeInPanoMode:u}=e;return{position:(0,C.m)(p.ep.toVisionVector(t)),scale:(0,C.m)(p.ep.toVisionVector(i)),rotation:(0,C.J5)(p.ep.toVisionQuaternion(s)),index:o,enabled:r,meshGroup:n,created:(0,g.U)(a),modified:(0,g.U)(h),id:l,name:d,discardContents:c,activeInPanoMode:u}}}class O extends m.MU{constructor(e,t,i){const s=new S,o=new P,r=new u({deserializer:s,serializer:o});super({queue:e,path:`${t}/api/v1/jsonstore/model/trims/${i}`,batchUpdate:!0,deserialize:e=>r.deserialize(e),serialize:e=>r.serialize(e)})}}var k=i(19663);class B extends k.m{constructor(e){super(),this.payload=e,this.id="CREATE_MESH_TRIM"}}class A extends k.m{constructor(e){super(),this.payload=e,this.id="DELETE_MESH_TRIM"}}class F extends k.m{constructor(e){super(),this.payload=e,this.id="MOVE_MESH_TRIM_ALL_FLOORS"}}var E=i(98766),R=i(52498),I=i(76532);const{TRIM:V}=d.Z.WORKSHOP;class _ extends s.Y{constructor(){super(...arguments),this.name="trim-data",this.createMeshTrim=async e=>{try{this.data.add(e)}catch(e){if(e instanceof E.M){const e=V.MAX_TRIMS_ERROR_MESSAGE;this.engine.commandBinder.issueCommand(new h.I(e,{throttle:0,type:a.N.ERROR}))}}},this.deleteMeshTrim=async e=>{this.data.delete(e)},this.save=async()=>{const e=this.monitor.getDiffRecord();if(this.monitor.clearDiffRecord(),!e.length)return;const t={};for(const i of e)switch(i.action){case n.KI.added:case n.KI.updated:t[i.index]=this.data.getTrimById(i.index);break;case n.KI.removed:t[i.index]=null}try{await this.store.update(t)}catch(e){this.log.debug("error when writing to json storage"),this.log.debug(e);const t=V.UNABLE_TO_SAVE_CHANGES_ERROR_MESSAGE;this.engine.commandBinder.issueCommand(new h.I(t,{throttle:30,type:a.N.ERROR}))}},this.moveMeshTrimToAllFloors=async e=>{const t=e.enabled;this.deleteMeshTrim(e),e.meshGroup=R.eh,e.enabled=t,this.createMeshTrim(e)},this.onMeshTrimsChanged=(0,l.P)(this.save,1e3)}async init(e,t){this.engine=t,this.store=new O(e.queue,e.baseUrl,e.baseModelId);let i={};try{i=await this.store.read()||{}}catch(e){this.log.debug("error when reading from json storage"),this.log.debug(e)}const s=[...(await t.market.waitForData(I._)).meshGroups.floors.keys()].sort();this.data=new c.Z(i,s),this.monitor=new r.c(this.data.meshTrimsById,{aggregationType:o.E.Immediate}),t.market.register(this,c.Z,this.data),this.bindings.push(t.commandBinder.addBinding(B,this.createMeshTrim),t.commandBinder.addBinding(A,this.deleteMeshTrim),t.commandBinder.addBinding(F,this.moveMeshTrimToAllFloors)),this.monitor.onChanged(this.onMeshTrimsChanged)}}},22533:(e,t,i)=>{"use strict";var s;i.d(t,{S:()=>s}),function(e){e[e.Standard=0]="Standard",e[e.Depth=1]="Depth",e[e.Transparent=2]="Transparent",e[e.Wireframe=3]="Wireframe",e[e.UV=4]="UV"}(s||(s={}))},17988:(e,t,i)=>{"use strict";i.d(t,{F:()=>n});var s=i(12529),o=i(81396),r=i(53203);class n extends o.Mesh{constructor(e){super(),this.roomMesh=e,this.excludeFromOctree=!0,this.layers.mask=e.layers.mask,this.name=`DepthPassRoomMesh:${e.meshGroup}-${e.meshSubgroup}`,this.renderOrder=s.z.ghostFloorDepthPrepass,this.visible=!1,this.roomMesh.onOpacityUpdate=e=>{this.visible=e<r.xx.FADE_OPAQUE},this.roomMesh.onBuild=()=>{this.geometry=this.roomMesh.geometry,this.material=this.roomMesh.material},this.roomMesh.onMaterialUpdate=()=>{this.material=this.roomMesh.material};let t=!0,i=!0;this.onBeforeRender=(e,s,o,r,n,a)=>{this.roomMesh.updateUniforms(n,a),t=n.colorWrite,i=n.depthWrite,n.colorWrite=!1,n.depthWrite=!0},this.onAfterRender=(e,s,o,r,n,a)=>{n.colorWrite=t,n.depthWrite=i}}}},1134:(e,t,i)=>{"use strict";i.d(t,{e:()=>r});var s=i(81396),o=i(35895);class r extends s.Object3D{constructor(){super(...arguments),this.boundingBox=new s.Box3,this.size=new s.Vector3,this.center=new s.Vector3,this._detail="default",this._chunks=[],this.onChunksLoaded=new Set}get detail(){return this._detail}get chunks(){return this._chunks}get visibleChunks(){return this._chunks}notifyOnChunksLoaded(e){return(0,o.k1)((()=>this.onChunksLoaded.add(e)),(()=>this.onChunksLoaded.delete(e)),!0)}dispose(){for(const e of this._chunks)e.dispose();this._chunks.length=0}overrideMaxDetail(e){}}},92324:(e,t,i)=>{"use strict";i.d(t,{s:()=>o});var s=i(67992);class o extends s.V{constructor(){super(),this.name="room-mesh-data",this.floors=new Set,this.rooms=new Set}}},51411:(e,t,i)=>{"use strict";i.d(t,{n:()=>o});var s=i(19663);class o extends s.m{constructor(e,t,i){super(),this.id="MESH_PREVIEW_POSITION",this.payload={enabled:e,previewCirclePosition:t,size:i}}}},68191:(e,t,i)=>{"use strict";i.d(t,{U:()=>r});var s=i(19663),o=i(22533);class r extends s.m{constructor(e){super(),this.id="SET_CHUNK_RENDER_MODE",this.payload={mode:e}}}r.modes=o.S},24048:(e,t,i)=>{"use strict";var s;i.d(t,{k:()=>s}),function(e){e.Mesh="mesh",e.PanoramaMesh="mesh.inside",e.PanoramaCube="cubemap.inside",e.Hidden="mesh.hidden"}(s||(s={}))},20152:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>Ne});var s=i(97542),o=i(61864),r=i(92810),n=i(54244),a=i(90304),h=i(9037),l=i(26568),d=i(53954),c=i(70950),u=i(84366),m=i(23254),p=i(95882),g=i(23885),f=i(81396),y=i(33320);let w=!1;var v=i(51411),b=i(24048),M=i(80626),T=i(76532),D=i(97178);const x={meshTrimMatrices:[],meshTrimsDiscardContents:[],hasKeepVolume:!1};class S{constructor(e){this.floorUniforms={},this.sharedFloorUniforms={},this.isPanoMode=e}setMeshTrim(e){const{meshGroup:t,index:i,discardContents:s}=e,o=this.getEmptyCacheUniforms(),r=this.getFloorUniforms(t).meshTrimMatrices.slice();this.computeTrimMatrixFromTrim(e,r[i]),o.meshTrimMatrices=r,o.meshTrimsDiscardContents=this.setMeshTrimDiscardContents(t,i,s),this.setFloorUniforms(t,o)}updateMeshTrimArrays(e,t){const i=[],s=[];let o=!1;t.forEach((e=>{i.push(this.computeTrimMatrixFromTrim(e)),s.push(e.discardContents),o||(o=e.enabled&&!e.discardContents)})),this.setFloorUniforms(e,{meshTrimMatrices:i,meshTrimsDiscardContents:s,hasKeepVolume:o});"-1"==`${e}`?Object.keys(this.sharedFloorUniforms).forEach((e=>{this.updateSharedFloorUniforms(e)})):this.updateSharedFloorUniforms(e)}computeTrimMatrixFromTrim(e,t=new f.Matrix4){return e.enabled&&(!this.isPanoMode||this.isPanoMode&&e.activeInPanoMode)?(t.compose(e.position,e.rotation,e.scale),t.invert()):t.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0),t}setMeshTrimDiscardContents(e,t,i){const s=this.getFloorUniforms(e).meshTrimsDiscardContents.slice();return s[t]=i,s}updateSharedFloorUniforms(e){const t=this.getFloorUniforms(e),i=this.getFloorUniforms(-1),s=new f.Matrix4;s.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);const o=this.concatUniformArrays(i.meshTrimMatrices,t.meshTrimMatrices,s),r=this.concatUniformArrays(i.meshTrimsDiscardContents,t.meshTrimsDiscardContents,!0);this.setSharedFloorUniforms(e,{meshTrimMatrices:o,meshTrimsDiscardContents:r,hasKeepVolume:i.hasKeepVolume||t.hasKeepVolume})}concatUniformArrays(e,t,i){const s=e.concat(t).slice(0,D.t);for(let e=s.length;e<D.t;e++)s.push(i);return s}getFloorUniforms(e){return this.floorUniforms[`${e}`]||this.getEmptyCacheUniforms()}setFloorUniforms(e,t){this.floorUniforms[`${e}`]=t}getSharedFloorUniforms(e){return this.sharedFloorUniforms[`${e}`]||this.getEmptyCacheUniforms()}setSharedFloorUniforms(e,t){this.sharedFloorUniforms[`${e}`]=t}getEmptyCacheUniforms(){return Object.assign({},x)}}var C=i(39880),P=i(69984),O=i(97998),k=i(34029),B=i(52223),A=i(53203),F=i(22533),E=i(34742),R=i(12529),I=i(21270),V=i(93377);class _ extends V.b{constructor(e,t,i,s){const o={};for(const e of t)o[e]=!0;super({extensions:{derivatives:!0},fragmentShader:D.Z.modelChunk.fragmentShader,vertexShader:D.Z.modelChunk.vertexShader,uniforms:i,name:e,defines:o}),this.getSide=s,this.capabilities=t}get side(){return this.getSide()}set side(e){}}var L=i(75730),N=i(35895);function U(e,t){return null==t?t:t.isVector3||t.isVector4||t.isMatrix3||t.isMatrix4||t.isColor?null!=e?(e.copy(t),e):t.clone():t}let z,G=1;try{z=(0,L.k7)(0,0)}catch(e){}const H=5+D.t,j=[{key:E.h.PanoTextureTransition,enabled:function(e){return e.progress.value>0&&e.progress.value<1&&e.pano0Map.value!==e.pano1Map.value},dependsOn:[E.h.PanoTexture],uniformsUsed:["progress","pano0Map","pano1Map"]},{key:E.h.PanoTexture,enabled:function(e){return e.panoOpacity.value>0},dependsOn:[],uniformsUsed:["panoOpacity"]},{key:E.h.ColorOverlay,enabled:function(e){return null!==e.colorOverlay.value},dependsOn:[],uniformsUsed:["colorOverlay"]},{key:E.h.MeshPreviewSphere,enabled:function(e){return null!==e.meshPreviewCenter.value},dependsOn:[E.h.MeshTexture],uniformsUsed:[]},{key:E.h.MeshTexture,enabled:function(e){return e.meshOpacity.value>0&&e.map.value},dependsOn:[],uniformsUsed:["meshOpacity","map"]},{key:E.h.Wireframe,enabled:function(e){return!1},dependsOn:[],uniformsUsed:[]},{key:E.h.FlatShading,enabled:function(e){return!1},dependsOn:[],uniformsUsed:[]},{key:E.h.PanoOverlay,enabled:function(e){return!!e.overlay0Map.value},dependsOn:[E.h.PanoTexture],uniformsUsed:["overlay0Map"]},{key:E.h.PanoOverlayTransition,enabled:function(e){return!!(e.progress.value>0&&e.progress.value<1&&e.overlay0Map.value&&e.overlay1Map.value&&e.overlay0Map.value!==e.overlay1Map.value)},dependsOn:[E.h.PanoOverlay,E.h.PanoTexture,E.h.PanoTextureTransition],uniformsUsed:["progress","overlay0Map","overlay1Map"]},{key:E.h.MeshTrimVertex,enabled:function(e,{maxVaryings:t}){return t>H&&e.meshTrimMatrices.value.some((e=>!!e.elements[15]))},dependsOn:[E.h.MeshTexture],uniformsUsed:["meshTrimMatrices","meshTrimsDiscardContents"]},{key:E.h.MeshTrimPixel,enabled:function(e,{maxVaryings:t}){return t<=H&&e.meshTrimMatrices.value.some((e=>!!e.elements[15]))},dependsOn:[E.h.MeshTexture],uniformsUsed:["meshTrimMatrices","meshTrimsDiscardContents"]},{key:E.h.FloorTrimVertex,enabled:function(e){return!!(e.floorTrimHeight.value<1&&1===e.opacity.value)},dependsOn:[E.h.MeshTexture],uniformsUsed:["floorTrimHeight"]},{key:E.h.FloorTrimPixel,enabled:function(e){return!!(e.floorTrimHeight.value<1&&1===e.opacity.value)},dependsOn:[E.h.MeshTexture],uniformsUsed:["floorTrimHeight"]}],Q=new Set(j.map((e=>e.uniformsUsed)).reduce(((e,t)=>e.concat(t)),[])),Z=new Set(["progress","panoOpacity","meshOpacity","pano0Map","pano0Position","pano0Matrix1","pano0Matrix2","pano1Map","pano1Position","pano1Matrix1","pano1Matrix2","overlay0Map","overlay0Matrix","overlay1Map","overlay1Matrix"]);class W{constructor(e,t,i,s,o="",r=!1){this.meshGroup=e,this.meshSubgroup=t,this.geometry=i,this.sharedState=s,this.textureName=o,this.id=G++,this.name="",this.lod=I.V.Standard,this.capabilityOverrides={},this.onMaterialUpdate=new Set,this.uniformCache=W.getUniformDefaults(),this.opacity=1,this.temp={m1:new f.Matrix4,m2:new f.Matrix4,quat:new f.Quaternion,m3:null},this.standardMaterial=this.getChunkMaterial(this.getCapabilities(),!1),this.updateRenderingMode(),r&&this.setMaterialsUniform(s.globalUniforms)}dispose(){this.geometry.dispose()}static getUniformDefaults(){const e={};for(const t in D.Z.modelChunk.uniforms){const i=f.UniformsUtils.clone(D.Z.modelChunk.uniforms[t]);for(const t in i)e[t]=i[t]}return e}set material(e){if(this._material!==e&&this.onMaterialUpdate)for(const t of this.onMaterialUpdate.values())t(e);this._material=e}get material(){return this._material}notifyOnMaterialUpdated(e){return(0,N.k1)((()=>this.onMaterialUpdate.add(e)),(()=>this.onMaterialUpdate.delete(e)),!0)}setMeshTexture(e){this.setMaterialsUniform({map:e})}getColorOverlay(){return this._colorOverlay}setColorOverlay(e){this._colorOverlay!==e&&(this.setMaterialsUniform({colorOverlay:e}),this._colorOverlay=e)}setMeshTextureOpacity(e){e!==this._meshTextureOpacity&&(this.setMaterialsUniform({meshOpacity:e,panoOpacity:1-e}),this._meshTextureOpacity=e)}setProgress(e){this._progress!==e&&(this.setMaterialsUniform({progress:e}),this._progress=e)}needsTransparent(){return this.opacity<A.xx.FADE_OPAQUE}setOpacity(e){this.opacity=e;const t=this.needsTransparent();return this.onOpacityUpdate&&this.onOpacityUpdate(e),t!==this._material.transparent}getOpacity(){return this.opacity}setTime(e){this.sharedState.renderingMode===F.S.Wireframe&&this.setMaterialsUniform({time:e})}setMeshPreviewSphere(e,t=.3){this.setMaterialsUniform({meshPreviewCenter:e,meshPreviewSize:t})}setWireframe(e){if(e){if(this.geometry.getIndex()){const e=this.geometry;e.boundsTree&&(e.boundsTree.geometry=this.geometry.clone()),this.geometry.copy(this.geometry.toNonIndexed())}(0,L.ko)(this.geometry)}this.overrideCapability(E.h.Wireframe,e)}setFlatShading(e){e&&this.geometry.computeVertexNormals(),this.overrideCapability(E.h.FlatShading,e)}getCapabilities(){const e=new Set;for(const t in this.capabilityOverrides)this.capabilityOverrides[t]&&e.add(t);for(const t of j)if(!e.has(t.key)&&t.enabled(this.uniformCache,this.sharedState)){e.add(t.key);for(const i of t.dependsOn)e.add(i)}return e}overrideCapability(e,t){this.capabilityOverrides[e]=t,this.updateMaterialCapabilities()}updateMaterialCapabilities(){const e=this.getCapabilities(),t=this.standardMaterial,i=this.needsTransparent();if((0,C.TH)(t.capabilities,e)&&i===t.transparent)return this._material;const s=this.getChunkMaterial(e,i);return this.standardMaterial=s,this.sharedState.renderingMode===F.S.Standard&&(this.material=s),this._material}getChunkMaterial(e,t){let i="chunkMaterial_";for(const t of j)i+=e.has(t.key)?"1":"0";const s=-1===this.meshGroup&&-1===this.meshSubgroup;i+=s?"f":t?"1":"0";const{chunkMaterials:o}=this.sharedState;if(!o[i]){const r=new _(i,e,this.getUniformsForCapabilities(e),(()=>s?f.BackSide:this.sharedState.side));s?(r.transparent=!0,r.depthWrite=!1):(r.transparent=t,r.depthWrite=!t),o[i]=r}return o[i]}getUniformsForCapabilities(e){const t={};for(const i of e){const e=D.Z.modelChunk.uniforms[i];for(const i in e)t[i]=f.UniformsUtils.clone(this.uniformCache[i])}return t}setMaterialsUniform(e,t=!1){let i,s=!1;const o=this.uniformCache.meshPreviewCenter.value;for(const r in e){let n=!1;const a=e[r];if(n="meshPreviewCenter"!==r?n||Q.has(r):n||null===o!=(null===a),!(r in this.uniformCache))throw new Error(`Uniform ${r} does not exist in Chunk`);const h=this.uniformCache[r];h.value!==a&&(h.value=U(h.value,a),"opacity"===r&&(i=a),t&&Z.has(r)&&(this.sharedState.globalUniforms[r]=U(this.sharedState.globalUniforms[r],a)),s=s||n)}return void 0!==i&&(s=this.setOpacity(i)||s),s&&this.updateMaterialCapabilities(),this._material}updateRenderingMode(){const{renderingMode:e,modeMaterials:t}=this.sharedState;e===F.S.Standard?this.material=this.standardMaterial:this.material=t.get(e)}setProjectedPano(e,t,i,s,o=!1){let r=1===e?"pano1Map":"pano0Map";const n={};n[r]=s||z,t&&(r=1===e?"pano1Position":"pano0Position",n[r]=t),i&&t&&(r=1===e?"pano1Matrix":"pano0Matrix",this.temp.m1.makeRotationFromQuaternion(this.temp.quat.copy(i).invert()),this.temp.m2.makeScale(-1,1,1),n[`${r}1`]=this.temp.m1,n[`${r}2`]=this.temp.m2),this.setMaterialsUniform(n,o)}setOverlayPano(e,t,i,s=!1){const o=`overlay${e}`,r={};if(t){this.temp.m3||(this.temp.m3=new f.Matrix4);const e=this.temp.m3.makeRotationFromQuaternion(t);r[o+"Matrix"]=e}r[o+"Map"]=i||z,this.setMaterialsUniform(r,s)}onBeforeDraw(e){if(this.sharedState.renderingMode===F.S.Standard){for(const t of Object.keys(e.uniforms))this.uniformCache[t]&&null!==this.uniformCache[t].value&&(e.uniforms[t].value=this.uniformCache[t].value);e.uniformsNeedUpdate=!0}}getSortKey(){var e,t,i;return null!==(i=null===(t=null===(e=this.uniformCache.map)||void 0===e?void 0:e.value)||void 0===t?void 0:t.id)&&void 0!==i?i:0}}const $=new f.Vector3(0,0,0),q=new f.Vector3(100,100,100),K=new f.Vector3,Y=new f.Vector3,J=new f.Box3;class X extends f.Mesh{constructor(e){super(),this.bounds=new f.Box3,this.geometry=new f.BoxGeometry(1,1,1),this.geometry.computeBoundingBox(),this.geometry.computeBoundingSphere(),this.chunk=new W(-1,-1,this.geometry,e);const t=e=>{this.material=e};this.chunk.notifyOnMaterialUpdated(t),t(this.chunk.material),this.name="FallbackMesh",this.renderOrder=R.z.boundingSkybox,this.setFromCenterAndSize($,q),this.onBeforeRender=(e,t,i,s,o,r)=>{o instanceof f.RawShaderMaterial&&this.chunk.onBeforeDraw(o)}}setBounds(e){if(this.bounds.equals(e))return;this.bounds.copy(e);const t=e.getSize(K);this.position.copy(e.getCenter(Y)),this.scale.set(t.x,t.y,t.z),this.updateMatrixWorld(!0)}setFromCenterAndSize(e,t=q){this.setBounds(J.setFromCenterAndSize(e,t))}}var ee=i(14564),te=i(75810),ie=i(68191),se=i(19663);class oe extends se.m{constructor(e,t,i){super(),this.id="SET_PANO_OVERLAY",this.payload={sweepId:e,texture:t,quaternion:i}}}const re=new O.Z("modelrenderer");class ne{constructor(e,t,i,s,o,r,n,a,h){this.scene=e,this.container=t,this.mesh=i,this.panoRenderer=s,this.meshData=o,this.sweepData=r,this.applicationData=n,this.chunkSharedState=a,this.renderOptions=h,this.chunkRenderingModeOverride=null,this.lastChunkRenderingModeOverride=null,this.fallbackMesh=new X(this.chunkSharedState),this.overlayColors=[],this.toolMeshColorEnabled=!1,this.TOOL_MESH_COLOR_OVERLAY=new f.Vector4(0,0,0,.3),this.overlayTextures=[{sweepId:void 0,texture:void 0,renderTarget:new f.WebGLCubeRenderTarget(2048,{format:f.RGBAFormat}),quaternion:new f.Quaternion},{sweepId:void 0,texture:void 0,renderTarget:new f.WebGLCubeRenderTarget(2048,{format:f.RGBAFormat}),quaternion:new f.Quaternion}],this.overlayEnabled=!1,this.bindings=[],this.updateFallbackMesh=(()=>{const e=new f.Box3;return t=>{if(this.sweepData.currentAlignedSweepObject&&this.viewmodeData.isInside()){const t=e.copy(this.meshData.extendedBounds).expandByScalar(.2);this.fallbackMesh.setBounds(t)}else this.cameraData&&this.fallbackMesh.setFromCenterAndSize(this.cameraData.pose.position);this.fallbackMesh.material&&(this.fallbackMesh.material.transparent=!(t<1e-5))}})(),this.debugColorizeChunks=(()=>{let e=!1;return(t,i)=>{const s=new f.Vector4(1,1,1,0);e=!e,(e=>{for(const t of this.mesh.chunks){const o=i?100*t.id:100*t.meshSubgroup,r=e?(0,P.G1)(.5,o):s;t.setColorOverlay(r)}})(t||e)}})()}init(){}dispose(){}async activate(e){this.engine=e,[this.viewmodeData,this.settings,this.cameraData,this.renderer]=await Promise.all([e.market.waitForData(m.O),e.market.waitForData(k.e),e.market.waitForData(h.M),e.getModuleBySymbol(r.Aj)]),this.scene.add(this.container),this.scene.add(this.fallbackMesh),this.fallbackMesh.layers.mask=this.container.layers.mask,this.updateRenderState(),this.bindings.push(this.viewmodeData.onChanged(this.updateRenderState.bind(this)),this.sweepData.onChanged(this.updateRenderState.bind(this)),this.settings.onChanged(this.updateRenderState.bind(this)),e.commandBinder.addBinding(ee.u,this.onMeshOverlayCommand.bind(this)),e.commandBinder.addBinding(te.I,this.toggleMeshOverlayColor.bind(this)),e.commandBinder.addBinding(ie.U,this.onSetChunkRenderStateCommand.bind(this)),e.commandBinder.addBinding(oe,this.onPanoOverlayCommand.bind(this))),this.bindings.push(this.mesh.notifyOnChunksLoaded((e=>{for(const t of this.overlayColors)t(e)}))),this.renderOptions.colorizeRooms&&this.debugColorizeChunks(!0),this.renderOptions.colorizeChunks&&this.debugColorizeChunks(!0,!0),this.renderOptions.wireframe&&this.toggleWireframe(!0),this.setupDebugMenu(e)}deactivate(){for(const e of this.bindings)e.cancel();this.bindings=[],this.scene.remove(this.container),this.scene.remove(this.fallbackMesh),this.currentSweepId&&this.panoRenderer.freeTexture(this.currentSweepId),this.currentSweepId=null,this.targetSweepId=null}updateSweepRenderTarget(e,t,i,s){const o=this.panoRenderer.useTexture(t);if(o){let t=!0;for(const r of this.allChunks())r.setProjectedPano(e,i,s,o,t),t=!1}}*allChunks(){yield*this.mesh.chunks,yield this.fallbackMesh.chunk}updateExistingTexture(e,t,i,s){let o=!0;for(const r of this.allChunks())e===this.currentSweepId&&r.setProjectedPano(0,i,s,t,o),e===this.targetSweepId&&r.setProjectedPano(1,i,s,t,o),o=!1}render(){}beforeRender(){var e;const{floorOpacity:t}=this.meshData.meshGroupVisuals;for(const i of this.mesh.visibleChunks)i.setMaterialsUniform({meshOpacity:this.meshData.meshTextureOpacity.value,panoOpacity:1-this.meshData.meshTextureOpacity.value,opacity:null!==(e=t.get(i.meshGroup))&&void 0!==e?e:1,progress:this.sweepData.transition.progress.value});const i=(0,g.w2)(this.meshData.meshTextureOpacity.value,0,1,1);this.fallbackMesh.chunk.setMeshTextureOpacity(i),this.fallbackMesh.chunk.setProgress(this.sweepData.transition.progress.value),this.updateFallbackMesh(i)}updateChunkMaterialMode(e,t){const i=e?f.DoubleSide:f.FrontSide;this.chunkSharedState.side=i,this.chunkSharedState.renderingMode=t||F.S.Standard;for(const e of this.mesh.chunks)e.updateRenderingMode()}updateRenderState(){if(this.viewmodeData.currentMode!==this.lastViewmode||this.lastChunkRenderingModeOverride!==this.chunkRenderingModeOverride){this.lastChunkRenderingModeOverride=this.chunkRenderingModeOverride;const e=this.viewmodeData.isInside();this.updateChunkMaterialMode(e,this.chunkRenderingModeOverride),this.lastViewmode=this.viewmodeData.currentMode}if(this.viewmodeData.transition.active&&(0,p.Bw)(this.viewmodeData.transition.to)||this.viewmodeData.isInside()){const e=this.sweepData.currentSweep,t=this.sweepData.transition,i=t.active&&(this.applicationData.phase===n.nh.PLAYING||this.applicationData.phase===n.nh.STARTING),s=i?t.from:e,o=i?t.to:e,r=this.currentSweepId,a=this.targetSweepId;this.currentSweepId=s||null,this.targetSweepId=o||null,this.handleSweepChange(0,r,this.currentSweepId),this.handleSweepChange(1,a,this.targetSweepId)}if(this.overlayEnabled){const e=this.overlayTextures.find((e=>e.sweepId===this.currentSweepId)),t=this.overlayTextures.find((e=>e.sweepId===this.targetSweepId));let i=!0;for(const s of this.allChunks())s.setOverlayPano(0,e?e.quaternion:void 0,e?e.texture:void 0,i),s.setOverlayPano(1,t?t.quaternion:void 0,t?t.texture:void 0,i),i=!1}}handleSweepChange(e,t,i){if(t!==i&&(t&&this.panoRenderer.freeTexture(t),i)){const t=this.sweepData.getSweep(i);this.updateSweepRenderTarget(e,i,t.position,t.rotation)}}async onPanoOverlayCommand(e){this.overlayEnabled=!0;const t=t=>{const i=t.renderTarget;t.renderTarget.width=e.texture.image.width,t.renderTarget.height=e.texture.image.height,t.sweepId=e.sweepId,t.texture=i.texture,t.quaternion=e.quaternion,this.renderer.cwfRenderer.copyCubemap(e.texture,t.renderTarget)};let i=!1;for(const s of this.overlayTextures)i||s.sweepId!==e.sweepId||(t(s),i=!0);for(const e of this.overlayTextures)i||e.sweepId===this.targetSweepId||e.sweepId===this.currentSweepId||(t(e),i=!0);this.updateRenderState()}async onMeshOverlayCommand(e){let t=()=>!0;switch(e.selectBy){case ee.u.selectBy.all:t=()=>!0,this.overlayColors.length=0;break;case ee.u.selectBy.byMeshGroup:t=t=>t.meshGroup===e.index;break;case ee.u.selectBy.byMeshSubGroup:t=t=>t.meshSubgroup===e.index}if(!t)return;let i="rand";e.colorStyle===ee.u.colorBy.explicit&&(i=e.color?new f.Vector4(e.color.x,e.color.y,e.color.z,e.color.w):null);const s=s=>{for(const o of s)t(o)&&o.setColorOverlay("rand"===i?(0,P.G1)(e.alpha):i)};s([...this.allChunks()]),e.selectBy===ee.u.selectBy.all&&null===i||this.overlayColors.push(s)}async toggleMeshOverlayColor({enabled:e}){if(e!==this.toolMeshColorEnabled)return this.toolMeshColorEnabled=e,this.onMeshOverlayCommand({color:e?this.TOOL_MESH_COLOR_OVERLAY:null,selectBy:ee.u.selectBy.all,colorStyle:ee.u.colorBy.explicit,alpha:.5})}async onSetChunkRenderStateCommand(e){this.chunkRenderingModeOverride=e.mode,this.updateRenderState()}toggleWireframe(e){for(const t of this.mesh.chunks)t.setWireframe(e)}async setupDebugMenu(e){const[t,i]=await Promise.all([e.market.waitForData(k.e),e.getModuleBySymbol(o.Ak)]);i.registerButton("Mesh","Toggle visible",(()=>{this.container.visible=!this.container.visible})),i.registerButton("Mesh","Toggle UV debug",(()=>{const t=this.chunkRenderingModeOverride?null:F.S.UV;e.commandBinder.issueCommand(new ie.U(t))})),i.registerButton("Mesh","Toggle depth",(()=>{const t=this.chunkRenderingModeOverride?null:F.S.Depth;e.commandBinder.issueCommand(new ie.U(t))})),i.registerButton("Mesh","Toggle transparent",(()=>{const t=this.chunkRenderingModeOverride?null:F.S.Transparent;e.commandBinder.issueCommand(new ie.U(t))})),i.registerButton("Mesh","Toggle wireframe",(()=>{const t=this.chunkRenderingModeOverride?null:F.S.Wireframe;e.commandBinder.issueCommand(new ie.U(t))}));let s=!1;i.registerButton("Mesh","Toggle flat shading",(()=>{s=!s;for(const e of this.mesh.chunks)e.setFlatShading(s)})),i.registerButton("Mesh","Cycle all chunk materials",(()=>{this.debugCycleChunkMaterials()})),i.registerButton("Mesh","Highlight Rooms",this.debugColorizeChunks),i.registerButton("Mesh","Highlight Chunks",(()=>this.debugColorizeChunks(!0,!0))),t.onPropertyChanged(A.NR,(e=>{for(const t of this.mesh.chunks){const i=this.meshData.meshGroups.floors.get(t.meshGroup);i&&t.setMaterialsUniform({floorTrimHeight:1-e/100,floorHeightMin:i.boundingBox.min.y,floorHeightMax:i.boundingBox.max.y})}}));const r=(e,s,o,r,n)=>{i.registerSetting(e,s,o,!0,B.SettingPersistence.NONE,n),t.onPropertyChanged(s,r)};r("Wireframe",A.Lp,!1,this.toggleWireframe.bind(this));const n={[E.h.Wireframe]:{Wireframe:["thickness","wireframeOpacity","stroke","fillEnabled","fill","insideAltColor","dualStroke","secondThickness"],"Wireframe Dashes":["dashEnabled","dashLength","dashAnimate","dashOverlap"],"Wireframe Advanced":["squeeze","squeezeMin","squeezeMax"]}};for(const e in n){const t=D.Z.modelChunk.uniforms[e];for(const i in n[e])for(const s of n[e][i])r(i,s,t[s].value,(e=>{for(const t of this.mesh.chunks)t.setMaterialsUniform({[s]:e})}),t[s].range)}}async debugCycleChunkMaterials(){const e=[];for(const t in E.h)isNaN(Number(t))&&e.push(E.h[t]);const t=[];for(const e in F.S)isNaN(Number(e))&&t.push(F.S[e]);re.info(`Available ChunkMaterialCapabilities: ${e}`);const i=[];for(let t=0;t<1<<e.length-1;t++){const s={};for(let i=e.length-1;i>=0;i--){s[e[i]]=Boolean(t&1<<i)}i.push(s)}re.info(`There are ${i.length*t.length} options to test`);for(let e=0;e<t.length;e++){await this.engine.commandBinder.issueCommand(new ie.U(e));for(const t of i){re.info(`Testing ChunkRenderingMode.${F.S[e]} with capabilities: ${JSON.stringify(t,void 0,2)}`);const i=new f.Vector4(Math.random(),Math.random(),Math.random(),.5);for(const e of Object.keys(t))for(const s of this.mesh.chunks)s.setColorOverlay(i),s.setMeshPreviewSphere(new f.Vector3(0,0,0)),s.overrideCapability(e,t[e]);await(0,C.gw)(100)}await(0,C.gw)(100)}await this.engine.commandBinder.issueCommand(new ie.U(null))}}var ae=i(53261),he=i(44443),le=i(64210);class de{constructor(e,t,i=128,s=128){this.slots=e,this.renderToTextureModule=t,this.width=i,this.height=s,this.sizeMultiplier=1/8,this.pixelBuffer=new Uint8Array(4),this.readBuffer=null,this.readPixelsAsync=Promise.resolve(this.pixelBuffer)}get renderTarget(){return this._renderTarget||(this._renderTarget=new f.WebGLRenderTarget(this.width,this.height,{type:f.UnsignedByteType,format:f.RGBAFormat,depthBuffer:!1,stencilBuffer:!1,generateMipmaps:!1,minFilter:f.NearestFilter,magFilter:f.NearestFilter})),this._renderTarget}pixels(){return this.pixelBuffer}renderAndReadAsync(e,t){this.beforeRender(),this.updateReadBufferSize(),this.readPixelsAsync=this.renderToTextureModule.renderAndReadAsync({mesh:e,camera:t,target:this.renderTarget,clear:!0},{x:0,y:0,width:this.width,height:this.height},{buffer:this.pixelBuffer}).then((e=>(this.pixelBuffer=e,this.pixelBuffer))),this.afterRender()}render(e,t,i=!1,s=!0){this.beforeRender();let{width:o,height:r}=this.renderToTextureModule.getRenderSize();o=Math.max(1,Math.floor(o*this.sizeMultiplier)),r=Math.max(1,Math.floor(r*this.sizeMultiplier)),o===this.renderTarget.width&&r===this.renderTarget.height||this.renderTarget.setSize(o,r),this.renderToTextureModule.render(this.renderTarget,e,t),i&&this.renderToScreen(),s&&this.afterRender()}renderToScreen(){this.renderToTextureModule.renderToScreen(this.renderTarget,!1)}readPixels(){this.updateReadBufferSize();const e=this.pixelBuffer;return this.pixelBuffer=this.renderToTextureModule.getRenderTargetData(this.renderTarget,e),this.pixelBuffer}updateReadBufferSize(){const e=this.renderTarget.width*this.renderTarget.height*4;this.pixelBuffer.length!==e&&(this.pixelBuffer=new Uint8Array(e))}beforeRender(){this.width===this.renderTarget.width&&this.height===this.renderTarget.height||this.renderTarget.setSize(this.width,this.height);for(const e of this.slots)for(const t of e.chunks)this.beforeRenderChunk(t,e)}afterRender(){for(const e of this.slots)for(const t of e.chunks)this.afterRenderChunk(t,e)}}class ce extends de{constructor(e,t,i,s=128,o=128){super(t,i,s,o),this.qualities=e,this.prevColor=new WeakMap}beforeRenderChunk(e,t){this.prevColor.set(t,e.getColorOverlay()),e.setColorOverlay((0,le.O7)(t.quality,this.qualities))}afterRenderChunk(e,t){var i;e.setColorOverlay(null!==(i=this.prevColor.get(t))&&void 0!==i?i:null)}}class ue extends de{constructor(){super(...arguments),this.prevColor=new WeakMap}beforeRender(){this.maxValue=this.slots.reduce(((e,t)=>Math.max(e,t.screenCoverageScore)),1e-9),super.beforeRender()}beforeRenderChunk(e,t){const i=t.screenCoverageScore/this.maxValue,s=new f.Vector4(i,i,i,1);this.prevColor.set(t,e.getColorOverlay()),e.setColorOverlay(s)}afterRenderChunk(e,t){var i;e.setColorOverlay(null!==(i=this.prevColor.get(t))&&void 0!==i?i:null)}}var me=i(99935),pe=i(83021);class ge{constructor(e,t,i=I.V.Standard,s){this.textureName=e,this.lod=i,this.chunkedTexInfo=s,this.chunks=new Set,this.loading=!1,this.unloaded=!1,this.screenCoverage=0,this.screenCoverageScore=0,this.sightings=new pe.P(A.EJ.sightingMaxAge),s&&(t.registerQualities(i,s.maxTextureSize,s.maxTexelSize,s.minScale),this.quality=t.min(i),this.targetQuality=this.quality),this.minQuality=t.min(i),this.maxQuality=this.minQuality}setTexture(e){const t=this.texture;if(this.texture=e,t&&t!==e&&t.dispose(),e)for(const t of this.chunks)t.setMeshTexture(e)}getEmbeddedTexture(){var e;const t=null===(e=this.chunks.entries().next())||void 0===e?void 0:e.value;return t?t[0].embeddedTexture:void 0}}class fe{constructor(e,t,i,s=.85){this.textureQualityMap=e,this.renderer=t,this.maxBudget=i,this.pctTotalMemory=s,this.lods=new Map,this.orders={},this.slotTexSizes=new Map}dispose(){this.slots.length=0,this.lods.clear()}update(e){this.updateBudget(void 0),this.updateTargetQualitiesFromBudget()}updateBudget(e){if(e&&(this.slots=e,e.forEach((e=>{const t=this.lods.get(e.lod)||new Set;t.add(e),this.lods.set(e.lod,t),this.orders[e.lod]=this.textureQualityMap.order(e.lod)}))),this.shouldRestrictBudget()){const e=this.calcCurrentTexturesSize(),t=this.renderer.estimatedGPUMemoryAllocated()-e,i=this.maxBudget()-t;this.budget=i*this.pctTotalMemory}else this.budget=1/0;return this}updateTargetQualitiesFromBudget(){let e=0;for(const t of this.slots)this.updateBudgetSize(t),t.targetQuality=t.minQuality,e+=this.getBudgetSize(t,t.targetQuality);const t=(t,i)=>t.maxQuality<i||(e-=this.getBudgetSize(t,t.targetQuality),e+=this.getBudgetSize(t,i),!(e>this.budget)&&(t.targetQuality=i,!0));if(this.shouldRestrictBudget()){for(const e of this.slots)for(const i of this.orders[e.lod])if(!t(e,i))return}else for(const[e,i]of this.lods.entries())for(const s of this.orders[e])for(const e of i)if(!t(e,s))return}shouldRestrictBudget(){return this.maxBudget()!==1/0}getBudgetSize(e,t){if(!t)return 0;const i=this.textureQualityMap.get(t);let s=0;if(e){const i=this.textureQualityMap.min(e.lod);i!==t&&e.getEmbeddedTexture()&&(s=this.getBudgetSize(e,i));const o=e.textureName+t,r=this.slotTexSizes.get(o);if(r)return r+s}return(i?i.textureSize:4096)**2*4+s}updateBudgetSize(e){var t;const i=e.textureName+e.quality,s=null!==(t=e.texture)&&void 0!==t?t:e.getEmbeddedTexture();this.slotTexSizes.set(i,this.texSizeBytes(s))}calcCurrentTexturesSize(){return this.slots.reduce(((e,t)=>{var i;const s=t.getEmbeddedTexture(),o=null!==(i=t.texture)&&void 0!==i?i:s;return o===s?e+this.texSizeBytes(o):e+this.texSizeBytes(o)+this.texSizeBytes(s)}),0)}texSizeBytes(e){if(!e)return 0;if(e.mipmaps.length>0)return e.mipmaps.reduce(((e,t)=>e+t.data.length),0);let t=e.image.width*e.image.height*4,i=t/4;for(;i>=1;)t+=i,i/=4;return t}}var ye=i(73121),we=i(49827),ve=i(60047),be=i(947);const Me=new O.Z("tex-lod");class Te{constructor(e,t,i,s,o,r,n,a,h,l){this.textureQualityMap=e,this.textureLOD=t,this.api=i,this.modelObject=s,this.camera=o,this.renderer=r,this.renderToTextureModule=n,this.engine=a,this.chunkVisibilityChecker=h,this.rendererModule=l,this.name="texture-streaming",this.slots=[],this.textureNameToSlot={},this.chunkIdToSlot={},this._systemMin=me.S.LOW,this._systemMax=me.S.ULTRA,this.allowTextureDownload=()=>!0,this.concurrentLoadingTextures=1,this.concurrentDownloadingTiles=12,this.autoLoadTiles=!1,this.lastSortedAt=0,this.loadingTextures=0,this.downloadingTiles=0,this.totalTextures={},this.totalTiles=0,this.textureQualityRenderPass=new ce(this.textureQualityMap,this.slots,this.renderToTextureModule),this.textureScoreRenderPass=new ue(this.slots,this.renderToTextureModule),this.abortController=new AbortController,this._chunkSlotsSet=new Set,this.loadImage=async(e,t,i,s,o)=>{var r;const n=this.textureQualityMap.get(t),{assetType:a,lod:h}=n,l=(null===(r=e.chunkedTexInfo)||void 0===r?void 0:r.maxTextureSize)||n.assetSize;let d,c=e.textureName;if(this.textureApiInfo){if(!e.chunkedTexInfo){const e=c.match(/[_.]([0-9]{3})[_.]/);if(!e)throw new Error(`Could not parse texture index from texture name: ${c}`);c=e[1]}d=(await this.textureApiInfo[a].get()).urlTemplate.replace("<folder>",`${h}`).replace("<texture>",c)}else{const e=c.match(/^([a-f0-9]+(_10k|_50k)?)/);if(!e)throw new Error(`Unknown format for texture name: ${c}`);d=`${e[0]}_texture_jpg_${a}/${c}`}const{sourceSize:u=l,sourceX:m=0,sourceY:p=0,destSize:g=u}=s,f={};return g!==u&&(f.width=`${g}`),u!==l&&(f.crop=`${u},${u},x${m/l},y${p/l}`),d=(0,he.bf)(d,f),i.getImageBitmap(d,g,g,o)}}setModel(e,t,i,s){this.textureApiInfo=i,this.modelObject=e,this.slots.length=0,this.textureNameToSlot={},this.chunkIdToSlot={},this.addChunkSlots([...t]),this.textureBudgeter=new fe(this.textureQualityMap,this.rendererModule,s),this.textureBudgeter.updateBudget(this.slots),this.updateSystemQualityRanges(),this.chunkVisibilityChecker.notifyOnNewSighting(((e,t)=>{const i=this.addChunkSlots([e]);for(const e of i)e.sightings.push(t)}))}dispose(){this.slots.length=0,this.textureNameToSlot={},this.chunkIdToSlot={},this._chunkSlotsSet.clear(),this.textureBudgeter.dispose()}addChunkSlots(e){this._chunkSlotsSet.clear();let t=!1;for(const i of e){const e=i.textureName;let s=this.textureNameToSlot[e];s||(t=!0,s=new ge(e,this.textureQualityMap,i.lod,i.textureLODInfo),i.embeddedTexture&&s.setTexture(i.embeddedTexture),this.textureNameToSlot[i.textureName]=s,this.slots.push(s)),s.chunks.has(i)||(t=!0,s.chunks.add(i),s.texture&&i.setMeshTexture(s.texture)),this._chunkSlotsSet.add(s),this.chunkIdToSlot[i.id]=s}return t&&this.textureBudgeter&&(this.textureBudgeter.updateBudget(this.slots),this.updateSystemQualityRanges()),this._chunkSlotsSet}removeChunks(e){for(const t of e){const e=this.chunkIdToSlot[t.id];if(e){if(e.chunks.delete(t),0===e.chunks.size){e.unloaded=!0,e.setTexture(null);const i=this.slots.indexOf(e);this.slots.splice(i,1),delete this.textureNameToSlot[t.textureName]}delete this.chunkIdToSlot[t.id]}else Me.error("Missing slot for chunk!")}}setQuality(e,t){this._systemMin=e,this._systemMax=t,this.updateSystemQualityRanges()}updateSystemQualityRanges(){const e=this.textureQualityMap;this.minQuality={},this.maxQuality={};for(const t of new Set([...this.slots.map((e=>e.lod))]).values())this.minQuality[t]=e.nearestQuality(t,this._systemMin),this.maxQuality[t]=e.nearestQuality(t,this._systemMax);for(const e of this.slots)e.minQuality=this.minQuality[e.lod],e.maxQuality=e.maxQuality?Math.min(this.maxQuality[e.lod],e.maxQuality):this.maxQuality[e.lod]}get textureCount(){return this.slots.length}async loadAll(e){this.slots[0]&&this.slots[0].textureName&&await this.loadSlots(this.slots,e)}async loadSlots(e,t=this.textureQualityMap.min(I.V.Standard)){const i=this.textureQualityMap;i.valid(t)?await Promise.all(e.map((e=>this.loadTexture(e,t,!1)))):Me.warn(t,"not found in",i)}onWebGLContextLost(){this.abortController.abort();for(const e of this.slots)e.texture=null}onWebGLContextRestored(){this.abortController=new AbortController;for(const e of this.slots)this.loadTexture(e,e.quality)}async loadTexture(e,t,i=!0){var s,o;const r=this.textureQualityMap;r.valid(t)||Me.warn(t,"not found in",r);const n=r.get(t),a=(null===(s=e.chunkedTexInfo)||void 0===s?void 0:s.maxTextureSize)||n.assetSize,h=Math.min(a,n.textureSize);e.lastLoadedAt=performance.now();let l=e.texture;if(!l||t!==e.quality){if(l&&t<e.quality&&e.texture){const i=null===(o=e.chunks.values().next().value)||void 0===o?void 0:o.embeddedTexture;if(i&&i.image.width>=h&&i.mipmaps[0].data.length<=h*h*4)return e.quality=t,void e.setTexture(i);{const i=this.renderToTextureModule.resizeTexture(l,h);return De(i,e),e.quality=t,void e.setTexture(i)}}e.loading=!0,this.loadingTextures++,this.totalTextures[h]=(this.totalTextures[h]||0)+1;try{l=this.textureLOD!==M.l.NONE&&i?await this.loadTextureTiled(h,t,e,this.abortController.signal):await this.loadTextureSolid(h,t,e,this.abortController.signal),e.unloaded?l.dispose():(De(l,e),e.setTexture(l))}catch(e){}finally{this.loadingTextures--,e.quality=t,e.loading=!1}}}async loadTextureTiled(e,t,i,s){var o;const r=this.renderer.initSizedTexture2D(e,{generateMipmaps:!1,minFilter:f.LinearFilter,magFilter:f.LinearFilter}),n=this.textureQualityMap,a=(null===(o=i.chunkedTexInfo)||void 0===o?void 0:o.maxTextureSize)||n.get(t).assetSize,h=Math.min(a,n.get(t).textureSize),l=Math.min(h,n.get(t).tileSize),d=async(e,o)=>{let n;this.downloadingTiles+=1,this.totalTiles+=1;const d=A.ZP.flipDownload;try{n=await this.loadImage(i,t,this.api,{sourceSize:a*(l/h),sourceX:a*(e/h),sourceY:a*(o/h),destSize:l},{priority:ae.ru.LOW,flipY:d,signal:s})}finally{this.downloadingTiles-=1}const c=e,u=A.ZP.flipUpload?h-o-l:o,m=new ve._("mesh/texture/upload-tiles",(()=>this.engine.after(be.A.End).then((()=>{if(s.aborted)throw new DOMException("Aborted","AbortError");r.flipY=d&&n instanceof HTMLImageElement,this.renderer.uploadTexture2D(n,r,c,u)}))),100);return(await this.engine.commandBinder.issueCommand(m)).promise.finally((()=>{var e,t;n&&(null===(t=(e=n).close)||void 0===t||t.call(e))}))},c=[];for(let e=0;e<h;e+=l)for(let t=0;t<h;t+=l)c.push(d(e,t));try{await Promise.all(c)}catch(e){throw r.dispose(),e}return r}async loadTextureSolid(e,t,i,s){var o,r;const n=this.renderer.initSizedTexture2D(e);let a=null;try{const e=this.textureQualityMap.get(t).textureSize,h=A.ZP.flipDownload;a=await this.loadImage(i,t,this.api,{destSize:e},{priority:ae.ru.LOW,flipY:h,signal:s}),n.flipY=h&&a instanceof HTMLImageElement,this.renderer.uploadTexture2D(a,n,0,0)}catch(e){throw n.dispose(),e}finally{a&&(null===(r=(o=a).close)||void 0===r||r.call(o))}return n}setImageLoader(e){this.loadImage=e}analyzeTextureScreenCoverageFromRaycasts(){const e=this.renderer.getSize().x,t=this.camera.getWorldPosition(new f.Vector3);for(const i of this.slots){i.screenCoverage=0,i.screenCoverageScore=0,i.maxQuality=i.minQuality;const s=i.sightings.getList(),o=i.sightings.index-1;for(let r=0;r<s.length;r++){const n=s[(o-r+s.length)%s.length];if(n.raycastAge<this.chunkVisibilityChecker.raycastCounter-A.ZP.sightingMaxAge)break;const a=t.distanceTo(n.point),h=(0,ye._U)(a,this.camera.projectionMatrix,e);let l=this.textureQualityMap.fromPixelSize(i.lod,h);l=Math.min(l,this.maxQuality[i.lod]),i.screenCoverageScore+=l,i.screenCoverage+=1,i.maxQuality=Math.max(l,i.maxQuality)}}this.slots.sort(((e,t)=>t.screenCoverageScore-e.screenCoverageScore)),this.textureBudgeter.updateTargetQualitiesFromBudget()}update(e){if(!this.camera||!this.autoLoadTiles||this.abortController.signal.aborted)return;this.textureBudgeter.update(e);const t=performance.now();this.textureLOD===M.l.RAYCAST&&this.scheduleRaycastTasks(t);let i=!1;for(let e=this.slots.length-1;e>=0;e--){const s=this.slots[e],o=(0,we.uZ)(s.targetQuality,s.minQuality,s.maxQuality),r=t-s.lastLoadedAt<1e3;if(!s.loading&&s.quality>o){r?i=!0:this.loadTexture(s,o);break}}if(this.allowTextureDownload()&&!i)for(const e of this.slots){if(this.loadingTextures>=this.concurrentLoadingTextures||this.downloadingTiles>=this.concurrentDownloadingTiles)break;const t=(0,we.uZ)(e.targetQuality,e.minQuality,e.maxQuality);!e.loading&&e.quality<t&&this.loadTexture(e,this.textureQualityMap.moreDetailed(e.lod,e.quality))}A.ZP.debugRttScores&&this.textureScoreRenderPass.render(this.modelObject,this.camera,!0,A.ZP.debugRttClear),A.ZP.debugRttQuality&&this.textureQualityRenderPass.render(this.modelObject,this.camera,!0,A.ZP.debugRttClear)}scheduleRaycastTasks(e){if(!this.analyzeTaskPromise&&e-this.lastSortedAt>200){const e=()=>{this.analyzeTextureScreenCoverageFromRaycasts(),this.lastSortedAt=performance.now()},t=async e=>{await e.promise,this.analyzeTaskPromise=null},i=new ve._("mesh/texture/analyze-screen-coverage",e,100);this.analyzeTaskPromise=this.engine.commandBinder.issueCommand(i).then(t)}}}function De(e,t){e.addEventListener("dispose",(()=>{e===t.texture&&Me.warn("Streamed texture disposed while still in use")}))}var xe=i(92324),Se=i(16769),Ce=i(7402);const Pe=Math.round(A.ZP.sightingMaxAge/60/5)||1;class Oe{constructor(e,t,i){this.scene=e,this.raycaster=t,this.raycastCounter=0,this.onNewSighting=new Set,this.poseSetAt=0,this.raycastRandomScreenLocation=(()=>{const e=new f.Vector3,t=new f.Vector3,i=new f.Vector3;function s(e){return!!((0,Se.Pv)(e)&&e instanceof Ce.g)&&e.chunks.some((e=>e.getOpacity()>A.xx.FADE_TILE_VISIBLE_THRESHOLD))}return(o,r,n)=>{this.raycastCounter++;const a=(2-2*r)/o,h=-1+r,l=this.raycastCounter%(o*o),d=l%o,c=h+(l-d)/o*a,u=h+d*a+Math.random()*a,m=c+Math.random()*a;e.set(u,m,-1).unproject(this.camera),t.set(u,m,1).unproject(this.camera),i.subVectors(t,e).normalize();const p=this.raycaster.pick(e,i,s);if(p){if(p.face&&p.object instanceof Ce.g){const e=p.object.getChunk(p.face.materialIndex),t={point:p.point.clone(),raycastAge:this.raycastCounter};for(const i of this.onNewSighting.values())i(e,t)}n&&n(p)}}})(),this.camera=e.camera,i.pose.onChanged((()=>{this.poseSetAt=this.raycastCounter}))}update(){const e=A.ZP.debugLOD?(0,le.ef)(65280,this.scene):void 0,t=performance.now();for(let i=0;i<Pe&&performance.now()-t<.5&&this.raycastCounter<=this.poseSetAt+A.ZP.sightingMaxAge;i++)this.raycastRandomScreenLocation(5,.05,e)}notifyOnNewSighting(e){return(0,N.k1)((()=>this.onNewSighting.add(e)),(()=>this.onNewSighting.delete(e)),!0)}}var ke=i(92901),Be=i(31362);const Ae=i.p+"images/uv_grid_opengl.jpg";var Fe=i(87926);class Ee{constructor(){this.materials=new Map}get(e){let t=this.materials.get(e);return t||(t=Re[e](),this.materials.set(e,t)),t}}const Re={[F.S.Depth](){const e=f.UniformsUtils.clone(D.Z.depth.uniforms);return new f.RawShaderMaterial({fragmentShader:D.Z.depth.fragmentShader,vertexShader:D.Z.depth.vertexShader,uniforms:e,side:f.FrontSide,name:"materialDepth"})},[F.S.Transparent](){const e=f.UniformsUtils.clone(D.Z.modelOutside.uniforms);return e.opacity.value=.2,e.colorOverlay.value.set(1,1,1,1),new f.RawShaderMaterial({fragmentShader:D.Z.modelOutside.fragmentShader,vertexShader:D.Z.modelOutside.vertexShader,uniforms:e,side:f.FrontSide,transparent:!0,name:"materialTransparent"})},[F.S.Wireframe](){const e=f.UniformsUtils.clone(D.Z.modelOutside.uniforms);return e.opacity.value=.5,e.colorOverlay.value.set(1,1,1,1),new f.RawShaderMaterial({fragmentShader:D.Z.modelOutside.fragmentShader,vertexShader:D.Z.modelOutside.vertexShader,uniforms:e,side:f.FrontSide,transparent:!0,wireframe:!0,name:"materialWireframe"})},[F.S.UV]:()=>new f.MeshBasicMaterial({name:"uv-debug",map:(0,Fe.p)(Ae)})};class Ie{constructor(){this.side=f.FrontSide,this.renderingMode=F.S.Standard,this.chunkMaterials={},this.modeMaterials=new Ee,this.globalUniforms={}}forEachChunkMaterial(e){const{chunkMaterials:t}=this;for(const i in t)e(t[i])}dispose(){const{chunkMaterials:e}=this;for(const t in e){const i=e[t];for(const e in i.uniforms)i.uniforms[e].value instanceof f.Texture&&i.uniforms[e].value.dispose();i.dispose(),delete e[t]}}}var Ve=i(2569);class _e{constructor(){this._configs={},this._orders={},this._maxLod=-1/0,this._maxTs=1/0,this._streamAbove=0}static encodeKey(e,t){return e-t}get maxLod(){return this._maxLod}get maxTexelSize(){return this._maxTs}limitStreamingBelow(e){this._streamAbove=e}order(e,t=!1){return t&&!this._orders[e]&&(this._orders[e]=[]),this._orders[e]}reset(){this._configs={},this._orders={},this._maxTs=1/0,this._maxLod=-1/0}valid(e){return e in this._configs}get(e){if(!this.valid(e))throw new Error("invalid quality level "+e);return this._configs[e]}max(e){return e<this._streamAbove?this.min(e):this.order(e)[this.order(e).length-1]}min(e){return this.order(e)[0]}fromPixelSize(e,t){const i=this.order(e),s=this.min(e);let o=this.max(e);for(let e=i.length-1;e>=0;e--)t>this.get(i[e]).texelSize&&i.indexOf(o)>i.indexOf(s)&&(o=i[e]);return o}moreDetailed(e,t){let i=this.min(e);const s=this.max(e),o=this.order(e),r=o.indexOf(t);return r+1===o.length?s:(-1!==r&&(i=Math.min(s,o[r+1%o.length])),i)}lessDetailed(e,t){const i=this.order(e),s=i.indexOf(t);let o=t;return-1!==s&&(o=i[Math.max(0,s-1)]),o}minSize(){return Object.values(this._configs).sort(Le)[0]}maxSize(){const e=Object.values(this._configs).sort(Le);return e[e.length-1]}nearestQuality(e,t){const i=this.order(e),s=this.max(e),o=(0,Ve.et)(t,1,4,0,i.length-1);return Math.min(s,i[Math.round(o)])}registerQualities(e,t,i,s,o="max"){const r=this._configs,n=this.order(e,!0),a=t*s;for(let s=t,h=i;s>=a;s*=.5,h*=2){const i=_e.encodeKey(e,h),a=r[i];(!a||a&&a.assetSize<t)&&(this._maxTs=Math.min(this._maxTs,h),this._maxLod=Math.max(this._maxLod,e),r[i]={key:i,lod:e,texelSize:h,textureSize:s,assetSize:t,assetType:o,tileSize:Math.min(s,A.ZP.textureTileSize)}),-1===n.indexOf(i)&&n.push(i)}n.sort(((e,t)=>r[t].texelSize-r[e].texelSize))}}function Le(e,t){return e.textureSize>t.textureSize?1:-1}class Ne extends s.Y{constructor(){super(...arguments),this.name="model-mesh",this.chunkSharedState=new Ie,this.textureQualityMap=new _e,this.tiled=!0,this.onMeshTrimGroupChanged=e=>{this.setTrimsForMeshGroup(e)},this.updateTrimVisibilityForViewmode=e=>{if(this.meshTrimUniforms&&e!==this.meshTrimUniforms.isPanoMode){this.meshTrimUniforms.isPanoMode=e;for(const e of this.meshTrimData.meshTrimsByMeshGroup.keys)this.setTrimsForMeshGroup(e)}},this.updateMeshTrim=e=>{this.meshTrimUniforms.setMeshTrim(e),this.setTrimsForMeshGroup(e.meshGroup)},this._renderMode=b.k.Hidden,this.meshTrimFilter=e=>{const t=e.object,i=e.point;let s=!1,o=!1;if(this.meshTrimData&&this.viewmodeData&&(0,Se.Pv)(t)){for(const e of this.meshTrimData.activeTrimsForMeshGroup(t.meshGroup)){const t=e.isPointTrimmed(i,this.viewmodeData.isPano());if(e.discardContents){if(t)return!1}else s=s||!t,o=!0}if(o)return s}return!0}}async init(e,t){w||(f.Mesh.prototype.raycast=y.uL,f.BufferGeometry.prototype.computeBoundsTree=y.Xy,f.BufferGeometry.prototype.disposeBoundsTree=y.sn,w=!0),this.engine=t,this.market=t.market;const[i,s,a,c,g,M,T,D,x,S]=await Promise.all([t.getModuleBySymbol(r.Aj),t.getModuleBySymbol(r.fQ),t.getModuleBySymbol(r.Vs),t.market.waitForData(l.T),t.getModuleBySymbol(r.PZ),t.getModuleBySymbol(r.tA),t.market.waitForData(h.M),t.market.waitForData(m.O),t.market.waitForData(d.Z),t.getModuleBySymbol(r.RR)]),C=await t.getModuleBySymbol(o.Ak);this.appData=await t.market.waitForData(n.pu),this.sweepData=x,this.viewmodeData=D;const P=i.getScene(),O=a.getApi(),k=c.model,B=k.uuid,{chunkSharedState:F}=this,E=t.claimRenderLayer(this.name);this.panoRenderer=S.getRenderer();const R=this.roomMeshData=new xe.s;F.maxVaryings=i.maxVaryings,this.chunkVisibiltyChecker=new Oe(P,s.picking,T),this.tiled=k.tileset&&C.tryGetProperty(A.iT,!1)&&!(0,Be.Q0)();const I=await this.getModelMeshFactory();this.modelMesh=await I({uuid:B,renderLayer:E,engine:t,settings:A.ZP,roomMeshData:R,chunkSharedState:F,chunkFactory:(e,t,i,s)=>{const o=new W(e,t,i,F,s,!0);if(this.meshTrimUniforms){const t=this.meshTrimUniforms.getSharedFloorUniforms(e);o.setMaterialsUniform(t)}return o},chunkVisibilityChecker:this.chunkVisibiltyChecker,gltfConfig:e.gltfConfig}),this.engine.market.register(this,xe.s,R);let V=Promise.resolve();const _=this.modelMesh;this.setupMarketData(_.chunks,x);let L=e.startingMode;switch(null===L&&(L=this.viewmodeData.transition.active?this.viewmodeData.transition.to:this.viewmodeData.currentMode),L){case p.Ey.Dollhouse:case p.Ey.Floorplan:case p.Ey.Mesh:this.setRenderMode(b.k.Mesh);break;default:this.setRenderMode(b.k.PanoramaMesh)}this.modelTextureLoader=new Te(this.textureQualityMap,e.textureLOD,O,this.modelMesh,P.camera,i.cwfRenderer,M,t,this.chunkVisibiltyChecker,i),this.setTextureStreamMode(e.textureLOD),V=this.modelMesh.initTextureLoader(this.modelTextureLoader,c.model.textures),s.setupOctree(this.modelMesh.boundingBox),this.modelMesh.registerCollision(g),this.renderer=new ne(P.scene,this.modelMesh,this.modelMesh,this.panoRenderer,this.meshData,this.sweepData,this.appData,F,e),await t.addComponent(this,this.renderer),await V,this.bindings.push(this.engine.subscribe(u.Z,(e=>{this.renderer.updateExistingTexture(e.sweepId,e.renderTarget.texture)}))),this.bindings.push(t.commandBinder.addBinding(v.n,this.setPreviewPosition.bind(this)),t.subscribe(ke.zq,(()=>{this.modelTextureLoader.onWebGLContextLost()})),t.subscribe(ke.Xq,(()=>{this.modelTextureLoader.onWebGLContextRestored()}))),await this.bindMeshTrimListeners(s)}dispose(e){super.dispose(e),this.modelMesh.dispose(),this.modelTextureLoader.dispose(),this.roomMeshData.floors.clear(),this.roomMeshData.rooms.clear()}onUpdate(e){this.modelMesh.onUpdate(),this.modelTextureLoader&&!A.ZP.debugPauseTexStream&&this.modelTextureLoader.update(e),this.chunkVisibiltyChecker&&this.chunkVisibiltyChecker.update(),this.meshData.meshTextureOpacity.active&&(this.meshData.meshTextureOpacity.updateProgress(this.viewmodeData.transition.progress),this.meshData.commit());const t=performance.now()/1e3;for(const e of this.modelMesh.chunks)e.setTime(t)}async getModelMeshFactory(){return(this.tiled?await Promise.all([i.e(217),i.e(764),i.e(648),i.e(666),i.e(756),i.e(319),i.e(321)]).then(i.bind(i,49648)):await Promise.all([i.e(217),i.e(764),i.e(648),i.e(666),i.e(756),i.e(319),i.e(321)]).then(i.bind(i,8641))).createModelMesh}setupMarketData(e,t){var i;const s=new Map,o=new Map,r=new Map,n=new f.Box3,h=new f.Vector3;e.forEach((t=>{if(t.geometry.boundingBox&&n.union(t.geometry.boundingBox),t.geometry){const i=(0,L.bo)(t.geometry);r.set(t,i),h.addScaledVector(i,1/e.length)}s.set(t.meshGroup,(s.get(t.meshGroup)||[]).concat(t)),o.set(t.meshSubgroup,(o.get(t.meshSubgroup)||[]).concat(t))}));const l=n.clone(),d=new f.Box3;t.iterate((e=>{e.isUnplaced()||(d.setFromCenterAndSize(e.position,a.fU.UNIT),l.union(d))}));const c=new T._(0,[...n.min.toArray(),...n.max.toArray()],[...l.min.toArray(),...l.max.toArray()],h);for(const[e,t]of s.entries()){const s=new f.Box3,n=new f.Vector3;t.forEach((e=>{e.geometry.boundingBox&&s.union(e.geometry.boundingBox);const i=r.get(e);i&&n.addScaledVector(i,1/t.length)})),c.meshGroups.floors.set(e,{meshGroup:e,boundingBox:s,parentMeshGroup:null,centerOfMass:n});const a=[...new Set(t.map((e=>e.meshSubgroup)))].sort(((e,t)=>t-e));c.meshGroups.roomsByFloor.set(e,a);const h=c.meshGroups.rooms;for(const t of a){const s=new f.Box3,n=new f.Vector3,a=o.get(t)||[];a.forEach((e=>{e.geometry.boundingBox&&s.union(e.geometry.boundingBox);const t=r.get(e);t&&n.addScaledVector(t,1/a.length)}));const l={meshGroup:t,boundingBox:s,parentMeshGroup:e,centerOfMass:n},d=h.get(t);d?d.parentMeshGroup=Math.min(null!==(i=d.parentMeshGroup)&&void 0!==i?i:1/0,e):h.set(t,l)}c.meshGroupVisuals.floorOpacity.set(e,1)}this.meshData=c,this.market.register(this,T._,this.meshData)}async bindMeshTrimListeners(e){this.meshTrimData=await this.engine.market.waitForData(c.Z),this.meshTrimUniforms=new S(this.getRenderMode()===b.k.PanoramaMesh);const{meshTrimsByMeshGroup:t}=this.meshTrimData;this.bindings.push(this.meshTrimData.onMeshGroupChanged(this.onMeshTrimGroupChanged),this.meshTrimData.onMeshTrimChanged(this.updateMeshTrim)),t.keys.forEach((e=>{this.setTrimsForMeshGroup(e)})),this.setMaterialsUniforms(-1);const i=()=>{e.setIntersectionFilter(this.meshTrimData.meshTrimsById.values.some((e=>e.enabled))?this.meshTrimFilter:null)};this.bindings.push(this.meshTrimData.onMeshTrimChanged(i)),i()}setTrimsForMeshGroup(e){const t=this.meshTrimData.getTrimsForMeshGroup(e);this.meshTrimUniforms.updateMeshTrimArrays(e,t),this.setMaterialsUniforms(e)}setMaterialsUniforms(e){const t=`${e}`;if("-1"===t)this.modelMesh.chunks.forEach((e=>{const t=this.meshTrimUniforms.getSharedFloorUniforms(e.meshGroup);e.setMaterialsUniform(t)}));else{const i=this.meshTrimUniforms.getSharedFloorUniforms(e);this.modelMesh.chunks.forEach((e=>{`${e.meshGroup}`===t&&e.setMaterialsUniform(i)}))}}async setPreviewPosition(e){const t=e.enabled&&e.previewCirclePosition?e.previewCirclePosition:null,i=e.size?e.size:.3;for(const e of this.modelMesh.chunks)e.setMeshPreviewSphere(t,i)}setChunkSide(e){const t=this.chunkSharedState.side;return this.chunkSharedState.side=e,t}stats(){const e={};return e.textureCount=this.modelTextureLoader.textureCount,e.streaming=this.tiled,e}getRenderMode(){return this._renderMode}setRenderMode(e,t=g.Q9){let i=1;switch(e){case b.k.Hidden:i=1,this.modelMesh.visible=!1;break;case b.k.Mesh:i=1,this.modelMesh.visible=!0;break;case b.k.PanoramaMesh:i=0,this.modelMesh.visible=!0;break;case b.k.PanoramaCube:i=0,this.modelMesh.visible=!1;break;default:throw new Error(`unknown mode ${e}!`)}const s=this.meshData.meshTextureOpacity;s.value===i&&s.easing===t||(this.meshData.meshTextureOpacity.modifyAnimation(this.meshData.meshTextureOpacity.value,i,100,t),this.updateTrimVisibilityForViewmode(0===i)),this.log.debug(`setRenderMode from ${this._renderMode} to ${e}`),this._renderMode=e}setMeshOptions(e){var t;const i=null!==(t=e.overrideMaxDetail)&&void 0!==t?t:this.modelMesh.detail;this.modelMesh.overrideMaxDetail(i)}getMeshDetail(){return this.modelMesh.detail}setTextureLimits(e,t){this.modelMesh.setTextureQuality(this.modelTextureLoader,e,t)}setTextureStreamMode(e){switch(e){case M.l.NONE:this.modelTextureLoader.autoLoadTiles=!1;break;case M.l.RAYCAST:this.modelTextureLoader.autoLoadTiles=!0}}}},8641:(e,t,i)=>{"use strict";i.r(t),i.d(t,{createModelMesh:()=>x});var s=i(82668),o=i(17878),r=i(2224);class n extends r.y{constructor(e){super(e),this.name="MeshCreationException"}}var a=i(81396),h=i(7402),l=i(48917),d=i(17988);class c extends a.Object3D{constructor(e,t=o.o.ALL){super(),this.renderLayer=t,this.roomMeshes=new l.Z((e=>e.meshSubgroup)),this.boundingBox=new a.Box3,this.size=new a.Vector3,this.center=new a.Vector3,this._chunks=[],this.built=!1,this.name=`FloorMesh:${e}`,this.meshGroup=e}dispose(){this.reset(),this.roomMeshes.clear()}reset(){for(const e of this.roomMeshes)e.dispose(),this.remove(e);this._chunks.length=0,this.built=!1}addChunk(e){const t=this.getOrCreateRoomMesh(e.meshSubgroup);this._chunks.push(e),t.addChunk(e)}build(){if(this.built)throw new Error("build() should only be called once");this.boundingBox.makeEmpty();for(const e of this.roomMeshes)this.add(e),this.boundingBox.union(e.boundingBox);this.center=this.boundingBox.getCenter(this.center),this.size=this.boundingBox.getSize(this.size),this.built=!0}get chunks(){return this._chunks}getOrCreateRoomMesh(e){let t=this.roomMeshes.get(e);if(!t){t=new h.g(this.meshGroup,e,this.renderLayer);const i=new d.F(t);this.roomMeshes.add(t),this.add(t),this.add(i)}return t}}var u=i(1134),m=i(97998),p=i(3614),g=i(75730);const f=new m.Z("dam-loader");class y{constructor(e){this.chunkFactory=e,this.decoder=i(92011)}async load(e,t,i){f.time("download");const s=await t.get(e,{responseType:"arraybuffer",onProgress:i});return f.timeEnd("download"),this.parse(s)}parse(e){f.time("parse proto");const t=this.decoder.binary_mesh.read(new p(e));f.timeEnd("parse proto"),f.time("convert to webgl");const i=this.convertProtobufToSceneObject(t);return f.timeEnd("convert to webgl"),i}convertProtobufToSceneObject(e){if(0===e.chunk.length)return f.warn("No chunks in damfile..."),[];const t=new a.Matrix4;t.set(1,0,0,0,0,0,1,0,0,-1,0,0,0,0,0,1);return e.chunk.map((e=>{const i=new a.BufferGeometry;i.setAttribute("position",new a.BufferAttribute(new Float32Array(e.vertices.xyz,0,3),3)),e.vertices.uv.length>0&&i.setAttribute("uv",new a.BufferAttribute(new Float32Array(e.vertices.uv,0,2),2)),i.setIndex(new a.BufferAttribute(new Uint32Array(e.faces.faces,0,1),1)),i.applyMatrix4(t),i.computeBoundingBox();const{group:s,subgroup:o}=(0,g.xc)(e.chunk_name);return this.chunkFactory(s,o,i,e.material_name)}))}}var w=i(21270),v=i(31362);const b=new m.Z("mesh");class M extends u.e{constructor(e,t,i=o.o.ALL,s){super(),this.uuid=e,this.api=t,this.renderLayer=i,this.damMeshUrls=s,this.floorMeshes=new l.Z((e=>e.meshGroup)),this.built=!1,this.name=`ModelMesh:${e}`,this.layers.mask=i.mask}dispose(){super.dispose(),this.floorMeshes.mapElements((e=>{e.dispose(),this.remove(e)})),this.floorMeshes.clear(),this.built=!1}reset(){this.floorMeshes.mapElements((e=>{e.reset(),this.remove(e)})),this._chunks.length=0,this.built=!1}async load(e){const{roomMeshData:t}=e;(0,v.Q0)()&&(e.chunks=[]);let i=e.chunks?e.chunks:await class{static async load(e,t,i,s,o,r=0){const n=new y(i),a=s[r];if(!a)return Promise.reject("No suitable model file found...");const h=await a.url.get();return n.load(h,t,o).catch((()=>this.load(e,t,i,s,o,++r)))}}.load(this.uuid,this.api,e.chunkFactory,this.damMeshUrls,e.onProgress).catch((e=>{b.error(e);const t=e instanceof Error?e.message:"Failed to load model mesh";throw new n(t)}));if(0===i.length){b.warn("No geometry found for model, loading faux geometry, disabling outside view-mode");const t=new a.PlaneGeometry(5,5,1,1);t.rotateX(-Math.PI/2),t.computeBoundingBox();const s=e.chunkFactory(0,0,t);s.sharedState.forEachChunkMaterial((e=>e.visible=!1)),i=[s]}i.forEach((e=>{this.addChunk(e)})),this.build(t)}addChunk(e){const t=this.getOrCreateFloorMesh(e.meshGroup);this._chunks.push(e),t.addChunk(e)}build(e){var t,i;if(this.built)throw new Error("build() should only be called once");let s=0,o=0;for(const e of this.floorMeshes){this.add(e);for(const o of e.roomMeshes)o.build(),o.geometry.boundsTree||null===(i=(t=o.geometry).computeBoundsTree)||void 0===i||i.call(t),s++;e.build(),o++}b.debug(`FloorMeshes: ${o} RoomMeshes: ${s} Chunks: ${this._chunks.length}`),this.boundingBox.makeEmpty();for(const e of this.floorMeshes)this.boundingBox.union(e.boundingBox);this.size=this.boundingBox.getSize(this.size),this.center=this.boundingBox.getCenter(this.center),e.root=this,e.floors=new Set(this.floorMeshes),e.rooms=this.roomMeshes,e.commit(),this.built=!0}get roomMeshes(){const e=new Set;for(const t of this.floorMeshes)for(const i of t.roomMeshes)e.add(i);return e}async initTextureLoader(e,t){var i;(i=e.textureQualityMap).reset(),i.registerQualities(w.V.Standard,512,.048,.5,"low"),i.registerQualities(w.V.Standard,2048,.012,.5,"high"),e.setModel(this,this.chunks,t,(()=>1/0)),await e.loadAll(e.textureQualityMap.min(w.V.Standard))}registerCollision(e){e.registerMesh(this,!0);for(const t of this.roomMeshes)e.registerSnappingMeshGeometry(t.name,t.geometry,{meshGroup:t.meshGroup})}setTextureQuality(e,t,i){e.setQuality(t,i)}onUpdate(){}getOrCreateFloorMesh(e){let t=this.floorMeshes.get(e);return t||(t=new c(e,this.renderLayer),this.floorMeshes.add(t),this.add(t)),t}}var T=i(92810),D=i(26568);const x=async function({uuid:e,renderLayer:t,engine:i,chunkFactory:o,roomMeshData:r}){const[n,a]=await Promise.all([i.getModuleBySymbol(T.Vs),i.market.waitForData(D.T)]),h=new M(e,n.getApi(),t,a.model.meshUrls||[]);return await h.load({roomMeshData:r,chunkFactory:o,onProgress:e=>{i.broadcast(new s.Z(e.loaded,e.total))}}),h}},49648:(e,t,i)=>{"use strict";i.r(t),i.d(t,{createModelMesh:()=>te});var s=i(92810),o=i(9037),r=i(17878),n=i(56692),a=i(2032),h=i(90365),l=i(81396);const d=new l.Vector3,c=(new l.Matrix4).set(1,0,0,0,0,0,-1,0,0,1,0,0,0,0,0,1),u=c.clone().invert();function m(e,t){if(!1!==t(e)&&e.children)for(const i of e.children)m(i,t)}function p(e,t){const{box:i,boxTransformInverse:s}=function(e){let t=g.get(e);if(!t){const i=e.boundingVolume.box,s=new l.Box3,o=new l.Matrix4,r=new l.Vector3(i[3],i[4],i[5]),n=new l.Vector3(i[6],i[7],i[8]),a=new l.Vector3(i[9],i[10],i[11]),h=r.length(),d=n.length(),c=a.length();r.normalize(),n.normalize(),a.normalize(),0===h&&r.crossVectors(n,a),0===d&&n.crossVectors(r,a),0===c&&a.crossVectors(r,n),o.set(r.x,n.x,a.x,i[0],r.y,n.y,a.y,i[1],r.z,n.z,a.z,i[2],0,0,0,1).invert(),s.min.set(-h,-d,-c),s.max.set(h,d,c),t={box:s,boxTransformInverse:o},g.set(e,t)}return t}(t);return d.copy(e).applyMatrix4(c).applyMatrix4(s),i.containsPoint(d)}const g=new WeakMap;function f(e){for(var t;e;){const i=null===(t=e.extras)||void 0===t?void 0:t.level;if("number"==typeof i)return i;e=e.parent}return-1}var y=i(26269),w=i(99935),v=i(1134),b=i(82668),M=i(35895),T=i(46950),D=i(97998),x=i(28714),S=i(95912),C=i(60047),P=i(35048),O=i(82854),k=i(1217);class B{constructor(e){this.parser=e,this.name="CrossModelTextureCache"}static addToLoader(e){e.pluginCallbacks.unshift((e=>new B(e)))}loadTexture(e){var t;const i=this.parser,s=i.json,o=s.textures[e];let r=o.source;Object.keys(o.extensions).forEach((e=>r=r||o.extensions[e].source));const n=null===(t=s.images[r])||void 0===t?void 0:t.uri;if(void 0===n)return null;const a=A.get(n);if(a)return a;let h=i._invokeOne((t=>t===this?null:t.loadTexture&&t.loadTexture(e)));h||(h=i.loadTexture(e));const l=h.then((e=>{const t=()=>{A.delete(n),null==e||e.removeEventListener("dispose",t)};return null==e||e.addEventListener("dispose",t),e}));return A.set(n,l),l}}const A=new Map;var F=i(7402),E=i(27896);class R{constructor(e){this.parser=e,this.name="MTTR_three_mesh_bvh"}async loadMesh(e){const t=async(t,i)=>{var s,o;const r=this.parser.json.meshes[e].primitives[i];if(null===(s=r.extensions)||void 0===s?void 0:s.MTTR_three_mesh_bvh){const e=null===(o=r.extensions)||void 0===o?void 0:o.MTTR_three_mesh_bvh,i={roots:(await Promise.all(e.roots.map((e=>this.parser.loadAccessor(e))))).map((e=>{const t=e.array;return t.byteLength!==t.buffer.byteLength?t.slice().buffer:t.buffer})),index:new Uint8Array(0)};t.geometry.boundsTree=E.r.deserialize(i,t.geometry,{setIndex:!1})}},i=[],s=await this.loadMeshInternal(e);if(s)if("Group"===s.type){const e=s;i.push(...e.children.map(((e,i)=>t(e,i))))}else"Mesh"===s.type&&i.push(t(s,0));return await Promise.all(i),s}async loadMeshInternal(e){const t=this.parser,i=t,s=this.parser.json.meshes[e],o=s.primitives,n=[];for(let e=0,s=o.length;e<s;e++){const s=void 0===o[e].material?t.createDefaultMaterial(i.cache):t.getDependency("material",o[e].material);n.push(s)}return n.push(t.loadGeometries(o)),Promise.all(n).then((function(i){const n=i.slice(0,i.length-1),a=i[i.length-1],h=[];for(let i=0,l=a.length;i<l;i++){const l=a[i],d=o[i];let c;const u=n[i];if(d.mode!==I.TRIANGLES&&void 0!==d.mode)throw new Error("THREE.GLTFLoader: Primitive mode unsupported: "+d.mode);c=new F.g(0,0,r.o.DEFAULT),c.geometry=l,c.material=u,c.name=t.createUniqueName(s.name||"mesh_"+e),V(c,s),t.assignFinalMaterial(c),h.push(c)}if(1===h.length)return h[0];const d=new l.Group;for(let e=0,t=h.length;e<t;e++)d.add(h[e]);return d}))}}const I={FLOAT:5126,FLOAT_MAT3:35675,FLOAT_MAT4:35676,FLOAT_VEC2:35664,FLOAT_VEC3:35665,FLOAT_VEC4:35666,LINEAR:9729,REPEAT:10497,SAMPLER_2D:35678,POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6,UNSIGNED_BYTE:5121,UNSIGNED_SHORT:5123};function V(e,t){void 0!==t.extras&&"object"==typeof t.extras&&Object.assign(e.userData,t.extras)}var _=i(72348),L=i(51524);class N extends _.KTX2Loader{constructor(e,t,i){super(i),this.urlSigner=e,this.api=t,this.taskCache=new WeakMap}load(e,t,i,s){const o=new l.CompressedTexture([],0,0,l.RGB_ETC2_Format);return this.urlSigner(e).then((async e=>{const s=await this.api.get(e,{onProgress:i,responseType:"arraybuffer",priority:L.t.tileAssetRequestPriority});let r=this.taskCache.get(s);if(!r){r={promise:this._createTexture(s)},this.taskCache.set(s,r)}const n=await r.promise;o.copy(n),o.needsUpdate=!0,t(o)})).catch(s),o}preload(){this.init()}}let U,z;var G,H=i(17099),j=i(21270);class Q extends H.Z{constructor(e,t,i,s){super(),this.urlSigner=e,this.api=t,this.onProgress=s,this.priorityCallback=i}add(e,t){return super.add(e,(async e=>{var i,s,o;if(null===(i=null==e?void 0:e.content)||void 0===i?void 0:i.uri){const i=e.content.uri;try{const r=null===(s=this.onProgress)||void 0===s?void 0:s.bind(this,e);if(e.content.uri=await this.urlSigner(i),(null===(o=e.extras)||void 0===o?void 0:o.level)===j.V.Min)return t(e).then((e=>r?function(e,t){if(e.ok&&e.body){const i=e.body.getReader(),s=e.headers.get("Content-Length");let o=s?+s:0,r=0!==o,n=0;const a=new ReadableStream({async start(e){for(;;){const{done:s,value:a}=await i.read();if(a&&(n+=a.byteLength,e.enqueue(a)),s&&(o=n,r=!0),null==t||t(new ProgressEvent("progress",{lengthComputable:r,loaded:n,total:o})),s){e.close();break}}}});e=new Response(a)}return e}(e,r):e));{const i=window.fetch;try{return window.fetch=async(e,t)=>{const i=await this.api.get(e,{responseType:"blob",priority:L.t.tileAssetRequestPriority,onProgress:r,signal:null==t?void 0:t.signal});return new Response(i)},t(e)}finally{window.fetch=i}}}finally{e.content.uri=i}}}))}}class Z extends H.Z{constructor(){super(...arguments),this.prioritizeBy=G.FRUSTUM_ERROR_THRESH,this.priorityCallback=(e,t)=>{switch(this.prioritizeBy){case G.FRUSTUM_ERROR_THRESH:return this.sortBySelectors(e,t,[e=>Number(e.__inFrustum),e=>Number(e.__error<=L.t.errorTarget),e=>e.__error,e=>e.__distanceFromCamera]);default:return this.defaultPriorityCallback(e,t)}}}sortBySelectors(e,t,i){for(const s of i){const i=this.compareTile(e,t,s);if(null!==i)return i}return 0}compareTile(e,t,i){const s=i(e),o=i(t);return s===o?null:s>o?1:-1}defaultPriorityCallback(e,t){return e.__depth!==t.__depth?e.__depth>t.__depth?-1:1:e.__inFrustum!==t.__inFrustum?e.__inFrustum?1:-1:e.__used!==t.__used?e.__used?1:-1:e.__error!==t.__error?e.__error>t.__error?1:-1:e.__distanceFromCamera!==t.__distanceFromCamera?e.__distanceFromCamera>t.__distanceFromCamera?-1:1:0}}!function(e){e.DEFAULT="DEFAULT",e.FRUSTUM_ERROR_THRESH="FRUSTUM_ERROR_THRESH"}(G||(G={}));var W=i(17988),$=i(75730);const q=3;class K{constructor(e,t,i,s,o,r,n){this.container=e,this.threeRenderer=t,this.chunkFactory=i,this.tilesetInfo=s,this.commandBinder=o,this.api=r,this.gltfConfig=n,this.name="MttrTileLoader",this.log=new D.Z("3d-tiles"),this.loadStats={startTimes:{start:0,tileset:0,lod0:0},timings:{tileset:"",lod0:""}},this.lodDeferreds=[],this.tileProgress=new WeakMap,this.onChunksLoaded=new Set,this.onChunksUnloaded=new Set,this.tileSetLoaded=!1,this.minimalLoaded=!1,this.minimalTileCount=0,this.signUrl=async e=>{if(e.startsWith("blob"))return e;return(await this.tilesetInfo.urlTemplate.get()).replace(L.t.urlTemplateToken,e)},this.onTileGltfDownloadProgress=(e,t)=>{this.tileProgress.set(e,t.lengthComputable?.5*t.loaded/t.total:0),this.checkLoadStatus()},this.checkLoadStatus=(()=>{const e=[];let t=!1,i=!1;return(0,S.P)((()=>{t&&i||!this.tileSetLoaded||(0===e.length&&(e.push([],[]),this.tilesRenderer.traverse(((t,i,s)=>{var o;const r=null===(o=t.extras)||void 0===o?void 0:o.level;return 0!==r&&1!==r||e[r].push(t),!1}),null)),t||(t=this.notifyIfFullyLoaded(0,e,!0),t&&(this.tilesRenderer.update(),this.log.debug("LOD0 fully downloaded, allow showing more lods"),this.minimalLoaded=!0,this.minimalTileCount=e[0].length,this.loadStats.timings.lod0=(performance.now()-this.loadStats.startTimes.lod0).toFixed(1)+"ms")),i||(i=this.notifyIfFullyLoaded(1,e,!1),i&&(e.length=0)))}),16)})()}async init(){this.loadStats.startTimes.start=performance.now();const e=await this.signUrl(this.tilesetInfo.rootFilename);this.tilesRenderer=new P.I(e),this.tilesRenderer.manager=x.U;const t=function(e,t,i,s,o){U||(U=new O.DRACOLoader(t),U.setDecoderPath(`${o.dracoDecoderPath}`),U.preload());const r=new k.GLTFLoader(t);return r.setDRACOLoader(U),r.register((e=>new R(e))),B.addToLoader(r),z||(z=new N(i,s,t),z.setTranscoderPath(o.basisTranscoderPath),z.detectSupport(e),z.preload()),r.setKTX2Loader(z),r}(this.threeRenderer,this.tilesRenderer.manager,this.signUrl,this.api,this.gltfConfig);this.configureTilesRenderer(this.tilesRenderer,t)}setCamera(e,t,i){this.tilesRenderer.setCamera(e),this.tilesRenderer.setResolution(e,t,i)}hasCamera(e){return this.tilesRenderer.hasCamera(e)}deleteCamera(e){return this.tilesRenderer.deleteCamera(e)}notifyOnChunksLoaded(e){return(0,M.k1)((()=>this.onChunksLoaded.add(e)),(()=>this.onChunksLoaded.delete(e)),!0)}notifyOnChunksUnloaded(e){return(0,M.k1)((()=>this.onChunksUnloaded.add(e)),(()=>this.onChunksUnloaded.delete(e)),!0)}notifyOnLodProgress(e,t){this.getLodDeferred(e).progress(t)}awaitLod(e){return this.getLodDeferred(e).nativePromise()}getLodDeferred(e){return this.lodDeferreds[e]||(this.lodDeferreds[e]=new T.Q)}update(){const{minimalLoaded:e,tilesRenderer:t}=this;e?(t.displayActiveTiles=L.t.displayActiveTiles,t.loadSiblings=L.t.loadSiblings,t.stopAtEmptyTiles=L.t.stopAtEmptyTiles,t.autoDisableRendererCulling=L.t.autoDisableRendererCulling,t.downloadQueue.maxJobs=L.t.downloadQueueConcurrency,t.parseQueue.maxJobs=L.t.parseQueueConcurrency):(t.loadSiblings=!0,t.downloadQueue.maxJobs=1/0,t.parseQueue.maxJobs=1/0),t.optimizeRaycast=L.t.optimizeRaycast,t.errorTarget=L.t.errorTarget,t.lruCache.maxSize=e?Math.max(L.t.lruMaxTiles,this.minimalTileCount):1/0,t.update(),t.lruCache.minSize=L.t.lruMinExtraTiles+t.lruCache.usedSet.size,t.lruCache.unloadPercent=L.t.lruUnloadPercent}getDownloadParseStatus(){return this.tilesRenderer.stats}configureTilesRenderer(e,t){const i=e.calculateError.bind(e);e.calculateError=e=>{i(e),this.adjustScreenSpaceError&&(e.__error=this.adjustScreenSpaceError(e.__error,e))},e.manager.addHandler(/\gltf$/,t),e.manager.addHandler(/\glb$/,t),e.errorTarget=L.t.errorTarget,e.loadSiblings=L.t.loadSiblings,e.stopAtEmptyTiles=L.t.stopAtEmptyTiles,e.displayActiveTiles=L.t.displayActiveTiles;const s=e.preprocessNode.bind(e);e.preprocessNode=function(e,t,i){return s(e,t,"")},this.configurePriorityQueues(),this.container.add(e.group);const o=(new l.Quaternion).setFromAxisAngle(new l.Vector3(-1,0,0),l.MathUtils.degToRad(90));this.container.quaternion.copy(o),this.container.updateMatrixWorld(!0),e.onLoadTileSet=this.onLoadTileset.bind(this),e.onLoadModel=this.onLoadModel.bind(this),e.onDisposeModel=this.onDisposeModel.bind(this);const r=e.setTileActive.bind(e);e.setTileActive=(e,t)=>{r(e,t),this.onTileActiveChange&&this.onTileActiveChange(e,t)};const n=e.setTileVisible.bind(e);e.setTileVisible=(e,t)=>{n(e,t),this.onTileVisibleChange&&this.onTileVisibleChange(e,t)};const a=e.tileInView.bind(e);e.tileInView=e=>{let t=a(e);return this.adjustTileInView&&(t=this.adjustTileInView(t,e)),t}}configurePriorityQueues(){const{tilesRenderer:e}=this;e.downloadQueue=new Q(this.signUrl,this.api,e.downloadQueue.priorityCallback,this.onTileGltfDownloadProgress),e.parseQueue=new Z;const t=e=>{this.commandBinder.issueCommand(new C._("mesh/tiled/priorityQueue",e,100))};e.parseQueue.schedulingCallback=t,e.downloadQueue.schedulingCallback=t}async onLoadModel(e,t){const i=function(e,t,i){var s;const o=[],r=(null===(s=t.extras)||void 0===s?void 0:s.id)||""+e.id;return e.name=r,e.traverse((s=>{var n,a;if(s.matrixAutoUpdate=!1,s.updateMatrix(),s instanceof F.g){const h=new W.F(s);e.add(h);const{group:d,subgroup:c,chunkIndex:u}=(0,$.xc)(`${r}-${s.name}`),m=s.material,p=m.map;let g=null!==(n=null==p?void 0:p.name)&&void 0!==n?n:m.name;g=g.replace(".jpg",""),p&&(p.encoding=l.LinearEncoding);const f=i(d,c,s.geometry,g);f.textureLODInfo=function(e,t){if(t&&(null==t?void 0:t.maxTextureSize)&&(null==t?void 0:t.texelSize)&&(null==t?void 0:t.textureScale)&&t.textureScale>0){const i=t.textureScale;return{name:e,maxTexelSize:.001*t.texelSize,maxTextureSize:t.maxTextureSize,minTexelSize:1/i*t.texelSize*.001,minTextureSize:i*t.maxTextureSize,minScale:i}}return null}(g,t.extras)||void 0,f.lod=(null===(a=t.extras)||void 0===a?void 0:a.level)||0,f.chunkIndex=u,f.notifyOnMaterialUpdated((e=>{s.material=e}));const y={};y.map=p,s.buildWithTileChunk(f),s.material=f.setMaterialsUniform(y),f.name=s.name,f.embeddedTexture=y.map,o.push(f)}})),e.matrixWorld.copy(e.matrix),e.matrixWorld.premultiply(u),e.children.forEach((e=>e.updateMatrixWorld(!0))),o}(e,t,this.chunkFactory);for(const e of i)this.container.addChunk(e);for(const e of this.onChunksLoaded.values())e(i,t);this.onModelLoaded&&this.onModelLoaded(e,t),this.tileProgress.set(t,1),await new Promise((e=>setTimeout(e,0))),this.checkLoadStatus()}onDisposeModel(e,t){e.traverse((e=>{if((0,y.oR)(e)){const i=e.chunks;if(!i)return void this.log.error("Missing chunks from RoomMesh");for(const e of i)this.container.removeChunk(e);for(const e of this.onChunksUnloaded.values())e(i,t)}})),this.onModelUnloaded&&this.onModelUnloaded(e,t),e.traverse((e=>{(0,y.oR)(e)&&e.reset()}))}onLoadTileset(e,t){var i;const s=!this.tileSetLoaded;let o="";s&&(o=(performance.now()-this.loadStats.startTimes.start).toFixed(1),this.loadStats.timings.tileset=o+"ms");const r=null===(i=t.match(/[^/]*\.json/))||void 0===i?void 0:i[0];this.log.debug(`Tileset ${r} load${s?`: ${o}ms`:""}`,e),s&&(this.loadStats.startTimes.lod0=performance.now()),this.tileSetLoaded=!0,this.onTilesetLoaded&&this.onTilesetLoaded(e,t),this.tilesRenderer.update()}notifyIfFullyLoaded(e,t,i){if(!t[e])return!1;const s=t[e],o=s.filter((e=>e.__loadingState!==q)),r=this.getLodDeferred(e);return i&&r.notify(s.reduce(((e,t)=>e+(this.tileProgress.get(t)||0)/s.length),0)),0===o.length&&r.resolve(),0===o.length}}var Y=i(53203);class J extends v.e{constructor(e,t=r.o.ALL,i){super(),this.uuid=e,this.renderLayer=t,this.chunkSharedState=i,this.boundingBox=new l.Box3,this.size=new l.Vector3,this.center=new l.Vector3,this.tilesByChunkId=new Map,this.bindings=[],this.roomMeshesByTile=new Map,this.activeRoomMeshes=new Set,this.visibleRoomMeshes=new Set,this.activeTiles=new Set,this.tileActiveDescendantCounts=new WeakMap,this.maxLOD=L.t.maxLOD,this.isActiveRoomMeshFilter=e=>this.activeRoomMeshes.has(e),this.isActiveRoomMeshSnapFilter=e=>{var t,i,s;const o=null===(t=e.meta)||void 0===t?void 0:t.tile;if(o){const{snappingMaxLOD:e}=L.t,t=null!==(s=null===(i=o.extras)||void 0===i?void 0:i.level)&&void 0!==s?s:1/0;if(t===e&&(this.tileActiveDescendantCounts.get(o)||0)>0||t<=e&&this.activeTiles.has(o))return!0}return!1},this.layers.mask=t.mask,this.overriddenIsTileInView=this.overriddenIsTileInView.bind(this)}dispose(){super.dispose(),this.bindings.forEach((e=>e.cancel())),this.bindings=[]}async load(e,t,i,s,o,r,l,d,c,m,p){this.renderer=t,this.canvasData=o,l.root=this,this.maxLOD=L.t.maxLOD=r.maxLOD,this.tileLoader=new K(this,t.threeRenderer,d,r,e.commandBinder,m,p),await this.tileLoader.init(),this.tileLoader.setCamera(i,s.width,s.height),this.bindings.push(e.subscribe(n.a,(e=>{this.tileLoader.setCamera(i,e.width,e.height)}))),this.tileLoader.onModelLoaded=(e,t)=>{let i=this.roomMeshesByTile.get(t);i||(i=[],this.roomMeshesByTile.set(t,i)),e.traverse((e=>{e.layers.mask=this.renderLayer.mask,(0,y.oR)(e)&&(l.rooms.add(e),i.push(e),this.inputIni&&this.registerMeshForCollision(this.inputIni,e,t))})),l.commit()},this.tileLoader.onModelUnloaded=(e,t)=>{e.traverse((e=>{e instanceof F.g&&(l.rooms.delete(e),this.inputIni&&this.unregisterMeshFromCollision(this.inputIni,e))})),l.commit(),this.tileLoader.onTileActiveChange(t,!1),this.tileLoader.onTileVisibleChange(t,!1),this.roomMeshesByTile.delete(t)},this.tileLoader.onTileActiveChange=(e,t)=>{var i;const s=t?"add":"delete";null===(i=this.roomMeshesByTile.get(e))||void 0===i||i.forEach((e=>{this.activeRoomMeshes[s](e)})),this.activeTiles[s](e);for(let i=e.parent;i;i=i.parent){const e=this.tileActiveDescendantCounts.get(i)||0;this.tileActiveDescendantCounts.set(i,e+(t?1:-1))}},this.tileLoader.onTileVisibleChange=(e,t)=>{var i;null===(i=this.roomMeshesByTile.get(e))||void 0===i||i.forEach((e=>{this.visibleRoomMeshes[t?"add":"delete"](e)}))},this.initTileLodAdjustments(this.tileLoader,c),this.tileLoader.notifyOnLodProgress(0,(t=>{e.broadcast(new b.Z(t,1))})),this.tileLoader.notifyOnChunksLoaded(((e,i)=>{var s;0===(null===(s=i.extras)||void 0===s?void 0:s.level)&&e.forEach((e=>{t.threeRenderer.initTexture(e.embeddedTexture)}))})),this.tileLoader.update(),this._detail="minimal",await this.tileLoader.awaitLod(L.t.initialMaxLOD),this._detail="default",this.tileLoader.tilesRenderer.getBounds(this.boundingBox),this.boundingBox.applyMatrix4(u),this.boundingBox.getSize(this.size),this.boundingBox.getCenter(this.center),e.broadcast(new a.em(h.Y.StreamingMesh)),e.broadcast(new a.em(h.Y.StreamingTexture));const g=this.size.clone().multiplyScalar(.5).length(),{smallMeshThreshold:f,smallMeshErrorMultiplier:w}=L.t;g<f&&(L.t.errorTarget=Math.min(L.t.errorTarget,g*w))}initTextureLoader(e,t){return e.autoLoadTiles=!1,e.setModel(this,this._chunks,t,(()=>L.t.limitMemoryUsage?L.t.allocatedMegsBeforeLimitingLod*2**20:1/0)),this.bindings.push(this.tileLoader.notifyOnChunksLoaded((t=>{e.addChunkSlots(t);for(const e of this.onChunksLoaded.values())e(t)})),this.tileLoader.notifyOnChunksUnloaded((t=>{e.removeChunks(t)}))),e.allowTextureDownload=()=>{const{downloading:e,parsing:t}=this.tileLoader.getDownloadParseStatus();return 0===e&&0===t},Promise.resolve()}initTileLodAdjustments(e,t){e.adjustTileInView=this.overriddenIsTileInView;const i=this.tilesByChunkId,s=new Map;let o=0;this.bindings.push(e.notifyOnChunksLoaded(((e,t)=>{for(const s of e)i.set(s.id,t)})),t.notifyOnNewSighting(((e,t)=>{o++;const r=i.get(e.id);if(r){for(let e=r.parent;e;e=e.parent)s.set(e,o);m(r,(e=>!(e!==r&&!p(t.point,e))&&(s.set(e,o),!0)))}})),this.tileLoader.notifyOnChunksUnloaded((e=>{for(const t of e)i.delete(t.id),t.dispose()}))),e.adjustScreenSpaceError=(e,t)=>{var i,r;if(1!==L.t.errorMultiplierRaycastOcclusion){(null!==(i=s.get(t))&&void 0!==i?i:-1/0)<o-Y.EJ.sightingMaxAge&&(e*=L.t.errorMultiplierRaycastOcclusion)}if(1!==L.t.errorMultiplierHiddenFloors){(null===(r=this.roomMeshesByTile.get(t))||void 0===r?void 0:r.some((e=>e.chunks.some((e=>e.getOpacity()>Y.xx.FADE_TILE_VISIBLE_THRESHOLD)))))||(e*=L.t.errorMultiplierHiddenFloors)}return f(t)<L.t.minLOD&&(e=Math.max(e,L.t.errorTarget+1e-10)),e}}registerCollision(e){this.inputIni=e,this.tileLoader.tilesRenderer.forEachLoadedModel(((t,i)=>{t.traverse((t=>{(0,y.oR)(t)&&this.registerMeshForCollision(e,t,i)}))}))}addChunk(e){this._chunks.push(e)}removeChunk(e){const t=this._chunks.indexOf(e);this._chunks.splice(t,1)}onUpdate(){L.t.disableTileUpdates||(this.tileLoader.tilesRenderer.setResolution(this.renderer.getCamera(),this.canvasData.width,this.canvasData.height),this.tileLoader.update(),this.checkDispose()),this.downgradeIfMemoryConstrained()}setTextureQuality(e,t,i){e.setQuality(w.S.LOW,i)}get visibleChunks(){const e=this.visibleRoomMeshes;return{*[Symbol.iterator](){for(const t of e)for(const e of t.chunks)yield e}}}registerMeshForCollision(e,t,i){e.registerMesh(t,!0,this.isActiveRoomMeshFilter),t.chunks[0].lod<=L.t.snappingMaxLOD&&e.registerSnappingMeshGeometry(t.name,t.geometry,{tile:i,meshGroup:t.meshGroup},this.isActiveRoomMeshSnapFilter)}unregisterMeshFromCollision(e,t){e.unregisterMesh(t),e.unregisterSnappingMeshGeometry(t.name)}overriddenIsTileInView(e,t){const i=f(t.parent);if("minimal"===this._detail&&-1===i)return!0;let s=L.t.nonMeshMaxLOD;return"max"===this._detail&&(s=L.t.maxLOD),"minimal"===this._detail&&(s=L.t.initialMaxLOD),!(i>=s)&&e}overrideMaxDetail(e){if(e!==this._detail)switch(this._detail=e,this._detail){case"minimal":this.setMaxLOD(0);break;case"max":this.setMaxLOD(null);break;default:const e=this.size.length()/2<L.t.smallMeshThreshold;this.setMaxLOD(e?L.t.maxLOD:L.t.nonMeshMaxLOD)}}setMaxLOD(e){e=null!=e?e:this.maxLOD,L.t.maxLOD=e}downgradeIfMemoryConstrained(){this.renderer.estimatedGPUMemoryAllocated()>2**20*(L.t.limitMemoryUsage?L.t.allocatedMegsBeforeLimitingLod:1/0)&&L.t.maxLOD>0&&this.setMaxLOD(L.t.maxLOD-1)}checkDispose(){if(L.t.disposeModel){const e=this.tileLoader.tilesRenderer,t=e;for(const e of t.cameras)t.deleteCamera(e);const i=.001,s=new l.OrthographicCamera(-i,i,i,-i,i,2*i);s.position.set(0,-1e4,0),s.rotation.setFromVector3(new l.Vector3(0,-1,0)),s.updateMatrix(),s.updateMatrixWorld(),this.tileLoader.setCamera(s,100,100),e.lruCache.minSize=0,e.lruCache.unloadPercent=1,e.lruCache.markAllUnused(),e.update(),e.dispose(),this.chunkSharedState.dispose(),L.t.disableTileUpdates=!0}}}var X=i(5332),ee=i(26568);const te=async function({uuid:e,engine:t,renderLayer:i,settings:r,roomMeshData:n,chunkFactory:a,chunkVisibilityChecker:h,chunkSharedState:l,gltfConfig:d}){r.flipDownload=!1,r.flipUpload=!1;const[c,u,m,p,g]=await Promise.all([t.getModuleBySymbol(s.Aj),t.getModuleBySymbol(s.Vs),t.market.waitForData(o.M),t.market.waitForData(X.W),t.market.waitForData(ee.T)]),f=new J(e,i,l);return await f.load(t,c,c.getCamera(),m,p,g.model.tileset,n,a,h,u.getApi(),d),f}},64210:(e,t,i)=>{"use strict";i.d(t,{O7:()=>m,dw:()=>c,ef:()=>d});var s=i(81396),o=i(53203),r=i(69984);const n=o.ZP.sightingMaxAge,a=new s.Color;let h,l=-1;const d=(e,t)=>{h||(h=new s.InstancedMesh(new s.SphereGeometry(.005,8,4),new s.MeshBasicMaterial,n),u(h));const i=new s.Matrix4;return({point:s,distance:o})=>{i.makeScale(o,o,o).setPosition(s),h.setMatrixAt(++l%n,i),h.instanceMatrix.needsUpdate=!0;for(let t=n;t--;)h.setColorAt((l-t+n)%n,a.set(e).multiplyScalar(1-t/n));h.instanceColor&&(h.instanceColor.needsUpdate=!0),h.parent||t.scene.add(h)}},c=()=>{var e;h&&(null===(e=h.parent)||void 0===e||e.remove(h),u(h))};function u(e){const t=(new s.Matrix4).makeScale(0,0,0);for(let i=0;i<n;i++)e.setMatrixAt(i,t)}function m(e,t){const i=t.get(e);if(i.lod in p){if(!(e in p)){const o=p[i.lod],r=.3+.6*Math.max(0,t.order(i.lod,!1).indexOf(e));p[e]=new s.Vector4(o.x,o.y,o.z,r)}return p[e]}return(0,r.G1)(.5,e)}const p={0:new s.Vector4(1,0,0,1),1:new s.Vector4(0,1,0,1),2:new s.Vector4(0,0,1,1),3:new s.Vector4(1,1,0,1),4:new s.Vector4(1,0,1,1),5:new s.Vector4(1,1,1,1),6:new s.Vector4(0,1,1,1),7:new s.Vector4(0,0,0,1)}},75730:(e,t,i)=>{"use strict";i.d(t,{bo:()=>a,k7:()=>n,ko:()=>r,xc:()=>o});var s=i(81396);function o(e){const t=e.match(/group([0-9]+)/),i=e.match(/sub([0-9]+)/),s=e.match(/type([0-9]+)/),o=e.match(/mirror([0-9]+)/),r=e.match(/window([0-9]+)/),n=e.match(/chunk([0-9]+)/),a=e.match(/_chunk([0-9]+)/),h=s?parseInt(s[1],10):o&&!isNaN(parseInt(o[1],10))?100:r&&!isNaN(parseInt(r[1],10))?101:0;return{group:t?parseInt(t[1],10):0,subgroup:i?parseInt(i[1],10):0,chunkIndex:n?parseInt(n[1],10):0,nodeIndex:a?parseInt(a[1],10):0,type:h}}function r(e,t=!1){let i=e.getAttribute("barycentric");if(i)return i;const o=(e.getIndex()||e.getAttribute("position")).count/3,r=[];for(let e=0;e<o;e++){const i=t?1:0;e%2==0?r.push(0,0,1,0,1,0,1,0,i):r.push(0,1,0,0,0,1,1,0,i)}const n=new Float32Array(r);return i=new s.BufferAttribute(n,3),e.setAttribute("barycentric",i),i}function n(e=16777215*Math.random(),t=1){const i=function(e,t,i,s=1){const o=document.createElement("canvas");o.width=t,o.height=i;const r=o.getContext("2d");return r.fillStyle=`rgba(${255*e.r|0},${255*e.g|0},${255*e.b|0}, ${255*s|0})`,r.fillRect(0,0,t,i),o}(new s.Color(e),1,1,t),o=new s.CubeTexture([i,i,i,i,i,i]);return o.format=s.RGBAFormat,o.needsUpdate=!0,o}const a=(()=>{const e=new s.Vector3,t=new s.Vector3,i=new s.Vector3,o=new s.Vector3,r=new s.Vector3,n=new s.Vector3;return a=>{const h=new s.Vector3,l=a.index,d=a.getAttribute("position");if(null!=d&&null!=l){let s=0;for(let a=0,c=l.count;a<c;a+=3){const c=l.getX(a+0),u=l.getX(a+1),m=l.getX(a+2);e.fromBufferAttribute(d,c),t.fromBufferAttribute(d,u),i.fromBufferAttribute(d,m),o.subVectors(i,t),r.subVectors(e,t),o.cross(r);const p=o.length()/2;p>0&&(s+=p,n.set(0,0,0).add(e).add(t).add(i).multiplyScalar(1/3*p),h.add(n))}s>0?h.multiplyScalar(1/s):a.boundingBox&&a.boundingBox.getCenter(h)}return h}})()},93042:(e,t,i)=>{"use strict";i.r(t),i.d(t,{Cursor:()=>o.C,SetMouseCursorCommand:()=>r.u,default:()=>n});var s=i(97542),o=i(34956),r=i(38987);class n extends s.Y{constructor(){super(...arguments),this.name="mouse-cursor",this.activeCursor=null}async init(e,t){this.config=Object.assign({classPrefix:"cursor"},e),this.bindings.push(t.commandBinder.addBinding(r.u,(async e=>this.changeCursor(e.cursor))))}changeCursor(e){const{container:t,classPrefix:i}=this.config;e!==this.activeCursor&&(this.activeCursor&&t.classList.remove(`${i}-${this.activeCursor}`),e&&t.classList.add(`${i}-${e}`),this.activeCursor=e)}}},21584:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>Fe});var s=i(97542),o=i(34029),r=i(92810),n=i(9037),a=i(64918),h=i(66777),l=i(5135),d=i(12039),c=i(41753);class u extends c.f{constructor(e){super(e),this.name="NavigationException"}}const m={Locked:"Cannot move while navigation is locked",InsideOnly:"Cannot navigate between panos when not in Panorama mode",InvalidSweep:"Not at a valid sweep",InTransition:"Cannot move while in a transition",NoDestinationFound:"Cannot move in that direction",InvalidMode:"Cannot move to mode"};var p=i(86819),g=i(23612),f=i(72996),y=i(94046),w=i(81396),v=i(33324),b=i(16769),M=i(20043),T=i(35826),D=i(38987),x=i(34956),S=i(76631);class C{constructor(e,t,i,s,o,r,n,a,h,l){this.canStartTransition=e,this.commandBinder=t,this.input=i,this.floorsViewData=s,this.navigation=o,this.sweepData=r,this.cameraModule=n,this.toolsData=a,this.meshQuery=h,this.doubleClickToEnter=l,this.bindings=[],this.cancelTransition=!1,this.didSetCursor=!1,this.targetFloorId=null,this.targetHitPoint=new w.Vector3,this.changeFloorPromise=null,this.toggleInput=e=>{this.bindings.forEach((t=>e?t.renew():t.cancel()))},this.onRoomClick=(e,t,i)=>{if(!this.canStartTransition())return!1;if(this.changeFloorPromise)return!0;if(!i||!i.point)return!1;const s=this.meshQuery.floorIdFromObject(t);if(!s)return!1;if(!this.floorsViewData.isNavigable(s))return this.onBackgroundClick();if(this.cancelTransition)return!0;const o=this.floorsViewData.currentFloorId;return 1!==this.floorsViewData.totalFloors&&s!==o?(this.targetFloorId||(this.targetFloorId=s,this.targetHitPoint.copy(i.point)),!o&&(this.changeFloorIfNeeded(),!0)):this.floorsViewData.roomSelectModeActive||this.floorsViewData.floorSelectModeActive?!!this.doubleClickToEnter||this.roomNavZoom(e,t,i):this.floorsViewData.floorSelectModeActive?(this.cancelTransition=!1,!1):(this.navigation.focus(i.point).then((()=>{this.cancelTransition||this.setFocusSweep(i),this.cancelTransition=!1})),!0)},this.roomNavZoom=(e,t,i)=>!i||(this.cancelTransition=!0,(async()=>{await this.changeFloorPromise,await this.cameraModule.cancelTransition(),await this.navigation.navigateToPanoNearIntersection(i),this.cancelTransition=!1})(),this.commandBinder.issueCommand(new D.u(x.C.DEFAULT)),!0),this.onBackgroundClick=()=>!!this.canStartTransition()&&(!!this.floorsViewData.showAllFloorsOption&&(!this.targetFloorId&&(!!this.cancelTransition||(this.cancelTransition=!0,void this.cameraModule.cancelTransition().then((()=>this.commandBinder.issueCommand(new v.Vw(null,!1)))).then((()=>{this.cancelTransition=!1})))))),this.onBackgroundHover=()=>{const{activeToolName:e}=this.toolsData;return null!==this.floorsViewData.currentFloorId&&e!==S.w1.LABELS&&e!==S.w1.ROOM_BOUNDS&&(this.didSetCursor=!0,this.commandBinder.issueCommand(new D.u(x.C.FINGER))),!1},this.onBackgroundUnhover=()=>(this.didSetCursor&&(this.commandBinder.issueCommand(new D.u(x.C.DEFAULT)),this.didSetCursor=!1),!1),this.onFloorChange=()=>{null===this.floorsViewData.currentFloorId&&this.commandBinder.issueCommand(new D.u(x.C.DEFAULT))},this.clearFloorChange=()=>{this.targetFloorId=null},this.changeFloorIfNeeded=async()=>{this.targetFloorId&&(this.changeFloorPromise=this.commandBinder.issueCommand(new v.Vw(this.targetFloorId,!1,void 0,this.targetHitPoint)),await this.changeFloorPromise,this.targetFloorId=null,this.changeFloorPromise=null)},this.bindings.push(this.input.registerMeshHandler(p.Rd,f.s.is(b.Pv),this.onRoomClick,{default:!0}),this.input.registerUnfilteredHandler(p.Rd,this.clearFloorChange),this.input.registerHandler(p.Rd,this.changeFloorIfNeeded),this.input.registerMeshHandler(p.Rd,f.s.isType(y.i),this.onBackgroundClick,{default:!0}),this.input.registerMeshHandler(g.z,f.s.isType(y.i),this.onBackgroundHover),this.input.registerMeshHandler(g.A,f.s.isType(y.i),this.onBackgroundUnhover),this.floorsViewData.makeFloorChangeSubscription(this.onFloorChange)),l&&this.bindings.push(this.input.registerMeshHandler(p.bN,f.s.is(b.Pv),this.roomNavZoom,{default:!0}))}async setFocusSweep(e){if(!this.canStartTransition())return;if(this.cancelTransition)return;const t=(0,M.bG)(this.sweepData,!1,e,this.meshQuery);t.length>0&&t[0].sweep&&await this.commandBinder.issueCommand(new T.aK(t[0].sweep.id))}}var P=i(90304),O=i(32597),k=i(12250),B=i(89553),A=i(6667),F=i(82814),E=i(16782),R=i(97998);const I=new R.Z("nav-input");class V{constructor(e,t,i){this.navigation=e,this.inputIni=t,this.insideNav=[],this.insideVrNav=[],this.toggleInput=e=>{this.insideNav.forEach((t=>e?t.renew():t.cancel()))},this.toggleVrInput=e=>{this.insideVrNav.forEach((t=>e?t.renew():t.cancel()))},this.registerInsideClickHandlers=e=>[e.registerMeshHandler(p.Rd,f.s.is(b.Pv),((e,t,i)=>this.tryExecuteAction(((e,t)=>U(e)&&_(t)),((e,t)=>_(t)&&this.navigation.navigateTowardsIntersection(t)),e,i)),{default:!0}),e.registerMeshHandler(p.Rd,f.s.isType(y.i),((e,t,i)=>this.tryExecuteAction(((e,t)=>U(e)&&_(t)),((e,t)=>_(t)&&this.navigation.navigateTowardsIntersection(t)),e,i)),{default:!0})],this.registerVrClickHandlers=e=>[e.registerMeshHandler(p.Rd,f.s.isInstanceOf(F.S),((e,t,i)=>this.tryExecuteAction(((e,t)=>U(e)&&_(t)),((e,t)=>_(t)&&this.navigation.navigateToPanoNearIntersection(t)),e,i)),{default:!0})],this.registerWheelInput=e=>[e.registerHandler(k.a,(e=>this.tryExecuteAction((e=>!0),(e=>{if(e instanceof k.a){const t=new w.Vector3(0,0,Math.sign(e.delta.y));return!!this.navigation.navigateInLocalDirection(t)}return!1}),e)))],this.hotkeys=e=>[e.registerHandler(B.e,(e=>this.tryExecuteAction((e=>(L(e)||N(e))&&e.key in V.hotkeyDirections),(e=>{if(L(e)){this.continuousMovementHotkey=e.key;const t=V.hotkeyDirections[e.key];this.navigation.setContinuousNavigationLocalDirection(t)}else N(e)&&e.key===this.continuousMovementHotkey&&this.navigation.setContinuousNavigationLocalDirection();return!1}),e)))],this.tryExecuteAction=(e,t,i,s)=>{let o=!1;try{this.navigation.isNavigationInputAllowed()&&e(i,s)&&(o=t(i,s))}catch(e){if(!(e instanceof u))throw I.warn(e),e;I.debug(e)}return o},this.insideVrNav.push(...this.registerVrClickHandlers(this.inputIni)),this.insideNav.push(...this.registerInsideClickHandlers(this.inputIni),...this.hotkeys(this.inputIni)),i&&this.insideNav.push(...this.registerWheelInput(this.inputIni))}get bindings(){return[...this.insideNav,...this.insideVrNav]}}function _(e){return void 0!==e&&void 0!==e.point}function L(e){return e instanceof B.e&&e.state===A.M.DOWN}function N(e){return e instanceof B.e&&e.state===A.M.UP}function U(e){return e instanceof p.Rd&&e.button===E.M.PRIMARY}V.hotkeyDirections={[O.R.W]:P.fU.FORWARD,[O.R.A]:P.fU.LEFT,[O.R.S]:P.fU.BACK,[O.R.D]:P.fU.RIGHT,[O.R.UPARROW]:P.fU.FORWARD,[O.R.DOWNARROW]:P.fU.BACK};var z=i(53954),G=i(41326),H=i(23254),j=i(95882),Q=i(14715),Z=i(79727),W=i(91380),$=i(73121),q=i(40731);function K(e){if(e&&"object"==typeof e&&"min"in e&&"max"in e){const t=e;if((0,q.u)(t.min)&&(0,q.u)(t.max))return!0}return!1}var Y=i(37519);function J(e){if(e&&"object"==typeof e&&"x"in e&&"y"in e){const t=e;return(0,Y.hj)(t.x)&&(0,Y.hj)(t.y)}return!1}function X(e){if(e&&"object"==typeof e&&"min"in e&&"max"in e){const t=e;if(J(t.min)&&J(t.max))return!0}return!1}var ee=i(88338),te=i(20217);class ie{constructor(e,t,i,s,o,r){this.canStartTransition=e,this.pose=t,this.cameraData=i,this.cameraModule=s,this.viewmodeData=o,this.issueCommand=r,this.focus=this.focus.bind(this),this.focusFloorplan=this.focusFloorplan.bind(this)}async focus(e,t){if(!this.canStartTransition())return;let i,s,o;K(e)?(s=e,i=s.getCenter(new w.Vector3)):X(e)?(s=new w.Box3,s.min.set(e.min.x,0,e.min.y),s.max.set(e.max.x,0,e.max.y),o=new w.Box2(e.min,e.max),i=s.getCenter(new w.Vector3)):(i=e,s=void 0);const{from:r,transition:n,mode:h}=null!=t?t:{};let{position:l,rotation:d,focalDistance:c}=this.pose;const u=this.pose.pitchFactor()<.01;if(h!==j.Ey.Dollhouse||!u&&!1!==(null==t?void 0:t.forceOrtho)||(d=(0,$.Gf)(d,-55)),o&&X(o)||(null==t?void 0:t.forceOrtho)||h===j.Ey.Floorplan){if(o||(s&&(o=new w.Box2(new w.Vector2(s.min.x,s.min.z),new w.Vector2(s.max.x,s.max.z))),!s&&i&&(o=new w.Box2(new w.Vector2(i.x-5,i.z-5),new w.Vector2(i.x+5,i.z+5)))),!o)throw new Error("Floorplan mode requires a box or point");return this.focusFloorplan(i,o,h===j.Ey.Dollhouse)}r?(l=r,d=(0,$.n0)(l,i)):s?({focalDistance:c,rotation:d,position:l}=this.getPoseForBox({box:s,targetRotation:d})):l=(0,$.fd)(d,i,c);const m={pose:{position:l,rotation:d},transitionType:null!=n?n:a.n.Interpolate,focalDistance:c,transitionTime:ee.E.TRANSITION_TIME_DH,autoOrtho:u};return this.cameraModule.moveTo(m).nativePromise()}async focusFloorplan(e,t,i){i&&await this.issueCommand(new te.ho);const s=function(e,t,i,s){const o=e.pose,r=(new w.Vector3).copy(i());let n=null,a=o.focalDistance;const h=s.min.distanceTo(s.max)/(0,$._U)(o.position.distanceTo(r),o.projection.asThreeMatrix4(),e.width)/e.screenDiagonalPx,l=h<.2,d=h>1.2,c=(l?.2+.1:1.2-.1)/h;if(t.isFloorplan()&&(r.y=o.position.y,a=o.focalDistance,n=o.projection.clone(),(l||d)&&(n.elements[0]*=c,n.elements[5]*=c)),t.isDollhouse()&&(r.y=o.fovCorrectedPosition().y,a=o.fovCorrectedFocalDistance(),l||d)){const e=o.fovCorrectedFocalDistance()/c,t=o.fovCorrectedPosition().y-o.fovCorrectedFocalDistance()+e;r.y=t,a=e}return{position:r,projection:n,focalDistance:a}}(this.cameraData,this.viewmodeData,(()=>e),t);return this.cameraModule.moveTo({pose:{position:s.position},projection:s.projection?s.projection:void 0,transitionType:a.n.Interpolate,transitionTime:ee.E.TRANSITION_TIME_ROOM,autoOrtho:this.cameraData.pose.autoOrtho,focalDistance:s.focalDistance}).nativePromise()}getPoseForBox({box:e,targetRotation:t}){let i=this.pose;i.pitchFactor()<.01&&(i=i.clone(),i.unapplyPhiBasedFovSquish(),i.resetProjMatrix());const s=t||i.rotation;const o=i.aspect(),r=e.getCenter(new w.Vector3),n={targetPosition:r,targetRotation:s.clone(),angleDown:undefined,box:e,fovY:i.fovY(),aspectRatio:o},a=(0,$.YN)(n),h=r.distanceTo(a.position);return{position:a.position,rotation:a.rotation,focalDistance:h}}}var se=i(98169),oe=i(81248),re=i(69505),ne=i(2569),ae=i(59370),he=i(93642);const le=!0,de=8,ce=.1,ue=-25,me=500,pe=-2,ge=32,fe=32;class ye{constructor(e,t,i,s,o){this.cameraData=e,this.sweepData=t,this.viewmodeModule=i,this.picking=s,this.issueCommand=o,this.tryNavigateToPoint=async(e,t,i,s,o,r,n)=>{const h=n?n.slice():[],l=r?r.slice():[];if(h.push(se._T()),h.push(se._k()),l.push(this.scoreByLatitude(e,ge)),t===a.n.Interpolate&&(0,j.Bw)(this.viewmodeModule.currentMode)&&this.sweepData.currentSweep){const t=this.sweepData.currentSweep,i=this.sweepData.getSweep(t),s=i.position.clone().sub(e).normalize();h.push(se.T3(i)),l.push(oe.o7(e,s))}h.push(this.withinLatitudeFilter(e)),h.push(this.notTooCloseFilter(e)),le||h.push(this.notTooFarFilter(e)),l.push(oe.Dv(e,pe));const c=this.sweepData.sortByScore(h,l),u=this.sweepData.currentSweepObject,m=new w.Vector3;let p=null,g=!1,f=null;const y=(0,j.Bw)(this.viewmodeModule.currentMode),v=null!=o?o:15,b=y&&u?new Set(u.neighbours.filter((e=>this.sweepData.getSweep(e).position.distanceTo(u.position)<v)).concat([u.id])):new Set;for(const o of c)if(this.sweepCanSeeNote(e,o.sweep)){if(f=o.sweep,t===a.n.Interpolate||b.has(f.id)){m.copy(e).sub(f.position).normalize(),g=!0;const o=this.getSweepToPointRotation(f.position,e,s),r=this.cameraData.pose.projection.asThreeMatrix4(),{width:n,height:h}=this.cameraData,l=(0,ae.bD)(e,f.position,o,n,h,r);i&&i(l),p=y?this.issueCommand(new d.ju({transition:a.n.Interpolate,sweep:f.id,rotation:o})):this.viewmodeModule.switchToMode(j.Ey.Panorama,t,{rotation:o,sweepID:f.id})}break}return!(!f||!g&&t===a.n.Interpolate)&&(p||(p=this.issueCommand(new d.ju({transition:t,transitionTime:me,sweep:f.id,rotation:this.getSweepToPointRotation(f.position,e,s)}))),p&&await p,!0)},this.getSweepToPointRotation=(e,t,i)=>{const s=(0,$.n0)(e,t);return i&&s.multiply(i),(0,ne.Z)(s)},this.sweepCanSeeNote=(()=>{const e=new w.Vector3;return(t,i)=>{e.copy(t).sub(i.position);const s=e.length();e.normalize();let o=this.picking.pick(i.position,e,b.Pv);return(!o||o.distance>s)&&(o=this.picking.pick(t,e.negate(),b.Pv)),!o||s<=o.distance}})(),this.notTooCloseFilter=e=>t=>Math.abs(t.position.x-e.x)>ce||Math.abs(t.position.z-e.z)>ce,this.notTooFarFilter=e=>t=>t.position.distanceTo(e)>de,this.withinLatitudeFilter=e=>t=>{const i=(new w.Vector3).copy(e).sub(t.position),s=-Math.atan(i.y/Math.sqrt(i.x*i.x+i.z*i.z)),o=(0,re.Id)(ue);return he.zf-o<s&&s<he.uQ+o},this.scoreByLatitude=(()=>{const e=new w.Vector3;return(t,i)=>s=>{e.copy(t).sub(s.position);const o=Math.abs(Math.atan(e.y/Math.sqrt(e.x*e.x+e.z*e.z)))/(Math.PI/2),r=Math.floor(20*o)/20;return i*(1-r)}})(),this.scoreByDirection=(()=>{const e=new w.Vector3,t=new w.Vector3;return(i,s,o)=>r=>{if(Math.abs(s.dot(P.fU.UP))>.75)return 0;t.copy(s).normalize(),e.copy(i).sub(r.position).normalize();return-1*e.dot(t)*o}})(),this.scoreByFloor=(e,t)=>i=>i.floorId===e?t:0}async focusPin(e,t,i,s,o){const{anchorPosition:r,stemNormal:n,stemLength:a}=e,h=r.clone().add(n.clone().setLength(a)),l=[this.scoreByDirection(h,n,fe),this.scoreByFloor(e.floorId,32)];return this.focusPoint(h,t,i,s,o,l)}focus(e,t){var i;let s;if(K(e))s=e.getCenter(new w.Vector3);else if(X(e)){const t=e.getCenter(new w.Vector2);s=new w.Vector3(t.x,0,t.y)}else s=e;return this.focusPoint(s,null!==(i=null==t?void 0:t.transition)&&void 0!==i?i:a.n.Interpolate,void 0,void 0,void 0,(null==t?void 0:t.from)?[oe.Dv(null==t?void 0:t.from,100)]:[],[se.no(s,.25)])}async focusPoint(e,t,i,s,o,r,n){if(this.cameraData.canTransition()&&this.sweepData.canTransition()&&!await this.tryNavigateToPoint(e,t,i,s,o,r,n)){if(t!==a.n.Interpolate)return this.goToNearestSweep(e,t,i,s);try{await this.issueCommand(new G._i(G.BD.DOLLHOUSE,a.n.Interpolate))}catch(o){await this.goToNearestSweep(e,t,i,s)}await this.tryNavigateToPoint(e,t,i,s,o,r,n)||await this.goToNearestSweep(e,t,i,s)}}async goToNearestSweep(e,t,i,s){const o=e,r=this.sweepData.getClosestSweep(o,!0);if(!r)throw new Error("Cannot find sweep closest to Mattertag disc");const n=this.getSweepToPointRotation(r.position,o,s),h=this.cameraData.pose.projection.asThreeMatrix4(),{width:l,height:c}=this.cameraData,u=(0,ae.bD)(o,r.position,n,l,c,h);i&&i(u),await this.issueCommand(new d.ju({sweep:r.id,rotation:n,transition:t||a.n.Interpolate}))}}var we=i(26256),ve=i(75287);class be extends ve.T{constructor(e=1,t=0){super(),this._slowdown=!0,this._initialSpeed=0,this._speed=0,this._acceleration=1,this._desiredAcceleration=1,this._jerk=1,this._maxSpeed=1,this._minSpeed=.05,this._startValue=0,this._value=0,this._endValue=e,this._delay=t}get active(){return this._active}get currentValue(){return this._value}get endValue(){return this._endValue}get speed(){return this._speed}get isSlowingDown(){return this._isSlowingDown}get maxSpeed(){return this._maxSpeed}get acceleration(){return this._acceleration}getProgressPercent(){return(this._value-this._startValue)/(this._endValue-this._startValue)}onComplete(e){return this._onComplete=e,this}setStartValue(e){return this._startValue=e,this}setCurrentValue(e){return this._value=e,this}setEndValue(e){return this._endValue=e,this}setInitialSpeed(e){return this._initialSpeed=e,this}setMaxSpeed(e){return this._maxSpeed=e,this}setDesiredAcceleration(e){return this._desiredAcceleration=e,this._jerk=3*Math.abs(this._desiredAcceleration-this._acceleration),this}activate(e){return this._active=e,this}setAccel(e){return this._acceleration=Math.abs(e),this}start(){this._active=!0,this._value=this._startValue,this._speed=this._initialSpeed}setSlowdown(e){return this._slowdown=e,this}tick(e){if(!this._active||e<=0)return;if(this._delay>0)return void(this._delay-=e);e>33&&(e=33);const t=.001*e,i=this._value,s=this._speed*t,o=(0,ne.r_)(i,this.endValue,s),r=this._endValue-i<=this.getSlowdownDistance(),n=this._slowdown&&r,a=n?this._minSpeed:this._maxSpeed,h=this.acceleration*t;this._speed=(0,ne.r_)(this._speed,a,h),this._isSlowingDown=n;const l=this._desiredAcceleration,d=this._jerk*t;return this._acceleration=(0,ne.r_)(this._acceleration,l,d),this._value=o,o===this.endValue?(this.activate(!1),this.commit(),void(this._onComplete&&this._onComplete())):void 0}getSlowdownDistance(){const e=this.speed,t=this._acceleration,i=e/(0===t?1e-5:t);return this.speed*i+.5*t*i*i}stop(e){this._active=!1,this._endValue=e,this.commit()}}var Me=i(46950),Te=i(14496),De=i(27641);const xe=new R.Z("walk");class Se{constructor(e,t,i,s,o,r,n){this.cameraPose=e,this.sweepTransition=t,this.sweepControl=i,this.cameraControl=s,this.generators=o,this.navigation=r,this.active=!1,this.path=[],this.lastQueueTime=0,this.repeatedQueueDelayMS=150,this.positionTracker=(new be).setAccel(15).setMaxSpeed(5),this.baseTransitionTime=h.ZP.camera.baseTransitionTime,this.baseTransitionSpeed=h.ZP.camera.transitionSpeed,n.onPropertyChanged(Te.baseTransitionSpeedKey,(e=>{this.baseTransitionTime=e}))}get isActive(){return this.active}get targetSweep(){return this.nextSweep}setContinuousMovementDirection(e){this.continuousMovementDirection=e,!this.active&&e&&this.navigation.navigateInLocalDirection(e)}stop(){this.path.length=0,this.cameraControl.endExternalTransition(),this.nextSweep=void 0,this.active=!1,this.generator&&(this.generators.stopGenerator(this.generator),this.generator=null)}appendNode(e,t){if(!this.canQueueSweeps(e))return Promise.resolve();if(!e)return Promise.resolve();if(this.path.push(e),this.lastQueueTime=Date.now(),!this.active){this.active=!0;const{generator:t,deferred:i}=this.createTransition();this.generators.startGenerator(t),this.generator=t,this.activePromise=i.nativePromise().then((()=>{this.activePromise=null})),this.positionTracker.setEndValue(this.getDistanceToSweep(e)),this.checkForSpeedIncrease(e)}return this.activePromise?this.activePromise:Promise.resolve()}canQueueSweeps(e){if(e&&this.path.indexOf(e)>=0)return!1;return!(this.path.length>=2)&&!(this.active&&Date.now()-this.lastQueueTime<this.repeatedQueueDelayMS)}attemptContinuousNavigation(){try{this.continuousMovementDirection&&this.navigation.navigateInLocalDirection(this.continuousMovementDirection)}catch(e){if(!(e instanceof u||e instanceof De.f))throw xe.warn(e),e;xe.debug(e)}}createTransition(){const e=this,t=new Me.Q;return{generator:function*(){let i=Date.now();e.cameraControl.beginExternalTransition();const s=(new w.Vector3).copy(e.cameraPose.position),o=new w.Vector3,r=new w.Vector3,n=e.positionTracker;for(n.setMaxSpeed(5).setAccel(15);e.path.length>0;){const t=e.path.shift();if(!t){e.nextSweep=void 0;break}e.nextSweep=t,s.copy(e.cameraPose.position);const a=s.distanceTo(t.position);for(n.setStartValue(0).setEndValue(a).setInitialSpeed(n.speed).activate(!0),r.subVectors(t.position,s),r.normalize(),yield new we.M8(e.sweepControl.activateSweepUnsafe({sweepId:t.id}).then((()=>{e.sweepControl.beginSweepTransition({sweepId:t.id,internalProgress:!1})}))),n.start(),e.checkForSpeedIncrease(t);n.active;){const a=Date.now()-i,h=0===e.path.length;let l=!1,d=!1;if(!h){const i=e.path[0];l=n.endValue-n.currentValue+t.position.distanceTo(i.position)<n.getSlowdownDistance();const s=o.subVectors(i.position,t.position);s.normalize();r.dot(s)<.3&&(d=!0)}n.setSlowdown(h||l||d),n.tick(a);const c=n.getProgressPercent();e.cameraControl.updateCameraPosition(o.lerpVectors(s,t.position,c)),e.sweepTransition.progress.modifyAnimation(c,1,0),i=Date.now(),e.continuousMovementDirection&&e.canQueueSweeps()&&n.isSlowingDown&&e.attemptContinuousNavigation(),n.active&&(yield new we.Jj)}e.sweepControl.endSweepTransition({sweepId:t.id})}t.resolve(),e.stop()},deferred:t}}getDistanceToSweep(e){return(this.nextSweep?this.nextSweep.position:this.cameraPose.position).distanceTo(e.position)}checkForSpeedIncrease(e){const t=this.positionTracker,i=this.getDistanceToSweep(e),s=t.endValue-t.currentValue,o=s+i-t.getSlowdownDistance();if(s+i>12){const e=this.baseTransitionTime,i=this.baseTransitionSpeed,s=.001*(e+Math.log2(1+o)*Te.TRANSITION_DISTANCE_MULTIPLIER*i)-t.maxSpeed/t.acceleration,r=o/s;s>0&&t.setMaxSpeed(r).setDesiredAcceleration(3*r)}else t.setMaxSpeed(5).setDesiredAcceleration(15)}}var Ce=i(59625);class Pe{constructor(e,t,i,s,o,r,n){this.engine=e,this.navigation=t,this.settingsData=i,this.cameraData=s,this.cameraModule=o,this.viewmodeData=r,this.toolsData=n,this.defaultSize=(new w.Vector3).fromArray([3,3,3])}async enforceLabelEditorFriendlyViewmode(){var e;let t=this.viewmodeData.isDollhouse();this.toolsData.activeToolName!==S.w1.LABELS&&t&&(t=this.settingsData.tryGetProperty(Ce.gx.LabelsDollhouse,!0));let i=!0;if(await(null===(e=this.cameraData.transition.promise)||void 0===e?void 0:e.nativePromise()),!this.viewmodeData.isFloorplan()&&!t)try{await this.engine.commandBinder.issueCommand(new G._i(G.BD.FLOORPLAN,a.n.Interpolate))}catch(e){i=!1}return i}async navigateToLabel(e){try{const t=await this.enforceLabelEditorFriendlyViewmode(),i=this.settingsData.tryGetProperty(h.eC,!1)&&(0,ae.Eb)(this.cameraData.pose.pitchFactor())?j.Ey.Floorplan:this.viewmodeData.currentMode||j.Ey.Floorplan;return await this.navigation.focus((new w.Box3).setFromCenterAndSize(new w.Vector3(0,1,0).add(e.position),this.defaultSize),{floorId:e.floorId,mode:i}),t}catch(e){return!1}}}var Oe=i(2032),ke=i(90365),Be=i(18596);var Ae=i(6282);class Fe extends s.Y{constructor(){super(...arguments),this.name="navigation",this.navigationRules=[()=>!0],this.navigationEnabled=!0,this.inputOutside=[],this.addNavigationRule=e=>{-1===this.navigationRules.indexOf(e)&&this.navigationRules.push(e)},this.removeNavigationRule=e=>{const t=this.navigationRules.indexOf(e);-1!==t&&this.navigationRules.splice(t,1)},this.isNavigationInputAllowed=()=>{const e=this.navigationRules.reduce(((e,t)=>e&&t()),!0);return!(!this.navigationEnabled||!e)||(this.log.debug("Cannot move while navigation is locked",{blockedByRules:!e,blockedByCommand:!this.navigationEnabled}),!1)}}async init(e,t){const{market:i}=t;this.commandBinder=t.commandBinder,this.bindings.push(this.commandBinder.addBinding(d.ZK,(async()=>this.lockNavigation())),this.commandBinder.addBinding(d.Lp,(async()=>this.unlockNavigation())),this.commandBinder.addBinding(d.zs,(async e=>{const{focusPosition:t,transition:i,orientationAdjust:s,onSweepChosen:o,neighborDistanceThreshold:r}=e;return this.navigationPoint.focusPoint(t,i,o,s,r)})),this.commandBinder.addBinding(d.OR,(async e=>{const{pinPosition:t,transition:i,orientationAdjust:s,onSweepChosen:o,neighborDistanceThreshold:r}=e;return this.navigationPoint.focusPin(t,i,o,s,r)})),this.commandBinder.addBinding(d.ju,(e=>this.navigateToSweep(e.sweep,e.rotation,e.transition,e.transitionTime,e.transitionSpeedMultiplier))),this.commandBinder.addBinding(d.Cs,(async({position:e,floorId:t,viewmodeOnly:i})=>i?this.navigationLabel.enforceLabelEditorFriendlyViewmode():!(!e||!t)&&this.navigationLabel.navigateToLabel({position:e,floorId:t}))),this.commandBinder.addBinding(d.SG,(async({room:e})=>{var t,i;const s=this.floorsViewData.getFloor(null!==(t=e.floorId)&&void 0!==t?t:this.floorsViewData.topFloorId).medianSweepHeight(),o=new w.Box3(new w.Vector3(e.bbox.min.x,s,e.bbox.min.y),new w.Vector3(e.bbox.max.x,s,e.bbox.max.y)),r=(0,ae.Eb)(this.cameraData.pose.pitchFactor())||this.viewmodeData.isInside()?j.Ey.Floorplan:null!==(i=this.viewmodeData.currentMode)&&void 0!==i?i:void 0;return this.focus(o,{mode:r,floorId:e.floorId})})),this.commandBinder.addBinding(d.L4,(async e=>this.navigateToPose(e.pose)))),[this.settingsData,this.sweepData,this.sweepModule,this.viewmodeData,this.viewmodeModule,this.cameraData,this.cameraModule,this.interactionmodeData,this.meshQuery]=await Promise.all([i.waitForData(o.e),i.waitForData(z.Z),t.getModuleBySymbol(r.l),i.waitForData(H.O),t.getModuleBySymbol(r.XT),i.waitForData(n.M),t.getModuleBySymbol(r.kg),i.waitForData(l.Z),t.getModuleBySymbol(r.hi)]);const[s,a,c]=await Promise.all([t.getModuleBySymbol(r.fQ),i.waitForData(W.t),i.waitForData(Z.c)]);this.floorsViewData=c,this.navigationPoint=new ye(this.cameraData,this.sweepData,this.viewmodeModule,null==s?void 0:s.picking,t.commandBinder.issueCommand),this.navigationLabel=new Pe(t,this,this.settingsData,this.cameraData,this.cameraModule,this.viewmodeData,a);const u=await t.getModuleBySymbol(r.PZ),m=()=>this.isNavigationInputAllowed()&&this.viewmodeData.canStartTransition()&&this.sweepData.canTransition(),p=()=>m()&&(this.viewmodeData.isDollhouse()||this.viewmodeData.isFloorplan()||this.viewmodeData.isOrthographic());this.navigationOutside=new ie(p,this.cameraData.pose,this.cameraData,this.cameraModule,this.viewmodeData,t.commandBinder.issueCommand),this.inputOutside.push(new C(p,t.commandBinder,u,c,this,this.sweepData,this.cameraModule,a,this.meshQuery,this.settingsData.tryGetProperty(h.eC,!1))),this.inputInside=new V(this,u,!!e.enableWheel),this.inputOutside.forEach((e=>this.bindings.push(...e.bindings))),this.bindings.push(...this.inputInside.bindings),this.updateInputBindings(),this.bindings.push(this.viewmodeData.makeModeChangeSubscription((()=>{this.viewmodeData.currentMode!==j.Ey.Transition&&this.updateInputBindings()}))),this.navigationWalk=new Se(this.cameraData.pose,this.sweepData.transition,this.sweepModule,this.cameraModule,t,this,this.settingsData),t.broadcast(new Oe.em(ke.Y.Navigation))}updateInputBindings(){const e=this.interactionmodeData.isVR(),t=this.viewmodeData.isInside();this.inputInside.toggleInput(t&&!e),this.inputInside.toggleVrInput(t&&e),this.inputOutside.forEach((e=>e.toggleInput(!t)))}navigateInLocalDirection(e){const t=this.cameraData.pose.rotation;return this.navigateInDirection(e.clone().applyQuaternion(t))}setContinuousNavigationLocalDirection(e){this.navigationWalk.setContinuousMovementDirection(e)}navigateTowardsIntersection(e){try{this.navigateInDirection(e.point.clone().sub(this.cameraData.pose.position))}catch(e){return!1}return!0}navigateToPanoNearIntersection(e){const t=(0,M.bG)(this.sweepData,this.viewmodeData.isInside(),e,this.meshQuery),i=t.length>0?t[0].sweep:this.sweepData.getClosestSweep(e.point,!0),s=h.y4[this.interactionmodeData.mode];if(this.viewmodeData.isInside()&&i)return this.navigateToSweep(i.id,void 0,s),!0;if(this.viewmodeData.canSwitchViewMode(j.Ey.Panorama)){const e=i?i.id:void 0;return this.commandBinder.issueCommand(new G._i(G.BD.INSIDE,s,{sweepID:e})),!0}return!1}navigateInDirection(e){if(!this.viewmodeData.isInside())throw new u(m.InsideOnly);if(!this.sweepData.currentSweep)throw new u(m.InvalidSweep);const t=h.y4[this.interactionmodeData.mode],i=(0,M.Tq)(this.sweepData,e,this.navigationWalk.isActive?.65:void 0,this.navigationWalk.isActive?this.navigationWalk.targetSweep:void 0);if(i.length>0&&i[0].sweep)return this.navigateToSweep(i[0].sweep.id,void 0,t);throw new u(m.NoDestinationFound)}async focus(e,t){var i;if(!(t=Object.assign({mode:this.viewmodeData.currentMode,transition:a.n.Interpolate},t)).mode||!this.isNavigationInputAllowed())throw new u(m.Locked);let s;if(await(0,Ae.E)(this.cameraData,this.viewmodeData),this.cameraData.pose.autoOrtho&&t.mode===j.Ey.Floorplan&&(t.forceOrtho=!0),(t.floorId&&t.floorId!==this.floorsViewData.currentFloorId||null===this.floorsViewData.currentFloorId||this.viewmodeData.isInside())&&(this.viewmodeData.isInside()&&this.floorsViewData.transitionToFloorInstant(null),this.commandBinder.issueCommand(new v.Vw(t.floorId||this.floorsViewData.getHighestVisibleFloorId(),!0,ee.E.TRANSITION_TIME_DH/2))),this.viewmodeData.isInside()){let o;if(K(e))o=e;else if(X(e)){const s=this.floorsViewData.floors.getFloor(null!==(i=t.floorId)&&void 0!==i?i:this.floorsViewData.getHighestVisibleFloorId()).medianSweepFloorHeight();o=new w.Box3(new w.Vector3(e.min.x,s,e.min.y),new w.Vector3(e.max.x,s,e.max.y))}s=function(e,t){t||(t=(e.currentFloor||e.getFloor(e.bottomFloorId)).boundingBox);const i=t.min.y,s=t.getCenter(new w.Vector3),o=t.max.clone();o.y=i;const r=s.distanceTo(o)/Math.tan(Be.oR.fov/2*re.Ue),n=P.Hk.DOWNWARD.clone().multiply((new w.Quaternion).setFromAxisAngle(P.fU.RIGHT,45*re.Ue)),a=P.fU.FORWARD.clone().applyQuaternion(n).multiplyScalar(-1*r);return{position:s.clone().add(a),rotation:n}}(this.floorsViewData,o)}try{switch(t.mode){case j.Ey.Panorama:await this.navigationPoint.focus(e,t);break;case j.Ey.Dollhouse:await this.viewmodeModule.switchToMode(t.mode,t.transition,s),await this.navigationOutside.focus(e,t);break;case j.Ey.Floorplan:await this.viewmodeModule.switchToMode(t.mode,t.transition),await this.navigationOutside.focus(e,t);break;default:throw this.log.warn(`navigation.focus: ${t.mode} not implemented yet`),new u(m.InvalidMode)}}catch(i){throw this.log.debug("Unable to set focus",e,t,i),i}}lockNavigation(){this.navigationEnabled=!1,this.log.debug("Navigation input locked")}unlockNavigation(){this.navigationEnabled=!0,this.log.debug("Navigation input unlocked")}async navigateToSweep(e,t,i,s,o){const r=this.settingsData.tryGetProperty(Q.gj,a.n.Interpolate);let n;void 0===i&&(i=r);const h=this.sweepData.currentSweep;if(i===a.n.Interpolate){const t=!h||this.sweepData.isSweepAligned(h),s=this.sweepData.isSweepAligned(e),o=h!==e,r=!t||!s,n=this.viewmodeData.isInside();o&&r&&n&&(i=a.n.FadeToBlack)}if(this.viewmodeData.isInside()){const r=void 0===t&&void 0===s&&void 0===o,h=this.cameraData.canTransition()&&this.sweepData.canTransition();if(i===a.n.Interpolate&&this.sweepData.currentSweep!==e&&r&&(h||this.navigationWalk.isActive)){const t=this.sweepData.getSweep(e);n=this.navigationWalk.appendNode(t,this.sweepData.currentSweep)}else{if(!h)return this.sweepData.currentSweep;n=this.sweepModule.moveToSweep({transitionType:i,sweepId:e,rotation:t,transitionTime:s,transitionSpeedMultiplier:o})}}else n=this.viewmodeModule.switchToMode(j.Ey.Panorama,i,{sweepID:e,rotation:t});return await n,this.sweepData.currentSweep}navigateToPose(e){const t={2:G.BD.DOLLHOUSE,3:G.BD.FLOORPLAN},{sweepIndex:i,quaternion:s,mode:o}=e;let{panoId:r,position:n}=e;if(!r&&void 0!==i){const e=this.sweepData.getSweepByIndex(i);e&&(r=e.id)}if(r)this.commandBinder.issueCommand(new d.ju({sweep:r,rotation:s,transition:a.n.FadeToBlack}));else{if(!(o in t))throw new Error("Unknown navigation link pose: "+JSON.stringify(e));this.commandBinder.issueCommand(new G._i(t[o],a.n.Interpolate,{rotation:s,position:n}))}}}},44649:(e,t,i)=>{"use strict";i.d(t,{B:()=>c});var s=i(81396),o=i(31546),r=i(41602),n=i(21646),a=i(2569),h=i(73121),l=i(41835),d=i(46950);class c extends l.Z{constructor(e,t,i,n,a=!1){super(),this.cameraPoseProxy=e,this.getDefaultZoom=t,this.targetProjection=new o.M,this.currentOrientation=new s.Quaternion,this.currentPosition=new s.Vector3,this.positionDelta=new s.Vector3,this.linearAccel=new s.Vector2,this.linearVelocity=new s.Vector2,this.zoomAccel=0,this.zoomVelocity=0,this.scale=1,this.maxZoom=0,this.minZoom=r.Tm,this.checkBounds=a,this.bounds=i.clone(),this.boundsCenter=n.clone(),this.setupBounds(),this.transition={active:!1,startTime:0,elapsed:0,duration:0,linearVelocity:new s.Vector2,zoomVelocity:0,easeOut:!1}}start(){this.scale=1,this.maxZoom=this.getDefaultZoom()*r.Ct}setPanAcceleration(e,t=!1,i){if(!this.transition.active){t&&this.haltVelocity(e,this.linearVelocity);const s=this.cameraPoseProxy.pose,o=s.projection.elements[0],r=s.projection.elements[5];this.linearAccel.x=void 0!==e.x?e.x/o:this.linearAccel.x,this.linearAccel.y=void 0!==e.y?e.y/r:this.linearAccel.y,void 0!==i&&this.linearAccel.setLength(i)}}setZoomAcceleration(e){this.transition.active||(this.zoomAccel=e)}haltVelocity(e,t){e.x&&t.x&&Math.sign(e.x)!==Math.sign(t.x)&&(t.x=0),e.y&&t.y&&Math.sign(e.y)!==Math.sign(t.y)&&(t.y=0)}startRotateTransition(e,t,i){return Promise.resolve()}startTranslateTransition(e,t,i=!0){return this.startTransition(e,0,t.clone().multiplyScalar(r.SI),0,i).nativePromise()}startZoomTransition(e,t,i){return this.startTransition(e,0,new s.Vector2(0,0),t,i).nativePromise()}startTransition(e,t,i,s,o){const r=new d.Q;return this.poseController?(this.transition.active=!0,this.transition.duration=e,this.transition.elapsed=0,this.transition.startTime=Date.now(),this.transition.deferred=r,this.transition.linearVelocity.copy(i),this.transition.zoomVelocity=s,this.transition.easeOut=o,this.linearAccel.set(0,0),this.zoomAccel=0,this.linearVelocity.copy(i),this.zoomVelocity=s,this.poseController.beginExternalTransition(),r.promise()):r.resolve().promise()}stopTransition(){var e;this.transition.active&&(null===(e=this.poseController)||void 0===e||e.endExternalTransition(),this.transition.active=!1),this.transition.deferred&&(this.transition.deferred.resolve(),this.transition.deferred=void 0)}updateTransition(e,t,i){this.transition.elapsed+=e;const s=this.getTransitionScale(e);t&&(this.linearVelocity.copy(this.transition.linearVelocity).multiplyScalar(s),this.pan(this.linearVelocity)),i&&(this.zoomVelocity=this.transition.zoomVelocity*s,this.zoom(this.zoomVelocity)),this.transition.elapsed>=this.transition.duration&&(this.stop(this.transition.easeOut),this.transition.active=!1)}getTransitionScale(e){if(this.transition.elapsed>=this.transition.duration){return(this.transition.duration-(this.transition.elapsed-e))/e}return e/r.SI}updateDefault(e,t,i){const s=e/r.SI;t&&(this.linearVelocity.addScaledVector(this.linearAccel,s),this.pan(this.linearVelocity),this.linearVelocity.multiplyScalar(Math.pow(1-r.O8,s))),i&&(this.zoomVelocity=this.zoomVelocity+this.zoomAccel*s,this.zoom(this.zoomVelocity),this.zoomVelocity*=Math.pow(1-r.TD,s))}update(e){const t=this.linearAccel.length()>n.Z.epsilon||this.linearVelocity.length()>n.Z.epsilon,i=Math.abs(this.zoomAccel)>n.Z.epsilon||Math.abs(this.zoomVelocity)>n.Z.epsilon;this.transition.active?this.updateTransition(e,t,i):this.updateDefault(e,t,i)}stopMomentum(){this.transition.active||(this.linearVelocity.set(0,0),this.zoomVelocity=0)}stopAcceleration(){this.transition.active||(this.setPanAcceleration({x:0,y:0}),this.setZoomAcceleration(0))}stop(e=!1){this.stopTransition(),this.stopAcceleration(),e||this.stopMomentum()}pan(e){if(!this.poseController)return;const t=this.cameraPoseProxy.pose;this.positionDelta.x=e.x,this.positionDelta.y=e.y,this.positionDelta.z=0,this.positionDelta.applyQuaternion(t.rotation),this.currentPosition.copy(t.position).add(this.positionDelta),this.checkBounds&&!this.insideBounds(this.currentPosition,t.rotation,t.projection)||this.poseController.updateCameraPosition(this.currentPosition)}zoom(e){if(!this.poseController)return;const t=this.cameraPoseProxy.pose;this.targetProjection.copy(t.projection);const i=this.scale*(1-e);if(Math.abs(i-this.scale)>n.Z.epsilon){this.targetProjection.elements[0]*=i/this.scale,this.targetProjection.elements[5]*=i/this.scale;const s=(0,h.S3)(this.targetProjection);if(e<0&&s<=this.minZoom||e>0&&s>=this.maxZoom)return;if(this.checkBounds&&!this.insideBounds(t.position,t.rotation,this.targetProjection))return;this.scale=i,this.poseController.updateCameraProjection(this.targetProjection.clone())}}setupBounds(){this.bounds.min.x=this.adjustBound(this.bounds.min.x,2),this.bounds.min.y=this.adjustBound(this.bounds.min.y,2),this.bounds.min.z=this.adjustBound(this.bounds.min.z,2),this.bounds.max.x=this.adjustBound(this.bounds.max.x,-2),this.bounds.max.y=this.adjustBound(this.bounds.max.y,-2),this.bounds.max.z=this.adjustBound(this.bounds.max.z,-2),this.worldBounds=(new s.Box3).setFromCenterAndSize(this.boundsCenter,new s.Vector3(this.bounds.max.x-this.bounds.min.x,this.bounds.max.y-this.bounds.min.y,this.bounds.max.z-this.bounds.min.z))}insideBounds(e,t,i){return!!(0,a.cb)(e,t,i,this.worldBounds)}adjustBound(e,t){return Math.sign(e+t)===Math.sign(e)?e+t:0}}},66445:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>T});var s,o=i(81396),r=i(44649),n=i(41602),a=i(16782),h=i(32597),l=i(6667),d=i(95882),c=i(10699),u=i(12250),m=i(80592),p=i(11442),g=i(89553),f=i(76532),y=i(2569),w=i(92810),v=i(9037),b=i(66211),M=i(34029);!function(e){e[e.NONE=a.r.NONE]="NONE",e[e.PAN=a.r.PRIMARY]="PAN",e[e.ROTATE=a.r.SECONDARY]="ROTATE",e[e.ZOOM=a.r.MIDDLE]="ZOOM"}(s||(s={}));class T extends b.Z{constructor(){super(...arguments),this.name="orthographic-controls",this.controlState=s.NONE,this.controlsEngaged=!1,this.movementKeys=new o.Vector2,this.resetControlState=()=>{this.controlState=s.NONE}}async init(e,t){const[i,s,o,r,a]=await Promise.all([t.market.waitForData(f._),t.getModuleBySymbol(w.kg),t.getModuleBySymbol(w.Ng),t.market.waitForData(v.M),t.market.waitForData(M.e)]);this.meshData=i,this.cameraModule=s,this.modelSize=Math.max(i.extendedSize.length(),1),this.commonControlsModule=o,this.createCameraControls(i,r,a),this.registerActiveStateChangeBinding(),t.getModuleBySymbol(w.PZ).then((e=>{this.inputBindings.push(e.registerHandler(u.a,(e=>{this.onScrollWheel(e)}))),this.inputBindings.push(e.registerHandler(m.E0,(e=>{this.onDragBegin(e.buttons)}))),this.inputBindings.push(e.registerHandler(m._t,(e=>{this.controlsEngaged=!0,this.onDrag(e.delta),this.controls.update(n.SI),this.controls.stop()}))),this.inputBindings.push(e.registerHandler(m._R,(e=>{this.controlsEngaged&&(e.timeSinceLastMove<100&&(this.onDrag(e.delta),this.controls.update(n.SI),this.controls.stopAcceleration()),this.onDragEnd(e.delta,e.buttons),this.controlsEngaged=!1)}))),this.inputBindings.push(e.registerHandler(p.G,(e=>{this.controls.setZoomAcceleration(-e.pinchDelta*n.N4),this.controls.update(n.SI),this.controls.stop()}))),this.inputBindings.push(e.registerHandler(p.i,this.resetControlState)),this.inputBindings.push(e.registerHandler(g.e,(e=>{e.state!==l.M.PRESSED&&this.onKey(e)}))),this.updateInputBindings()})),this.bindings.push(t.subscribe(c.Z,(e=>{this.controls.isActive&&this.controls.start()})))}onUpdate(e){this.controls.isActive&&this.controls.update(e)}createCameraControls(e,t,i){const s=this.commonControlsModule.cameraPoseProxy,o=t.defaultZoom.bind(t);this.controls=new r.B(s,o,e.extendedBounds,e.meshCenter,!0),this.commonControlsModule.addControls(d.Ey.Orthographic,this.controls)}onActiveStateChanged(){super.onActiveStateChanged(),this.resetControlState()}onScrollWheel(e){if(0!==e.delta.y){const t=(0,y.et)(this.modelSize,1,1500,2,.125);this.controls.setZoomAcceleration(e.delta.y*n.jX/(t*n.mP)),this.controls.update(n.SI),this.controls.setZoomAcceleration(0)}}onDragBegin(e){if(this.controlState===s.NONE){const t=e;this.controlState=t}this.controls.stop()}onDrag(e){switch(this.controlState){case s.PAN:const t=e;this.controls.setPanAcceleration({x:-t.x,y:-t.y});break;case s.ZOOM:0!==e.y&&this.controls.setZoomAcceleration(-e.y)}}onDragEnd(e,t){t&this.controlState||(this.controlState=s.NONE)}onKey(e){const{key:t,state:i}=e,s=i===l.M.DOWN;let o=!1;switch(t){case h.R.A:this.movementKeys.x=s?-1:0,o=!0;break;case h.R.D:this.movementKeys.x=s?1:0,o=!0;break;case h.R.W:this.movementKeys.y=s?1:0,o=!0;break;case h.R.S:this.movementKeys.y=s?-1:0,o=!0;break;case h.R.K:this.controls.setZoomAcceleration(s?n.Gu:0);break;case h.R.I:this.controls.setZoomAcceleration(s?-n.Gu:0)}if(o){const e=this.movementKeys;this.controls.setPanAcceleration({x:e.x,y:e.y},!1,n.bC)}}}},93642:(e,t,i)=>{"use strict";i.d(t,{O8:()=>a,SI:()=>o,WI:()=>h,uQ:()=>r,zf:()=>n});var s=i(69505);const o=1e3/60,r=(0,s.Id)(70),n=-r,a=.05,h=.1/60},49219:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>D,lookAccelerationKey:()=>T});var s=i(81396),o=i(90304),r=i(93642),n=i(21646),a=i(49827),h=i(41835),l=i(46950);class d extends h.Z{constructor(e){super(),this.cameraPoseProxy=e,this.lookVelocity=new s.Vector2,this.lookAccel=new s.Vector2,this.tempAxis=new s.Vector3,this.tempOrientation=new s.Quaternion,this.currentOrientation=new s.Quaternion,this.tempEuler=new s.Euler,this.transition={active:!1,startTime:0,elapsed:0,duration:0,velocity:new s.Vector2,easeOut:!1}}setLookAcceleration(e,t=!1){this.transition.active||(t&&(e.x&&this.lookVelocity.x&&Math.sign(e.x)!==Math.sign(this.lookVelocity.x)&&(this.lookVelocity.x=0),e.y&&this.lookVelocity.y&&Math.sign(e.y)!==Math.sign(this.lookVelocity.y)&&(this.lookVelocity.y=0)),this.lookAccel.x=void 0!==e.x?e.x:this.lookAccel.x,this.lookAccel.y=void 0!==e.y?e.y:this.lookAccel.y)}startTransition(e,t,i){var s;const o=new l.Q;return this.transition.active=!0,this.transition.duration=e,this.transition.elapsed=0,this.transition.startTime=Date.now(),this.transition.deferred=o,this.transition.velocity.copy(t),this.transition.easeOut=i,this.lookAccel.set(0,0),this.lookVelocity.copy(t),null===(s=this.poseController)||void 0===s||s.beginExternalTransition(),o.promise()}stopTransition(){var e;this.transition.active&&(null===(e=this.poseController)||void 0===e||e.endExternalTransition(),this.transition.active=!1),this.transition.deferred&&(this.transition.deferred.resolve(),this.transition.deferred=void 0)}updateTransition(e){const t=e/r.SI;if(this.lookVelocity.copy(this.transition.velocity),this.transition.elapsed+=e,this.transition.elapsed>=this.transition.duration){const t=this.transition.duration-(this.transition.elapsed-e);this.lookVelocity.multiplyScalar(t/e)}else this.lookVelocity.multiplyScalar(t)}updateCameraParameters(){var e;const t=this.cameraPoseProxy.pose;this.tempEuler.setFromQuaternion(t.rotation,"YXZ");const i=this.tempEuler.x,s=(0,a.uZ)(this.lookVelocity.y,r.zf-i,r.uQ-i);this.tempAxis.copy(o.fU.RIGHT),this.tempOrientation.setFromAxisAngle(this.tempAxis.applyQuaternion(t.rotation),s),this.currentOrientation.copy(t.rotation).premultiply(this.tempOrientation),this.tempOrientation.setFromAxisAngle(o.fU.UP,this.lookVelocity.x),this.currentOrientation.premultiply(this.tempOrientation),t.rotation.equals(this.currentOrientation)||(this.tempOrientation.copy(this.currentOrientation).normalize(),null===(e=this.poseController)||void 0===e||e.updateCameraRotation(this.tempOrientation))}update(e){const t=this.cameraPoseProxy.pose,i=e/r.SI;t.rotation.equals(this.currentOrientation)||this.currentOrientation.copy(t.rotation),this.transition.active?(this.updateTransition(e),this.updateCameraParameters(),this.transition.elapsed>=this.transition.duration&&(this.stop(this.transition.easeOut),this.transition.active=!1)):(this.lookAccel.length()>n.Z.epsilon||this.lookVelocity.length()>n.Z.epsilon)&&(this.lookVelocity.addScaledVector(this.lookAccel,i),this.updateCameraParameters(),this.lookVelocity.multiplyScalar(Math.pow(1-r.O8,i)))}stop(e=!1){this.stopTransition(),this.lookAccel.set(0,0),e||this.lookVelocity.set(0,0)}startRotateTransition(e,t,i){return this.beforeStartRotationTransition&&this.beforeStartRotationTransition(),this.startTransition(e,t.clone().multiplyScalar(r.SI),i).nativePromise()}startTranslateTransition(e,t,i=!0){throw new Error("Panning isn't supported in Panorama Controls")}startZoomTransition(e,t,i){throw new Error("Zooming isn't supported in Panorama Controls")}}var c=i(5135),u=i(95882),m=i(16782),p=i(32597),g=i(6667),f=i(80592),y=i(89553),w=i(34029),v=i(92810),b=i(9037),M=i(66211);const T="Rotation speed";class D extends M.Z{constructor(){super(...arguments),this.name="panorama-controls",this.controlsEngaged=!1,this.lookAccelerationSpeed=r.WI,this.calcRotationAngle=(()=>{const e=new s.Matrix4,t=new s.Vector3,i=new s.Vector3;return(o,r)=>{e.copy(this.cameraData.pose.projection.asThreeMatrix4()),e.invert(),t.set(o.x-r.x,o.y-r.y,-1).applyMatrix4(e),i.set(o.x,o.y,-1).applyMatrix4(e);const n=Math.sqrt(t.x*t.x+t.z*t.z),a=Math.sqrt(i.x*i.x+i.z*i.z),h=Math.atan2(t.y,n),l=Math.atan2(i.y,a)-h;t.y=0,i.y=0,t.normalize(),i.normalize();const d=Math.acos(t.dot(i));let c=0;return isNaN(d)||(c=d,r.x>0&&(c*=-1)),new s.Vector2(-c,-l)}})()}async init(e,t){const i=await t.getModuleBySymbol(v.Ng);this.controls=new d(i.cameraPoseProxy),this.cameraData=await t.market.waitForData(b.M);const s=this.cameraData;this.controls.beforeStartRotationTransition=()=>{s.transition&&s.transition.activeInternal&&s.transition.to.rotation&&(s.transition.to.rotation=void 0)},i.addControls(u.Ey.Panorama,this.controls),i.addControls(u.Ey.Mesh,this.controls),this.market=t.market,this.registerActiveStateChangeBinding(),t.getModuleBySymbol(v.PZ).then((e=>{e.registerHandler(f.E0,(e=>{this.shouldBeActive()&&this.controls.stop()})),e.registerHandler(f._t,(e=>{this.shouldBeActive()&&e.buttons&m.r.PRIMARY&&(this.controlsEngaged=!0,this.onDrag(e.position,e.delta),this.controls.update(r.SI),this.controls.stop())})),e.registerHandler(f._R,(e=>{this.shouldBeActive()&&this.controlsEngaged&&(e.timeSinceLastMove<100&&!(e.buttons&m.r.PRIMARY)&&(this.onDrag(e.position,e.delta),this.controls.update(r.SI),this.controls.setLookAcceleration({x:0,y:0})),this.controlsEngaged=!1)})),e.registerHandler(y.e,(e=>{this.shouldBeActive()&&this.onKey(e.key,e.state)})),this.updateInputBindings()}))}onUpdate(e){this.shouldBeActive()&&this.controls.update(e)}onDrag(e,t){this.controls.setLookAcceleration(this.calcRotationAngle(e,t))}onKey(e,t){var i,s;const o=null!==(s=null===(i=this.market.tryGetData(w.e))||void 0===i?void 0:i.tryGetProperty(T,null))&&void 0!==s?s:null;this.lookAccelerationSpeed=o?o*(Math.PI/180)/60:this.lookAccelerationSpeed;const r=t===g.M.DOWN;switch(e){case p.R.LEFTARROW:case p.R.J:this.controls.setLookAcceleration({x:r?this.lookAccelerationSpeed:0},!0);break;case p.R.RIGHTARROW:case p.R.L:this.controls.setLookAcceleration({x:r?-this.lookAccelerationSpeed:0},!0);break;case p.R.K:this.controls.setLookAcceleration({y:r?-this.lookAccelerationSpeed:0},!0);break;case p.R.I:this.controls.setLookAcceleration({y:r?this.lookAccelerationSpeed:0},!0)}}shouldBeActive(){var e,t;return null!==(t=!(null===(e=this.market.tryGetData(c.Z))||void 0===e?void 0:e.isVR()))&&void 0!==t&&t}}},25814:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>C});var s=i(97542),o=i(92810),r=i(17878),n=i(81396),a=i(27115),h=i.n(a),l=i(92393),d=i.n(l),c=i(17965),u=i.n(c),m=i(14282),p=i.n(m),g=i(98419),f=i.n(g),y=i(20367),w=i.n(y);const v={circle_projection:new n.RawShaderMaterial({uniforms:{textureSampleScale:{value:5},panoTexture:{value:null},borderSize:{value:0},borderColor:{value:new n.Vector4(1,1,1,1)}},depthWrite:!1,depthTest:!0,side:n.DoubleSide,vertexShader:h(),fragmentShader:d()}),equirectangular:new n.RawShaderMaterial({uniforms:{cubemap:{value:null},yaw:{value:0}},depthWrite:!1,depthTest:!1,vertexShader:u(),fragmentShader:p()}),compose:new n.RawShaderMaterial({uniforms:{mask:{value:null},bg:{value:null}},transparent:!0,vertexShader:f(),fragmentShader:w()})};var b=i(87928),M=i(41187);class T{constructor(e,t){this._renderer=e,this._renderTarget=t}get target(){return this._renderTarget}get width(){return this._renderTarget.width}get height(){return this._renderTarget.height}get bpp(){return 4}readRenderTargetData(e){const t=this._renderTarget.width,i=this._renderTarget.height;return e=e||new Uint8Array(t*i*4),this._renderer.readRenderTargetPixels(this._renderTarget,0,0,t,i,e),e}setSize(e,t){this._renderTarget.setSize(e,t)}dispose(){this._renderTarget.dispose()}}var D=i(947),x=i(56512);const S=new n.DataTexture(new Uint8Array([255,255,255,255]),1,1);S.needsUpdate=!0;class C extends s.Y{constructor(){super(...arguments),this.name="render-to-texture",this.circleProjectionPlane=new b.E((0,x.fc)(),v.circle_projection),this.equirectProjectionPlane=new b.E((0,x.fc)(),v.equirectangular),this.composePlane=new b.E((0,x.fc)(),v.compose),this.cachedSize=new n.Vector2,this.cachedViewport=new n.Vector4,this.cachedViewport2=new n.Vector4,this.debugRenderOffset=0}async init(e,t){this.engine=t;const i=await t.getModuleBySymbol(o.Aj);this.cwfRenderer=i.cwfRenderer,this.renderer=i.threeRenderer,this.camera=new n.PerspectiveCamera,this.orthoCamera=new n.OrthographicCamera(-.5,.5,.5,-.5,0,1),this.camera.layers.mask=r.o.ALL.mask,this.scene=new n.Scene,this.scene.name="rtt",this.scene.matrixWorldAutoUpdate=!1,this.bindings.push(t.commandBinder.addBinding(M.oM,(async e=>new T(this.renderer,this.createRenderTarget2D(0))))),this.bindings.push(t.commandBinder.addBinding(M.sH,(async e=>{this.renderContext(e.renderTarget.target,e.context)}))),this.bindings.push(t.commandBinder.addBinding(M.vU,(async e=>{this.render(e.renderTarget.target,e.sceneObject,e.camera)}))),this.bindings.push(t.commandBinder.addBinding(M.fZ,(async e=>{this.renderEquirectangular(e.texture,e.renderTarget.target,e.heading)}))),this.bindings.push(t.commandBinder.addBinding(M.sp,(async e=>{this.render(e.renderTarget,e.sceneObject,e.camera)}))),this.bindings.push(t.commandBinder.addBinding(M.ep,(async e=>{this.compose(e.target1.target,e.target2.target,e.target2.target)})))}getRenderSize(){const e=this.renderer.getPixelRatio(),t=this.renderer.getSize(this.cachedSize);return t.width*=e,t.height*=e,t}onUpdate(){this.debugRenderOffset=0}createRenderTarget2D(e,t=e,i,s=!0){const o=new n.WebGLRenderTarget(e,t,i);return o.texture.generateMipmaps=s,o}clearRenderTarget2D(e){this.renderer.setRenderTarget(e),this.renderer.clear(),this.renderer.setRenderTarget(null)}disposeRenderTarget2D(e){e.texture.dispose(),e.depthTexture&&e.depthTexture.dispose(),e.dispose()}getRenderTargetData(e,t){const i=e.width,s=e.height;return t=t||new Uint8Array(i*s*4),this.renderer.readRenderTargetPixels(e,0,0,i,s,t),t}compose(e,t,i=S){const{uniforms:s}=this.composePlane.material;s.bg.value=this.isRenderTarget(t)?t.texture:t,s.mask.value=this.isRenderTarget(i)?i.texture:i,this.scene.add(this.composePlane),this.overrideRenderTarget(e,(()=>{this.renderer.render(this.scene,this.orthoCamera)})),this.scene.remove(this.composePlane),s.bg.value=null,s.mask.value=null}copyTexture(e,t){return this.compose(e,t)}resizeTexture(e,t){const i=new n.WebGLRenderTarget(t,t);return e.isCompressedTexture?i.texture.format=n.RGBAFormat:i.texture.format=e.format,i.texture.addEventListener("dispose",i.dispose.bind(i)),this.copyTexture(i,e),i.texture}renderToScreen(e,t=!0,i,s){(t?Promise.resolve():this.engine.after(D.A.Render)).then((()=>{const t=this.isRenderTarget(e)?e.width:e.image.width,o=this.isRenderTarget(e)?e.height:e.image.height,r=i?i.x:this.debugRenderOffset,n=i?i.y:60;this.renderer.getViewport(this.cachedViewport),this.renderer.setViewport(r,n,t,o),this.compose(null,e,s),this.renderer.setViewport(this.cachedViewport),i||(this.debugRenderOffset+=t+4)}))}isRenderTarget(e){return e.hasOwnProperty("texture")}renderContext(e,t){e.texture.image=t.canvas,e.texture.needsUpdate=!0}render(e,t,i,s){const o=t.parent;if(o&&this.scene.applyMatrix4(o.matrixWorld),this.scene.add(t),e.isWebGLCubeRenderTarget){const t=new n.CubeCamera(.01,1e3,e);i.getWorldPosition(t.position),i.getWorldQuaternion(t.quaternion),this.scene.add(t),this.scene.updateMatrixWorld(),t.layers.mask=s?s.mask:i.layers.mask,t.update(this.renderer,this.scene),this.scene.remove(t)}else{i.getWorldPosition(this.camera.position),i.getWorldQuaternion(this.camera.quaternion),this.camera.projectionMatrix.copy(i.projectionMatrix),this.camera.layers.mask=s?s.mask:i.layers.mask;const t=this.renderer.getSize(this.cachedSize),o=t.width/t.height,r=e.width/e.height,n=this.camera.projectionMatrix;o>r?n.elements[0]=n.elements[5]/r:n.elements[5]=n.elements[0]*r,this.overrideRenderTarget(e,(()=>{this.renderer.clear(),this.renderer.render(this.scene,this.camera)}))}return o&&(o.add(t),this.scene.matrixWorld.identity()),this.scene.remove(t),e}setScissors(e,t){if(e){if(!t)throw Error("Rect to restrict rendering to required when enabling scissors.");this.renderer.setScissorTest(!0),this.renderer.setScissor(t.x,t.y,t.width,t.height)}else{const e=this.renderer.getSize(this.cachedSize);this.renderer.setScissor(0,0,e.width,e.height),this.renderer.setScissorTest(!1)}}renderSphericalProjection(e,t,i,s,o){const r=v.circle_projection;r.uniforms.borderColor.value.copy(o||new n.Vector4(1,1,1,1)),r.uniforms.borderSize.value=s||0,r.uniforms.textureSampleScale.value=i||5,r.uniforms.panoTexture.value=e,this.scene.add(this.circleProjectionPlane),this.circleProjectionPlane.position.z=0,this.overrideRenderTarget(t,(()=>{this.renderer.render(this.scene,this.orthoCamera)})),this.scene.remove(this.circleProjectionPlane),r.uniforms.panoTexture.value=null}renderEquirectangular(e,t,i=0){const s=this.equirectProjectionPlane.material;s.uniforms.cubemap.value=e,s.uniforms.yaw.value=i,this.scene.add(this.equirectProjectionPlane),this.equirectProjectionPlane.position.z=0,this.overrideRenderTarget(t,(()=>{this.renderer.render(this.scene,this.orthoCamera)})),this.scene.remove(this.equirectProjectionPlane),s.uniforms.cubemap.value=null,s.uniforms.yaw.value=0}overrideRenderTarget(e,t){const i=this.renderTargetSwap(e);t(),this.renderTargetRestore(i)}renderTargetSwap(e){const t=this.renderer.xr.enabled,i=this.renderer.getRenderTarget(),s=this.renderer.getViewport(this.cachedViewport2);return this.renderer.xr.enabled=!1,this.renderer.setRenderTarget(e),{xr:t,rtt:i,viewport:s}}renderTargetRestore(e){this.renderer.xr.enabled=e.xr,this.renderer.setRenderTarget(e.rtt),this.renderer.setViewport(e.viewport)}renderAndReadAsync(e,t,i){const s=i.buffer;if(!this.renderer.capabilities.isWebGL2)return this.log.debug("renderAsync call webgl2, falling back to sync render/read"),this.render(e.target,e.mesh,e.camera),Promise.resolve(this.getRenderTargetData(e.target,s));const o=this.renderTargetSwap(e.target),r=this.renderer.getContext(),n=i.webglBuffer||r.createBuffer();if(!n)throw Error("Unable to create pack buffer");return r.bindBuffer(r.PIXEL_PACK_BUFFER,n),r.bufferData(r.PIXEL_PACK_BUFFER,s.byteLength,r.DYNAMIC_READ),e.clear&&this.renderer.clear(),this.renderer.render(e.mesh,e.camera),r.readPixels(t.x,t.y,t.width,t.height,r.RGBA,r.UNSIGNED_BYTE,0),r.bindBuffer(r.PIXEL_PACK_BUFFER,null),this.renderTargetRestore(o),this.cwfRenderer.fence(r).then((()=>(r.bindBuffer(r.PIXEL_PACK_BUFFER,n),r.getBufferSubData(r.PIXEL_PACK_BUFFER,0,s),r.bindBuffer(r.PIXEL_PACK_BUFFER,null),i.webglBuffer||r.deleteBuffer(n),s)))}}},28870:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>f});var s=i(97542),o=i(28269),r=i(53954),n=i(23254),a=i(79992),h=i(86400),l=i(17788),d=i(97998),c=i(20241);const u=new d.Z("mds-floor-deserializer");class m{deserialize(e){var t,i,s,o;if(!e||!this.validate(e))return u.debug("Deserialized invalid room data from MDS",e),null;const r=e=>null!==e?e:void 0,n=e.dimensions||{height:null,width:null,depth:null,areaFloor:null},{height:a,width:h,depth:l,areaFloor:d}=n;return new c.d({id:e.id,meshSubgroup:e.meshId||-1,floorId:null!==(i=null===(t=e.floor)||void 0===t?void 0:t.id)&&void 0!==i?i:"",height:r(a),width:r(h),depth:r(l),areaFloor:r(d),tags:null!==(o=null===(s=e.tags)||void 0===s?void 0:s.filter((e=>e)))&&void 0!==o?o:void 0})}validate(e){const t=["id","meshId"].every((t=>t in e)),i=e.floor&&e.floor.id&&"number"==typeof e.floor.meshId;return t&&!!i}}class p extends l.u{constructor(){super(...arguments),this.prefetchKey="data.model.rooms"}async read(e){const t=new m,i={modelId:this.getViewId()};return this.query(h.GetRooms,i,e).then((e=>{var i,s;const o=null===(s=null===(i=null==e?void 0:e.data)||void 0===i?void 0:i.model)||void 0===s?void 0:s.rooms;if(!o||!Array.isArray(o))return null;return o.reduce(((e,i)=>{const s=t.deserialize(i);return s&&(e[s.id]=s),e}),{})}))}}var g=i(80742);class f extends s.Y{constructor(){super(...arguments),this.name="room-data",this.visitedRooms={}}async init(e,t){const{baseModelId:i,baseUrl:s}=e,h=await t.market.waitForData(g.R);this.store=new p({context:h.mdsContext,baseUrl:s,readonly:!0,viewId:i});const l=await this.store.read();this.data=new o.Z(l||{}),t.market.register(this,o.Z,this.data);const d=await t.market.waitForData(n.O),c=this.data.roomCount,u=e=>{const i=d.closestMode;this.visitedRooms[e.id]||(this.visitedRooms[e.id]={visitCount:0}),this.visitedRooms[e.id].visitCount++;const s=this.visitedRooms[e.id].visitCount;t.broadcast(new a.D(e.id,e.meshSubgroup,i,s,c,""))},m=await t.market.waitForData(r.Z),f=m.makeSweepChangeSubscription((e=>{if(e){const t=m.getSweep(e);this.data.selected.value=t.roomId,this.data.commit()}})),y=this.data.selected.onChanged((e=>{if(null!==e){const t=this.data.get(e);t&&u(t)}}));this.bindings.push(f,y)}}},48733:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var s=i(97542),o=i(97998),r=i(44584);function n(e,t){return fetch(e,Object.assign(Object.assign({},t),{credentials:"omit"}))}function a(e,t){const i=e instanceof Request?e.url:e,s=(0,r.m)(i,{});return fetch(e,Object.assign(Object.assign({},t),{headers:Object.assign(Object.assign({},null==t?void 0:t.headers),s)}))}const h=[1/0,Array,ArrayBuffer,Boolean,DataView,Date,Error,EvalError,Float32Array,Float64Array,Function,Int8Array,Int16Array,Int32Array,JSON,Map,Math,NaN,Number,Object,Promise,Proxy,RangeError,ReferenceError,Reflect,RegExp,Set,String,SyntaxError,TypeError,URIError,Uint8Array,Uint8ClampedArray,Uint16Array,Uint32Array,WeakMap,WeakSet,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,escape,eval,isFinite,isNaN,parseFloat,parseInt,unescape,XMLHttpRequest,Headers,HTMLIFrameElement,Document,HTMLDocument,HTMLCanvasElement,DOMException,URLSearchParams,ResizeObserver];class l{constructor(e,t){this.originalSetFunc=e,this.originalClearFunc=t,this.disposed=!1,this.ids=new Set}setFunc(e,t){if(this.disposed)return-1;const i=this.originalSetFunc((()=>e()),t);return this.ids.add(i),i}clearFunc(e){this.disposed||this.ids.has(e)&&(this.ids.delete(e),this.originalClearFunc(e))}dispose(){if(!this.disposed){this.disposed=!0;for(const e of this.ids)this.originalClearFunc(e);this.ids.clear()}}}const d=function(e){return Object.freeze(e),Object.getOwnPropertyNames(e).forEach((function(t){const i=Object.getOwnPropertyDescriptor(e,t);(null==i?void 0:i.value)&&"object"==typeof i.value&&d(i.value)})),e};class c extends s.Y{constructor(){super(...arguments),this.overlayElement=null,this.name="SesModule",this.frozen=!1}freezeForStrict(){if(!this.frozen){for(const e of h)d(e);this.frozen=!0}}async init(e){await i.e(519).then(i.bind(i,13806)),globalThis.process&&(globalThis.process.on=()=>{}),this.overlayElement=e.overlayRoot}async makeSecureEnvironment(e,t,i,s){if(!this.overlayElement)return this.log.warn("Not creating a secure env due to missing overlay element"),null;if(t.startsWith("http")||t.startsWith("//")&&window.location.href.match(/^https?:/))try{const e=await fetch(t);t=await e.text()}catch(e){return this.log.warn("There was an error retrieving the plugin source."),null}let r=this.overlayElement.querySelector(`.${e}`);r&&this.overlayElement.removeChild(r),r=document.createElement("div"),r.classList.add("plugin-root-element"),r.classList.add(e),this.overlayElement.appendChild(r);const h=r.attachShadow({mode:"closed"}),d=new o.Z(`plugin ${e}`);Object.freeze(d);const c=new l(((e,t)=>window.setInterval(e,t)),(e=>window.clearInterval(e))),u=new l(((e,t)=>window.setTimeout(e,t)),(e=>window.clearTimeout(e))),m={log:(...e)=>d.info(...e),error:(...e)=>d.error(...e),info:(...e)=>d.info(...e),warn:(...e)=>d.warn(...e),time:e=>d.time(e),timeEnd:e=>d.timeEnd(e)};function p(e,t){return c.setFunc(e,t)}function g(e){return c.clearFunc(e)}function f(e,t){return u.setFunc(e,t)}function y(e){return u.clearFunc(e)}const w={setInterval:p,clearInterval:g,setTimeout:f,clearTimeout:y,console:m,window:{setInterval:p,clearInterval:g,setTimeout:f,clearTimeout:y,console:m,parent:{postMessage(e,t,i){"string"==typeof t?window.parent.postMessage(e,t,i):window.parent.postMessage(e,t)}}}},v=Object.assign(Object.assign({},w.window),{HTMLIFrameElement:HTMLIFrameElement,location:{href:""},document:document,getComputedStyle:getComputedStyle.bind(globalThis),parent:window.parent}),b=Object.assign(Object.assign({},w),{window:v,HTMLIFrameElement:HTMLIFrameElement,Document:Document,HTMLDocument:HTMLDocument,HTMLCanvasElement:HTMLCanvasElement,DOMException:DOMException,URLSearchParams:URLSearchParams,ResizeObserver:ResizeObserver,Error:Error,Headers:Headers,document:document,navigator:{userAgent:navigator.userAgent,language:navigator.language},overlaySlot:()=>h,notifyEvent(t,i){((t,i)=>{s.set(e.split("-").slice(1).join("-"),{name:t,eventData:i})})(t,i)}});if(i.canFetch){const e=i.canFetchAsUser?a:n;v.fetch=e,b.fetch=e,b.XMLHttpRequest=XMLHttpRequest}const{strict:M}=i,T=new Compartment(M?w:b);T.evaluate(t,M?void 0:{__evadeImportExpressionTest__:!0,__evadeHtmlCommentTest__:!0});let D=!1;return{compartment:T,overlayElement:this.overlayElement,dispose:()=>{var e;D||(D=!0,null===(e=this.overlayElement)||void 0===e||e.removeChild(r),c.dispose(),u.dispose())}}}}const u=c},89137:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>M});var s=i(97542),o=i(92810),r=i(81396),n=i(80218),a=i.n(n),h=i(46262),l=i.n(h);const d={sky:{uniforms:{topColor:{type:"v3",value:new r.Vector3(.094,.102,.11)},bottomColor:{type:"v3",value:new r.Vector3(.2,.216,.235)},radius:{type:"f",value:100}},vertexShader:a(),fragmentShader:l()}};class c extends r.RawShaderMaterial{constructor(e={}){super(Object.assign({fragmentShader:d.sky.fragmentShader,vertexShader:d.sky.vertexShader,uniforms:r.UniformsUtils.clone(d.sky.uniforms),name:"SkyboxMaterial"},e))}}var u=i(17878),m=i(94046),p=i(12529),g=i(18596);class f{constructor(e,t,i,s,o=u.o.ALL){this.scene=e,this.cameraData=t,this.addToRaycasting=i,this.removeFromRaycasting=s,this.renderLayer=o,this.bindings=[]}init(){const{visualMesh:e,colliderMesh:t}=this.setupSkysphere();this.skyVisual=e,this.material=this.skyVisual.material,this.skyCollider=t}dispose(){this.material.uniforms.pano0Map.value.dispose(),this.material.uniforms.pano1Map.value.dispose(),this.material.dispose(),this.skyVisual.geometry.dispose()}activate(e){this.scene.addChild(this.scene.ids.Root,this.skyVisual),this.skyVisual.updateMatrixWorld(!0),this.skyVisual.visible=!0,this.addToRaycasting(this.skyCollider,!1)}deactivate(e){for(const e of this.bindings)e.cancel();this.bindings=[],this.scene.removeChild(this.scene.ids.Root,this.skyVisual),this.scene.removeChild(this.scene.ids.CameraRig,this.skyCollider),this.removeFromRaycasting(this.skyCollider)}updateBackgroundColors(e,t){const i=new r.Color(e),s=new r.Color(t);this.material.uniforms.topColor.value.set(i.r,i.g,i.b),this.material.uniforms.bottomColor.value.set(s.r,s.g,s.b)}beforeRender(){this.skyVisual.position.copy(this.cameraData.pose.position),this.skyCollider.position.copy(this.cameraData.pose.position);const e=this.cameraData.pose.autoOrtho?this.cameraData.pose.fovDistanceScale():1;this.skyVisual.scale.set(e,e,e)}render(){}setupSkysphere(e){const t=new r.SphereGeometry(1e4,5,5);t.computeBoundingBox();const i=g.oR.far-10,s=new r.SphereGeometry(i,5,5);s.computeBoundingBox(),e||(e=new c({side:r.BackSide}));const o=new m.i(s,e);o.layers.mask=this.renderLayer.mask,o.name="Skysphere",o.renderOrder=p.z.boundingSkybox,o.updateMatrixWorld(!0),e.uniforms.radius.value=i,e.depthWrite=!1,e.depthTest=!1;return{visualMesh:o,colliderMesh:new m.i(t,new r.MeshBasicMaterial({opacity:0,depthWrite:!1,side:r.BackSide}))}}}var y=i(34029),w=i(28361),v=i(59625),b=i(9037);class M extends s.Y{constructor(){super(...arguments),this.name="skybox-module"}async init(e,t){const[i,s,r,n]=await Promise.all([t.getModuleBySymbol(o.Aj),t.market.waitForData(y.e),t.getModuleBySymbol(o.PZ),t.market.waitForData(b.M)]),a=t.claimRenderLayer("skybox"),h=i.getScene();this.skybox=new f(h,n,r.registerMesh,r.unregisterMesh,a),t.addComponent(this,this.skybox);const l=s.tryGetProperty(v.gx.BackgroundColor,w.z.default);this.skybox.updateBackgroundColors(w.K[l].bgPrimary,w.K[l].bgSecondary),this.bindings.push(s.onPropertyChanged(v.gx.BackgroundColor,(e=>{this.skybox.updateBackgroundColors(w.K[e].bgPrimary,w.K[e].bgSecondary)})))}}},84366:(e,t,i)=>{"use strict";i.d(t,{Z:()=>o});var s=i(98010);class o extends s.v0{constructor(e,t,i){super(),this.size=e,this.sweepId=t,this.renderTarget=i}}},71076:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var s=i(97542),o=i(53954),r=i(21973),n=i(35826),a=i(23254),h=i(69739),l=i(79727),d=i(10374),c=i(96154);class u extends s.Y{constructor(){super(...arguments),this.name="sweep-viewdata",this.selectionEnabled=!0,this.nonPanoCurrentPuckVisible=!1,this.sweepMap={},this.sweepIdList=[],this.enableSweepSelection=async e=>{this.selectionEnabled=!0},this.disableSweepSelection=async e=>{this.selectionEnabled=!1},this.toggleCurrentPuck=async e=>{this.nonPanoCurrentPuckVisible=e.visible,this.updateVisibility(this.viewData.data.currentSweep)},this.onSweepSelectCommand=async e=>{this.selectionEnabled&&this.viewData.modifySelectAnimation(e.id,e.selected,e.duration)},this.onSweepHoverCommand=async e=>{this.viewData.modifySelectAnimation(e.id,e.hovered,e.duration)},this.updateVisibility=async e=>{if(this.viewmodeData.isInside()){if(e){const t=this.viewData.getSweep(e).neighbours;this.viewData.iterate((e=>{const i=this.viewData.getSweepVisibility(e)&&-1!==t.indexOf(e.id);this.viewData.setVisible(e.id,i)})),this.viewData.setVisible(e,!1)}}else this.viewData.iterate((e=>{this.viewData.setVisible(e.id,this.floorsViewData.isCurrentOrAllFloors(e.floorId))})),e&&this.viewData.setVisible(e,this.nonPanoCurrentPuckVisible)}}async init(e,t){this.data=await t.market.waitForData(o.Z),this.viewData=t.market.tryGetData(r.D)||new r.D(this.data),t.market.register(this,r.D,this.viewData),this.bindings.push(t.commandBinder.addBinding(n.iF,this.onSweepSelectCommand),t.commandBinder.addBinding(n.kR,this.onSweepHoverCommand),t.commandBinder.addBinding(n.zd,this.enableSweepSelection),t.commandBinder.addBinding(n.ZD,this.disableSweepSelection),t.commandBinder.addBinding(n.e9,this.toggleCurrentPuck),this.data.onChanged((()=>this.viewData.updateViewData())),t.msgBus.subscribe(c.sY,(()=>this.viewData.updateViewData()))),this.viewmodeData=await t.market.waitForData(a.O),this.floorsViewData=await t.market.waitForData(l.c),this.bindings.push(this.data.onPropertyChanged("currentSweep",this.updateVisibility),t.subscribe(h.P,(()=>this.updateVisibility(this.viewData.data.currentSweep))),t.subscribe(d.bS,(()=>this.updateVisibility(this.viewData.data.currentSweep))),this.viewmodeData.makeModeChangeSubscription((()=>this.updateVisibility(this.viewData.data.currentSweep)))),this.updateVisibility(this.viewData.data.currentSweep)}onUpdate(e){this.viewData.updateAnimations(e)}}},9163:(e,t,i)=>{"use strict";i.r(t),i.d(t,{AssetDockedMessage:()=>m.ro,AssetUndockedMessage:()=>m.A,CloseAndRemoveToolsCommand:()=>u.Ye,CloseCurrentToolCommand:()=>u.eS,CloseToolCommand:()=>u.CH,CollapseBottomPanelCommand:()=>u.qy,OpenInitialToolCommand:()=>u.r_,OpenPreviousToolCommand:()=>u.cR,OpenToolCommand:()=>u.z2,PanelCollapseMessage:()=>m.U7,PanelTransitionEndMessage:()=>m.rh,RegisterToolsCommand:()=>u.MV,SetAppBarVisibleMessage:()=>m.bz,ToggleToolCommand:()=>u.tT,ToggleViewingControlsMessage:()=>m.ps,Tool:()=>A.U,ToolPalette:()=>l.$r,ToolPanelLayout:()=>l.wS,ToolToggleCollapseCommand:()=>u.Fg,Tools:()=>l.w1,ToolsData:()=>d.t,default:()=>F});var s=i(97542),o=i(92810),r=i(54244);var n=i(9037),a=i(23254),h=i(6282),l=i(76631),d=i(91380),c=i(1512),u=i(89549),m=i(40964),p=i(59425),g=i(92211),f=i(40505),y=i(25071),w=i(38319),v=i(50575),b=i(34029),M=i(92290),T=i(80008),D=i(38399),x=i(80361),S=i(19280),C=i(2367),P=i(90632),O=i(45905),k=i(13297);class B extends s.Y{constructor(){super(...arguments),this.name="tools-module",this.activeToolDurationMap={},this.keyboardTimeout=0,this.bottomPanelHeight=0,this.sidePanelWidth=0,this.sidePanelLeft=0,this.initialUrlToolOpened=!1,this.closeAndRemoveTools=async e=>!(e&&!await this.deactivateCurrentTool())&&(this.closeModal(),this.toolsData.setActiveTool(null),this.toolsData.removeAllTools(),!0),this.onAssetDocked=()=>{this.toolsData.assetDocked=!0,this.toolsData.commit()},this.onAssetUndocked=()=>{this.toolsData.assetDocked=!1,this.toolsData.commit()},this.handleTextBoxFocus=e=>{window.clearTimeout(this.keyboardTimeout),e.focused?this.keyboardTimeout=window.setTimeout((()=>{document.documentElement.classList.add("keyboard-layout")}),500):document.documentElement.classList.remove("keyboard-layout")},this.onToggleModal=async e=>{this.toggleModal(e.modal,e.open)},this.closeModal=()=>{this.toolsData.openModal&&this.toggleModal(this.toolsData.openModal,!1)},this.allowToolOrViewChange=async()=>{var e;const t=this.toolsData.getActiveTool();if(!(null===(e=null==t?void 0:t.manager)||void 0===e?void 0:e.hasPendingEdits))return!0;if(await t.manager.hasPendingEdits()){const e={title:D.Z.TOOLS.UNSAVED_CHANGES_TITLE,message:D.Z.TOOLS.UNSAVED_CHANGES_MESSAGE,cancellable:!0,confirmPhraseKey:D.Z.TOOLS.UNSAVED_CHANGES_CONFIRM,cancelPhraseKey:D.Z.TOOLS.UNSAVED_CHANGES_CANCEL};return await this.engine.commandBinder.issueCommand(new M.EW(M.Rx.DISPLAY,e))===M.Uc.CLOSE}return!0},this.toggleTool=async({toolName:e,active:t})=>t?this.openTool(e,!1):this.closeTool(e),this.closeCurrentTool=async()=>!!await this.deactivateCurrentTool()&&(this.toolsData.setActiveTool(null),!0),this.openInitialTool=async()=>{var e;const t=(0,p.u)(this.settingsData,this.initialUrlToolOpened);if(t){const i=this.toolsData.getTool(t);if(i){const s=(0,p.B)(this.settingsData,i);s&&(null===(e=i.manager)||void 0===e?void 0:e.deepLink)?i.manager.deepLink(s):this.engine.commandBinder.issueCommandWhenBound(new u.tT(t,!0))}}this.initialUrlToolOpened=!0},this.openPreviousTool=async()=>{this.toolsData.softOpening=!1,this.toolsData.commit();const e=this.toolsData.previousToolName;if(null===e)return;const t=this.toolsData.getTool(e)||null;null!==t?await this.deactivateCurrentTool(t)&&(await this.activateTool(t),this.toolsData.setActiveTool(e)):this.log.error(`Tool not loaded: ${e}`)},this.updateLayoutSize=()=>{const{toolPanelLayout:e}=this.toolsData;let t=e;const i=this.isSidePanelLayout();switch(e){case l.wS.NORMAL:i||(t=l.wS.NARROW);break;case l.wS.SIDE_PANEL:i||(t=l.wS.BOTTOM_PANEL);break;case l.wS.NARROW:i&&(t=l.wS.NORMAL);break;case l.wS.BOTTOM_PANEL:i&&(t=l.wS.SIDE_PANEL)}t!==e?(this.toolsData.toolPanelLayout=t,this.toolsData.commit()):e===l.wS.BOTTOM_PANEL&&this.adjustCanvasForPanel()},this.handleToolCollapse=async e=>{e!==this.toolsData.toolCollapsed&&(this.toolsData.toolCollapsed=e,this.toolsData.commit(),e||this.toolsData.toolPanelLayout!==l.wS.BOTTOM_PANEL||this.closeModal(),this.engine.broadcast(new m.U7(e)))},this.handleBottomPanelCollapse=async e=>{if(this.toolsData.toolPanelLayout===l.wS.BOTTOM_PANEL)return this.handleToolCollapse(e.collapse)},this.adjustCanvasForPanelActual=async()=>{const{toolPanelLayout:e,toolCollapsed:t,assetDocked:i,openModal:s}=this.toolsData,{sidePanelWidth:o,bottomPanelHeight:r,sidePanelLeft:n}=this,a=this.toolsData.getActiveTool(),h=e===l.wS.SIDE_PANEL,d=a&&h&&!t,u=d&&!!(null==a?void 0:a.panelLeft),m=d?-y.LH:0,p=m!==o?m:void 0,g=u?y.LH:0,f=g!==n?g:void 0,v=e===l.wS.BOTTOM_PANEL&&!s&&i,b=-Math.floor((this.containerData.size.height-55)*c.GW/100),M=v?b:0,T=M!==r?M:void 0,D=0===p||0===T?0:c.tn;this.resizeCanvas((0,w.hf)(p,T,f,D)),this.bottomPanelHeight=M,this.sidePanelWidth=m,this.sidePanelLeft=g},this.adjustCanvasForPanel=(0,x.D)(this.adjustCanvasForPanelActual,50),this.resizeCanvas=async e=>{this.engine.commandBinder.issueCommand(new v.M(e))}}async init(e,t){this.engine=t,await t.getModuleBySymbol(o.nI),this.analytics=await t.getModuleBySymbol(o.V6),[this.settingsData,this.appData,this.containerData,this.storageData]=await Promise.all([t.market.waitForData(b.e),t.market.waitForData(r.pu),t.market.waitForData(C.V),t.market.waitForData(P.Q)]),this.toolsData=new d.t,this.updateLayoutSize(),this.bindings.push(t.commandBinder.addBinding(u.tT,this.toggleTool),t.commandBinder.addBinding(u.cR,this.openPreviousTool),t.commandBinder.addBinding(u.z2,(async e=>this.openTool(e.toolName,e.softOpening))),t.commandBinder.addBinding(u.r_,this.openInitialTool),t.commandBinder.addBinding(u.CH,(async e=>this.closeTool(e.toolName))),t.commandBinder.addBinding(u.eS,this.closeCurrentTool),t.commandBinder.addBinding(g.B,this.onToggleModal),t.commandBinder.addBinding(g.r,(async()=>this.closeModal())),t.commandBinder.addBinding(u.Fg,(async e=>{this.handleToolCollapse(e.collapse)})),t.commandBinder.addBinding(u.qy,this.handleBottomPanelCollapse),this.containerData.onPropertyChanged("size",this.updateLayoutSize),t.subscribe(m.ro,this.onAssetDocked),t.subscribe(m.A,this.onAssetUndocked),t.subscribe(f.SN,this.handleTextBoxFocus),this.toolsData.onPropertyChanged("toolPanelLayout",this.adjustCanvasForPanel),this.toolsData.onPropertyChanged("activeToolChanged",this.adjustCanvasForPanel),this.toolsData.onPropertyChanged("toolCollapsed",this.adjustCanvasForPanel),this.toolsData.onPropertyChanged("assetDocked",this.adjustCanvasForPanel),t.commandBinder.addBinding(u.Ye,(({checkForEdits:e})=>this.closeAndRemoveTools(e))),t.commandBinder.addBinding(u.MV,(async({tools:e})=>this.registerTools(...e)))),t.commandBinder.issueCommand(new T.vM(this.allowToolOrViewChange)),t.market.register(this,d.t,this.toolsData)}dispose(e){super.dispose(e),this.closeAndRemoveTools(!1),e.market.unregister(this,d.t)}registerTools(...e){e.forEach((e=>{e.featureFlag&&this.bindings.push(this.settingsData.onPropertyChanged(e.featureFlag,(()=>this.updateEnabledTools())))})),this.toolsData.addTool(...e)}closePanel(){this.toolsData.toolPanelLayout=this.isSidePanelLayout()?l.wS.NORMAL:l.wS.NARROW,this.toolsData.commit()}openPanel(e){if(!e.panel)return this.toolsData.toolCollapsed=!!e.panelBar,void this.toolsData.commit();const t=this.isSidePanelLayout();this.toolsData.toolPanelLayout=t?l.wS.SIDE_PANEL:l.wS.BOTTOM_PANEL;const i=!t&&!this.toolsData.softOpening;this.toolsData.toolCollapsed=i,this.toolsData.commit()}toggleModal(e,t){const{openModal:i,toolPanelLayout:s}=this.toolsData;if(t&&i===e||!t&&e!==i)return;const o=e.toLowerCase(),n=this.appData.application===r.Mx.WORKSHOP?"workshop_gui":"showcase_gui";this.analytics.track(n,{gui_action:`open_${o}`}),e&&this.analytics.track("modal_shown",{modal:e}),this.toolsData.openModal=t?e:null,this.toolsData.commit(),s===l.wS.BOTTOM_PANEL&&t!==!!i&&this.adjustCanvasForPanel(),this.engine.broadcast(new f.nV(e,t))}async activateTool(e){if(this.toolInit)throw new Error("Current tool has not finished initializing!");this.toolsData.toolChangeInProgress=!0,this.toolsData.commit(),this.closeModal(),this.openPanel(e),e.manager&&(this.toolInit=e.manager.activate(),await this.toolInit,this.toolInit=null),this.toolsData.toolChangeInProgress=!1,this.toolsData.commit(),this.startTrackingTool(e)}async deactivateCurrentTool(e,t=!0){if(this.toolClosing)return!1;const i=this.toolsData.getActiveTool();if(!i)return!0;if(await this.toolInit,t&&!await this.allowToolOrViewChange())return!1;this.engine.broadcast(new f.Z_(!0)),await(0,k.PM)(this.storageData,(e=>e.transactionState===O.g.IDLE)),i.manager&&(this.toolsData.toolChangeInProgress=!0,this.toolsData.commit(),this.toolClosing=i.manager.deactivate(),await this.toolClosing,this.toolsData.toolChangeInProgress=!1,this.toolsData.commit()),this.engine.broadcast(new f.Z_(!1)),this.closeModal();const s=this.toolsData.toolPanelLayout===l.wS.BOTTOM_PANEL||this.toolsData.toolPanelLayout===l.wS.NARROW||(null==e?void 0:e.panelLeft)===i.panelLeft;return!(e&&e.panel&&e.panel===i.panel&&s)&&this.closePanel(),this.toolClosing=null,this.stopTrackingTool(i),!0}async activateToolName(e,t){const{activeToolName:i,toolChangeInProgress:s}=this.toolsData;if(s)return;if(i===e)return this.toolsData.softOpening=t,void this.toolsData.commit();const o=this.toolsData.getTool(e)||null;if(null===o)return void this.log.error(`Tool not loaded: ${e}`);const[r,l]=await Promise.all([this.engine.market.waitForData(n.M),this.engine.market.waitForData(a.O)]);await(0,h.E)(r,l),await this.deactivateCurrentTool(o)&&(await(0,h.E)(r,l),this.toolsData.softOpening=t,this.toolsData.commit(),await this.activateTool(o),this.toolsData.setActiveTool(o.id))}async deactivateToolName(e,t){e===this.toolsData.activeToolName&&await this.deactivateCurrentTool(t)&&this.toolsData.setActiveTool(null)}async openTool(e,t){await this.activateToolName(e,t)}async closeTool(e){await this.deactivateToolName(e)}startTrackingTool(e){this.activeToolDurationMap[e.analytic]=Date.now()}stopTrackingTool(e){const{analytic:t}=e;this.activeToolDurationMap[t]=Date.now()-this.activeToolDurationMap[t];const i={tool:`${t}_session_time`,duration:this.activeToolDurationMap[t]};this.analytics.track("tool_session_time",i)}updateEnabledTools(){const e=this.toolsData.toolsMap;e.atomic((()=>{for(const t of e)if(t.featureFlag){const e=this.settingsData.tryGetProperty(t.featureFlag,!1);t.enabled!==e&&(t.enabled=e,t.commit())}}))}isSidePanelLayout(){return!(0,S.p)(this.containerData.size)}}var A=i(50892);const F=B},8960:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>C});var s=i(97542),o=i(92810),r=i(34029),n=i(95882),a=i(64918),h=i(76532),l=i(90304),d=i(81396),c=i(73121),u=i(53954),m=i(98169),p=i(81248),g=i(9037),f=i(59625),y=i(12039),w=i(54244),v=i(10374),b=i(55318),M=i(79727),T=i(22201),D=i(66777),x=i(41326),S=i(65019);class C extends s.Y{constructor(){super(...arguments),this.name="viewmode-change"}async init(e,t){this.engine=t,this.viewmodesModule=await t.getModuleBySymbol(o.XT),this.bindings.push(t.subscribe(v.bS,(e=>this.setEnabledModes(e.application))),t.commandBinder.addBinding(x._i,this.onChangeViewmodeCommand.bind(this))),Promise.all([t.market.waitForData(w.pu),t.market.waitForData(r.e)]).then((([t,i])=>{this.settings=i,(0,S.g_)(i,e.inWorkshop),(0,S.CE)(i,e.inWorkshop),this.setEnabledModes(t.application),this.bindings.push(i.onPropertyChanged(f.gx.FloorPlan,(()=>(0,S.g_)(i,e.inWorkshop))),i.onPropertyChanged(f.gx.Dollhouse,(()=>(0,S.CE)(i,e.inWorkshop))))}))}async setEnabledModes(e){if(e===w.Mx.SHOWCASE){if(this.viewmodesModule.data.isDollhouseDisabled=()=>!this.settings.tryGetProperty(S.wY,!1),this.viewmodesModule.data.isFloorplanDisabled=()=>!this.settings.tryGetProperty(S.dF,!1),this.previousApp){const e=this.viewmodesModule.currentMode;if(e===n.Ey.Dollhouse&&this.viewmodesModule.data.isDollhouseDisabled()||e===n.Ey.Floorplan&&this.viewmodesModule.data.isFloorplanDisabled()){const e=(await this.engine.market.waitForData(b.O)).pose;if(e&&(0,n.Bw)(e.mode))this.goToInsideMode(a.n.Interpolate,{position:e.camera.position},e.mode);else{const e=(await this.engine.market.waitForData(g.M)).pose,t=(await this.engine.market.waitForData(u.Z)).getClosestSweep(e.position,!0),i=t?{position:t.position}:void 0;this.goToInsideMode(a.n.Interpolate,i,n.Ey.Panorama)}}}}else e===w.Mx.WORKSHOP&&(this.viewmodesModule.data.isDollhouseDisabled=()=>!1,this.viewmodesModule.data.isFloorplanDisabled=()=>!1);const t=this.settings.tryGetProperty(D.eC,!1),i=this.viewmodesModule.data.isFloorplanDisabled()||this.viewmodesModule.data.isDollhouseDisabled();this.settings.setProperty(D.eC,t&&!i),this.previousApp=e}async onChangeViewmodeCommand(e){try{switch(e.mode){case x.BD.INSIDE:return this.goToInsideMode(e.transitionType,e.pose,n.Ey.Panorama,e.transitionTime);case x.BD.DOLLHOUSE:return this.goToDollhouse(e.transitionType,e.pose,e.transitionTime);case x.BD.FLOORPLAN:return this.goToFloorplan(e.transitionType,e.pose,e.transitionTime);case x.BD.ORTHOGRAPHIC:return this.goToOrthographic(e.transitionType,e.pose,e.transitionTime);case x.BD.MESH:return this.goToInsideMode(e.transitionType,e.pose,n.Ey.Mesh,e.transitionTime)}}catch(t){throw this.log.error(t),new T.x6(`Could not move to mode ${e.mode}`,t)}}async goToInsideMode(e=a.n.Interpolate,t={},i,s){const o=this.engine.market.tryGetData(u.Z);if(!o)throw new T.YR;if(!(0,n.Bw)(i))throw new T.YR;let r=t.sweepID||o.currentSweep;if(r||(r=await this.getLookAtSweep(o)),o.isSweepUnaligned(r)){const e=o.getFirstAlignedSweep();r=e?e.id:this.getFirstSweepId(o)}return(0,n.Bw)(this.viewmodesModule.currentMode)&&o.isSweepUnaligned(o.currentSweep)?this.engine.commandBinder.issueCommand(new y.ju({sweep:r,rotation:t.rotation,transition:a.n.FadeToBlack})):this.viewmodesModule.switchToMode(i,e,{sweepID:r,rotation:t.rotation},s)}async getLookAtSweep(e){const t=this.engine.market.tryGetData(g.M);if(!t)return this.getFirstSweepId(e);const i=this.engine.market.tryGetData(M.c),s=(null==i?void 0:i.getFloorMin())||this.getModelMinHeight(),o=new d.Plane(l.fU.DOWN,s),r=(0,c.Fe)(t.pose.position,t.pose.rotation,o);if(!r)return this.getFirstSweepId(e);const n=[m._T(),m._k(),e=>!i||i.isCurrentOrAllFloors(e.floorId)],a=[p.l0(r)],h=e.sortByScore(n,a).shift();if(h)return h.sweep.id;const u=e.getClosestSweep(r,!0);return u?u.id:this.getFirstSweepId(e)}getFirstSweepId(e){const t=e.getFirstSweep();if(void 0===t)throw new Error("First enabled sweep not found");return t.id}getModelMinHeight(){return this.meshData||(this.meshData=this.engine.market.tryGetData(h._)),this.meshData?this.meshData.extendedBounds.min.y:0}async goToDollhouse(e=a.n.Interpolate,t={},i){if(this.viewmodesModule.data.isDollhouseDisabled())throw new T.YR;return this.viewmodesModule.switchToMode(n.Ey.Dollhouse,e,t,i)}async goToFloorplan(e=a.n.Interpolate,t={},i){if(this.viewmodesModule.data.isFloorplanDisabled())throw new T.YR;const s=await this.engine.getModuleBySymbol(o.iM);return s&&await s.getTransitionPromise(),this.viewmodesModule.switchToMode(n.Ey.Floorplan,e,t,i,!0)}async goToOrthographic(e=a.n.Interpolate,t={},i){const s=await this.engine.getModuleBySymbol(o.iM);return s&&await s.getTransitionPromise(),this.viewmodesModule.switchToMode(n.Ey.Orthographic,e,t,i)}}},69984:(e,t,i)=>{"use strict";i.d(t,{D5:()=>l,Ex:()=>d,G1:()=>a,rn:()=>h});var s=i(81396),o=i(39880);const r=()=>Math.random(),n={},a=(e,t=r())=>(n[t]||(n[t]=new s.Vector4(r(),r(),r(),e)),n[t]),h=()=>new s.Color(r(),r(),r()),l=e=>e instanceof Object&&"r"in e&&"g"in e&&"b"in e;function d(e){return`#${(0,o.Q_)(255*e.r,2,"0",16)}${(0,o.Q_)(255*e.g,2,"0",16)}${(0,o.Q_)(255*e.b,2,"0",16)}`}},6282:(e,t,i)=>{"use strict";async function s(e,t){if(await e.transition.promise,!t.canStartTransition()){const e=new Promise((e=>{const i=t.onChanged((()=>{t.canStartTransition()&&(i.cancel(),e())}))}));await e}}i.d(t,{E:()=>s})},92393:e=>{e.exports="precision highp float;precision highp int;uniform mat4 viewMatrix;uniform vec3 cameraPosition;uniform float borderSize;uniform float textureSampleScale;uniform samplerCube panoTexture;uniform vec4 borderColor;varying vec2 vUv;const vec4 transparent=vec4(0.,0.,0.,0.);const float PI=3.14159265359;const float HALF_PI=PI/2.;void main(){float fadeDist=0.1;float r=1.;vec2 uv=(vUv*2.)-vec2(1.,1.);float d=length(uv);float p=d*PI-HALF_PI;float y=sin(p);float h=cos(p)*textureSampleScale;vec3 sampleVec=vec3(uv.x*h,y,uv.y*h);vec4 panoColor=textureCube(panoTexture,sampleVec);panoColor.a=clamp(((1.-d)/d)/fadeDist,0.,1.);vec4 outColor=mix(panoColor,transparent,step(1.,d));float isBorder=max(sign(borderSize),0.)*step(1.-borderSize,d)*step(d,1.);gl_FragColor=mix(outColor,borderColor,isBorder);}"},27115:e=>{e.exports="precision highp float;precision highp int;uniform mat4 modelMatrix;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;uniform mat4 viewMatrix;uniform mat3 normalMatrix;uniform vec3 cameraPosition;attribute vec3 position;attribute vec3 normal;attribute vec2 uv;varying vec2 vUv;void main(){vUv=uv;vec4 projectedPosition=projectionMatrix*viewMatrix*modelMatrix*vec4(position,1.);gl_Position=projectedPosition;}"},20367:e=>{e.exports="precision highp float;precision highp int;uniform mat4 viewMatrix;uniform vec3 cameraPosition;uniform sampler2D bg;uniform sampler2D mask;varying vec2 vUv;void main(){vec4 existingColor=texture2D(bg,vUv);vec4 maskColor=texture2D(mask,vUv);gl_FragColor=vec4(existingColor.rgb*maskColor.rgb,maskColor.a);}"},98419:e=>{e.exports="precision highp float;precision highp int;uniform mat4 modelMatrix;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;uniform mat4 viewMatrix;uniform mat3 normalMatrix;uniform vec3 cameraPosition;attribute vec3 position;attribute vec3 normal;attribute vec2 uv;varying vec2 vUv;void main(){vUv=uv;gl_Position=projectionMatrix*vec4(position,1.);}"},14282:e=>{e.exports="precision highp float;precision highp int;uniform mat4 viewMatrix;uniform vec3 cameraPosition;\n#define M_PI  3.14159265359\nuniform samplerCube cubemap;uniform float yaw;varying vec2 vUv;void main(){vec2 uv=vUv;float theta=uv.x*2.*M_PI+yaw;float phi=uv.y*M_PI;vec3 longitude=vec3(sin(theta),1.,-cos(theta));vec3 latitude=vec3(sin(phi),-cos(phi),sin(phi));vec3 dir=longitude*latitude;normalize(dir);gl_FragColor=vec4(textureCube(cubemap,dir).rgb,1.);}"},17965:e=>{e.exports="precision highp float;precision highp int;uniform mat4 modelMatrix;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;uniform mat4 viewMatrix;uniform mat3 normalMatrix;uniform vec3 cameraPosition;attribute vec3 position;attribute vec3 normal;attribute vec2 uv;varying vec2 vUv;void main(){vUv=vec2(1.-uv.x,uv.y);gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}"},46262:e=>{e.exports="precision highp float;precision highp int;uniform mat4 viewMatrix;uniform vec3 cameraPosition;uniform vec3 topColor;uniform vec3 bottomColor;uniform float radius;varying vec4 worldPosition;\n#define SRGB_TO_LINEAR(c)pow((c),vec3(2.2))\n#define LINEAR_TO_SRGB(c)pow((c),vec3(1./2.2))\n#define USE_DITHER \nfloat gradientNoise(in vec2 uv){const vec3 magic=vec3(0.06711056,0.00583715,52.9829189);return fract(magic.z*fract(dot(uv,magic.xy)));}void main(){float normalizedHeight=(worldPosition.y+radius)/(radius*2.);float ratio=smoothstep(0.,0.5,normalizedHeight);vec3 colorFromGradient=mix(SRGB_TO_LINEAR(bottomColor),SRGB_TO_LINEAR(topColor),ratio);vec3 color=LINEAR_TO_SRGB(colorFromGradient);\n#if defined (USE_DITHER)\ncolor+=(1./255.)*gradientNoise(gl_FragCoord.xy)-(0.5/255.);\n#endif\ngl_FragColor=vec4(color,1.);}"},80218:e=>{e.exports="precision highp float;precision highp int;uniform mat4 modelMatrix;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;uniform mat4 viewMatrix;uniform mat3 normalMatrix;uniform vec3 cameraPosition;attribute vec3 position;attribute vec3 normal;attribute vec2 uv;varying vec4 worldPosition;void main(){worldPosition=modelMatrix*vec4(position,1.);gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}"}}]);