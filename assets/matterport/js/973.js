/*! For license information please see 973.js.LICENSE.txt */
(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[973],{70759:(e,t,n)=>{"use strict";n.d(t,{i:()=>z});var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}Object.create;Object.create;var o=n(67294);var a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function s(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function u(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function c(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;var l=n(94184),d=n.n(l),h=function(e){return(Array.isArray(e)?e:[e]).filter((function(e){return!!e})).map((function(e){return"mdc-theme--"+e.replace(/([A-Z])/g,(function(e){return"-"+e.toLowerCase()}))}))},p=o.forwardRef((function(e,t){var n=e.tag,r=void 0===n?"div":n,i=(e.theme,e.element),s=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}(e,["tag","theme","element"]),u=i?i.props(s):s,c=i?f(t,i.setRef):t;return o.createElement(r,a({},u,{ref:c}))})),f=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){var n,r;try{for(var i=s(e),o=i.next();!o.done;o=i.next()){var a=o.value;"function"==typeof a?a(t):a&&"current"in a&&(a.current=t)}}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}}},m=function(e,t){"function"==typeof e?e(t):e&&"current"in e&&(e.current=t)};function v(e){var t=o.forwardRef(e),n=function(e,t){return o.createElement(o.Fragment,null)};return n.displayName=e.constructor.name||"RMWCComponent",t.displayName=n.displayName,t}var y={blur:"onBlur",cancel:"onCancel",click:"onClick",close:"onClose",contextmenu:"onContextMenu",copy:"onCopy",cut:"onCut",auxclick:"onAuxClick",doubleclick:"onDoubleClick",dragend:"onDragEnd",dragstart:"onDragStart",drop:"onDrop",focus:"onFocus",input:"onInput",invalid:"onInvalid",keydown:"onKeyDown",keypress:"onKeyPress",keyup:"onKeyUp",mousedown:"onMouseDown",mouseup:"onMouseUp",paste:"onPaste",pause:"onPause",play:"onPlay",pointercancel:"onPointerCancel",pointerdown:"onPointerDown",pointerup:"onPointerUp",ratechange:"onRateChange",reset:"onReset",seeked:"onSeeked",submit:"onSubmit",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchstart:"onTouchStart",volumechange:"onVolumeChange",abort:"onAbort",animationend:"onAnimationEnd",animationiteration:"onAnimationIteration",animationstart:"onAnimationStart",canplay:"onCanPlay",canplaythrough:"onCanPlayThrough",drag:"onDrag",dragenter:"onDragEnter",dragexit:"onDragExit",dragleave:"onDragLeave",dragover:"onDragOver",durationchange:"onDurationChange",emptied:"onEmptied",encrypted:"onEncrypted",ended:"onEnded",error:"onError",gotpointercapture:"onGotPointerCapture",load:"onLoad",loadeddata:"onLoadedData",loadedmetadata:"onLoadedMetadata",loadstart:"onLoadStart",lostpointercapture:"onLostPointerCapture",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseover:"onMouseOver",playing:"onPlaying",pointermove:"onPointerMove",pointerout:"onPointerOut",pointerover:"onPointerOver",progress:"onProgress",scroll:"onScroll",seeking:"onSeeking",stalled:"onStalled",suspend:"onSuspend",timeupdate:"onTimeUpdate",toggle:"onToggle",touchmove:"onTouchMove",transitionend:"onTransitionEnd",waiting:"onWaiting",wheel:"onWheel",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",pointerenter:"onPointerEnter",pointerleave:"onPointerLeave",change:"onChange",select:"onSelect",beforeinput:"onBeforeInput",compositionend:"onCompositionEnd",compositionstart:"onCompositionStart",compositionupdate:"onCompositionUpdate"},g=function(e){return y[e]||e},_=function(){function e(e){this._classes=new Set,this._events={},this._style={},this._props={},this._ref=null,this._onChange=null,this._onChange=e,this.onChange=this.onChange.bind(this),this.addClass=this.addClass.bind(this),this.removeClass=this.removeClass.bind(this),this.hasClass=this.hasClass.bind(this),this.setProp=this.setProp.bind(this),this.getProp=this.getProp.bind(this),this.removeProp=this.removeProp.bind(this),this.setStyle=this.setStyle.bind(this),this.addEventListener=this.addEventListener.bind(this),this.removeEventListener=this.removeEventListener.bind(this),this.setRef=this.setRef.bind(this)}return e.prototype.onChange=function(){this._onChange&&this._onChange()},e.prototype.destroy=function(){var e=this;this._onChange=null,this._events={},this._style={},this._props={},this._classes=new Set,setTimeout((function(){e._ref=null}))},e.prototype.addClass=function(e){this._classes.has(e)||(this._classes.add(e),this.onChange())},e.prototype.removeClass=function(e){this._classes.has(e)&&(this._classes.delete(e),this.onChange())},e.prototype.hasClass=function(e){return this._classes.has(e)},e.prototype.setProp=function(e,t,n){void 0===n&&(n=!1),this._props[e]!==t&&(this._props[e]=t,!n&&this.onChange())},e.prototype.getProp=function(e){return this._props[e]},e.prototype.removeProp=function(e){void 0!==this._props[e]&&(delete this._props[e],this.onChange())},e.prototype.props=function(e){var t=this,n=e.className,r=void 0===n?"":n,i=e.style,o=void 0===i?{}:i,s=Object.entries(e).reduce((function(e,n){var r=u(n,2),i=r[0],o=r[1],a=t._events[i];if("function"==typeof o&&"function"==typeof a){e[i]=function(e){return a(e),o(e)}}return e}),a({},this._events)),l=d()(r,c([],u(this._classes))),h=a(a({},this._style),o);return a(a(a(a({},e),this._props),s),{style:h,className:l})},e.prototype.setStyle=function(e,t){e=e.startsWith("--")?e:e.replace(/(-[a-z])/g,(function(e){return e.toUpperCase().replace("-","")})),this._style[e]!==t&&(this._style[e]=t,this.onChange())},e.prototype.addEventListener=function(e,t){var n=g(e);this._events[n]!==t&&(this._events[n]=t,this.onChange())},e.prototype.removeEventListener=function(e,t){var n=g(e);this._events[n]&&(delete this._events[n],this.onChange())},e.prototype.setRef=function(e){e&&(this._ref=e)},Object.defineProperty(e.prototype,"ref",{get:function(){return this._ref},enumerable:!1,configurable:!0}),e}(),E=function(e){return function(t,n,r){var i;void 0===r&&(r=!1),i=new CustomEvent(t,{detail:n,bubbles:r}),Object.defineProperty(i,"target",{value:n,writable:!1}),Object.defineProperty(i,"currentTarget",{value:n,writable:!1});var o=t;return e[o]&&e[o](i),i}},b={bottom:0,height:0,left:0,right:0,top:0,width:0},x=function(e,t){return(x=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};var T=function(){return(T=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};var M={animation:{prefixed:"-webkit-animation",standard:"animation"},transform:{prefixed:"-webkit-transform",standard:"transform"},transition:{prefixed:"-webkit-transition",standard:"transition"}},C={animationend:{cssProperty:"animation",prefixed:"webkitAnimationEnd",standard:"animationend"},animationiteration:{cssProperty:"animation",prefixed:"webkitAnimationIteration",standard:"animationiteration"},animationstart:{cssProperty:"animation",prefixed:"webkitAnimationStart",standard:"animationstart"},transitionend:{cssProperty:"transition",prefixed:"webkitTransitionEnd",standard:"transitionend"}};function S(e){return Boolean(e.document)&&"function"==typeof e.document.createElement}var A=function(){function e(e){void 0===e&&(e={}),this.adapter_=e}return Object.defineProperty(e,"cssClasses",{get:function(){return{}},enumerable:!0,configurable:!0}),Object.defineProperty(e,"strings",{get:function(){return{}},enumerable:!0,configurable:!0}),Object.defineProperty(e,"numbers",{get:function(){return{}},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{}},enumerable:!0,configurable:!0}),e.prototype.init=function(){},e.prototype.destroy=function(){},e}();var w={ACTIVE:"mdc-slider--active",DISABLED:"mdc-slider--disabled",DISCRETE:"mdc-slider--discrete",FOCUS:"mdc-slider--focus",HAS_TRACK_MARKER:"mdc-slider--display-markers",IN_TRANSIT:"mdc-slider--in-transit",IS_DISCRETE:"mdc-slider--discrete"},O={ARIA_DISABLED:"aria-disabled",ARIA_VALUEMAX:"aria-valuemax",ARIA_VALUEMIN:"aria-valuemin",ARIA_VALUENOW:"aria-valuenow",CHANGE_EVENT:"MDCSlider:change",INPUT_EVENT:"MDCSlider:input",PIN_VALUE_MARKER_SELECTOR:".mdc-slider__pin-value-marker",STEP_DATA_ATTR:"data-step",THUMB_CONTAINER_SELECTOR:".mdc-slider__thumb-container",TRACK_MARKER_CONTAINER_SELECTOR:".mdc-slider__track-marker-container",TRACK_SELECTOR:".mdc-slider__track"},P={PAGE_FACTOR:4},k=["mousedown","pointerdown","touchstart"],R=["mouseup","pointerup","touchend"],I={mousedown:"mousemove",pointerdown:"pointermove",touchstart:"touchmove"},L="ArrowDown",D="ArrowLeft",N="ArrowRight",H="ArrowUp",V="End",j="Home",B="PageDown",X="PageUp",U=function(e){function t(n){var r=e.call(this,T({},t.defaultAdapter,n))||this;return r.savedTabIndex_=NaN,r.active_=!1,r.inTransit_=!1,r.isDiscrete_=!1,r.hasTrackMarker_=!1,r.handlingThumbTargetEvt_=!1,r.min_=0,r.max_=100,r.step_=0,r.value_=0,r.disabled_=!1,r.preventFocusState_=!1,r.thumbContainerPointerHandler_=function(){return r.handlingThumbTargetEvt_=!0},r.interactionStartHandler_=function(e){return r.handleDown_(e)},r.keydownHandler_=function(e){return r.handleKeydown_(e)},r.focusHandler_=function(){return r.handleFocus_()},r.blurHandler_=function(){return r.handleBlur_()},r.resizeHandler_=function(){return r.layout()},r}return function(e,t){function n(){this.constructor=e}x(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(t,e),Object.defineProperty(t,"cssClasses",{get:function(){return w},enumerable:!0,configurable:!0}),Object.defineProperty(t,"strings",{get:function(){return O},enumerable:!0,configurable:!0}),Object.defineProperty(t,"numbers",{get:function(){return P},enumerable:!0,configurable:!0}),Object.defineProperty(t,"defaultAdapter",{get:function(){return{hasClass:function(){return!1},addClass:function(){},removeClass:function(){},getAttribute:function(){return null},setAttribute:function(){},removeAttribute:function(){},computeBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},getTabIndex:function(){return 0},registerInteractionHandler:function(){},deregisterInteractionHandler:function(){},registerThumbContainerInteractionHandler:function(){},deregisterThumbContainerInteractionHandler:function(){},registerBodyInteractionHandler:function(){},deregisterBodyInteractionHandler:function(){},registerResizeHandler:function(){},deregisterResizeHandler:function(){},notifyInput:function(){},notifyChange:function(){},setThumbContainerStyleProperty:function(){},setTrackStyleProperty:function(){},setMarkerValue:function(){},setTrackMarkers:function(){},isRTL:function(){return!1}}},enumerable:!0,configurable:!0}),t.prototype.init=function(){var e=this;this.isDiscrete_=this.adapter_.hasClass(w.IS_DISCRETE),this.hasTrackMarker_=this.adapter_.hasClass(w.HAS_TRACK_MARKER),k.forEach((function(t){e.adapter_.registerInteractionHandler(t,e.interactionStartHandler_),e.adapter_.registerThumbContainerInteractionHandler(t,e.thumbContainerPointerHandler_)})),this.adapter_.registerInteractionHandler("keydown",this.keydownHandler_),this.adapter_.registerInteractionHandler("focus",this.focusHandler_),this.adapter_.registerInteractionHandler("blur",this.blurHandler_),this.adapter_.registerResizeHandler(this.resizeHandler_),this.layout(),this.isDiscrete_&&0===this.getStep()&&(this.step_=1)},t.prototype.destroy=function(){var e=this;k.forEach((function(t){e.adapter_.deregisterInteractionHandler(t,e.interactionStartHandler_),e.adapter_.deregisterThumbContainerInteractionHandler(t,e.thumbContainerPointerHandler_)})),this.adapter_.deregisterInteractionHandler("keydown",this.keydownHandler_),this.adapter_.deregisterInteractionHandler("focus",this.focusHandler_),this.adapter_.deregisterInteractionHandler("blur",this.blurHandler_),this.adapter_.deregisterResizeHandler(this.resizeHandler_)},t.prototype.setupTrackMarker=function(){this.isDiscrete_&&this.hasTrackMarker_&&0!==this.getStep()&&this.adapter_.setTrackMarkers(this.getStep(),this.getMax(),this.getMin())},t.prototype.layout=function(){this.rect_=this.adapter_.computeBoundingRect(),this.updateUIForCurrentValue_()},t.prototype.getValue=function(){return this.value_},t.prototype.setValue=function(e){this.setValue_(e,!1)},t.prototype.getMax=function(){return this.max_},t.prototype.setMax=function(e){if(e<this.min_)throw new Error("Cannot set max to be less than the slider's minimum value");this.max_=e,this.setValue_(this.value_,!1,!0),this.adapter_.setAttribute(O.ARIA_VALUEMAX,String(this.max_)),this.setupTrackMarker()},t.prototype.getMin=function(){return this.min_},t.prototype.setMin=function(e){if(e>this.max_)throw new Error("Cannot set min to be greater than the slider's maximum value");this.min_=e,this.setValue_(this.value_,!1,!0),this.adapter_.setAttribute(O.ARIA_VALUEMIN,String(this.min_)),this.setupTrackMarker()},t.prototype.getStep=function(){return this.step_},t.prototype.setStep=function(e){if(e<0)throw new Error("Step cannot be set to a negative number");this.isDiscrete_&&("number"!=typeof e||e<1)&&(e=1),this.step_=e,this.setValue_(this.value_,!1,!0),this.setupTrackMarker()},t.prototype.isDisabled=function(){return this.disabled_},t.prototype.setDisabled=function(e){this.disabled_=e,this.toggleClass_(w.DISABLED,this.disabled_),this.disabled_?(this.savedTabIndex_=this.adapter_.getTabIndex(),this.adapter_.setAttribute(O.ARIA_DISABLED,"true"),this.adapter_.removeAttribute("tabindex")):(this.adapter_.removeAttribute(O.ARIA_DISABLED),isNaN(this.savedTabIndex_)||this.adapter_.setAttribute("tabindex",String(this.savedTabIndex_)))},t.prototype.handleDown_=function(e){var t=this;if(!this.disabled_){this.preventFocusState_=!0,this.setInTransit_(!this.handlingThumbTargetEvt_),this.handlingThumbTargetEvt_=!1,this.setActive_(!0);var n=function(e){t.handleMove_(e)},r=I[e.type],i=function(){t.handleUp_(),t.adapter_.deregisterBodyInteractionHandler(r,n),R.forEach((function(e){return t.adapter_.deregisterBodyInteractionHandler(e,i)}))};this.adapter_.registerBodyInteractionHandler(r,n),R.forEach((function(e){return t.adapter_.registerBodyInteractionHandler(e,i)})),this.setValueFromEvt_(e)}},t.prototype.handleMove_=function(e){e.preventDefault(),this.setValueFromEvt_(e)},t.prototype.handleUp_=function(){this.setActive_(!1),this.adapter_.notifyChange()},t.prototype.getClientX_=function(e){return e.targetTouches&&e.targetTouches.length>0?e.targetTouches[0].clientX:e.clientX},t.prototype.setValueFromEvt_=function(e){var t=this.getClientX_(e),n=this.computeValueFromClientX_(t);this.setValue_(n,!0)},t.prototype.computeValueFromClientX_=function(e){var t=this.max_,n=this.min_,r=(e-this.rect_.left)/this.rect_.width;return this.adapter_.isRTL()&&(r=1-r),n+r*(t-n)},t.prototype.handleKeydown_=function(e){var t=this.getKeyId_(e),n=this.getValueForKeyId_(t);isNaN(n)||(e.preventDefault(),this.adapter_.addClass(w.FOCUS),this.setValue_(n,!0),this.adapter_.notifyChange())},t.prototype.getKeyId_=function(e){return e.key===D||37===e.keyCode?D:e.key===N||39===e.keyCode?N:e.key===H||38===e.keyCode?H:e.key===L||40===e.keyCode?L:e.key===j||36===e.keyCode?j:e.key===V||35===e.keyCode?V:e.key===X||33===e.keyCode?X:e.key===B||34===e.keyCode?B:""},t.prototype.getValueForKeyId_=function(e){var t=this,n=t.max_,r=t.min_,i=t.step_||(n-r)/100;switch(this.adapter_.isRTL()&&(e===D||e===N)&&(i=-i),e){case D:case L:return this.value_-i;case N:case H:return this.value_+i;case j:return this.min_;case V:return this.max_;case X:return this.value_+i*P.PAGE_FACTOR;case B:return this.value_-i*P.PAGE_FACTOR;default:return NaN}},t.prototype.handleFocus_=function(){this.preventFocusState_||this.adapter_.addClass(w.FOCUS)},t.prototype.handleBlur_=function(){this.preventFocusState_=!1,this.adapter_.removeClass(w.FOCUS)},t.prototype.setValue_=function(e,t,n){if(void 0===n&&(n=!1),e!==this.value_||n){var r=this.min_,i=this.max_,o=e===r||e===i;this.step_&&!o&&(e=this.quantize_(e)),e<r?e=r:e>i&&(e=i),e=e||0,this.value_=e,this.adapter_.setAttribute(O.ARIA_VALUENOW,String(this.value_)),this.updateUIForCurrentValue_(),t&&(this.adapter_.notifyInput(),this.isDiscrete_&&this.adapter_.setMarkerValue(e))}},t.prototype.quantize_=function(e){return Math.round(e/this.step_)*this.step_},t.prototype.updateUIForCurrentValue_=function(){var e=this,t=this,n=t.max_,r=t.min_,i=(t.value_-r)/(n-r),o=i*this.rect_.width;this.adapter_.isRTL()&&(o=this.rect_.width-o);var a=function(e,t){if(S(e)&&t in M){var n=e.document.createElement("div"),r=M[t],i=r.standard,o=r.prefixed;return i in n.style?i:o}return t}(window,"transform"),s=function(e,t){if(S(e)&&t in C){var n=e.document.createElement("div"),r=C[t],i=r.standard,o=r.prefixed;return r.cssProperty in n.style?i:o}return t}(window,"transitionend");if(this.inTransit_){var u=function(){e.setInTransit_(!1),e.adapter_.deregisterThumbContainerInteractionHandler(s,u)};this.adapter_.registerThumbContainerInteractionHandler(s,u)}requestAnimationFrame((function(){e.adapter_.setThumbContainerStyleProperty(a,"translateX("+o+"px) translateX(-50%)"),e.adapter_.setTrackStyleProperty(a,"scaleX("+i+")")}))},t.prototype.setActive_=function(e){this.active_=e,this.toggleClass_(w.ACTIVE,this.active_)},t.prototype.setInTransit_=function(e){this.inTransit_=e,this.toggleClass_(w.IN_TRANSIT,this.inTransit_)},t.prototype.toggleClass_=function(e,t){t?this.adapter_.addClass(e):this.adapter_.removeClass(e)},t}(A);var Y=function(e){var t=(0,o.useRef)(),n=(0,o.useRef)(),s=function(e){var t=e.foundation,n=e.props,r=e.elements,i=e.api,s=u((0,o.useState)(0),2)[1],l=(0,o.useRef)(n);l.current=n;var d=(0,o.useMemo)((function(){return Object.keys(r).reduce((function(e,t){return e[t]=new _((function(){s((function(e){return e+1}))})),e}),{})}),[]),h=(0,o.useMemo)((function(){var e=t(a(a({},d),{getProps:function(){return l.current},emit:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return E(l.current).apply(void 0,c([],u(e)))}}));return i&&m(l.current.apiRef,i(a({foundation:e},d))),e}),[]);return(0,o.useEffect)((function(){var e=h;return e.init(),i&&m(l.current.apiRef,i(a({foundation:e},d))),m(l.current.foundationRef,e),function(){e.destroy(),m(l.current.apiRef,null),m(l.current.foundationRef,null),Object.values(d).map((function(e){return e.destroy()})),l.current={}}}),[h,d]),a({foundation:h},d)}({props:e,elements:{rootEl:!0,thumbContainerEl:!0,sliderPinEl:!0},foundation:function(e){var r,i,o,a=e.rootEl,s=e.thumbContainerEl,u=e.sliderPinEl,c=e.emit;return new U({hasClass:function(e){return a.hasClass(e)},addClass:function(e){return a.addClass(e)},removeClass:function(e){return a.removeClass(e)},getAttribute:function(e){return a.getProp(e)},setAttribute:(r=function(e,t){return a.setProp(e,t)},i=300,function(){var e=this,t=arguments,n=function(){o=null,r.apply(e,t)};null!==o&&clearTimeout(o),o=setTimeout(n,i)}),removeAttribute:function(e){return a.removeProp(e)},computeBoundingRect:function(){return a.ref?a.ref.getBoundingClientRect():b},getTabIndex:function(){return a.ref?a.ref.tabIndex:0},registerInteractionHandler:function(e,t){a.addEventListener(e,t)},deregisterInteractionHandler:function(e,t){a.removeEventListener(e,t)},registerThumbContainerInteractionHandler:function(e,t){s.addEventListener(e,t)},deregisterThumbContainerInteractionHandler:function(e,t){s.removeEventListener(e,t)},registerBodyInteractionHandler:function(e,t){document.body&&document.body.addEventListener(e,t)},deregisterBodyInteractionHandler:function(e,t){document.body&&document.body.removeEventListener(e,t)},registerResizeHandler:function(e){window.addEventListener("resize",e)},deregisterResizeHandler:function(e){window.removeEventListener("resize",e)},notifyInput:function(){c("onInput",{value:l.getValue()})},notifyChange:function(){c("onChange",{value:l.getValue()})},setThumbContainerStyleProperty:function(e,t){s.setStyle(e,t)},setTrackStyleProperty:function(e,n){var r;null===(r=t.current)||void 0===r||r.style.setProperty(e,n)},setMarkerValue:function(e){u.setProp("value",e)},setTrackMarkers:function(e,t,r){var i,o=e.toLocaleString(),a="linear-gradient(to right, currentColor 2px, transparent 0) "+("0 center / calc((100% - 2px) / "+("(("+t.toLocaleString()+" - "+r.toLocaleString()+") / "+o+")")+") 100% repeat-x");null===(i=n.current)||void 0===i||i.style.setProperty("background",a)},isRTL:function(){return!!a.ref&&"rtl"===getComputedStyle(a.ref).direction}})}}),l=s.foundation,d=i(s,["foundation"]);return(0,o.useEffect)((function(){void 0!==e.max&&l.setMax(+e.max)}),[e.max,l]),(0,o.useEffect)((function(){void 0!==e.min&&l.setMin(+e.min)}),[e.min,l]),(0,o.useEffect)((function(){var t=void 0!==e.value?Number(e.value):l.getValue(),n=l.getMin(),r=l.getMax();t<n&&(console.warn("Attempted to set slider to "+t+" which is less than min: "+n),t=n),t>r&&(console.warn("Attempted to set slider to "+t+" which is greater than max: "+r),t=r),l.setValue(t)}),[e.value,l]),(0,o.useEffect)((function(){void 0!==e.step&&l.setStep(+e.step)}),[e.step,l]),(0,o.useEffect)((function(){void 0!==e.disabled&&l.setDisabled(e.disabled)}),[e.disabled,l]),(0,o.useEffect)((function(){void 0!==e.discrete&&(l.isDiscrete_=e.discrete),e.discrete&&0===l.getStep()&&l.setStep(1)}),[e.discrete,l]),(0,o.useEffect)((function(){var t=l.hasTrackMarker_;void 0!==e.displayMarkers&&e.displayMarkers!==t&&(l.hasTrackMarker_=e.displayMarkers,window.requestAnimationFrame((function(){return l.setupTrackMarker()})))}),[e.displayMarkers,l]),(0,o.useEffect)((function(){var e=l.handleDown_.bind(l);l.handleDown_=function(t){t.persist(),e(t)}}),[l]),r({setTrackRef:function(e){return t.current=e},setTrackMarkerContainerRef:function(e){return n.current=e}},d)},F=o.memo(o.forwardRef((function(e,t){return o.createElement("div",{ref:t,className:"mdc-slider__track"})}))),K=o.memo(o.forwardRef((function(e,t){return o.createElement("div",{ref:t,className:"mdc-slider__track-marker-container"})}))),q=o.memo((function(e){var t=e.value;return o.createElement("div",{className:"mdc-slider__pin"},o.createElement("span",{className:"mdc-slider__pin-value-marker"},t))})),J=o.memo((function(){return o.createElement("svg",{className:"mdc-slider__thumb",width:"21",height:"21"},o.createElement("circle",{cx:"10.5",cy:"10.5",r:"7.875"}))})),G=o.memo((function(){return o.createElement("div",{className:"mdc-slider__focus-ring"})})),z=v((function(e,t){var n=Y(e),a=n.rootEl,s=n.thumbContainerEl,l=n.sliderPinEl,f=n.setTrackRef,m=n.setTrackMarkerContainerRef,v=e.value,y=(e.min,e.max),g=e.discrete,_=e.displayMarkers,E=e.step,b=e.disabled,x=(e.onChange,e.onInput,e.children),T=(e.foundationRef,i(e,["value","min","max","discrete","displayMarkers","step","disabled","onChange","onInput","children","foundationRef"])),M=function(e,t){return d().apply(void 0,c(c([e.className],u(e.theme?h(e.theme):[])),u("function"==typeof t?t(e):t)))}(e,["mdc-slider",{"mdc-slider--discrete":g,"mdc-slider--display-markers":_&&g}]),C=E?{"data-step":E}:{};return _&&!g&&console.warn("The 'displayMarkers' prop on rmwc Slider will\n        only work in conjunction with the 'discrete' prop"),o.createElement(p,r({tabIndex:0,role:"slider","aria-valuemax":y,"aria-valuenow":v,"aria-label":"Select Value"},b?{"aria-disabled":b}:{},C,T,{ref:t,element:a,className:M}),o.createElement("div",{className:"mdc-slider__track-container"},o.createElement(F,{ref:f}),_&&o.createElement(K,{ref:m})),o.createElement(p,{element:s,className:"mdc-slider__thumb-container"},g&&o.createElement(q,{value:l.getProp("value")}),o.createElement(J,null),o.createElement(G,null)),x)}))},85925:(e,t,n)=>{"use strict";n(54443)},43517:(e,t,n)=>{"use strict";function r(e,t=!1){let n=0;for(let t=0,r=e.length;t<r;t++){const i=e[t],o=e[t===r-1?0:t+1];n+=i[0]*o[1],n-=o[0]*i[1]}return t?n/2:Math.abs(n/2)}n.d(t,{m:()=>r})},5696:(e,t,n)=>{"use strict";function r(e){return Math.sqrt(Math.pow(e[1][0]-e[0][0],2)+Math.pow(e[1][1]-e[0][1],2))}function i(e,t,n=0){const i=r(t);return function(e,t,n=0){return Math.abs((r=e,i=t[0],o=t[1],(r[0]-o[0])*(i[1]-o[1])-(r[1]-o[1])*(i[0]-o[0])))<=n;var r,i,o}(e,t,n)&&r([t[0],e])<=i&&r([t[1],e])<=i}n.d(t,{s8:()=>i})},65661:(e,t,n)=>{"use strict";function r(e){return function(e){const t=e[0],n=e[e.length-1];return t[0]===n[0]&&t[1]===n[1]}(e)?e:[...e,e[0]]}n.d(t,{D:()=>u});var i=n(5696);function o(e,t){const[[n,r],[o,a]]=e,[[s,u],[c,l]]=t;if(n===s&&r===u)return!0;if(o===c&&a===l)return!0;if((0,i.s8)(e[0],t)||(0,i.s8)(e[1],t))return!0;if((0,i.s8)(t[0],e)||(0,i.s8)(t[1],e))return!0;const d=(l-u)*(o-n)-(c-s)*(a-r);if(0===d)return!1;const h=r-u,p=n-s,f=((c-s)*h-(l-u)*p)/d,m=((o-n)*h-(a-r)*p)/d;return f>0&&f<1&&m>0&&m<1}function a(e,t){let n=!1;const a=r(t);for(let t=0,r=a.length-1;t<r;t++){const r=a[t],s=a[t+1];if(o(e,[r,s])||(0,i.s8)(r,e)&&(0,i.s8)(s,e)){n=!0;break}}return n}function s(e,t){let n=e[0],r=e[1],i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){const a=t[e][0],s=t[e][1],u=t[o][0],c=t[o][1];s>r!=c>r&&n<(u-a)*(r-s)/(c-s)+a&&(i=!i)}return i}function u(e,t){let n=!0;const i=r(e);for(let e=0,r=i.length-1;e<r;e++){const r=i[e];if(!s(r,t)){n=!1;break}if(a([r,i[e+1]],t)){n=!1;break}}return n}},54443:(e,t,n)=>{"use strict";n.r(t)},17141:(e,t,n)=>{var r;!function(i,o){var a={};function s(e){return function(){var t={method:e},n=Array.prototype.slice.call(arguments);/^get/.test(e)?(a.assert(n.length>0,"Get methods require a callback."),n.unshift(t)):(/^set/.test(e)&&(a.assert(0!==n.length,"Set methods require a value."),t.value=n[0]),n=[t]),this.send.apply(this,n)}}a.DEBUG=!1,a.VERSION="0.0.11",a.CONTEXT="player.js",a.POST_MESSAGE=!!i.postMessage,a.origin=function(e){return"//"===e.substr(0,2)&&(e=i.location.protocol+e),e.split("/").slice(0,3).join("/")},a.addEvent=function(e,t,n){e&&(e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent?e.attachEvent("on"+t,n):e["on"+t]=n)},a.log=function(){a.log.history=a.log.history||[],a.log.history.push(arguments),i.console&&a.DEBUG&&i.console.log(Array.prototype.slice.call(arguments))},a.isString=function(e){return"[object String]"===Object.prototype.toString.call(e)},a.isObject=function(e){return"[object Object]"===Object.prototype.toString.call(e)},a.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},a.isNone=function(e){return null==e},a.has=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.indexOf=function(e,t){if(null==e)return-1;var n=0,r=e.length;if(Array.prototype.IndexOf&&e.indexOf===Array.prototype.IndexOf)return e.indexOf(t);for(;n<r;n++)if(e[n]===t)return n;return-1},a.assert=function(e,t){if(!e)throw t||"Player.js Assert Failed"},a.Keeper=function(){this.init()},a.Keeper.prototype.init=function(){this.data={}},a.Keeper.prototype.getUUID=function(){return"listener-xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},a.Keeper.prototype.has=function(e,t){if(!this.data.hasOwnProperty(e))return!1;if(a.isNone(t))return!0;for(var n=this.data[e],r=0;r<n.length;r++)if(n[r].id===t)return!0;return!1},a.Keeper.prototype.add=function(e,t,n,r,i){var o={id:e,event:t,cb:n,ctx:r,one:i};this.has(t)?this.data[t].push(o):this.data[t]=[o]},a.Keeper.prototype.execute=function(e,t,n,r){if(!this.has(e,t))return!1;for(var i=[],o=[],s=0;s<this.data[e].length;s++){var u=this.data[e][s];a.isNone(t)||!a.isNone(t)&&u.id===t?(o.push({cb:u.cb,ctx:u.ctx?u.ctx:r,data:n}),!1===u.one&&i.push(u)):i.push(u)}0===i.length?delete this.data[e]:this.data[e]=i;for(var c=0;c<o.length;c++){var l=o[c];l.cb.call(l.ctx,l.data)}},a.Keeper.prototype.on=function(e,t,n,r){this.add(e,t,n,r,!1)},a.Keeper.prototype.one=function(e,t,n,r){this.add(e,t,n,r,!0)},a.Keeper.prototype.off=function(e,t){var n=[];if(!this.data.hasOwnProperty(e))return n;for(var r=[],i=0;i<this.data[e].length;i++){var o=this.data[e][i];a.isNone(t)||o.cb===t?a.isNone(o.id)||n.push(o.id):r.push(o)}return 0===r.length?delete this.data[e]:this.data[e]=r,n},a.Player=function(e,t){if(!(this instanceof a.Player))return new a.Player(e,t);this.init(e,t)},a.EVENTS={READY:"ready",PLAY:"play",PAUSE:"pause",ENDED:"ended",TIMEUPDATE:"timeupdate",PROGRESS:"progress",ERROR:"error"},a.EVENTS.all=function(){var e=[];for(var t in a.EVENTS)a.has(a.EVENTS,t)&&a.isString(a.EVENTS[t])&&e.push(a.EVENTS[t]);return e},a.METHODS={PLAY:"play",PAUSE:"pause",GETPAUSED:"getPaused",MUTE:"mute",UNMUTE:"unmute",GETMUTED:"getMuted",SETVOLUME:"setVolume",GETVOLUME:"getVolume",GETDURATION:"getDuration",SETCURRENTTIME:"setCurrentTime",GETCURRENTTIME:"getCurrentTime",SETLOOP:"setLoop",GETLOOP:"getLoop",REMOVEEVENTLISTENER:"removeEventListener",ADDEVENTLISTENER:"addEventListener"},a.METHODS.all=function(){var e=[];for(var t in a.METHODS)a.has(a.METHODS,t)&&a.isString(a.METHODS[t])&&e.push(a.METHODS[t]);return e},a.READIED=[],a.Player.prototype.init=function(e,t){var n=this;a.isString(e)&&(e=o.getElementById(e)),this.elem=e,a.assert("IFRAME"===e.nodeName,'playerjs.Player constructor requires an Iframe, got "'+e.nodeName+'"'),a.assert(e.src,"playerjs.Player constructor requires a Iframe with a 'src' attribute."),this.origin=a.origin(e.src),this.keeper=new a.Keeper,this.isReady=!1,this.queue=[],this.events=a.EVENTS.all(),this.methods=a.METHODS.all(),a.POST_MESSAGE?a.addEvent(i,"message",(function(e){n.receive(e)})):a.log("Post Message is not Available."),a.indexOf(a.READIED,e.src)>-1?n.loaded=!0:this.elem.onload=function(){n.loaded=!0}},a.Player.prototype.send=function(e,t,n){if(e.context=a.CONTEXT,e.version=a.VERSION,t){var r=this.keeper.getUUID();e.listener=r,this.keeper.one(r,e.method,t,n)}return this.isReady||"ready"===e.value?(a.log("Player.send",e,this.origin),!0===this.loaded&&this.elem.contentWindow.postMessage(JSON.stringify(e),this.origin),!0):(a.log("Player.queue",e),this.queue.push(e),!1)},a.Player.prototype.receive=function(e){if(a.log("Player.receive",e),e.origin!==this.origin)return!1;var t;try{t=JSON.parse(e.data)}catch(e){return!1}if(t.context!==a.CONTEXT)return!1;"ready"===t.event&&t.value&&t.value.src===this.elem.src&&this.ready(t),this.keeper.has(t.event,t.listener)&&this.keeper.execute(t.event,t.listener,t.value,this)},a.Player.prototype.ready=function(e){if(!0===this.isReady)return!1;e.value.events&&(this.events=e.value.events),e.value.methods&&(this.methods=e.value.methods),this.isReady=!0,this.loaded=!0;for(var t=0;t<this.queue.length;t++){var n=this.queue[t];a.log("Player.dequeue",n),"ready"===e.event&&this.keeper.execute(n.event,n.listener,!0,this),this.send(n)}this.queue=[]},a.Player.prototype.on=function(e,t,n){var r=this.keeper.getUUID();return"ready"===e?this.keeper.one(r,e,t,n):this.keeper.on(r,e,t,n),this.send({method:"addEventListener",value:e,listener:r}),!0},a.Player.prototype.off=function(e,t){var n=this.keeper.off(e,t);if(a.log("Player.off",n),n.length>0)for(var r in n)return this.send({method:"removeEventListener",value:e,listener:n[r]}),!0;return!1},a.Player.prototype.supports=function(e,t){a.assert(a.indexOf(["method","event"],e)>-1,'evtOrMethod needs to be either "event" or "method" got '+e),t=a.isArray(t)?t:[t];for(var n="event"===e?this.events:this.methods,r=0;r<t.length;r++)if(-1===a.indexOf(n,t[r]))return!1;return!0};for(var u=0,c=a.METHODS.all().length;u<c;u++){var l=a.METHODS.all()[u];a.Player.prototype.hasOwnProperty(l)||(a.Player.prototype[l]=s(l))}a.addEvent(i,"message",(function(e){var t;try{t=JSON.parse(e.data)}catch(e){return!1}if(t.context!==a.CONTEXT)return!1;"ready"===t.event&&t.value&&t.value.src&&a.READIED.push(t.value.src)})),a.Receiver=function(e,t){this.init(e,t)},a.Receiver.prototype.init=function(e,t){var n=this;this.isReady=!1,this.origin=a.origin(o.referrer),this.methods={},this.supported={events:e||a.EVENTS.all(),methods:t||a.METHODS.all()},this.eventListeners={},this.reject=!(i.self!==i.top&&a.POST_MESSAGE),this.reject||a.addEvent(i,"message",(function(e){n.receive(e)}))},a.Receiver.prototype.receive=function(e){if(e.origin!==this.origin)return!1;var t={};if(a.isObject(e.data))t=e.data;else try{t=i.JSON.parse(e.data)}catch(e){a.log("JSON Parse Error",e)}if(a.log("Receiver.receive",e,t),!t.method)return!1;if(t.context!==a.CONTEXT)return!1;if(-1===a.indexOf(a.METHODS.all(),t.method))return this.emit("error",{code:2,msg:'Invalid Method "'+t.method+'"'}),!1;var n=a.isNone(t.listener)?null:t.listener;if("addEventListener"===t.method)this.eventListeners.hasOwnProperty(t.value)?-1===a.indexOf(this.eventListeners[t.value],n)&&this.eventListeners[t.value].push(n):this.eventListeners[t.value]=[n],"ready"===t.value&&this.isReady&&this.ready();else if("removeEventListener"===t.method){if(this.eventListeners.hasOwnProperty(t.value)){var r=a.indexOf(this.eventListeners[t.value],n);r>-1&&this.eventListeners[t.value].splice(r,1),0===this.eventListeners[t.value].length&&delete this.eventListeners[t.value]}}else this.get(t.method,t.value,n)},a.Receiver.prototype.get=function(e,t,n){var r=this;if(!this.methods.hasOwnProperty(e))return this.emit("error",{code:3,msg:'Method Not Supported"'+e+'"'}),!1;var i=this.methods[e];if("get"===e.substr(0,3)){i.call(this,(function(t){r.send(e,t,n)}))}else i.call(this,t)},a.Receiver.prototype.on=function(e,t){this.methods[e]=t},a.Receiver.prototype.send=function(e,t,n){if(a.log("Receiver.send",e,t,n),this.reject)return a.log("Receiver.send.reject",e,t,n),!1;var r={context:a.CONTEXT,version:a.VERSION,event:e};a.isNone(t)||(r.value=t),a.isNone(n)||(r.listener=n);var o=JSON.stringify(r);i.parent.postMessage(o,""===this.origin?"*":this.origin)},a.Receiver.prototype.emit=function(e,t){if(!this.eventListeners.hasOwnProperty(e))return!1;a.log("Instance.emit",e,t,this.eventListeners[e]);for(var n=0;n<this.eventListeners[e].length;n++){var r=this.eventListeners[e][n];this.send(e,t,r)}return!0},a.Receiver.prototype.ready=function(){a.log("Receiver.ready"),this.isReady=!0;var e={src:i.location.toString(),events:this.supported.events,methods:this.supported.methods};this.emit("ready",e)||this.send("ready",e)},a.HTML5Adapter=function(e){if(!(this instanceof a.HTML5Adapter))return new a.HTML5Adapter(e);this.init(e)},a.HTML5Adapter.prototype.init=function(e){a.assert(e,"playerjs.HTML5Adapter requires a video element");var t=this.receiver=new a.Receiver;e.addEventListener("playing",(function(){t.emit("play")})),e.addEventListener("pause",(function(){t.emit("pause")})),e.addEventListener("ended",(function(){t.emit("ended")})),e.addEventListener("timeupdate",(function(){t.emit("timeupdate",{seconds:e.currentTime,duration:e.duration})})),e.addEventListener("progress",(function(){t.emit("buffered",{percent:e.buffered.length})})),t.on("play",(function(){e.play()})),t.on("pause",(function(){e.pause()})),t.on("getPaused",(function(t){t(e.paused)})),t.on("getCurrentTime",(function(t){t(e.currentTime)})),t.on("setCurrentTime",(function(t){e.currentTime=t})),t.on("getDuration",(function(t){t(e.duration)})),t.on("getVolume",(function(t){t(100*e.volume)})),t.on("setVolume",(function(t){e.volume=t/100})),t.on("mute",(function(){e.muted=!0})),t.on("unmute",(function(){e.muted=!1})),t.on("getMuted",(function(t){t(e.muted)})),t.on("getLoop",(function(t){t(e.loop)})),t.on("setLoop",(function(t){e.loop=t}))},a.HTML5Adapter.prototype.ready=function(){this.receiver.ready()},a.JWPlayerAdapter=function(e){if(!(this instanceof a.JWPlayerAdapter))return new a.JWPlayerAdapter(e);this.init(e)},a.JWPlayerAdapter.prototype.init=function(e){a.assert(e,"playerjs.JWPlayerAdapter requires a player object");var t=this.receiver=new a.Receiver;this.looped=!1,e.on("pause",(function(){t.emit("pause")})),e.on("play",(function(){t.emit("play")})),e.on("time",(function(e){var n=e.position,r=e.duration;if(!n||!r)return!1;var i={seconds:n,duration:r};t.emit("timeupdate",i)}));var n=this;e.on("complete",(function(){!0===n.looped?e.seek(0):t.emit("ended")})),e.on("error",(function(){t.emit("error")})),t.on("play",(function(){e.play(!0)})),t.on("pause",(function(){e.pause(!0)})),t.on("getPaused",(function(t){t(e.getState().toLowerCase()!=="PLAYING".toLowerCase())})),t.on("getCurrentTime",(function(t){t(e.getPosition())})),t.on("setCurrentTime",(function(t){e.seek(t)})),t.on("getDuration",(function(t){t(e.getDuration())})),t.on("getVolume",(function(t){t(e.getVolume())})),t.on("setVolume",(function(t){e.setVolume(t)})),t.on("mute",(function(){e.setMute(!0)})),t.on("unmute",(function(){e.setMute(!1)})),t.on("getMuted",(function(t){t(!0===e.getMute())})),t.on("getLoop",(function(e){e(this.looped)}),this),t.on("setLoop",(function(e){this.looped=e}),this)},a.JWPlayerAdapter.prototype.ready=function(){this.receiver.ready()},a.MockAdapter=function(){if(!(this instanceof a.MockAdapter))return new a.MockAdapter;this.init()},a.MockAdapter.prototype.init=function(){var e={duration:20,currentTime:0,interval:null,timeupdate:function(){},volume:100,mute:!1,playing:!1,loop:!1,play:function(){e.interval=setInterval((function(){e.currentTime+=.25,e.timeupdate({seconds:e.currentTime,duration:e.duration})}),250),e.playing=!0},pause:function(){clearInterval(e.interval),e.playing=!1}},t=this.receiver=new a.Receiver;t.on("play",(function(){var t=this;e.play(),this.emit("play"),e.timeupdate=function(e){t.emit("timeupdate",e)}})),t.on("pause",(function(){e.pause(),this.emit("pause")})),t.on("getPaused",(function(t){t(!e.playing)})),t.on("getCurrentTime",(function(t){t(e.currentTime)})),t.on("setCurrentTime",(function(t){e.currentTime=t})),t.on("getDuration",(function(t){t(e.duration)})),t.on("getVolume",(function(t){t(e.volume)})),t.on("setVolume",(function(t){e.volume=t})),t.on("mute",(function(){e.mute=!0})),t.on("unmute",(function(){e.mute=!1})),t.on("getMuted",(function(t){t(e.mute)})),t.on("getLoop",(function(t){t(e.loop)})),t.on("setLoop",(function(t){e.loop=t}))},a.MockAdapter.prototype.ready=function(){this.receiver.ready()},a.VideoJSAdapter=function(e){if(!(this instanceof a.VideoJSAdapter))return new a.VideoJSAdapter(e);this.init(e)},a.VideoJSAdapter.prototype.init=function(e){a.assert(e,"playerjs.VideoJSReceiver requires a player object");var t=this.receiver=new a.Receiver;e.on("pause",(function(){t.emit("pause")})),e.on("play",(function(){t.emit("play")})),e.on("timeupdate",(function(n){var r=e.currentTime(),i=e.duration();if(!r||!i)return!1;var o={seconds:r,duration:i};t.emit("timeupdate",o)})),e.on("ended",(function(){t.emit("ended")})),e.on("error",(function(){t.emit("error")})),t.on("play",(function(){e.play()})),t.on("pause",(function(){e.pause()})),t.on("getPaused",(function(t){t(e.paused())})),t.on("getCurrentTime",(function(t){t(e.currentTime())})),t.on("setCurrentTime",(function(t){e.currentTime(t)})),t.on("getDuration",(function(t){t(e.duration())})),t.on("getVolume",(function(t){t(100*e.volume())})),t.on("setVolume",(function(t){e.volume(t/100)})),t.on("mute",(function(){e.volume(0)})),t.on("unmute",(function(){e.volume(1)})),t.on("getMuted",(function(t){t(0===e.volume())})),t.on("getLoop",(function(t){t(e.loop())})),t.on("setLoop",(function(t){e.loop(t)}))},a.VideoJSAdapter.prototype.ready=function(){this.receiver.ready()},void 0===(r=function(){return a}.call(t,n,t,e))||(e.exports=r)}(window,document)},4061:(e,t,n)=>{"use strict";var r=n(43842);function i(e,t,n){var i,s,u,c;t=t||1;for(var l=0;l<e[0].length;l++){var d=e[0][l];(!l||d[0]<i)&&(i=d[0]),(!l||d[1]<s)&&(s=d[1]),(!l||d[0]>u)&&(u=d[0]),(!l||d[1]>c)&&(c=d[1])}var h=u-i,p=c-s,f=Math.min(h,p),m=f/2;if(0===f){var v=[i,s];return v.distance=0,v}for(var y=new r(void 0,o),g=i;g<u;g+=f)for(var _=s;_<c;_+=f)y.push(new a(g+m,_+m,m,e));var E=function(e){for(var t=0,n=0,r=0,i=e[0],o=0,s=i.length,u=s-1;o<s;u=o++){var c=i[o],l=i[u],d=c[0]*l[1]-l[0]*c[1];n+=(c[0]+l[0])*d,r+=(c[1]+l[1])*d,t+=3*d}return 0===t?new a(i[0][0],i[0][1],0,e):new a(n/t,r/t,0,e)}(e),b=new a(i+h/2,s+p/2,0,e);b.d>E.d&&(E=b);for(var x=y.length;y.length;){var T=y.pop();T.d>E.d&&(E=T,n&&console.log("found best %d after %d probes",Math.round(1e4*T.d)/1e4,x)),T.max-E.d<=t||(m=T.h/2,y.push(new a(T.x-m,T.y-m,m,e)),y.push(new a(T.x+m,T.y-m,m,e)),y.push(new a(T.x-m,T.y+m,m,e)),y.push(new a(T.x+m,T.y+m,m,e)),x+=4)}n&&(console.log("num probes: "+x),console.log("best distance: "+E.d));var M=[E.x,E.y];return M.distance=E.d,M}function o(e,t){return t.max-e.max}function a(e,t,n,r){this.x=e,this.y=t,this.h=n,this.d=function(e,t,n){for(var r=!1,i=1/0,o=0;o<n.length;o++)for(var a=n[o],u=0,c=a.length,l=c-1;u<c;l=u++){var d=a[u],h=a[l];d[1]>t!=h[1]>t&&e<(h[0]-d[0])*(t-d[1])/(h[1]-d[1])+d[0]&&(r=!r),i=Math.min(i,s(e,t,d,h))}return 0===i?0:(r?1:-1)*Math.sqrt(i)}(e,t,r),this.max=this.d+this.h*Math.SQRT2}function s(e,t,n,r){var i=n[0],o=n[1],a=r[0]-i,s=r[1]-o;if(0!==a||0!==s){var u=((e-i)*a+(t-o)*s)/(a*a+s*s);u>1?(i=r[0],o=r[1]):u>0&&(i+=a*u,o+=s*u)}return(a=e-i)*a+(s=t-o)*s}r.default&&(r=r.default),e.exports=i,e.exports.default=i},82582:function(e){e.exports=function(){"use strict";function e(e,r,i,o,a){!function e(n,r,i,o,a){for(;o>i;){if(o-i>600){var s=o-i+1,u=r-i+1,c=Math.log(s),l=.5*Math.exp(2*c/3),d=.5*Math.sqrt(c*l*(s-l)/s)*(u-s/2<0?-1:1);e(n,r,Math.max(i,Math.floor(r-u*l/s+d)),Math.min(o,Math.floor(r+(s-u)*l/s+d)),a)}var h=n[r],p=i,f=o;for(t(n,i,r),a(n[o],h)>0&&t(n,i,o);p<f;){for(t(n,p,f),p++,f--;a(n[p],h)<0;)p++;for(;a(n[f],h)>0;)f--}0===a(n[i],h)?t(n,i,f):t(n,++f,o),f<=r&&(i=f+1),r<=f&&(o=f-1)}}(e,r,i||0,o||e.length-1,a||n)}function t(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function n(e,t){return e<t?-1:e>t?1:0}var r=function(e){void 0===e&&(e=9),this._maxEntries=Math.max(4,e),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function i(e,t,n){if(!n)return t.indexOf(e);for(var r=0;r<t.length;r++)if(n(e,t[r]))return r;return-1}function o(e,t){a(e,0,e.children.length,t,e)}function a(e,t,n,r,i){i||(i=f(null)),i.minX=1/0,i.minY=1/0,i.maxX=-1/0,i.maxY=-1/0;for(var o=t;o<n;o++){var a=e.children[o];s(i,e.leaf?r(a):a)}return i}function s(e,t){return e.minX=Math.min(e.minX,t.minX),e.minY=Math.min(e.minY,t.minY),e.maxX=Math.max(e.maxX,t.maxX),e.maxY=Math.max(e.maxY,t.maxY),e}function u(e,t){return e.minX-t.minX}function c(e,t){return e.minY-t.minY}function l(e){return(e.maxX-e.minX)*(e.maxY-e.minY)}function d(e){return e.maxX-e.minX+(e.maxY-e.minY)}function h(e,t){return e.minX<=t.minX&&e.minY<=t.minY&&t.maxX<=e.maxX&&t.maxY<=e.maxY}function p(e,t){return t.minX<=e.maxX&&t.minY<=e.maxY&&t.maxX>=e.minX&&t.maxY>=e.minY}function f(e){return{children:e,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function m(t,n,r,i,o){for(var a=[n,r];a.length;)if(!((r=a.pop())-(n=a.pop())<=i)){var s=n+Math.ceil((r-n)/i/2)*i;e(t,s,n,r,o),a.push(n,s,s,r)}}return r.prototype.all=function(){return this._all(this.data,[])},r.prototype.search=function(e){var t=this.data,n=[];if(!p(e,t))return n;for(var r=this.toBBox,i=[];t;){for(var o=0;o<t.children.length;o++){var a=t.children[o],s=t.leaf?r(a):a;p(e,s)&&(t.leaf?n.push(a):h(e,s)?this._all(a,n):i.push(a))}t=i.pop()}return n},r.prototype.collides=function(e){var t=this.data;if(!p(e,t))return!1;for(var n=[];t;){for(var r=0;r<t.children.length;r++){var i=t.children[r],o=t.leaf?this.toBBox(i):i;if(p(e,o)){if(t.leaf||h(e,o))return!0;n.push(i)}}t=n.pop()}return!1},r.prototype.load=function(e){if(!e||!e.length)return this;if(e.length<this._minEntries){for(var t=0;t<e.length;t++)this.insert(e[t]);return this}var n=this._build(e.slice(),0,e.length-1,0);if(this.data.children.length)if(this.data.height===n.height)this._splitRoot(this.data,n);else{if(this.data.height<n.height){var r=this.data;this.data=n,n=r}this._insert(n,this.data.height-n.height-1,!0)}else this.data=n;return this},r.prototype.insert=function(e){return e&&this._insert(e,this.data.height-1),this},r.prototype.clear=function(){return this.data=f([]),this},r.prototype.remove=function(e,t){if(!e)return this;for(var n,r,o,a=this.data,s=this.toBBox(e),u=[],c=[];a||u.length;){if(a||(a=u.pop(),r=u[u.length-1],n=c.pop(),o=!0),a.leaf){var l=i(e,a.children,t);if(-1!==l)return a.children.splice(l,1),u.push(a),this._condense(u),this}o||a.leaf||!h(a,s)?r?(n++,a=r.children[n],o=!1):a=null:(u.push(a),c.push(n),n=0,r=a,a=a.children[0])}return this},r.prototype.toBBox=function(e){return e},r.prototype.compareMinX=function(e,t){return e.minX-t.minX},r.prototype.compareMinY=function(e,t){return e.minY-t.minY},r.prototype.toJSON=function(){return this.data},r.prototype.fromJSON=function(e){return this.data=e,this},r.prototype._all=function(e,t){for(var n=[];e;)e.leaf?t.push.apply(t,e.children):n.push.apply(n,e.children),e=n.pop();return t},r.prototype._build=function(e,t,n,r){var i,a=n-t+1,s=this._maxEntries;if(a<=s)return o(i=f(e.slice(t,n+1)),this.toBBox),i;r||(r=Math.ceil(Math.log(a)/Math.log(s)),s=Math.ceil(a/Math.pow(s,r-1))),(i=f([])).leaf=!1,i.height=r;var u=Math.ceil(a/s),c=u*Math.ceil(Math.sqrt(s));m(e,t,n,c,this.compareMinX);for(var l=t;l<=n;l+=c){var d=Math.min(l+c-1,n);m(e,l,d,u,this.compareMinY);for(var h=l;h<=d;h+=u){var p=Math.min(h+u-1,d);i.children.push(this._build(e,h,p,r-1))}}return o(i,this.toBBox),i},r.prototype._chooseSubtree=function(e,t,n,r){for(;r.push(t),!t.leaf&&r.length-1!==n;){for(var i=1/0,o=1/0,a=void 0,s=0;s<t.children.length;s++){var u=t.children[s],c=l(u),d=(h=e,p=u,(Math.max(p.maxX,h.maxX)-Math.min(p.minX,h.minX))*(Math.max(p.maxY,h.maxY)-Math.min(p.minY,h.minY))-c);d<o?(o=d,i=c<i?c:i,a=u):d===o&&c<i&&(i=c,a=u)}t=a||t.children[0]}var h,p;return t},r.prototype._insert=function(e,t,n){var r=n?e:this.toBBox(e),i=[],o=this._chooseSubtree(r,this.data,t,i);for(o.children.push(e),s(o,r);t>=0&&i[t].children.length>this._maxEntries;)this._split(i,t),t--;this._adjustParentBBoxes(r,i,t)},r.prototype._split=function(e,t){var n=e[t],r=n.children.length,i=this._minEntries;this._chooseSplitAxis(n,i,r);var a=this._chooseSplitIndex(n,i,r),s=f(n.children.splice(a,n.children.length-a));s.height=n.height,s.leaf=n.leaf,o(n,this.toBBox),o(s,this.toBBox),t?e[t-1].children.push(s):this._splitRoot(n,s)},r.prototype._splitRoot=function(e,t){this.data=f([e,t]),this.data.height=e.height+1,this.data.leaf=!1,o(this.data,this.toBBox)},r.prototype._chooseSplitIndex=function(e,t,n){for(var r,i,o,s,u,c,d,h=1/0,p=1/0,f=t;f<=n-t;f++){var m=a(e,0,f,this.toBBox),v=a(e,f,n,this.toBBox),y=(i=m,o=v,s=void 0,u=void 0,c=void 0,d=void 0,s=Math.max(i.minX,o.minX),u=Math.max(i.minY,o.minY),c=Math.min(i.maxX,o.maxX),d=Math.min(i.maxY,o.maxY),Math.max(0,c-s)*Math.max(0,d-u)),g=l(m)+l(v);y<h?(h=y,r=f,p=g<p?g:p):y===h&&g<p&&(p=g,r=f)}return r||n-t},r.prototype._chooseSplitAxis=function(e,t,n){var r=e.leaf?this.compareMinX:u,i=e.leaf?this.compareMinY:c;this._allDistMargin(e,t,n,r)<this._allDistMargin(e,t,n,i)&&e.children.sort(r)},r.prototype._allDistMargin=function(e,t,n,r){e.children.sort(r);for(var i=this.toBBox,o=a(e,0,t,i),u=a(e,n-t,n,i),c=d(o)+d(u),l=t;l<n-t;l++){var h=e.children[l];s(o,e.leaf?i(h):h),c+=d(o)}for(var p=n-t-1;p>=t;p--){var f=e.children[p];s(u,e.leaf?i(f):f),c+=d(u)}return c},r.prototype._adjustParentBBoxes=function(e,t,n){for(var r=n;r>=0;r--)s(t[r],e)},r.prototype._condense=function(e){for(var t=e.length-1,n=void 0;t>=0;t--)0===e[t].children.length?t>0?(n=e[t-1].children).splice(n.indexOf(e[t]),1):this.clear():o(e[t],this.toBBox)},r}()},43842:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});class r{constructor(e=[],t=i){if(this.data=e,this.length=this.data.length,this.compare=t,this.length>0)for(let e=(this.length>>1)-1;e>=0;e--)this._down(e)}push(e){this.data.push(e),this.length++,this._up(this.length-1)}pop(){if(0===this.length)return;const e=this.data[0],t=this.data.pop();return this.length--,this.length>0&&(this.data[0]=t,this._down(0)),e}peek(){return this.data[0]}_up(e){const{data:t,compare:n}=this,r=t[e];for(;e>0;){const i=e-1>>1,o=t[i];if(n(r,o)>=0)break;t[e]=o,e=i}t[e]=r}_down(e){const{data:t,compare:n}=this,r=this.length>>1,i=t[e];for(;e<r;){let r=1+(e<<1),o=t[r];const a=r+1;if(a<this.length&&n(t[a],o)<0&&(r=a,o=t[a]),n(o,i)>=0)break;t[e]=o,e=r}t[e]=i}}function i(e,t){return e<t?-1:e>t?1:0}}}]);