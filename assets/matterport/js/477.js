/*! For license information please see 477.js.LICENSE.txt */
(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[477],{16870:(e,t)=>{"use strict";function r(e,t){return{validate:e,compare:t}}Object.defineProperty(t,"__esModule",{value:!0}),t.formatNames=t.fastFormats=t.fullFormats=void 0,t.fullFormats={date:r(o,n),time:r(c,d),"date-time":r((function(e){const t=e.split(u);return 2===t.length&&o(t[0])&&c(t[1],!0)}),l),duration:/^P(?!$)((\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?|(\d+W)?)$/,uri:function(e){return f.test(e)&&h.test(e)},"uri-reference":/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,"uri-template":/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,url:/^(?:https?|ftp):\/\/(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)(?:\.(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i,regex:function(e){if(y.test(e))return!1;try{return new RegExp(e),!0}catch(e){return!1}},uuid:/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,"json-pointer":/^(?:\/(?:[^~/]|~0|~1)*)*$/,"json-pointer-uri-fragment":/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,"relative-json-pointer":/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/,byte:function(e){return p.lastIndex=0,p.test(e)},int32:{type:"number",validate:function(e){return Number.isInteger(e)&&e<=2147483647&&e>=-2147483648}},int64:{type:"number",validate:function(e){return Number.isInteger(e)}},float:{type:"number",validate:m},double:{type:"number",validate:m},password:!0,binary:!0},t.fastFormats={...t.fullFormats,date:r(/^\d\d\d\d-[0-1]\d-[0-3]\d$/,n),time:r(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,d),"date-time":r(/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,l),uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i},t.formatNames=Object.keys(t.fullFormats);const a=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,s=[0,31,28,31,30,31,30,31,31,30,31,30,31];function o(e){const t=a.exec(e);if(!t)return!1;const r=+t[1],o=+t[2],n=+t[3];return o>=1&&o<=12&&n>=1&&n<=(2===o&&function(e){return e%4==0&&(e%100!=0||e%400==0)}(r)?29:s[o])}function n(e,t){if(e&&t)return e>t?1:e<t?-1:0}const i=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;function c(e,t){const r=i.exec(e);if(!r)return!1;const a=+r[1],s=+r[2],o=+r[3],n=r[5];return(a<=23&&s<=59&&o<=59||23===a&&59===s&&60===o)&&(!t||""!==n)}function d(e,t){if(!e||!t)return;const r=i.exec(e),a=i.exec(t);return r&&a?(e=r[1]+r[2]+r[3]+(r[4]||""))>(t=a[1]+a[2]+a[3]+(a[4]||""))?1:e<t?-1:0:void 0}const u=/t|\s/i;function l(e,t){if(!e||!t)return;const[r,a]=e.split(u),[s,o]=t.split(u),i=n(r,s);return void 0!==i?i||d(a,o):void 0}const f=/\/|:/,h=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;const p=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/gm;function m(){return!0}const y=/[^\\]\\Z/},5477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(16870),s=r(57963),o=r(93487),n=new o.Name("fullFormats"),i=new o.Name("fastFormats"),c=(e,t={keywords:!0})=>{if(Array.isArray(t))return d(e,t,a.fullFormats,n),e;const[r,o]="fast"===t.mode?[a.fastFormats,i]:[a.fullFormats,n];return d(e,t.formats||a.formatNames,r,o),t.keywords&&s.default(e),e};function d(e,t,r,a){var s,n;null!==(s=(n=e.opts.code).formats)&&void 0!==s||(n.formats=o._`require("ajv-formats/dist/formats").${a}`);for(const a of t)e.addFormat(a,r[a])}c.get=(e,t="full")=>{const r=("fast"===t?a.fastFormats:a.fullFormats)[e];if(!r)throw new Error(`Unknown format "${e}"`);return r},e.exports=t=c,Object.defineProperty(t,"__esModule",{value:!0}),t.default=c},57963:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatLimitDefinition=void 0;const a=r(1581),s=r(93487),o=s.operators,n={formatMaximum:{okStr:"<=",ok:o.LTE,fail:o.GT},formatMinimum:{okStr:">=",ok:o.GTE,fail:o.LT},formatExclusiveMaximum:{okStr:"<",ok:o.LT,fail:o.GTE},formatExclusiveMinimum:{okStr:">",ok:o.GT,fail:o.LTE}},i={message:({keyword:e,schemaCode:t})=>s.str`should be ${n[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>s._`{comparison: ${n[e].okStr}, limit: ${t}}`};t.formatLimitDefinition={keyword:Object.keys(n),type:"string",schemaType:"string",$data:!0,error:i,code(e){const{gen:t,data:r,schemaCode:o,keyword:i,it:c}=e,{opts:d,self:u}=c;if(!d.validateFormats)return;const l=new a.KeywordCxt(c,u.RULES.all.format.definition,"format");function f(e){return s._`${e}.compare(${r}, ${o}) ${n[i].fail} 0`}l.$data?function(){const r=t.scopeValue("formats",{ref:u.formats,code:d.code.formats}),a=t.const("fmt",s._`${r}[${l.schemaCode}]`);e.fail$data(s.or(s._`typeof ${a} != "object"`,s._`${a} instanceof RegExp`,s._`typeof ${a}.compare != "function"`,f(a)))}():function(){const r=l.schema,a=u.formats[r];if(!a||!0===a)return;if("object"!=typeof a||a instanceof RegExp||"function"!=typeof a.compare)throw new Error(`"${i}": format "${r}" does not define "compare" function`);const o=t.scopeValue("formats",{key:r,ref:a,code:d.code.formats?s._`${d.code.formats}${s.getProperty(r)}`:void 0});e.fail$data(f(o))}()},dependencies:["format"]};t.default=e=>(e.addKeyword(t.formatLimitDefinition),e)},1581:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;const a=r(27159),s=r(93924),o=r(1240),n=r(98),i=["/properties"],c="http://json-schema.org/draft-07/schema";class d extends a.default{_addVocabularies(){super._addVocabularies(),s.default.forEach((e=>this.addVocabulary(e))),this.opts.discriminator&&this.addKeyword(o.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const e=this.opts.$data?this.$dataMetaSchema(n,i):n;this.addMetaSchema(e,c,!1),this.refs["http://json-schema.org/schema"]=c}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(c)?c:void 0)}}e.exports=t=d,Object.defineProperty(t,"__esModule",{value:!0}),t.default=d;var u=r(74815);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return u.KeywordCxt}});var l=r(93487);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return l._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return l.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return l.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return l.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return l.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return l.CodeGen}});var f=r(67426);Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return f.default}});var h=r(6646);Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return h.default}})},57023:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.regexpCode=t.getEsmExportName=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r,t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class a extends r{constructor(e){if(super(),!t.IDENTIFIER.test(e))throw new Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}t.Name=a;class s extends r{constructor(e){super(),this._items="string"==typeof e?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const e=this._items[0];return""===e||'""'===e}get str(){var e;return null!==(e=this._str)&&void 0!==e?e:this._str=this._items.reduce(((e,t)=>`${e}${t}`),"")}get names(){var e;return null!==(e=this._names)&&void 0!==e?e:this._names=this._items.reduce(((e,t)=>(t instanceof a&&(e[t.str]=(e[t.str]||0)+1),e)),{})}}function o(e,...t){const r=[e[0]];let a=0;for(;a<t.length;)c(r,t[a]),r.push(e[++a]);return new s(r)}t._Code=s,t.nil=new s(""),t._=o;const n=new s("+");function i(e,...t){const r=[u(e[0])];let a=0;for(;a<t.length;)r.push(n),c(r,t[a]),r.push(n,u(e[++a]));return function(e){let t=1;for(;t<e.length-1;){if(e[t]===n){const r=d(e[t-1],e[t+1]);if(void 0!==r){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}(r),new s(r)}function c(e,t){var r;t instanceof s?e.push(...t._items):t instanceof a?e.push(t):e.push("number"==typeof(r=t)||"boolean"==typeof r||null===r?r:u(Array.isArray(r)?r.join(","):r))}function d(e,t){if('""'===t)return e;if('""'===e)return t;if("string"==typeof e){if(t instanceof a||'"'!==e[e.length-1])return;return"string"!=typeof t?`${e.slice(0,-1)}${t}"`:'"'===t[0]?e.slice(0,-1)+t.slice(1):void 0}return"string"!=typeof t||'"'!==t[0]||e instanceof a?void 0:`"${e}${t.slice(1)}`}function u(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.str=i,t.addCodeArg=c,t.strConcat=function(e,t){return t.emptyStr()?e:e.emptyStr()?t:i`${e}${t}`},t.stringify=function(e){return new s(u(e))},t.safeStringify=u,t.getProperty=function(e){return"string"==typeof e&&t.IDENTIFIER.test(e)?new s(`.${e}`):o`[${e}]`},t.getEsmExportName=function(e){if("string"==typeof e&&t.IDENTIFIER.test(e))return new s(`${e}`);throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)},t.regexpCode=function(e){return new s(e.toString())}},93487:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;const a=r(57023),s=r(98490);var o=r(57023);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return o._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return o.str}}),Object.defineProperty(t,"strConcat",{enumerable:!0,get:function(){return o.strConcat}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return o.nil}}),Object.defineProperty(t,"getProperty",{enumerable:!0,get:function(){return o.getProperty}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return o.stringify}}),Object.defineProperty(t,"regexpCode",{enumerable:!0,get:function(){return o.regexpCode}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return o.Name}});var n=r(98490);Object.defineProperty(t,"Scope",{enumerable:!0,get:function(){return n.Scope}}),Object.defineProperty(t,"ValueScope",{enumerable:!0,get:function(){return n.ValueScope}}),Object.defineProperty(t,"ValueScopeName",{enumerable:!0,get:function(){return n.ValueScopeName}}),Object.defineProperty(t,"varKinds",{enumerable:!0,get:function(){return n.varKinds}}),t.operators={GT:new a._Code(">"),GTE:new a._Code(">="),LT:new a._Code("<"),LTE:new a._Code("<="),EQ:new a._Code("==="),NEQ:new a._Code("!=="),NOT:new a._Code("!"),OR:new a._Code("||"),AND:new a._Code("&&"),ADD:new a._Code("+")};class i{optimizeNodes(){return this}optimizeNames(e,t){return this}}class c extends i{constructor(e,t,r){super(),this.varKind=e,this.name=t,this.rhs=r}render({es5:e,_n:t}){const r=e?s.varKinds.var:this.varKind,a=void 0===this.rhs?"":` = ${this.rhs}`;return`${r} ${this.name}${a};`+t}optimizeNames(e,t){if(e[this.name.str])return this.rhs&&(this.rhs=x(this.rhs,e,t)),this}get names(){return this.rhs instanceof a._CodeOrName?this.rhs.names:{}}}class d extends i{constructor(e,t,r){super(),this.lhs=e,this.rhs=t,this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(!(this.lhs instanceof a.Name)||e[this.lhs.str]||this.sideEffects)return this.rhs=x(this.rhs,e,t),this}get names(){return O(this.lhs instanceof a.Name?{}:{...this.lhs.names},this.rhs)}}class u extends d{constructor(e,t,r,a){super(e,r,a),this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class l extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`${this.label}:`+e}}class f extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`break${this.label?` ${this.label}`:""};`+e}}class h extends i{constructor(e){super(),this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class p extends i{constructor(e){super(),this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(e,t){return this.code=x(this.code,e,t),this}get names(){return this.code instanceof a._CodeOrName?this.code.names:{}}}class m extends i{constructor(e=[]){super(),this.nodes=e}render(e){return this.nodes.reduce(((t,r)=>t+r.render(e)),"")}optimizeNodes(){const{nodes:e}=this;let t=e.length;for(;t--;){const r=e[t].optimizeNodes();Array.isArray(r)?e.splice(t,1,...r):r?e[t]=r:e.splice(t,1)}return e.length>0?this:void 0}optimizeNames(e,t){const{nodes:r}=this;let a=r.length;for(;a--;){const s=r[a];s.optimizeNames(e,t)||(T(e,s.names),r.splice(a,1))}return r.length>0?this:void 0}get names(){return this.nodes.reduce(((e,t)=>C(e,t.names)),{})}}class y extends m{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class v extends m{}class g extends y{}g.kind="else";class $ extends y{constructor(e,t){super(t),this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);return this.else&&(t+="else "+this.else.render(e)),t}optimizeNodes(){super.optimizeNodes();const e=this.condition;if(!0===e)return this.nodes;let t=this.else;if(t){const e=t.optimizeNodes();t=this.else=Array.isArray(e)?new g(e):e}return t?!1===e?t instanceof $?t:t.nodes:this.nodes.length?this:new $(I(e),t instanceof $?[t]:t.nodes):!1!==e&&this.nodes.length?this:void 0}optimizeNames(e,t){var r;if(this.else=null===(r=this.else)||void 0===r?void 0:r.optimizeNames(e,t),super.optimizeNames(e,t)||this.else)return this.condition=x(this.condition,e,t),this}get names(){const e=super.names;return O(e,this.condition),this.else&&C(e,this.else.names),e}}$.kind="if";class _ extends y{}_.kind="for";class w extends _{constructor(e){super(),this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iteration=x(this.iteration,e,t),this}get names(){return C(super.names,this.iteration.names)}}class b extends _{constructor(e,t,r,a){super(),this.varKind=e,this.name=t,this.from=r,this.to=a}render(e){const t=e.es5?s.varKinds.var:this.varKind,{name:r,from:a,to:o}=this;return`for(${t} ${r}=${a}; ${r}<${o}; ${r}++)`+super.render(e)}get names(){const e=O(super.names,this.from);return O(e,this.to)}}class E extends _{constructor(e,t,r,a){super(),this.loop=e,this.varKind=t,this.name=r,this.iterable=a}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iterable=x(this.iterable,e,t),this}get names(){return C(super.names,this.iterable.names)}}class S extends y{constructor(e,t,r){super(),this.name=e,this.args=t,this.async=r}render(e){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(e)}}S.kind="func";class P extends m{render(e){return"return "+super.render(e)}}P.kind="return";class k extends y{render(e){let t="try"+super.render(e);return this.catch&&(t+=this.catch.render(e)),this.finally&&(t+=this.finally.render(e)),t}optimizeNodes(){var e,t;return super.optimizeNodes(),null===(e=this.catch)||void 0===e||e.optimizeNodes(),null===(t=this.finally)||void 0===t||t.optimizeNodes(),this}optimizeNames(e,t){var r,a;return super.optimizeNames(e,t),null===(r=this.catch)||void 0===r||r.optimizeNames(e,t),null===(a=this.finally)||void 0===a||a.optimizeNames(e,t),this}get names(){const e=super.names;return this.catch&&C(e,this.catch.names),this.finally&&C(e,this.finally.names),e}}class N extends y{constructor(e){super(),this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}N.kind="catch";class j extends y{render(e){return"finally"+super.render(e)}}j.kind="finally";function C(e,t){for(const r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function O(e,t){return t instanceof a._CodeOrName?C(e,t.names):e}function x(e,t,r){return e instanceof a.Name?o(e):(s=e)instanceof a._Code&&s._items.some((e=>e instanceof a.Name&&1===t[e.str]&&void 0!==r[e.str]))?new a._Code(e._items.reduce(((e,t)=>(t instanceof a.Name&&(t=o(t)),t instanceof a._Code?e.push(...t._items):e.push(t),e)),[])):e;var s;function o(e){const a=r[e.str];return void 0===a||1!==t[e.str]?e:(delete t[e.str],a)}}function T(e,t){for(const r in t)e[r]=(e[r]||0)-(t[r]||0)}function I(e){return"boolean"==typeof e||"number"==typeof e||null===e?!e:a._`!${M(e)}`}t.CodeGen=class{constructor(e,t={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...t,_n:t.lines?"\n":""},this._extScope=e,this._scope=new s.Scope({parent:e}),this._nodes=[new v]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){const r=this._extScope.value(e,t);return(this._values[r.prefix]||(this._values[r.prefix]=new Set)).add(r),r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,a){const s=this._scope.toName(t);return void 0!==r&&a&&(this._constants[s.str]=r),this._leafNode(new c(e,s,r)),s}const(e,t,r){return this._def(s.varKinds.const,e,t,r)}let(e,t,r){return this._def(s.varKinds.let,e,t,r)}var(e,t,r){return this._def(s.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new d(e,t,r))}add(e,r){return this._leafNode(new u(e,t.operators.ADD,r))}code(e){return"function"==typeof e?e():e!==a.nil&&this._leafNode(new p(e)),this}object(...e){const t=["{"];for(const[r,s]of e)t.length>1&&t.push(","),t.push(r),(r!==s||this.opts.es5)&&(t.push(":"),(0,a.addCodeArg)(t,s));return t.push("}"),new a._Code(t)}if(e,t,r){if(this._blockNode(new $(e)),t&&r)this.code(t).else().code(r).endIf();else if(t)this.code(t).endIf();else if(r)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(e){return this._elseNode(new $(e))}else(){return this._elseNode(new g)}endIf(){return this._endBlockNode($,g)}_for(e,t){return this._blockNode(e),t&&this.code(t).endFor(),this}for(e,t){return this._for(new w(e),t)}forRange(e,t,r,a,o=(this.opts.es5?s.varKinds.var:s.varKinds.let)){const n=this._scope.toName(e);return this._for(new b(o,n,t,r),(()=>a(n)))}forOf(e,t,r,o=s.varKinds.const){const n=this._scope.toName(e);if(this.opts.es5){const e=t instanceof a.Name?t:this.var("_arr",t);return this.forRange("_i",0,a._`${e}.length`,(t=>{this.var(n,a._`${e}[${t}]`),r(n)}))}return this._for(new E("of",o,n,t),(()=>r(n)))}forIn(e,t,r,o=(this.opts.es5?s.varKinds.var:s.varKinds.const)){if(this.opts.ownProperties)return this.forOf(e,a._`Object.keys(${t})`,r);const n=this._scope.toName(e);return this._for(new E("in",o,n,t),(()=>r(n)))}endFor(){return this._endBlockNode(_)}label(e){return this._leafNode(new l(e))}break(e){return this._leafNode(new f(e))}return(e){const t=new P;if(this._blockNode(t),this.code(e),1!==t.nodes.length)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(P)}try(e,t,r){if(!t&&!r)throw new Error('CodeGen: "try" without "catch" and "finally"');const a=new k;if(this._blockNode(a),this.code(e),t){const e=this.name("e");this._currNode=a.catch=new N(e),t(e)}return r&&(this._currNode=a.finally=new j,this.code(r)),this._endBlockNode(N,j)}throw(e){return this._leafNode(new h(e))}block(e,t){return this._blockStarts.push(this._nodes.length),e&&this.code(e).endBlock(t),this}endBlock(e){const t=this._blockStarts.pop();if(void 0===t)throw new Error("CodeGen: not in self-balancing block");const r=this._nodes.length-t;if(r<0||void 0!==e&&r!==e)throw new Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`);return this._nodes.length=t,this}func(e,t=a.nil,r,s){return this._blockNode(new S(e,t,r)),s&&this.code(s).endFunc(),this}endFunc(){return this._endBlockNode(S)}optimize(e=1){for(;e-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(e){return this._currNode.nodes.push(e),this}_blockNode(e){this._currNode.nodes.push(e),this._nodes.push(e)}_endBlockNode(e,t){const r=this._currNode;if(r instanceof e||t&&r instanceof t)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){const t=this._currNode;if(!(t instanceof $))throw new Error('CodeGen: "else" without "if"');return this._currNode=t.else=e,this}get _root(){return this._nodes[0]}get _currNode(){const e=this._nodes;return e[e.length-1]}set _currNode(e){const t=this._nodes;t[t.length-1]=e}},t.not=I;const R=A(t.operators.AND);t.and=function(...e){return e.reduce(R)};const D=A(t.operators.OR);function A(e){return(t,r)=>t===a.nil?r:r===a.nil?t:a._`${M(t)} ${e} ${M(r)}`}function M(e){return e instanceof a.Name?e:a._`(${e})`}t.or=function(...e){return e.reduce(D)}},98490:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;const a=r(57023);class s extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`),this.value=e.value}}var o;!function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"}(o=t.UsedValueState||(t.UsedValueState={})),t.varKinds={const:new a.Name("const"),let:new a.Name("let"),var:new a.Name("var")};class n{constructor({prefixes:e,parent:t}={}){this._names={},this._prefixes=e,this._parent=t}toName(e){return e instanceof a.Name?e:this.name(e)}name(e){return new a.Name(this._newName(e))}_newName(e){return`${e}${(this._names[e]||this._nameGroup(e)).index++}`}_nameGroup(e){var t,r;if((null===(r=null===(t=this._parent)||void 0===t?void 0:t._prefixes)||void 0===r?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e))throw new Error(`CodeGen: prefix "${e}" is not allowed in this scope`);return this._names[e]={prefix:e,index:0}}}t.Scope=n;class i extends a.Name{constructor(e,t){super(t),this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e,this.scopePath=a._`.${new a.Name(t)}[${r}]`}}t.ValueScopeName=i;const c=a._`\n`;t.ValueScope=class extends n{constructor(e){super(e),this._values={},this._scope=e.scope,this.opts={...e,_n:e.lines?c:a.nil}}get(){return this._scope}name(e){return new i(e,this._newName(e))}value(e,t){var r;if(void 0===t.ref)throw new Error("CodeGen: ref must be passed in value");const a=this.toName(e),{prefix:s}=a,o=null!==(r=t.key)&&void 0!==r?r:t.ref;let n=this._values[s];if(n){const e=n.get(o);if(e)return e}else n=this._values[s]=new Map;n.set(o,a);const i=this._scope[s]||(this._scope[s]=[]),c=i.length;return i[c]=t.ref,a.setValue(t,{property:s,itemIndex:c}),a}getValue(e,t){const r=this._values[e];if(r)return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,(t=>{if(void 0===t.scopePath)throw new Error(`CodeGen: name "${t}" has no value`);return a._`${e}${t.scopePath}`}))}scopeCode(e=this._values,t,r){return this._reduceValues(e,(e=>{if(void 0===e.value)throw new Error(`CodeGen: name "${e}" has no value`);return e.value.code}),t,r)}_reduceValues(e,r,n={},i){let c=a.nil;for(const d in e){const u=e[d];if(!u)continue;const l=n[d]=n[d]||new Map;u.forEach((e=>{if(l.has(e))return;l.set(e,o.Started);let n=r(e);if(n){const r=this.opts.es5?t.varKinds.var:t.varKinds.const;c=a._`${c}${r} ${e} = ${n};${this.opts._n}`}else{if(!(n=null==i?void 0:i(e)))throw new s(e);c=a._`${c}${n}${this.opts._n}`}l.set(e,o.Completed)}))}return c}}},4181:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0;const a=r(93487),s=r(76776),o=r(22141);function n(e,t){const r=e.const("err",t);e.if(a._`${o.default.vErrors} === null`,(()=>e.assign(o.default.vErrors,a._`[${r}]`)),a._`${o.default.vErrors}.push(${r})`),e.code(a._`${o.default.errors}++`)}function i(e,t){const{gen:r,validateName:s,schemaEnv:o}=e;o.$async?r.throw(a._`new ${e.ValidationError}(${t})`):(r.assign(a._`${s}.errors`,t),r.return(!1))}t.keywordError={message:({keyword:e})=>a.str`must pass "${e}" keyword validation`},t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?a.str`"${e}" keyword must be ${t} ($data)`:a.str`"${e}" keyword is invalid ($data)`},t.reportError=function(e,r=t.keywordError,s,o){const{it:c}=e,{gen:u,compositeRule:l,allErrors:f}=c,h=d(e,r,s);(null!=o?o:l||f)?n(u,h):i(c,a._`[${h}]`)},t.reportExtraError=function(e,r=t.keywordError,a){const{it:s}=e,{gen:c,compositeRule:u,allErrors:l}=s;n(c,d(e,r,a)),u||l||i(s,o.default.vErrors)},t.resetErrorsCount=function(e,t){e.assign(o.default.errors,t),e.if(a._`${o.default.vErrors} !== null`,(()=>e.if(t,(()=>e.assign(a._`${o.default.vErrors}.length`,t)),(()=>e.assign(o.default.vErrors,null)))))},t.extendErrors=function({gen:e,keyword:t,schemaValue:r,data:s,errsCount:n,it:i}){if(void 0===n)throw new Error("ajv implementation error");const c=e.name("err");e.forRange("i",n,o.default.errors,(n=>{e.const(c,a._`${o.default.vErrors}[${n}]`),e.if(a._`${c}.instancePath === undefined`,(()=>e.assign(a._`${c}.instancePath`,(0,a.strConcat)(o.default.instancePath,i.errorPath)))),e.assign(a._`${c}.schemaPath`,a.str`${i.errSchemaPath}/${t}`),i.opts.verbose&&(e.assign(a._`${c}.schema`,r),e.assign(a._`${c}.data`,s))}))};const c={keyword:new a.Name("keyword"),schemaPath:new a.Name("schemaPath"),params:new a.Name("params"),propertyName:new a.Name("propertyName"),message:new a.Name("message"),schema:new a.Name("schema"),parentSchema:new a.Name("parentSchema")};function d(e,t,r){const{createErrors:s}=e.it;return!1===s?a._`{}`:function(e,t,r={}){const{gen:s,it:n}=e,i=[u(n,r),l(e,r)];return function(e,{params:t,message:r},s){const{keyword:n,data:i,schemaValue:d,it:u}=e,{opts:l,propertyName:f,topSchemaRef:h,schemaPath:p}=u;s.push([c.keyword,n],[c.params,"function"==typeof t?t(e):t||a._`{}`]),l.messages&&s.push([c.message,"function"==typeof r?r(e):r]);l.verbose&&s.push([c.schema,d],[c.parentSchema,a._`${h}${p}`],[o.default.data,i]);f&&s.push([c.propertyName,f])}(e,t,i),s.object(...i)}(e,t,r)}function u({errorPath:e},{instancePath:t}){const r=t?a.str`${e}${(0,s.getErrorPath)(t,s.Type.Str)}`:e;return[o.default.instancePath,(0,a.strConcat)(o.default.instancePath,r)]}function l({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:o}){let n=o?t:a.str`${t}/${e}`;return r&&(n=a.str`${n}${(0,s.getErrorPath)(r,s.Type.Str)}`),[c.schemaPath,n]}},25173:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;const a=r(93487),s=r(67426),o=r(22141),n=r(32531),i=r(76776),c=r(74815);class d{constructor(e){var t;let r;this.refs={},this.dynamicAnchors={},"object"==typeof e.schema&&(r=e.schema),this.schema=e.schema,this.schemaId=e.schemaId,this.root=e.root||this,this.baseId=null!==(t=e.baseId)&&void 0!==t?t:(0,n.normalizeId)(null==r?void 0:r[e.schemaId||"$id"]),this.schemaPath=e.schemaPath,this.localRefs=e.localRefs,this.meta=e.meta,this.$async=null==r?void 0:r.$async,this.refs={}}}function u(e){const t=f.call(this,e);if(t)return t;const r=(0,n.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:i,lines:d}=this.opts.code,{ownProperties:u}=this.opts,l=new a.CodeGen(this.scope,{es5:i,lines:d,ownProperties:u});let h;e.$async&&(h=l.scopeValue("Error",{ref:s.default,code:a._`require("ajv/dist/runtime/validation_error").default`}));const p=l.scopeName("validate");e.validateName=p;const m={gen:l,allErrors:this.opts.allErrors,data:o.default.data,parentData:o.default.parentData,parentDataProperty:o.default.parentDataProperty,dataNames:[o.default.data],dataPathArr:[a.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:l.scopeValue("schema",!0===this.opts.code.source?{ref:e.schema,code:(0,a.stringify)(e.schema)}:{ref:e.schema}),validateName:p,ValidationError:h,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:a.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:a._`""`,opts:this.opts,self:this};let y;try{this._compilations.add(e),(0,c.validateFunctionCode)(m),l.optimize(this.opts.code.optimize);const t=l.toString();y=`${l.scopeRefs(o.default.scope)}return ${t}`,this.opts.code.process&&(y=this.opts.code.process(y,e));const r=new Function(`${o.default.self}`,`${o.default.scope}`,y)(this,this.scope.get());if(this.scope.value(p,{ref:r}),r.errors=null,r.schema=e.schema,r.schemaEnv=e,e.$async&&(r.$async=!0),!0===this.opts.code.source&&(r.source={validateName:p,validateCode:t,scopeValues:l._values}),this.opts.unevaluated){const{props:e,items:t}=m;r.evaluated={props:e instanceof a.Name?void 0:e,items:t instanceof a.Name?void 0:t,dynamicProps:e instanceof a.Name,dynamicItems:t instanceof a.Name},r.source&&(r.source.evaluated=(0,a.stringify)(r.evaluated))}return e.validate=r,e}catch(t){throw delete e.validate,delete e.validateName,y&&this.logger.error("Error compiling schema, function code:",y),t}finally{this._compilations.delete(e)}}function l(e){return(0,n.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:u.call(this,e)}function f(e){for(const a of this._compilations)if(r=e,(t=a).schema===r.schema&&t.root===r.root&&t.baseId===r.baseId)return a;var t,r}function h(e,t){let r;for(;"string"==typeof(r=this.refs[t]);)t=r;return r||this.schemas[t]||p.call(this,e,t)}function p(e,t){const r=this.opts.uriResolver.parse(t),a=(0,n._getFullPath)(this.opts.uriResolver,r);let s=(0,n.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&a===s)return y.call(this,r,e);const o=(0,n.normalizeId)(a),i=this.refs[o]||this.schemas[o];if("string"==typeof i){const t=p.call(this,e,i);if("object"!=typeof(null==t?void 0:t.schema))return;return y.call(this,r,t)}if("object"==typeof(null==i?void 0:i.schema)){if(i.validate||u.call(this,i),o===(0,n.normalizeId)(t)){const{schema:t}=i,{schemaId:r}=this.opts,a=t[r];return a&&(s=(0,n.resolveUrl)(this.opts.uriResolver,s,a)),new d({schema:t,schemaId:r,root:e,baseId:s})}return y.call(this,r,i)}}t.SchemaEnv=d,t.compileSchema=u,t.resolveRef=function(e,t,r){var a;r=(0,n.resolveUrl)(this.opts.uriResolver,t,r);const s=e.refs[r];if(s)return s;let o=h.call(this,e,r);if(void 0===o){const s=null===(a=e.localRefs)||void 0===a?void 0:a[r],{schemaId:n}=this.opts;s&&(o=new d({schema:s,schemaId:n,root:e,baseId:t}))}return void 0!==o?e.refs[r]=l.call(this,o):void 0},t.getCompilingSchema=f,t.resolveSchema=p;const m=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function y(e,{baseId:t,schema:r,root:a}){var s;if("/"!==(null===(s=e.fragment)||void 0===s?void 0:s[0]))return;for(const a of e.fragment.slice(1).split("/")){if("boolean"==typeof r)return;const e=r[(0,i.unescapeFragment)(a)];if(void 0===e)return;const s="object"==typeof(r=e)&&r[this.opts.schemaId];!m.has(a)&&s&&(t=(0,n.resolveUrl)(this.opts.uriResolver,t,s))}let o;if("boolean"!=typeof r&&r.$ref&&!(0,i.schemaHasRulesButRef)(r,this.RULES)){const e=(0,n.resolveUrl)(this.opts.uriResolver,t,r.$ref);o=p.call(this,a,e)}const{schemaId:c}=this.opts;return o=o||new d({schema:r,schemaId:c,root:a,baseId:t}),o.schema!==o.root.schema?o:void 0}},22141:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s={data:new a.Name("data"),valCxt:new a.Name("valCxt"),instancePath:new a.Name("instancePath"),parentData:new a.Name("parentData"),parentDataProperty:new a.Name("parentDataProperty"),rootData:new a.Name("rootData"),dynamicAnchors:new a.Name("dynamicAnchors"),vErrors:new a.Name("vErrors"),errors:new a.Name("errors"),this:new a.Name("this"),self:new a.Name("self"),scope:new a.Name("scope"),json:new a.Name("json"),jsonPos:new a.Name("jsonPos"),jsonLen:new a.Name("jsonLen"),jsonPart:new a.Name("jsonPart")};t.default=s},6646:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(32531);class s extends Error{constructor(e,t,r,s){super(s||`can't resolve reference ${r} from id ${t}`),this.missingRef=(0,a.resolveUrl)(e,t,r),this.missingSchema=(0,a.normalizeId)((0,a.getFullPath)(e,this.missingRef))}}t.default=s},32531:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;const a=r(76776),s=r(64063),o=r(49461),n=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);t.inlineRef=function(e,t=!0){return"boolean"==typeof e||(!0===t?!c(e):!!t&&d(e)<=t)};const i=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function c(e){for(const t in e){if(i.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(c))return!0;if("object"==typeof r&&c(r))return!0}return!1}function d(e){let t=0;for(const r in e){if("$ref"===r)return 1/0;if(t++,!n.has(r)&&("object"==typeof e[r]&&(0,a.eachItem)(e[r],(e=>t+=d(e))),t===1/0))return 1/0}return t}function u(e,t="",r){!1!==r&&(t=h(t));const a=e.parse(t);return l(e,a)}function l(e,t){return e.serialize(t).split("#")[0]+"#"}t.getFullPath=u,t._getFullPath=l;const f=/#\/?$/;function h(e){return e?e.replace(f,""):""}t.normalizeId=h,t.resolveUrl=function(e,t,r){return r=h(r),e.resolve(t,r)};const p=/^[a-z_][-a-z0-9._]*$/i;t.getSchemaRefs=function(e,t){if("boolean"==typeof e)return{};const{schemaId:r,uriResolver:a}=this.opts,n=h(e[r]||t),i={"":n},c=u(a,n,!1),d={},l=new Set;return o(e,{allKeys:!0},((e,t,a,s)=>{if(void 0===s)return;const o=c+t;let n=i[s];function u(t){const r=this.opts.uriResolver.resolve;if(t=h(n?r(n,t):t),l.has(t))throw m(t);l.add(t);let a=this.refs[t];return"string"==typeof a&&(a=this.refs[a]),"object"==typeof a?f(e,a.schema,t):t!==h(o)&&("#"===t[0]?(f(e,d[t],t),d[t]=e):this.refs[t]=o),t}function y(e){if("string"==typeof e){if(!p.test(e))throw new Error(`invalid anchor "${e}"`);u.call(this,`#${e}`)}}"string"==typeof e[r]&&(n=u.call(this,e[r])),y.call(this,e.$anchor),y.call(this,e.$dynamicAnchor),i[t]=n})),d;function f(e,t,r){if(void 0!==t&&!s(e,t))throw m(r)}function m(e){return new Error(`reference "${e}" resolves to more than one schema`)}}},13141:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRules=t.isJSONType=void 0;const r=new Set(["string","number","integer","boolean","null","object","array"]);t.isJSONType=function(e){return"string"==typeof e&&r.has(e)},t.getRules=function(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}},76776:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0;const a=r(93487),s=r(57023);function o(e,t=e.schema){const{opts:r,self:a}=e;if(!r.strictSchema)return;if("boolean"==typeof t)return;const s=a.RULES.keywords;for(const r in t)s[r]||p(e,`unknown keyword: "${r}"`)}function n(e,t){if("boolean"==typeof e)return!e;for(const r in e)if(t[r])return!0;return!1}function i(e){return"number"==typeof e?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}function c(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function d({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:s}){return(o,n,i,c)=>{const d=void 0===i?n:i instanceof a.Name?(n instanceof a.Name?e(o,n,i):t(o,n,i),i):n instanceof a.Name?(t(o,i,n),n):r(n,i);return c!==a.Name||d instanceof a.Name?d:s(o,d)}}function u(e,t){if(!0===t)return e.var("props",!0);const r=e.var("props",a._`{}`);return void 0!==t&&l(e,r,t),r}function l(e,t,r){Object.keys(r).forEach((r=>e.assign(a._`${t}${(0,a.getProperty)(r)}`,!0)))}t.toHash=function(e){const t={};for(const r of e)t[r]=!0;return t},t.alwaysValidSchema=function(e,t){return"boolean"==typeof t?t:0===Object.keys(t).length||(o(e,t),!n(t,e.self.RULES.all))},t.checkUnknownRules=o,t.schemaHasRules=n,t.schemaHasRulesButRef=function(e,t){if("boolean"==typeof e)return!e;for(const r in e)if("$ref"!==r&&t.all[r])return!0;return!1},t.schemaRefOrVal=function({topSchemaRef:e,schemaPath:t},r,s,o){if(!o){if("number"==typeof r||"boolean"==typeof r)return r;if("string"==typeof r)return a._`${r}`}return a._`${e}${t}${(0,a.getProperty)(s)}`},t.unescapeFragment=function(e){return c(decodeURIComponent(e))},t.escapeFragment=function(e){return encodeURIComponent(i(e))},t.escapeJsonPointer=i,t.unescapeJsonPointer=c,t.eachItem=function(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)},t.mergeEvaluated={props:d({mergeNames:(e,t,r)=>e.if(a._`${r} !== true && ${t} !== undefined`,(()=>{e.if(a._`${t} === true`,(()=>e.assign(r,!0)),(()=>e.assign(r,a._`${r} || {}`).code(a._`Object.assign(${r}, ${t})`)))})),mergeToName:(e,t,r)=>e.if(a._`${r} !== true`,(()=>{!0===t?e.assign(r,!0):(e.assign(r,a._`${r} || {}`),l(e,r,t))})),mergeValues:(e,t)=>!0===e||{...e,...t},resultToName:u}),items:d({mergeNames:(e,t,r)=>e.if(a._`${r} !== true && ${t} !== undefined`,(()=>e.assign(r,a._`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`))),mergeToName:(e,t,r)=>e.if(a._`${r} !== true`,(()=>e.assign(r,!0===t||a._`${r} > ${t} ? ${r} : ${t}`))),mergeValues:(e,t)=>!0===e||Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})},t.evaluatedPropsToName=u,t.setEvaluated=l;const f={};var h;function p(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,!0===r)throw new Error(t);e.self.logger.warn(t)}}t.useFunc=function(e,t){return e.scopeValue("func",{ref:t,code:f[t.code]||(f[t.code]=new s._Code(t.code))})},function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"}(h=t.Type||(t.Type={})),t.getErrorPath=function(e,t,r){if(e instanceof a.Name){const s=t===h.Num;return r?s?a._`"[" + ${e} + "]"`:a._`"['" + ${e} + "']"`:s?a._`"/" + ${e}`:a._`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,a.getProperty)(e).toString():"/"+i(e)},t.checkStrictMode=p},58876:(e,t)=>{"use strict";function r(e,t){return t.rules.some((t=>a(e,t)))}function a(e,t){var r;return void 0!==e[t.keyword]||(null===(r=t.definition.implements)||void 0===r?void 0:r.some((t=>void 0!==e[t])))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0,t.schemaHasRulesForType=function({schema:e,self:t},a){const s=t.RULES.types[a];return s&&!0!==s&&r(e,s)},t.shouldUseGroup=r,t.shouldUseRule=a},55667:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;const a=r(4181),s=r(93487),o=r(22141),n={message:"boolean schema is false"};function i(e,t){const{gen:r,data:s}=e,o={gen:r,keyword:"false schema",data:s,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,a.reportError)(o,n,void 0,t)}t.topBoolOrEmptySchema=function(e){const{gen:t,schema:r,validateName:a}=e;!1===r?i(e,!1):"object"==typeof r&&!0===r.$async?t.return(o.default.data):(t.assign(s._`${a}.errors`,null),t.return(!0))},t.boolOrEmptySchema=function(e,t){const{gen:r,schema:a}=e;!1===a?(r.var(t,!1),i(e)):r.var(t,!0)}},50453:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0;const a=r(13141),s=r(58876),o=r(4181),n=r(93487),i=r(76776);var c;function d(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(a.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}!function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"}(c=t.DataType||(t.DataType={})),t.getSchemaTypes=function(e){const t=d(e.type);if(t.includes("null")){if(!1===e.nullable)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&void 0!==e.nullable)throw new Error('"nullable" cannot be used without "type"');!0===e.nullable&&t.push("null")}return t},t.getJSONTypes=d,t.coerceAndCheckDataType=function(e,t){const{gen:r,data:a,opts:o}=e,i=function(e,t){return t?e.filter((e=>u.has(e)||"array"===t&&"array"===e)):[]}(t,o.coerceTypes),d=t.length>0&&!(0===i.length&&1===t.length&&(0,s.schemaHasRulesForType)(e,t[0]));if(d){const s=f(t,a,o.strictNumbers,c.Wrong);r.if(s,(()=>{i.length?function(e,t,r){const{gen:a,data:s,opts:o}=e,i=a.let("dataType",n._`typeof ${s}`),c=a.let("coerced",n._`undefined`);"array"===o.coerceTypes&&a.if(n._`${i} == 'object' && Array.isArray(${s}) && ${s}.length == 1`,(()=>a.assign(s,n._`${s}[0]`).assign(i,n._`typeof ${s}`).if(f(t,s,o.strictNumbers),(()=>a.assign(c,s)))));a.if(n._`${c} !== undefined`);for(const e of r)(u.has(e)||"array"===e&&"array"===o.coerceTypes)&&d(e);function d(e){switch(e){case"string":return void a.elseIf(n._`${i} == "number" || ${i} == "boolean"`).assign(c,n._`"" + ${s}`).elseIf(n._`${s} === null`).assign(c,n._`""`);case"number":return void a.elseIf(n._`${i} == "boolean" || ${s} === null
              || (${i} == "string" && ${s} && ${s} == +${s})`).assign(c,n._`+${s}`);case"integer":return void a.elseIf(n._`${i} === "boolean" || ${s} === null
              || (${i} === "string" && ${s} && ${s} == +${s} && !(${s} % 1))`).assign(c,n._`+${s}`);case"boolean":return void a.elseIf(n._`${s} === "false" || ${s} === 0 || ${s} === null`).assign(c,!1).elseIf(n._`${s} === "true" || ${s} === 1`).assign(c,!0);case"null":return a.elseIf(n._`${s} === "" || ${s} === 0 || ${s} === false`),void a.assign(c,null);case"array":a.elseIf(n._`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${s} === null`).assign(c,n._`[${s}]`)}}a.else(),p(e),a.endIf(),a.if(n._`${c} !== undefined`,(()=>{a.assign(s,c),function({gen:e,parentData:t,parentDataProperty:r},a){e.if(n._`${t} !== undefined`,(()=>e.assign(n._`${t}[${r}]`,a)))}(e,c)}))}(e,t,i):p(e)}))}return d};const u=new Set(["string","number","integer","boolean","null"]);function l(e,t,r,a=c.Correct){const s=a===c.Correct?n.operators.EQ:n.operators.NEQ;let o;switch(e){case"null":return n._`${t} ${s} null`;case"array":o=n._`Array.isArray(${t})`;break;case"object":o=n._`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":o=i(n._`!(${t} % 1) && !isNaN(${t})`);break;case"number":o=i();break;default:return n._`typeof ${t} ${s} ${e}`}return a===c.Correct?o:(0,n.not)(o);function i(e=n.nil){return(0,n.and)(n._`typeof ${t} == "number"`,e,r?n._`isFinite(${t})`:n.nil)}}function f(e,t,r,a){if(1===e.length)return l(e[0],t,r,a);let s;const o=(0,i.toHash)(e);if(o.array&&o.object){const e=n._`typeof ${t} != "object"`;s=o.null?e:n._`!${t} || ${e}`,delete o.null,delete o.array,delete o.object}else s=n.nil;o.number&&delete o.integer;for(const e in o)s=(0,n.and)(s,l(e,t,r,a));return s}t.checkDataType=l,t.checkDataTypes=f;const h={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>"string"==typeof e?n._`{type: ${e}}`:n._`{type: ${t}}`};function p(e){const t=function(e){const{gen:t,data:r,schema:a}=e,s=(0,i.schemaRefOrVal)(e,a,"type");return{gen:t,keyword:"type",data:r,schema:a.type,schemaCode:s,schemaValue:s,parentSchema:a,params:{},it:e}}(e);(0,o.reportError)(t,h)}t.reportTypeError=p},90313:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignDefaults=void 0;const a=r(93487),s=r(76776);function o(e,t,r){const{gen:o,compositeRule:n,data:i,opts:c}=e;if(void 0===r)return;const d=a._`${i}${(0,a.getProperty)(t)}`;if(n)return void(0,s.checkStrictMode)(e,`default is ignored for: ${d}`);let u=a._`${d} === undefined`;"empty"===c.useDefaults&&(u=a._`${u} || ${d} === null || ${d} === ""`),o.if(u,a._`${d} = ${(0,a.stringify)(r)}`)}t.assignDefaults=function(e,t){const{properties:r,items:a}=e.schema;if("object"===t&&r)for(const t in r)o(e,t,r[t].default);else"array"===t&&Array.isArray(a)&&a.forEach(((t,r)=>o(e,r,t.default)))}},74815:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;const a=r(55667),s=r(50453),o=r(58876),n=r(50453),i=r(90313),c=r(95005),d=r(13099),u=r(93487),l=r(22141),f=r(32531),h=r(76776),p=r(4181);function m({gen:e,validateName:t,schema:r,schemaEnv:a,opts:s},o){s.code.es5?e.func(t,u._`${l.default.data}, ${l.default.valCxt}`,a.$async,(()=>{e.code(u._`"use strict"; ${y(r,s)}`),function(e,t){e.if(l.default.valCxt,(()=>{e.var(l.default.instancePath,u._`${l.default.valCxt}.${l.default.instancePath}`),e.var(l.default.parentData,u._`${l.default.valCxt}.${l.default.parentData}`),e.var(l.default.parentDataProperty,u._`${l.default.valCxt}.${l.default.parentDataProperty}`),e.var(l.default.rootData,u._`${l.default.valCxt}.${l.default.rootData}`),t.dynamicRef&&e.var(l.default.dynamicAnchors,u._`${l.default.valCxt}.${l.default.dynamicAnchors}`)}),(()=>{e.var(l.default.instancePath,u._`""`),e.var(l.default.parentData,u._`undefined`),e.var(l.default.parentDataProperty,u._`undefined`),e.var(l.default.rootData,l.default.data),t.dynamicRef&&e.var(l.default.dynamicAnchors,u._`{}`)}))}(e,s),e.code(o)})):e.func(t,u._`${l.default.data}, ${function(e){return u._`{${l.default.instancePath}="", ${l.default.parentData}, ${l.default.parentDataProperty}, ${l.default.rootData}=${l.default.data}${e.dynamicRef?u._`, ${l.default.dynamicAnchors}={}`:u.nil}}={}`}(s)}`,a.$async,(()=>e.code(y(r,s)).code(o)))}function y(e,t){const r="object"==typeof e&&e[t.schemaId];return r&&(t.code.source||t.code.process)?u._`/*# sourceURL=${r} */`:u.nil}function v(e,t){$(e)&&(_(e),g(e))?function(e,t){const{schema:r,gen:a,opts:s}=e;s.$comment&&r.$comment&&b(e);(function(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,f.resolveUrl)(e.opts.uriResolver,e.baseId,t))})(e),function(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}(e);const o=a.const("_errs",l.default.errors);w(e,o),a.var(t,u._`${o} === ${l.default.errors}`)}(e,t):(0,a.boolOrEmptySchema)(e,t)}function g({schema:e,self:t}){if("boolean"==typeof e)return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function $(e){return"boolean"!=typeof e.schema}function _(e){(0,h.checkUnknownRules)(e),function(e){const{schema:t,errSchemaPath:r,opts:a,self:s}=e;t.$ref&&a.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(t,s.RULES)&&s.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}(e)}function w(e,t){if(e.opts.jtd)return E(e,[],!1,t);const r=(0,s.getSchemaTypes)(e.schema);E(e,r,!(0,s.coerceAndCheckDataType)(e,r),t)}function b({gen:e,schemaEnv:t,schema:r,errSchemaPath:a,opts:s}){const o=r.$comment;if(!0===s.$comment)e.code(u._`${l.default.self}.logger.log(${o})`);else if("function"==typeof s.$comment){const r=u.str`${a}/$comment`,s=e.scopeValue("root",{ref:t.root});e.code(u._`${l.default.self}.opts.$comment(${o}, ${r}, ${s}.schema)`)}}function E(e,t,r,a){const{gen:s,schema:i,data:c,allErrors:d,opts:f,self:p}=e,{RULES:m}=p;function y(h){(0,o.shouldUseGroup)(i,h)&&(h.type?(s.if((0,n.checkDataType)(h.type,c,f.strictNumbers)),S(e,h),1===t.length&&t[0]===h.type&&r&&(s.else(),(0,n.reportTypeError)(e)),s.endIf()):S(e,h),d||s.if(u._`${l.default.errors} === ${a||0}`))}!i.$ref||!f.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(i,m)?(f.jtd||function(e,t){if(e.schemaEnv.meta||!e.opts.strictTypes)return;(function(e,t){if(!t.length)return;if(!e.dataTypes.length)return void(e.dataTypes=t);t.forEach((t=>{k(e.dataTypes,t)||N(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)})),function(e,t){const r=[];for(const a of e.dataTypes)k(t,a)?r.push(a):t.includes("integer")&&"number"===a&&r.push("integer");e.dataTypes=r}(e,t)})(e,t),e.opts.allowUnionTypes||function(e,t){t.length>1&&(2!==t.length||!t.includes("null"))&&N(e,"use allowUnionTypes to allow union type keyword")}(e,t);!function(e,t){const r=e.self.RULES.all;for(const a in r){const s=r[a];if("object"==typeof s&&(0,o.shouldUseRule)(e.schema,s)){const{type:r}=s.definition;r.length&&!r.some((e=>P(t,e)))&&N(e,`missing type "${r.join(",")}" for keyword "${a}"`)}}}(e,e.dataTypes)}(e,t),s.block((()=>{for(const e of m.rules)y(e);y(m.post)}))):s.block((()=>C(e,"$ref",m.all.$ref.definition)))}function S(e,t){const{gen:r,schema:a,opts:{useDefaults:s}}=e;s&&(0,i.assignDefaults)(e,t.type),r.block((()=>{for(const r of t.rules)(0,o.shouldUseRule)(a,r)&&C(e,r.keyword,r.definition,t.type)}))}function P(e,t){return e.includes(t)||"number"===t&&e.includes("integer")}function k(e,t){return e.includes(t)||"integer"===t&&e.includes("number")}function N(e,t){t+=` at "${e.schemaEnv.baseId+e.errSchemaPath}" (strictTypes)`,(0,h.checkStrictMode)(e,t,e.opts.strictTypes)}t.validateFunctionCode=function(e){$(e)&&(_(e),g(e))?function(e){const{schema:t,opts:r,gen:a}=e;m(e,(()=>{r.$comment&&t.$comment&&b(e),function(e){const{schema:t,opts:r}=e;void 0!==t.default&&r.useDefaults&&r.strictSchema&&(0,h.checkStrictMode)(e,"default is ignored in the schema root")}(e),a.let(l.default.vErrors,null),a.let(l.default.errors,0),r.unevaluated&&function(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",u._`${r}.evaluated`),t.if(u._`${e.evaluated}.dynamicProps`,(()=>t.assign(u._`${e.evaluated}.props`,u._`undefined`))),t.if(u._`${e.evaluated}.dynamicItems`,(()=>t.assign(u._`${e.evaluated}.items`,u._`undefined`)))}(e),w(e),function(e){const{gen:t,schemaEnv:r,validateName:a,ValidationError:s,opts:o}=e;r.$async?t.if(u._`${l.default.errors} === 0`,(()=>t.return(l.default.data)),(()=>t.throw(u._`new ${s}(${l.default.vErrors})`))):(t.assign(u._`${a}.errors`,l.default.vErrors),o.unevaluated&&function({gen:e,evaluated:t,props:r,items:a}){r instanceof u.Name&&e.assign(u._`${t}.props`,r);a instanceof u.Name&&e.assign(u._`${t}.items`,a)}(e),t.return(u._`${l.default.errors} === 0`))}(e)}))}(e):m(e,(()=>(0,a.topBoolOrEmptySchema)(e)))};class j{constructor(e,t,r){if((0,c.validateKeywordUsage)(e,t,r),this.gen=e.gen,this.allErrors=e.allErrors,this.keyword=r,this.data=e.data,this.schema=e.schema[r],this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,h.schemaRefOrVal)(e,this.schema,r,this.$data),this.schemaType=t.schemaType,this.parentSchema=e.schema,this.params={},this.it=e,this.def=t,this.$data)this.schemaCode=e.gen.const("vSchema",T(this.$data,e));else if(this.schemaCode=this.schemaValue,!(0,c.validSchemaType)(this.schema,t.schemaType,t.allowUndefined))throw new Error(`${r} value must be ${JSON.stringify(t.schemaType)}`);("code"in t?t.trackErrors:!1!==t.errors)&&(this.errsCount=e.gen.const("_errs",l.default.errors))}result(e,t,r){this.failResult((0,u.not)(e),t,r)}failResult(e,t,r){this.gen.if(e),r?r():this.error(),t?(this.gen.else(),t(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(e,t){this.failResult((0,u.not)(e),void 0,t)}fail(e){if(void 0===e)return this.error(),void(this.allErrors||this.gen.if(!1));this.gen.if(e),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);const{schemaCode:t}=this;this.fail(u._`${t} !== undefined && (${(0,u.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t)return this.setParams(t),this._error(e,r),void this.setParams({});this._error(e,r)}_error(e,t){(e?p.reportExtraError:p.reportError)(this,this.def.error,t)}$dataError(){(0,p.reportError)(this,this.def.$dataError||p.keyword$DataError)}reset(){if(void 0===this.errsCount)throw new Error('add "trackErrors" to keyword definition');(0,p.resetErrorsCount)(this.gen,this.errsCount)}ok(e){this.allErrors||this.gen.if(e)}setParams(e,t){t?Object.assign(this.params,e):this.params=e}block$data(e,t,r=u.nil){this.gen.block((()=>{this.check$data(e,r),t()}))}check$data(e=u.nil,t=u.nil){if(!this.$data)return;const{gen:r,schemaCode:a,schemaType:s,def:o}=this;r.if((0,u.or)(u._`${a} === undefined`,t)),e!==u.nil&&r.assign(e,!0),(s.length||o.validateSchema)&&(r.elseIf(this.invalid$data()),this.$dataError(),e!==u.nil&&r.assign(e,!1)),r.else()}invalid$data(){const{gen:e,schemaCode:t,schemaType:r,def:a,it:s}=this;return(0,u.or)(function(){if(r.length){if(!(t instanceof u.Name))throw new Error("ajv implementation error");const e=Array.isArray(r)?r:[r];return u._`${(0,n.checkDataTypes)(e,t,s.opts.strictNumbers,n.DataType.Wrong)}`}return u.nil}(),function(){if(a.validateSchema){const r=e.scopeValue("validate$data",{ref:a.validateSchema});return u._`!${r}(${t})`}return u.nil}())}subschema(e,t){const r=(0,d.getSubschema)(this.it,e);(0,d.extendSubschemaData)(r,this.it,e),(0,d.extendSubschemaMode)(r,e);const a={...this.it,...r,items:void 0,props:void 0};return v(a,t),a}mergeEvaluated(e,t){const{it:r,gen:a}=this;r.opts.unevaluated&&(!0!==r.props&&void 0!==e.props&&(r.props=h.mergeEvaluated.props(a,e.props,r.props,t)),!0!==r.items&&void 0!==e.items&&(r.items=h.mergeEvaluated.items(a,e.items,r.items,t)))}mergeValidEvaluated(e,t){const{it:r,gen:a}=this;if(r.opts.unevaluated&&(!0!==r.props||!0!==r.items))return a.if(t,(()=>this.mergeEvaluated(e,u.Name))),!0}}function C(e,t,r,a){const s=new j(e,r,t);"code"in r?r.code(s,a):s.$data&&r.validate?(0,c.funcKeywordCode)(s,r):"macro"in r?(0,c.macroKeywordCode)(s,r):(r.compile||r.validate)&&(0,c.funcKeywordCode)(s,r)}t.KeywordCxt=j;const O=/^\/(?:[^~]|~0|~1)*$/,x=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function T(e,{dataLevel:t,dataNames:r,dataPathArr:a}){let s,o;if(""===e)return l.default.rootData;if("/"===e[0]){if(!O.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);s=e,o=l.default.rootData}else{const n=x.exec(e);if(!n)throw new Error(`Invalid JSON-pointer: ${e}`);const i=+n[1];if(s=n[2],"#"===s){if(i>=t)throw new Error(c("property/index",i));return a[t-i]}if(i>t)throw new Error(c("data",i));if(o=r[t-i],!s)return o}let n=o;const i=s.split("/");for(const e of i)e&&(o=u._`${o}${(0,u.getProperty)((0,h.unescapeJsonPointer)(e))}`,n=u._`${n} && ${o}`);return n;function c(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=T},95005:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0;const a=r(93487),s=r(22141),o=r(10412),n=r(4181);function i(e){const{gen:t,data:r,it:s}=e;t.if(s.parentData,(()=>t.assign(r,a._`${s.parentData}[${s.parentDataProperty}]`)))}function c(e,t,r){if(void 0===r)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword","function"==typeof r?{ref:r}:{ref:r,code:(0,a.stringify)(r)})}t.macroKeywordCode=function(e,t){const{gen:r,keyword:s,schema:o,parentSchema:n,it:i}=e,d=t.macro.call(i.self,o,n,i),u=c(r,s,d);!1!==i.opts.validateSchema&&i.self.validateSchema(d,!0);const l=r.name("valid");e.subschema({schema:d,schemaPath:a.nil,errSchemaPath:`${i.errSchemaPath}/${s}`,topSchemaRef:u,compositeRule:!0},l),e.pass(l,(()=>e.error(!0)))},t.funcKeywordCode=function(e,t){var r;const{gen:d,keyword:u,schema:l,parentSchema:f,$data:h,it:p}=e;!function({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}(p,t);const m=!h&&t.compile?t.compile.call(p.self,l,f,p):t.validate,y=c(d,u,m),v=d.let("valid");function g(r=(t.async?a._`await `:a.nil)){const n=p.opts.passContext?s.default.this:s.default.self,i=!("compile"in t&&!h||!1===t.schema);d.assign(v,a._`${r}${(0,o.callValidateCode)(e,y,n,i)}`,t.modifying)}function $(e){var r;d.if((0,a.not)(null!==(r=t.valid)&&void 0!==r?r:v),e)}e.block$data(v,(function(){if(!1===t.errors)g(),t.modifying&&i(e),$((()=>e.error()));else{const r=t.async?function(){const e=d.let("ruleErrs",null);return d.try((()=>g(a._`await `)),(t=>d.assign(v,!1).if(a._`${t} instanceof ${p.ValidationError}`,(()=>d.assign(e,a._`${t}.errors`)),(()=>d.throw(t))))),e}():function(){const e=a._`${y}.errors`;return d.assign(e,null),g(a.nil),e}();t.modifying&&i(e),$((()=>function(e,t){const{gen:r}=e;r.if(a._`Array.isArray(${t})`,(()=>{r.assign(s.default.vErrors,a._`${s.default.vErrors} === null ? ${t} : ${s.default.vErrors}.concat(${t})`).assign(s.default.errors,a._`${s.default.vErrors}.length`),(0,n.extendErrors)(e)}),(()=>e.error()))}(e,r)))}})),e.ok(null!==(r=t.valid)&&void 0!==r?r:v)},t.validSchemaType=function(e,t,r=!1){return!t.length||t.some((t=>"array"===t?Array.isArray(e):"object"===t?e&&"object"==typeof e&&!Array.isArray(e):typeof e==t||r&&void 0===e))},t.validateKeywordUsage=function({schema:e,opts:t,self:r,errSchemaPath:a},s,o){if(Array.isArray(s.keyword)?!s.keyword.includes(o):s.keyword!==o)throw new Error("ajv implementation error");const n=s.dependencies;if(null==n?void 0:n.some((t=>!Object.prototype.hasOwnProperty.call(e,t))))throw new Error(`parent schema must have dependencies of ${o}: ${n.join(",")}`);if(s.validateSchema){if(!s.validateSchema(e[o])){const e=`keyword "${o}" value is invalid at path "${a}": `+r.errorsText(s.validateSchema.errors);if("log"!==t.validateSchema)throw new Error(e);r.logger.error(e)}}}},13099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0;const a=r(93487),s=r(76776);t.getSubschema=function(e,{keyword:t,schemaProp:r,schema:o,schemaPath:n,errSchemaPath:i,topSchemaRef:c}){if(void 0!==t&&void 0!==o)throw new Error('both "keyword" and "schema" passed, only one allowed');if(void 0!==t){const o=e.schema[t];return void 0===r?{schema:o,schemaPath:a._`${e.schemaPath}${(0,a.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:o[r],schemaPath:a._`${e.schemaPath}${(0,a.getProperty)(t)}${(0,a.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,s.escapeFragment)(r)}`}}if(void 0!==o){if(void 0===n||void 0===i||void 0===c)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:o,schemaPath:n,topSchemaRef:c,errSchemaPath:i}}throw new Error('either "keyword" or "schema" must be passed')},t.extendSubschemaData=function(e,t,{dataProp:r,dataPropType:o,data:n,dataTypes:i,propertyName:c}){if(void 0!==n&&void 0!==r)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:d}=t;if(void 0!==r){const{errorPath:n,dataPathArr:i,opts:c}=t;u(d.let("data",a._`${t.data}${(0,a.getProperty)(r)}`,!0)),e.errorPath=a.str`${n}${(0,s.getErrorPath)(r,o,c.jsPropertySyntax)}`,e.parentDataProperty=a._`${r}`,e.dataPathArr=[...i,e.parentDataProperty]}if(void 0!==n){u(n instanceof a.Name?n:d.let("data",n,!0)),void 0!==c&&(e.propertyName=c)}function u(r){e.data=r,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,r]}i&&(e.dataTypes=i)},t.extendSubschemaMode=function(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:a,createErrors:s,allErrors:o}){void 0!==a&&(e.compositeRule=a),void 0!==s&&(e.createErrors=s),void 0!==o&&(e.allErrors=o),e.jtdDiscriminator=t,e.jtdMetadata=r}},27159:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;var a=r(74815);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return a.KeywordCxt}});var s=r(93487);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return s._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return s.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return s.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return s.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return s.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return s.CodeGen}});const o=r(67426),n=r(6646),i=r(13141),c=r(25173),d=r(93487),u=r(32531),l=r(50453),f=r(76776),h=r(64775),p=r(43589),m=(e,t)=>new RegExp(e,t);m.code="new RegExp";const y=["removeAdditional","useDefaults","coerceTypes"],v=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),g={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},$={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};function _(e){var t,r,a,s,o,n,i,c,d,u,l,f,h,y,v,g,$,_,w,b,E,S,P,k,N;const j=e.strict,C=null===(t=e.code)||void 0===t?void 0:t.optimize,O=!0===C||void 0===C?1:C||0,x=null!==(a=null===(r=e.code)||void 0===r?void 0:r.regExp)&&void 0!==a?a:m,T=null!==(s=e.uriResolver)&&void 0!==s?s:p.default;return{strictSchema:null===(n=null!==(o=e.strictSchema)&&void 0!==o?o:j)||void 0===n||n,strictNumbers:null===(c=null!==(i=e.strictNumbers)&&void 0!==i?i:j)||void 0===c||c,strictTypes:null!==(u=null!==(d=e.strictTypes)&&void 0!==d?d:j)&&void 0!==u?u:"log",strictTuples:null!==(f=null!==(l=e.strictTuples)&&void 0!==l?l:j)&&void 0!==f?f:"log",strictRequired:null!==(y=null!==(h=e.strictRequired)&&void 0!==h?h:j)&&void 0!==y&&y,code:e.code?{...e.code,optimize:O,regExp:x}:{optimize:O,regExp:x},loopRequired:null!==(v=e.loopRequired)&&void 0!==v?v:200,loopEnum:null!==(g=e.loopEnum)&&void 0!==g?g:200,meta:null===($=e.meta)||void 0===$||$,messages:null===(_=e.messages)||void 0===_||_,inlineRefs:null===(w=e.inlineRefs)||void 0===w||w,schemaId:null!==(b=e.schemaId)&&void 0!==b?b:"$id",addUsedSchema:null===(E=e.addUsedSchema)||void 0===E||E,validateSchema:null===(S=e.validateSchema)||void 0===S||S,validateFormats:null===(P=e.validateFormats)||void 0===P||P,unicodeRegExp:null===(k=e.unicodeRegExp)||void 0===k||k,int32range:null===(N=e.int32range)||void 0===N||N,uriResolver:T}}class w{constructor(e={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,e=this.opts={...e,..._(e)};const{es5:t,lines:r}=this.opts.code;this.scope=new d.ValueScope({scope:{},prefixes:v,es5:t,lines:r}),this.logger=function(e){if(!1===e)return j;if(void 0===e)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}(e.logger);const a=e.validateFormats;e.validateFormats=!1,this.RULES=(0,i.getRules)(),b.call(this,g,e,"NOT SUPPORTED"),b.call(this,$,e,"DEPRECATED","warn"),this._metaOpts=N.call(this),e.formats&&P.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),e.keywords&&k.call(this,e.keywords),"object"==typeof e.meta&&this.addMetaSchema(e.meta),S.call(this),e.validateFormats=a}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:e,meta:t,schemaId:r}=this.opts;let a=h;"id"===r&&(a={...h},a.id=a.$id,delete a.$id),t&&e&&this.addMetaSchema(a,a[r],!1)}defaultMeta(){const{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta="object"==typeof e?e[t]||e:void 0}validate(e,t){let r;if("string"==typeof e){if(r=this.getSchema(e),!r)throw new Error(`no schema with key or ref "${e}"`)}else r=this.compile(e);const a=r(t);return"$async"in r||(this.errors=r.errors),a}compile(e,t){const r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if("function"!=typeof this.opts.loadSchema)throw new Error("options.loadSchema should be a function");const{loadSchema:r}=this.opts;return a.call(this,e,t);async function a(e,t){await s.call(this,e.$schema);const r=this._addSchema(e,t);return r.validate||o.call(this,r)}async function s(e){e&&!this.getSchema(e)&&await a.call(this,{$ref:e},!0)}async function o(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof n.default))throw t;return i.call(this,t),await c.call(this,t.missingSchema),o.call(this,e)}}function i({missingSchema:e,missingRef:t}){if(this.refs[e])throw new Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}async function c(e){const r=await d.call(this,e);this.refs[e]||await s.call(this,r.$schema),this.refs[e]||this.addSchema(r,e,t)}async function d(e){const t=this._loading[e];if(t)return t;try{return await(this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,a=this.opts.validateSchema){if(Array.isArray(e)){for(const t of e)this.addSchema(t,void 0,r,a);return this}let s;if("object"==typeof e){const{schemaId:t}=this.opts;if(s=e[t],void 0!==s&&"string"!=typeof s)throw new Error(`schema ${t} must be string`)}return t=(0,u.normalizeId)(t||s),this._checkUnique(t),this.schemas[t]=this._addSchema(e,r,t,a,!0),this}addMetaSchema(e,t,r=this.opts.validateSchema){return this.addSchema(e,t,!0,r),this}validateSchema(e,t){if("boolean"==typeof e)return!0;let r;if(r=e.$schema,void 0!==r&&"string"!=typeof r)throw new Error("$schema must be a string");if(r=r||this.opts.defaultMeta||this.defaultMeta(),!r)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const a=this.validate(r,e);if(!a&&t){const e="schema is invalid: "+this.errorsText();if("log"!==this.opts.validateSchema)throw new Error(e);this.logger.error(e)}return a}getSchema(e){let t;for(;"string"==typeof(t=E.call(this,e));)e=t;if(void 0===t){const{schemaId:r}=this.opts,a=new c.SchemaEnv({schema:{},schemaId:r});if(t=c.resolveSchema.call(this,a,e),!t)return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp)return this._removeAllSchemas(this.schemas,e),this._removeAllSchemas(this.refs,e),this;switch(typeof e){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const t=E.call(this,e);return"object"==typeof t&&this._cache.delete(t.schema),delete this.schemas[e],delete this.refs[e],this}case"object":{const t=e;this._cache.delete(t);let r=e[this.opts.schemaId];return r&&(r=(0,u.normalizeId)(r),delete this.schemas[r],delete this.refs[r]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(const t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if("string"==typeof e)r=e,"object"==typeof t&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),t.keyword=r);else{if("object"!=typeof e||void 0!==t)throw new Error("invalid addKeywords parameters");if(r=(t=e).keyword,Array.isArray(r)&&!r.length)throw new Error("addKeywords: keyword must be string or non-empty array")}if(O.call(this,r,t),!t)return(0,f.eachItem)(r,(e=>x.call(this,e))),this;I.call(this,t);const a={...t,type:(0,l.getJSONTypes)(t.type),schemaType:(0,l.getJSONTypes)(t.schemaType)};return(0,f.eachItem)(r,0===a.type.length?e=>x.call(this,e,a):e=>a.type.forEach((t=>x.call(this,e,a,t)))),this}getKeyword(e){const t=this.RULES.all[e];return"object"==typeof t?t.definition:!!t}removeKeyword(e){const{RULES:t}=this;delete t.keywords[e],delete t.all[e];for(const r of t.rules){const t=r.rules.findIndex((t=>t.keyword===e));t>=0&&r.rules.splice(t,1)}return this}addFormat(e,t){return"string"==typeof t&&(t=new RegExp(t)),this.formats[e]=t,this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){return e&&0!==e.length?e.map((e=>`${r}${e.instancePath} ${e.message}`)).reduce(((e,r)=>e+t+r)):"No errors"}$dataMetaSchema(e,t){const r=this.RULES.all;e=JSON.parse(JSON.stringify(e));for(const a of t){const t=a.split("/").slice(1);let s=e;for(const e of t)s=s[e];for(const e in r){const t=r[e];if("object"!=typeof t)continue;const{$data:a}=t.definition,o=s[e];a&&o&&(s[e]=D(o))}}return e}_removeAllSchemas(e,t){for(const r in e){const a=e[r];t&&!t.test(r)||("string"==typeof a?delete e[r]:a&&!a.meta&&(this._cache.delete(a.schema),delete e[r]))}}_addSchema(e,t,r,a=this.opts.validateSchema,s=this.opts.addUsedSchema){let o;const{schemaId:n}=this.opts;if("object"==typeof e)o=e[n];else{if(this.opts.jtd)throw new Error("schema must be object");if("boolean"!=typeof e)throw new Error("schema must be object or boolean")}let i=this._cache.get(e);if(void 0!==i)return i;r=(0,u.normalizeId)(o||r);const d=u.getSchemaRefs.call(this,e,r);return i=new c.SchemaEnv({schema:e,schemaId:n,meta:t,baseId:r,localRefs:d}),this._cache.set(i.schema,i),s&&!r.startsWith("#")&&(r&&this._checkUnique(r),this.refs[r]=i),a&&this.validateSchema(e,!0),i}_checkUnique(e){if(this.schemas[e]||this.refs[e])throw new Error(`schema with key or id "${e}" already exists`)}_compileSchemaEnv(e){if(e.meta?this._compileMetaSchema(e):c.compileSchema.call(this,e),!e.validate)throw new Error("ajv implementation error");return e.validate}_compileMetaSchema(e){const t=this.opts;this.opts=this._metaOpts;try{c.compileSchema.call(this,e)}finally{this.opts=t}}}function b(e,t,r,a="error"){for(const s in e){const o=s;o in t&&this.logger[a](`${r}: option ${s}. ${e[o]}`)}}function E(e){return e=(0,u.normalizeId)(e),this.schemas[e]||this.refs[e]}function S(){const e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(const t in e)this.addSchema(e[t],t)}function P(){for(const e in this.opts.formats){const t=this.opts.formats[e];t&&this.addFormat(e,t)}}function k(e){if(Array.isArray(e))this.addVocabulary(e);else{this.logger.warn("keywords option as map is deprecated, pass array");for(const t in e){const r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}}function N(){const e={...this.opts};for(const t of y)delete e[t];return e}t.default=w,w.ValidationError=o.default,w.MissingRefError=n.default;const j={log(){},warn(){},error(){}};const C=/^[a-z_$][a-z0-9_$:-]*$/i;function O(e,t){const{RULES:r}=this;if((0,f.eachItem)(e,(e=>{if(r.keywords[e])throw new Error(`Keyword ${e} is already defined`);if(!C.test(e))throw new Error(`Keyword ${e} has invalid name`)})),t&&t.$data&&!("code"in t)&&!("validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function x(e,t,r){var a;const s=null==t?void 0:t.post;if(r&&s)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:o}=this;let n=s?o.post:o.rules.find((({type:e})=>e===r));if(n||(n={type:r,rules:[]},o.rules.push(n)),o.keywords[e]=!0,!t)return;const i={keyword:e,definition:{...t,type:(0,l.getJSONTypes)(t.type),schemaType:(0,l.getJSONTypes)(t.schemaType)}};t.before?T.call(this,n,i,t.before):n.rules.push(i),o.all[e]=i,null===(a=t.implements)||void 0===a||a.forEach((e=>this.addKeyword(e)))}function T(e,t,r){const a=e.rules.findIndex((e=>e.keyword===r));a>=0?e.rules.splice(a,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function I(e){let{metaSchema:t}=e;void 0!==t&&(e.$data&&this.opts.$data&&(t=D(t)),e.validateSchema=this.compile(t,!0))}const R={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function D(e){return{anyOf:[e,R]}}},43510:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(64063);a.code='require("ajv/dist/runtime/equal").default',t.default=a},74499:(e,t)=>{"use strict";function r(e){const t=e.length;let r,a=0,s=0;for(;s<t;)a++,r=e.charCodeAt(s++),r>=55296&&r<=56319&&s<t&&(r=e.charCodeAt(s),56320==(64512&r)&&s++);return a}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,r.code='require("ajv/dist/runtime/ucs2length").default'},43589:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(60540);a.code='require("ajv/dist/runtime/uri").default',t.default=a},67426:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super("validation failed"),this.errors=e,this.ajv=this.validation=!0}}t.default=r},4783:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateAdditionalItems=void 0;const a=r(93487),s=r(76776),o={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>a.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>a._`{limit: ${e}}`},code(e){const{parentSchema:t,it:r}=e,{items:a}=t;Array.isArray(a)?n(e,a):(0,s.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas')}};function n(e,t){const{gen:r,schema:o,data:n,keyword:i,it:c}=e;c.items=!0;const d=r.const("len",a._`${n}.length`);if(!1===o)e.setParams({len:t.length}),e.pass(a._`${d} <= ${t.length}`);else if("object"==typeof o&&!(0,s.alwaysValidSchema)(c,o)){const o=r.var("valid",a._`${d} <= ${t.length}`);r.if((0,a.not)(o),(()=>function(o){r.forRange("i",t.length,d,(t=>{e.subschema({keyword:i,dataProp:t,dataPropType:s.Type.Num},o),c.allErrors||r.if((0,a.not)(o),(()=>r.break()))}))}(o))),e.ok(o)}}t.validateAdditionalItems=n,t.default=o},69351:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(10412),s=r(93487),o=r(22141),n=r(76776),i={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>s._`{additionalProperty: ${e.additionalProperty}}`},code(e){const{gen:t,schema:r,parentSchema:i,data:c,errsCount:d,it:u}=e;if(!d)throw new Error("ajv implementation error");const{allErrors:l,opts:f}=u;if(u.props=!0,"all"!==f.removeAdditional&&(0,n.alwaysValidSchema)(u,r))return;const h=(0,a.allSchemaProperties)(i.properties),p=(0,a.allSchemaProperties)(i.patternProperties);function m(e){t.code(s._`delete ${c}[${e}]`)}function y(a){if("all"===f.removeAdditional||f.removeAdditional&&!1===r)m(a);else{if(!1===r)return e.setParams({additionalProperty:a}),e.error(),void(l||t.break());if("object"==typeof r&&!(0,n.alwaysValidSchema)(u,r)){const r=t.name("valid");"failing"===f.removeAdditional?(v(a,r,!1),t.if((0,s.not)(r),(()=>{e.reset(),m(a)}))):(v(a,r),l||t.if((0,s.not)(r),(()=>t.break())))}}}function v(t,r,a){const s={keyword:"additionalProperties",dataProp:t,dataPropType:n.Type.Str};!1===a&&Object.assign(s,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(s,r)}t.forIn("key",c,(r=>{h.length||p.length?t.if(function(r){let o;if(h.length>8){const e=(0,n.schemaRefOrVal)(u,i.properties,"properties");o=(0,a.isOwnProperty)(t,e,r)}else o=h.length?(0,s.or)(...h.map((e=>s._`${r} === ${e}`))):s.nil;return p.length&&(o=(0,s.or)(o,...p.map((t=>s._`${(0,a.usePattern)(e,t)}.test(${r})`)))),(0,s.not)(o)}(r),(()=>y(r))):y(r)})),e.ok(s._`${d} === ${o.default.errors}`)}};t.default=i},71125:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(76776),s={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const o=t.name("valid");r.forEach(((t,r)=>{if((0,a.alwaysValidSchema)(s,t))return;const n=e.subschema({keyword:"allOf",schemaProp:r},o);e.ok(o),e.mergeEvaluated(n)}))}};t.default=s},50019:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:r(10412).validateUnion,error:{message:"must match a schema in anyOf"}};t.default=a},79864:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(76776),o={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>void 0===t?a.str`must contain at least ${e} valid item(s)`:a.str`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>void 0===t?a._`{minContains: ${e}}`:a._`{minContains: ${e}, maxContains: ${t}}`},code(e){const{gen:t,schema:r,parentSchema:o,data:n,it:i}=e;let c,d;const{minContains:u,maxContains:l}=o;i.opts.next?(c=void 0===u?1:u,d=l):c=1;const f=t.const("len",a._`${n}.length`);if(e.setParams({min:c,max:d}),void 0===d&&0===c)return void(0,s.checkStrictMode)(i,'"minContains" == 0 without "maxContains": "contains" keyword ignored');if(void 0!==d&&c>d)return(0,s.checkStrictMode)(i,'"minContains" > "maxContains" is always invalid'),void e.fail();if((0,s.alwaysValidSchema)(i,r)){let t=a._`${f} >= ${c}`;return void 0!==d&&(t=a._`${t} && ${f} <= ${d}`),void e.pass(t)}i.items=!0;const h=t.name("valid");function p(){const e=t.name("_valid"),r=t.let("count",0);m(e,(()=>t.if(e,(()=>function(e){t.code(a._`${e}++`),void 0===d?t.if(a._`${e} >= ${c}`,(()=>t.assign(h,!0).break())):(t.if(a._`${e} > ${d}`,(()=>t.assign(h,!1).break())),1===c?t.assign(h,!0):t.if(a._`${e} >= ${c}`,(()=>t.assign(h,!0))))}(r)))))}function m(r,a){t.forRange("i",0,f,(t=>{e.subschema({keyword:"contains",dataProp:t,dataPropType:s.Type.Num,compositeRule:!0},r),a()}))}void 0===d&&1===c?m(h,(()=>t.if(h,(()=>t.break())))):0===c?(t.let(h,!0),void 0!==d&&t.if(a._`${n}.length > 0`,p)):(t.let(h,!1),p()),e.result(h,(()=>e.reset()))}};t.default=o},67772:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0;const a=r(93487),s=r(76776),o=r(10412);t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{const s=1===t?"property":"properties";return a.str`must have ${s} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:s}})=>a._`{property: ${e},
    missingProperty: ${s},
    depsCount: ${t},
    deps: ${r}}`};const n={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){const[t,r]=function({schema:e}){const t={},r={};for(const a in e){if("__proto__"===a)continue;(Array.isArray(e[a])?t:r)[a]=e[a]}return[t,r]}(e);i(e,t),c(e,r)}};function i(e,t=e.schema){const{gen:r,data:s,it:n}=e;if(0===Object.keys(t).length)return;const i=r.let("missing");for(const c in t){const d=t[c];if(0===d.length)continue;const u=(0,o.propertyInData)(r,s,c,n.opts.ownProperties);e.setParams({property:c,depsCount:d.length,deps:d.join(", ")}),n.allErrors?r.if(u,(()=>{for(const t of d)(0,o.checkReportMissingProp)(e,t)})):(r.if(a._`${u} && (${(0,o.checkMissingProp)(e,d,i)})`),(0,o.reportMissingProp)(e,i),r.else())}}function c(e,t=e.schema){const{gen:r,data:a,keyword:n,it:i}=e,c=r.name("valid");for(const d in t)(0,s.alwaysValidSchema)(i,t[d])||(r.if((0,o.propertyInData)(r,a,d,i.opts.ownProperties),(()=>{const t=e.subschema({keyword:n,schemaProp:d},c);e.mergeValidEvaluated(t,c)}),(()=>r.var(c,!0))),e.ok(c))}t.validatePropertyDeps=i,t.validateSchemaDeps=c,t.default=n},89434:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(76776),o={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>a.str`must match "${e.ifClause}" schema`,params:({params:e})=>a._`{failingKeyword: ${e.ifClause}}`},code(e){const{gen:t,parentSchema:r,it:o}=e;void 0===r.then&&void 0===r.else&&(0,s.checkStrictMode)(o,'"if" without "then" and "else" is ignored');const i=n(o,"then"),c=n(o,"else");if(!i&&!c)return;const d=t.let("valid",!0),u=t.name("_valid");if(function(){const t=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},u);e.mergeEvaluated(t)}(),e.reset(),i&&c){const r=t.let("ifClause");e.setParams({ifClause:r}),t.if(u,l("then",r),l("else",r))}else i?t.if(u,l("then")):t.if((0,a.not)(u),l("else"));function l(r,s){return()=>{const o=e.subschema({keyword:r},u);t.assign(d,u),e.mergeValidEvaluated(o,d),s?t.assign(s,a._`${r}`):e.setParams({ifClause:r})}}e.pass(d,(()=>e.error(!0)))}};function n(e,t){const r=e.schema[t];return void 0!==r&&!(0,s.alwaysValidSchema)(e,r)}t.default=o},8200:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(4783),s=r(72924),o=r(64665),n=r(1119),i=r(79864),c=r(67772),d=r(33708),u=r(69351),l=r(76239),f=r(12296),h=r(15697),p=r(50019),m=r(14200),y=r(71125),v=r(89434),g=r(66552);t.default=function(e=!1){const t=[h.default,p.default,m.default,y.default,v.default,g.default,d.default,u.default,c.default,l.default,f.default];return e?t.push(s.default,n.default):t.push(a.default,o.default),t.push(i.default),t}},64665:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateTuple=void 0;const a=r(93487),s=r(76776),o=r(10412),n={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return i(e,"additionalItems",t);r.items=!0,(0,s.alwaysValidSchema)(r,t)||e.ok((0,o.validateArray)(e))}};function i(e,t,r=e.schema){const{gen:o,parentSchema:n,data:i,keyword:c,it:d}=e;!function(e){const{opts:a,errSchemaPath:o}=d,n=r.length,i=n===e.minItems&&(n===e.maxItems||!1===e[t]);if(a.strictTuples&&!i){const e=`"${c}" is ${n}-tuple, but minItems or maxItems/${t} are not specified or different at path "${o}"`;(0,s.checkStrictMode)(d,e,a.strictTuples)}}(n),d.opts.unevaluated&&r.length&&!0!==d.items&&(d.items=s.mergeEvaluated.items(o,r.length,d.items));const u=o.name("valid"),l=o.const("len",a._`${i}.length`);r.forEach(((t,r)=>{(0,s.alwaysValidSchema)(d,t)||(o.if(a._`${l} > ${r}`,(()=>e.subschema({keyword:c,schemaProp:r,dataProp:r},u))),e.ok(u))}))}t.validateTuple=i,t.default=n},1119:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(76776),o=r(10412),n=r(4783),i={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>a.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>a._`{limit: ${e}}`},code(e){const{schema:t,parentSchema:r,it:a}=e,{prefixItems:i}=r;a.items=!0,(0,s.alwaysValidSchema)(a,t)||(i?(0,n.validateAdditionalItems)(e,i):e.ok((0,o.validateArray)(e)))}};t.default=i},15697:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(76776),s={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:s}=e;if((0,a.alwaysValidSchema)(s,r))return void e.fail();const o=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},o),e.failResult(o,(()=>e.reset()),(()=>e.error()))},error:{message:"must NOT be valid"}};t.default=s},14200:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(76776),o={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>a._`{passingSchemas: ${e.passing}}`},code(e){const{gen:t,schema:r,parentSchema:o,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(n.opts.discriminator&&o.discriminator)return;const i=r,c=t.let("valid",!1),d=t.let("passing",null),u=t.name("_valid");e.setParams({passing:d}),t.block((function(){i.forEach(((r,o)=>{let i;(0,s.alwaysValidSchema)(n,r)?t.var(u,!0):i=e.subschema({keyword:"oneOf",schemaProp:o,compositeRule:!0},u),o>0&&t.if(a._`${u} && ${c}`).assign(c,!1).assign(d,a._`[${d}, ${o}]`).else(),t.if(u,(()=>{t.assign(c,!0),t.assign(d,o),i&&e.mergeEvaluated(i,a.Name)}))}))})),e.result(c,(()=>e.reset()),(()=>e.error(!0)))}};t.default=o},12296:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(10412),s=r(93487),o=r(76776),n=r(76776),i={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:i,parentSchema:c,it:d}=e,{opts:u}=d,l=(0,a.allSchemaProperties)(r),f=l.filter((e=>(0,o.alwaysValidSchema)(d,r[e])));if(0===l.length||f.length===l.length&&(!d.opts.unevaluated||!0===d.props))return;const h=u.strictSchema&&!u.allowMatchingProperties&&c.properties,p=t.name("valid");!0===d.props||d.props instanceof s.Name||(d.props=(0,n.evaluatedPropsToName)(t,d.props));const{props:m}=d;function y(e){for(const t in h)new RegExp(e).test(t)&&(0,o.checkStrictMode)(d,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}function v(r){t.forIn("key",i,(o=>{t.if(s._`${(0,a.usePattern)(e,r)}.test(${o})`,(()=>{const a=f.includes(r);a||e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:o,dataPropType:n.Type.Str},p),d.opts.unevaluated&&!0!==m?t.assign(s._`${m}[${o}]`,!0):a||d.allErrors||t.if((0,s.not)(p),(()=>t.break()))}))}))}!function(){for(const e of l)h&&y(e),d.allErrors?v(e):(t.var(p,!0),v(e),t.if(p))}()}};t.default=i},72924:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(64665),s={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,a.validateTuple)(e,"items")};t.default=s},76239:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(74815),s=r(10412),o=r(76776),n=r(69351),i={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:i,data:c,it:d}=e;"all"===d.opts.removeAdditional&&void 0===i.additionalProperties&&n.default.code(new a.KeywordCxt(d,n.default,"additionalProperties"));const u=(0,s.allSchemaProperties)(r);for(const e of u)d.definedProperties.add(e);d.opts.unevaluated&&u.length&&!0!==d.props&&(d.props=o.mergeEvaluated.props(t,(0,o.toHash)(u),d.props));const l=u.filter((e=>!(0,o.alwaysValidSchema)(d,r[e])));if(0===l.length)return;const f=t.name("valid");for(const r of l)h(r)?p(r):(t.if((0,s.propertyInData)(t,c,r,d.opts.ownProperties)),p(r),d.allErrors||t.else().var(f,!0),t.endIf()),e.it.definedProperties.add(r),e.ok(f);function h(e){return d.opts.useDefaults&&!d.compositeRule&&void 0!==r[e].default}function p(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},f)}}};t.default=i},33708:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(76776),o={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>a._`{propertyName: ${e.propertyName}}`},code(e){const{gen:t,schema:r,data:o,it:n}=e;if((0,s.alwaysValidSchema)(n,r))return;const i=t.name("valid");t.forIn("key",o,(r=>{e.setParams({propertyName:r}),e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:!0},i),t.if((0,a.not)(i),(()=>{e.error(!0),n.allErrors||t.break()}))})),e.ok(i)}};t.default=o},66552:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(76776),s={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){void 0===t.if&&(0,a.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};t.default=s},10412:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;const a=r(93487),s=r(76776),o=r(22141),n=r(76776);function i(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:a._`Object.prototype.hasOwnProperty`})}function c(e,t,r){return a._`${i(e)}.call(${t}, ${r})`}function d(e,t,r,s){const o=a._`${t}${(0,a.getProperty)(r)} === undefined`;return s?(0,a.or)(o,(0,a.not)(c(e,t,r))):o}function u(e){return e?Object.keys(e).filter((e=>"__proto__"!==e)):[]}t.checkReportMissingProp=function(e,t){const{gen:r,data:s,it:o}=e;r.if(d(r,s,t,o.opts.ownProperties),(()=>{e.setParams({missingProperty:a._`${t}`},!0),e.error()}))},t.checkMissingProp=function({gen:e,data:t,it:{opts:r}},s,o){return(0,a.or)(...s.map((s=>(0,a.and)(d(e,t,s,r.ownProperties),a._`${o} = ${s}`))))},t.reportMissingProp=function(e,t){e.setParams({missingProperty:t},!0),e.error()},t.hasPropFunc=i,t.isOwnProperty=c,t.propertyInData=function(e,t,r,s){const o=a._`${t}${(0,a.getProperty)(r)} !== undefined`;return s?a._`${o} && ${c(e,t,r)}`:o},t.noPropertyInData=d,t.allSchemaProperties=u,t.schemaProperties=function(e,t){return u(t).filter((r=>!(0,s.alwaysValidSchema)(e,t[r])))},t.callValidateCode=function({schemaCode:e,data:t,it:{gen:r,topSchemaRef:s,schemaPath:n,errorPath:i},it:c},d,u,l){const f=l?a._`${e}, ${t}, ${s}${n}`:t,h=[[o.default.instancePath,(0,a.strConcat)(o.default.instancePath,i)],[o.default.parentData,c.parentData],[o.default.parentDataProperty,c.parentDataProperty],[o.default.rootData,o.default.rootData]];c.opts.dynamicRef&&h.push([o.default.dynamicAnchors,o.default.dynamicAnchors]);const p=a._`${f}, ${r.object(...h)}`;return u!==a.nil?a._`${d}.call(${u}, ${p})`:a._`${d}(${p})`};const l=a._`new RegExp`;t.usePattern=function({gen:e,it:{opts:t}},r){const s=t.unicodeRegExp?"u":"",{regExp:o}=t.code,i=o(r,s);return e.scopeValue("pattern",{key:i.toString(),ref:i,code:a._`${"new RegExp"===o.code?l:(0,n.useFunc)(e,o)}(${r}, ${s})`})},t.validateArray=function(e){const{gen:t,data:r,keyword:o,it:n}=e,i=t.name("valid");if(n.allErrors){const e=t.let("valid",!0);return c((()=>t.assign(e,!1))),e}return t.var(i,!0),c((()=>t.break())),i;function c(n){const c=t.const("len",a._`${r}.length`);t.forRange("i",0,c,(r=>{e.subschema({keyword:o,dataProp:r,dataPropType:s.Type.Num},i),t.if((0,a.not)(i),n)}))}},t.validateUnion=function(e){const{gen:t,schema:r,keyword:o,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some((e=>(0,s.alwaysValidSchema)(n,e)))&&!n.opts.unevaluated)return;const i=t.let("valid",!1),c=t.name("_valid");t.block((()=>r.forEach(((r,s)=>{const n=e.subschema({keyword:o,schemaProp:s,compositeRule:!0},c);t.assign(i,a._`${i} || ${c}`);e.mergeValidEvaluated(n,c)||t.if((0,a.not)(i))})))),e.result(i,(()=>e.reset()),(()=>e.error(!0)))}},78386:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};t.default=r},95684:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(78386),s=r(28280),o=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",a.default,s.default];t.default=o},28280:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.callRef=t.getValidate=void 0;const a=r(6646),s=r(10412),o=r(93487),n=r(22141),i=r(25173),c=r(76776),d={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:s}=e,{baseId:n,schemaEnv:c,validateName:d,opts:f,self:h}=s,{root:p}=c;if(("#"===r||"#/"===r)&&n===p.baseId)return function(){if(c===p)return l(e,d,c,c.$async);const r=t.scopeValue("root",{ref:p});return l(e,o._`${r}.validate`,p,p.$async)}();const m=i.resolveRef.call(h,p,n,r);if(void 0===m)throw new a.default(s.opts.uriResolver,n,r);return m instanceof i.SchemaEnv?function(t){const r=u(e,t);l(e,r,t,t.$async)}(m):function(a){const s=t.scopeValue("schema",!0===f.code.source?{ref:a,code:(0,o.stringify)(a)}:{ref:a}),n=t.name("valid"),i=e.subschema({schema:a,dataTypes:[],schemaPath:o.nil,topSchemaRef:s,errSchemaPath:r},n);e.mergeEvaluated(i),e.ok(n)}(m)}};function u(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):o._`${r.scopeValue("wrapper",{ref:t})}.validate`}function l(e,t,r,a){const{gen:i,it:d}=e,{allErrors:u,schemaEnv:l,opts:f}=d,h=f.passContext?n.default.this:o.nil;function p(e){const t=o._`${e}.errors`;i.assign(n.default.vErrors,o._`${n.default.vErrors} === null ? ${t} : ${n.default.vErrors}.concat(${t})`),i.assign(n.default.errors,o._`${n.default.vErrors}.length`)}function m(e){var t;if(!d.opts.unevaluated)return;const a=null===(t=null==r?void 0:r.validate)||void 0===t?void 0:t.evaluated;if(!0!==d.props)if(a&&!a.dynamicProps)void 0!==a.props&&(d.props=c.mergeEvaluated.props(i,a.props,d.props));else{const t=i.var("props",o._`${e}.evaluated.props`);d.props=c.mergeEvaluated.props(i,t,d.props,o.Name)}if(!0!==d.items)if(a&&!a.dynamicItems)void 0!==a.items&&(d.items=c.mergeEvaluated.items(i,a.items,d.items));else{const t=i.var("items",o._`${e}.evaluated.items`);d.items=c.mergeEvaluated.items(i,t,d.items,o.Name)}}a?function(){if(!l.$async)throw new Error("async schema referenced by sync schema");const r=i.let("valid");i.try((()=>{i.code(o._`await ${(0,s.callValidateCode)(e,t,h)}`),m(t),u||i.assign(r,!0)}),(e=>{i.if(o._`!(${e} instanceof ${d.ValidationError})`,(()=>i.throw(e))),p(e),u||i.assign(r,!1)})),e.ok(r)}():e.result((0,s.callValidateCode)(e,t,h),(()=>m(t)),(()=>p(t)))}t.getValidate=u,t.callRef=l,t.default=d},1240:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(89306),o=r(25173),n=r(76776),i={keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===s.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>a._`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){const{gen:t,data:r,schema:i,parentSchema:c,it:d}=e,{oneOf:u}=c;if(!d.opts.discriminator)throw new Error("discriminator: requires discriminator option");const l=i.propertyName;if("string"!=typeof l)throw new Error("discriminator: requires propertyName");if(i.mapping)throw new Error("discriminator: mapping is not supported");if(!u)throw new Error("discriminator: requires oneOf keyword");const f=t.let("valid",!1),h=t.const("tag",a._`${r}${(0,a.getProperty)(l)}`);function p(r){const s=t.name("valid"),o=e.subschema({keyword:"oneOf",schemaProp:r},s);return e.mergeEvaluated(o,a.Name),s}t.if(a._`typeof ${h} == "string"`,(()=>function(){const r=function(){var e;const t={},r=s(c);let a=!0;for(let t=0;t<u.length;t++){let c=u[t];(null==c?void 0:c.$ref)&&!(0,n.schemaHasRulesButRef)(c,d.self.RULES)&&(c=o.resolveRef.call(d.self,d.schemaEnv.root,d.baseId,null==c?void 0:c.$ref),c instanceof o.SchemaEnv&&(c=c.schema));const f=null===(e=null==c?void 0:c.properties)||void 0===e?void 0:e[l];if("object"!=typeof f)throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${l}"`);a=a&&(r||s(c)),i(f,t)}if(!a)throw new Error(`discriminator: "${l}" must be required`);return t;function s({required:e}){return Array.isArray(e)&&e.includes(l)}function i(e,t){if(e.const)f(e.const,t);else{if(!e.enum)throw new Error(`discriminator: "properties/${l}" must have "const" or "enum"`);for(const r of e.enum)f(r,t)}}function f(e,r){if("string"!=typeof e||e in t)throw new Error(`discriminator: "${l}" values must be unique strings`);t[e]=r}}();t.if(!1);for(const e in r)t.elseIf(a._`${h} === ${e}`),t.assign(f,p(r[e]));t.else(),e.error(!1,{discrError:s.DiscrError.Mapping,tag:h,tagName:l}),t.endIf()}()),(()=>e.error(!1,{discrError:s.DiscrError.Tag,tag:h,tagName:l}))),e.ok(f)}};t.default=i},89306:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DiscrError=void 0,function(e){e.Tag="tag",e.Mapping="mapping"}(t.DiscrError||(t.DiscrError={}))},93924:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(95684),s=r(62649),o=r(8200),n=r(39502),i=r(66167),c=[a.default,s.default,(0,o.default)(),n.default,i.metadataVocabulary,i.contentVocabulary];t.default=c},89651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>a.str`must match format "${e}"`,params:({schemaCode:e})=>a._`{format: ${e}}`},code(e,t){const{gen:r,data:s,$data:o,schema:n,schemaCode:i,it:c}=e,{opts:d,errSchemaPath:u,schemaEnv:l,self:f}=c;d.validateFormats&&(o?function(){const o=r.scopeValue("formats",{ref:f.formats,code:d.code.formats}),n=r.const("fDef",a._`${o}[${i}]`),c=r.let("fType"),u=r.let("format");r.if(a._`typeof ${n} == "object" && !(${n} instanceof RegExp)`,(()=>r.assign(c,a._`${n}.type || "string"`).assign(u,a._`${n}.validate`)),(()=>r.assign(c,a._`"string"`).assign(u,n))),e.fail$data((0,a.or)(!1===d.strictSchema?a.nil:a._`${i} && !${u}`,function(){const e=l.$async?a._`(${n}.async ? await ${u}(${s}) : ${u}(${s}))`:a._`${u}(${s})`,r=a._`(typeof ${u} == "function" ? ${e} : ${u}.test(${s}))`;return a._`${u} && ${u} !== true && ${c} === ${t} && !${r}`}()))}():function(){const o=f.formats[n];if(!o)return void function(){if(!1===d.strictSchema)return void f.logger.warn(e());throw new Error(e());function e(){return`unknown format "${n}" ignored in schema at path "${u}"`}}();if(!0===o)return;const[i,c,h]=function(e){const t=e instanceof RegExp?(0,a.regexpCode)(e):d.code.formats?a._`${d.code.formats}${(0,a.getProperty)(n)}`:void 0,s=r.scopeValue("formats",{key:n,ref:e,code:t});if("object"==typeof e&&!(e instanceof RegExp))return[e.type||"string",e.validate,a._`${s}.validate`];return["string",e,s]}(o);i===t&&e.pass(function(){if("object"==typeof o&&!(o instanceof RegExp)&&o.async){if(!l.$async)throw new Error("async format in sync schema");return a._`await ${h}(${s})`}return"function"==typeof c?a._`${h}(${s})`:a._`${h}.test(${s})`}())}())}};t.default=s},39502:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=[r(89651).default];t.default=a},66167:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.contentVocabulary=t.metadataVocabulary=void 0,t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]},64693:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(76776),o=r(43510),n={keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>a._`{allowedValue: ${e}}`},code(e){const{gen:t,data:r,$data:n,schemaCode:i,schema:c}=e;n||c&&"object"==typeof c?e.fail$data(a._`!${(0,s.useFunc)(t,o.default)}(${r}, ${i})`):e.fail(a._`${c} !== ${r}`)}};t.default=n},30966:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(76776),o=r(43510),n={keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>a._`{allowedValues: ${e}}`},code(e){const{gen:t,data:r,$data:n,schema:i,schemaCode:c,it:d}=e;if(!n&&0===i.length)throw new Error("enum must have non-empty array");const u=i.length>=d.opts.loopEnum;let l;const f=()=>null!=l?l:l=(0,s.useFunc)(t,o.default);let h;if(u||n)h=t.let("valid"),e.block$data(h,(function(){t.assign(h,!1),t.forOf("v",c,(e=>t.if(a._`${f()}(${r}, ${e})`,(()=>t.assign(h,!0).break()))))}));else{if(!Array.isArray(i))throw new Error("ajv implementation error");const e=t.const("vSchema",c);h=(0,a.or)(...i.map(((t,s)=>function(e,t){const s=i[t];return"object"==typeof s&&null!==s?a._`${f()}(${r}, ${e}[${t}])`:a._`${r} === ${s}`}(e,s))))}e.pass(h)}};t.default=n},62649:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(83983),s=r(90430),o=r(93229),n=r(74336),i=r(90498),c=r(33301),d=r(31687),u=r(82958),l=r(64693),f=r(30966),h=[a.default,s.default,o.default,n.default,i.default,c.default,d.default,u.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},l.default,f.default];t.default=h},31687:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxItems"===e?"more":"fewer";return a.str`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>a._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,o="maxItems"===t?a.operators.GT:a.operators.LT;e.fail$data(a._`${r}.length ${o} ${s}`)}};t.default=s},93229:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=r(76776),o=r(74499),n={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxLength"===e?"more":"fewer";return a.str`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>a._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:n,it:i}=e,c="maxLength"===t?a.operators.GT:a.operators.LT,d=!1===i.opts.unicode?a._`${r}.length`:a._`${(0,s.useFunc)(e.gen,o.default)}(${r})`;e.fail$data(a._`${d} ${c} ${n}`)}};t.default=n},83983:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s=a.operators,o={maximum:{okStr:"<=",ok:s.LTE,fail:s.GT},minimum:{okStr:">=",ok:s.GTE,fail:s.LT},exclusiveMaximum:{okStr:"<",ok:s.LT,fail:s.GTE},exclusiveMinimum:{okStr:">",ok:s.GT,fail:s.LTE}},n={message:({keyword:e,schemaCode:t})=>a.str`must be ${o[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>a._`{comparison: ${o[e].okStr}, limit: ${t}}`},i={keyword:Object.keys(o),type:"number",schemaType:"number",$data:!0,error:n,code(e){const{keyword:t,data:r,schemaCode:s}=e;e.fail$data(a._`${r} ${o[t].fail} ${s} || isNaN(${r})`)}};t.default=i},90498:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxProperties"===e?"more":"fewer";return a.str`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>a._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,o="maxProperties"===t?a.operators.GT:a.operators.LT;e.fail$data(a._`Object.keys(${r}).length ${o} ${s}`)}};t.default=s},90430:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(93487),s={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>a.str`must be multiple of ${e}`,params:({schemaCode:e})=>a._`{multipleOf: ${e}}`},code(e){const{gen:t,data:r,schemaCode:s,it:o}=e,n=o.opts.multipleOfPrecision,i=t.let("res"),c=n?a._`Math.abs(Math.round(${i}) - ${i}) > 1e-${n}`:a._`${i} !== parseInt(${i})`;e.fail$data(a._`(${s} === 0 || (${i} = ${r}/${s}, ${c}))`)}};t.default=s},74336:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(10412),s=r(93487),o={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>s.str`must match pattern "${e}"`,params:({schemaCode:e})=>s._`{pattern: ${e}}`},code(e){const{data:t,$data:r,schema:o,schemaCode:n,it:i}=e,c=i.opts.unicodeRegExp?"u":"",d=r?s._`(new RegExp(${n}, ${c}))`:(0,a.usePattern)(e,o);e.fail$data(s._`!${d}.test(${t})`)}};t.default=o},33301:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(10412),s=r(93487),o=r(76776),n={keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>s.str`must have required property '${e}'`,params:({params:{missingProperty:e}})=>s._`{missingProperty: ${e}}`},code(e){const{gen:t,schema:r,schemaCode:n,data:i,$data:c,it:d}=e,{opts:u}=d;if(!c&&0===r.length)return;const l=r.length>=u.loopRequired;if(d.allErrors?function(){if(l||c)e.block$data(s.nil,f);else for(const t of r)(0,a.checkReportMissingProp)(e,t)}():function(){const o=t.let("missing");if(l||c){const r=t.let("valid",!0);e.block$data(r,(()=>function(r,o){e.setParams({missingProperty:r}),t.forOf(r,n,(()=>{t.assign(o,(0,a.propertyInData)(t,i,r,u.ownProperties)),t.if((0,s.not)(o),(()=>{e.error(),t.break()}))}),s.nil)}(o,r))),e.ok(r)}else t.if((0,a.checkMissingProp)(e,r,o)),(0,a.reportMissingProp)(e,o),t.else()}(),u.strictRequired){const t=e.parentSchema.properties,{definedProperties:a}=e.it;for(const e of r)if(void 0===(null==t?void 0:t[e])&&!a.has(e)){const t=`required property "${e}" is not defined at "${d.schemaEnv.baseId+d.errSchemaPath}" (strictRequired)`;(0,o.checkStrictMode)(d,t,d.opts.strictRequired)}}function f(){t.forOf("prop",n,(r=>{e.setParams({missingProperty:r}),t.if((0,a.noPropertyInData)(t,i,r,u.ownProperties),(()=>e.error()))}))}}};t.default=n},82958:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(50453),s=r(93487),o=r(76776),n=r(43510),i={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>s.str`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>s._`{i: ${e}, j: ${t}}`},code(e){const{gen:t,data:r,$data:i,schema:c,parentSchema:d,schemaCode:u,it:l}=e;if(!i&&!c)return;const f=t.let("valid"),h=d.items?(0,a.getSchemaTypes)(d.items):[];function p(o,n){const i=t.name("item"),c=(0,a.checkDataTypes)(h,i,l.opts.strictNumbers,a.DataType.Wrong),d=t.const("indices",s._`{}`);t.for(s._`;${o}--;`,(()=>{t.let(i,s._`${r}[${o}]`),t.if(c,s._`continue`),h.length>1&&t.if(s._`typeof ${i} == "string"`,s._`${i} += "_"`),t.if(s._`typeof ${d}[${i}] == "number"`,(()=>{t.assign(n,s._`${d}[${i}]`),e.error(),t.assign(f,!1).break()})).code(s._`${d}[${i}] = ${o}`)}))}function m(a,i){const c=(0,o.useFunc)(t,n.default),d=t.name("outer");t.label(d).for(s._`;${a}--;`,(()=>t.for(s._`${i} = ${a}; ${i}--;`,(()=>t.if(s._`${c}(${r}[${a}], ${r}[${i}])`,(()=>{e.error(),t.assign(f,!1).break(d)}))))))}e.block$data(f,(function(){const a=t.let("i",s._`${r}.length`),o=t.let("j");e.setParams({i:a,j:o}),t.assign(f,!0),t.if(s._`${a} > 1`,(()=>(h.length>0&&!h.some((e=>"object"===e||"array"===e))?p:m)(a,o)))}),s._`${u} === false`),e.ok(f)}};t.default=i},64063:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var a,s,o;if(Array.isArray(t)){if((a=t.length)!=r.length)return!1;for(s=a;0!=s--;)if(!e(t[s],r[s]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((a=(o=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(s=a;0!=s--;)if(!Object.prototype.hasOwnProperty.call(r,o[s]))return!1;for(s=a;0!=s--;){var n=o[s];if(!e(t[n],r[n]))return!1}return!0}return t!=t&&r!=r}},49461:e=>{"use strict";var t=e.exports=function(e,t,a){"function"==typeof t&&(a=t,t={}),r(t,"function"==typeof(a=t.cb||a)?a:a.pre||function(){},a.post||function(){},e,"",e)};function r(e,a,s,o,n,i,c,d,u,l){if(o&&"object"==typeof o&&!Array.isArray(o)){for(var f in a(o,n,i,c,d,u,l),o){var h=o[f];if(Array.isArray(h)){if(f in t.arrayKeywords)for(var p=0;p<h.length;p++)r(e,a,s,h[p],n+"/"+f+"/"+p,i,n,f,o,p)}else if(f in t.propsKeywords){if(h&&"object"==typeof h)for(var m in h)r(e,a,s,h[m],n+"/"+f+"/"+m.replace(/~/g,"~0").replace(/\//g,"~1"),i,n,f,o,m)}else(f in t.keywords||e.allKeys&&!(f in t.skipKeywords))&&r(e,a,s,h,n+"/"+f,i,n,f,o)}s(o,n,i,c,d,u,l)}}t.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0},t.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},t.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},t.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}},60540:function(e,t){!function(e){"use strict";function t(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(t.length>1){t[0]=t[0].slice(0,-1);for(var a=t.length-1,s=1;s<a;++s)t[s]=t[s].slice(1,-1);return t[a]=t[a].slice(1),t.join("")}return t[0]}function r(e){return"(?:"+e+")"}function a(e){return void 0===e?"undefined":null===e?"null":Object.prototype.toString.call(e).split(" ").pop().split("]").shift().toLowerCase()}function s(e){return e.toUpperCase()}function o(e){return null!=e?e instanceof Array?e:"number"!=typeof e.length||e.split||e.setInterval||e.call?[e]:Array.prototype.slice.call(e):[]}function n(e,t){var r=e;if(t)for(var a in t)r[a]=t[a];return r}function i(e){var a="[A-Za-z]",s="[0-9]",o=t(s,"[A-Fa-f]"),n=r(r("%[EFef]"+o+"%"+o+o+"%"+o+o)+"|"+r("%[89A-Fa-f]"+o+"%"+o+o)+"|"+r("%"+o+o)),i="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",c=t("[\\:\\/\\?\\#\\[\\]\\@]",i),d=e?"[\\uE000-\\uF8FF]":"[]",u=t(a,s,"[\\-\\.\\_\\~]",e?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]"),l=r(a+t(a,s,"[\\+\\-\\.]")+"*"),f=r(r(n+"|"+t(u,i,"[\\:]"))+"*"),h=(r(r("25[0-5]")+"|"+r("2[0-4]"+s)+"|"+r("1"+s+s)+"|"+r("[1-9]"+s)+"|"+s),r(r("25[0-5]")+"|"+r("2[0-4]"+s)+"|"+r("1"+s+s)+"|"+r("0?[1-9]"+s)+"|0?0?"+s)),p=r(h+"\\."+h+"\\."+h+"\\."+h),m=r(o+"{1,4}"),y=r(r(m+"\\:"+m)+"|"+p),v=r(r(m+"\\:")+"{6}"+y),g=r("\\:\\:"+r(m+"\\:")+"{5}"+y),$=r(r(m)+"?\\:\\:"+r(m+"\\:")+"{4}"+y),_=r(r(r(m+"\\:")+"{0,1}"+m)+"?\\:\\:"+r(m+"\\:")+"{3}"+y),w=r(r(r(m+"\\:")+"{0,2}"+m)+"?\\:\\:"+r(m+"\\:")+"{2}"+y),b=r(r(r(m+"\\:")+"{0,3}"+m)+"?\\:\\:"+m+"\\:"+y),E=r(r(r(m+"\\:")+"{0,4}"+m)+"?\\:\\:"+y),S=r(r(r(m+"\\:")+"{0,5}"+m)+"?\\:\\:"+m),P=r(r(r(m+"\\:")+"{0,6}"+m)+"?\\:\\:"),k=r([v,g,$,_,w,b,E,S,P].join("|")),N=r(r(u+"|"+n)+"+"),j=(r(k+"\\%25"+N),r(k+r("\\%25|\\%(?!"+o+"{2})")+N)),C=r("[vV]"+o+"+\\."+t(u,i,"[\\:]")+"+"),O=r("\\["+r(j+"|"+k+"|"+C)+"\\]"),x=r(r(n+"|"+t(u,i))+"*"),T=r(O+"|"+p+"(?!"+x+")|"+x),I=r(s+"*"),R=r(r(f+"@")+"?"+T+r("\\:"+I)+"?"),D=r(n+"|"+t(u,i,"[\\:\\@]")),A=r(D+"*"),M=r(D+"+"),z=r(r(n+"|"+t(u,i,"[\\@]"))+"+"),V=r(r("\\/"+A)+"*"),F=r("\\/"+r(M+V)+"?"),U=r(z+V),q=r(M+V),K="(?!"+D+")",L=(r(V+"|"+F+"|"+U+"|"+q+"|"+K),r(r(D+"|"+t("[\\/\\?]",d))+"*")),G=r(r(D+"|[\\/\\?]")+"*"),H=r(r("\\/\\/"+R+V)+"|"+F+"|"+q+"|"+K),J=r(l+"\\:"+H+r("\\?"+L)+"?"+r("\\#"+G)+"?"),B=r(r("\\/\\/"+R+V)+"|"+F+"|"+U+"|"+K),W=r(B+r("\\?"+L)+"?"+r("\\#"+G)+"?");return r(J+"|"+W),r(l+"\\:"+H+r("\\?"+L)+"?"),r(r("\\/\\/("+r("("+f+")@")+"?("+T+")"+r("\\:("+I+")")+"?)")+"?("+V+"|"+F+"|"+q+"|"+K+")"),r("\\?("+L+")"),r("\\#("+G+")"),r(r("\\/\\/("+r("("+f+")@")+"?("+T+")"+r("\\:("+I+")")+"?)")+"?("+V+"|"+F+"|"+U+"|"+K+")"),r("\\?("+L+")"),r("\\#("+G+")"),r(r("\\/\\/("+r("("+f+")@")+"?("+T+")"+r("\\:("+I+")")+"?)")+"?("+V+"|"+F+"|"+q+"|"+K+")"),r("\\?("+L+")"),r("\\#("+G+")"),r("("+f+")@"),r("\\:("+I+")"),{NOT_SCHEME:new RegExp(t("[^]",a,s,"[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(t("[^\\%\\:]",u,i),"g"),NOT_HOST:new RegExp(t("[^\\%\\[\\]\\:]",u,i),"g"),NOT_PATH:new RegExp(t("[^\\%\\/\\:\\@]",u,i),"g"),NOT_PATH_NOSCHEME:new RegExp(t("[^\\%\\/\\@]",u,i),"g"),NOT_QUERY:new RegExp(t("[^\\%]",u,i,"[\\:\\@\\/\\?]",d),"g"),NOT_FRAGMENT:new RegExp(t("[^\\%]",u,i,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(t("[^]",u,i),"g"),UNRESERVED:new RegExp(u,"g"),OTHER_CHARS:new RegExp(t("[^\\%]",u,c),"g"),PCT_ENCODED:new RegExp(n,"g"),IPV4ADDRESS:new RegExp("^("+p+")$"),IPV6ADDRESS:new RegExp("^\\[?("+k+")"+r(r("\\%25|\\%(?!"+o+"{2})")+"("+N+")")+"?\\]?$")}}var c=i(!1),d=i(!0),u=function(){function e(e,t){var r=[],a=!0,s=!1,o=void 0;try{for(var n,i=e[Symbol.iterator]();!(a=(n=i.next()).done)&&(r.push(n.value),!t||r.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{!a&&i.return&&i.return()}finally{if(s)throw o}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),l=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},f=2147483647,h=36,p=1,m=26,y=38,v=700,g=72,$=128,_="-",w=/^xn--/,b=/[^\0-\x7E]/,E=/[\x2E\u3002\uFF0E\uFF61]/g,S={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},P=h-p,k=Math.floor,N=String.fromCharCode;function j(e){throw new RangeError(S[e])}function C(e,t){for(var r=[],a=e.length;a--;)r[a]=t(e[a]);return r}function O(e,t){var r=e.split("@"),a="";return r.length>1&&(a=r[0]+"@",e=r[1]),a+C((e=e.replace(E,".")).split("."),t).join(".")}function x(e){for(var t=[],r=0,a=e.length;r<a;){var s=e.charCodeAt(r++);if(s>=55296&&s<=56319&&r<a){var o=e.charCodeAt(r++);56320==(64512&o)?t.push(((1023&s)<<10)+(1023&o)+65536):(t.push(s),r--)}else t.push(s)}return t}var T=function(e){return e-48<10?e-22:e-65<26?e-65:e-97<26?e-97:h},I=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},R=function(e,t,r){var a=0;for(e=r?k(e/v):e>>1,e+=k(e/t);e>P*m>>1;a+=h)e=k(e/P);return k(a+(P+1)*e/(e+y))},D=function(e){var t=[],r=e.length,a=0,s=$,o=g,n=e.lastIndexOf(_);n<0&&(n=0);for(var i=0;i<n;++i)e.charCodeAt(i)>=128&&j("not-basic"),t.push(e.charCodeAt(i));for(var c=n>0?n+1:0;c<r;){for(var d=a,u=1,l=h;;l+=h){c>=r&&j("invalid-input");var y=T(e.charCodeAt(c++));(y>=h||y>k((f-a)/u))&&j("overflow"),a+=y*u;var v=l<=o?p:l>=o+m?m:l-o;if(y<v)break;var w=h-v;u>k(f/w)&&j("overflow"),u*=w}var b=t.length+1;o=R(a-d,b,0==d),k(a/b)>f-s&&j("overflow"),s+=k(a/b),a%=b,t.splice(a++,0,s)}return String.fromCodePoint.apply(String,t)},A=function(e){var t=[],r=(e=x(e)).length,a=$,s=0,o=g,n=!0,i=!1,c=void 0;try{for(var d,u=e[Symbol.iterator]();!(n=(d=u.next()).done);n=!0){var l=d.value;l<128&&t.push(N(l))}}catch(e){i=!0,c=e}finally{try{!n&&u.return&&u.return()}finally{if(i)throw c}}var y=t.length,v=y;for(y&&t.push(_);v<r;){var w=f,b=!0,E=!1,S=void 0;try{for(var P,C=e[Symbol.iterator]();!(b=(P=C.next()).done);b=!0){var O=P.value;O>=a&&O<w&&(w=O)}}catch(e){E=!0,S=e}finally{try{!b&&C.return&&C.return()}finally{if(E)throw S}}var T=v+1;w-a>k((f-s)/T)&&j("overflow"),s+=(w-a)*T,a=w;var D=!0,A=!1,M=void 0;try{for(var z,V=e[Symbol.iterator]();!(D=(z=V.next()).done);D=!0){var F=z.value;if(F<a&&++s>f&&j("overflow"),F==a){for(var U=s,q=h;;q+=h){var K=q<=o?p:q>=o+m?m:q-o;if(U<K)break;var L=U-K,G=h-K;t.push(N(I(K+L%G,0))),U=k(L/G)}t.push(N(I(U,0))),o=R(s,T,v==y),s=0,++v}}}catch(e){A=!0,M=e}finally{try{!D&&V.return&&V.return()}finally{if(A)throw M}}++s,++a}return t.join("")},M=function(e){return O(e,(function(e){return w.test(e)?D(e.slice(4).toLowerCase()):e}))},z=function(e){return O(e,(function(e){return b.test(e)?"xn--"+A(e):e}))},V={version:"2.1.0",ucs2:{decode:x,encode:function(e){return String.fromCodePoint.apply(String,l(e))}},decode:D,encode:A,toASCII:z,toUnicode:M},F={};function U(e){var t=e.charCodeAt(0);return t<16?"%0"+t.toString(16).toUpperCase():t<128?"%"+t.toString(16).toUpperCase():t<2048?"%"+(t>>6|192).toString(16).toUpperCase()+"%"+(63&t|128).toString(16).toUpperCase():"%"+(t>>12|224).toString(16).toUpperCase()+"%"+(t>>6&63|128).toString(16).toUpperCase()+"%"+(63&t|128).toString(16).toUpperCase()}function q(e){for(var t="",r=0,a=e.length;r<a;){var s=parseInt(e.substr(r+1,2),16);if(s<128)t+=String.fromCharCode(s),r+=3;else if(s>=194&&s<224){if(a-r>=6){var o=parseInt(e.substr(r+4,2),16);t+=String.fromCharCode((31&s)<<6|63&o)}else t+=e.substr(r,6);r+=6}else if(s>=224){if(a-r>=9){var n=parseInt(e.substr(r+4,2),16),i=parseInt(e.substr(r+7,2),16);t+=String.fromCharCode((15&s)<<12|(63&n)<<6|63&i)}else t+=e.substr(r,9);r+=9}else t+=e.substr(r,3),r+=3}return t}function K(e,t){function r(e){var r=q(e);return r.match(t.UNRESERVED)?r:e}return e.scheme&&(e.scheme=String(e.scheme).replace(t.PCT_ENCODED,r).toLowerCase().replace(t.NOT_SCHEME,"")),void 0!==e.userinfo&&(e.userinfo=String(e.userinfo).replace(t.PCT_ENCODED,r).replace(t.NOT_USERINFO,U).replace(t.PCT_ENCODED,s)),void 0!==e.host&&(e.host=String(e.host).replace(t.PCT_ENCODED,r).toLowerCase().replace(t.NOT_HOST,U).replace(t.PCT_ENCODED,s)),void 0!==e.path&&(e.path=String(e.path).replace(t.PCT_ENCODED,r).replace(e.scheme?t.NOT_PATH:t.NOT_PATH_NOSCHEME,U).replace(t.PCT_ENCODED,s)),void 0!==e.query&&(e.query=String(e.query).replace(t.PCT_ENCODED,r).replace(t.NOT_QUERY,U).replace(t.PCT_ENCODED,s)),void 0!==e.fragment&&(e.fragment=String(e.fragment).replace(t.PCT_ENCODED,r).replace(t.NOT_FRAGMENT,U).replace(t.PCT_ENCODED,s)),e}function L(e){return e.replace(/^0*(.*)/,"$1")||"0"}function G(e,t){var r=e.match(t.IPV4ADDRESS)||[],a=u(r,2)[1];return a?a.split(".").map(L).join("."):e}function H(e,t){var r=e.match(t.IPV6ADDRESS)||[],a=u(r,3),s=a[1],o=a[2];if(s){for(var n=s.toLowerCase().split("::").reverse(),i=u(n,2),c=i[0],d=i[1],l=d?d.split(":").map(L):[],f=c.split(":").map(L),h=t.IPV4ADDRESS.test(f[f.length-1]),p=h?7:8,m=f.length-p,y=Array(p),v=0;v<p;++v)y[v]=l[v]||f[m+v]||"";h&&(y[p-1]=G(y[p-1],t));var g=y.reduce((function(e,t,r){if(!t||"0"===t){var a=e[e.length-1];a&&a.index+a.length===r?a.length++:e.push({index:r,length:1})}return e}),[]).sort((function(e,t){return t.length-e.length}))[0],$=void 0;if(g&&g.length>1){var _=y.slice(0,g.index),w=y.slice(g.index+g.length);$=_.join(":")+"::"+w.join(":")}else $=y.join(":");return o&&($+="%"+o),$}return e}var J=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i,B=void 0==="".match(/(){0}/)[1];function W(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r={},a=!1!==t.iri?d:c;"suffix"===t.reference&&(e=(t.scheme?t.scheme+":":"")+"//"+e);var s=e.match(J);if(s){B?(r.scheme=s[1],r.userinfo=s[3],r.host=s[4],r.port=parseInt(s[5],10),r.path=s[6]||"",r.query=s[7],r.fragment=s[8],isNaN(r.port)&&(r.port=s[5])):(r.scheme=s[1]||void 0,r.userinfo=-1!==e.indexOf("@")?s[3]:void 0,r.host=-1!==e.indexOf("//")?s[4]:void 0,r.port=parseInt(s[5],10),r.path=s[6]||"",r.query=-1!==e.indexOf("?")?s[7]:void 0,r.fragment=-1!==e.indexOf("#")?s[8]:void 0,isNaN(r.port)&&(r.port=e.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?s[4]:void 0)),r.host&&(r.host=H(G(r.host,a),a)),void 0!==r.scheme||void 0!==r.userinfo||void 0!==r.host||void 0!==r.port||r.path||void 0!==r.query?void 0===r.scheme?r.reference="relative":void 0===r.fragment?r.reference="absolute":r.reference="uri":r.reference="same-document",t.reference&&"suffix"!==t.reference&&t.reference!==r.reference&&(r.error=r.error||"URI is not a "+t.reference+" reference.");var o=F[(t.scheme||r.scheme||"").toLowerCase()];if(t.unicodeSupport||o&&o.unicodeSupport)K(r,a);else{if(r.host&&(t.domainHost||o&&o.domainHost))try{r.host=V.toASCII(r.host.replace(a.PCT_ENCODED,q).toLowerCase())}catch(e){r.error=r.error||"Host's domain name can not be converted to ASCII via punycode: "+e}K(r,c)}o&&o.parse&&o.parse(r,t)}else r.error=r.error||"URI can not be parsed.";return r}function Z(e,t){var r=!1!==t.iri?d:c,a=[];return void 0!==e.userinfo&&(a.push(e.userinfo),a.push("@")),void 0!==e.host&&a.push(H(G(String(e.host),r),r).replace(r.IPV6ADDRESS,(function(e,t,r){return"["+t+(r?"%25"+r:"")+"]"}))),"number"!=typeof e.port&&"string"!=typeof e.port||(a.push(":"),a.push(String(e.port))),a.length?a.join(""):void 0}var Q=/^\.\.?\//,Y=/^\/\.(\/|$)/,X=/^\/\.\.(\/|$)/,ee=/^\/?(?:.|\n)*?(?=\/|$)/;function te(e){for(var t=[];e.length;)if(e.match(Q))e=e.replace(Q,"");else if(e.match(Y))e=e.replace(Y,"/");else if(e.match(X))e=e.replace(X,"/"),t.pop();else if("."===e||".."===e)e="";else{var r=e.match(ee);if(!r)throw new Error("Unexpected dot segment condition");var a=r[0];e=e.slice(a.length),t.push(a)}return t.join("")}function re(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.iri?d:c,a=[],s=F[(t.scheme||e.scheme||"").toLowerCase()];if(s&&s.serialize&&s.serialize(e,t),e.host)if(r.IPV6ADDRESS.test(e.host));else if(t.domainHost||s&&s.domainHost)try{e.host=t.iri?V.toUnicode(e.host):V.toASCII(e.host.replace(r.PCT_ENCODED,q).toLowerCase())}catch(r){e.error=e.error||"Host's domain name can not be converted to "+(t.iri?"Unicode":"ASCII")+" via punycode: "+r}K(e,r),"suffix"!==t.reference&&e.scheme&&(a.push(e.scheme),a.push(":"));var o=Z(e,t);if(void 0!==o&&("suffix"!==t.reference&&a.push("//"),a.push(o),e.path&&"/"!==e.path.charAt(0)&&a.push("/")),void 0!==e.path){var n=e.path;t.absolutePath||s&&s.absolutePath||(n=te(n)),void 0===o&&(n=n.replace(/^\/\//,"/%2F")),a.push(n)}return void 0!==e.query&&(a.push("?"),a.push(e.query)),void 0!==e.fragment&&(a.push("#"),a.push(e.fragment)),a.join("")}function ae(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a={};return arguments[3]||(e=W(re(e,r),r),t=W(re(t,r),r)),!(r=r||{}).tolerant&&t.scheme?(a.scheme=t.scheme,a.userinfo=t.userinfo,a.host=t.host,a.port=t.port,a.path=te(t.path||""),a.query=t.query):(void 0!==t.userinfo||void 0!==t.host||void 0!==t.port?(a.userinfo=t.userinfo,a.host=t.host,a.port=t.port,a.path=te(t.path||""),a.query=t.query):(t.path?("/"===t.path.charAt(0)?a.path=te(t.path):(void 0===e.userinfo&&void 0===e.host&&void 0===e.port||e.path?e.path?a.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:a.path=t.path:a.path="/"+t.path,a.path=te(a.path)),a.query=t.query):(a.path=e.path,void 0!==t.query?a.query=t.query:a.query=e.query),a.userinfo=e.userinfo,a.host=e.host,a.port=e.port),a.scheme=e.scheme),a.fragment=t.fragment,a}function se(e,t,r){var a=n({scheme:"null"},r);return re(ae(W(e,a),W(t,a),a,!0),a)}function oe(e,t){return"string"==typeof e?e=re(W(e,t),t):"object"===a(e)&&(e=W(re(e,t),t)),e}function ne(e,t,r){return"string"==typeof e?e=re(W(e,r),r):"object"===a(e)&&(e=re(e,r)),"string"==typeof t?t=re(W(t,r),r):"object"===a(t)&&(t=re(t,r)),e===t}function ie(e,t){return e&&e.toString().replace(t&&t.iri?d.ESCAPE:c.ESCAPE,U)}function ce(e,t){return e&&e.toString().replace(t&&t.iri?d.PCT_ENCODED:c.PCT_ENCODED,q)}var de={scheme:"http",domainHost:!0,parse:function(e,t){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e},serialize:function(e,t){var r="https"===String(e.scheme).toLowerCase();return e.port!==(r?443:80)&&""!==e.port||(e.port=void 0),e.path||(e.path="/"),e}},ue={scheme:"https",domainHost:de.domainHost,parse:de.parse,serialize:de.serialize};function le(e){return"boolean"==typeof e.secure?e.secure:"wss"===String(e.scheme).toLowerCase()}var fe={scheme:"ws",domainHost:!0,parse:function(e,t){var r=e;return r.secure=le(r),r.resourceName=(r.path||"/")+(r.query?"?"+r.query:""),r.path=void 0,r.query=void 0,r},serialize:function(e,t){if(e.port!==(le(e)?443:80)&&""!==e.port||(e.port=void 0),"boolean"==typeof e.secure&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){var r=e.resourceName.split("?"),a=u(r,2),s=a[0],o=a[1];e.path=s&&"/"!==s?s:void 0,e.query=o,e.resourceName=void 0}return e.fragment=void 0,e}},he={scheme:"wss",domainHost:fe.domainHost,parse:fe.parse,serialize:fe.serialize},pe={},me="[A-Za-z0-9\\-\\.\\_\\~\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]",ye="[0-9A-Fa-f]",ve=r(r("%[EFef]"+ye+"%"+ye+ye+"%"+ye+ye)+"|"+r("%[89A-Fa-f]"+ye+"%"+ye+ye)+"|"+r("%"+ye+ye)),ge="[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]",$e=t("[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]",'[\\"\\\\]'),_e="[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]",we=new RegExp(me,"g"),be=new RegExp(ve,"g"),Ee=new RegExp(t("[^]",ge,"[\\.]",'[\\"]',$e),"g"),Se=new RegExp(t("[^]",me,_e),"g"),Pe=Se;function ke(e){var t=q(e);return t.match(we)?t:e}var Ne={scheme:"mailto",parse:function(e,t){var r=e,a=r.to=r.path?r.path.split(","):[];if(r.path=void 0,r.query){for(var s=!1,o={},n=r.query.split("&"),i=0,c=n.length;i<c;++i){var d=n[i].split("=");switch(d[0]){case"to":for(var u=d[1].split(","),l=0,f=u.length;l<f;++l)a.push(u[l]);break;case"subject":r.subject=ce(d[1],t);break;case"body":r.body=ce(d[1],t);break;default:s=!0,o[ce(d[0],t)]=ce(d[1],t)}}s&&(r.headers=o)}r.query=void 0;for(var h=0,p=a.length;h<p;++h){var m=a[h].split("@");if(m[0]=ce(m[0]),t.unicodeSupport)m[1]=ce(m[1],t).toLowerCase();else try{m[1]=V.toASCII(ce(m[1],t).toLowerCase())}catch(e){r.error=r.error||"Email address's domain name can not be converted to ASCII via punycode: "+e}a[h]=m.join("@")}return r},serialize:function(e,t){var r=e,a=o(e.to);if(a){for(var n=0,i=a.length;n<i;++n){var c=String(a[n]),d=c.lastIndexOf("@"),u=c.slice(0,d).replace(be,ke).replace(be,s).replace(Ee,U),l=c.slice(d+1);try{l=t.iri?V.toUnicode(l):V.toASCII(ce(l,t).toLowerCase())}catch(e){r.error=r.error||"Email address's domain name can not be converted to "+(t.iri?"Unicode":"ASCII")+" via punycode: "+e}a[n]=u+"@"+l}r.path=a.join(",")}var f=e.headers=e.headers||{};e.subject&&(f.subject=e.subject),e.body&&(f.body=e.body);var h=[];for(var p in f)f[p]!==pe[p]&&h.push(p.replace(be,ke).replace(be,s).replace(Se,U)+"="+f[p].replace(be,ke).replace(be,s).replace(Pe,U));return h.length&&(r.query=h.join("&")),r}},je=/^([^\:]+)\:(.*)/,Ce={scheme:"urn",parse:function(e,t){var r=e.path&&e.path.match(je),a=e;if(r){var s=t.scheme||a.scheme||"urn",o=r[1].toLowerCase(),n=r[2],i=s+":"+(t.nid||o),c=F[i];a.nid=o,a.nss=n,a.path=void 0,c&&(a=c.parse(a,t))}else a.error=a.error||"URN can not be parsed.";return a},serialize:function(e,t){var r=t.scheme||e.scheme||"urn",a=e.nid,s=r+":"+(t.nid||a),o=F[s];o&&(e=o.serialize(e,t));var n=e,i=e.nss;return n.path=(a||t.nid)+":"+i,n}},Oe=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/,xe={scheme:"urn:uuid",parse:function(e,t){var r=e;return r.uuid=r.nss,r.nss=void 0,t.tolerant||r.uuid&&r.uuid.match(Oe)||(r.error=r.error||"UUID is not valid."),r},serialize:function(e,t){var r=e;return r.nss=(e.uuid||"").toLowerCase(),r}};F[de.scheme]=de,F[ue.scheme]=ue,F[fe.scheme]=fe,F[he.scheme]=he,F[Ne.scheme]=Ne,F[Ce.scheme]=Ce,F[xe.scheme]=xe,e.SCHEMES=F,e.pctEncChar=U,e.pctDecChars=q,e.parse=W,e.removeDotSegments=te,e.serialize=re,e.resolveComponents=ae,e.resolve=se,e.normalize=oe,e.equal=ne,e.escapeComponent=ie,e.unescapeComponent=ce,Object.defineProperty(e,"__esModule",{value:!0})}(t)},64775:e=>{"use strict";e.exports=JSON.parse('{"$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON AnySchema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},98:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')}}]);