/*! For license information please see 319.js.LICENSE.txt */
"use strict";(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[319],{38319:(e,t,s)=>{s.d(t,{hf:()=>n,lb:()=>o});var i=s(50575);const o=(e=200)=>({resizeDimensions:[{property:i.P.width,setDimension:e=>e.width,duration:e},{property:i.P.height,setDimension:e=>e.height,duration:e},{property:i.P.top,setDimension:()=>0,duration:e},{property:i.P.left,setDimension:()=>0,duration:e}]}),n=(e,t,s,o=200)=>{const n=[];return void 0!==e&&n.push({property:i.P.width,setDimension:t=>t.width+e,duration:o}),void 0!==t&&n.push({property:i.P.height,setDimension:e=>e.height+t,duration:o}),void 0!==s&&n.push({property:i.P.left,setDimension:()=>s,duration:o}),{resizeDimensions:n}}},14564:(e,t,s)=>{s.d(t,{u:()=>r});var i,o,n=s(19663);!function(e){e.all="all",e.byMeshGroup="byMeshGroup",e.byMeshSubGroup="byMeshSubGroup"}(i||(i={})),function(e){e.explicit="explicit",e.random="random"}(o||(o={}));class r extends n.m{constructor(e,t){super(),this.id="SET_MESH_OVERLAY_COLOR",this.payload={selectBy:(null==t?void 0:t.style)||i.all,colorStyle:(null==e?void 0:e.style)||o.explicit,color:(null==e?void 0:e.color)||null,alpha:(null==e?void 0:e.alpha)||.5,index:null==t?void 0:t.index}}}r.selectBy=i,r.colorBy=o,r.COLOR_DIM={x:0,y:0,z:0,w:.3}},75810:(e,t,s)=>{s.d(t,{I:()=>o});var i=s(19663);class o extends i.m{constructor(e){super(),this.id="TOGGLE_MESH_OVERLAY_COLOR",this.payload={enabled:e}}}},21270:(e,t,s)=>{var i;s.d(t,{V:()=>i}),function(e){e[e.Min=0]="Min",e[e.Standard=1]="Standard",e[e.High=2]="High",e[e.Detail=3]="Detail"}(i||(i={}))},51524:(e,t,s)=>{s.d(t,{t:()=>a});var i=s(59279),o=s(53261),n=s(31362),r=s(21270);const a={urlTemplateToken:"<file>",initialMaxLOD:r.V.Min,nonMeshMaxLOD:r.V.Standard,maxLOD:r.V.High,minLOD:r.V.Min,loadSiblings:!0,displayActiveTiles:!1,autoDisableRendererCulling:!0,optimizeRaycast:!1,stopAtEmptyTiles:!1,disableTileUpdates:!1,disposeModel:!1,limitMemoryUsage:(0,n.tq)(),allocatedMegsBeforeLimitingLod:350,lruMinExtraTiles:(0,n.tq)()?0:100,lruMaxTiles:800,lruUnloadPercent:.05,tileAssetRequestPriority:o.ru.MEDIUM,downloadQueueConcurrency:8,parseQueueConcurrency:10,snappingMaxLOD:r.V.Standard,errorTarget:Number((0,i.eY)("errorTarget",(0,n.tq)()?6:4)),errorMultiplierHiddenFloors:.01,errorMultiplierRaycastOcclusion:.1,smallMeshThreshold:Number((0,i.eY)("smallMeshThreshold",40)),smallMeshErrorMultiplier:Number((0,i.eY)("smallMeshErrorMultiplier",.1))}},26269:(e,t,s)=>{s.d(t,{$4:()=>n,oR:()=>o});var i=s(7402);const o=e=>!!e&&e instanceof i.g,n={hasMeshGroup:e=>"object"==typeof e&&!!e&&"meshGroup"in e,hasMeshSubgroup:e=>"object"==typeof e&&!!e&&"meshSubgroup"in e,isRoomMesh:o,isVisibleRoomMesh:e=>o(e)&&e.raycastEnabled&&e.visible}},88338:(e,t,s)=>{s.d(t,{E:()=>i});const i={longerTransitionMaxDist:10,TRANSITION_TIME_DH:650,TRANSITION_TIME_ROOM:800}},20043:(e,t,s)=>{s.d(t,{Tq:()=>r,bG:()=>c});var i=s(98169),o=s(81248),n=s(88338);const r=(e,t,s,i,...o)=>a({sweepData:e,direction:t,directionFactor:s,sourceSweep:i,ignoreSweeps:o}),a=e=>{const{sweepData:t,direction:s,sourceSweep:n,ignoreSweeps:r,directionFactor:a}=e;if(!t.currentSweepObject)return[];const c=n||t.currentSweepObject,h=[i.ff(c),i._k(),i.vO(c),i.pI(c.position,s,a)];for(const e of r)h.push(i.ff(e));const l=[o.o7(c.position,s),o.TE(c.position)],p=t.getSweepNeighbours(c);return t.sortByScore(h,l,p)},c=(e,t,s,r)=>{const a=[i._k(),i._T()],c=[o.Dv(s.point)],h=e.currentSweepObject;t&&h&&a.push(i.ff(h),i.SF(h.position,n.E.longerTransitionMaxDist),i.vO(h)),s.face&&a.push(i.D5(s.point,s.face.normal));const l=r.floorIdFromObject(s.object);l&&c.push(o.Bv(l));const p=e.sortByScore(a,c);if(0===p.length){const t=e.getClosestSweep(s.point,!0);p.push({sweep:t,score:0})}return p}},49416:(e,t,s)=>{s.d(t,{O:()=>n});var i=s(63319),o=s(9373);function n(e,t,s){const n=e&&e.hasRooms(),r=s?t.getLayer(s):t.getActiveLayer();return r&&r.layerType===i.s0.COMMON_USER_LAYER?!n:!!(0,o.AI)(t.getCurrentView())||!n}},20241:(e,t,s)=>{s.d(t,{d:()=>i});class i{constructor(e){this.tags=[],e&&Object.assign(this,e)}}},3907:(e,t,s)=>{s.d(t,{MU:()=>a});var i,o=s(39880),n=s(44584);!function(e){e.GET="GET",e.POST="POST",e.PATCH="PATCH",e.PUT="PUT",e.DELETE="DELETE",e.OPTIONS="OPTIONS"}(i||(i={}));class r extends class{constructor(){this._options={responseType:"json"}}get options(){const e=this._options;return e.headers=(0,n.m)(this.url,this._options.headers||{}),e}}{constructor(e){super(),this.config=e,this.url=e.path}async read(){const{deserialize:e}=this.config;let t=null;return this.config.cachedData&&this.config.cachedData.data?t=this.config.cachedData.data:(t=await this.config.queue.get(this.config.path,this.options),this.config.cachedData&&(this.config.cachedData.data=t)),e(t)}clearCache(){this.config.cachedData&&(this.config.cachedData.data=null)}}class a extends r{constructor(e){super(e),this.config=e,this.acceptsPartial=!1,this.config.batchUpdate="batchUpdate"in this.config&&this.config.batchUpdate}async create(e){throw Error("Not implemented")}updateBatch(e,t){const{serialize:s}=this.config,o=[],n=[...new Set([...Object.keys(e),...Object.keys(t)])];for(const s of n){e[s]||t[s]||o.push(this.config.queue.delete(`${this.config.path}/${s}`,this.options))}const r=s(e,t),a=Object.assign(Object.assign({},this.options),{body:r});return o.push(this.config.queue.request(this.config.httpMethod||i.POST,this.config.path,a)),Promise.all(o)}updateInternal(e,t){const{serialize:s}=this.config,n=[],r=Object.assign({},this.options),a=Object.keys(e),c=Object.keys(t),h=(0,o.XN)(a.concat(c));for(const o in h){const a=h[o],c=e[a]||t[a];if(c){const e={};e[a]=c;const o={},h=t[a];h&&(o[a]=h);const l=s(e,o);r.body=l,n.push(this.config.queue.request(this.config.httpMethod||i.POST,this.config.path,r))}else n.push(this.config.queue.delete(`${this.config.path}/${a}`,this.options))}return Promise.all(n)}async update(e,t){this.clearCache(),await(this.config.batchUpdate?this.updateBatch(e,t||{}):this.updateInternal(e,t||{}))}async delete(e){throw Error("Not implemented")}}}}]);