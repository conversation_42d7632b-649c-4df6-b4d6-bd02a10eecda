/*! For license information please see 324.js.LICENSE.txt */
"use strict";(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[324],{62576:(t,i,e)=>{e.d(i,{Bl:()=>c,Lm:()=>l,Pi:()=>a,RV:()=>o,e_:()=>r,v6:()=>s});var n=e(19663);class s extends n.m{constructor(){super(),this.id="PLUGIN_RESET_ALL",this.payload={}}}class o extends n.m{constructor(t,i,e,n){super(),this.id="PLUGIN_RELOAD",this.payload={name:t,config:i,configMeta:e,permissions:n||{}}}}class a extends n.m{constructor(t,i,e,n){super(),this.id="PLUGIN_LOAD",this.payload={name:t,config:i,configMeta:e,permissions:n||{}}}}class r extends n.m{constructor(t){super(),this.id="PLUGIN_UNLOAD",this.payload={name:t}}}class l extends n.m{constructor(t,i){super(),this.id="PLUGIN_CONFIG_FETCH_DATA",this.payload={operation:t,callback:i}}}class c extends n.m{constructor(t,i){super(),this.id="ATTACHMENT_ASSOCIATE_WITH_PLUGIN",this.payload={attachmentId:t,pluginId:i}}}},14057:(t,i,e)=>{e.r(i),e.d(i,{FetchLevel:()=>n.u,PluginConfigData:()=>n.i,default:()=>S,getPluginMetadataUrl:()=>f,getPluginUrl:()=>p});var n=e(23748),s=e(97542),o=e(92810),a=e(3907);const r="0.0";function l(t){if(!t)return[];const i={[r]:c}["0.0"];if(!i)throw new Error(`[PluginConfigDeserializer] Data with version "${t.version}": not recognized.`);return i(t)}function c(t){return t["0.0"]}const u="0.0";function d(t){const i={[u]:g};if(!t)throw new Error("[PluginConfigSerializer] no data to serialize.");const e=i["0.0"];if(!e)throw new Error('[PluginConfigSerializer] Version "0.0" not recognized.');return e(t)}function g(t){return{[u]:t}}class h extends a.MU{constructor(t,i,e){super({queue:t,path:`${i}/api/v1/jsonstore/model/plugins/${e}`,batchUpdate:!0,deserialize:l,serialize:d})}}function p(t,i,e){return e+`${t}/${i}/${t}.js`}function f(t,i,e){return e+`${t}/${i}/plugin.json`}const m=t=>{var i,e,n;return null!==(n=null===(e=null===(i=null==t?void 0:t.src)||void 0===i?void 0:i.match(/(\d+\.\d+\.\d+)\/[^\/]*\.js$/))||void 0===e?void 0:e[1])&&void 0!==n?n:null};var v=e(44443),y=e(8807),P=e(7162),b=e(36625),C=e(80742),w=e(62576),D=e(59279);const U="unknown-app-key";class S extends s.Y{constructor(){super(...arguments),this.name="plugin-config",this._registryLoaded=!1}get serviceSdkKey(){if(!this._applicationKey)throw new Error("[PluginConfigData] service key has not yet been set.");return this._applicationKey}get canOverrideStrict(){var t,i;return null===(i=null===(t=this._config)||void 0===t?void 0:t.pluginPolicies)||void 0===i?void 0:i.canDebug}get registryLoaded(){return this._registryLoaded}async init(t,i){this.queue=t.queue,this.pluginConfigData=new n.i,this._config=t;if([this._policyData,this._layersData]=await Promise.all([i.market.waitForData(y.n),i.market.waitForData(C.R)]),t.pluginPolicies.enabled){const e=(await i.getModuleBySymbol(o.Vs)).getApi(),n=await e.getAppKey("showcase","plugin");if(n instanceof Object){const i=n;await this.initializePluginRegistry(i,t),await this.setupConfigStore(t.baseUrl,t.modelId,false),this._registryLoaded=!0}}i.commandBinder.addBinding(w.Bl,(async t=>{var i,e;const n=(await this.pluginConfigData.getMdsResult()).find((i=>i.name===t.pluginId)),s=null!==(e=null===(i=null==n?void 0:n.attachments)||void 0===i?void 0:i.map((t=>t.id)))&&void 0!==e?e:[];this.pluginConfigData.updateMds({name:t.pluginId,attachments:[...s,t.attachmentId]})})),i.market.register(this,n.i,this.pluginConfigData)}async saveToMds(t){var i;if(!this.pluginConfigData.mdsIsSetup)return void this.log.warn("Plugin changes will NOT be saved");const e=null!==(i=m(t))&&void 0!==i?i:"0.0.0";if(!this._manifest.find((i=>i.name===t.id&&i.versions[e])))return void this.log.warn(`Version ${e} does not exist in registry. Changes not saved to MDS.`);const n=[];t.config.photoUrl&&n.push(t.config.photoUrl),t.config.logoUrl&&n.push(t.config.logoUrl);const s={name:t.id,version:e};n.length>0&&(s.attachments=n),this.pluginConfigData.updateMds(s)}deleteFromMds(t){this.pluginConfigData.mdsIsSetup?this.pluginConfigData.deleteMdsById(t.id):this.log.warn("Plugin changes will NOT be saved")}saveConfig(t,i){const e=this.pluginConfigData.lastSavedConfiguration.values();this.log.debugInfo(`configuration for ${t.id} updated. ${JSON.stringify(e,void 0,2)}`),t.enabled?this.saveToMds(t):this.deleteFromMds(t),this.currentStore.update(e)}setupConfigStore(t,i,e){this.currentStore=new h(this.queue,t,i),this.pluginConfigData.setupConfigStore(this._layersData.mdsContext,e,t);return this.currentStore.read().then((t=>{t||(t=[]),this.log.debugInfo(`Saved configuration data loaded for ${t.length} plugin(s). ${JSON.stringify(t,void 0,2)}`),this.pluginConfigData.lastSavedConfiguration.replace(t),this.pluginConfigData.lastSavedConfiguration.onElementChanged({onAdded:this.saveConfig.bind(this),onUpdated:this.saveConfig.bind(this),onRemoved:this.deleteFromMds.bind(this)})})).catch((t=>this.log.error("Failed to load configured plugins: ",t)))}getAutoUpgradedVersion(t,i){var e;const n=Object.keys(i.versions).sort(((t,i)=>P.o.compare(i,t))).filter((t=>this.hasRequiredPolicies(i.versions[t].requiredPolicies))),s=null!==(e=b.maxSatisfying(n,`~${t}`))&&void 0!==e?e:t,o=i.currentVersion;let a=s;return b.gt(s,o)&&(a=o),a}dispose(t){super.dispose(t),t.market.unregister(this,n.i),this.pluginConfigData=void 0}async initializePluginRegistry(t,i){const{manifestUrl:e,applicationKey:n}=this.getManifestUrl(t,i);this._applicationKey=n;const s=await this.queue.get(e,{responseType:"json"}).catch((t=>(this.log.error(t),null)));null!==s?(this._manifest=s,await this.populateFromManifest(s,t,n)):this.log.error("Plugin manifest could not be found, please contact support.")}getManifestUrl(t,i,e=!0){let n=t.manifestUrl,s=t.applicationKey;return this.pluginConfigData.manifestUrl&&((0,v.mM)(this.pluginConfigData.manifestUrl)||i.pluginPolicies.canDebug)&&(n=this.pluginConfigData.manifestUrl),n.match(/localhost/)&&e&&(i.pluginPolicies.canDebug||(s=U)),{manifestUrl:n,applicationKey:s}}async populateFromManifest(t,i,e){const n=[];for(const s of t)n.push(this.registerManifestEntry(s,i,e));await Promise.all(n)}async registerManifestEntry(t,i,e){var n,s,o;const a=this.findLatestPermittedVersion(t.versions,t.currentVersion);if(!a)return;t.currentVersion=a;const r=Object.assign(Object.assign({},t),{src:t.src||p(t.name,t.currentVersion,i.baseUrl),meta:t.meta||f(t.name,t.currentVersion,i.baseUrl),icon:t.icon||"",applicationKey:t.applicationKey||e||U,fetchLevel:null!==(n=t.fetchLevel)&&void 0!==n?n:this.pluginConfigData.defaultFetchLevel});if(!(0,v.mM)(r.src)||!(0,v.mM)(r.meta))return;const l=await this.queue.get(r.meta,{responseType:"json"}).catch((t=>{this.log.error(t)}));var c,u,d;l&&this.pluginConfigData.add({name:r.name,description:l.description,version:r.currentVersion,config:l.config||{},outputs:l.outputs||{},applicationKey:r.applicationKey,src:r.src,meta:r.meta,icon:(null===(s=l.options)||void 0===s?void 0:s.icon)?r.icon||(c=t.name,u=t.currentVersion,d=i.baseUrl,d+`${c}/${u}/${c}.svg`):void 0,enabled:!1,strict:this.canOverrideStrict&&null!==this.pluginConfigData.defaultStrict?this.pluginConfigData.defaultStrict:r.sesStrict,fetchLevel:null!==(o=r.fetchLevel)&&void 0!==o?o:this.pluginConfigData.defaultFetchLevel,options:l.options})}findLatestPermittedVersion(t,i){const e=Object.keys(t).sort(((t,i)=>P.o.compare(i,t)));for(const n of e){const e=t[n].requiredPolicies;if(!P.o.gt(n,i)&&this.hasRequiredPolicies(e))return n}return null}hasRequiredPolicies(t){if(!t||0===t.length)return!0;const i=this._config.pluginPolicies.groups||[],e=t=>-1===t.indexOf(".")?-1!==i.indexOf(t):this._policyData.hasPolicy(t);return t.reduce(((t,i)=>{if(!t)return t;if(i instanceof Object){if("or"===i.operator)return i.policies.some((t=>e(t)));if("xor"===i.operator){let t=0;return i.policies.forEach((i=>{t+=e(i)?1:0})),1===t}return this.log.warn(`unrecognized required policy entry, operator: <${i.operator}> - plugin disabled`),!1}return e(i)}),!0)}async getConfiguredPlugins(){const t=await this.currentStore.read()||[],i=[],e=(0,D.eY)("mls",0);return t.forEach((t=>{var s,o,a;if(t.enabled){const r=this.pluginConfigData.availablePlugins.get(t.id);let l=!(r&&(!r||void 0!==r.strict))||r.strict;if(!r)return this.log.warn(`"${t.id}" plugin not found in current plugin manifest -- was it configured with a different one?`),void this.log.warn(`Unrecognized plugin disallowed "${null==t?void 0:t.id}" cannot load from ${null==t?void 0:t.src}`);const c={config:t.config||{},src:t.src||r.src,meta:t.meta||r.meta,id:t.id||r.name,strict:l,applicationKey:(null==r?void 0:r.applicationKey)||"FAKE_APP_KEY",fetchLevel:null!==(s=null==r?void 0:r.fetchLevel)&&void 0!==s?s:n.u.None};if(e){if(!c.meta)return void this.log.warn(`MLS mode requires plugin meta. Plugin meta URL missing from plugin "${null==t?void 0:t.id}"`);if(!(null===(o=r.options)||void 0===o?void 0:o.mlsEnabled))return void this.log.info(`Plugin "${null==t?void 0:t.id}" not allowed in MLS mode.`)}{const t=m(c),i=this._manifest.find((t=>t.name===c.id));if(t&&i){const e=this.getAutoUpgradedVersion(t,i);null!==e&&e!==t&&(this.log.debugInfo(`Replacing ${i.name} version ${t} with version ${e}`),c.src=c.src.replace(t,e),c.meta=null===(a=c.meta)||void 0===a?void 0:a.replace(t,e))}else this.log.debugInfo(`Missing config version or manifest entry for ${c.id}. Not auto-updating patch for this plugin.`)}i.push(c)}})),i}}},3907:(t,i,e)=>{e.d(i,{MU:()=>r});var n,s=e(39880),o=e(44584);!function(t){t.GET="GET",t.POST="POST",t.PATCH="PATCH",t.PUT="PUT",t.DELETE="DELETE",t.OPTIONS="OPTIONS"}(n||(n={}));class a extends class{constructor(){this._options={responseType:"json"}}get options(){const t=this._options;return t.headers=(0,o.m)(this.url,this._options.headers||{}),t}}{constructor(t){super(),this.config=t,this.url=t.path}async read(){const{deserialize:t}=this.config;let i=null;return this.config.cachedData&&this.config.cachedData.data?i=this.config.cachedData.data:(i=await this.config.queue.get(this.config.path,this.options),this.config.cachedData&&(this.config.cachedData.data=i)),t(i)}clearCache(){this.config.cachedData&&(this.config.cachedData.data=null)}}class r extends a{constructor(t){super(t),this.config=t,this.acceptsPartial=!1,this.config.batchUpdate="batchUpdate"in this.config&&this.config.batchUpdate}async create(t){throw Error("Not implemented")}updateBatch(t,i){const{serialize:e}=this.config,s=[],o=[...new Set([...Object.keys(t),...Object.keys(i)])];for(const e of o){t[e]||i[e]||s.push(this.config.queue.delete(`${this.config.path}/${e}`,this.options))}const a=e(t,i),r=Object.assign(Object.assign({},this.options),{body:a});return s.push(this.config.queue.request(this.config.httpMethod||n.POST,this.config.path,r)),Promise.all(s)}updateInternal(t,i){const{serialize:e}=this.config,o=[],a=Object.assign({},this.options),r=Object.keys(t),l=Object.keys(i),c=(0,s.XN)(r.concat(l));for(const s in c){const r=c[s],l=t[r]||i[r];if(l){const t={};t[r]=l;const s={},c=i[r];c&&(s[r]=c);const u=e(t,s);a.body=u,o.push(this.config.queue.request(this.config.httpMethod||n.POST,this.config.path,a))}else o.push(this.config.queue.delete(`${this.config.path}/${r}`,this.options))}return Promise.all(o)}async update(t,i){this.clearCache(),await(this.config.batchUpdate?this.updateBatch(t,i||{}):this.updateInternal(t,i||{}))}async delete(t){throw Error("Not implemented")}}}}]);