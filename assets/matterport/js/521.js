/*! For license information please see 521.js.LICENSE.txt */
"use strict";(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[521],{1158:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var n=a(53613),l=a(61864),r=a(53203),i=a(14564),o=a(64210),s=a(92810);const u=async function(e){const t=await e.getModuleBySymbol(l.Ak),a=await e.getModuleBySymbol(s.Ve),u=t.addPanel(n.s.TITLE,n.s.HOTKEYS,{width:350});await t.loadPromise.then((()=>{const n="meshtextures";[{panel:u,header:n,setting:"disableTextureStreamBelowLod",initialValue:()=>-1,onChange:e=>{e>-1&&a.textureQualityMap.limitStreamingBelow(e)},urlParam:!0,rangePrecision:0,range:[-1,7]},{panel:u,header:n,setting:"textureStreamPause",initialValue:()=>r.ZP.debugPauseTexStream,onChange:e=>{r.ZP.debugPauseTexStream=e},urlParam:!0},{panel:u,header:n,setting:"textureStreamRaycastHits",initialValue:()=>r.ZP.debugLOD,onChange:e=>{r.ZP.debugLOD=e,e||(0,o.dw)()},urlParam:!0},{panel:u,header:n,setting:"debugRTTQuality",initialValue:()=>r.ZP.debugRttQuality,onChange:t=>{r.ZP.debugRttQuality=t,t||e.commandBinder.issueCommand(new i.u({color:null},{style:i.u.selectBy.all}))},urlParam:!0},{panel:u,header:n,setting:"debugRTTScores",initialValue:()=>r.ZP.debugRttScores,onChange:t=>{r.ZP.debugRttScores=t,t||e.commandBinder.issueCommand(new i.u({color:null},{style:i.u.selectBy.all}))},urlParam:!0}].forEach((e=>t.registerMenuEntry(e)))}))}},50447:(e,t,a)=>{a.r(t),a.d(t,{default:()=>f,tiledMeshDebugMenu:()=>w});var n=a(81396),l=a(97998),r=a(61864),i=a(92810),o=a(53613),s=a(35895);function u(e,t=16){let a;const n=(0,s.k1)((()=>a=window.setInterval((()=>e()),t)),(()=>{a&&clearInterval(a)}));return n.cancel(),n}var d=a(59279),g=a(39880),c=a(92558),m=a(69984),h=a(947),p=a(51524),y=a(53203);const M=new l.Z("tiled-mesh"),b={hideMenu:"1"!==(0,d.eY)("dmenu","0"),debug:"1"===(0,d.eY)("debugTiles","0")||"1"===(0,d.eY)("debug-tiles","0"),statsTiles:!1,statsTileset:!0,statsTextures:!0,statsTextureStream:!0};let T=null;function x(e,t,a,n){T||(T=document.createElement("div"),T.style.color="#FFFFFF",T.style.fontFamily="Roboto",T.style.fontWeight="300",T.style.fontSize="12px",T.style.position="absolute",T.style.top="85px",T.style.width="500px",T.style.pointerEvents="none",T.style.whiteSpace="pre",T.style.zIndex="99999",T.style.textShadow="0 0 1px black, 0 0 1px black, 0 0 1px black, 0 0 1px black",document.body.appendChild(T));let l=b.statsTileset?function(e,t){var a,n,l;const r=e.tilesRenderer,i=Object.values(null!==(a=r.tileSets)&&void 0!==a?a:[])[0];return`\ntileset: preset: ${null===(n=null==i?void 0:i.asset.extras)||void 0===n?void 0:n.preset}, depth: ${null===(l=null==i?void 0:i.asset.extras)||void 0===l?void 0:l.depth}, version: ${null==i?void 0:i.asset.tilesetVersion}\nview: errorTarget: ${r.errorTarget}, maxLOD: ${p.t.maxLOD}, detail: '${t.detail}'\n`}(e,n):"";l+=b.statsTiles?function(e,t,a){const n=e.tilesRenderer,l=n.visibleTiles,r={};l.forEach((e=>{var t;const a=`lod${null===(t=e.extras)||void 0===t?void 0:t.level}`,n=r[a]||0;r[a]=n+1}));const i=t.threeRenderer.info,{stats:o,downloadQueue:s,parseQueue:u,lruCache:d}=n,{active:g,downloading:c,inFrustum:m,parsing:h,used:y,visible:M}=o;return`\nthree:\n    drawCalls: ${i.render.calls}\n    geometries: ${i.memory.geometries}\n    textures: ${i.memory.textures}\n    triangles: ${i.render.triangles}\n    memory allocated (megs): ${Math.floor(t.estimatedGPUMemoryAllocated()/2**20)}\ntiles:\n    detail (per mode): ${a.detail}\n    maxLOD (current): ${p.t.maxLOD}\n    errorTarget: ${p.t.errorTarget}\n    tiles in frustum: ${m}\n    visible: ${M}`+Object.keys(r).sort().map((e=>`\n     ${e} tiles: ${r[e]||0}`)).join()+`\n    downloading gltf: ${c}\n    parsing gltf: ${h}\n    active: ${g}\n    used: ${y}\n    queues:\n      download: ${s.currJobs} running, ${s.items.length} waiting\n      parse: ${u.currJobs} running, ${u.items.length} waiting\n    lruCache: ${d.itemSet.size}\n`}(e,a,n):"",l+=b.statsTextureStream?function(e){const t="downloaded:\n"+Object.keys(e.totalTextures).map((t=>`    ${t}: ${e.totalTextures[t]} `)).join("\n")+"\n";return`textureStreaming:\n    downloadingTiles: ${e.downloadingTiles} / ${e.totalTiles}\n    downloadingTextures: ${e.loadingTextures}\n`+t}(t):"",T.textContent=l}let v=null;async function w(e){if(!b.debug)return;const t=await e.getModuleBySymbol(r.Ak),a=await e.getModuleBySymbol(i.PZ),n=await e.getModuleBySymbol(i.Aj),l=n.getScene(),s=await e.getModuleBySymbol(i.Ve),d=s.modelTextureLoader,h=s.modelMesh,T=s.modelMesh.tileLoader,w=t.tryGetProperty(y.iT,!1);function C(){v||(v=setInterval((()=>x(T,d,n,h)),150))}const f=t.addPanel(o.s.TITLE,o.s.HOTKEYS,{width:350}),V={viz:"visualize",stats:"stats",tile:"tileset",log:"log"},O=function(e,t,a,n,l){const r=P(a,l,((e,t)=>e.setWireframe(t))),i=function(e,t){const a={scale:1},n=(t,n)=>{if(!t)return;const l=e.container.tilesByChunkId.get(t.id),r=(null==l?void 0:l.__error)||1e-4,i=t.lod!==p.t.maxLOD&&r>p.t.errorTarget?1:.5,o=Math.max(0,Math.min(1,1-a.scale/r)),s=n?S(t.lod,o,i):null;t.setColorOverlay(s)},l=P(e,t,n),r=u((()=>l.colorize(e.container.chunks)));return{toggle:e=>{e?r.renew():r.cancel(),l.toggle(e)},colorize:l.colorize,subscription:r,config:a}}(a,l),o=P(a,l,((e,t)=>{e.setColorOverlay(t?S(e.lod,1,.5):null)})),s=P(a,l,((e,t)=>{e.setColorOverlay(t?(0,m.G1)(.5,e.id||0):null)})),d=P(a,l,((e,t)=>{var n;const l=a.container.tilesByChunkId.get(e.id);e.setColorOverlay(t?(0,m.G1)(.5,(0,c.un)((null===(n=null==l?void 0:l.content)||void 0===n?void 0:n.uri)||"missing")||0):null)})),g=P(a,l,((e,t)=>{e.setColorOverlay(t?(0,m.G1)(.5,(e.meshGroup<<16)+e.meshSubgroup||0):null)})),h=P(a,l,((e,t)=>{e.setColorOverlay(t?(0,m.G1)(.5,e.meshSubgroup||0):null)})),y=P(a,l,((e,t)=>{e.setColorOverlay(t?(0,m.G1)(.5,e.meshGroup||0):null)})),b=P(a,l,((e,t)=>{e.setColorOverlay(t?(0,m.G1)(.5,(0,c.un)(e.textureName)||0):null)})),T=P(a,l,((e,t)=>{const n=a.container.tilesByChunkId.get(e.id);e.setColorOverlay(t?(0,m.G1)(.5,(null==n?void 0:n.geometricError)||0):null)})),x=n.slots,v=n.textureQualityMap,w=P(a,l,((e,t)=>{const a=x.find((t=>t.textureName===e.textureName));if(a){const n=a.loading?1:a.quality>v.min(e.lod)?.7:.3,l=v.maxTexelSize/v.get(a.quality).texelSize;e.setColorOverlay(t?S(e.lod,l,n):null)}})),C=u((()=>w.colorize(a.container.chunks)));let f="none";const V={none:void 0,byError:i,byGeometricError:T,byTile:d,bySubgroup:h,byMeshgroup:y,bySubAndMeshgroup:g,byTexture:b,byStreamedTextures:{subscription:C,toggle:e=>{e?C.renew():C.cancel(),e&&M.info("colorize=byStreamedTextures solid color: loading, dark color: streamed, light color: basis"),w.toggle(e)}},byChunk:s,byLod:o};return[{panel:e,header:t.viz,setting:"disableTileUpdates",initialValue:()=>p.t.disableTileUpdates,onChange:e=>{p.t.disableTileUpdates=e},urlParam:!0},{panel:e,header:t.viz,setting:"wireframe",initialValue:()=>!1,onChange:r.toggle,urlParam:!0},{panel:e,header:t.viz,setting:"colorize",initialValue:()=>"none",onChange:e=>{var t,a;null===(t=V[f])||void 0===t||t.toggle(!1),null===(a=V[e])||void 0===a||a.toggle(!0),f=e},options:Object.keys(V),urlParam:!0},{panel:e,header:t.viz,setting:"colorizeByErrorScale",initialValue:()=>1,onChange:e=>{i.config.scale=e},range:[0,6],rangePrecision:3,urlParam:!0}]}(f,V,T,d,e),E=[{panel:f,header:V.stats,setting:"tilesetStatsOverlay",initialValue:()=>b.statsTileset,onChange:e=>{b.statsTileset=e,e&&C()},urlParam:!0},{panel:f,header:V.stats,setting:"tileStatsOverlay",initialValue:()=>b.statsTiles,onChange:e=>{b.statsTiles=e,e&&C()},urlParam:!0},{panel:f,header:V.stats,setting:"textureStatsOverlay",initialValue:()=>b.statsTextures,onChange:e=>{b.statsTextures=e,e&&C()},urlParam:!0},{panel:f,header:V.stats,setting:"textureStreamingOverlay",initialValue:()=>b.statsTextureStream,onChange:e=>{b.statsTextureStream=e,e&&C()},urlParam:!0}],L=[{panel:f,header:V.log,buttonName:"Log: App State",callback:()=>{M.warn(s),M.warn(a),M.warn(l),M.warn(t)}}];w&&(b.hideMenu||await(0,g.gw)(1e3).then((()=>{t.getSettingsGui().loadGuiPackage().then((()=>{t.getSettingsGui().toggle(t.getMainPanelId()),t.getSettingsGui().toggle(t.getMainPanelId())}))})),await(0,g.gw)(16),O.forEach((e=>t.registerMenuEntry(e))),await(0,g.gw)(16),function(e,t){return[{panel:e,header:t.tile,setting:"maxLOD",initialValue:()=>p.t.maxLOD,onChange:e=>{p.t.maxLOD=e},range:[0,4],rangePrecision:0,urlParam:!0},{panel:e,header:t.tile,setting:"nonMeshMaxLOD",initialValue:()=>p.t.nonMeshMaxLOD,onChange:e=>{p.t.nonMeshMaxLOD=e},range:[0,4],rangePrecision:0,urlParam:!0},{panel:e,header:t.tile,setting:"minLOD",initialValue:()=>p.t.minLOD,onChange:e=>{p.t.minLOD=e},range:[0,4],rangePrecision:0,urlParam:!0},{panel:e,header:t.tile,setting:"smallMeshThreshold",initialValue:()=>p.t.smallMeshThreshold,onChange:e=>{p.t.smallMeshThreshold=e},range:[0,100],rangePrecision:1,urlParam:!0},{panel:e,header:t.tile,setting:"errorTarget",initialValue:()=>p.t.errorTarget,onChange:e=>{p.t.errorTarget=e},range:[0,20],rangePrecision:1,urlParam:!0},{panel:e,header:t.tile,setting:"smallMeshErrorMultiplier",initialValue:()=>p.t.smallMeshErrorMultiplier,onChange:e=>{p.t.smallMeshErrorMultiplier=e},range:[.01,2],rangePrecision:2,urlParam:!0},{panel:e,header:t.tile,setting:"displayActiveTiles",initialValue:()=>p.t.displayActiveTiles,onChange:e=>{p.t.displayActiveTiles=e},urlParam:!0},{panel:e,header:t.tile,setting:"loadSiblings",initialValue:()=>p.t.loadSiblings,onChange:e=>{p.t.loadSiblings=e},urlParam:!0},{panel:e,header:t.tile,setting:"autoDisableRendererCulling",initialValue:()=>p.t.autoDisableRendererCulling,onChange:e=>{p.t.autoDisableRendererCulling=e},urlParam:!0},{panel:e,header:t.tile,setting:"stopAtEmptyTiles",initialValue:()=>p.t.stopAtEmptyTiles,onChange:e=>{p.t.stopAtEmptyTiles=e},urlParam:!0},{panel:e,header:t.tile,setting:"disableTileUpdates",initialValue:()=>p.t.disableTileUpdates,onChange:e=>{p.t.disableTileUpdates=e},urlParam:!0},{panel:e,header:t.tile,setting:"disposeModel",initialValue:()=>p.t.disposeModel,onChange:e=>{p.t.disposeModel=e},urlParam:!0},{panel:e,header:t.tile,setting:"limitMemoryUsage",initialValue:()=>p.t.limitMemoryUsage,onChange:e=>{p.t.limitMemoryUsage=e},urlParam:!0},{panel:e,header:t.tile,setting:"allocatedMegsBeforeLimitingLod",initialValue:()=>p.t.allocatedMegsBeforeLimitingLod,onChange:e=>{p.t.allocatedMegsBeforeLimitingLod=e},urlParam:!0,range:[100,1e3]},{panel:e,header:t.tile,setting:"lruMinExtraTiles",initialValue:()=>p.t.lruMinExtraTiles,onChange:e=>{p.t.lruMinExtraTiles=e},urlParam:!0,range:[0,2e3]},{panel:e,header:t.tile,setting:"lruMaxTiles",initialValue:()=>p.t.lruMaxTiles,onChange:e=>{p.t.lruMaxTiles=e},urlParam:!0,range:[0,2e3]},{panel:e,header:t.tile,setting:"lruUnloadPercent",initialValue:()=>p.t.lruUnloadPercent,onChange:e=>{p.t.lruUnloadPercent=e},urlParam:!0,range:[0,1]},{panel:e,header:"Priority",setting:"errorMultiplierRaycastOcclusion",initialValue:()=>p.t.errorMultiplierRaycastOcclusion,onChange:e=>{p.t.errorMultiplierRaycastOcclusion=e},urlParam:!0,range:[.001,1],rangePrecision:2},{panel:e,header:"Priority",setting:"errorMultiplierHiddenFloors",initialValue:()=>p.t.errorMultiplierHiddenFloors,onChange:e=>{p.t.errorMultiplierHiddenFloors=e},urlParam:!0,range:[.001,1],rangePrecision:2}]}(f,V).forEach((e=>t.registerMenuEntry(e))),E.forEach((e=>t.registerMenuEntry(e))),L.forEach((e=>t.registerMenuButton(e))))}function P(e,t,a){let n=!1;const l=e=>{t.after(h.A.End).then((()=>{e.forEach((e=>{e&&a(e,n)}))}))},r=e.notifyOnChunksLoaded(l);r.cancel();return{toggle:t=>{t?r.renew():r.cancel(),t!==n&&(n=t,l([...e.container.chunks]))},colorize:l,subscription:r}}const C={0:new n.Vector4(1,0,0,1),1:new n.Vector4(0,1,0,1),2:new n.Vector4(0,0,1,1),3:new n.Vector4(1,1,1,1),4:new n.Vector4(1,0,1,1),5:new n.Vector4(0,1,1,1),6:new n.Vector4(1,1,0,1),7:new n.Vector4(0,0,0,1)};function S(e,t,a){var l,r;const i=null!==(r=null===(l=C[e])||void 0===l?void 0:l.clone())&&void 0!==r?r:new n.Vector4;return i.multiplyScalar(t),i.setW(a),i}const f=w},53613:(e,t,a)=>{a.d(t,{s:()=>n});const n={TITLE:"streamed-mesh (T)",HOTKEYS:[a(32597).R.T]}},14564:(e,t,a)=>{a.d(t,{u:()=>i});var n,l,r=a(19663);!function(e){e.all="all",e.byMeshGroup="byMeshGroup",e.byMeshSubGroup="byMeshSubGroup"}(n||(n={})),function(e){e.explicit="explicit",e.random="random"}(l||(l={}));class i extends r.m{constructor(e,t){super(),this.id="SET_MESH_OVERLAY_COLOR",this.payload={selectBy:(null==t?void 0:t.style)||n.all,colorStyle:(null==e?void 0:e.style)||l.explicit,color:(null==e?void 0:e.color)||null,alpha:(null==e?void 0:e.alpha)||.5,index:null==t?void 0:t.index}}}i.selectBy=n,i.colorBy=l,i.COLOR_DIM={x:0,y:0,z:0,w:.3}},21270:(e,t,a)=>{var n;a.d(t,{V:()=>n}),function(e){e[e.Min=0]="Min",e[e.Standard=1]="Standard",e[e.High=2]="High",e[e.Detail=3]="Detail"}(n||(n={}))},51524:(e,t,a)=>{a.d(t,{t:()=>o});var n=a(59279),l=a(53261),r=a(31362),i=a(21270);const o={urlTemplateToken:"<file>",initialMaxLOD:i.V.Min,nonMeshMaxLOD:i.V.Standard,maxLOD:i.V.High,minLOD:i.V.Min,loadSiblings:!0,displayActiveTiles:!1,autoDisableRendererCulling:!0,optimizeRaycast:!1,stopAtEmptyTiles:!1,disableTileUpdates:!1,disposeModel:!1,limitMemoryUsage:(0,r.tq)(),allocatedMegsBeforeLimitingLod:350,lruMinExtraTiles:(0,r.tq)()?0:100,lruMaxTiles:800,lruUnloadPercent:.05,tileAssetRequestPriority:l.ru.MEDIUM,downloadQueueConcurrency:8,parseQueueConcurrency:10,snappingMaxLOD:i.V.Standard,errorTarget:Number((0,n.eY)("errorTarget",(0,r.tq)()?6:4)),errorMultiplierHiddenFloors:.01,errorMultiplierRaycastOcclusion:.1,smallMeshThreshold:Number((0,n.eY)("smallMeshThreshold",40)),smallMeshErrorMultiplier:Number((0,n.eY)("smallMeshErrorMultiplier",.1))}},64210:(e,t,a)=>{a.d(t,{O7:()=>m,dw:()=>g,ef:()=>d});var n=a(81396),l=a(53203),r=a(69984);const i=l.ZP.sightingMaxAge,o=new n.Color;let s,u=-1;const d=(e,t)=>{s||(s=new n.InstancedMesh(new n.SphereGeometry(.005,8,4),new n.MeshBasicMaterial,i),c(s));const a=new n.Matrix4;return({point:n,distance:l})=>{a.makeScale(l,l,l).setPosition(n),s.setMatrixAt(++u%i,a),s.instanceMatrix.needsUpdate=!0;for(let t=i;t--;)s.setColorAt((u-t+i)%i,o.set(e).multiplyScalar(1-t/i));s.instanceColor&&(s.instanceColor.needsUpdate=!0),s.parent||t.scene.add(s)}},g=()=>{var e;s&&(null===(e=s.parent)||void 0===e||e.remove(s),c(s))};function c(e){const t=(new n.Matrix4).makeScale(0,0,0);for(let a=0;a<i;a++)e.setMatrixAt(a,t)}function m(e,t){const a=t.get(e);if(a.lod in h){if(!(e in h)){const l=h[a.lod],r=.3+.6*Math.max(0,t.order(a.lod,!1).indexOf(e));h[e]=new n.Vector4(l.x,l.y,l.z,r)}return h[e]}return(0,r.G1)(.5,e)}const h={0:new n.Vector4(1,0,0,1),1:new n.Vector4(0,1,0,1),2:new n.Vector4(0,0,1,1),3:new n.Vector4(1,1,0,1),4:new n.Vector4(1,0,1,1),5:new n.Vector4(1,1,1,1),6:new n.Vector4(0,1,1,1),7:new n.Vector4(0,0,0,1)}},69984:(e,t,a)=>{a.d(t,{D5:()=>u,Ex:()=>d,G1:()=>o,rn:()=>s});var n=a(81396),l=a(39880);const r=()=>Math.random(),i={},o=(e,t=r())=>(i[t]||(i[t]=new n.Vector4(r(),r(),r(),e)),i[t]),s=()=>new n.Color(r(),r(),r()),u=e=>e instanceof Object&&"r"in e&&"g"in e&&"b"in e;function d(e){return`#${(0,l.Q_)(255*e.r,2,"0",16)}${(0,l.Q_)(255*e.g,2,"0",16)}${(0,l.Q_)(255*e.b,2,"0",16)}`}}}]);