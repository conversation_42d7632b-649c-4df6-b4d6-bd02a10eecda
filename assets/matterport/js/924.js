/*! For license information please see 924.js.LICENSE.txt */
"use strict";(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[924],{74924:(e,t,i)=>{i.r(t),i.d(t,{default:()=>Z});var a=i(97542),o=i(92810),s=i(12250),n=i(11442),r=i(89553),h=i(32597),d=i(6667),m=i(9037),l=i(18596),c=i(23254),u=i(53954),p=i(49827),w=i(69505),g=i(31546);const b=.1,y=.7,z=2,D=3,B=1.1;var S=i(33585),v=i(61948),T=i(76609);class Z extends a.Y{constructor(){super(...arguments),this.name="zoom-controls",this.uhQuality={},this.config={enabled:!0}}async init(e,t){this.config.enabled=!!e.enabled,this.sweepTilingModule=await t.getModuleBySymbol(o.RR),this.config.enabled&&await this.registerControls(t),this.engine=t,this.cameraModule=await t.getModuleBySymbol(o.kg),this.cameraData=await t.market.waitForData(m.M),this.viewmodeData=await t.market.waitForData(c.O),this.sweepData=await t.market.waitForData(u.Z),this.bindings.push(t.commandBinder.addBinding(S.bj,(async e=>this.zoomBy(e.step))),t.commandBinder.addBinding(S.KB,(async e=>this.zoomBy(-e.step))),t.commandBinder.addBinding(S.ob,(async()=>this.zoomTo(1))),t.commandBinder.addBinding(S.ts,(async e=>this.zoomTo(e.value))),t.commandBinder.addBinding(S._N,(async()=>Promise.resolve(this.getMaxZoomAvailable()))))}async registerControls(e){e.getModuleBySymbol(o.PZ).then((e=>{this.bindings.push(e.registerHandler(s.a,(e=>this.zoomByInput(this.scrollToZoomDelta(e))))),this.bindings.push(e.registerHandler(n.G,(e=>this.zoomByInput(this.pinchToZoomDelta(e))))),this.bindings.push(e.registerHandler(r.e,(e=>this.keyHandler(e))))}))}zoomTo(e){const t=this.cameraData.zoom();if(!this.validateViewmode())return t;const{currentSweep:i,currentSweepObject:a}=this.sweepData;if(a&&this.checkTilingZoomLevels(e,a),(e=(0,p.uZ)(e,y,this.getMaxZoom(i)))!==t){const t=(0,w.ZY)(this.cameraData.baseFovY/e),i=l.oR.near,a=l.oR.far;this.cameraModule.updateCameraProjection((new g.M).makePerspectiveFov(t,this.cameraData.aspect(),i,a))}return this.engine.broadcast(new v.Z(e)),e}checkTilingZoomLevels(e,t){if(e>=B&&this.zoomedSweep!==t){this.zoomedSweep=t;this.sweepTilingModule.enableZooming(!0,t.id)?this.uhQuality[t.id]=!0:(this.uhQuality[t.id]=!1,this.zoomedSweep=void 0)}else e<B&&this.zoomedSweep&&this.zoomedSweep===t&&(this.sweepTilingModule.enableZooming(!1,this.zoomedSweep.id),this.zoomedSweep=void 0)}getMaxZoom(e){return this.uhQuality[null!=e?e:"none"]?D:z}zoomBy(e){const t=this.cameraData.zoom();return this.validateViewmode()?this.zoomTo(t+e):t}validateViewmode(){return this.viewmodeData.isInside()&&this.cameraData.canTransition()}scrollToZoomDelta(e){return-Math.sign(e.delta.y)*b}pinchToZoomDelta(e){return e.pinchDelta*(z-y)}zoomByInput(e){const t=this.cameraData.zoom();if(!this.validateViewmode())return t;const i=this.sweepData.currentSweep,a=(0,p.uZ)(t+e,y,this.getMaxZoom(i));return this.zoomTo(a)}keyHandler(e){if(e.state===d.M.DOWN)switch(e.key){case h.R.PLUSEQUALS:this.zoomByInput(b);break;case h.R.DASHUNDERSCORE:this.zoomByInput(-b);break;case h.R.OPENBRACKET:this.zoomTo(1)}}getMaxZoomAvailable(){if(!this.sweepData.currentSweepObject)return z;return this.sweepData.currentSweepObject.availableResolution(T.SL.ULTRAHIGH)>=T.SL.ULTRAHIGH?D:z}}}}]);