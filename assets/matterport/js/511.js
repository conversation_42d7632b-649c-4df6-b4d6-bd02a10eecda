/*! For license information please see 511.js.LICENSE.txt */
"use strict";(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[511],{36447:(r,t,e)=>{e.d(t,{h:()=>i});var n=e(22427);class o{constructor(r){this.worker=r,this.lock=new n.H}async exec(r,t){const e=await this.lock.lock();return new Promise(((n,o)=>{const i=r=>{this.worker.removeEventListener("message",i),n(r.data),e.unlock()};this.worker.addEventListener("message",i),this.worker.onerror=r=>{o(r),e.unlock()},this.worker.postMessage(r,t)}))}}function i(r){return function(){return new o(new r)}}},65511:(r,t,e)=>{e.r(t),e.d(t,{createJpegDecodeWorker:()=>f,createJpegEncodeWorker:()=>a});var n=e(36447),o=e(20477),i=e.n(o);const a=(0,n.h)((function(){return i()('/*! For license information please see JPEGEncoder.worker.worker.js.LICENSE.txt */\n(()=>{var t={742:(t,r)=>{"use strict";r.byteLength=function(t){var r=a(t),e=r[0],n=r[1];return 3*(e+n)/4-n},r.toByteArray=function(t){var r,e,i=a(t),f=i[0],u=i[1],s=new o(function(t,r,e){return 3*(r+e)/4-e}(0,f,u)),h=0,p=u>0?f-4:f;for(e=0;e<p;e+=4)r=n[t.charCodeAt(e)]<<18|n[t.charCodeAt(e+1)]<<12|n[t.charCodeAt(e+2)]<<6|n[t.charCodeAt(e+3)],s[h++]=r>>16&255,s[h++]=r>>8&255,s[h++]=255&r;2===u&&(r=n[t.charCodeAt(e)]<<2|n[t.charCodeAt(e+1)]>>4,s[h++]=255&r);1===u&&(r=n[t.charCodeAt(e)]<<10|n[t.charCodeAt(e+1)]<<4|n[t.charCodeAt(e+2)]>>2,s[h++]=r>>8&255,s[h++]=255&r);return s},r.fromByteArray=function(t){for(var r,n=t.length,o=n%3,i=[],f=16383,u=0,a=n-o;u<a;u+=f)i.push(s(t,u,u+f>a?a:u+f));1===o?(r=t[n-1],i.push(e[r>>2]+e[r<<4&63]+"==")):2===o&&(r=(t[n-2]<<8)+t[n-1],i.push(e[r>>10]+e[r>>4&63]+e[r<<2&63]+"="));return i.join("")};for(var e=[],n=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=0,u=i.length;f<u;++f)e[f]=i[f],n[i.charCodeAt(f)]=f;function a(t){var r=t.length;if(r%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var e=t.indexOf("=");return-1===e&&(e=r),[e,e===r?0:4-e%4]}function s(t,r,n){for(var o,i,f=[],u=r;u<n;u+=3)o=(t[u]<<16&16711680)+(t[u+1]<<8&65280)+(255&t[u+2]),f.push(e[(i=o)>>18&63]+e[i>>12&63]+e[i>>6&63]+e[63&i]);return f.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},764:(t,r,e)=>{"use strict";var n=e(742),o=e(645),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;r.lW=a,r.h2=50;var f=2147483647;function u(t){if(t>f)throw new RangeError(\'The value "\'+t+\'" is invalid for option "size"\');var r=new Uint8Array(t);return Object.setPrototypeOf(r,a.prototype),r}function a(t,r,e){if("number"==typeof t){if("string"==typeof r)throw new TypeError(\'The "string" argument must be of type string. Received type number\');return p(t)}return s(t,r,e)}function s(t,r,e){if("string"==typeof t)return function(t,r){"string"==typeof r&&""!==r||(r="utf8");if(!a.isEncoding(r))throw new TypeError("Unknown encoding: "+r);var e=0|g(t,r),n=u(e),o=n.write(t,r);o!==e&&(n=n.slice(0,o));return n}(t,r);if(ArrayBuffer.isView(t))return function(t){if(q(t,Uint8Array)){var r=new Uint8Array(t);return y(r.buffer,r.byteOffset,r.byteLength)}return c(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(q(t,ArrayBuffer)||t&&q(t.buffer,ArrayBuffer))return y(t,r,e);if("undefined"!=typeof SharedArrayBuffer&&(q(t,SharedArrayBuffer)||t&&q(t.buffer,SharedArrayBuffer)))return y(t,r,e);if("number"==typeof t)throw new TypeError(\'The "value" argument must not be of type number. Received type number\');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return a.from(n,r,e);var o=function(t){if(a.isBuffer(t)){var r=0|l(t.length),e=u(r);return 0===e.length||t.copy(e,0,0,r),e}if(void 0!==t.length)return"number"!=typeof t.length||F(t.length)?u(0):c(t);if("Buffer"===t.type&&Array.isArray(t.data))return c(t.data)}(t);if(o)return o;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),r,e);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function h(t){if("number"!=typeof t)throw new TypeError(\'"size" argument must be of type number\');if(t<0)throw new RangeError(\'The value "\'+t+\'" is invalid for option "size"\')}function p(t){return h(t),u(t<0?0:0|l(t))}function c(t){for(var r=t.length<0?0:0|l(t.length),e=u(r),n=0;n<r;n+=1)e[n]=255&t[n];return e}function y(t,r,e){if(r<0||t.byteLength<r)throw new RangeError(\'"offset" is outside of buffer bounds\');if(t.byteLength<r+(e||0))throw new RangeError(\'"length" is outside of buffer bounds\');var n;return n=void 0===r&&void 0===e?new Uint8Array(t):void 0===e?new Uint8Array(t,r):new Uint8Array(t,r,e),Object.setPrototypeOf(n,a.prototype),n}function l(t){if(t>=f)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+f.toString(16)+" bytes");return 0|t}function g(t,r){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||q(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError(\'The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type \'+typeof t);var e=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===e)return 0;for(var o=!1;;)switch(r){case"ascii":case"latin1":case"binary":return e;case"utf8":case"utf-8":return _(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*e;case"hex":return e>>>1;case"base64":return z(t).length;default:if(o)return n?-1:_(t).length;r=(""+r).toLowerCase(),o=!0}}function w(t,r,e){var n=!1;if((void 0===r||r<0)&&(r=0),r>this.length)return"";if((void 0===e||e>this.length)&&(e=this.length),e<=0)return"";if((e>>>=0)<=(r>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return R(this,r,e);case"utf8":case"utf-8":return T(this,r,e);case"ascii":return C(this,r,e);case"latin1":case"binary":return O(this,r,e);case"base64":return L(this,r,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,r,e);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function v(t,r,e){var n=t[r];t[r]=t[e],t[e]=n}function d(t,r,e,n,o){if(0===t.length)return-1;if("string"==typeof e?(n=e,e=0):e>2147483647?e=2147483647:e<-2147483648&&(e=-2147483648),F(e=+e)&&(e=o?0:t.length-1),e<0&&(e=t.length+e),e>=t.length){if(o)return-1;e=t.length-1}else if(e<0){if(!o)return-1;e=0}if("string"==typeof r&&(r=a.from(r,n)),a.isBuffer(r))return 0===r.length?-1:b(t,r,e,n,o);if("number"==typeof r)return r&=255,"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,r,e):Uint8Array.prototype.lastIndexOf.call(t,r,e):b(t,[r],e,n,o);throw new TypeError("val must be string, number or Buffer")}function b(t,r,e,n,o){var i,f=1,u=t.length,a=r.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||r.length<2)return-1;f=2,u/=2,a/=2,e/=2}function s(t,r){return 1===f?t[r]:t.readUInt16BE(r*f)}if(o){var h=-1;for(i=e;i<u;i++)if(s(t,i)===s(r,-1===h?0:i-h)){if(-1===h&&(h=i),i-h+1===a)return h*f}else-1!==h&&(i-=i-h),h=-1}else for(e+a>u&&(e=u-a),i=e;i>=0;i--){for(var p=!0,c=0;c<a;c++)if(s(t,i+c)!==s(r,c)){p=!1;break}if(p)return i}return-1}function m(t,r,e,n){e=Number(e)||0;var o=t.length-e;n?(n=Number(n))>o&&(n=o):n=o;var i=r.length;n>i/2&&(n=i/2);for(var f=0;f<n;++f){var u=parseInt(r.substr(2*f,2),16);if(F(u))return f;t[e+f]=u}return f}function A(t,r,e,n){return D(_(r,t.length-e),t,e,n)}function E(t,r,e,n){return D(function(t){for(var r=[],e=0;e<t.length;++e)r.push(255&t.charCodeAt(e));return r}(r),t,e,n)}function B(t,r,e,n){return D(z(r),t,e,n)}function U(t,r,e,n){return D(function(t,r){for(var e,n,o,i=[],f=0;f<t.length&&!((r-=2)<0);++f)n=(e=t.charCodeAt(f))>>8,o=e%256,i.push(o),i.push(n);return i}(r,t.length-e),t,e,n)}function L(t,r,e){return 0===r&&e===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(r,e))}function T(t,r,e){e=Math.min(t.length,e);for(var n=[],o=r;o<e;){var i,f,u,a,s=t[o],h=null,p=s>239?4:s>223?3:s>191?2:1;if(o+p<=e)switch(p){case 1:s<128&&(h=s);break;case 2:128==(192&(i=t[o+1]))&&(a=(31&s)<<6|63&i)>127&&(h=a);break;case 3:i=t[o+1],f=t[o+2],128==(192&i)&&128==(192&f)&&(a=(15&s)<<12|(63&i)<<6|63&f)>2047&&(a<55296||a>57343)&&(h=a);break;case 4:i=t[o+1],f=t[o+2],u=t[o+3],128==(192&i)&&128==(192&f)&&128==(192&u)&&(a=(15&s)<<18|(63&i)<<12|(63&f)<<6|63&u)>65535&&a<1114112&&(h=a)}null===h?(h=65533,p=1):h>65535&&(h-=65536,n.push(h>>>10&1023|55296),h=56320|1023&h),n.push(h),o+=p}return function(t){var r=t.length;if(r<=I)return String.fromCharCode.apply(String,t);var e="",n=0;for(;n<r;)e+=String.fromCharCode.apply(String,t.slice(n,n+=I));return e}(n)}a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),r={foo:function(){return 42}};return Object.setPrototypeOf(r,Uint8Array.prototype),Object.setPrototypeOf(t,r),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,r,e){return s(t,r,e)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,r,e){return function(t,r,e){return h(t),t<=0?u(t):void 0!==r?"string"==typeof e?u(t).fill(r,e):u(t).fill(r):u(t)}(t,r,e)},a.allocUnsafe=function(t){return p(t)},a.allocUnsafeSlow=function(t){return p(t)},a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,r){if(q(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),q(r,Uint8Array)&&(r=a.from(r,r.offset,r.byteLength)),!a.isBuffer(t)||!a.isBuffer(r))throw new TypeError(\'The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array\');if(t===r)return 0;for(var e=t.length,n=r.length,o=0,i=Math.min(e,n);o<i;++o)if(t[o]!==r[o]){e=t[o],n=r[o];break}return e<n?-1:n<e?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,r){if(!Array.isArray(t))throw new TypeError(\'"list" argument must be an Array of Buffers\');if(0===t.length)return a.alloc(0);var e;if(void 0===r)for(r=0,e=0;e<t.length;++e)r+=t[e].length;var n=a.allocUnsafe(r),o=0;for(e=0;e<t.length;++e){var i=t[e];if(q(i,Uint8Array))o+i.length>n.length?a.from(i).copy(n,o):Uint8Array.prototype.set.call(n,i,o);else{if(!a.isBuffer(i))throw new TypeError(\'"list" argument must be an Array of Buffers\');i.copy(n,o)}o+=i.length}return n},a.byteLength=g,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var r=0;r<t;r+=2)v(this,r,r+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var r=0;r<t;r+=4)v(this,r,r+3),v(this,r+1,r+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var r=0;r<t;r+=8)v(this,r,r+7),v(this,r+1,r+6),v(this,r+2,r+5),v(this,r+3,r+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?T(this,0,t):w.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",e=r.h2;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},i&&(a.prototype[i]=a.prototype.inspect),a.prototype.compare=function(t,r,e,n,o){if(q(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw new TypeError(\'The "target" argument must be one of type Buffer or Uint8Array. Received type \'+typeof t);if(void 0===r&&(r=0),void 0===e&&(e=t?t.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),r<0||e>t.length||n<0||o>this.length)throw new RangeError("out of range index");if(n>=o&&r>=e)return 0;if(n>=o)return-1;if(r>=e)return 1;if(this===t)return 0;for(var i=(o>>>=0)-(n>>>=0),f=(e>>>=0)-(r>>>=0),u=Math.min(i,f),s=this.slice(n,o),h=t.slice(r,e),p=0;p<u;++p)if(s[p]!==h[p]){i=s[p],f=h[p];break}return i<f?-1:f<i?1:0},a.prototype.includes=function(t,r,e){return-1!==this.indexOf(t,r,e)},a.prototype.indexOf=function(t,r,e){return d(this,t,r,e,!0)},a.prototype.lastIndexOf=function(t,r,e){return d(this,t,r,e,!1)},a.prototype.write=function(t,r,e,n){if(void 0===r)n="utf8",e=this.length,r=0;else if(void 0===e&&"string"==typeof r)n=r,e=this.length,r=0;else{if(!isFinite(r))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");r>>>=0,isFinite(e)?(e>>>=0,void 0===n&&(n="utf8")):(n=e,e=void 0)}var o=this.length-r;if((void 0===e||e>o)&&(e=o),t.length>0&&(e<0||r<0)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var i=!1;;)switch(n){case"hex":return m(this,t,r,e);case"utf8":case"utf-8":return A(this,t,r,e);case"ascii":case"latin1":case"binary":return E(this,t,r,e);case"base64":return B(this,t,r,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,t,r,e);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var I=4096;function C(t,r,e){var n="";e=Math.min(t.length,e);for(var o=r;o<e;++o)n+=String.fromCharCode(127&t[o]);return n}function O(t,r,e){var n="";e=Math.min(t.length,e);for(var o=r;o<e;++o)n+=String.fromCharCode(t[o]);return n}function R(t,r,e){var n=t.length;(!r||r<0)&&(r=0),(!e||e<0||e>n)&&(e=n);for(var o="",i=r;i<e;++i)o+=Y[t[i]];return o}function S(t,r,e){for(var n=t.slice(r,e),o="",i=0;i<n.length-1;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}function x(t,r,e){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+r>e)throw new RangeError("Trying to access beyond buffer length")}function M(t,r,e,n,o,i){if(!a.isBuffer(t))throw new TypeError(\'"buffer" argument must be a Buffer instance\');if(r>o||r<i)throw new RangeError(\'"value" argument is out of bounds\');if(e+n>t.length)throw new RangeError("Index out of range")}function P(t,r,e,n,o,i){if(e+n>t.length)throw new RangeError("Index out of range");if(e<0)throw new RangeError("Index out of range")}function k(t,r,e,n,i){return r=+r,e>>>=0,i||P(t,0,e,4),o.write(t,r,e,n,23,4),e+4}function j(t,r,e,n,i){return r=+r,e>>>=0,i||P(t,0,e,8),o.write(t,r,e,n,52,8),e+8}a.prototype.slice=function(t,r){var e=this.length;(t=~~t)<0?(t+=e)<0&&(t=0):t>e&&(t=e),(r=void 0===r?e:~~r)<0?(r+=e)<0&&(r=0):r>e&&(r=e),r<t&&(r=t);var n=this.subarray(t,r);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUintLE=a.prototype.readUIntLE=function(t,r,e){t>>>=0,r>>>=0,e||x(t,r,this.length);for(var n=this[t],o=1,i=0;++i<r&&(o*=256);)n+=this[t+i]*o;return n},a.prototype.readUintBE=a.prototype.readUIntBE=function(t,r,e){t>>>=0,r>>>=0,e||x(t,r,this.length);for(var n=this[t+--r],o=1;r>0&&(o*=256);)n+=this[t+--r]*o;return n},a.prototype.readUint8=a.prototype.readUInt8=function(t,r){return t>>>=0,r||x(t,1,this.length),this[t]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(t,r){return t>>>=0,r||x(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(t,r){return t>>>=0,r||x(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(t,r){return t>>>=0,r||x(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(t,r){return t>>>=0,r||x(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,r,e){t>>>=0,r>>>=0,e||x(t,r,this.length);for(var n=this[t],o=1,i=0;++i<r&&(o*=256);)n+=this[t+i]*o;return n>=(o*=128)&&(n-=Math.pow(2,8*r)),n},a.prototype.readIntBE=function(t,r,e){t>>>=0,r>>>=0,e||x(t,r,this.length);for(var n=r,o=1,i=this[t+--n];n>0&&(o*=256);)i+=this[t+--n]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*r)),i},a.prototype.readInt8=function(t,r){return t>>>=0,r||x(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},a.prototype.readInt16LE=function(t,r){t>>>=0,r||x(t,2,this.length);var e=this[t]|this[t+1]<<8;return 32768&e?4294901760|e:e},a.prototype.readInt16BE=function(t,r){t>>>=0,r||x(t,2,this.length);var e=this[t+1]|this[t]<<8;return 32768&e?4294901760|e:e},a.prototype.readInt32LE=function(t,r){return t>>>=0,r||x(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,r){return t>>>=0,r||x(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,r){return t>>>=0,r||x(t,4,this.length),o.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,r){return t>>>=0,r||x(t,4,this.length),o.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,r){return t>>>=0,r||x(t,8,this.length),o.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,r){return t>>>=0,r||x(t,8,this.length),o.read(this,t,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(t,r,e,n){(t=+t,r>>>=0,e>>>=0,n)||M(this,t,r,e,Math.pow(2,8*e)-1,0);var o=1,i=0;for(this[r]=255&t;++i<e&&(o*=256);)this[r+i]=t/o&255;return r+e},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(t,r,e,n){(t=+t,r>>>=0,e>>>=0,n)||M(this,t,r,e,Math.pow(2,8*e)-1,0);var o=e-1,i=1;for(this[r+o]=255&t;--o>=0&&(i*=256);)this[r+o]=t/i&255;return r+e},a.prototype.writeUint8=a.prototype.writeUInt8=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,1,255,0),this[r]=255&t,r+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,2,65535,0),this[r]=255&t,this[r+1]=t>>>8,r+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,2,65535,0),this[r]=t>>>8,this[r+1]=255&t,r+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,4,4294967295,0),this[r+3]=t>>>24,this[r+2]=t>>>16,this[r+1]=t>>>8,this[r]=255&t,r+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,4,4294967295,0),this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t,r+4},a.prototype.writeIntLE=function(t,r,e,n){if(t=+t,r>>>=0,!n){var o=Math.pow(2,8*e-1);M(this,t,r,e,o-1,-o)}var i=0,f=1,u=0;for(this[r]=255&t;++i<e&&(f*=256);)t<0&&0===u&&0!==this[r+i-1]&&(u=1),this[r+i]=(t/f>>0)-u&255;return r+e},a.prototype.writeIntBE=function(t,r,e,n){if(t=+t,r>>>=0,!n){var o=Math.pow(2,8*e-1);M(this,t,r,e,o-1,-o)}var i=e-1,f=1,u=0;for(this[r+i]=255&t;--i>=0&&(f*=256);)t<0&&0===u&&0!==this[r+i+1]&&(u=1),this[r+i]=(t/f>>0)-u&255;return r+e},a.prototype.writeInt8=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,1,127,-128),t<0&&(t=255+t+1),this[r]=255&t,r+1},a.prototype.writeInt16LE=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,2,32767,-32768),this[r]=255&t,this[r+1]=t>>>8,r+2},a.prototype.writeInt16BE=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,2,32767,-32768),this[r]=t>>>8,this[r+1]=255&t,r+2},a.prototype.writeInt32LE=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,4,2147483647,-2147483648),this[r]=255&t,this[r+1]=t>>>8,this[r+2]=t>>>16,this[r+3]=t>>>24,r+4},a.prototype.writeInt32BE=function(t,r,e){return t=+t,r>>>=0,e||M(this,t,r,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t,r+4},a.prototype.writeFloatLE=function(t,r,e){return k(this,t,r,!0,e)},a.prototype.writeFloatBE=function(t,r,e){return k(this,t,r,!1,e)},a.prototype.writeDoubleLE=function(t,r,e){return j(this,t,r,!0,e)},a.prototype.writeDoubleBE=function(t,r,e){return j(this,t,r,!1,e)},a.prototype.copy=function(t,r,e,n){if(!a.isBuffer(t))throw new TypeError("argument should be a Buffer");if(e||(e=0),n||0===n||(n=this.length),r>=t.length&&(r=t.length),r||(r=0),n>0&&n<e&&(n=e),n===e)return 0;if(0===t.length||0===this.length)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(e<0||e>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-r<n-e&&(n=t.length-r+e);var o=n-e;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(r,e,n):Uint8Array.prototype.set.call(t,this.subarray(e,n),r),o},a.prototype.fill=function(t,r,e,n){if("string"==typeof t){if("string"==typeof r?(n=r,r=0,e=this.length):"string"==typeof e&&(n=e,e=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){var o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(r<0||this.length<r||this.length<e)throw new RangeError("Out of range index");if(e<=r)return this;var i;if(r>>>=0,e=void 0===e?this.length:e>>>0,t||(t=0),"number"==typeof t)for(i=r;i<e;++i)this[i]=t;else{var f=a.isBuffer(t)?t:a.from(t,n),u=f.length;if(0===u)throw new TypeError(\'The value "\'+t+\'" is invalid for argument "value"\');for(i=0;i<e-r;++i)this[i+r]=f[i%u]}return this};var N=/[^+/0-9A-Za-z-_]/g;function _(t,r){var e;r=r||1/0;for(var n=t.length,o=null,i=[],f=0;f<n;++f){if((e=t.charCodeAt(f))>55295&&e<57344){if(!o){if(e>56319){(r-=3)>-1&&i.push(239,191,189);continue}if(f+1===n){(r-=3)>-1&&i.push(239,191,189);continue}o=e;continue}if(e<56320){(r-=3)>-1&&i.push(239,191,189),o=e;continue}e=65536+(o-55296<<10|e-56320)}else o&&(r-=3)>-1&&i.push(239,191,189);if(o=null,e<128){if((r-=1)<0)break;i.push(e)}else if(e<2048){if((r-=2)<0)break;i.push(e>>6|192,63&e|128)}else if(e<65536){if((r-=3)<0)break;i.push(e>>12|224,e>>6&63|128,63&e|128)}else{if(!(e<1114112))throw new Error("Invalid code point");if((r-=4)<0)break;i.push(e>>18|240,e>>12&63|128,e>>6&63|128,63&e|128)}}return i}function z(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(N,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function D(t,r,e,n){for(var o=0;o<n&&!(o+e>=r.length||o>=t.length);++o)r[o+e]=t[o];return o}function q(t,r){return t instanceof r||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===r.name}function F(t){return t!=t}var Y=function(){for(var t="0123456789abcdef",r=new Array(256),e=0;e<16;++e)for(var n=16*e,o=0;o<16;++o)r[n+o]=t[e]+t[o];return r}()},645:(t,r)=>{r.read=function(t,r,e,n,o){var i,f,u=8*o-n-1,a=(1<<u)-1,s=a>>1,h=-7,p=e?o-1:0,c=e?-1:1,y=t[r+p];for(p+=c,i=y&(1<<-h)-1,y>>=-h,h+=u;h>0;i=256*i+t[r+p],p+=c,h-=8);for(f=i&(1<<-h)-1,i>>=-h,h+=n;h>0;f=256*f+t[r+p],p+=c,h-=8);if(0===i)i=1-s;else{if(i===a)return f?NaN:1/0*(y?-1:1);f+=Math.pow(2,n),i-=s}return(y?-1:1)*f*Math.pow(2,i-n)},r.write=function(t,r,e,n,o,i){var f,u,a,s=8*i-o-1,h=(1<<s)-1,p=h>>1,c=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,y=n?0:i-1,l=n?1:-1,g=r<0||0===r&&1/r<0?1:0;for(r=Math.abs(r),isNaN(r)||r===1/0?(u=isNaN(r)?1:0,f=h):(f=Math.floor(Math.log(r)/Math.LN2),r*(a=Math.pow(2,-f))<1&&(f--,a*=2),(r+=f+p>=1?c/a:c*Math.pow(2,1-p))*a>=2&&(f++,a/=2),f+p>=h?(u=0,f=h):f+p>=1?(u=(r*a-1)*Math.pow(2,o),f+=p):(u=r*Math.pow(2,p-1)*Math.pow(2,o),f=0));o>=8;t[e+y]=255&u,y+=l,u/=256,o-=8);for(f=f<<o|u,s+=o;s>0;t[e+y]=255&f,y+=l,f/=256,s-=8);t[e+y-l]|=128*g}}},r={};function e(n){var o=r[n];if(void 0!==o)return o.exports;var i=r[n]={exports:{}};return t[n](i,i.exports,e),i.exports}(()=>{"use strict";var t=e(764).lW;function r(r){Math.round;var e,n,o,i,f,u=Math.floor,a=new Array(64),s=new Array(64),h=new Array(64),p=new Array(64),c=new Array(65535),y=new Array(65535),l=new Array(64),g=new Array(64),w=[],v=0,d=7,b=new Array(64),m=new Array(64),A=new Array(64),E=new Array(256),B=new Array(2048),U=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],L=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],T=[0,1,2,3,4,5,6,7,8,9,10,11],I=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],C=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],O=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],R=[0,1,2,3,4,5,6,7,8,9,10,11],S=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],x=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function M(t,r){for(var e=0,n=0,o=new Array,i=1;i<=16;i++){for(var f=1;f<=t[i];f++)o[r[n]]=[],o[r[n]][0]=e,o[r[n]][1]=i,n++,e++;e*=2}return o}function P(t){for(var r=t[0],e=t[1]-1;e>=0;)r&1<<e&&(v|=1<<d),e--,--d<0&&(255==v?(k(255),k(0)):k(v),d=7,v=0)}function k(t){w.push(t)}function j(t){k(t>>8&255),k(255&t)}function N(t){t&&(j(65505),j(t.length+2),function(t){for(var r=0,e=t.length;r<e;r++)k(t.charCodeAt(r))}(t))}function _(t,r,e,n,o){for(var i,f=o[0],u=o[240],a=function(t,r){var e,n,o,i,f,u,a,s,h,p,c=0;for(h=0;h<8;++h){e=t[c],n=t[c+1],o=t[c+2],i=t[c+3],f=t[c+4],u=t[c+5],a=t[c+6];var y=e+(s=t[c+7]),g=e-s,w=n+a,v=n-a,d=o+u,b=o-u,m=i+f,A=i-f,E=y+m,B=y-m,U=w+d,L=w-d;t[c]=E+U,t[c+4]=E-U;var T=.707106781*(L+B);t[c+2]=B+T,t[c+6]=B-T;var I=.382683433*((E=A+b)-(L=v+g)),C=.5411961*E+I,O=1.306562965*L+I,R=.707106781*(U=b+v),S=g+R,x=g-R;t[c+5]=x+C,t[c+3]=x-C,t[c+1]=S+O,t[c+7]=S-O,c+=8}for(c=0,h=0;h<8;++h){e=t[c],n=t[c+8],o=t[c+16],i=t[c+24],f=t[c+32],u=t[c+40],a=t[c+48];var M=e+(s=t[c+56]),P=e-s,k=n+a,j=n-a,N=o+u,_=o-u,z=i+f,D=i-f,q=M+z,F=M-z,Y=k+N,W=k-N;t[c]=q+Y,t[c+32]=q-Y;var V=.707106781*(W+F);t[c+16]=F+V,t[c+48]=F-V;var J=.382683433*((q=D+_)-(W=j+P)),Z=.5411961*q+J,G=1.306562965*W+J,H=.707106781*(Y=_+j),K=P+H,Q=P-H;t[c+40]=Q+Z,t[c+24]=Q-Z,t[c+8]=K+G,t[c+56]=K-G,c++}for(h=0;h<64;++h)p=t[h]*r[h],l[h]=p>0?p+.5|0:p-.5|0;return l}(t,r),s=0;s<64;++s)g[U[s]]=a[s];var h=g[0]-e;e=g[0],0==h?P(n[0]):(P(n[y[i=32767+h]]),P(c[i]));for(var p=63;p>0&&0==g[p];p--);if(0==p)return P(f),e;for(var w,v=1;v<=p;){for(var d=v;0==g[v]&&v<=p;++v);var b=v-d;if(b>=16){w=b>>4;for(var m=1;m<=w;++m)P(u);b&=15}i=32767+g[v],P(o[(b<<4)+y[i]]),P(c[i]),v++}return 63!=p&&P(f),e}function z(t){if(t<=0&&(t=1),t>100&&(t=100),f!=t){(function(t){for(var r=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],e=0;e<64;e++){var n=u((r[e]*t+50)/100);n<1?n=1:n>255&&(n=255),a[U[e]]=n}for(var o=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],i=0;i<64;i++){var f=u((o[i]*t+50)/100);f<1?f=1:f>255&&(f=255),s[U[i]]=f}for(var c=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],y=0,l=0;l<8;l++)for(var g=0;g<8;g++)h[y]=1/(a[U[y]]*c[l]*c[g]*8),p[y]=1/(s[U[y]]*c[l]*c[g]*8),y++})(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),f=t}}this.encode=function(r,f){(new Date).getTime();var u=f.quality;u&&z(u),w=new Array,v=0,d=7,j(65496),j(65504),j(16),k(74),k(70),k(73),k(70),k(0),k(1),k(1),k(0),j(1),j(1),k(0),k(0),N(f.exif),function(){j(65499),j(132),k(0);for(var t=0;t<64;t++)k(a[t]);k(1);for(var r=0;r<64;r++)k(s[r])}(),function(t,r){j(65472),j(17),k(8),j(r),j(t),k(3),k(1),k(17),k(0),k(2),k(17),k(1),k(3),k(17),k(1)}(r.width,r.height),function(){j(65476),j(418),k(0);for(var t=0;t<16;t++)k(L[t+1]);for(var r=0;r<=11;r++)k(T[r]);k(16);for(var e=0;e<16;e++)k(I[e+1]);for(var n=0;n<=161;n++)k(C[n]);k(1);for(var o=0;o<16;o++)k(O[o+1]);for(var i=0;i<=11;i++)k(R[i]);k(17);for(var f=0;f<16;f++)k(S[f+1]);for(var u=0;u<=161;u++)k(x[u])}(),j(65498),j(12),k(3),k(1),k(0),k(2),k(17),k(3),k(17),k(0),k(63),k(0);var c=0,y=0,l=0;v=0,d=7,this.encode.displayName="_encode_";for(var g,E,U,M,D,q,F,Y,W,V=r.data,J=r.width,Z=r.height,G=f.flipY,H=4*J,K=G?Z:0,Q=G?-8:8;G&&K>0||!G&&K<Z;){for(g=0;g<H;){for(q=D=H*K+g,F=-1,Y=0,W=0;W<64;W++)q=D+(Y=(G?63-W:W)>>3)*H+(F=4*(7&W)),K+Y>=Z&&(q-=H*(K+1+Y-Z)),g+F>=H&&(q-=g+F-H+4),E=V[q++],U=V[q++],M=V[q++],b[W]=(B[E]+B[U+256>>0]+B[M+512>>0]>>16)-128,m[W]=(B[E+768>>0]+B[U+1024>>0]+B[M+1280>>0]>>16)-128,A[W]=(B[E+1280>>0]+B[U+1536>>0]+B[M+1792>>0]>>16)-128;c=_(b,h,c,e,o),y=_(m,p,y,n,i),l=_(A,p,l,n,i),g+=32}K+=Q}if(d>=0){var X=[];X[1]=d+1,X[0]=(1<<d+1)-1,P(X)}return j(65497),new t(w)},function(){(new Date).getTime();r||(r=50),function(){for(var t=String.fromCharCode,r=0;r<256;r++)E[r]=t(r)}(),e=M(L,T),n=M(O,R),o=M(I,C),i=M(S,x),function(){for(var t=1,r=2,e=1;e<=15;e++){for(var n=t;n<r;n++)y[32767+n]=e,c[32767+n]=[],c[32767+n][1]=e,c[32767+n][0]=n;for(var o=-(r-1);o<=-t;o++)y[32767+o]=e,c[32767+o]=[],c[32767+o][1]=e,c[32767+o][0]=r-1+o;t<<=1,r<<=1}}(),function(){for(var t=0;t<256;t++)B[t]=19595*t,B[t+256>>0]=38470*t,B[t+512>>0]=7471*t+32768,B[t+768>>0]=-11059*t,B[t+1024>>0]=-21709*t,B[t+1280>>0]=32768*t+8421375,B[t+1536>>0]=-27439*t,B[t+1792>>0]=-5329*t}(),z(r),(new Date).getTime()}()}self.addEventListener("message",(t=>{const{data:e,width:n,height:o,options:i}=t.data,f=new Uint8Array(e),u=(a={data:f,width:n,height:o},(s=(s=i)||{}).quality=s.quality||50,s.exif=s.exif||"",{data:new r(s.quality).encode(a,s),width:a.width,height:a.height});var a,s;const h=u.data.buffer;postMessage({data:h,width:u.width,height:u.height},[h])}))})()})();',"Worker",void 0,void 0)})),f=(0,n.h)((function(){return i()('/*! For license information please see JPEGDecoder.worker.worker.js.LICENSE.txt */\n(()=>{var r={742:(r,t)=>{"use strict";t.byteLength=function(r){var t=s(r),e=t[0],n=t[1];return 3*(e+n)/4-n},t.toByteArray=function(r){var t,e,i=s(r),a=i[0],f=i[1],u=new o(function(r,t,e){return 3*(t+e)/4-e}(0,a,f)),h=0,c=f>0?a-4:a;for(e=0;e<c;e+=4)t=n[r.charCodeAt(e)]<<18|n[r.charCodeAt(e+1)]<<12|n[r.charCodeAt(e+2)]<<6|n[r.charCodeAt(e+3)],u[h++]=t>>16&255,u[h++]=t>>8&255,u[h++]=255&t;2===f&&(t=n[r.charCodeAt(e)]<<2|n[r.charCodeAt(e+1)]>>4,u[h++]=255&t);1===f&&(t=n[r.charCodeAt(e)]<<10|n[r.charCodeAt(e+1)]<<4|n[r.charCodeAt(e+2)]>>2,u[h++]=t>>8&255,u[h++]=255&t);return u},t.fromByteArray=function(r){for(var t,n=r.length,o=n%3,i=[],a=16383,f=0,s=n-o;f<s;f+=a)i.push(u(r,f,f+a>s?s:f+a));1===o?(t=r[n-1],i.push(e[t>>2]+e[t<<4&63]+"==")):2===o&&(t=(r[n-2]<<8)+r[n-1],i.push(e[t>>10]+e[t>>4&63]+e[t<<2&63]+"="));return i.join("")};for(var e=[],n=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,f=i.length;a<f;++a)e[a]=i[a],n[i.charCodeAt(a)]=a;function s(r){var t=r.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var e=r.indexOf("=");return-1===e&&(e=t),[e,e===t?0:4-e%4]}function u(r,t,n){for(var o,i,a=[],f=t;f<n;f+=3)o=(r[f]<<16&16711680)+(r[f+1]<<8&65280)+(255&r[f+2]),a.push(e[(i=o)>>18&63]+e[i>>12&63]+e[i>>6&63]+e[63&i]);return a.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},764:(r,t,e)=>{"use strict";var n=e(742),o=e(645),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;t.lW=s,t.h2=50;var a=2147483647;function f(r){if(r>a)throw new RangeError(\'The value "\'+r+\'" is invalid for option "size"\');var t=new Uint8Array(r);return Object.setPrototypeOf(t,s.prototype),t}function s(r,t,e){if("number"==typeof r){if("string"==typeof t)throw new TypeError(\'The "string" argument must be of type string. Received type number\');return c(r)}return u(r,t,e)}function u(r,t,e){if("string"==typeof r)return function(r,t){"string"==typeof t&&""!==t||(t="utf8");if(!s.isEncoding(t))throw new TypeError("Unknown encoding: "+t);var e=0|v(r,t),n=f(e),o=n.write(r,t);o!==e&&(n=n.slice(0,o));return n}(r,t);if(ArrayBuffer.isView(r))return function(r){if(N(r,Uint8Array)){var t=new Uint8Array(r);return p(t.buffer,t.byteOffset,t.byteLength)}return l(r)}(r);if(null==r)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r);if(N(r,ArrayBuffer)||r&&N(r.buffer,ArrayBuffer))return p(r,t,e);if("undefined"!=typeof SharedArrayBuffer&&(N(r,SharedArrayBuffer)||r&&N(r.buffer,SharedArrayBuffer)))return p(r,t,e);if("number"==typeof r)throw new TypeError(\'The "value" argument must not be of type number. Received type number\');var n=r.valueOf&&r.valueOf();if(null!=n&&n!==r)return s.from(n,t,e);var o=function(r){if(s.isBuffer(r)){var t=0|y(r.length),e=f(t);return 0===e.length||r.copy(e,0,0,t),e}if(void 0!==r.length)return"number"!=typeof r.length||_(r.length)?f(0):l(r);if("Buffer"===r.type&&Array.isArray(r.data))return l(r.data)}(r);if(o)return o;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof r[Symbol.toPrimitive])return s.from(r[Symbol.toPrimitive]("string"),t,e);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r)}function h(r){if("number"!=typeof r)throw new TypeError(\'"size" argument must be of type number\');if(r<0)throw new RangeError(\'The value "\'+r+\'" is invalid for option "size"\')}function c(r){return h(r),f(r<0?0:0|y(r))}function l(r){for(var t=r.length<0?0:0|y(r.length),e=f(t),n=0;n<t;n+=1)e[n]=255&r[n];return e}function p(r,t,e){if(t<0||r.byteLength<t)throw new RangeError(\'"offset" is outside of buffer bounds\');if(r.byteLength<t+(e||0))throw new RangeError(\'"length" is outside of buffer bounds\');var n;return n=void 0===t&&void 0===e?new Uint8Array(r):void 0===e?new Uint8Array(r,t):new Uint8Array(r,t,e),Object.setPrototypeOf(n,s.prototype),n}function y(r){if(r>=a)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a.toString(16)+" bytes");return 0|r}function v(r,t){if(s.isBuffer(r))return r.length;if(ArrayBuffer.isView(r)||N(r,ArrayBuffer))return r.byteLength;if("string"!=typeof r)throw new TypeError(\'The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type \'+typeof r);var e=r.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===e)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return e;case"utf8":case"utf-8":return X(r).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*e;case"hex":return e>>>1;case"base64":return z(r).length;default:if(o)return n?-1:X(r).length;t=(""+t).toLowerCase(),o=!0}}function g(r,t,e){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===e||e>this.length)&&(e=this.length),e<=0)return"";if((e>>>=0)<=(t>>>=0))return"";for(r||(r="utf8");;)switch(r){case"hex":return I(this,t,e);case"utf8":case"utf-8":return L(this,t,e);case"ascii":return C(this,t,e);case"latin1":case"binary":return x(this,t,e);case"base64":return T(this,t,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return O(this,t,e);default:if(n)throw new TypeError("Unknown encoding: "+r);r=(r+"").toLowerCase(),n=!0}}function d(r,t,e){var n=r[t];r[t]=r[e],r[e]=n}function w(r,t,e,n,o){if(0===r.length)return-1;if("string"==typeof e?(n=e,e=0):e>2147483647?e=2147483647:e<-2147483648&&(e=-2147483648),_(e=+e)&&(e=o?0:r.length-1),e<0&&(e=r.length+e),e>=r.length){if(o)return-1;e=r.length-1}else if(e<0){if(!o)return-1;e=0}if("string"==typeof t&&(t=s.from(t,n)),s.isBuffer(t))return 0===t.length?-1:m(r,t,e,n,o);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(r,t,e):Uint8Array.prototype.lastIndexOf.call(r,t,e):m(r,[t],e,n,o);throw new TypeError("val must be string, number or Buffer")}function m(r,t,e,n,o){var i,a=1,f=r.length,s=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(r.length<2||t.length<2)return-1;a=2,f/=2,s/=2,e/=2}function u(r,t){return 1===a?r[t]:r.readUInt16BE(t*a)}if(o){var h=-1;for(i=e;i<f;i++)if(u(r,i)===u(t,-1===h?0:i-h)){if(-1===h&&(h=i),i-h+1===s)return h*a}else-1!==h&&(i-=i-h),h=-1}else for(e+s>f&&(e=f-s),i=e;i>=0;i--){for(var c=!0,l=0;l<s;l++)if(u(r,i+l)!==u(t,l)){c=!1;break}if(c)return i}return-1}function b(r,t,e,n){e=Number(e)||0;var o=r.length-e;n?(n=Number(n))>o&&(n=o):n=o;var i=t.length;n>i/2&&(n=i/2);for(var a=0;a<n;++a){var f=parseInt(t.substr(2*a,2),16);if(_(f))return a;r[e+a]=f}return a}function A(r,t,e,n){return Y(X(t,r.length-e),r,e,n)}function E(r,t,e,n){return Y(function(r){for(var t=[],e=0;e<r.length;++e)t.push(255&r.charCodeAt(e));return t}(t),r,e,n)}function B(r,t,e,n){return Y(z(t),r,e,n)}function U(r,t,e,n){return Y(function(r,t){for(var e,n,o,i=[],a=0;a<r.length&&!((t-=2)<0);++a)n=(e=r.charCodeAt(a))>>8,o=e%256,i.push(o),i.push(n);return i}(t,r.length-e),r,e,n)}function T(r,t,e){return 0===t&&e===r.length?n.fromByteArray(r):n.fromByteArray(r.slice(t,e))}function L(r,t,e){e=Math.min(r.length,e);for(var n=[],o=t;o<e;){var i,a,f,s,u=r[o],h=null,c=u>239?4:u>223?3:u>191?2:1;if(o+c<=e)switch(c){case 1:u<128&&(h=u);break;case 2:128==(192&(i=r[o+1]))&&(s=(31&u)<<6|63&i)>127&&(h=s);break;case 3:i=r[o+1],a=r[o+2],128==(192&i)&&128==(192&a)&&(s=(15&u)<<12|(63&i)<<6|63&a)>2047&&(s<55296||s>57343)&&(h=s);break;case 4:i=r[o+1],a=r[o+2],f=r[o+3],128==(192&i)&&128==(192&a)&&128==(192&f)&&(s=(15&u)<<18|(63&i)<<12|(63&a)<<6|63&f)>65535&&s<1114112&&(h=s)}null===h?(h=65533,c=1):h>65535&&(h-=65536,n.push(h>>>10&1023|55296),h=56320|1023&h),n.push(h),o+=c}return function(r){var t=r.length;if(t<=k)return String.fromCharCode.apply(String,r);var e="",n=0;for(;n<t;)e+=String.fromCharCode.apply(String,r.slice(n,n+=k));return e}(n)}s.TYPED_ARRAY_SUPPORT=function(){try{var r=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(r,t),42===r.foo()}catch(r){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(r,t,e){return u(r,t,e)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(r,t,e){return function(r,t,e){return h(r),r<=0?f(r):void 0!==t?"string"==typeof e?f(r).fill(t,e):f(r).fill(t):f(r)}(r,t,e)},s.allocUnsafe=function(r){return c(r)},s.allocUnsafeSlow=function(r){return c(r)},s.isBuffer=function(r){return null!=r&&!0===r._isBuffer&&r!==s.prototype},s.compare=function(r,t){if(N(r,Uint8Array)&&(r=s.from(r,r.offset,r.byteLength)),N(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(r)||!s.isBuffer(t))throw new TypeError(\'The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array\');if(r===t)return 0;for(var e=r.length,n=t.length,o=0,i=Math.min(e,n);o<i;++o)if(r[o]!==t[o]){e=r[o],n=t[o];break}return e<n?-1:n<e?1:0},s.isEncoding=function(r){switch(String(r).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(r,t){if(!Array.isArray(r))throw new TypeError(\'"list" argument must be an Array of Buffers\');if(0===r.length)return s.alloc(0);var e;if(void 0===t)for(t=0,e=0;e<r.length;++e)t+=r[e].length;var n=s.allocUnsafe(t),o=0;for(e=0;e<r.length;++e){var i=r[e];if(N(i,Uint8Array))o+i.length>n.length?s.from(i).copy(n,o):Uint8Array.prototype.set.call(n,i,o);else{if(!s.isBuffer(i))throw new TypeError(\'"list" argument must be an Array of Buffers\');i.copy(n,o)}o+=i.length}return n},s.byteLength=v,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var r=this.length;if(r%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<r;t+=2)d(this,t,t+1);return this},s.prototype.swap32=function(){var r=this.length;if(r%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<r;t+=4)d(this,t,t+3),d(this,t+1,t+2);return this},s.prototype.swap64=function(){var r=this.length;if(r%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<r;t+=8)d(this,t,t+7),d(this,t+1,t+6),d(this,t+2,t+5),d(this,t+3,t+4);return this},s.prototype.toString=function(){var r=this.length;return 0===r?"":0===arguments.length?L(this,0,r):g.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(r){if(!s.isBuffer(r))throw new TypeError("Argument must be a Buffer");return this===r||0===s.compare(this,r)},s.prototype.inspect=function(){var r="",e=t.h2;return r=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(r+=" ... "),"<Buffer "+r+">"},i&&(s.prototype[i]=s.prototype.inspect),s.prototype.compare=function(r,t,e,n,o){if(N(r,Uint8Array)&&(r=s.from(r,r.offset,r.byteLength)),!s.isBuffer(r))throw new TypeError(\'The "target" argument must be one of type Buffer or Uint8Array. Received type \'+typeof r);if(void 0===t&&(t=0),void 0===e&&(e=r?r.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),t<0||e>r.length||n<0||o>this.length)throw new RangeError("out of range index");if(n>=o&&t>=e)return 0;if(n>=o)return-1;if(t>=e)return 1;if(this===r)return 0;for(var i=(o>>>=0)-(n>>>=0),a=(e>>>=0)-(t>>>=0),f=Math.min(i,a),u=this.slice(n,o),h=r.slice(t,e),c=0;c<f;++c)if(u[c]!==h[c]){i=u[c],a=h[c];break}return i<a?-1:a<i?1:0},s.prototype.includes=function(r,t,e){return-1!==this.indexOf(r,t,e)},s.prototype.indexOf=function(r,t,e){return w(this,r,t,e,!0)},s.prototype.lastIndexOf=function(r,t,e){return w(this,r,t,e,!1)},s.prototype.write=function(r,t,e,n){if(void 0===t)n="utf8",e=this.length,t=0;else if(void 0===e&&"string"==typeof t)n=t,e=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(e)?(e>>>=0,void 0===n&&(n="utf8")):(n=e,e=void 0)}var o=this.length-t;if((void 0===e||e>o)&&(e=o),r.length>0&&(e<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var i=!1;;)switch(n){case"hex":return b(this,r,t,e);case"utf8":case"utf-8":return A(this,r,t,e);case"ascii":case"latin1":case"binary":return E(this,r,t,e);case"base64":return B(this,r,t,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,r,t,e);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var k=4096;function C(r,t,e){var n="";e=Math.min(r.length,e);for(var o=t;o<e;++o)n+=String.fromCharCode(127&r[o]);return n}function x(r,t,e){var n="";e=Math.min(r.length,e);for(var o=t;o<e;++o)n+=String.fromCharCode(r[o]);return n}function I(r,t,e){var n=r.length;(!t||t<0)&&(t=0),(!e||e<0||e>n)&&(e=n);for(var o="",i=t;i<e;++i)o+=q[r[i]];return o}function O(r,t,e){for(var n=r.slice(t,e),o="",i=0;i<n.length-1;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}function R(r,t,e){if(r%1!=0||r<0)throw new RangeError("offset is not uint");if(r+t>e)throw new RangeError("Trying to access beyond buffer length")}function P(r,t,e,n,o,i){if(!s.isBuffer(r))throw new TypeError(\'"buffer" argument must be a Buffer instance\');if(t>o||t<i)throw new RangeError(\'"value" argument is out of bounds\');if(e+n>r.length)throw new RangeError("Index out of range")}function M(r,t,e,n,o,i){if(e+n>r.length)throw new RangeError("Index out of range");if(e<0)throw new RangeError("Index out of range")}function S(r,t,e,n,i){return t=+t,e>>>=0,i||M(r,0,e,4),o.write(r,t,e,n,23,4),e+4}function j(r,t,e,n,i){return t=+t,e>>>=0,i||M(r,0,e,8),o.write(r,t,e,n,52,8),e+8}s.prototype.slice=function(r,t){var e=this.length;(r=~~r)<0?(r+=e)<0&&(r=0):r>e&&(r=e),(t=void 0===t?e:~~t)<0?(t+=e)<0&&(t=0):t>e&&(t=e),t<r&&(t=r);var n=this.subarray(r,t);return Object.setPrototypeOf(n,s.prototype),n},s.prototype.readUintLE=s.prototype.readUIntLE=function(r,t,e){r>>>=0,t>>>=0,e||R(r,t,this.length);for(var n=this[r],o=1,i=0;++i<t&&(o*=256);)n+=this[r+i]*o;return n},s.prototype.readUintBE=s.prototype.readUIntBE=function(r,t,e){r>>>=0,t>>>=0,e||R(r,t,this.length);for(var n=this[r+--t],o=1;t>0&&(o*=256);)n+=this[r+--t]*o;return n},s.prototype.readUint8=s.prototype.readUInt8=function(r,t){return r>>>=0,t||R(r,1,this.length),this[r]},s.prototype.readUint16LE=s.prototype.readUInt16LE=function(r,t){return r>>>=0,t||R(r,2,this.length),this[r]|this[r+1]<<8},s.prototype.readUint16BE=s.prototype.readUInt16BE=function(r,t){return r>>>=0,t||R(r,2,this.length),this[r]<<8|this[r+1]},s.prototype.readUint32LE=s.prototype.readUInt32LE=function(r,t){return r>>>=0,t||R(r,4,this.length),(this[r]|this[r+1]<<8|this[r+2]<<16)+16777216*this[r+3]},s.prototype.readUint32BE=s.prototype.readUInt32BE=function(r,t){return r>>>=0,t||R(r,4,this.length),16777216*this[r]+(this[r+1]<<16|this[r+2]<<8|this[r+3])},s.prototype.readIntLE=function(r,t,e){r>>>=0,t>>>=0,e||R(r,t,this.length);for(var n=this[r],o=1,i=0;++i<t&&(o*=256);)n+=this[r+i]*o;return n>=(o*=128)&&(n-=Math.pow(2,8*t)),n},s.prototype.readIntBE=function(r,t,e){r>>>=0,t>>>=0,e||R(r,t,this.length);for(var n=t,o=1,i=this[r+--n];n>0&&(o*=256);)i+=this[r+--n]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*t)),i},s.prototype.readInt8=function(r,t){return r>>>=0,t||R(r,1,this.length),128&this[r]?-1*(255-this[r]+1):this[r]},s.prototype.readInt16LE=function(r,t){r>>>=0,t||R(r,2,this.length);var e=this[r]|this[r+1]<<8;return 32768&e?4294901760|e:e},s.prototype.readInt16BE=function(r,t){r>>>=0,t||R(r,2,this.length);var e=this[r+1]|this[r]<<8;return 32768&e?4294901760|e:e},s.prototype.readInt32LE=function(r,t){return r>>>=0,t||R(r,4,this.length),this[r]|this[r+1]<<8|this[r+2]<<16|this[r+3]<<24},s.prototype.readInt32BE=function(r,t){return r>>>=0,t||R(r,4,this.length),this[r]<<24|this[r+1]<<16|this[r+2]<<8|this[r+3]},s.prototype.readFloatLE=function(r,t){return r>>>=0,t||R(r,4,this.length),o.read(this,r,!0,23,4)},s.prototype.readFloatBE=function(r,t){return r>>>=0,t||R(r,4,this.length),o.read(this,r,!1,23,4)},s.prototype.readDoubleLE=function(r,t){return r>>>=0,t||R(r,8,this.length),o.read(this,r,!0,52,8)},s.prototype.readDoubleBE=function(r,t){return r>>>=0,t||R(r,8,this.length),o.read(this,r,!1,52,8)},s.prototype.writeUintLE=s.prototype.writeUIntLE=function(r,t,e,n){(r=+r,t>>>=0,e>>>=0,n)||P(this,r,t,e,Math.pow(2,8*e)-1,0);var o=1,i=0;for(this[t]=255&r;++i<e&&(o*=256);)this[t+i]=r/o&255;return t+e},s.prototype.writeUintBE=s.prototype.writeUIntBE=function(r,t,e,n){(r=+r,t>>>=0,e>>>=0,n)||P(this,r,t,e,Math.pow(2,8*e)-1,0);var o=e-1,i=1;for(this[t+o]=255&r;--o>=0&&(i*=256);)this[t+o]=r/i&255;return t+e},s.prototype.writeUint8=s.prototype.writeUInt8=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,1,255,0),this[t]=255&r,t+1},s.prototype.writeUint16LE=s.prototype.writeUInt16LE=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,2,65535,0),this[t]=255&r,this[t+1]=r>>>8,t+2},s.prototype.writeUint16BE=s.prototype.writeUInt16BE=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,2,65535,0),this[t]=r>>>8,this[t+1]=255&r,t+2},s.prototype.writeUint32LE=s.prototype.writeUInt32LE=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,4,4294967295,0),this[t+3]=r>>>24,this[t+2]=r>>>16,this[t+1]=r>>>8,this[t]=255&r,t+4},s.prototype.writeUint32BE=s.prototype.writeUInt32BE=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,4,4294967295,0),this[t]=r>>>24,this[t+1]=r>>>16,this[t+2]=r>>>8,this[t+3]=255&r,t+4},s.prototype.writeIntLE=function(r,t,e,n){if(r=+r,t>>>=0,!n){var o=Math.pow(2,8*e-1);P(this,r,t,e,o-1,-o)}var i=0,a=1,f=0;for(this[t]=255&r;++i<e&&(a*=256);)r<0&&0===f&&0!==this[t+i-1]&&(f=1),this[t+i]=(r/a>>0)-f&255;return t+e},s.prototype.writeIntBE=function(r,t,e,n){if(r=+r,t>>>=0,!n){var o=Math.pow(2,8*e-1);P(this,r,t,e,o-1,-o)}var i=e-1,a=1,f=0;for(this[t+i]=255&r;--i>=0&&(a*=256);)r<0&&0===f&&0!==this[t+i+1]&&(f=1),this[t+i]=(r/a>>0)-f&255;return t+e},s.prototype.writeInt8=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,1,127,-128),r<0&&(r=255+r+1),this[t]=255&r,t+1},s.prototype.writeInt16LE=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,2,32767,-32768),this[t]=255&r,this[t+1]=r>>>8,t+2},s.prototype.writeInt16BE=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,2,32767,-32768),this[t]=r>>>8,this[t+1]=255&r,t+2},s.prototype.writeInt32LE=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,4,2147483647,-2147483648),this[t]=255&r,this[t+1]=r>>>8,this[t+2]=r>>>16,this[t+3]=r>>>24,t+4},s.prototype.writeInt32BE=function(r,t,e){return r=+r,t>>>=0,e||P(this,r,t,4,2147483647,-2147483648),r<0&&(r=4294967295+r+1),this[t]=r>>>24,this[t+1]=r>>>16,this[t+2]=r>>>8,this[t+3]=255&r,t+4},s.prototype.writeFloatLE=function(r,t,e){return S(this,r,t,!0,e)},s.prototype.writeFloatBE=function(r,t,e){return S(this,r,t,!1,e)},s.prototype.writeDoubleLE=function(r,t,e){return j(this,r,t,!0,e)},s.prototype.writeDoubleBE=function(r,t,e){return j(this,r,t,!1,e)},s.prototype.copy=function(r,t,e,n){if(!s.isBuffer(r))throw new TypeError("argument should be a Buffer");if(e||(e=0),n||0===n||(n=this.length),t>=r.length&&(t=r.length),t||(t=0),n>0&&n<e&&(n=e),n===e)return 0;if(0===r.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(e<0||e>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),r.length-t<n-e&&(n=r.length-t+e);var o=n-e;return this===r&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,e,n):Uint8Array.prototype.set.call(r,this.subarray(e,n),t),o},s.prototype.fill=function(r,t,e,n){if("string"==typeof r){if("string"==typeof t?(n=t,t=0,e=this.length):"string"==typeof e&&(n=e,e=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===r.length){var o=r.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(r=o)}}else"number"==typeof r?r&=255:"boolean"==typeof r&&(r=Number(r));if(t<0||this.length<t||this.length<e)throw new RangeError("Out of range index");if(e<=t)return this;var i;if(t>>>=0,e=void 0===e?this.length:e>>>0,r||(r=0),"number"==typeof r)for(i=t;i<e;++i)this[i]=r;else{var a=s.isBuffer(r)?r:s.from(r,n),f=a.length;if(0===f)throw new TypeError(\'The value "\'+r+\'" is invalid for argument "value"\');for(i=0;i<e-t;++i)this[i+t]=a[i%f]}return this};var D=/[^+/0-9A-Za-z-_]/g;function X(r,t){var e;t=t||1/0;for(var n=r.length,o=null,i=[],a=0;a<n;++a){if((e=r.charCodeAt(a))>55295&&e<57344){if(!o){if(e>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(a+1===n){(t-=3)>-1&&i.push(239,191,189);continue}o=e;continue}if(e<56320){(t-=3)>-1&&i.push(239,191,189),o=e;continue}e=65536+(o-55296<<10|e-56320)}else o&&(t-=3)>-1&&i.push(239,191,189);if(o=null,e<128){if((t-=1)<0)break;i.push(e)}else if(e<2048){if((t-=2)<0)break;i.push(e>>6|192,63&e|128)}else if(e<65536){if((t-=3)<0)break;i.push(e>>12|224,e>>6&63|128,63&e|128)}else{if(!(e<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(e>>18|240,e>>12&63|128,e>>6&63|128,63&e|128)}}return i}function z(r){return n.toByteArray(function(r){if((r=(r=r.split("=")[0]).trim().replace(D,"")).length<2)return"";for(;r.length%4!=0;)r+="=";return r}(r))}function Y(r,t,e,n){for(var o=0;o<n&&!(o+e>=t.length||o>=r.length);++o)t[o+e]=r[o];return o}function N(r,t){return r instanceof t||null!=r&&null!=r.constructor&&null!=r.constructor.name&&r.constructor.name===t.name}function _(r){return r!=r}var q=function(){for(var r="0123456789abcdef",t=new Array(256),e=0;e<16;++e)for(var n=16*e,o=0;o<16;++o)t[n+o]=r[e]+r[o];return t}()},645:(r,t)=>{t.read=function(r,t,e,n,o){var i,a,f=8*o-n-1,s=(1<<f)-1,u=s>>1,h=-7,c=e?o-1:0,l=e?-1:1,p=r[t+c];for(c+=l,i=p&(1<<-h)-1,p>>=-h,h+=f;h>0;i=256*i+r[t+c],c+=l,h-=8);for(a=i&(1<<-h)-1,i>>=-h,h+=n;h>0;a=256*a+r[t+c],c+=l,h-=8);if(0===i)i=1-u;else{if(i===s)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,n),i-=u}return(p?-1:1)*a*Math.pow(2,i-n)},t.write=function(r,t,e,n,o,i){var a,f,s,u=8*i-o-1,h=(1<<u)-1,c=h>>1,l=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:i-1,y=n?1:-1,v=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(f=isNaN(t)?1:0,a=h):(a=Math.floor(Math.log(t)/Math.LN2),t*(s=Math.pow(2,-a))<1&&(a--,s*=2),(t+=a+c>=1?l/s:l*Math.pow(2,1-c))*s>=2&&(a++,s/=2),a+c>=h?(f=0,a=h):a+c>=1?(f=(t*s-1)*Math.pow(2,o),a+=c):(f=t*Math.pow(2,c-1)*Math.pow(2,o),a=0));o>=8;r[e+p]=255&f,p+=y,f/=256,o-=8);for(a=a<<o|f,u+=o;u>0;r[e+p]=255&a,p+=y,a/=256,u-=8);r[e+p-y]|=128*v}},870:(r,t,e)=>{var n=e(587),o=e(914);r.exports={encode:n,decode:o}},914:(r,t,e)=>{var n=e(764).lW,o=function(){"use strict";var r=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),t=4017,e=799,n=3406,o=2276,i=1567,a=3784,f=5793,s=2896;function u(){}function h(r,t){for(var e,n,o=0,i=[],a=16;a>0&&!r[a-1];)a--;i.push({children:[],index:0});var f,s=i[0];for(e=0;e<a;e++){for(n=0;n<r[e];n++){for((s=i.pop()).children[s.index]=t[o];s.index>0;){if(0===i.length)throw new Error("Could not recreate Huffman Table");s=i.pop()}for(s.index++,i.push(s);i.length<=e;)i.push(f={children:[],index:0}),s.children[s.index]=f.children,s=f;o++}e+1<a&&(i.push(f={children:[],index:0}),s.children[s.index]=f.children,s=f)}return i[0].children}function c(t,e,n,o,i,a,f,s,u){n.precision,n.samplesPerLine,n.scanLines;var h=n.mcusPerLine,c=n.progressive,l=(n.maxH,n.maxV,e),p=0,y=0;function v(){if(y>0)return y--,p>>y&1;if(255==(p=t[e++])){var r=t[e++];if(r)throw new Error("unexpected marker: "+(p<<8|r).toString(16))}return y=7,p>>>7}function g(r){for(var t,e=r;null!==(t=v());){if("number"==typeof(e=e[t]))return e;if("object"!=typeof e)throw new Error("invalid huffman sequence")}return null}function d(r){for(var t=0;r>0;){var e=v();if(null===e)return;t=t<<1|e,r--}return t}function w(r){var t=d(r);return t>=1<<r-1?t:t+(-1<<r)+1}var m=0;var b,A=0;function E(r,t,e,n,o){var i=e%h,a=(e/h|0)*r.v+n,f=i*r.h+o;t(r,r.blocks[a][f])}function B(r,t,e){var n=e/r.blocksPerLine|0,o=e%r.blocksPerLine;t(r,r.blocks[n][o])}var U,T,L,k,C,x,I=o.length;x=c?0===a?0===s?function(r,t){var e=g(r.huffmanTableDC),n=0===e?0:w(e)<<u;t[0]=r.pred+=n}:function(r,t){t[0]|=v()<<u}:0===s?function(t,e){if(m>0)m--;else for(var n=a,o=f;n<=o;){var i=g(t.huffmanTableAC),s=15&i,h=i>>4;if(0!==s)e[r[n+=h]]=w(s)*(1<<u),n++;else{if(h<15){m=d(h)+(1<<h)-1;break}n+=16}}}:function(t,e){for(var n=a,o=f,i=0;n<=o;){var s=r[n],h=e[s]<0?-1:1;switch(A){case 0:var c=g(t.huffmanTableAC),l=15&c;if(i=c>>4,0===l)i<15?(m=d(i)+(1<<i),A=4):(i=16,A=1);else{if(1!==l)throw new Error("invalid ACn encoding");b=w(l),A=i?2:3}continue;case 1:case 2:e[s]?e[s]+=(v()<<u)*h:0==--i&&(A=2==A?3:0);break;case 3:e[s]?e[s]+=(v()<<u)*h:(e[s]=b<<u,A=0);break;case 4:e[s]&&(e[s]+=(v()<<u)*h)}n++}4===A&&0==--m&&(A=0)}:function(t,e){var n=g(t.huffmanTableDC),o=0===n?0:w(n);e[0]=t.pred+=o;for(var i=1;i<64;){var a=g(t.huffmanTableAC),f=15&a,s=a>>4;if(0!==f)e[r[i+=s]]=w(f),i++;else{if(s<15)break;i+=16}}};var O,R,P,M,S=0;for(R=1==I?o[0].blocksPerLine*o[0].blocksPerColumn:h*n.mcusPerColumn,i||(i=R);S<R;){for(T=0;T<I;T++)o[T].pred=0;if(m=0,1==I)for(U=o[0],C=0;C<i;C++)B(U,x,S),S++;else for(C=0;C<i;C++){for(T=0;T<I;T++)for(P=(U=o[T]).h,M=U.v,L=0;L<M;L++)for(k=0;k<P;k++)E(U,x,S,L,k);if(++S===R)break}if(y=0,(O=t[e]<<8|t[e+1])<65280)throw new Error("marker was not found");if(!(O>=65488&&O<=65495))break;e+=2}return e-l}function l(r,u){var h,c,l=[],p=u.blocksPerLine,y=u.blocksPerColumn,v=p<<3,g=new Int32Array(64),d=new Uint8Array(64);function w(r,h,c){var l,p,y,v,g,d,w,m,b,A,E=u.quantizationTable,B=c;for(A=0;A<64;A++)B[A]=r[A]*E[A];for(A=0;A<8;++A){var U=8*A;0!=B[1+U]||0!=B[2+U]||0!=B[3+U]||0!=B[4+U]||0!=B[5+U]||0!=B[6+U]||0!=B[7+U]?(l=f*B[0+U]+128>>8,p=f*B[4+U]+128>>8,y=B[2+U],v=B[6+U],g=s*(B[1+U]-B[7+U])+128>>8,m=s*(B[1+U]+B[7+U])+128>>8,d=B[3+U]<<4,w=B[5+U]<<4,b=l-p+1>>1,l=l+p+1>>1,p=b,b=y*a+v*i+128>>8,y=y*i-v*a+128>>8,v=b,b=g-w+1>>1,g=g+w+1>>1,w=b,b=m+d+1>>1,d=m-d+1>>1,m=b,b=l-v+1>>1,l=l+v+1>>1,v=b,b=p-y+1>>1,p=p+y+1>>1,y=b,b=g*o+m*n+2048>>12,g=g*n-m*o+2048>>12,m=b,b=d*e+w*t+2048>>12,d=d*t-w*e+2048>>12,w=b,B[0+U]=l+m,B[7+U]=l-m,B[1+U]=p+w,B[6+U]=p-w,B[2+U]=y+d,B[5+U]=y-d,B[3+U]=v+g,B[4+U]=v-g):(b=f*B[0+U]+512>>10,B[0+U]=b,B[1+U]=b,B[2+U]=b,B[3+U]=b,B[4+U]=b,B[5+U]=b,B[6+U]=b,B[7+U]=b)}for(A=0;A<8;++A){var T=A;0!=B[8+T]||0!=B[16+T]||0!=B[24+T]||0!=B[32+T]||0!=B[40+T]||0!=B[48+T]||0!=B[56+T]?(l=f*B[0+T]+2048>>12,p=f*B[32+T]+2048>>12,y=B[16+T],v=B[48+T],g=s*(B[8+T]-B[56+T])+2048>>12,m=s*(B[8+T]+B[56+T])+2048>>12,d=B[24+T],w=B[40+T],b=l-p+1>>1,l=l+p+1>>1,p=b,b=y*a+v*i+2048>>12,y=y*i-v*a+2048>>12,v=b,b=g-w+1>>1,g=g+w+1>>1,w=b,b=m+d+1>>1,d=m-d+1>>1,m=b,b=l-v+1>>1,l=l+v+1>>1,v=b,b=p-y+1>>1,p=p+y+1>>1,y=b,b=g*o+m*n+2048>>12,g=g*n-m*o+2048>>12,m=b,b=d*e+w*t+2048>>12,d=d*t-w*e+2048>>12,w=b,B[0+T]=l+m,B[56+T]=l-m,B[8+T]=p+w,B[48+T]=p-w,B[16+T]=y+d,B[40+T]=y-d,B[24+T]=v+g,B[32+T]=v-g):(b=f*c[A+0]+8192>>14,B[0+T]=b,B[8+T]=b,B[16+T]=b,B[24+T]=b,B[32+T]=b,B[40+T]=b,B[48+T]=b,B[56+T]=b)}for(A=0;A<64;++A){var L=128+(B[A]+8>>4);h[A]=L<0?0:L>255?255:L}}for(var m=0;m<y;m++){var b=m<<3;for(h=0;h<8;h++)l.push(new Uint8Array(v));for(var A=0;A<p;A++){w(u.blocks[m][A],d,g);var E=0,B=A<<3;for(c=0;c<8;c++){var U=l[b+c];for(h=0;h<8;h++)U[B+h]=d[E++]}}}return l}function p(r){return r<0?0:r>255?255:r}return u.prototype={load:function(r){var t=new XMLHttpRequest;t.open("GET",r,!0),t.responseType="arraybuffer",t.onload=function(){var r=new Uint8Array(t.response||t.mozResponseArrayBuffer);this.parse(r),this.onload&&this.onload()}.bind(this),t.send(null)},parse:function(t){var e=0;t.length;function n(){var r=t[e]<<8|t[e+1];return e+=2,r}function o(){var r=n(),o=t.subarray(e,e+r-2);return e+=o.length,o}function i(r){var t,e,n=0,o=0;for(e in r.components)r.components.hasOwnProperty(e)&&(n<(t=r.components[e]).h&&(n=t.h),o<t.v&&(o=t.v));var i=Math.ceil(r.samplesPerLine/8/n),a=Math.ceil(r.scanLines/8/o);for(e in r.components)if(r.components.hasOwnProperty(e)){t=r.components[e];for(var f=Math.ceil(Math.ceil(r.samplesPerLine/8)*t.h/n),s=Math.ceil(Math.ceil(r.scanLines/8)*t.v/o),u=i*t.h,h=a*t.v,c=[],l=0;l<h;l++){for(var p=[],y=0;y<u;y++)p.push(new Int32Array(64));c.push(p)}t.blocksPerLine=f,t.blocksPerColumn=s,t.blocks=c}r.maxH=n,r.maxV=o,r.mcusPerLine=i,r.mcusPerColumn=a}var a,f,s=null,u=null,p=[],y=[],v=[],g=[],d=n();if(65496!=d)throw new Error("SOI not found");for(d=n();65497!=d;){switch(d){case 65280:break;case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var w=o();65504===d&&74===w[0]&&70===w[1]&&73===w[2]&&70===w[3]&&0===w[4]&&(s={version:{major:w[5],minor:w[6]},densityUnits:w[7],xDensity:w[8]<<8|w[9],yDensity:w[10]<<8|w[11],thumbWidth:w[12],thumbHeight:w[13],thumbData:w.subarray(14,14+3*w[12]*w[13])}),65518===d&&65===w[0]&&100===w[1]&&111===w[2]&&98===w[3]&&101===w[4]&&0===w[5]&&(u={version:w[6],flags0:w[7]<<8|w[8],flags1:w[9]<<8|w[10],transformCode:w[11]});break;case 65499:for(var m=n()+e-2;e<m;){var b=t[e++],A=new Int32Array(64);if(b>>4==0)for(N=0;N<64;N++){A[r[N]]=t[e++]}else{if(b>>4!=1)throw new Error("DQT: invalid table spec");for(N=0;N<64;N++){A[r[N]]=n()}}p[15&b]=A}break;case 65472:case 65473:case 65474:n(),(a={}).extended=65473===d,a.progressive=65474===d,a.precision=t[e++],a.scanLines=n(),a.samplesPerLine=n(),a.components={},a.componentsOrder=[];var E,B=t[e++];for(z=0;z<B;z++){E=t[e];var U=t[e+1]>>4,T=15&t[e+1],L=t[e+2];a.componentsOrder.push(E),a.components[E]={h:U,v:T,quantizationIdx:L},e+=3}i(a),y.push(a);break;case 65476:var k=n();for(z=2;z<k;){var C=t[e++],x=new Uint8Array(16),I=0;for(N=0;N<16;N++,e++)I+=x[N]=t[e];var O=new Uint8Array(I);for(N=0;N<I;N++,e++)O[N]=t[e];z+=17+I,(C>>4==0?g:v)[15&C]=h(x,O)}break;case 65501:n(),f=n();break;case 65498:n();var R=t[e++],P=[];for(z=0;z<R;z++){_=a.components[t[e++]];var M=t[e++];_.huffmanTableDC=g[M>>4],_.huffmanTableAC=v[15&M],P.push(_)}var S=t[e++],j=t[e++],D=t[e++],X=c(t,e,a,P,f,S,j,D>>4,15&D);e+=X;break;case 65535:255!==t[e]&&e--;break;default:if(255==t[e-3]&&t[e-2]>=192&&t[e-2]<=254){e-=3;break}throw new Error("unknown JPEG marker "+d.toString(16))}d=n()}if(1!=y.length)throw new Error("only single frame JPEGs supported");for(var z=0;z<y.length;z++){var Y=y[z].components;for(var N in Y)Y[N].quantizationTable=p[Y[N].quantizationIdx],delete Y[N].quantizationIdx}this.width=a.samplesPerLine,this.height=a.scanLines,this.jfif=s,this.adobe=u,this.components=[];for(z=0;z<a.componentsOrder.length;z++){var _=a.components[a.componentsOrder[z]];this.components.push({lines:l(0,_),scaleX:_.h/a.maxH,scaleY:_.v/a.maxV})}},getData:function(r,t){var e,n,o,i,a,f,s,u,h,c,l,y,v,g,d,w,m,b,A,E,B,U=this.width/r,T=this.height/t,L=0,k=r*t*this.components.length,C=new Uint8Array(k);switch(this.components.length){case 1:for(e=this.components[0],c=0;c<t;c++)for(a=e.lines[0|c*e.scaleY*T],h=0;h<r;h++)l=a[0|h*e.scaleX*U],C[L++]=l;break;case 2:for(e=this.components[0],n=this.components[1],c=0;c<t;c++)for(a=e.lines[0|c*e.scaleY*T],f=n.lines[0|c*n.scaleY*T],h=0;h<r;h++)l=a[0|h*e.scaleX*U],C[L++]=l,l=f[0|h*n.scaleX*U],C[L++]=l;break;case 3:for(B=!0,this.adobe&&this.adobe.transformCode?B=!0:void 0!==this.colorTransform&&(B=!!this.colorTransform),e=this.components[0],n=this.components[1],o=this.components[2],c=0;c<t;c++)for(a=e.lines[0|c*e.scaleY*T],f=n.lines[0|c*n.scaleY*T],s=o.lines[0|c*o.scaleY*T],h=0;h<r;h++)B?(l=a[0|h*e.scaleX*U],y=f[0|h*n.scaleX*U],b=p(l+1.402*((v=s[0|h*o.scaleX*U])-128)),A=p(l-.3441363*(y-128)-.71413636*(v-128)),E=p(l+1.772*(y-128))):(b=a[0|h*e.scaleX*U],A=f[0|h*n.scaleX*U],E=s[0|h*o.scaleX*U]),C[L++]=b,C[L++]=A,C[L++]=E;break;case 4:if(!this.adobe)throw new Error("Unsupported color mode (4 components)");for(B=!1,this.adobe&&this.adobe.transformCode?B=!0:void 0!==this.colorTransform&&(B=!!this.colorTransform),e=this.components[0],n=this.components[1],o=this.components[2],i=this.components[3],c=0;c<t;c++)for(a=e.lines[0|c*e.scaleY*T],f=n.lines[0|c*n.scaleY*T],s=o.lines[0|c*o.scaleY*T],u=i.lines[0|c*i.scaleY*T],h=0;h<r;h++)B?(l=a[0|h*e.scaleX*U],y=f[0|h*n.scaleX*U],v=s[0|h*o.scaleX*U],g=u[0|h*i.scaleX*U],d=255-p(l+1.402*(v-128)),w=255-p(l-.3441363*(y-128)-.71413636*(v-128)),m=255-p(l+1.772*(y-128))):(d=a[0|h*e.scaleX*U],w=f[0|h*n.scaleX*U],m=s[0|h*o.scaleX*U],g=u[0|h*i.scaleX*U]),C[L++]=255-d,C[L++]=255-w,C[L++]=255-m,C[L++]=255-g;break;default:throw new Error("Unsupported color mode")}return C},copyToImageData:function(r,t){var e,n,o,i,a,f,s,u,h,c=r.width,l=r.height,y=r.data,v=this.getData(c,l),g=0,d=0;switch(this.components.length){case 1:for(n=0;n<l;n++)for(e=0;e<c;e++)o=v[g++],y[d++]=o,y[d++]=o,y[d++]=o,t&&(y[d++]=255);break;case 3:for(n=0;n<l;n++)for(e=0;e<c;e++)s=v[g++],u=v[g++],h=v[g++],y[d++]=s,y[d++]=u,y[d++]=h,t&&(y[d++]=255);break;case 4:for(n=0;n<l;n++)for(e=0;e<c;e++)a=v[g++],f=v[g++],o=v[g++],s=255-p(a*(1-(i=v[g++])/255)+i),u=255-p(f*(1-i/255)+i),h=255-p(o*(1-i/255)+i),y[d++]=s,y[d++]=u,y[d++]=h,t&&(y[d++]=255);break;default:throw new Error("Unsupported color mode")}}},u}();r.exports=function(r,t){var e={useTArray:!1,colorTransform:void 0,formatAsRGBA:!0};t?"object"==typeof t?t={useTArray:void 0===t.useTArray?e.useTArray:t.useTArray,colorTransform:void 0===t.colorTransform?e.colorTransform:t.colorTransform,formatAsRGBA:void 0===t.formatAsRGBA?e.formatAsRGBA:t.formatAsRGBA}:(t=e).useTArray=!0:t=e;var i=new Uint8Array(r),a=new o;a.parse(i),a.colorTransform=t.colorTransform;var f=t.formatAsRGBA?4:3,s=a.width*a.height*f;try{var u={width:a.width,height:a.height,data:t.useTArray?new Uint8Array(s):new n(s)}}catch(r){throw r instanceof RangeError?new Error("Could not allocate enough memory for the image. Required: "+s):r}return a.copyToImageData(u,t.formatAsRGBA),u}},587:(r,t,e)=>{var n=e(764).lW;function o(r){Math.round;var t,e,o,i,a,f=Math.floor,s=new Array(64),u=new Array(64),h=new Array(64),c=new Array(64),l=new Array(65535),p=new Array(65535),y=new Array(64),v=new Array(64),g=[],d=0,w=7,m=new Array(64),b=new Array(64),A=new Array(64),E=new Array(256),B=new Array(2048),U=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],T=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],L=[0,1,2,3,4,5,6,7,8,9,10,11],k=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],C=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],x=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],I=[0,1,2,3,4,5,6,7,8,9,10,11],O=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],R=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function P(r,t){for(var e=0,n=0,o=new Array,i=1;i<=16;i++){for(var a=1;a<=r[i];a++)o[t[n]]=[],o[t[n]][0]=e,o[t[n]][1]=i,n++,e++;e*=2}return o}function M(r){for(var t=r[0],e=r[1]-1;e>=0;)t&1<<e&&(d|=1<<w),e--,--w<0&&(255==d?(S(255),S(0)):S(d),w=7,d=0)}function S(r){g.push(r)}function j(r){S(r>>8&255),S(255&r)}function D(r,t,e,n,o){for(var i,a=o[0],f=o[240],s=function(r,t){var e,n,o,i,a,f,s,u,h,c,l=0;for(h=0;h<8;++h){e=r[l],n=r[l+1],o=r[l+2],i=r[l+3],a=r[l+4],f=r[l+5],s=r[l+6];var p=e+(u=r[l+7]),v=e-u,g=n+s,d=n-s,w=o+f,m=o-f,b=i+a,A=i-a,E=p+b,B=p-b,U=g+w,T=g-w;r[l]=E+U,r[l+4]=E-U;var L=.707106781*(T+B);r[l+2]=B+L,r[l+6]=B-L;var k=.382683433*((E=A+m)-(T=d+v)),C=.5411961*E+k,x=1.306562965*T+k,I=.707106781*(U=m+d),O=v+I,R=v-I;r[l+5]=R+C,r[l+3]=R-C,r[l+1]=O+x,r[l+7]=O-x,l+=8}for(l=0,h=0;h<8;++h){e=r[l],n=r[l+8],o=r[l+16],i=r[l+24],a=r[l+32],f=r[l+40],s=r[l+48];var P=e+(u=r[l+56]),M=e-u,S=n+s,j=n-s,D=o+f,X=o-f,z=i+a,Y=i-a,N=P+z,_=P-z,q=S+D,G=S-D;r[l]=N+q,r[l+32]=N-q;var F=.707106781*(G+_);r[l+16]=_+F,r[l+48]=_-F;var H=.382683433*((N=Y+X)-(G=j+M)),W=.5411961*N+H,V=1.306562965*G+H,J=.707106781*(q=X+j),Q=M+J,Z=M-J;r[l+40]=Z+W,r[l+24]=Z-W,r[l+8]=Q+V,r[l+56]=Q-V,l++}for(h=0;h<64;++h)c=r[h]*t[h],y[h]=c>0?c+.5|0:c-.5|0;return y}(r,t),u=0;u<64;++u)v[U[u]]=s[u];var h=v[0]-e;e=v[0],0==h?M(n[0]):(M(n[p[i=32767+h]]),M(l[i]));for(var c=63;c>0&&0==v[c];c--);if(0==c)return M(a),e;for(var g,d=1;d<=c;){for(var w=d;0==v[d]&&d<=c;++d);var m=d-w;if(m>=16){g=m>>4;for(var b=1;b<=g;++b)M(f);m&=15}i=32767+v[d],M(o[(m<<4)+p[i]]),M(l[i]),d++}return 63!=c&&M(a),e}function X(r){if(r<=0&&(r=1),r>100&&(r=100),a!=r){(function(r){for(var t=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],e=0;e<64;e++){var n=f((t[e]*r+50)/100);n<1?n=1:n>255&&(n=255),s[U[e]]=n}for(var o=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],i=0;i<64;i++){var a=f((o[i]*r+50)/100);a<1?a=1:a>255&&(a=255),u[U[i]]=a}for(var l=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],p=0,y=0;y<8;y++)for(var v=0;v<8;v++)h[p]=1/(s[U[p]]*l[y]*l[v]*8),c[p]=1/(u[U[p]]*l[y]*l[v]*8),p++})(r<50?Math.floor(5e3/r):Math.floor(200-2*r)),a=r}}this.encode=function(r,a){(new Date).getTime();a&&X(a),g=new Array,d=0,w=7,j(65496),j(65504),j(16),S(74),S(70),S(73),S(70),S(0),S(1),S(1),S(0),j(1),j(1),S(0),S(0),function(){j(65499),j(132),S(0);for(var r=0;r<64;r++)S(s[r]);S(1);for(var t=0;t<64;t++)S(u[t])}(),function(r,t){j(65472),j(17),S(8),j(t),j(r),S(3),S(1),S(17),S(0),S(2),S(17),S(1),S(3),S(17),S(1)}(r.width,r.height),function(){j(65476),j(418),S(0);for(var r=0;r<16;r++)S(T[r+1]);for(var t=0;t<=11;t++)S(L[t]);S(16);for(var e=0;e<16;e++)S(k[e+1]);for(var n=0;n<=161;n++)S(C[n]);S(1);for(var o=0;o<16;o++)S(x[o+1]);for(var i=0;i<=11;i++)S(I[i]);S(17);for(var a=0;a<16;a++)S(O[a+1]);for(var f=0;f<=161;f++)S(R[f])}(),j(65498),j(12),S(3),S(1),S(0),S(2),S(17),S(3),S(17),S(0),S(63),S(0);var f=0,l=0,p=0;d=0,w=7,this.encode.displayName="_encode_";for(var y,v,E,U,P,z,Y,N,_,q=r.data,G=r.width,F=r.height,H=4*G,W=0;W<F;){for(y=0;y<H;){for(z=P=H*W+y,Y=-1,N=0,_=0;_<64;_++)z=P+(N=_>>3)*H+(Y=4*(7&_)),W+N>=F&&(z-=H*(W+1+N-F)),y+Y>=H&&(z-=y+Y-H+4),v=q[z++],E=q[z++],U=q[z++],m[_]=(B[v]+B[E+256>>0]+B[U+512>>0]>>16)-128,b[_]=(B[v+768>>0]+B[E+1024>>0]+B[U+1280>>0]>>16)-128,A[_]=(B[v+1280>>0]+B[E+1536>>0]+B[U+1792>>0]>>16)-128;f=D(m,h,f,t,o),l=D(b,c,l,e,i),p=D(A,c,p,e,i),y+=32}W+=8}if(w>=0){var V=[];V[1]=w+1,V[0]=(1<<w+1)-1,M(V)}return j(65497),new n(g)},function(){(new Date).getTime();r||(r=50),function(){for(var r=String.fromCharCode,t=0;t<256;t++)E[t]=r(t)}(),t=P(T,L),e=P(x,I),o=P(k,C),i=P(O,R),function(){for(var r=1,t=2,e=1;e<=15;e++){for(var n=r;n<t;n++)p[32767+n]=e,l[32767+n]=[],l[32767+n][1]=e,l[32767+n][0]=n;for(var o=-(t-1);o<=-r;o++)p[32767+o]=e,l[32767+o]=[],l[32767+o][1]=e,l[32767+o][0]=t-1+o;r<<=1,t<<=1}}(),function(){for(var r=0;r<256;r++)B[r]=19595*r,B[r+256>>0]=38470*r,B[r+512>>0]=7471*r+32768,B[r+768>>0]=-11059*r,B[r+1024>>0]=-21709*r,B[r+1280>>0]=32768*r+8421375,B[r+1536>>0]=-27439*r,B[r+1792>>0]=-5329*r}(),X(r),(new Date).getTime()}()}r.exports=function(r,t){void 0===t&&(t=50);return{data:new o(t).encode(r,t),width:r.width,height:r.height}}}},t={};function e(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return r[n](i,i.exports,e),i.exports}e.n=r=>{var t=r&&r.__esModule?()=>r.default:()=>r;return e.d(t,{a:t}),t},e.d=(r,t)=>{for(var n in t)e.o(t,n)&&!e.o(r,n)&&Object.defineProperty(r,n,{enumerable:!0,get:t[n]})},e.o=(r,t)=>Object.prototype.hasOwnProperty.call(r,t),(()=>{"use strict";var r=e(870);self.addEventListener("message",(t=>{const e=t.data,n=(0,r.decode)(e),o=n.data.buffer;postMessage({data:o,width:n.width,height:n.height},[o])}))})()})();',"Worker",void 0,void 0)}))},20477:r=>{r.exports=function(r,t,e,n){var o=self||window;try{try{var i;try{i=new o.Blob([r])}catch(t){(i=new(o.BlobBuilder||o.WebKitBlobBuilder||o.MozBlobBuilder||o.MSBlobBuilder)).append(r),i=i.getBlob()}var a=o.URL||o.webkitURL,f=a.createObjectURL(i),s=new o[t](f,e);return a.revokeObjectURL(f),s}catch(n){return new o[t]("data:application/javascript,".concat(encodeURIComponent(r)),e)}}catch(r){if(!n)throw Error("Inline worker is not supported");return new o[t](n,e)}}}}]);