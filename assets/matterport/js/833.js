/*! For license information please see 833.js.LICENSE.txt */
(self.webpackChunkmp_webgl=self.webpackChunkmp_webgl||[]).push([[833],{43517:(t,e,i)=>{"use strict";function s(t,e=!1){let i=0;for(let e=0,s=t.length;e<s;e++){const n=t[e],a=t[e===s-1?0:e+1];i+=n[0]*a[1],i-=a[0]*n[1]}return e?i/2:Math.abs(i/2)}i.d(e,{m:()=>s})},5696:(t,e,i)=>{"use strict";function s(t){return Math.sqrt(Math.pow(t[1][0]-t[0][0],2)+Math.pow(t[1][1]-t[0][1],2))}function n(t,e,i=0){const n=s(e);return function(t,e,i=0){return Math.abs((s=t,n=e[0],a=e[1],(s[0]-a[0])*(n[1]-a[1])-(s[1]-a[1])*(n[0]-a[0])))<=i;var s,n,a}(t,e,i)&&s([e[0],t])<=n&&s([e[1],t])<=n}i.d(e,{s8:()=>n})},65661:(t,e,i)=>{"use strict";function s(t){return function(t){const e=t[0],i=t[t.length-1];return e[0]===i[0]&&e[1]===i[1]}(t)?t:[...t,t[0]]}i.d(e,{D:()=>h});var n=i(5696);function a(t,e){const[[i,s],[a,o]]=t,[[r,h],[d,l]]=e;if(i===r&&s===h)return!0;if(a===d&&o===l)return!0;if((0,n.s8)(t[0],e)||(0,n.s8)(t[1],e))return!0;if((0,n.s8)(e[0],t)||(0,n.s8)(e[1],t))return!0;const c=(l-h)*(a-i)-(d-r)*(o-s);if(0===c)return!1;const u=s-h,p=i-r,m=((d-r)*u-(l-h)*p)/c,g=((a-i)*u-(o-s)*p)/c;return m>0&&m<1&&g>0&&g<1}function o(t,e){let i=!1;const o=s(e);for(let e=0,s=o.length-1;e<s;e++){const s=o[e],r=o[e+1];if(a(t,[s,r])||(0,n.s8)(s,t)&&(0,n.s8)(r,t)){i=!0;break}}return i}function r(t,e){let i=t[0],s=t[1],n=!1;for(let t=0,a=e.length-1;t<e.length;a=t++){const o=e[t][0],r=e[t][1],h=e[a][0],d=e[a][1];r>s!=d>s&&i<(h-o)*(s-r)/(d-r)+o&&(n=!n)}return n}function h(t,e){let i=!0;const n=s(t);for(let t=0,s=n.length-1;t<s;t++){const s=n[t];if(!r(s,e)){i=!1;break}if(o([s,n[t+1]],e)){i=!1;break}}return i}},4061:(t,e,i)=>{"use strict";var s=i(43842);function n(t,e,i){var n,r,h,d;e=e||1;for(var l=0;l<t[0].length;l++){var c=t[0][l];(!l||c[0]<n)&&(n=c[0]),(!l||c[1]<r)&&(r=c[1]),(!l||c[0]>h)&&(h=c[0]),(!l||c[1]>d)&&(d=c[1])}var u=h-n,p=d-r,m=Math.min(u,p),g=m/2;if(0===m){var v=[n,r];return v.distance=0,v}for(var f=new s(void 0,a),y=n;y<h;y+=m)for(var w=r;w<d;w+=m)f.push(new o(y+g,w+g,g,t));var b=function(t){for(var e=0,i=0,s=0,n=t[0],a=0,r=n.length,h=r-1;a<r;h=a++){var d=n[a],l=n[h],c=d[0]*l[1]-l[0]*d[1];i+=(d[0]+l[0])*c,s+=(d[1]+l[1])*c,e+=3*c}return 0===e?new o(n[0][0],n[0][1],0,t):new o(i/e,s/e,0,t)}(t),S=new o(n+u/2,r+p/2,0,t);S.d>b.d&&(b=S);for(var D=f.length;f.length;){var I=f.pop();I.d>b.d&&(b=I,i&&console.log("found best %d after %d probes",Math.round(1e4*I.d)/1e4,D)),I.max-b.d<=e||(g=I.h/2,f.push(new o(I.x-g,I.y-g,g,t)),f.push(new o(I.x+g,I.y-g,g,t)),f.push(new o(I.x-g,I.y+g,g,t)),f.push(new o(I.x+g,I.y+g,g,t)),D+=4)}i&&(console.log("num probes: "+D),console.log("best distance: "+b.d));var P=[b.x,b.y];return P.distance=b.d,P}function a(t,e){return e.max-t.max}function o(t,e,i,s){this.x=t,this.y=e,this.h=i,this.d=function(t,e,i){for(var s=!1,n=1/0,a=0;a<i.length;a++)for(var o=i[a],h=0,d=o.length,l=d-1;h<d;l=h++){var c=o[h],u=o[l];c[1]>e!=u[1]>e&&t<(u[0]-c[0])*(e-c[1])/(u[1]-c[1])+c[0]&&(s=!s),n=Math.min(n,r(t,e,c,u))}return 0===n?0:(s?1:-1)*Math.sqrt(n)}(t,e,s),this.max=this.d+this.h*Math.SQRT2}function r(t,e,i,s){var n=i[0],a=i[1],o=s[0]-n,r=s[1]-a;if(0!==o||0!==r){var h=((t-n)*o+(e-a)*r)/(o*o+r*r);h>1?(n=s[0],a=s[1]):h>0&&(n+=o*h,a+=r*h)}return(o=t-n)*o+(r=e-a)*r}s.default&&(s=s.default),t.exports=n,t.exports.default=n},82582:function(t){t.exports=function(){"use strict";function t(t,s,n,a,o){!function t(i,s,n,a,o){for(;a>n;){if(a-n>600){var r=a-n+1,h=s-n+1,d=Math.log(r),l=.5*Math.exp(2*d/3),c=.5*Math.sqrt(d*l*(r-l)/r)*(h-r/2<0?-1:1);t(i,s,Math.max(n,Math.floor(s-h*l/r+c)),Math.min(a,Math.floor(s+(r-h)*l/r+c)),o)}var u=i[s],p=n,m=a;for(e(i,n,s),o(i[a],u)>0&&e(i,n,a);p<m;){for(e(i,p,m),p++,m--;o(i[p],u)<0;)p++;for(;o(i[m],u)>0;)m--}0===o(i[n],u)?e(i,n,m):e(i,++m,a),m<=s&&(n=m+1),s<=m&&(a=m-1)}}(t,s,n||0,a||t.length-1,o||i)}function e(t,e,i){var s=t[e];t[e]=t[i],t[i]=s}function i(t,e){return t<e?-1:t>e?1:0}var s=function(t){void 0===t&&(t=9),this._maxEntries=Math.max(4,t),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function n(t,e,i){if(!i)return e.indexOf(t);for(var s=0;s<e.length;s++)if(i(t,e[s]))return s;return-1}function a(t,e){o(t,0,t.children.length,e,t)}function o(t,e,i,s,n){n||(n=m(null)),n.minX=1/0,n.minY=1/0,n.maxX=-1/0,n.maxY=-1/0;for(var a=e;a<i;a++){var o=t.children[a];r(n,t.leaf?s(o):o)}return n}function r(t,e){return t.minX=Math.min(t.minX,e.minX),t.minY=Math.min(t.minY,e.minY),t.maxX=Math.max(t.maxX,e.maxX),t.maxY=Math.max(t.maxY,e.maxY),t}function h(t,e){return t.minX-e.minX}function d(t,e){return t.minY-e.minY}function l(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function c(t){return t.maxX-t.minX+(t.maxY-t.minY)}function u(t,e){return t.minX<=e.minX&&t.minY<=e.minY&&e.maxX<=t.maxX&&e.maxY<=t.maxY}function p(t,e){return e.minX<=t.maxX&&e.minY<=t.maxY&&e.maxX>=t.minX&&e.maxY>=t.minY}function m(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function g(e,i,s,n,a){for(var o=[i,s];o.length;)if(!((s=o.pop())-(i=o.pop())<=n)){var r=i+Math.ceil((s-i)/n/2)*n;t(e,r,i,s,a),o.push(i,r,r,s)}}return s.prototype.all=function(){return this._all(this.data,[])},s.prototype.search=function(t){var e=this.data,i=[];if(!p(t,e))return i;for(var s=this.toBBox,n=[];e;){for(var a=0;a<e.children.length;a++){var o=e.children[a],r=e.leaf?s(o):o;p(t,r)&&(e.leaf?i.push(o):u(t,r)?this._all(o,i):n.push(o))}e=n.pop()}return i},s.prototype.collides=function(t){var e=this.data;if(!p(t,e))return!1;for(var i=[];e;){for(var s=0;s<e.children.length;s++){var n=e.children[s],a=e.leaf?this.toBBox(n):n;if(p(t,a)){if(e.leaf||u(t,a))return!0;i.push(n)}}e=i.pop()}return!1},s.prototype.load=function(t){if(!t||!t.length)return this;if(t.length<this._minEntries){for(var e=0;e<t.length;e++)this.insert(t[e]);return this}var i=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===i.height)this._splitRoot(this.data,i);else{if(this.data.height<i.height){var s=this.data;this.data=i,i=s}this._insert(i,this.data.height-i.height-1,!0)}else this.data=i;return this},s.prototype.insert=function(t){return t&&this._insert(t,this.data.height-1),this},s.prototype.clear=function(){return this.data=m([]),this},s.prototype.remove=function(t,e){if(!t)return this;for(var i,s,a,o=this.data,r=this.toBBox(t),h=[],d=[];o||h.length;){if(o||(o=h.pop(),s=h[h.length-1],i=d.pop(),a=!0),o.leaf){var l=n(t,o.children,e);if(-1!==l)return o.children.splice(l,1),h.push(o),this._condense(h),this}a||o.leaf||!u(o,r)?s?(i++,o=s.children[i],a=!1):o=null:(h.push(o),d.push(i),i=0,s=o,o=o.children[0])}return this},s.prototype.toBBox=function(t){return t},s.prototype.compareMinX=function(t,e){return t.minX-e.minX},s.prototype.compareMinY=function(t,e){return t.minY-e.minY},s.prototype.toJSON=function(){return this.data},s.prototype.fromJSON=function(t){return this.data=t,this},s.prototype._all=function(t,e){for(var i=[];t;)t.leaf?e.push.apply(e,t.children):i.push.apply(i,t.children),t=i.pop();return e},s.prototype._build=function(t,e,i,s){var n,o=i-e+1,r=this._maxEntries;if(o<=r)return a(n=m(t.slice(e,i+1)),this.toBBox),n;s||(s=Math.ceil(Math.log(o)/Math.log(r)),r=Math.ceil(o/Math.pow(r,s-1))),(n=m([])).leaf=!1,n.height=s;var h=Math.ceil(o/r),d=h*Math.ceil(Math.sqrt(r));g(t,e,i,d,this.compareMinX);for(var l=e;l<=i;l+=d){var c=Math.min(l+d-1,i);g(t,l,c,h,this.compareMinY);for(var u=l;u<=c;u+=h){var p=Math.min(u+h-1,c);n.children.push(this._build(t,u,p,s-1))}}return a(n,this.toBBox),n},s.prototype._chooseSubtree=function(t,e,i,s){for(;s.push(e),!e.leaf&&s.length-1!==i;){for(var n=1/0,a=1/0,o=void 0,r=0;r<e.children.length;r++){var h=e.children[r],d=l(h),c=(u=t,p=h,(Math.max(p.maxX,u.maxX)-Math.min(p.minX,u.minX))*(Math.max(p.maxY,u.maxY)-Math.min(p.minY,u.minY))-d);c<a?(a=c,n=d<n?d:n,o=h):c===a&&d<n&&(n=d,o=h)}e=o||e.children[0]}var u,p;return e},s.prototype._insert=function(t,e,i){var s=i?t:this.toBBox(t),n=[],a=this._chooseSubtree(s,this.data,e,n);for(a.children.push(t),r(a,s);e>=0&&n[e].children.length>this._maxEntries;)this._split(n,e),e--;this._adjustParentBBoxes(s,n,e)},s.prototype._split=function(t,e){var i=t[e],s=i.children.length,n=this._minEntries;this._chooseSplitAxis(i,n,s);var o=this._chooseSplitIndex(i,n,s),r=m(i.children.splice(o,i.children.length-o));r.height=i.height,r.leaf=i.leaf,a(i,this.toBBox),a(r,this.toBBox),e?t[e-1].children.push(r):this._splitRoot(i,r)},s.prototype._splitRoot=function(t,e){this.data=m([t,e]),this.data.height=t.height+1,this.data.leaf=!1,a(this.data,this.toBBox)},s.prototype._chooseSplitIndex=function(t,e,i){for(var s,n,a,r,h,d,c,u=1/0,p=1/0,m=e;m<=i-e;m++){var g=o(t,0,m,this.toBBox),v=o(t,m,i,this.toBBox),f=(n=g,a=v,r=void 0,h=void 0,d=void 0,c=void 0,r=Math.max(n.minX,a.minX),h=Math.max(n.minY,a.minY),d=Math.min(n.maxX,a.maxX),c=Math.min(n.maxY,a.maxY),Math.max(0,d-r)*Math.max(0,c-h)),y=l(g)+l(v);f<u?(u=f,s=m,p=y<p?y:p):f===u&&y<p&&(p=y,s=m)}return s||i-e},s.prototype._chooseSplitAxis=function(t,e,i){var s=t.leaf?this.compareMinX:h,n=t.leaf?this.compareMinY:d;this._allDistMargin(t,e,i,s)<this._allDistMargin(t,e,i,n)&&t.children.sort(s)},s.prototype._allDistMargin=function(t,e,i,s){t.children.sort(s);for(var n=this.toBBox,a=o(t,0,e,n),h=o(t,i-e,i,n),d=c(a)+c(h),l=e;l<i-e;l++){var u=t.children[l];r(a,t.leaf?n(u):u),d+=c(a)}for(var p=i-e-1;p>=e;p--){var m=t.children[p];r(h,t.leaf?n(m):m),d+=c(h)}return d},s.prototype._adjustParentBBoxes=function(t,e,i){for(var s=i;s>=0;s--)r(e[s],t)},s.prototype._condense=function(t){for(var e=t.length-1,i=void 0;e>=0;e--)0===t[e].children.length?e>0?(i=t[e-1].children).splice(i.indexOf(t[e]),1):this.clear():a(t[e],this.toBBox)},s}()},43842:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});class s{constructor(t=[],e=n){if(this.data=t,this.length=this.data.length,this.compare=e,this.length>0)for(let t=(this.length>>1)-1;t>=0;t--)this._down(t)}push(t){this.data.push(t),this.length++,this._up(this.length-1)}pop(){if(0===this.length)return;const t=this.data[0],e=this.data.pop();return this.length--,this.length>0&&(this.data[0]=e,this._down(0)),t}peek(){return this.data[0]}_up(t){const{data:e,compare:i}=this,s=e[t];for(;t>0;){const n=t-1>>1,a=e[n];if(i(s,a)>=0)break;e[t]=a,t=n}e[t]=s}_down(t){const{data:e,compare:i}=this,s=this.length>>1,n=e[t];for(;t<s;){let s=1+(t<<1),a=e[s];const o=s+1;if(o<this.length&&i(e[o],a)<0&&(s=o,a=e[o]),i(a,n)>=0)break;e[t]=a,t=s}e[t]=n}}function n(t,e){return t<e?-1:t>e?1:0}},48603:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>o});var s=i(97542),n=i(92810),a=i(51524);class o extends s.Y{constructor(){super(...arguments),this.name="automation-support"}async init(t,e){const i=window;if(i.MP_AUTOMATION)this.addAutomationHooks(e,i.MP_AUTOMATION);else{const t=performance.now(),s=setInterval((()=>{i.MP_AUTOMATION?(this.addAutomationHooks(e,i.MP_AUTOMATION),clearInterval(s)):performance.now()-t>5e3&&clearInterval(s)}),100)}}async addAutomationHooks(t,e){const i=await t.getModuleBySymbol(n.Aj);e.estimatedGPUMemoryAllocated=()=>i.estimatedGPUMemoryAllocated(),e.maxLOD=()=>a.t.maxLOD}}},65792:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>v});var s=i(97542),n=i(92810),a=i(9037),o=i(54244),r=i(23254),h=i(95882),d=i(95912),l=i(80361),c=i(9133),u=i(2569),p=i(59296);function m(t){const e=(0,p.J5)(u.ep.toVisionQuaternion(t.rotation));return{position:(0,p.m)(u.ep.toVisionVector(t.position)),rotation:e,aspect:t.aspect(),isOrtho:t.isOrtho(),fovX:t.fovX(),fovY:t.fovY()}}function g(t){return(0,c.U)(new Date(t))}class v extends s.Y{constructor(){super(...arguments),this.name="dwell-analytics",this.pendingDwellEvents=[],this.onCameraChange=(0,d.P)((t=>{this.checkForDwellEvent(),t.clone(this.currentEvent.pose),this.currentEvent.startTimeMs=Date.now(),this.scheduleDwellTimeOutEvent()}),1e3),this.checkForDwellEvent=(t=!1)=>{const{startTimeMs:e,viewmode:i}=this.currentEvent;if(!e||this.appData.phase!==o.nh.PLAYING)return;const s=Date.now(),n=s-e,a={pose:m(this.currentEvent.pose),durationMs:n,startTime:g(e),endTime:g(s),timedOut:t,viewmode:i};this.trackDwellEvent(a)},this.onViewmodeUpdate=t=>{t.value!==h.Ey.Transition&&(this.currentEvent.viewmode=(0,h.Ae)(t.value))},this.trackDwellEvent=t=>{this.pendingDwellEvents.push(t),this.trackPendingDwellEvents()},this.trackPendingDwellEvents=(0,d.P)((()=>{const t={events:JSON.stringify(this.pendingDwellEvents)};this.pendingDwellEvents=[],this.analytics.track("dwell_events",t)}),5e3),this.onBlur=()=>this.stopTrackingDwellTime(!1),this.stopTrackingDwellTime=(t=!1)=>{this.checkForDwellEvent(t),delete this.currentEvent.startTimeMs},this.resumeTrackingDwellTime=()=>{this.currentEvent.startTimeMs||(this.currentEvent.startTimeMs=Date.now()),this.scheduleDwellTimeOutEvent()},this.throttledResumeTrackingDwellTime=(0,d.P)(this.resumeTrackingDwellTime,100),this.scheduleDwellTimeOutEvent=(0,l.D)((()=>this.stopTrackingDwellTime(!0)),15e3)}async init(t,e){[this.analytics,this.cameraData,this.appData,this.viewmodeData]=await Promise.all([e.getModuleBySymbol(n.V6),e.market.waitForData(a.M),e.market.waitForData(o.pu),e.market.waitForData(r.O)]),this.currentEvent={pose:this.cameraData.pose.clone(),viewmode:(0,h.Ae)(null)},this.bindings.push(this.cameraData.pose.onChanged(this.onCameraChange)),this.bindings.push(this.viewmodeData.onPropertyChanged("currentModeObservable",this.onViewmodeUpdate)),window.addEventListener("blur",this.onBlur),window.addEventListener("focus",this.resumeTrackingDwellTime),window.addEventListener("keydown",this.resumeTrackingDwellTime,{capture:!0}),window.addEventListener("touchcancel",this.resumeTrackingDwellTime),window.addEventListener("touchstart",this.resumeTrackingDwellTime),window.addEventListener("touchend",this.resumeTrackingDwellTime),window.addEventListener("touchmove",this.throttledResumeTrackingDwellTime),window.addEventListener("mousemove",this.throttledResumeTrackingDwellTime)}dispose(t){super.dispose(t),window.removeEventListener("blur",this.onBlur),window.removeEventListener("focus",this.resumeTrackingDwellTime),window.removeEventListener("keydown",this.resumeTrackingDwellTime,{capture:!0}),window.removeEventListener("touchcancel",this.resumeTrackingDwellTime),window.removeEventListener("touchstart",this.resumeTrackingDwellTime),window.removeEventListener("touchend",this.resumeTrackingDwellTime),window.removeEventListener("touchmove",this.throttledResumeTrackingDwellTime),window.removeEventListener("mousemove",this.throttledResumeTrackingDwellTime)}}},86034:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>L});var s=i(97542),n=i(92810),a=i(79728),o=i(635),r=i(64567),h=i(71954),d=i(2541),l=i(34361),c=i(17788),u=i(97998),p=i(75287),m=i(81396);class g extends p.T{constructor(){super(...arguments),this.position=new m.Vector3,this.floorId="",this.sid="",this.layerId="",this.text="",this.visible=!1,this.roomId=void 0,this.created=new Date,this.modified=new Date,this.version="3.1"}copy(t){return this.floorId=t.floorId,this.roomId=t.roomId,this.sid=t.sid,this.text=t.text,this.visible=t.visible,this.roomId=t.roomId,this.created=t.created,this.modified=t.modified,this.position.copy(t.position),this.version=t.version,this.commit(),this}}var v=i(40731),f=i(2569);const y=new u.Z("mds-label-serialize");class w{constructor(){this.validate=t=>{if(!t)return!1;const e=["enabled","floorId","label","position"].filter((e=>!(e in t))),i=0===e.length,s=!!t.floorId&&"string"==typeof t.floorId,n=!!t.position&&(0,v.u)(t.position),a=i&&s&&n;return a||y.debug("Label invalid:",{missingFields:e,validPosition:n}),a}}serialize(t,e){const i=S(t,e);return this.validate(i)?i:null}}class b{constructor(){this.validate=t=>{if(!t)return!1;const e=["enabled","label","position","floorId","roomId","layerId"];return Object.keys(t).every((t=>e.includes(t)))}}serialize(t,e){const i=S(t,e);return i&&this.validate(i)?i:null}}const S=(t,e)=>{const i={};return void 0!==t.visible&&(i.enabled=t.visible),void 0!==t.text&&(i.label=t.text),void 0!==t.position&&(0,v.u)(t.position)&&(i.position=f.ep.toVisionVector(t.position)),void 0!==t.floorId&&""!==t.floorId&&(i.floorId=t.floorId),void 0!==t.roomId&&""!==t.roomId&&(i.roomId=t.roomId),e&&void 0!==t.layerId&&""!==t.layerId&&(i.layerId=t.layerId),Object.keys(i).length>0?i:null};var D=i(9133);const I=new u.Z("mds-label-deserializer");class P{constructor(){this.validate=t=>{if(!t)return!1;const e=["id","created","modified","enabled","floor","label","position"].filter((e=>!(e in t))),i=0===e.length,s=!(!t.floor||!t.floor.id),n=!!t.position&&(0,v.u)(t.position);return i&&s&&n||I.debug("Label invalid:",{missingFields:e,validFloor:s,validPosition:n}),i&&s&&n}}deserialize(t){var e;if(!t||!this.validate(t))return I.debug("Deserialized invalid Label data from MDS",t),null;const i=new g;return i.sid=t.id,i.layerId=(null===(e=t.layer)||void 0===e?void 0:e.id)||"",i.floorId=t.floor.id,i.text=t.label,i.visible=!!t.enabled,i.created=(0,D.p)(t.created),i.modified=(0,D.p)(t.modified),i.position=f.ep.fromVisionVector(t.position),t.room&&t.room.id&&(i.roomId=t.room.id),i}}var T=i(61341),M=i(97704),E=i(39011),x=i(5823);const C=new u.Z("MdsLabelStore");class R extends c.u{constructor(t){super(t),this.prefetchKey="data.model.labels",this.layeredType=x.SF.LABEL,this.deserializer=new P,this.updateSerializer=new b,this.createSerializer=new w}async read(t){const{includeDisabled:e=!1}=this.config,i={modelId:this.getViewId(),includeDisabled:e,includeLayers:this.readLayerId()};return this.query(T.GetLabels,i,t).then((t=>{var e,i;const s=null===(i=null===(e=null==t?void 0:t.data)||void 0===e?void 0:e.model)||void 0===i?void 0:i.labels;if(!s||!Array.isArray(s))return null;const n=[];for(const t of s){const e=this.deserializer.deserialize(t);e&&n.push(e)}return n.reduce(((t,e)=>(t[e.sid]=e,t)),{})}))}async create(t){const e=this.getViewId(),i=[];for(const s of t){const t=this.createSerializer.serialize(s,this.writeLayerId(s.layerId));if(!t)throw C.error("Failure saving label:",s.sid,s),new Error("Could not save Label");const n={modelId:e,labelId:s.sid,data:t,includeLayers:this.readLayerId()};await this.mutate(T.AddLabel,n).then((t=>{var e;const n=null===(e=t.data)||void 0===e?void 0:e.addLabel;if(!n)throw new Error("Could not save label: empty response");const a=(new g).copy(s);a.sid=n.id,a.commit(),C.debug(Object.assign({type:"addLabel"},t)),i.push(a)}))}return i}async update(t){if(!t||0===t.length)return;const e=this.getViewId();let i="";const s={};s.modelId=e,s.includeLayers=this.readLayerId();let n="";for(const e of t){const t=e.sid,a=this.updateSerializer.serialize(e,!1);if(!a)throw C.error("Failure updating label:",t,e),new Error("Could not update Label");i+=`, $patch${t}: LabelPatch!`,s[`patch${t}`]=a,n+=`patch${t}:\n        patchLabel(modelId: $modelId, labelId: "${t}", patch: $patch${t}) {\n          ...LabelDetails\n        }\n      `}const a=M.gql`
      mutation labelUpdate($modelId: ID! ${i}, $includeLayers: Boolean!) {
        ${n}
      }

      ${(0,E.S)(T.LabelDetails)}
    `;return this.mutate(a,s).then((t=>{C.debug(Object.assign({type:"patchLabel"},t))}))}async delete(t){if(!t||0===t.length)return;const e=this.getViewId();let i="";for(const e of t){if(!e||e&&!e.sid)throw C.error("Failure deleting label:",e),new Error("Could not update Label");i+=`delete${e.sid}: deleteLabel(modelId: $modelId, labelId: "${e.sid}") `}const s=M.gql`
      mutation batchDeleteLabel($modelId: ID!) {
        ${i}
      }
    `;return this.mutate(s,{modelId:e}).then((()=>{}))}}var A=i(80742);class L extends s.Y{constructor(){super(...arguments),this.name="label-data",this.monitor=null}async init(t,e){const{readonly:i}=t;this.engine=e,this.market=e.market,this.layersData=await e.market.waitForData(A.R),this.store=new R({context:this.layersData.mdsContext,readonly:t.readonly,includeDisabled:!t.readonly,baseUrl:t.baseUrl});const s=await this.store.read()||{},o=await e.getModuleBySymbol(n.Lx),h=await e.getModuleBySymbol(n.hi);for(const t of Object.values(s))h.inferMeshIdsFromPoint(t,t.position,!1);this.labelData=e.market.tryGetData(l.D)||new l.D(s),this.store.onNewData((async t=>{var e;this.labelData.atomic((()=>{this.layersData.replaceBackendLayers(this.labelData.getCollection(),{})})),t&&this.labelData.atomic((()=>{this.layersData.replaceBackendLayers(this.labelData.getCollection(),t)})),null===(e=this.monitor)||void 0===e||e.clearDiffRecord()})),await this.store.refresh(),this.market.register(this,l.D,this.labelData),i||(this.monitor=new r.c(this.labelData.getCollection(),{aggregationType:d.E.Manual,shallow:!0},this.engine),this.bindings.push(o.onSave((()=>this.saveDiff()),{dataType:a.g.LABELS})))}dispose(t){this.store.dispose(),super.dispose(t)}async save(){return this.engine.commandBinder.issueCommand(new o.V({dataTypes:[a.g.LABELS]}))}getDiffRecord(){var t;return(null===(t=this.monitor)||void 0===t?void 0:t.getDiffRecord())||[]}async saveDiff(){if(!this.store||!this.monitor)return void this.log.warn("Labels changes will NOT be saved");this.monitor.commitChanges();const t=this.monitor.getDiffRecord();this.monitor.clearDiffRecord();const e=t.map((t=>{var e;const i=t.diff.layerId||(null===(e=this.labelData.getLabel(t.index))||void 0===e?void 0:e.layerId);return Object.assign(Object.assign({},t),{layerId:i})})).filter((t=>!this.layersData.isInMemoryLayer(t.layerId))),i=e.filter((t=>t.action===h.KI.removed)).map((t=>({sid:t.index,layerId:t.layerId}))),s=e.filter((t=>t.action===h.KI.added)).map((t=>this.labelData.getLabel(t.index))),n=e.filter((t=>t.action===h.KI.updated)).map((t=>Object.assign(Object.assign({sid:t.index},t.diff),{layerId:t.layerId})));return Promise.all([this.store.delete(i),this.store.create(s),this.store.update(n)]).then((()=>{}))}onUpdate(t){}}},92543:(t,e,i)=>{"use strict";i.r(e),i.d(e,{USER_LABELS:()=>T,default:()=>mt});var s=i(97542),n=i(92810),a=i(34361),o=i(9037),r=i(79727),h=i(91380),d=i(76532),l=i(53954),c=i(28269),u=i(34029),p=i(12529),m=i(67971),g=i(81396);const v={ColorDefault:16777215,ColorHovered:i(15462).I.MP_BRAND.getHex(),ColorInvalid:16750933};class f extends m.$7{use(t){this.userData.sid=t.data.sid,this.collider.data=t.data,this.collider.name=t.data.text,this.collider.labelMesh=t}}class y extends g.Mesh{constructor(){super(...arguments),this.hitTest=(()=>{const t=new g.Matrix4,e=new g.Ray,i=new g.Plane(new g.Vector3(0,0,-1)),s=new g.Vector3;return(n,a)=>{if(t.copy(this.matrixWorld).invert(),e.copy(n.ray).applyMatrix4(t),e.intersectPlane(i,s)){const t=this.scale.x/this.scale.y;let e=.5;t<1.5&&(e=1/t),Math.abs(s.x)<=e&&Math.abs(s.y)<=.75&&(s.applyMatrix4(this.matrixWorld),a.push({distance:n.ray.origin.distanceTo(s),point:s.clone(),object:this}))}}})()}labelVisible(){return!(!this.labelMesh||!this.labelMesh.labelVisible())}getId(){if(!this.data)throw new Error("LabelInputCollider used before configure");return this.data.sid}raycast(t,e){if(!this.labelVisible())return;if(this.material.depthTest)return void this.hitTest(t,e);const i=this.material.depthTest?e:[];this.hitTest(t,i),i.length>0&&(i[0].distance/=1e4,e.push(i[0]))}}class w{constructor(t={}){this.makeLabel=()=>{const t=new f(Object.assign({},this.textStyle));return t.scaleType=m.N3.WORLD,t.setRenderOrder(p.z.labels),t.setRenderLayer(this.layer),t.opacity=0,t},this.textStyle=m.uc.makeConfig(Object.assign({color:"white",background:!1,backgroundAsCollider:!0,backgroundColliderType:y,backgroundColor:"#222",backgroundOpacity:1,outline:!0,outlineWidth:.06,wordWrapWidth:void 0,disableDepth:!0},t))}setRenderLayer(t){this.layer=t}}var b=i(19663);class S extends b.m{constructor(t){super(),this.id="FILTER_LABEL_VISIBILITY",this.payload={ids:t}}}class D extends b.m{constructor(t){super(),this.id="VISIBILITY_FILTER_ENABLED",this.payload={enabled:t}}}var I=i(15637),P=i(89557);const T={LABEL_SIZE:0,FADE_DURATION:200};class M extends g.Object3D{constructor(t){super(),this.text=t,this.maxOpacity=1,this.labelAnim=new P.z(0),this.filtered=!1,this.hidden=!1,this.selectState={active:!1,on:()=>{},off:()=>{}},this.validState={active:!0,on:()=>{const t=this.hoverState.active?v.ColorHovered:v.ColorDefault;this.text.setColor(t)},off:()=>{this.text.setColor(v.ColorInvalid)}},this.hoverState={active:!1,on:()=>{this.validState.active&&this.text.setColor(v.ColorHovered)},off:()=>{this.validState.active&&this.text.setColor(v.ColorDefault)}},this.add(t)}use(t){this.data=t,this.userData.sid=t.sid,this.userData.data=t,this.text.use(this),this.labelUpdate(t.position,new g.Quaternion,"")}getId(){return this.data.sid}labelVisible(){return!this.filtered&&!this.hidden}updatePose(t,e){return this.labelUpdate(t,e,this.data.text),this}setMaxOpacity(t){this.maxOpacity=t,this.toggleLabel(this.labelVisible())}free(){this.labelAnim.value>0&&this.toggleLabel(!1)}tickAnimations(t){return this.animateLabelOpacity(.5*t),this}toggleLabel(t){this.hidden=!t,this.updateOpacity()}isHidden(){return this.hidden}toggleFiltered(t){t!==this.filtered&&(this.filtered=t,this.updateOpacity())}updateOpacity(){const t=this.labelVisible()?this.maxOpacity:0;this.labelAnim.endValue!==t&&this.labelAnim.modifyAnimation(this.labelAnim.value,t,T.FADE_DURATION)}animateLabelOpacity(t){const e=this.labelAnim.tick(t);Math.min(e,this.maxOpacity)!==this.text.opacity&&(this.text.opacity=e)}labelUpdate(t,e,i){this.text.position.copy(t),this.text.quaternion.copy(e),this.text.text=i}billboard(t,e,i,s,n,a,o){this.text.scaleBillboard(t,e,i,s,n,a,o)}}var E=i(97998);const x=new E.Z("label-spawner");class C{constructor(t,e){this.map=t,this.maker=e,this.container=new g.Object3D,this.pool=[],this.bindings=[],this.meshesMap=(0,I.q)(),this.container.name="RoomLabels";const i=t.keys;for(const e of i){const i=t.get(e);this.add(i,e)}this.bindings.push(t.onElementChanged({onAdded:(t,e)=>{this.add(t,e),x.debug("LabelMesh: added:",e)},onRemoved:(t,e)=>{x.debug("LabelMesh: removing:",e);const i=this.meshesMap.get(e);i&&this.free(i)}}))}subscribe(t){return this.meshesMap.onElementChanged(t)}get(t){return this.meshesMap.get(t)}free(t){const e=t.getId(),i=this.meshesMap.get(e);i.free(),this.meshesMap.delete(e),this.container.remove(i),this.pool.push(i),x.debug("LabelMesh: removed:",i)}add(t,e){const i=this.get(e);if(i)return i;const s=this.pool.shift();if(void 0!==s)return s.use(t),this.container.add(s),this.meshesMap.set(e,s),s;{const i=new M(this.maker.makeLabel());return i.use(t),this.container.add(i),this.meshesMap.set(e,i),i}}dispose(){for(const t of this.meshesMap.values)this.free(t),t.text.dispose();this.meshesMap.clear();for(const t of this.pool)t.text.dispose();this.pool.length=0,this.bindings.forEach((t=>t.cancel()))}}var R=i(2569);class A{constructor(t,e,i,s){this.meshes=t,this.cameraData=i,this.screenFilter=s,this.dirty=!0,this.bindings=[],this.pendingMesh=null,this.setDirty=()=>{this.dirty=!0},this.bindings.push(i.onChanged(this.setDirty),e.onChanged(this.setDirty));for(const t of this.meshes.values)t.text.onGeomUpdate(this.setDirty);this.meshes.onElementChanged({onAdded:t=>t.text.onGeomUpdate(this.setDirty)})}dispose(){this.bindings.forEach((t=>t.cancel())),this.bindings.length=0}setPendingMeshId(t){if((!this.pendingMesh||this.pendingMesh.getId()!==t)&&(this.pendingMesh&&(this.pendingMesh.data.removeOnChanged(this.setDirty),this.pendingMesh=null),t)){const e=this.meshes.get(t);e.data.onChanged(this.setDirty),this.pendingMesh=e}}beforeRender(){if(!this.dirty)return;this.dirty=!1;const{position:t,rotation:e,projection:i}=this.cameraData.pose,{height:s}=this.cameraData,n=this.cameraData.zoom(),a=this.cameraData.aspect(),o=(0,R.dS)(64-T.LABEL_SIZE,0,64,.02,.1);for(const r of this.meshes.values)r.isHidden()||(r.updatePose(r.data.position,e),r.billboard(t,e,i,n,s,a,o));this.screenFilter.update();for(const t of this.meshes.values)t.isHidden()||t.toggleFiltered(!this.screenFilter.visible(t.data.sid))}render(t){for(const e of this.meshes)e.tickAnimations(t)}deactivate(){}init(){}activate(){}}var L=i(82582),O=i.n(L),k=i(59370);class F{constructor(t,e,i){this.meshes=t,this.cameraData=e,this.enabled=i,this.tree=new(O()),this.visibleMap={},this._screenPosition=new g.Vector2,this._ndcPosition=new g.Vector3,this._cornerWorldPosition=new g.Vector3,this._cornerScreenPosition=new g.Vector2,this._selectedId=null,this.update=()=>{this.enabled()?(this.visibleMap={},this.updatePositions()):Object.keys(this.visibleMap).length&&(this.visibleMap={})}}visible(t){if(!(t in this.visibleMap))return!1;return t in this.visibleMap}setSelectedMeshId(t){this._selectedId=t}updatePositions(){this.tree.clear(),this.meshes().filter((t=>!t.isHidden())).map((t=>{(0,k.q9)(this.cameraData,t.text.position,this._screenPosition,this._ndcPosition);const{width:e,height:i}=t.text.getUnscaledSize();this._cornerWorldPosition.set(.5*e,-.5*i,0),t.text.updateMatrixWorld(),this._cornerWorldPosition.applyMatrix4(t.text.matrixWorld),(0,k.q9)(this.cameraData,this._cornerWorldPosition,this._cornerScreenPosition);const s=t.getId()!==this._selectedId?this._ndcPosition.z:-999;return this.describeBbox(t.text,this._screenPosition,this._cornerScreenPosition,s)})).sort(((t,e)=>t.depth-e.depth)).forEach((t=>{this.tree.collides(t)||(this.tree.insert(t),this.visibleMap[t.id]=t)}))}describeBbox(t,e,i,s){i.sub(e);const n=i.x,a=i.y;return{id:t.userData.sid,depth:s,minX:e.x-n,minY:e.y-a,maxX:e.x+n,maxY:e.y+a}}}var N=i(54244),V=i(59625),B=i(23037),G=i(76631),_=i(23254),H=i(80742);class U{constructor(t){this.meshes=t,this.initialized=!1,this.pendingMeshId=null,this.selectedMeshId=null,this.dirty=!0,this.bindings=[],this.labelIdVisibility={},this.visibilityFilterEnabled=!1,this.featureEnabled=()=>{if(!this.initialized)return!1;const t=this.appData.phase===N.nh.PLAYING,e=this.settingsData.tryGetProperty(B.Nj,!1),i=this.settingsData.tryGetProperty(V.gx.Labels,!1)||this.toolsData.activeToolName===G.w1.LABELS,s=t&&e&&i;return this.settingsData.setProperty(B.Cu,s),s},this.visibleByTool=()=>{if(!this.initialized)return!1;const{activeToolName:t}=this.toolsData;return null===t||t===G.w1.LABELS||t===G.w1.PHOTOS||t===G.w1.SEARCH||t===G.w1.LAYERS},this.visibleByViewmode=()=>{const t=this.settingsData.tryGetProperty(V.gx.LabelsDollhouse,!1)||this.toolsData.activeToolName===G.w1.LABELS,e=this.viewmodeData.isDollhouse()&&t;return this.viewmodeData.isFloorplan()&&(this.appData.application===N.Mx.WORKSHOP&&this.visibleByTool()||this.settingsData.tryGetProperty(V.gx.Labels,!1))||e},this.hiddenByTransition=()=>this.viewmodeData.transitionActive()||this.floorsViewData.transitionActive,this.visibleByFloor=t=>{if(!(this.toolsData.activeToolName===G.w1.LABELS)){const{roomSelectModeActive:t,floorSelectModeActive:e}=this.floorsViewData;if(!t)return!1;if(e)return!1}return this.viewmodeData.isFloorplan()?this.floorsViewData.currentFloor?this.floorsViewData.isCurrentOrAllFloors(t):this.floorsViewData.topFloorId===t:this.floorsViewData.isCurrentOrAllFloors(t)},this.setDirty=()=>{this.dirty=!0}}async init(t){[this.viewmodeData,this.floorsViewData,this.settingsData,this.toolsData,this.appData,this.layersData]=await Promise.all([t.waitForData(_.O),t.waitForData(r.c),t.waitForData(u.e),t.waitForData(h.t),t.waitForData(N.pu),t.waitForData(H.R)]);const e=await t.waitForData(a.D);[this.viewmodeData,this.floorsViewData,this.settingsData,this.toolsData,this.appData,e].forEach((t=>this.bindings.push(t.onChanged(this.setDirty)))),this.bindings.push(this.layersData.onCurrentLayersChanged(this.setDirty)),this.meshes.onChanged(this.setDirty),this.initialized=!0}dispose(){this.bindings.forEach((t=>t.cancel())),this.bindings.length=0}setVisibilityFilterEnabled(t){this.visibilityFilterEnabled=t,this.setDirty()}updateLabelVisibility(t){this.labelIdVisibility=t.reduce(((t,e)=>(t[e]=!0,t)),{}),this.setDirty()}onUpdate(){if(!this.initialized||!this.dirty)return;this.dirty=!1;const t=this.featureEnabled()&&!this.hiddenByTransition()&&this.visibleByTool()&&this.visibleByViewmode(),e=t=>t.visible&&this.visibleByFloor(t.floorId)&&this.visibleById(t.sid)&&this.layerToggledOn(t.layerId)&&this.layersData.layerVisible(t.layerId);for(const i of this.meshes.values){const s=i.data.sid===this.pendingMeshId||i.data.sid===this.selectedMeshId,n=t&&(s||e(i.data));i.toggleLabel(n)}this.dirtyCb&&this.dirtyCb()}setPendingMeshId(t){this.pendingMeshId=t,this.setDirty()}setSelectedMeshId(t){this.selectedMeshId=t,this.setDirty()}setDirtyCallback(t){this.dirtyCb=t}visibleById(t){return!this.visibilityFilterEnabled||!!this.labelIdVisibility[t]}layerToggledOn(t){return this.appData.application===N.Mx.WORKSHOP||this.layersData.layerToggled(t)}}var W=i(41326),j=i(64918),z=i(72996),$=i(86819),q=i(23612),Y=i(35895),Z=i(89570),X=i(38987),K=i(34956),Q=i(31362),J=i(12039);class tt{constructor(t,e,i,s,n,a){this.labelRenderer=t,this.issueCommand=e,this.input=i,this.floorsViewData=s,this.toolsData=n,this.roomNavigationPose=a,this.inputBindings=[],this.appStateBindings=[],this.raycasterRegistrationBindings=[],this.active=!0,this.enabled=!0,this.refresh=()=>{this.shouldBeInteractive!==this.active&&(this.inputBindings.forEach((t=>this.shouldBeInteractive?t.renew():t.cancel())),this.raycasterRegistrationBindings.forEach((t=>this.shouldBeInteractive?t.renew():t.cancel())),this.active=this.shouldBeInteractive)},this.refreshColliders=()=>{for(const t of this.labelRenderer.labelMeshIterator())this.shouldBeInteractive?this.input.registerMesh(t.text.collider,!1):this.input.unregisterMesh(t.text.collider)},this.clearColliders=()=>{for(const t of this.labelRenderer.labelMeshIterator())this.input.unregisterMesh(t.text.collider)},this.inputBindings.push(...this.setupInputBindings()),this.raycasterRegistrationBindings.push(...this.setupRaycasterBindings());const o=this.toolsData.onPropertyChanged("activeToolName",this.refresh),r=this.floorsViewData.makeFloorChangeSubscription(this.refresh),h=this.floorsViewData.onRoomSelectModeChange(this.refresh);this.appStateBindings.push(o,r,h),this.refresh()}toggleInput(t){this.enabled!==t&&(this.enabled=t,this.appStateBindings.forEach((e=>t?e.renew():e.cancel())),this.refresh())}get shouldBeInteractive(){return this.enabled&&null===this.toolsData.activeToolName&&this.floorsViewData.roomSelectModeActive}setupRaycasterBindings(){return[(0,Y.k1)((()=>this.refreshColliders()),(()=>this.clearColliders()),!0,"toggleLabelMeshInput"),this.labelRenderer.subscribe({onAdded:t=>this.input.registerMesh(t.text.collider,!1),onRemoved:t=>this.input.unregisterMesh(t.text.collider)})]}setupInputBindings(){const t=z.s.is((t=>t instanceof y&&t.labelVisible())),e=this.input.registerMeshHandler($.Rd,t,((t,e)=>{const i=this.labelRenderer.getLabelMesh(e.getId());if(i){const t=i.data.roomId;if(t){const e=this.roomNavigationPose.getPoseForRoom(t,i.data.position);e&&this.issueCommand(new W._i(W.BD.INSIDE,j.n.Interpolate,e))}else this.issueCommand(new J.zs({focusPosition:i.data.position,transition:j.n.Interpolate}))}}));if((0,Q.Jm)())return[e];let i=null;return[new Z.V(this.input.registerMeshHandler(q.z,t,((t,e)=>{const s=this.labelRenderer.getLabelMesh(e.getId());s&&(s.hoverState.active=!0,s.hoverState.on(),i=s,this.issueCommand(new X.u(K.C.FINGER)))})),this.input.registerMeshHandler(q.A,z.s.isType(y),((t,e)=>{i&&this.issueCommand(new X.u(K.C.DEFAULT));const s=this.labelRenderer.getLabelMesh(e.getId());s&&(s.hoverState.active=!1,s.hoverState.off()),i=null})),(0,Y.k1)((()=>{}),(()=>{i&&(i.hoverState.active=!1,i.hoverState.off(),this.issueCommand(new X.u(K.C.DEFAULT)),i=null)}),!0,"labelHoverClear")),e]}}var et=i(90304),it=i(95882);const st=new E.Z("room-with-a-view");class nt{constructor(t,e,i){this.sweepData=t,this.roomData=e,this.meshData=i}getPoseForRoom(t,e){const i=this.bestViewForRoom(t,e);if(i){return{rotation:i.rotation,sweepID:i.sweepID,viewmode:it.Ey.Panorama}}return null}bestViewForRoom(t,e){const i=this.sweepData.filter((e=>e.roomId===t&&e.enabled));if(0===i.length)return st.debug("no sweeps in selected room",{roomId:t,scansInRoom:i}),null;let s=1;const n=this.roomData.get(t),a=this.meshData.meshGroups.rooms.get(n.meshSubgroup),o=(null==a?void 0:a.boundingBox.getSize(new g.Vector3).length())||1/0;e&&o>10&&(s=.1);const r=i.sort(((i,n)=>{let a=0,o=0;e&&(a=i.position.distanceTo(e),o=n.position.distanceTo(e));const r=i.neighbours.map((t=>this.sweepData.getSweep(t))).filter((e=>e.roomId===t)).length,h=n.neighbours.map((t=>this.sweepData.getSweep(t))).filter((e=>e.roomId===t)).length;return 1*s*(h-r)-3*(o-a)})),h=r[0];if(!h)return st.debug("no start sweep",{roomId:t,scansInRoom:i,connectedness:r}),null;const d=h.neighbours.map((t=>this.sweepData.getSweep(t))).sort(((e,i)=>{let s=h.position.distanceTo(e.position),n=h.position.distanceTo(i.position);return e.roomId===t&&(s*=1e3),i.roomId===t&&(n*=1e3),n-s})).map((t=>({sweep:t,dist:h.position.distanceTo(t.position),rm:t.roomId}))),l=d[0];st.warn({roomId:t,roomSize:o,scoring:r,byDistance:d,scansInRoom:i});const c=new g.Quaternion;if(1===i.length&&void 0!==l){const t=(new g.Matrix4).setPosition(h.position);t.lookAt(l.sweep.position,h.position,et.fU.UP),c.setFromRotationMatrix(t)}else{const t=(new g.Matrix4).setPosition(h.position);t.lookAt(h.position,l.sweep.position,et.fU.UP),c.setFromRotationMatrix(t)}return{sweepID:h.id,rotation:c}}}var at=i(5823),ot=i(38399),rt=i(71166),ht=i(65019),dt=i(56163),lt=i(35221);class ct extends dt.K{constructor(t,e,i,s,n){super(t,e,i),this.editMode=s,this.label=n,this.id=this.label.sid,this.title=this.label.text,this.icon="icon-toolbar-labels",this.typeId=at.SF.LABEL,this.floorId=this.label.floorId,this.roomId=this.label.roomId||"",this.layerId=this.label.layerId,this.dateBucket=(0,lt.f)(this.label.created),this.enabled=this.label.visible,this.onSelect=()=>{super.onSelect(),this.editMode||this.commandBinder.issueCommand(new J.Cs(this.label))}}supportsLayeredCopyMove(){return!0}supportsBatchDelete(){return!0}}const{LABELS:ut}=ot.Z.SHOWCASE;class pt extends s.Y{constructor(){super(...arguments),this.name="user-labels",this.labelMeshIterator=()=>this.spawner.meshesMap.values,this.filterVisibleLabels=async t=>{this.visibilityRules.updateLabelVisibility(t.ids)},this.changeVisibilityFilterEnabled=async t=>{this.visibilityRules.setVisibilityFilterEnabled(t.enabled)}}async init(t,e){const[i,s,p,m,g,v,f,y,b,I,P]=await Promise.all([e.market.waitForData(a.D),e.market.waitForData(o.M),e.market.waitForData(r.c),e.market.waitForData(h.t),e.market.waitForData(l.Z),e.market.waitForData(c.Z),e.market.waitForData(d._),e.getModuleBySymbol(n.Aj),e.getModuleBySymbol(n.PZ),e.getModuleBySymbol(n.e9),e.market.waitForData(u.e)]),T=y.getScene(),M=new w({assetBasePath:P.tryGetProperty("assetBasePath",""),lang:I.languageCode});M.setRenderLayer(e.claimRenderLayer("labels")),this.spawner=new C(i.getCollection(),M),T.add(this.spawner.container),this.visibilityRules=new U(this.spawner.meshesMap),this.visibilityRules.init(e.market),this.bindings.push(e.commandBinder.addBinding(S,this.filterVisibleLabels),e.commandBinder.addBinding(D,this.changeVisibilityFilterEnabled));this.labelFilter=new F(this.labelMeshIterator,s,(()=>this.visibilityRules.featureEnabled()&&this.visibilityRules.visibleByTool())),this.labelRenderer=new A(this.spawner.meshesMap,i.getCollection(),s,this.labelFilter),e.addComponent(this,this.labelRenderer),this.visibilityRules.setDirtyCallback(this.labelRenderer.setDirty);const E=new nt(g,v,f);this.labelNavInput=new tt(this,e.commandBinder.issueCommand,b,p,m,E),this.toggleInput(!0),async function(t,e,i){const[s,n,a]=await Promise.all([t.market.waitForData(N.pu),t.market.waitForData(H.R),t.market.waitForData(h.t)]);let o=s.application===N.Mx.WORKSHOP;const r=(i,s,a,r=[])=>{const h=[];return 0===r.length&&e.iterate((e=>{(o||e.visible&&n.layerToggled(e.layerId))&&i(e.text)&&h.push(new ct(t.commandBinder,n,s,o,e))})),t.commandBinder.issueCommand(new S(h.map((t=>t.id)))),h.sort(((t,e)=>t.title.localeCompare(e.title)))},d=e=>{t.commandBinder.issueCommand(new D(!!e))},l=t=>new Z.V(e.onChanged(t)),c=()=>{t.commandBinder.issueCommandWhenBound(new rt.c6({id:at.SF.LABEL,groupPhraseKey:ut.SEARCH_GROUP_HEADER,getSimpleMatches:r,registerChangeObserver:l,onSearchActivatedChanged:d,groupOrder:30,groupIcon:"toolbar-labels",batchSupported:!0}))},u=()=>{t.commandBinder.issueCommandWhenBound(new rt.Pe(at.SF.LABEL))},p=()=>{o=s.application===N.Mx.WORKSHOP;const t=a.activeToolName===G.w1.LABELS,e=i.tryGetProperty(V.gx.Labels,!1),n=i.tryGetProperty(ht.wY,!1),r=i.tryGetProperty(ht.dF,!1);(e||t)&&(n||r||o)?c():u()},m=[s.onPropertyChanged("application",p),i.onChanged(p),a.onChanged(p)];return p(),new Z.V(...m)}(e,i,P).then((t=>this.bindings.push(t)))}dispose(t){super.dispose(t),this.visibilityRules.dispose(),this.spawner.dispose()}getLabelMesh(t){return t&&this.spawner.get(t)||null}addLabelMesh(t,e){return this.spawner.add(t,e)}freeLabelMesh(t){t&&this.spawner.free(t)}setPendingMeshId(t){this.visibilityRules.setPendingMeshId(t),this.labelRenderer.setPendingMeshId(t)}setSelectedMeshId(t){this.labelFilter.setSelectedMeshId(t),this.visibilityRules.setSelectedMeshId(t)}toggleInput(t){this.labelNavInput.toggleInput(t)}subscribe(t){return this.spawner.subscribe(t)}onUpdate(){this.visibilityRules&&this.visibilityRules.onUpdate()}}const mt=pt},27990:(t,e,i)=>{"use strict";i.d(e,{Ko:()=>l,hJ:()=>u});var s=i(81396),n=i(69505),a=i(59370);const o=new s.Vector2,r=new s.Vector3,h=new s.Vector2,d=new s.Vector2,l=(t,e,i)=>{const s=((t,e,i)=>((0,a.q9)(i,t,h),(0,a.q9)(i,e,d),{pixelDistance:h.distanceTo(d),startScreenPosition:h,endScreenPosition:d}))(t,e,i);return{screenPosition:((t,e,i)=>(r.copy(t).add(e).multiplyScalar(.5),(0,a.q9)(i,r,o),o))(t,e,i),rotation:c(h,d),pixelDistance:s.pixelDistance,startScreenPosition:s.startScreenPosition,endScreenPosition:s.endScreenPosition}},c=(t,e)=>{const i=t.y-e.y,s=t.x-e.x;let a=Math.atan2(i,s)*n.MN;return a=a>=90||a<=-90?a+180:a,a},u=(t,e=10,i=40)=>({width:Math.max(9*Math.max(t,2)+e,i),height:18+e})},7856:(t,e,i)=>{"use strict";i.r(e),i.d(e,{Group:()=>it,Grouper:()=>et,MeasurementLabelBackgroundMesh:()=>wt,MeasurementLabelRenderer:()=>bt,MeasurementLineRenderer:()=>ut,MeasurementModeData:()=>K.X,MeasuringPhase:()=>X.au,default:()=>ci,labelVisible:()=>X.Ph});var s,n=i(97542),a=i(61864),o=i(92810),r=i(67971),h=i(5823),d=i(29537),l=i(34956),c=i(64918),u=i(38987),p=i(9037),m=i(79727),g=i(23254),v=i(95882),f=i(97998),y=i(81396),w=i(89557);!function(t){t[t.FloorplanOnly=0]="FloorplanOnly",t[t.ThreeD=1]="ThreeD"}(s||(s={}));var b=i(27990),S=i(66990),D=i(62944);const I=new f.Z("line-data"),P=()=>({lineVisibleByFeatureType:!0,labelVisibleByFeatureType:!0});class T{constructor(t,e,i,n,a,o,r=P){this.cameraData=t,this.viewmodeData=e,this.floorsViewData=i,this.isCurrentSweepAligned=n,this.getUnits=a,this.isFeatureEnabled=o,this.visibleFilter=r,this.derivedDataCache={},this.dollhouseLineStyle=s.ThreeD,this.setVisibilityFilter=t=>{this.visibleFilter=t},this.resetVisibilityFilter=()=>{this.visibleFilter=P},this.make=(t,e,i)=>{const s=e(),{start_position:n,end_position:a,visible:o,floorId:r,type:h,text:d}=s,l=o&&this.isFeatureEnabled(),c=l&&this.visibleByFloorAndModes(r,h),u=void 0!==i?i.opacity.value:0,p=0===u||u===S.iV.LABEL_HIDDEN_OPACITY&&c;if(i&&p&&(!l||!c))return i;let m=c,g=c;if(c){const e=this.visibleFilter(t);m=m&&e.lineVisibleByFeatureType,g=g&&e.labelVisibleByFeatureType}let v=0;const f=n.distanceTo(a),y=(0,D.up)(f,this.getUnits());if(g||!p){const{width:t,height:e}=(0,b.hJ)(d.length+y.length),i=(0,b.Ko)(n,a,this.cameraData);this.tmpVec.copy(i.startScreenPosition).sub(i.endScreenPosition);let s=Math.abs(this.tmpVec.y)<Math.abs(this.tmpVec.x)?e:t;S.iV.ALIGN_LABELS&&(s=t,v=i.rotation),g=g&&i.pixelDistance>s}let I;const P=m&&g?1:m&&!g?S.iV.LABEL_HIDDEN_OPACITY:0;i?(I=i.opacity,I.endValue===P&&m===i.visible&&g===i.labelVisible||I.modifyAnimation(I.value,P,S.iV.FADE_DURATION)):I=new w.z(0,P,S.iV.FADE_DURATION);const T={sid:t,rotation:v,labelVisible:g,visible:m,length:f,displayLength:y,labelContents:d.length>0?`${y} ${d}`:y,opacity:I};return this.derivedDataCache[t]={getLineData:e,previousDerivedData:T},T},this.update=t=>{if(this.derivedDataCache[t]){const{getLineData:e,previousDerivedData:i}=this.derivedDataCache[t];return this.make(t,e,i)}I.warn(`data not found for ${t}`)},this.get=t=>{if(this.derivedDataCache[t])return this.derivedDataCache[t].previousDerivedData},this.remove=t=>{this.derivedDataCache[t]&&delete this.derivedDataCache[t]},this.clear=()=>{this.derivedDataCache={}},this.visibleByFloorAndModes=(t,e)=>{if(this.floorsViewData.transition.progress.active)return!1;const i=this.viewmodeData.isInside(),n=this.viewmodeData.isFloorplan(),a=this.viewmodeData.isDollhouse(),o=this.cameraData.pose.pitchFactor(),r=this.floorsViewData.currentFloorId,h=!r,d=!(!r||r!==t),l=t===this.floorsViewData.topFloorId,c=e===s.FloorplanOnly&&(n||a&&o<=1e-5)&&(d||l&&h),u=e===this.dollhouseLineStyle&&a&&!(a&&o<=.9)&&(d||h);return e===s.ThreeD&&i&&this.isCurrentSweepAligned()||u||c},this.tmpVec=new y.Vector2}setDollhouseLineStyle(t){this.dollhouseLineStyle=t}}var M=i(29883),E=i(34029),x=i(59625),C=i(46391),R=i(26158),A=i(53954),L=i(54244),O=i(62436),k=i(57989),F=i(62292),N=i(51205),V=i(35870),B=i(19663);class G extends B.m{constructor(t,...e){super(),this.id="MEASUREMENTS_SET_VISIBILITY",this.payload={sids:e,visible:t}}}class _ extends B.m{constructor(t,e=""){super(),this.id="RENAME_MEASUREMENT",this.payload={sid:t,text:e}}}class H extends B.m{constructor(t){super(),this.payload=t,this.id="MEASURE_CONTENTS_REPLACE"}}class U extends B.m{constructor(t){super(),this.id="FILTER_MEASUREMENT_VISIBILITY",this.payload={sids:t}}}class W extends B.m{constructor(t){super(),this.id="MEASUREMENT_VISIBILITY_FILTER_ENABLED",this.payload={enabled:t}}}class j extends B.m{constructor(t){super(),this.id="NAVIGATE_TO_MEASUREMENT",this.payload={groupId:t}}}var z=i(75810),$=i(40964),q=i(89549),Y=i(76631),Z=i(51788),X=i(20341),K=i(92294),Q=i(72119),J=i(37262),tt=i(92558);class et{constructor(t){this.contents=t,this.groupInfo=[],this.groupIndices=[],this.groupInfoMap={},this.groupIndicesMap={}}startGroup(t){const e=0===this.groupCount,i=this.contents.length!==this.groupIndices[this.groupCount-1];if(e||i){let e=t.sid;if(!e)for(e=(0,tt.fV)();this.groupInfoMap.hasOwnProperty(e);)e=(0,tt.fV)();const i=Object.assign(Object.assign({},t),{sid:e});this.groupInfo.push(i),this.groupInfoMap[e]=i,this.groupIndices.push(this.contents.length),this.groupIndicesMap[this.contents.length]=!0}return this.groupIndices.length-1}reset(){this.contents.atomic((()=>{for(let t=this.contents.length-1;t>=0;--t)this.removeFromIdx(t);this.groupIndices=[],this.groupInfo=[],this.groupIndicesMap={}}))}isStartIndex(t){return!!this.groupIndicesMap[t]}*groups(){for(let t=0;t<this.groupCount;++t)yield this.getGroup(t)}*[Symbol.iterator](){for(const t of this.contents)yield t}getGroupStartIndex(t){return this.groupIndices[t]}getGroup(t){const e=this.groupIndices[t],i=this.groupIndices[t+1],s=isNaN(i)?this.contents.length-1:i-1;return new it(t,this.contents,e,s,Object.assign({},this.groupInfo[t]))}getGroupById(t){for(let e=0;e<this.groupInfo.length;e++)if(t===this.groupInfo[e].sid)return this.getGroup(e)}indexOfGroup(t){for(let e=0;e<this.groupInfo.length;e++)if(t(this.groupInfo[e]))return e;return-1}updateGroupInfo(t,e){const i=this.groupInfo[t].sid;delete this.groupInfoMap[i];const s=Object.assign({sid:i},e);this.groupInfo.splice(t,1,s),this.groupInfoMap[i]=s}get groupCount(){return this.groupIndices.length}get length(){return this.contents.length}get(t){return this.contents.get(t)}push(t){if(0===this.groupCount)throw Error("Grouper: Error pushing points when we have no groups!");return this.contents.push(t.clone()),this.contents.length}pop(){return this.removeFromIdx(this.contents.length-1)}removeFromIdx(t){if(t<this.contents.length&&t>=0&&this.contents.length>=0){const e=this.contents.get(t);return this.contents.remove(t),this.removeEmptyGroups(),e}}removeEmptyGroups(){this.contents.length<=this.groupIndices[this.groupCount-1]&&this.removeGroup(this.groupCount-1)}groupFromPointIndex(t){if(0>t||t>=this.length)return-1;let e=this.groupCount-1;for(;t<this.groupIndices[e];)--e;return e}removeGroup(t){if(t>this.groupCount||t<0)return;const e=this.getGroup(t);if(0===e.count)return;const i=this.groupIndices[t];this.groupIndices.splice(t,1),this.groupInfo.splice(t,1),delete this.groupInfoMap[e.info.sid],delete this.groupIndicesMap[i];for(let i=t;i<this.groupCount;++i){const t=this.groupIndices[i],s=t-e.count;this.groupIndices[i]=s,delete this.groupIndicesMap[t],this.groupIndicesMap[s]=!0}this.contents.splice(e.startIndex,e.count)}update(t,e){this.contents.update(t,e.clone())}copy(t,e=!0){return e&&this.reset(),this.contents.atomic((()=>{for(const e of t){this.startGroup(e.info);for(const t of e)this.push(t)}})),this}toString(){return`Grouper: { groupCount: ${this.groupCount}, length: ${this.length}, groups: [${[...this.groups()].map((t=>`${t}`))}]}`}}class it{constructor(t,e,i,s,n){this.index=t,this.grouper=e,this.startIndex=i,this.endIndex=s,this.data=n}*[Symbol.iterator](){for(let t=0;t<this.count;++t)yield this.get(t)}get(t){if(!this.has(t))throw RangeError(`Out of range error ${t} / ${this.count-1}`);return this.grouper.get(this.startIndex+t)}has(t){return t>=0&&this.startIndex+t<=this.endIndex}get count(){return this.endIndex-this.startIndex+1}get info(){return this.data}get isClosed(){const t=this.grouper.get(this.startIndex),e=this.grouper.get(this.endIndex);return this.count>2&&t.distanceTo(e)<=Q.NZ}get length(){let t=0,e=null;for(let i=0;i<this.count;i++)e&&(t+=this.get(i).distanceTo(e)),e=this.get(i);return t}get segmentLengths(){const t=[];let e=null;for(let i=0;i<this.count;i++)e&&t.push(this.get(i).distanceTo(e)),e=this.get(i);return t}hasLength(){const t=this.grouper.get(this.startIndex),e=this.grouper.get(this.endIndex);return t&&e&&(this.isClosed||t.distanceTo(e)>Q.NZ)}clone(){const t=[],e={get:e=>t[e],contents:t};for(let t=0;t<this.count;t++)e.contents.push(this.get(t));return new it(this.index,e,0,e.contents.length-1,Object.assign({},this.data))}describe(t=this.endIndex){return`GroupSegment${this.index}/${this.startIndex}/${t}}`}equals(t){if(this.count!==t.count||this.index!==t.index)return!1;if((0,J.NK)(this.info,t.info))return!1;for(let e=0;e<this.count;++e)if(!this.get(e).equals(t.get(e)))return!1;return!0}}var st,nt,at=i(31546),ot=i(54702),rt=i(35895);!function(t){t[t.ADDED=0]="ADDED",t[t.REMOVED=1]="REMOVED",t[t.UPDATED=2]="UPDATED",t[t.COUNT=3]="COUNT"}(st||(st={})),function(t){t[t.ADDED=1]="ADDED",t[t.REMOVED=2]="REMOVED",t[t.UPDATED=4]="UPDATED",t[t.ALL=7]="ALL"}(nt||(nt={}));const ht=(t,e)=>{const i=e&nt.ADDED,s=e&nt.REMOVED,n=e&nt.UPDATED,a={};let o;return new rt.gm((()=>t),(e=>o=t.onElementChanged((t=>(a.onAdded=i?t:void 0,a.onRemoved=s?t:void 0,a.onUpdated=n?t:void 0,a))(e))),(t=>{o&&o.cancel()}))};var dt;!function(t){t[t.x=16711680]="x",t[t.y=32768]="y",t[t.z=255]="z",t[t.free=16777215]="free",t[t.xz=16711935]="xz",t[t.laser=16724312]="laser",t[t.yellow=16776960]="yellow",t[t.white=16777215]="white"}(dt||(dt={}));var lt=i(73121),ct=i(56512);class ut{constructor(t,e,i,s,n,a,o=(()=>-1),r,h=16777215){this.points=t,this.createPointSubscription=e,this.cameraData=i,this.lineModule=s,this.mainLayer=n,this.lineLayer=a,this.selectedGroup=o,this.getLineDetails=r,this.lineColor=h,this.endpointGeometry=(0,ct.fc)(),this.linePool=[],this.groupToLines={},this.pointToLines={},this.activeLines=[],this.cameraQuaternion=new y.Quaternion,this.cameraPosition=new y.Vector3,this.cameraProjection=new at.M,this.getLinesForPoint=t=>{const e=[];if(this.pointToLines[t])for(const i of this.pointToLines[t]){const t=i.getMesh(ot.B.line),{startIndex:s,endIndex:n,group:a}=t.userData;e.push({endIndex:n,startIndex:s,line:i,group:a})}return e},this.updateMaterialColors(this.lineColor),this.dataSubs=[this.createPointSubscription(nt.REMOVED,((t,e)=>{this.resetLines()}))]}updateMaterialColors(t){const e=S.V9.lineDefault;this.lineMaterial=this.lineModule.makeLineMaterial(t,!0,{linewidth:e}),this.setStencilState(this.lineMaterial),this.endpointMaterial=this.lineModule.makeEndpointMaterial(t);const i={dashed:!0,dashSize:.025,gapSize:.05,linewidth:S.V9.dottedLineDefault};this.dottedLineMaterial=this.lineModule.makeLineMaterial(t,!1,i),this.setStencilState(this.dottedLineMaterial),this.xLineMaterial=this.lineModule.makeLineMaterial(dt.x,!1,i),this.yLineMaterial=this.lineModule.makeLineMaterial(dt.y,!1,i),this.zLineMaterial=this.lineModule.makeLineMaterial(dt.z,!1,i),this.xzLineMaterial=this.lineModule.makeLineMaterial(dt.xz,!1,i)}setStencilState(t){t.stencilRef=1,t.stencilFail=y.KeepStencilOp,t.stencilZFail=y.KeepStencilOp,t.stencilZPass=y.KeepStencilOp,t.stencilFunc=y.GreaterStencilFunc,t.stencilWrite=!0}setLineOpacityByGroup(t,e){const i=this.groupToLines[t];if(i)for(const t of i)t.opacity(e)}setLineOpacityByPoint(t,e){const i=this.getLinesForPoint(t);if(i)for(const t of i)t.line.opacity(e)}resetLines(){for(const t of this.lines)t.opacity(0),t.hide();this.groupToLines={},this.pointToLines={},this.lines.length=0}updateAllLines(){if(!(this.points.length<1)){for(let t=0;t<this.points.length;t++)this.updateLine(t);for(const t in this.groupToLines){const e=Number(t),i=this.groupToLines[e];for(const t of i)t.updateSelected(this.selectedGroup()===e)}}}init(){}dispose(){this.deactivate();for(const t of this.linePool)t.dispose();this.endpointGeometry.dispose(),this.dottedLineMaterial.dispose(),this.lineMaterial.dispose(),this.endpointMaterial.dispose(),this.xLineMaterial.dispose(),this.yLineMaterial.dispose(),this.zLineMaterial.dispose()}activate(){for(const t of this.dataSubs)t.renew()}deactivate(){for(const t of this.dataSubs)t.cancel();this.resetLines()}get lines(){return this.activeLines}get dottedMaterial(){return this.dottedLineMaterial}beforeRender(){this.cameraQuaternion.copy(this.cameraData.pose.rotation),this.cameraPosition.copy(this.cameraData.pose.position),this.cameraProjection.copy(this.cameraData.pose.projection)}get xMaterial(){return this.xLineMaterial}get yMaterial(){return this.yLineMaterial}get zMaterial(){return this.zLineMaterial}get xzMaterial(){return this.xzLineMaterial}render(){this.updateAllLines()}updateLine(t){const e=this.points.get(t),i=this.points.get(t+1),s=this.points.groupFromPointIndex(t)===this.points.groupFromPointIndex(t+1);e&&i&&s&&this.setLinePosition(t,e,i)}setLinePosition(t,e,i){let s=this.linePool[t];if(!s){const n=this.points.groupFromPointIndex(t);if(!this.getLineDetails(n,t,t+1).visible)return;const a=S.V9.endpointDefault>.01?this.endpointMaterial.clone():void 0;s=this.lineModule.makeLine(e,i,this.lineMaterial.clone(),a,(()=>!(0,lt.Pp)(this.cameraProjection))),this.setupLine(s,t),s.setRenderLayer(this.mainLayer)}s.visible||this.setupLine(s,t),this.linePool[t]=s,s.updateResolution(this.cameraData.width,this.cameraData.height),s.updatePositions(e,i),s.updateBillboard({rotation:this.cameraQuaternion,position:this.cameraPosition,projection:this.cameraProjection})}setupLine(t,e){const i=this.points.groupFromPointIndex(e);t.children.forEach((t=>{t.userData.startIndex=e,t.userData.endIndex=e+1,t.userData.group=i,t.layers.mask=this.mainLayer.mask})),t.getMesh(ot.B.line).layers.mask=this.lineLayer.mask,this.activeLines.push(t),this.addLineToGroup(i,t),this.addLineToPoint(e,t),this.addLineToPoint(e+1,t),t.show(),t.opacity(0)}addLineToGroup(t,e){this.groupToLines[t]||(this.groupToLines[t]=[]),this.groupToLines[t].push(e)}addLineToPoint(t,e){this.pointToLines[t]||(this.pointToLines[t]=[]),this.pointToLines[t].push(e)}}var pt=i(49827),mt=i(59370),gt=i(90304),vt=i(86819),ft=i(23612),yt=i(72996);class wt extends y.Mesh{}class bt{constructor(t,e,i,s,n,a,o,r,h,d,l){this.points=t,this.input=e,this.mobile=i,this.cameraData=s,this.renderLayer=n,this.renderOrder=a,this.textRenderer=o,this.getLineDetails=r,this.changeCursor=h,this.getPhase=d,this.setSelectedLine=l,this.meshPool=[],this.textContainer=new y.Object3D,this.textGeometry=new y.PlaneGeometry(1,1),this.tmpMidpoint=new y.Vector3,this.tmpCamPos=new y.Vector3,this.cameraRotation=new y.Quaternion,this.cameraProjection=new at.M,this.cameraPosition=new y.Vector3;this.inputSubs=[this.input.registerMeshHandler(vt.Rd,yt.s.isType(wt),((t,e)=>{var i,s;if(this.getPhase()===X.au.IDLE)return this.setSelectedLine(null===(s=null===(i=null==e?void 0:e.parent)||void 0===i?void 0:i.userData)||void 0===s?void 0:s.groupIndex),!0}))],this.mobile||this.inputSubs.push(...this.registerHoverHandlers()),this.deactivate(),this.deactivateInteraction()}registerHoverHandlers(){return[this.input.registerMeshHandler(ft.z,yt.s.isType(wt),(()=>{this.getPhase()===X.au.IDLE&&this.changeCursor(l.C.FINGER)})),this.input.registerMeshHandler(ft.A,yt.s.isType(wt),(()=>{this.changeCursor(l.C.DEFAULT)}))]}reset(){for(const t of this.meshPool)t&&(this.input.unregisterMesh(t.collider),this.textContainer.remove(t));this.meshPool=[]}init(){}dispose(){this.textGeometry.dispose()}activate(){}deactivate(){this.reset()}activateInteraction(){for(const t of this.inputSubs)t.renew()}deactivateInteraction(){for(const t of this.inputSubs)t.cancel()}get container(){return this.textContainer}beforeRender(){const t=this.cameraData.pose;this.cameraRotation.copy(t.rotation),this.cameraPosition.copy(t.position),this.cameraProjection.copy(t.projection);const e=(0,lt.s1)(this.cameraProjection),i=(0,lt.Pp)(this.cameraProjection),s=this.cameraData.height,n=this.cameraData.zoom(),a=this.cameraData.aspect();for(let t=1;t<this.points.length;++t){const o=t-1,r=this.points.groupFromPointIndex(t),h=this.getLineDetails(r,o,t);if(!h||r!==this.points.groupFromPointIndex(o)){this.removeLabelMesh(t);continue}const d=this.points.get(o),l=this.points.get(t),c=this.setMeshVisible(t,h.labelVisible);if(!c)continue;c.text!==h.labelContents&&(c.text=h.labelContents);const u=this.cameraPosition.distanceTo(c.position);if(this.updateMeshPose(c,d,l,this.cameraRotation,this.cameraPosition,u,h.rotation,i),i||e)c.scaleFactor=Q.Hn.SCALE_DISTANCE*n;else{const t=(0,mt.D_)(c.position,this.cameraPosition,this.cameraRotation,this.cameraProjection.asThreeMatrix4()),e=Math.abs(t.x);if(e<1){const t=(0,lt.mY)(this.cameraProjection,this.cameraPosition,c.position,s,Q.Hn.SCALE),i=((0,pt.uZ)(a,1,2.5)+n)*Q.Hn.SCALE_ASPECT,o=1+Q.Hn.SCALE_NDC-e*Q.Hn.SCALE_NDC-i;c.scaleFactor=Math.max(Math.min(1/t*o,3),.001)}else c.scaleFactor=.001}}for(let t=this.points.length;t<this.meshPool.length;t++)this.removeLabelMesh(t);this.meshPool=this.meshPool.slice(0,this.points.length)}setTextOpacityByPoint(t,e){const i=this.meshPool[t];i&&(i.opacity=e)}updateMeshPose(t,e,i,s,n,a,o,r){this.tmpMidpoint.copy(e).add(i).multiplyScalar(.5);const h=r?t=>this.tmpCamPos.copy(t).addScaledVector(gt.fU.UP,.15):t=>this.tmpCamPos.copy(n).sub(t).setLength(.15*a).add(t);t.setPosition(this.tmpMidpoint,h),t.setOrientation(s,o)}render(){}setMeshVisible(t,e){let i=this.meshPool[t];if(!i&&e){i=this.textRenderer.createLabel(),i.setRenderLayer(this.renderLayer),i.renderOrder=this.renderOrder,i.opacity=1,i.visible=!1;const e=this.points.groupFromPointIndex(t);i.userData.groupIndex=e,this.textContainer.add(i),this.input.registerMesh(i.collider,!1),this.meshPool[t]=i}if(i){const t=e?.001:S.iV.LABEL_HIDDEN_OPACITY;if(i.visible=i.opacity>t,!i.visible)return null}return i}removeLabelMesh(t){const e=this.meshPool[t];e&&(this.textContainer.remove(e),e.dispose(),this.input.unregisterMesh(e.collider),this.meshPool[t]=null)}}var St=i(17878);const Dt=i.p+"images/scope.svg";var It=i(87926),Pt=i(13310),Tt=i(85992),Mt=i(85042);const Et=i.p+"images/vert_arrows.png",xt=i.p+"images/surface_grid_planar_256.png";var Ct=i(12529);class Rt{constructor(t,e=St.o.ALL){this.scene=t,this.layer=e,this.supportsMobile=!0,this.style=Mt.L.GridPlane,this.alignToNormal=!0,this.xzTex=(0,It.p)(Et),this.zyTex=(0,It.p)(xt),this.bindings=[],this.onOpacityUpdate=t=>{this.container.children.forEach((e=>{e.isMesh&&(e.material.opacity=Math.max(0,t.opacity.value))}))},this.onPositionUpdate=(t,e)=>{this.container.position.copy(t).addScaledVector(e,.005),this.alignToNormal&&(0,lt.J2)(this.container,t,e)},this.scale=t=>{this.container.scale.set(t,t,t)},this.onRaycasterUpdate=t=>{t.hit&&t.hit.face&&this.onPositionUpdate(t.hit.point.clone(),t.hit.face.normal)},this.container=new y.Group;const i=new y.PlaneGeometry(.4,.4),s={color:16777215,side:y.DoubleSide,transparent:!0,depthTest:!0,depthWrite:!1};this.xzTex.generateMipmaps=!1,this.xzTex.minFilter=y.LinearFilter,this.xzMaterial=new y.MeshBasicMaterial(Object.assign(Object.assign({},s),{color:65280,map:this.xzTex})),this.xzMaterial.premultipliedAlpha=!1;const n=new y.Mesh(i,this.xzMaterial);n.rotateOnAxis(gt.fU.LEFT,Math.PI/2),this.zyTex.generateMipmaps=!1,this.zyTex.minFilter=y.NearestFilter,this.zyMaterial=new y.MeshBasicMaterial(Object.assign(Object.assign({},s),{map:this.zyTex})),this.zyMaterial.premultipliedAlpha=!1;const a=new y.Mesh(i,this.zyMaterial);this.container.add(n,a),this.container.children.forEach((t=>{t.renderOrder=Ct.z.reticule,t.layers.mask=this.layer.mask}))}init(){}render(){}dispose(){this.container.children.forEach((t=>{if(t.isMesh){t.geometry.dispose();const e=t.material;e.dispose(),e.map&&e.map.dispose()}}))}async activate(t){const e=await t.market.waitForData(Pt.Y),i=await t.market.waitForData(Tt.P);this.bindings.push(e.onChanged(this.onOpacityUpdate),i.onChanged(this.onRaycasterUpdate)),this.scene.add(this.container)}deactivate(t){for(const t of this.bindings)t.cancel();this.bindings.length=0,this.scene.remove(this.container)}setVisible(t){this.container.visible=t}}var At=i(2569),Lt=i(14564);class Ot{constructor(t,e,i,s,n,a,o){this.mobile=t,this.pointer=e,this.sceneInfo=i,this.renderToTexture=s,this.getLayer=n,this.issueCommand=a,this.editing=!1,this.editingStateChange=!1,this.setOverlay=t=>{this.issueCommand(new Lt.u({color:t?Lt.u.COLOR_DIM:null}))},this.setupCursorRenderCamera=()=>{const t=new y.PerspectiveCamera(Q.X7.perspective.fov,1,.1,100);t.name="Cursor Peek Camera";const e=St.o.ALL,i=["measurement-mode","measurement3d"];for(const t of i)e.removeLayers(this.getLayer(t));t.layers.mask=e.mask,t.updateProjectionMatrix();return{camera:t,update:(e,i,s)=>{t.fov=e,t.near=i,t.far=s,t.updateProjectionMatrix()}}};const r=(0,It.p)(Dt),h=new y.Vector2,d=t=>{const e=this.rttView.height,i=this.rttView.width,s=this.sceneInfo.cameraData;let n=e/2;const a=-i/2;n=t.y<e/2+n?e+n:-n;const o=t.x+a,r=s.height-t.y-n;return h.set(o,r)};this.cursorMesh=new Rt(this.sceneInfo.scene,this.getLayer("cursor-mesh")),this.cursorMesh.setVisible(!1);const l=new y.Vector3,c=new y.Quaternion,u=new y.Vector2;let p,m,g=o(),v=Q.X7.perspective;this.renderIntersection=(t,e,n)=>{p||(m=this.setupCursorRenderCamera(),p=m.camera,m.update(Q.X7.perspective.fov,.1,100)),g!==o()&&(v=o()?Q.X7.ortho:Q.X7.perspective,this.cursorMesh.scale(v.scale),m.update(v.fov,.1,100),g=o());const a=this.editingStateChange?1:Q.X7.smoothness;if(e){if(i.playerCamera.getWorldPosition(l),i.playerCamera.getWorldQuaternion(c),o()){const t=i.cameraData.zoom(),s=(0,At.dS)(t,v.thresholdClose,v.thresholdFar,v.offsetClose,v.offsetFar);p.position.copy(e.point).addScaledVector(gt.fU.UP,s),p.quaternion.copy(c)}else{const t=(0,At.dS)(e.distance,v.thresholdClose,v.thresholdFar,v.offsetClose,v.offsetFar),i=l.sub(e.point).setLength(t).add(e.point);p.position.lerp(i,a),p.lookAt(e.point)}p.updateMatrixWorld(),Q.E0&&this.setOverlay(!1),s.render(t,i.scene.scene,p),Q.E0&&this.setOverlay(!0),u.lerp(d(n),a),s.renderToScreen(t,!1,u,r)}this.editingStateChange=!1}}init(){}dispose(){}activate(){this.sceneInfo.scene.addChild(d.a.Root,this.cursorMesh.container);const t=this.mobile?Q.ox.mobileSize:this.sceneInfo.cameraData.height>Q.ox.highResThreshold?Q.ox.desktopSizeHighRes:Q.ox.desktopSize;this.rttView=this.renderToTexture.createRenderTarget2D(t,t,{format:y.RGBAFormat},!1)}setMeasuringPhase(t){const e=this.mobile?!!X.Pj.mobile[t]:!!X.Pj.desktop[t];e!==this.editing&&(this.editingStateChange=!0,this.editing=e,this.cursorMesh.setVisible(this.editing))}deactivate(){this.sceneInfo.scene.removeChild(d.a.Root,this.cursorMesh.container),this.renderToTexture.disposeRenderTarget2D(this.rttView),this.editing=!1}beforeRender(){this.editing&&(this.hit=this.mobile?this.pointer.lastIntersection:this.pointer.getIntersection(),this.hit&&this.cursorMesh.onPositionUpdate(this.hit.point,this.hit.normal))}render(){if(this.editing&&this.hit){const{screenPosition:t}=(0,mt.q9)(this.sceneInfo.cameraData,this.hit.point);this.renderIntersection(this.rttView,this.hit,t)}}}var kt,Ft=i(26269),Nt=i(82814),Vt=i(3968);!function(t){t[t.SnapPoint=1]="SnapPoint",t[t.SnapLine=2]="SnapLine",t[t.AxisAny=3]="AxisAny",t[t.AxisX=4]="AxisX",t[t.AxisY=5]="AxisY",t[t.AxisZ=6]="AxisZ",t[t.Mesh=7]="Mesh",t[t.RoomMesh=8]="RoomMesh",t[t.LinePoint=9]="LinePoint",t[t.LineSegment=10]="LineSegment"}(kt||(kt={}));class Bt extends Vt.FM{constructor(t,e,i){super(t,e),this.featureType=i}}class Gt extends Vt.FM{constructor(t,e,i){super(t,e),this.featureType=i}}const _t={[gt.eD.UP]:kt.AxisY,[gt.eD.DOWN]:kt.AxisY,[gt.eD.FORWARD]:kt.AxisZ,[gt.eD.BACK]:kt.AxisZ,[gt.eD.LEFT]:kt.AxisX,[gt.eD.RIGHT]:kt.AxisX,[gt.eD.HORIZONTAL_PLANE]:kt.AxisY,NONE:void 0};var Ht;!function(t){t[t.UserAxis=1]="UserAxis",t[t.UserPoint=2]="UserPoint",t[t.UserLine=3]="UserLine",t[t.ModelFeature=4]="ModelFeature"}(Ht||(Ht={}));const Ut={[kt.LinePoint]:Ht.UserPoint,[kt.LineSegment]:Ht.UserLine,[kt.AxisAny]:Ht.UserAxis,[kt.AxisX]:Ht.UserAxis,[kt.AxisY]:Ht.UserAxis,[kt.AxisZ]:Ht.UserAxis,[kt.Mesh]:Ht.ModelFeature,[kt.RoomMesh]:Ht.ModelFeature,[kt.SnapLine]:Ht.ModelFeature,[kt.SnapPoint]:Ht.ModelFeature};class Wt{constructor(t,e,i,s){this.constraint=e,this.meshQuery=i,this.point=new y.Vector3,this.normal=new y.Vector3,jt(t,i)?this.updateFromIntersection(t,e):zt(t,i)&&s&&this.updateFromSnapIntersection(t,e,s),this.source=t}updatePoint(t){return this.point.copy(t),this.source.point.copy(t),this}updateFromIntersection(t,e){return this.source=t,this.updateContents(e,t.point,t.face.normal,t.distance,t.object),this}updateFromSnapIntersection(t,e,i){return this.source=t,this.updateContents(e,t.point,i,t.distance,t.object),this}copy({constraint:t,point:e,normal:i,distance:s,object:n}){this.updateContents(t,e,i,s,n)}clone(){return new Wt(this.source,this.constraint,this.meshQuery)}updateContents(t,e,i,s,n){this.constraint=t,this.point.copy(e),this.normal.copy(i),this.distance=s,this.object=n;const a=this.meshQuery.roomIdFloorIdFromObject(n);a&&(this.roomId=a.roomId||null,this.floorId=a.floorId),this.featureType=(t=>{if(t)return"featureType"in t&&void 0!==t.featureType?t.featureType:t instanceof y.Vector3||t instanceof Vt.UQ?kt.SnapPoint:Ft.$4.isRoomMesh(t)?kt.RoomMesh:t instanceof Nt.S?kt.Mesh:t instanceof y.Line3||t instanceof Vt.FM?kt.SnapLine:void 0})(n)}}const jt=(t,e)=>t&&"face"in t&&void 0!==t.face&&(e.floorIdFromObject(t.object)||t.object.floorId),zt=(t,e)=>t&&"isLineOctreeIntersection"in t&&e.floorIdFromObject(t.object),$t=(()=>{class t extends y.Object3D{constructor(t,e,i,s){super(),this.floorId=t,this.roomId=e,this.meshGroup=i,this.meshSubGroup=s}}return(e,i,s,n,a,o,r)=>({point:e,object:new t(n,a,o,r),face:{a:0,b:1,c:2,normal:i,materialIndex:0},distance:s})})();var qt=i(83402),Yt=i(38042),Zt=i(16769),Xt=i(80592),Kt=i(92937);class Qt extends y.Mesh{constructor(t,e,i,s,n,a,o,r,h,d,l,c){super(),this.pointGroups=t,this.input=e,this.cameraData=i,this.mobile=s,this.changeCursor=n,this.getPhase=a,this.changePhase=o,this.restorePreviousPhase=r,this.setSelectedLine=h,this.getSelected=d,this.onDragStart=l,this.onDragEnd=c,this.inputSubscriptions=[],this.groupVisible=[],this.raycast=(()=>{const t=new y.Vector3,e=new y.Vector3,i=new y.Vector3,s=(t,e,i)=>{if(t.isOrtho())return i.copy(e);{const s=S.iV.OFFSET_TOWARDS_CAMERA;return i.copy(t.position).sub(e).setLength(s).add(e)}};return(n,a)=>{const{pose:o,width:r}=this.cameraData,h=this.mobile?3:-2,d=S.V9.lineDefault+h,l=o.projection.asThreeMatrix4(),c=o.position;let u,p=0,m=0;for(let a=0;a<this.pointGroups.groupCount;++a){const h=this.pointGroups.getGroup(a);if(!(void 0!==this.editingGroup&&this.editingGroup!==a)&&this.groupVisible[a]){for(let g=0;g<h.count-1;++g){const v=s(o,h.get(g),e),f=s(o,h.get(g+1),i);if(v&&f){const e=n.ray.distanceSqToSegment(v,f,void 0,t),i=c.distanceTo(t);let s=(0,lt._U)(i,l,r)*d;if(s*=s,e<s){let i,o=t;this.editingPointIndex?i=this.editingPointIndex:v.distanceToSquared(t)<2*s?(i=m,o=v):f.distanceToSquared(t)<2*s&&(i=m+1,o=f),(!u||e<p)&&(u={distance:n.ray.origin.distanceTo(o),point:o,object:this,instanceId:a,index:i},p=e)}}m++}m++}else m+=h.count}u&&a.push(u)}})(),this.inputSubscriptions.push(...this.registerCommonInput()),s||this.inputSubscriptions.push(...this.registerHoverInput()),this.deactivate()}activate(){this.input.registerMesh(this,!1),this.inputSubscriptions.forEach((t=>t.renew()))}deactivate(){this.input.unregisterMesh(this),this.inputSubscriptions.forEach((t=>t.cancel()))}dispose(){this.deactivate()}setEditingGroup(t){this.editingGroup=t}setGroupVisible(t,e){this.groupVisible[t]=e}validJoint(t){return!!t&&void 0!==t.index}registerHoverInput(){return[this.input.registerMeshHandler(ft.z,yt.s.isType(Qt),((t,e,i)=>{this.getPhase()===X.au.IDLE&&this.changeCursor(i&&void 0!==i.index?l.C.GRAB:l.C.FINGER)})),this.input.registerMeshHandler(ft.A,yt.s.isType(Qt),((t,e,i)=>{this.getPhase()===X.au.IDLE&&this.changeCursor(l.C.DEFAULT)}))]}registerCommonInput(){return[this.input.registerMeshHandler(vt.Rd,yt.s.isType(Qt),((t,e,i)=>{if(!(0,Kt._)(t))return!1;if(this.getPhase()!==X.au.IDLE)return!1;const s=i&&void 0!==i.instanceId?i.instanceId:-1,n=this.getSelected()===s?-1:s;return this.setSelectedLine(n),!0})),this.input.registerMeshHandler(Xt._t,yt.s.isType(Qt),((t,e,i)=>!!(0,Kt._)(t)&&(!!X.WN[this.getPhase()]&&(!this.validJoint(i)||(!i||void 0===i.instanceId||(this.getPhase()===X.au.EDITING||(this.editingPointIndex=i.index,this.onDragStart(i.instanceId),this.setSelectedLine(i.instanceId),this.changePhase(X.au.EDITING),this.mobile||this.changeCursor(l.C.GRABBING)),!0)))))),this.input.registerMeshHandler(Xt._R,yt.s.isType(Qt),((t,e,i)=>this.getPhase()===X.au.EDITING&&(this.editingPointIndex=void 0,this.restorePreviousPhase(),this.onDragEnd(),this.mobile||this.changeCursor(l.C.DEFAULT),!0)))]}}const Jt=new f.Z("snapping");class te{constructor(t,e,i,s,n,a,o){this.raycaster=t,this.getConstraintStyle=e,this.floorsViewData=s,this.viewmodeData=n,this.meshQuery=a,this.cameraPoseData=o,this.origin=null,this.planeNormal=new y.Vector3,this.plane=new y.Plane,this.ray=new y.Ray,this.originChangedListeners=[],this.registeredSnapFeatures={[Ht.UserAxis]:[],[Ht.UserLine]:[],[Ht.UserPoint]:[],[Ht.ModelFeature]:[]},this.clearOrigin=()=>{this.origin=null,this.originChangedListeners.forEach((t=>t(null)))},this.setOrigin=(t,e=!1)=>{this.origin&&!e||(this.origin=t,this.plane.setFromNormalAndCoplanarPoint(this.origin.normal,this.origin.point),this.originChangedListeners.forEach((t=>t(this.origin))),Jt.debug("Updating origin",this.origin,{forceUpdate:e}))},this.setOriginFromPointer=t=>{if(this.origin)return;const e=this.getMeshIntersection();if(!e)return;const i=new Wt(e,this.getConstraintStyle(),this.meshQuery);t&&i.updatePoint(t),this.setOrigin(i,!0)},this.snapFeatures=t=>this.registeredSnapFeatures[t].reduce(((t,e)=>t.concat(e.features)),[]),this.addSnapFeatures=(t,e,i)=>{const s=Ut[e];this.registeredSnapFeatures[s].push({owner:t,features:i}),Jt.debug(`Adding ${i.length} snap feature groups`,kt[e],e)},this.removeSnapFeatures=(t,e)=>{const i=Ut[e],s=this.registeredSnapFeatures[i].findIndex((e=>e.owner===t));if(-1!==s){const t=this.registeredSnapFeatures[i].splice(s,1);Jt.debug(`Removing ${t.length} snap feature groups`,kt[e],e)}else Jt.debug(`removeTemporarySnapFeature: ${e} ${kt[e]} not found from`,t,this.registeredSnapFeatures)},this.filters={nop:t=>!0,isNotMeasurementInput:t=>!(t instanceof Qt),meshVisible:t=>!!(0,Zt.Pv)(t)&&(!this.viewmodeData.isDollhouse()||this.floorsViewData.isCurrentMeshGroupOrAllFloors(t.meshGroup)),visibleFloor:t=>{const e=t.object;return!e||!e.meta||null==e.meta.meshGroup||this.floorsViewData.isCurrentMeshGroupOrAllFloors(e.meta.meshGroup)},userPoints:t=>{if(void 0===t.object)return!1;if((e=t.object)&&e instanceof y.Vector3)for(const e of this.snapFeatures(Ht.UserPoint))if(e.equals(t.object))return!0;var e;return!1},userLines:t=>{if(void 0===t.object)return!1;if((e=t.object)&&"isSnapAxisLine3"in e||"isSnapUserLine3"in e||"isSnapLine3"in e||"start"in e&&"end"in e&&"closestPointToPoint"in e){for(const e of this.snapFeatures(Ht.UserLine))if(e.equals(t.object))return!0;for(const e of this.snapFeatures(Ht.UserAxis))if(e.equals(t.object))return!0}var e;return!1},userFeatures:t=>this.filters.userPoints(t)||this.filters.userLines(t)},this.meshSnapRadius=i?Q.Oq.mobile:Q.Oq.desktop}preload(){this.raycaster.snapping.preloadMeshSnapping()}getMeshIntersection(t){let e=[];const i=this.viewmodeData.isFloorplan()||this.viewmodeData.isDollhouse()&&this.cameraPoseData.pitchFactor()<.01;if(t&&t.origin&&t.normal)e=this.raycaster.picking.cast(t.origin,t.normal,this.filters.meshVisible);else if(i){const t=this.floorsViewData.getHighestVisibleFloor(),i=new y.Vector3(0,1,0),s=(new y.Plane).setFromNormalAndCoplanarPoint(i,new y.Vector3(0,t.boundingBox.max.y,0)),n=new y.Vector3;if(this.raycaster.pointer.pointerRay.intersectPlane(s,n)){const s=this.raycaster.pointer.pointerRay.origin.distanceTo(n);e=[$t(n,i,s,t.id,null,t.meshGroup,null)]}}else e=this.raycaster.pointer.cast(this.filters.meshVisible);const s=e[0];return jt(s,this.meshQuery)?s:null}getIntersection(t){const e=this.getMeshIntersection(t);if(!e)return null;this.cachedHit||(this.cachedHit=new Wt(e,this.getConstraintStyle(),this.meshQuery));const i=this.cachedHit.updateFromIntersection(e,this.getConstraintStyle()),s=this.raycaster.pointer.pointerRay;this.ray.set(s.origin,s.direction),this.planeNormal.copy(i.normal);switch(this.getConstraintStyle()){case qt.l1.Free:return i;case qt.l1.Axes:return this.lockToWorldAxes(i);case qt.l1.PlanarAxes:const t=[...this.snapFeatures(Ht.UserAxis),...this.snapFeatures(Ht.UserLine),...this.snapFeatures(Ht.UserPoint)];return this.softSnapToEdges(i,t,this.filters.userFeatures);case qt.l1.Edges:const e=[...this.snapFeatures(Ht.UserLine),...this.snapFeatures(Ht.UserPoint)];return this.softSnapToEdges(i,e,this.filters.visibleFloor);case qt.l1.EdgesAndPlanarAxes:const s=[...this.snapFeatures(Ht.UserAxis),...this.snapFeatures(Ht.UserLine),...this.snapFeatures(Ht.UserPoint)];return this.softSnapToEdges(i,s,this.filters.visibleFloor)}return i}get lastIntersection(){return this.cachedHit}onOriginChanged(t,e=!1){const i={renew:()=>{this.originChangedListeners.push(t)},cancel:()=>{(0,Yt.bX)(this.originChangedListeners,t)}};return e&&i.renew(),i}lockToWorldAxes(t){if(this.origin){const e=(0,qt.r2)(this.origin.point,t.point,qt.l1.Axes);return this.cachedHit.copy(t),this.cachedHit.point.copy(e.position),this.cachedHit.constraint="NONE"!==e.axisName?qt.l1.Axes:qt.l1.Free,this.cachedHit.featureType=_t[e.axisName],this.cachedHit}return t}softSnapToEdges(t,e,i){const s=this.origin?this.origin.point:t.point,n=this.origin?this.origin.normal:t.normal;this.raycaster.snapping.add(...e);const a=this.raycaster.snapping.cast(this.ray,this.meshSnapRadius,s,n).filter(i),o=this.findClosestVisible(a);return this.raycaster.snapping.remove(...e),o?(this.cachedHit.updateFromSnapIntersection(o,this.getConstraintStyle(),n),this.cachedHit):t}findClosestVisible(t){let e=null;const i=new y.Vector3;for(const s of t){i.copy(s.point).sub(this.ray.origin).normalize();const t=this.raycaster.picking.pick(this.ray.origin,i,this.filters.meshVisible);if(!t||t.distance>s.distance-.05){e=s;break}}return e?{isLineOctreeIntersection:!0,distance:e.distance,distanceToRay:e.distanceToRay,point:e.point,object:e.object}:null}}var ee=i(80361);class ie{constructor(t,e,i,s){this.points=t,this.createPointSubscription=e,this.pointer=i,this.mobileCreateHackJob=s,this.subscriptions=[],this.lineSegments=[],this.updateSnapping=t=>{this.lineSegments.length>0&&(this.pointer.removeSnapFeatures(this,kt.LineSegment),this.lineSegments.length=0);for(let e=0;e<this.points.length;e++)if(e!==t&&e!==t-1&&e!==t+1&&!this.points.isStartIndex(e)){const t=this.points.get(e),i=this.points.get(e-1);this.lineSegments.push(new Gt(i,t,kt.LineSegment))}this.lineSegments.length>0&&this.pointer.addSnapFeatures(this,kt.LineSegment,this.lineSegments)}}init(){}dispose(){}activate(t){let e=-1,i=-1;const s=(0,ee.D)((()=>this.updateSnapping(this.points.length)),16);this.subscriptions.push(this.createPointSubscription(nt.ADDED,s,!0),this.createPointSubscription(nt.REMOVED,s,!0),this.createPointSubscription(nt.UPDATED,((t,s)=>{s===e||this.mobileCreateHackJob()&&s===i||(this.updateSnapping(s),i=e,e=s)}),!0))}deactivate(){this.subscriptions.forEach((t=>t.cancel())),this.subscriptions.length=0,this.pointer.removeSnapFeatures(this,kt.LinePoint),this.pointer.removeSnapFeatures(this,kt.LineSegment)}beforeRender(){}render(){}}var se=i(16782);class ne{constructor(t,e,i,s,n,a){this.points=t,this.pointer=i,this.getPhase=s,this.onDrag=n,this.onDragEnd=a,this.inputSubscriptions=[],this.dragging=!1,this.onDragBegin=(t,e,i)=>{if(!X.q8[this.getPhase()])return!1;if(!i||void 0===i.index)return!1;this.dragging=!0;const s=this.points.isStartIndex(i.index)?i.index+1:i.index-1,n=this.points.get(s);return this.pointer.clearOrigin(),this.pointer.setOriginFromPointer(n),!0},this.onDragEndEvent=()=>!!X.q8[this.getPhase()]&&(!!this.dragging&&(this.onDragEnd(),this.pointer.clearOrigin(),this.dragging=!1,!0)),this.onDragEvent=(t,e,i)=>{if(t.buttons!==se.r.PRIMARY)return!1;if(!X.q8[this.getPhase()])return!1;if(!i||void 0===i.index||void 0===i.instanceId)return!1;const s=this.pointer.getIntersection();return s&&this.points.update(i.index,s.point),this.onDrag(i.instanceId),!0},this.inputSubscriptions.push(e.registerMeshHandler(Xt.E0,yt.s.isType(Qt),this.onDragBegin),e.registerMeshHandler(Xt._t,yt.s.isType(Qt),this.onDragEvent),e.registerMeshHandler(Xt._R,yt.s.isType(Qt),this.onDragEndEvent)),this.deactivate()}activate(){for(const t of this.inputSubscriptions)t.renew()}deactivate(){for(const t of this.inputSubscriptions)t.cancel()}}var ae=i(67678);class oe{constructor(t,e,i,s,n,a,o){this.lines=t,this.getLinesForPoint=e,this.editingMaterial=i,this.createPointSubscription=s,this.getPhase=a,this.getSelected=o,this.bindings=[],this.onDragEnd=t=>{for(const t of this.lines)t.restoreLineMaterial()},this.onClick=(t,e,i)=>{if(this.getPhase()!==X.au.IDLE)return!1;for(const t of this.lines)t.restoreLineMaterial();if(!i||void 0===i.index)return!1;if(t.down){const t=i.index;void 0!==t&&this.styleLines(t)}return!1},this.styleLines=t=>{const e=this.getLinesForPoint(t);for(const t of e)t.group===this.getSelected()&&t.line.overrideLineMaterial(this.editingMaterial)},this.bindings.push(n.registerMeshHandler(ae.er,yt.s.isType(Qt),this.onClick),n.registerUnfilteredHandler(Xt._R,this.onDragEnd),...this.setupLineStyler(this.createPointSubscription)),this.deactivate()}activate(){for(const t of this.bindings)t.renew()}deactivate(){for(const t of this.bindings)t.cancel()}setupLineStyler(t){return[t(nt.ADDED,(()=>{const t=this.lines[this.lines.length-1];t&&t.restoreLineMaterial()}),!0),t(nt.UPDATED,((t,e)=>{this.styleLines(e)}),!0)]}}var re=i(62017),he=i(67238);class de{constructor(t,e,i,n,a,o,r,h,d,l){this.points=t,this.setSelected=e,this.pointer=i,this.changePhase=n,this.getPhase=a,this.isFloorplan=o,this.currentFloorId=r,this.currentRoomId=h,this.currentLayerId=d,this.inferRoomAssociation=l,this.id=de,this.onGroupCreated=t=>null,this.onGroupAddPoint=()=>null,this.onDone=()=>null,this.onEdit=t=>null,this.previousPhase=X.au.IDLE,this.currentLinePoints=0,this.previousPoint=new y.Vector3,this.inputSubscriptions=[],this.log=new f.Z("measurement-creator"),this.isReadonly=!1,this.createNewLine=t=>{const e=this.inferRoomAssociation(t),i=t.roomId||e.roomId||this.currentRoomId()||void 0,n=t.floorId||e.floorId||this.currentFloorId();if(this.log.debug(`Starting measurement: floorId="${n}" roomId="${i}"`),!n)throw new he.H(`Cannot create new line on invalid floor '${n}'`);const a={visible:!0,roomId:i,floorId:n,type:this.isFloorplan()?s.FloorplanOnly:s.ThreeD,text:"",created:new Date,modified:new Date,temporary:this.isReadonly,layerId:this.currentLayerId()},o=this.points.startGroup(a);this.currentGroup=o,this.points.push(t.point),this.currentLinePoints=1,this.setSelected(o);const r=this.points.getGroup(o);this.onGroupCreated(r.info.sid),this.pointer.setOrigin(t,!0)},this.addPointToLine=t=>{this.previousPoint.copy(t.point),this.points.push(t.point),++this.currentLinePoints,this.onGroupAddPoint(),this.pointer.setOrigin(t,!0)},this.updateLastPoint=t=>{this.points.update(this.points.length-1,t.point)},this.getIntersection=()=>this.pointer.getIntersection(),this.setPhase=t=>{const e=this.getPhase();t!==e&&(this.previousPhase=e,this.changePhase(t))},this.restorePreviousPhase=()=>{this.setPhase(this.previousPhase)}}start(){if(this.getPhase()===X.au.IDLE){this.previousPoint.set(1e3,1e3,1e3),this.setPhase(X.au.CREATING),this.setSelected(-1);for(const t of this.inputSubscriptions)t.renew()}}cancelSubs(){for(const t of this.inputSubscriptions)t.cancel()}stop(){this.cancelSubs(),this.currentLinePoints=0,this.setSelected(-1),this.setPhase(X.au.IDLE),this.pointer.clearOrigin(),this.onDone()}syncReadonly(t){this.isReadonly=t}}class le extends de{constructor(t,e,i,s,n,a,o,r,h,d,l,c,u,p,m){super(t,e,s,n,h,l,c,u,p,m),this.setCreatePointProgress=a,this.updateCreatePointHit=o,this.toggleCameraMovement=r,this.continuous=d,this.onLongPressSuccess=t=>{this.log.debug("onLongPressSuccess while in phase:",X.au[this.getPhase()]),this.getPhase()===X.au.CREATING?(this.createNewLine(t),this.addPointToLine(t)):this.getPhase()===X.au.CREATING_NEXT_POINT&&(this.continuous()?(this.updateLastPoint(t),this.addPointToLine(t)):this.updateLastPoint(t)),this.setPhase(X.au.POINT_PLACED)},this.onLongPressStart=t=>{if(t.buttons===se.r.PRIMARY&&(this.getPhase()===X.au.CREATING||this.getPhase()===X.au.CREATING_NEXT_POINT)){const t=this.getIntersection();t&&(this.previousPoint.equals(t.point)||(this.setPhase(X.au.CONFIRMING_POINT),this.toggleCameraMovement(!1),this.setCreatePointProgress(Date.now(),le.longPressCreateThreshold),this.updateCreatePointHit(t),this.previousPhase===X.au.CREATING_NEXT_POINT&&(this.updateLastPoint(t),this.toggleLastPointDraggable(!1)),this.creatingPointTimeout=window.setTimeout((()=>{this.restorePreviousPhase(),this.onLongPressSuccess(t),this.toggleLastPointDraggable(!0)}),le.longPressCreateThreshold)))}},this.onLongPressEnd=()=>{this.getPhase()===X.au.CONFIRMING_POINT&&(this.log.debug("onLongPressEnd, cancelling confirmation"),this.setCreatePointProgress(0,le.longPressCreateThreshold),window.clearTimeout(this.creatingPointTimeout),this.toggleCameraMovement(!0),this.restorePreviousPhase()),this.getPhase()===X.au.CREATING_NEXT_POINT&&this.points.update(this.points.length-1,this.points.get(this.points.length-2)),this.setPlacedtoCreatePhase()},this.onDrag=()=>{const t=this.getIntersection();t&&(this.setPlacedtoCreatePhase(),this.setPhase(X.au.EDITING),this.previousPhase===X.au.CREATING_NEXT_POINT?this.updateLastTwoPoints(t):this.previousPhase===X.au.CREATING&&this.updateLastPoint(t))},this.onDragEnd=()=>{this.getPhase()===X.au.EDITING&&this.restorePreviousPhase(),this.getPhase()!==X.au.CREATING&&this.getPhase()!==X.au.CREATING_NEXT_POINT||(this.toggleLastPointDraggable(!1),this.toggleCameraMovement(!0))},this.toggleLastPointDraggable=t=>{t?(this.dragSub.renew(),this.dragEndSub.renew()):(this.dragSub.cancel(),this.dragEndSub.cancel())},this.updateLastTwoPoints=t=>{this.points.update(this.points.length-1,t.point),this.points.update(this.points.length-2,t.point)},this.dragSub=i(Xt._t,this.onDrag),this.dragEndSub=i(Xt._R,this.onDragEnd),this.inputSubscriptions.push(i(re.Vh,this.onLongPressStart),i(re.pt,this.onLongPressEnd),i(vt.bN,(t=>t.preventDefault()))),this.dragSub.cancel(),this.dragEndSub.cancel(),this.cancelSubs()}start(){super.start()}stop(){if(this.getPhase()===X.au.CREATING_NEXT_POINT)if(this.continuous())this.points.pop();else{const t=this.points.getGroup(this.currentGroup);this.points.removeFromIdx(t.startIndex)}super.stop()}setPlacedtoCreatePhase(){this.getPhase()===X.au.POINT_PLACED&&(this.continuous()?(this.previousPhase===X.au.CREATING||this.previousPhase===X.au.CREATING_NEXT_POINT)&&this.setPhase(X.au.CREATING_NEXT_POINT):this.previousPhase===X.au.CREATING?this.setPhase(X.au.CREATING_NEXT_POINT):this.previousPhase===X.au.CREATING_NEXT_POINT&&this.setPhase(X.au.CREATING))}}le.longPressCreateThreshold=500;class ce extends de{constructor(t,e,i,s,n,a,o,r,h,d,l,c,u){super(t,e,s,n,a,o,r,h,d,c),this.continuous=l,this.meshQuery=u,this.onCreate=t=>{const e=this.previousPoint.distanceTo(t.point)>Q.yV;if(this.getPhase()===X.au.CREATING)return this.createNewLine(t),this.addPointToLine(t),void this.setPhase(X.au.CREATING_NEXT_POINT);e&&this.getPhase()===X.au.CREATING_NEXT_POINT&&(this.continuous()?(this.updateLastPoint(t),this.addPointToLine(t)):this.finishLine(t))},this.onMouseMove=()=>{if(this.getPhase()===X.au.CREATING_NEXT_POINT){const t=this.getIntersection();t&&this.updateLastPoint(t)}},this.onMouseClick=t=>{if(t.button!==se.M.PRIMARY||this.getPhase()===X.au.IDLE)return;const e=this.getIntersection();e&&this.onCreate(e)},this.onDoubleClick=t=>{if(t.preventDefault(),t.button!==se.M.PRIMARY||this.getPhase()===X.au.IDLE)return;const e=this.getIntersection();if(e&&2===this.currentLinePoints){const t={origin:e.point.addScaledVector(e.normal,.05),normal:e.normal},i=this.pointer.getMeshIntersection(t);if(i)if(this.continuous()){this.points.pop();const t=new Wt(i,qt.l1.Free,this.meshQuery);this.addPointToLine(t),this.addPointToLine(t),this.setPhase(X.au.CREATING_NEXT_POINT)}else this.finishLine(new Wt(i,qt.l1.Free,this.meshQuery))}},this.inputSubscriptions.push(i(ae.mE,this.onMouseMove),i(vt.Rd,this.onMouseClick),i(vt.bN,this.onDoubleClick)),this.cancelSubs()}start(){super.start()}stop(){this.getPhase()===X.au.CREATING_NEXT_POINT&&(this.points.pop(),this.currentLinePoints<3&&this.points.pop()),super.stop()}finishLine(t){this.points.pop(),this.addPointToLine(t),this.setPhase(X.au.CREATING),this.onDone()}}var ue=i(10385),pe=i(94046),me=i(89553),ge=i(89570),ve=i(6667),fe=i(32597),ye=i(31362),we=i(51180);class be{constructor(t,e,i,s,n){this.pointer=t,this.mobile=e,this.getPhase=i,this.scene=s,this.getLayer=n,this.subscriptions=[],this.editing=!1,this.axisLines=[],this.axisAlignmentHelper=new y.Object3D,this.updateSnapping=t=>{if(this.axisLines.length>0&&(this.pointer.removeSnapFeatures(this,kt.AxisAny),this.axisLines.length=0),t){this.axisAlignmentHelper.position.copy(t.point);const e=(0,lt.J2)(this.axisAlignmentHelper,t.point,t.normal);this.axisAlignmentHelper.updateMatrixWorld(!0);const i=this.axisAlignmentHelper.matrixWorld,s=100,n=(t,e,i,n)=>{const a=new Bt(t.clone().multiplyScalar(s),e.clone().multiplyScalar(s),n);return a.applyMatrix4(i),a},a=(new y.Matrix4).copyPosition(i);this.axisLines.push(n(gt.fU.UP,gt.fU.DOWN,a,kt.AxisY)),e||this.axisLines.push(n(gt.fU.FORWARD,gt.fU.BACK,i,kt.AxisY),n(gt.fU.UP,gt.fU.DOWN,i,kt.AxisX),n(gt.fU.LEFT,gt.fU.RIGHT,i,kt.AxisZ)),this.axisLines.length>0&&this.pointer.addSnapFeatures(this,kt.AxisAny,this.axisLines)}}}init(){}dispose(){}activate(){this.subscriptions.push(this.pointer.onOriginChanged(this.updateSnapping,!0)),this.axisLineRenderer=new Se(this.scene,this.getLayer("cursor-mesh"))}deactivate(){this.subscriptions.forEach((t=>t.cancel())),this.subscriptions.length=0,this.pointer.removeSnapFeatures(this,kt.AxisAny),this.axisLineRenderer.dispose(),this.axisLineRenderer=null}beforeRender(){const t=this.getPhase(),e=this.mobile?!!X.Pj.mobile[t]:!!X.Pj.desktop[t];e!==this.editing&&(this.editing=e,this.axisLineRenderer.clearLines())}render(){this.editing&&this.axisLineRenderer.render(this.pointer.lastIntersection,this.axisLines)}}class Se{constructor(t,e){this.scene=t,this.layer=e,this.offsetFromMesh=.0075,this.featureColors={[kt.AxisX]:dt.x,[kt.AxisZ]:dt.z,[kt.AxisY]:dt.y},this.axesVisibleInConstraints={[qt.l1.EdgesAndPlanarAxes]:!0,[qt.l1.Axes]:!0,[qt.l1.PlanarAxes]:!0},this.linesActive=[],this.linesFree=[],this.axesVisible=[],this.axisMat=new y.LineBasicMaterial({color:4095,linewidth:1,opacity:.75,transparent:!0,depthWrite:!1,depthTest:!0}),this.render=(t,e)=>{if(t&&this.axesVisibleInConstraints[t.constraint]&&e.length>0){if(e.length!==this.axesVisible.length)this.clearLines();else{this.axesVisible.every(((t,i)=>t.equals(e[i])))||this.clearLines()}if(this.container.parent!==this.scene.scene){for(const t of e){const e=this.getMesh(t);e.material.color.setHex(this.featureColors[t.featureType]),this.container.add(e),this.linesActive.push(e)}this.axesVisible=e.map((t=>t)),this.scene.addChild(d.a.Root,this.container),this.container.position.copy(t.normal).multiplyScalar(this.offsetFromMesh),this.container.updateMatrixWorld(!0)}}else this.clearLines()},this.clearLines=()=>{if(0!==this.linesActive.length){for(;this.linesActive.length>0;){const t=this.linesActive.pop();t&&(this.container.remove(t),this.linesFree.push(t))}this.scene.removeChild(d.a.Root,this.container)}},this.container=new y.Object3D}getMesh(t){let e=this.linesFree.pop();return e?e.geometry.setFromPoints([t.start,t.end]):(e=new De(t,this.axisMat.clone()),e.layers.mask=this.layer.mask),e}dispose(){for(this.clearLines();this.linesFree.length>0;){const t=this.linesFree.pop();t&&(this.container.remove(t),t.geometry.dispose(),t.material.dispose())}this.linesActive=[],this.linesFree=[],this.container=null}}class De extends y.Line{constructor(t,e){super(void 0,e),this.material=e,this.geometry=(new y.BufferGeometry).setFromPoints([t.start,t.end])}}var Ie=i(79728),Pe=i(635),Te=i(80742),Me=i(63319),Ee=i(97704),xe=i(17788),Ce=i(27553),Re=i(95548),Ae=i(77285);const Le={[s.FloorplanOnly]:h.eR.LINETYPE_2D,[s.ThreeD]:h.eR.LINETYPE_3D},Oe={[h.eR.LINETYPE_2D]:s.FloorplanOnly,[h.eR.LINETYPE_3D]:s.ThreeD};class ke{serialize(t,e){if(!t)return null;const i=[],{text:s,visible:n,type:a,floorId:o,roomId:r,layerId:d}=t.info;for(const e of t){const t={position:At.ep.toVisionVector(e)};o&&(t.floorId=o),r&&(t.roomId=r),i.push(t)}const l={enabled:n,label:s,version:"3.2",lineType:Le[a]||h.eR.LINETYPE_3D,points:i};return e&&(l.layerId=d),this.validate(l)?l:null}validate(t){return!!t&&!(t.points.length<2)}}var Fe=i(9133);const Ne=new f.Z("mds-measurement-serializer");class Ve{constructor(){this.points=(0,ue.C)([]),this.grouper=new et(this.points)}deserialize(t){var e;if(!t||!Array.isArray(t))return Ne.debug("No contents",t),null;this.grouper.reset();for(const i of t)if(this.validate(i)){const t=i.lineType||h.eR.LINETYPE_3D,s=Oe[t],n=this.getModelContextFromPoint(i.points[0]),a=Object.assign(Object.assign({layerId:(null===(e=i.layer)||void 0===e?void 0:e.id)||"",sid:i.id,text:i.label||"",visible:i.enabled,type:s},n),{created:(0,Fe.p)(i.created),modified:(0,Fe.p)(i.modified),temporary:!1});this.grouper.startGroup(a),i.points.forEach((t=>this.grouper.push(At.ep.fromVisionVector(t.position))))}else Ne.debug("Deserialized invalid Measurement data from MDS",i);return 0===this.grouper.length?null:this.grouper}validate(t){if(!t||"object"!=typeof t)return!1;const e=["id","points"].every((e=>e in t)),i=t.points&&Array.isArray(t.points)&&t.points.length>0,s=e&&i;return s||Ne.debug("Invalid MDS.MeasurementPath:",{hasRequiredFields:e,hasPoints:i,data:t}),s}getModelContextFromPoint(t){return{floorId:t.floor&&t.floor.id?t.floor.id:"",roomId:t.room&&t.room.id?t.room.id:""}}}class Be extends xe.u{constructor(){super(...arguments),this.serializer=new ke,this.deserializer=new Ve,this.prefetchKey="data.model.measurementPaths",this.layeredType=h.SF.MEASUREMENTPATH}async read(t){const{readonly:e}=this.config,i={modelId:this.getViewId(),includeDisabled:!e,includeLayers:this.readLayerId()};return this.query(Ae.GetMeasurements,i,t).then((t=>{var e,i;if(!Ce.w.isOk(t,"model.measurementPaths"))throw new Re.Zb("MdsMeasurementModeStore.read failed");return this.deserializer.deserialize(null===(i=null===(e=null==t?void 0:t.data)||void 0===e?void 0:e.model)||void 0===i?void 0:i.measurementPaths)}))}async create(t){const e=this.getViewId(),i=this.serializer.serialize(t,this.writeLayerId(t.info.layerId));if(!i)throw new Error("Could not create Measurement");return this.mutate(Ae.AddMeasurement,{modelId:e,data:i}).then((t=>{var e,i;const s=null===(i=null===(e=t.data)||void 0===e?void 0:e.addMeasurementPath)||void 0===i?void 0:i.id;if(!s)throw new Error("Unable to add measurement!");return s}))}async update(t){if(!t||0===t.length)return Promise.resolve();const e=this.getViewId();let i="";const s={};s.modelId=e;let n="";for(const e of t){const t=e.info.sid,a=this.serializer.serialize(e,!1);if(!a)throw new Error("Could not update Measurement");s[`data${t}`]=a,i+=`, $data${t}: MeasurementPathPatch!`,n+=`patch${t}: patchMeasurementPath(modelId: $modelId, pathId: "${t}", patch: $data${t}) {\n        id\n      }`}const a=Ee.gql`
      mutation PatchMeasurements($modelId: ID! ${i}) {
        ${n}
      }
    `;return this.mutate(a,s).then((()=>{}))}async delete(...t){if(!t||0===t.length)return;const e=this.getViewId();let i="";for(const e of t){const{pathId:t}=e;i+=`delete${t}: deleteMeasurementPath(modelId: $modelId, pathId: "${t}")`}const s=Ee.gql`
      mutation DeleteMeasurements($modelId: ID!) {
        ${i}
      }
    `;return this.mutate(s,{modelId:e}).then((()=>{}))}}var Ge=i(28269),_e=i(71954),He=i(56163),Ue=i(35221);class We extends He.K{constructor(t,e,i,s,n,a){super(t,e,i),this.group=s,this.units=n,this.id=this.group.info.sid,this.title=We.getTitle(this.group,this.units,this.textParser),this.description=We.getDescription(this.group,this.units),this.label=We.getLabel(this.group,this.textParser),this.icon="icon-tape-measure",this.enabled=this.group.info.visible,this.typeId=h.SF.MEASUREMENTPATH,this.floorId=this.group.info.floorId,this.roomId=this.group.info.roomId||"",this.layerId=this.group.info.layerId,this.dateBucket=(0,Ue.f)(this.group.info.created),this.onSelect=async()=>{super.onSelect(),this.commandBinder.issueCommand(new j(this.id))},this.textParser=a}supportsLayeredCopyMove(){return!0}supportsBatchDelete(){return!0}static getTitle(t,e,i){const s=(0,D.up)(t.length,e),n=We.getLabel(t,i);return n?`${s} ${n}`:s}static getLabel(t,e){return e.getPlainText(t.info.text)}static getDescription(t,e){if(t.count>2){return t.segmentLengths.map((t=>(0,D.up)(t,e))).join(" – ")}return""}}var je=i(52528),ze=i(71166),$e=i(85893),qe=i(67294),Ye=i(33558),Ze=i(36893),Xe=i(96377),Ke=i(38242),Qe=i(77230),Je=i(60543),ti=i(84426),ei=i(6608),ii=i(44472);const si=({item:t})=>{const{analytics:e}=(0,qe.useContext)(Ye.I),i=(0,Ze.l)(t.id),n=(0,Qe.A)(),a=(0,Xe.m)(),o=(0,Ke.e)();if(!i)return null;const{id:r,textParser:h,title:d,description:l,icon:c}=t,u=r===a,p=i.info.type===s.FloorplanOnly?"floorplan":"dollhouse",m=n?(0,Je.vr)(d,n):d,g=n?(0,Je.vr)(l,n):l,v=(0,$e.jsxs)("div",Object.assign({className:"item-details"},{children:[(0,$e.jsxs)("div",Object.assign({className:"item-header"},{children:[(0,$e.jsx)(ii.S,{text:m||"",textParser:h,markers:Je.PP}),(0,$e.jsx)("div",Object.assign({className:"list-item-decal"},{children:(0,$e.jsx)(ti.JO,{name:p})}))]})),l&&(0,$e.jsx)("div",Object.assign({className:"item-description"},{children:(0,$e.jsx)(ii.S,{text:g,textParser:h,markers:Je.PP})}))]}));return(0,$e.jsx)(ti.HC,{id:r,className:"search-result-item",title:v,active:u,disabled:!t.enabled,onClick:async()=>{e.trackGuiEvent("search_item_measurement_click",{tool:o}),t.onSelect()},badge:(0,$e.jsx)(ei.C,{iconClass:c})},r)};var ni=i(38399);const{MEASUREMENTS:ai}=ni.Z.SHOWCASE,oi=new je.v({});var ri=i(83252),hi=i(49416),di=i(77161);class li extends n.Y{constructor(){super(),this.name="measurement-mode",this.mutationRecord={[_e.KI.added]:new Set,[_e.KI.updated]:new Set,[_e.KI.removed]:new Set},this.removedLayerMap=new Map,this.store=null,this.newDataBinding=null,this.roomboundData=null,this.longPressStart=Date.now(),this.threshold=800,this.lineSidToPointMap={},this.mobile=!1,this.cameraAndDragBlocked=!1,this.blockNavigation=()=>!1,this.visibilityFilterEnabled=!1,this.editable=!0,this.changeCursor=t=>{this.engine.commandBinder.issueCommand(new u.u(t))},this.onEdit=t=>{const e=this.data.getGroupInfo(t),i=e&&e.info.sid||null;this.data.editingGroupId!==i&&(this.data.editingGroupId=i,this.colliders.setEditingGroup(t))},this.onEditEnd=()=>{if(this.data.editingGroupId){const t=this.pointGroups.getGroupById(this.data.editingGroupId);t&&(this.mutationRecord.updated.add(t.info.sid),this.save(),this.engine.broadcast(new we.av(this.buildAnalyticsMessageFromGroup(t))))}this.data.editingGroupId=null,this.colliders.setEditingGroup(void 0)},this.onCreatorAddNewLine=t=>{this.log.debug("onCreatorAddNewLine",t),this.data.creatingGroupId=t},this.onCreatorAddPoint=()=>{if(this.data.creatingGroupId){this.log.debug("onCreatorAddNewSegment",this.data.creatingGroupId);const t=this.pointGroups.getGroupById(this.data.creatingGroupId);if(t&&t.count>1&&t.length>0){const e=this.buildAnalyticsMessageFromGroup(t);this.engine.broadcast(new we.rf(Object.assign(Object.assign({},e),{startPosition:this.pointGroups.get(t.startIndex),endPosition:this.pointGroups.get(t.endIndex)})))}}},this.onCreatorStop=()=>{if(this.data.creatingGroupId){const t=this.pointGroups.getGroupById(this.data.creatingGroupId);t&&(t.count>1&&t.hasLength()?(this.engine.broadcast(new we.u6(Object.assign({},this.buildAnalyticsMessageFromGroup(t)))),this.mutationRecord.added.add(t.info.sid),this.save()):this.engine.broadcast(new we.ff))}this.data.creatingGroupId=null},this.onToggleMeasurementMode=async(t,e,i)=>{this.toggleMeasuringMode(t,i),await this.engine.commandBinder.issueCommand(new z.I(t&&e))},this.onViewmodeChange=()=>{this.getPhase()!==X.au.CLOSED&&this.stopMeasuring()},this.onSweepChange=()=>{const t=this.getPhase();t!==X.au.CREATING&&t!==X.au.CREATING_NEXT_POINT&&this.setSelected(-1)},this.initStorageOnApplicationChange=async()=>{var t;const e=this.loadSavedMeasurements();this.data.modeActive()&&this.engine.commandBinder.issueCommand(new O.O(!1)),this.store&&e&&!this.newDataBinding?this.newDataBinding=this.store.onNewData((async t=>{this.getPhase()!==X.au.CLOSED&&this.stopMeasuring(),this.loadSavedMeasurements()?this.replaceContents(null==t?void 0:t.groups()):this.replaceContents(),this.clearMutationRecord()})):this.newDataBinding&&!e&&(this.newDataBinding.cancel(),this.newDataBinding=null),e&&await(null===(t=this.store)||void 0===t?void 0:t.refresh())},this.getPhase=()=>this.data.phase,this.toggleCameraMovement=t=>{t||this.cameraAndDragBlocked?t&&this.cameraAndDragBlocked&&(this.dragInterceptor.cancel(),this.navigation.removeNavigationRule(this.blockNavigation),this.cameraAndDragBlocked=!1):(this.dragInterceptor.renew(),this.navigation.addNavigationRule(this.blockNavigation),this.cameraAndDragBlocked=!0)},this.getConstraint=()=>this.settings.tryGetProperty(R.F.MeasurementSnapping,!1)?this.viewmodeData.isFloorplan()||(0,mt.Eb)(this.cameraData.pose.pitchFactor())?Q.xh.floorplan:this.constraint:Q.xh.disabled,this.selectedItemChanged=()=>{const{activeItemId:t,selectedType:e}=this.searchData;-1===this.getSelected()||t&&e===h.SF.MEASUREMENTPATH||this.setSelectedById(null)},this.getSelected=()=>this.data.selectedGroupIndex,this.setSelected=t=>{this.data.selectedGroupIndex!==t&&this.data.setSelectedGroupIndex(t)},this.setSelectedById=t=>{if(null===t)this.setSelected(-1);else{const e=this.pointGroups.getGroupById(t);e&&this.setSelected(e.index)}},this.deleteSelectedMeasurement=()=>{-1!==this.data.selectedGroupIndex&&(this.deleteMeasurement(this.data.selectedGroupIndex),this.setSelected(-1),this.changePhase(X.au.IDLE),this.mobile||(this.navigation.removeNavigationRule(this.blockNavigation),this.engine.commandBinder.issueCommand(new u.u(l.C.DEFAULT))))},this.deleteMeasurementBySids=async t=>{const e=t.sids;for(const i of e){const e=this.pointGroups.getGroupById(i);if(!e)throw this.log.error("Measurement delete failed",Object.assign({},t)),Error("Measurement delete failed, not found");this.deleteMeasurement(e.index,!0)}this.save()},this.onToggleContinuous=()=>{this.stopMeasuring()},this.changePhase=t=>{this.getPhase()!==t&&(this.log.debug(`Phase Change: ${X.au[this.getPhase()]} -> ${X.au[t]}`),this.previousPhase=this.getPhase(),this.data.setPhase(t))},this.restorePreviousPhase=()=>{this.changePhase(this.previousPhase)},this.onPhaseChange=t=>{if(this.intersectionVisualizer.setMeasuringPhase(t),this.previousPhase!==t)switch(t){case X.au.CLOSED:case X.au.IDLE:this.engine.broadcast(new $.ps(!0)),this.engine.commandBinder.issueCommand(new Z.U);break;case X.au.EDITING:case X.au.CREATING:case X.au.POINT_PLACED:case X.au.CREATING_NEXT_POINT:case X.au.CONFIRMING_POINT:this.engine.broadcast(new $.ps(!1)),this.engine.commandBinder.issueCommand(new Z.t)}},this.buildAnalyticsMessage=t=>{const e=this.pointGroups.getGroup(t);return e&&e.hasLength()?this.buildAnalyticsMessageFromGroup(e):null},this.buildAnalyticsMessageFromGroup=t=>{const e=s[t.info.type];return Object.assign(Object.assign({sid:t.info&&t.info.sid?t.info.sid:t.describe(),totalLength:t?t.length:0,segments:t?t.count:0,temporary:!!t.info.temporary,viewmode:this.viewmodeData.currentMode,floorId:t.info.floorId,continuous:this.settings.tryGetProperty(R.F.MeasurementContinuousLines,!1)},this.getAnalyticsForConstraints()),{type:e})},this.getAnalyticsForConstraints=()=>{const t=this.pointer.lastIntersection;if(t){const e=t.featureType,i=t.constraint;return{featureType:void 0!==e?kt[e]:"None",constraint:qt.l1[i]}}return null},this.toggleMeasuringMode=(t,e)=>{this.log.debug("toggleMeasuringMode",t);t!==(this.getPhase()!==X.au.CLOSED)&&(t?(this.editable=e,this.engine.toggleRendering(this,!0),this.changePhase(X.au.IDLE),this.lineStyler.activate(),this.renderer.activate(),this.textRenderer.activate(),this.editable&&(this.editor.activate(),this.colliders.activate(),this.textRenderer.activateInteraction()),this.scene.addChild(d.a.Root,this.textRenderer.container),this.engine.broadcast(new we.$n(!0,this.viewmodeData.currentMode,this.pointGroups.groupCount))):(this.engine.toggleRendering(this,!1),this.stopMeasuring(),this.changePhase(X.au.CLOSED),this.toggleCameraMovement(!0),this.editable&&(this.editor.deactivate(),this.colliders.deactivate(),this.textRenderer.deactivateInteraction()),this.lineStyler.deactivate(),this.renderer.deactivate(),this.textRenderer.deactivate(),this.scene.removeChild(d.a.Root,this.textRenderer.container),this.engine.broadcast(new we.$n(!1,this.viewmodeData.currentMode,this.pointGroups.groupCount))))},this.startMeasuring=()=>{this.settings.tryGetProperty(R.F.MeasurementSnapping,!1)&&this.pointer.preload(),this.mobile||(this.navigation.addNavigationRule(this.blockNavigation),this.engine.commandBinder.issueCommand(new u.u(l.C.XHAIR))),this.creator.start()},this.stopMeasuring=()=>{if(this.getPhase()===X.au.CLOSED)return;const t=this.isMeasurementComplete(this.data.selectedGroupIndex);this.isCreating()&&!t&&this.deleteSelectedMeasurement(),this.creator.stop(),this.mobile||(this.navigation.removeNavigationRule(this.blockNavigation),this.engine.commandBinder.issueCommand(new u.u(l.C.DEFAULT)))},this.setupIntersectionVisuals=(t,e,i,s,n,a,o,r)=>{e.addVisibilityRule((()=>{const t=this.getPhase();return!(!a&&t===X.au.CREATING||t===X.au.CONFIRMING_POINT||t===X.au.CREATING_NEXT_POINT||t===X.au.EDITING)}));const h={cameraData:s,playerCamera:i.camera,scene:i};return new Ot(a,n,h,t,o,this.engine.commandBinder.issueCommand,r.isFloorplan)},this.renameMeasurement=async t=>{const e=this.pointGroups.getGroupById(t.sid);if(!e)throw this.log.error("Measurement rename failed",Object.assign({},t)),Error("Measurement rename failed, not found");const i=void 0!==t.text&&t.text.length<=24;if(!e||!i)throw this.log.error("Measurement text invalid, text must be between 0 and 24 charachters in length.",Object.assign({},t)),new Error("Measurement text invalid");{const i=""===e.info.text&&t.text.length>0,s=!i&&e.info.text!==t.text;this.pointGroups.updateGroupInfo(e.index,Object.assign(Object.assign({},e.info),{text:t.text})),this.mutationRecord.updated.add(e.info.sid),this.save(),i&&this.engine.broadcast(new we.ty(t.sid,t.text)),s&&this.engine.broadcast(new we.mM(t.sid,e.info.text,t.text))}},this.onChangeVisibility=async t=>{const{sids:e,visible:i}=t;for(const s of e){const e=this.pointGroups.getGroupById(s);if(!e)throw this.log.error("Measurement visibility toggle failed",Object.assign(Object.assign({},t),{group:e})),new Error("Measurement visibility toggle failed, not found");{this.pointGroups.updateGroupInfo(e.index,Object.assign(Object.assign({},e.info),{visible:i})),this.mutationRecord.updated.add(e.info.sid);const t=this.buildAnalyticsMessage(e.index);t&&this.engine.broadcast(new we.av(t)),i||e.index!==this.getSelected()||this.setSelected(-1)}}this.save()},this.filterVisibility=async t=>{this.data.idVisibility=new Set(t.sids)},this.changeVisibilityFilterEnabled=async t=>{this.data.idVisibility=new Set,this.visibilityFilterEnabled=t.enabled},this.currentRoomId=()=>(0,hi.O)(this.roomboundData,this.layersData,this.layersData.activeLayerId)?this.roomData.selected.value:null,this.clearMutationRecord=this.clearMutationRecord.bind(this),this.deleteMeasurement=this.deleteMeasurement.bind(this),this.replaceContents=this.replaceContents.bind(this),this.navigateToMeasurement=this.navigateToMeasurement.bind(this)}async init(t,e){var i;const{readonly:s,baseUrl:n}=t;this.config=t,this.mobile=(0,ye.tq)(),this.engine=e;const[a,d,l,c,u,v,f,y,w,b]=await Promise.all([e.getModuleBySymbol(o.Aj),e.getModuleBySymbol(o.tA),e.getModuleBySymbol(o.PZ),e.getModuleBySymbol(o.hn),e.getModuleBySymbol(o.fQ),e.market.waitForData(m.c),e.market.waitForData(p.M),e.market.waitForData(g.O),e.market.waitForData(A.Z),e.market.waitForData(Ge.Z)]);[this.settings,this.playerOptions,this.layersData,this.searchData]=await Promise.all([e.market.waitForData(E.e),e.market.waitForData(x.af),e.market.waitForData(Te.R),e.market.waitForData(M.T)]),e.market.waitForData(di.Z).then((t=>this.roomboundData=t)),this.playerOptions=await e.market.waitForData(x.af),this.layersData=await e.market.waitForData(Te.R),this.scene=a.getScene(),this.viewmodeData=y,this.cameraData=f,this.floorsViewData=v,this.roomData=b,this.input=l,this.navigation=await e.getModuleBySymbol(o.wR),this.meshQueryModule=await e.getModuleBySymbol(o.hi);const S=await e.getModuleBySymbol(o.Lx);this.applicationData=await e.market.waitForData(L.pu),this.lineDerivedDataFactory=new T(f,y,v,(()=>!!w.currentSweep&&w.isSweepAligned(w.currentSweep)),(()=>this.settings.tryGetProperty(R.F.UnitType,C.M.IMPERIAL)),(()=>!0));const D=e.claimRenderLayer(this.name);this.data=new K.X;const I=(0,ue.C)([]);this.pointGroups=new et(I);const P=(t,e,i=!1)=>ht(I,t).createSubscription(e,i);this.dataSubscription=I.onChanged((()=>{this.data.repopulate(this.pointGroups.groups()),this.data.commit()})),this.store=new Be({context:this.layersData.mdsContext,readonly:s,baseUrl:n}),this.registerRoomAssociationSource(e),async function(t,e,i,s){const n=await t.market.waitForData(L.pu);let a=n.application===L.Mx.WORKSHOP;const o=(n,o,r,h=[])=>{const d=s.tryGetProperty(R.F.UnitType,C.M.METRIC),l=[],c=e.groups();if(0===h.length)for(const e of c)(a||e.info.visible&&i.layerToggled(e.info.layerId))&&n(We.getTitle(e,d,oi),We.getDescription(e,d))&&l.push(new We(t.commandBinder,i,o,e,d,oi));return t.commandBinder.issueCommand(new U(l.map((t=>t.id)))),l},r=e=>{t.commandBinder.issueCommand(new W(!!e))},d=t=>new ge.V(e.onDataChanged(t),s.onPropertyChanged(R.F.UnitType,t)),l={renew:()=>{t.commandBinder.issueCommandWhenBound(new ze.c6({id:h.SF.MEASUREMENTPATH,groupPhraseKey:ai.SEARCH_GROUP_HEADER,getSimpleMatches:o,registerChangeObserver:d,onSearchActivatedChanged:r,groupOrder:40,groupIcon:"tape-measure",batchSupported:!0,itemFC:si}))},cancel:()=>{t.commandBinder.issueCommandWhenBound(new ze.Pe(h.SF.MEASUREMENTPATH))}},c=()=>{a=n.application===L.Mx.WORKSHOP,s.tryGetProperty(x.gx.Measurements,!0)||a?l.renew():l.cancel()},u=n.onPropertyChanged("application",c),p=s.onPropertyChanged(x.gx.Measurements,c);return c(),new ge.V(l,u,p)}(e,this.data,this.layersData,this.settings).then((t=>this.bindings.push(t))),this.constraint=this.mobile?Q.xh.mobile:Q.xh.desktop,this.pointer=new te(u,this.getConstraint,this.mobile,this.floorsViewData,this.viewmodeData,this.meshQueryModule,f.pose);const B=new ie(this.pointGroups,P,this.pointer,(()=>{const t=this.settings.tryGetProperty(R.F.MeasurementContinuousLines,!1),e=this.getPhase()===X.au.CREATING||this.getPhase()===X.au.CREATING_NEXT_POINT||this.previousPhase===X.au.CREATING_NEXT_POINT&&this.getPhase()===X.au.EDITING;return t&&this.mobile&&e}));e.addComponent(this,B);const z=new be(this.pointer,this.mobile,this.getPhase,this.scene,e.getRenderLayer);e.addComponent(this,z),this.creator=this.instantiateCreator(f),this.initStorageOnApplicationChange();const $=(t,e,i)=>{const s=this.pointGroups.getGroup(t),n=s.describe(i);this.lineSidToPointMap[n]=i;const a=this.lineDerivedDataFactory.get(n);return this.lineDerivedDataFactory.make(n,(()=>({start_position:this.pointGroups.get(e),end_position:this.pointGroups.get(i),visible:this.getMeasurementVisibility(s.info.sid,s.info.layerId,s.info.visible),floorId:s.info.floorId,roomId:s.info.roomId,type:s.info.type,text:this.pointGroups.isStartIndex(e)?s.info.text:""})),a)};this.renderer=new ut(this.pointGroups,P,f,c,D,e.claimRenderLayer("measure-lines"),this.getSelected,$),await e.addComponent(this,this.renderer);const q=new r.uc({assetBasePath:null!==(i=this.settings.getProperty("assetBasePath"))&&void 0!==i?i:"",color:"black",background:!0,backgroundColor:"#ffffff",backgroundColliderType:wt});this.textRenderer=new bt(this.pointGroups,l,this.mobile,f,D,Ct.z.labels,q,$,this.changeCursor,this.getPhase,this.setSelected),await e.addComponent(this,this.textRenderer),this.editor=new ne(this.pointGroups,l,this.pointer,this.getPhase,this.onEdit,this.onEditEnd),this.colliders=new Qt(this.pointGroups,this.input,f,this.mobile,this.changeCursor,this.getPhase,this.changePhase,this.restorePreviousPhase,this.setSelected,this.getSelected,this.onEdit,this.onEditEnd),this.lineStyler=new oe(this.renderer.lines,this.renderer.getLinesForPoint,this.renderer.dottedMaterial,P,this.input,this.getPhase,this.getSelected);const[Y]=await Promise.all([e.getModuleBySymbol(o.Lk)]);this.intersectionVisualizer=this.setupIntersectionVisuals(d,Y,this.scene,f,this.pointer,this.mobile,e.getRenderLayer,this.viewmodeData),e.addComponent(this,this.intersectionVisualizer),this.dragInterceptor=new ge.V(this.input.registerPriorityHandler(Xt._t,Nt.S,(()=>!0)),this.input.registerPriorityHandler(Xt._t,pe.i,(()=>!0))),this.dragInterceptor.cancel(),this.mobile||this.bindings.push(...this.hotkeys()),this.bindings.push(this.applicationData.onPropertyChanged("application",this.initStorageOnApplicationChange),y.makeModeChangeSubscription(this.onViewmodeChange),w.makeSweepChangeSubscription(this.onSweepChange),e.commandBinder.addBinding(O.O,(async t=>{this.onToggleMeasurementMode(t.on,t.dimWhileActive,t.editable)})),e.commandBinder.addBinding(k.c,(async()=>this.startMeasuring())),e.commandBinder.addBinding(F.Q,(async t=>this.setSelected(t.index))),e.commandBinder.addBinding(N.B,(async()=>this.stopMeasuring())),e.commandBinder.addBinding(V.Ev,(async()=>this.deleteSelectedMeasurement())),e.commandBinder.addBinding(V.Tn,(async t=>this.deleteMeasurement(t.index))),e.commandBinder.addBinding(G,this.onChangeVisibility),e.commandBinder.addBinding(_,this.renameMeasurement),e.commandBinder.addBinding(V.JM,this.deleteMeasurementBySids),e.commandBinder.addBinding(H,(async t=>{var e;return this.replaceContents(null===(e=t.points)||void 0===e?void 0:e.groups())})),e.commandBinder.addBinding(U,this.filterVisibility),e.commandBinder.addBinding(W,this.changeVisibilityFilterEnabled),e.commandBinder.addBinding(j,this.navigateToMeasurement),this.dataSubscription,this.settings.onPropertyChanged(R.F.MeasurementContinuousLines,(()=>this.onToggleContinuous())),S.onSave((()=>this.saveDiff()),{dataType:Ie.g.MEASUREMENTS}),this.layersData.onPropertyChanged("currentViewId",(()=>this.updateSettings())),this.layersData.onPropertyChanged("activeLayerId",(()=>this.updatePendingMeasurement())),this.data.onPropertyChanged("phase",this.onPhaseChange),this.searchData.onPropertyChanged("activeItemId",this.selectedItemChanged)),e.market.register(this,K.X,this.data),e.toggleRendering(this,!1),this.updateSettings(),this.registerDebugSettings()}replaceContents(t){this.lineDerivedDataFactory.clear();for(let t=this.pointGroups.groupCount;t>=0;t--){const e=this.pointGroups.getGroup(t);this.layersData.isInMemoryLayer(e.info.layerId)||this.pointGroups.removeGroup(t)}t&&this.pointGroups.copy(t,!1)}loadSavedMeasurements(){return this.config.readonly=this.applicationData.application!==L.Mx.WORKSHOP,this.creator.syncReadonly(this.config.readonly),!this.config.readonly||this.playerOptions.options.measurements_saved}isCreating(){const t=this.getPhase();return t===X.au.CREATING||t===X.au.CREATING_NEXT_POINT}setConstraintStyle(t){this.log.debug("setConstraintStyle:",qt.l1[t]),this.constraint=t}deleteMeasurement(t,e=!1){const i=this.buildAnalyticsMessage(t);i&&this.engine.broadcast(new we.$Z(Object.assign(Object.assign({},i),{count:this.pointGroups.groupCount-1})));const s=this.pointGroups.getGroup(t);if(s){const{sid:i,layerId:n}=s.info;this.pointGroups.removeGroup(t),i!==this.data.creatingGroupId&&(this.layersData.isInMemoryLayer(n)||(this.mutationRecord.removed.add(i),this.removedLayerMap.set(i,n),e||this.save()))}}isMeasurementComplete(t){if(-1===t||!this.isCreating())return!0;const e=this.getPhase(),i=this.pointGroups.getGroup(t);return this.settings.tryGetProperty(R.F.MeasurementContinuousLines,!1)?i.count>2&&e===X.au.CREATING_NEXT_POINT:i.count>=2&&e===X.au.CREATING}hotkeys(){return[this.input.registerHandler(me.e,(t=>{if(t.state===ve.M.PRESSED)switch(t.key){case fe.R.ESCAPE:this.getPhase()===X.au.CONFIRMING_POINT?(this.creator.stop(),this.creator.start(),this.changePhase(X.au.CREATING)):this.isCreating()?this.stopMeasuring():this.applicationData.application!==L.Mx.WORKSHOP&&this.getPhase()!==X.au.CLOSED&&this.engine.commandBinder.issueCommand(new q.tT(Y.w1.MEASUREMENTS,!1));break;case fe.R.BACKSPACE:case fe.R.DELETE:this.getPhase()!==X.au.CLOSED&&this.getPhase()!==X.au.EDITING&&this.deleteSelectedMeasurement();break;case fe.R.RETURN:const t=this.pointGroups.groupCount-1;this.isMeasurementComplete(t)&&this.stopMeasuring()}})),this.input.registerHandler(me.e,(t=>{if(t.key===fe.R.SHIFT||t.key===fe.R.ALT){const{altKey:e,shiftKey:i}=t.modifiers,s=i&&e?Q.xh.shiftAlt:e?Q.xh.alt:i?Q.xh.shift:Q.xh.desktop;this.setConstraintStyle(s)}}))]}onUpdate(t){if(this.getPhase()===X.au.CLOSED)return;const e=this.data.selectedGroupIndex,i=this.mobile?X.Ph.mobile[this.getPhase()]:X.Ph.desktop[this.getPhase()];for(const s in this.lineSidToPointMap){const n=this.lineDerivedDataFactory.get(s);if(n){n.opacity.tick(t);const a=this.lineSidToPointMap[s],o=this.pointGroups.groupFromPointIndex(a),r=o===e&&n.visible?1:n.opacity.value,h=i&&!n.labelVisible?0:r;this.textRenderer.setTextOpacityByPoint(a,h),this.renderer.setLineOpacityByPoint(a,r),-1!==o&&this.colliders.setGroupVisible(o,r>0)}}if(this.mobile)if(this.getPhase()===X.au.CONFIRMING_POINT){const t=(Date.now()-this.longPressStart)/this.threshold;t<=1&&(this.data.pressProgress=t,this.data.commit())}else this.getPhase()===X.au.CREATING&&0!==this.data.pressProgress&&(this.data.pressProgress=0,this.data.commit())}dispose(){var t,e;this.data.modeActive()&&(this.stopMeasuring(),this.engine.commandBinder.issueCommand(new O.O(!1))),this.colliders.dispose(),this.textRenderer.dispose(),this.renderer.dispose(),this.engine.disposeRenderLayer(this.name),null===(t=this.store)||void 0===t||t.dispose(),this.store=null,null===(e=this.newDataBinding)||void 0===e||e.cancel(),this.newDataBinding=null,super.dispose(this.engine)}clearMutationRecord(){for(const t of Object.values(this.mutationRecord))t.clear()}async save(){if(!this.config.readonly)return this.engine.commandBinder.issueCommand(new Pe.V({dataTypes:[Ie.g.MEASUREMENTS]}));this.clearMutationRecord()}async saveDiff(){var t,e;if(this.data.repopulate(this.pointGroups.groups()),this.data.commit(),this.data.notifyDataChanged(),this.config.readonly||!this.store)return void this.clearMutationRecord();const i=this.mutationRecord,s=[];this.log.debug("MDS mutation ops:",`{\n      added: '${[...i.added.keys()]}',\n      updated: '${[...i.updated.keys()]}',\n      removed: '${[...i.removed.keys()]}',\n    }`);const n=[];for(const t of i[_e.KI.removed]){if(i[_e.KI.added].has(t))i[_e.KI.added].delete(t);else{const e=this.removedLayerMap.get(t);n.push({pathId:t,layerId:e})}i[_e.KI.updated].delete(t)}this.removedLayerMap.clear();const a=this.store.delete(...n);s.push(a);for(const e of i[_e.KI.added]){if(this.layersData.isInMemoryLayer(null===(t=this.data.getGroupInfoBySid(e))||void 0===t?void 0:t.info.layerId))continue;const n=this.pointGroups.getGroupById(e);if(n){const t=this.store.create(n).then((t=>{t&&(this.log.debug(`Updating group id for new path: ${n.info.sid} -> ${t}`),this.pointGroups.updateGroupInfo(n.index,Object.assign(Object.assign({},n.info),{sid:t}))),this.data.repopulate(this.pointGroups.groups()),this.data.commit(),this.data.notifyDataChanged()}));s.push(t)}i[_e.KI.updated].delete(e)}const o=[];for(const t of i[_e.KI.updated]){if(this.layersData.isInMemoryLayer(null===(e=this.data.getGroupInfoBySid(t))||void 0===e?void 0:e.info.layerId))continue;const i=this.pointGroups.getGroupById(t);i&&o.push(i)}const r=this.store.update(o);return s.push(r),this.clearMutationRecord(),Promise.all(s)}updatePendingMeasurement(){const t=this.data.creatingGroupId;if(t){const e=this.pointGroups.getGroupById(t);if(!e)return void this.log.error("Missing pending measurement group");if(!this.layersData.isInMemoryLayer(e.info.layerId)){const i=this.layersData.activeLayerId;this.pointGroups.updateGroupInfo(e.index,Object.assign(Object.assign({},e.info),{layerId:i}));const s=this.data.getGroupInfoBySid(t);s&&(s.info.layerId=i,this.data.commit(),this.data.notifyDataChanged())}}}getMeasurementVisibility(t,e,i){if(!this.data.modeActive())return!1;const{selectedGroupSid:s,creatingGroupId:n,editingGroupId:a,idVisibility:o}=this.data,r=this.applicationData.application===L.Mx.WORKSHOP||this.layersData.layerToggled(e),h=this.layersData.layerVisible(e),d=[s,n,a].includes(t),l=!this.visibilityFilterEnabled||o.has(t);return r&&(d||i&&l&&h)}async registerDebugSettings(){const t=await this.engine.getModuleBySymbol(a.Ak);t.registerMenuEntry({header:"Measurement Debug",setting:"measure/fp_in_dh",initialValue:()=>!1,onChange:e=>(this.lineDerivedDataFactory.setDollhouseLineStyle(e?s.FloorplanOnly:s.ThreeD),t.updateSetting("measure/fp_in_dh",e))})}updateSettings(){const t=this.layersData.getCurrentView(),e=(null==t?void 0:t.viewType)===Me.XZ.TRUEPLAN,i=this.settings.tryGetProperty(Q.Ql,!0),s=e||i;this.settings.setProperty(Q.Ql,s)}instantiateCreator(t){let e;const i=this.viewmodeData,s=()=>{const e=i.isFloorplan(),s=i.isDollhouse()&&t.pose.pitchFactor()<.01;return e||s},n=t=>{const e={floorId:t.floorId||"",roomId:t.roomId||"",layerId:this.layersData.activeLayerId};return this.meshQueryModule.inferMeshIdsFromPoint(e,t.point,!0),e};if(this.mobile){const i=(t,e)=>{this.longPressStart=t,this.threshold=e},a=e=>{const i=(0,mt.q9)(t,e.point);this.data.setPointPosition(i.screenPosition)};e=new le(this.pointGroups,this.setSelected,this.input.registerUnfilteredHandler,this.pointer,this.changePhase,i,a,this.toggleCameraMovement,this.getPhase,(()=>this.settings.tryGetProperty(R.F.MeasurementContinuousLines,!1)),s,(()=>this.floorsViewData.getHighestVisibleFloorId()),this.currentRoomId,(()=>this.layersData.activeLayerId),n)}else e=new ce(this.pointGroups,this.setSelected,this.input.registerUnfilteredHandler,this.pointer,this.changePhase,this.getPhase,s,(()=>this.floorsViewData.getHighestVisibleFloorId()),this.currentRoomId,(()=>this.layersData.activeLayerId),(()=>this.settings.tryGetProperty(R.F.MeasurementContinuousLines,!1)),n,this.meshQueryModule);return e.onGroupCreated=this.onCreatorAddNewLine,e.onGroupAddPoint=this.onCreatorAddPoint,e.onEdit=this.onEdit,e.onDone=this.onCreatorStop,e.syncReadonly(this.config.readonly),e}async navigateToMeasurement({groupId:t}){const e=this.data.getGroupInfoBySid(t);if(!e)return;const i=[];for(const t of e)i.push(t);const n=(new y.Box3).setFromPoints(i),a=e.info.type===s.FloorplanOnly?v.Ey.Floorplan:v.Ey.Dollhouse;n.expandByScalar(a===v.Ey.Dollhouse?1.25:1);const o={mode:a,transition:c.n.Interpolate,floorId:e.info.floorId};try{await this.navigation.focus(n,o),this.setSelectedById(t)}catch(t){this.log.info("Unable to navigateToMeasurement:",t)}}registerRoomAssociationSource(t){const e=this.data;t.commandBinder.issueCommandWhenBound(new ri.I({type:"measurements",getPositionId:function*(){for(const t of e.groups())yield{id:t.info.sid,roomId:t.info.roomId,floorId:t.info.floorId,position:t.get(0),layerId:t.info.layerId}},updateRoomForId:(t,i)=>{const s=e.getGroupInfoBySid(t);if(!s)throw new Error("Invalid measurement group id");s.info.roomId=i||void 0}}))}}const ci=li},72119:(t,e,i)=>{"use strict";i.d(e,{E0:()=>o,Hn:()=>c,NZ:()=>a,Oq:()=>l,Ql:()=>u,X7:()=>h,ox:()=>r,xh:()=>d,yV:()=>n});var s=i(83402);const n=.01,a=.001,o=!0,r={highResThreshold:1081,desktopSize:192,desktopSizeHighRes:320,mobileSize:128},h={smoothness:.4,perspective:{fov:40,thresholdClose:1,thresholdFar:8,offsetClose:.25,offsetFar:.5,scale:1},ortho:{fov:5,thresholdClose:5,thresholdFar:20,offsetClose:15,offsetFar:30,scale:4}},d={desktop:s.l1.Edges,floorplan:s.l1.Axes,mobile:s.l1.Edges,alt:s.l1.Free,shift:s.l1.PlanarAxes,shiftAlt:s.l1.EdgesAndPlanarAxes,disabled:s.l1.Free},l={mobile:.15,desktop:.1},c={SCALE:.1,SCALE_NDC:.5,SCALE_ASPECT:.035,SCALE_DISTANCE:.025},u="measurements"},76325:(t,e,i)=>{"use strict";i.r(e),i.d(e,{ModelRatedMessage:()=>o.E,SubmitModelRatingCommand:()=>r.M,ToggleModelRatingDialogCommand:()=>r.m,default:()=>I});var s=i(70173),n=i(97542),a=i(7446),o=i(75783),r=i(99660),h=i(92211),d=i(73536),l=i(3907);const c=new(i(97998).Z)("model-rating-data-store"),u=t=>t,p=t=>t;class m extends l.MU{constructor(t,e,i){super({queue:t,deserialize:u,serialize:p,path:`${e}/api/v1/jsonstore/model/model-rating/${i}`}),this.queue=t,this.baseUrl=e,this.modelId=i}async canPromptRating(){let t;try{t=await this.read()}catch(t){return c.error("Failed to read existing rating data from JSONStore"),!1}const e=!!t.rated_at;return!!!t.prompt_dismissed_at&&!e}async recordRatingSubmitted(){this.update({rated_at:(new Date).toISOString()})}async recordAutomaticPromptDismissed(){this.update({prompt_dismissed_at:(new Date).toISOString()})}async reset(){var t;const e=`${this.baseUrl}/api/v1/jsonstore/model/model-rating/${this.modelId}`;if(!(null===(t=this.modelId)||void 0===t?void 0:t.length))throw new Error(`Refusing to DELETE ${e}`);if(window.confirm("Are you sure you want to reset the Model Rated value for this space? This cannot be undone."))return this.queue.delete(e,this.options).then((()=>{window.location.reload()}))}}var g=i(26158),v=i(34029),f=i(26568),y=i(61864);const w=t=>864e5*t,b=w(1),S=w(7);class D extends n.Y{constructor(){super(...arguments),this.name="model-rating",this.promptEnabled=!1,this.totalActiveTime=0,this.wasLastOpeningAutomatic=!1,this.canPrompt=async()=>{const t=Date.now(),e=+this.settings.tryGetProperty(g.F.LastRatingPromptTime,null),i=!e||isNaN(e)||+new Date(t)-+new Date(e)>=b,s=await this.store.canPromptRating();return this.log.debug(`canPromptUser: ${i}, canPromptModel: ${s}`),i&&s},this.handleActivityPingMessage=async t=>{if(this.totalActiveTime+=t.durationDollhouse+t.durationFloorplan+t.durationInside,this.log.debug(`prompt timing update: totalActiveTime:${this.totalActiveTime}, threshHold: 74500`),this.totalActiveTime<74500||!this.promptEnabled)return;this.engine.unsubscribe(s.i,this.handleActivityPingMessage);if(await this.canPrompt()&&!this.viewData.isDialogVisible){this.log.debug("automatically prompting user for Model Rating");const t=(new Date).toISOString();this.settings.setLocalStorageProperty(g.F.LastRatingPromptTime,t),this.engine.commandBinder.issueCommand(new r.m(!0)),this.wasLastOpeningAutomatic=!0}},this.handleSubmitModelRatingCommand=async({rating:t,didFinish:e})=>{this.log.info(t),this.engine.broadcast(new o.E(t)),this.store.recordRatingSubmitted(),e&&(this.engine.commandBinder.issueCommand(new r.m(!1)),this.engine.commandBinder.issueCommand(new h.B(d.P.RATING_THANK_YOU,!0)))},this.handleToggleDialogCommand=async({toVisible:t})=>{this.viewData.setDialogVisible(t);!this.viewData.isDialogVisible&&this.wasLastOpeningAutomatic&&(this.store.recordAutomaticPromptDismissed(),this.wasLastOpeningAutomatic=!1)}}async init(t,e){this.engine=e,this.config=t,this.viewData=new a.P,this.store=new m(t.queue,t.baseUrl,t.baseModelId),this.promptEnabled=t.promptEnabled,this.settings=await e.market.waitForData(v.e),this.modelData=await e.market.waitForData(f.T),this.bindings.push(e.commandBinder.addBinding(r.m,this.handleToggleDialogCommand),e.commandBinder.addBinding(r.M,this.handleSubmitModelRatingCommand));const i=this.modelData.model.created;let n=!!i&&Date.now()-+new Date(i)<S;this.promptEnabled&&t.debug&&(this.log.info("Debug Mode Enabled: Ignoring max model age requirement for rating prompt."),n=!0),this.log.debug(`promptEnabled: ${t.promptEnabled}, isModelEligible: ${n}`),this.promptEnabled&&n&&this.bindings.push(e.subscribe(s.i,this.handleActivityPingMessage)),e.market.register(this,a.P,this.viewData),this.registerSettings()}async registerSettings(){const t=await this.engine.getModuleBySymbol(y.Ak),{debug:e}=this.config;e&&t.registerMenuButton({header:"Model Rating",buttonName:"Reset Model Rated value",callback:()=>{this.settings.setLocalStorageProperty(g.F.LastRatingPromptTime,null),this.store.reset()}})}dispose(t){this.bindings.forEach((t=>{t.cancel()})),this.bindings=[],super.dispose(t)}}const I=D},81392:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>tt});var s=i(97542),n=i(54244),a=i(10374),o=i(34029),r=i(79728),h=i(89570),d=i(92810),l=i(80742),c=i(80008),u=i(20241),p=i(28269),m=i(635),g=i(77161),v=i(32683),f=i(83252),y=i(47309),w=i(35221),b=i(5823),S=i(26158),D=i(46391),I=i(63422),P=i(38399),T=i(71166),M=i(56163),E=i(76957),x=i(12039),C=i(15004),R=i(84784),A=i(18544),L=i(65019);class O extends M.K{constructor(t,e,i,s,n,a,o){super(t,e,s),this.room=n,this.title=a,this.id=this.room.id,this.icon=`icon-${(0,C.mX)(this.room.roomTypeIds)}`,this.typeId=b.SF.MODELROOM,this.layerId=I.gi,this.dateBucket=w.Z.OLDER,this.enabled=!0,this.onSelect=async()=>{super.onSelect(),await this.commandBinder.issueCommand(new x.SG(this.room))},this.floorId=o,this.roomId=n.id;const r=i.tryGetProperty(S.F.UnitType,D.M.IMPERIAL);this.description=this.room.getMeasurementText(r)}}var k=i(75668),F=i(97704),N=i(39011),V=i(17788),B=i(22999),G=i(97998),_=i(78283);const H=new G.Z("MdsRoomBoundsDeserializer");class U{deserialize(t){var e,i,s,n,a,o,r,h,d,l,c,u,p,m,g,v;const f={version:0,floors:{}};for(const r of t.floors||[]){const t={edges:{},vertices:{},rooms:{}};f.floors[r.id]=t;for(const s of r.vertices||[])s?t.vertices[s.id]={x:s.position.x,y:s.position.y,layerId:null!==(i=null===(e=s.layer)||void 0===e?void 0:e.id)&&void 0!==i?i:I.gi}:H.warn("Found null vertex in floor vertices!");for(const e of r.edges||[]){if(!e||null===e.thickness||null===e.centerLineBias){H.warn("Invalid edge!");continue}const i={vertices:(e.vertices||[]).map((t=>null==t?void 0:t.id)),thickness:e.thickness,bias:e.centerLineBias,openings:{},type:e.type||void 0,layerId:null!==(n=null===(s=e.layer)||void 0===s?void 0:s.id)&&void 0!==n?n:I.gi};t.edges[e.id]=i;for(const t of e.openings||[]){if(!t){H.warn("Found null opening!");continue}const e={height:t.height,lowerElevation:t.lowerElevation,relativePos:t.relativeCenter,type:t.type,width:t.width,layerId:null!==(o=null===(a=t.layer)||void 0===a?void 0:a.id)&&void 0!==o?o:I.gi};i.openings[t.id]=e}}}for(const e of t.rooms||[]){if(!e||!e.floor){H.warn("Found null room");continue}if(!f.floors[e.floor.id]){H.warn("Unable to find floor for room!");continue}const t=e.classifications||[].sort(((t,e)=>(t.confidence||0)-(e.confidence||0))),i=null===(r=e.dimensionEstimates)||void 0===r?void 0:r.units,s=(e.label||"").trim();f.floors[e.floor.id].rooms[e.id]={edges:(null===(d=null===(h=e.boundary)||void 0===h?void 0:h.edges)||void 0===d?void 0:d.map((t=>t.id)))||[],holes:(null===(l=e.holes)||void 0===l?void 0:l.map((t=>{var e;return(null===(e=t.edges)||void 0===e?void 0:e.map((t=>t.id)))||[]})))||[],classifications:t,label:s,width:this.ensureMetric("distance",null===(c=e.dimensionEstimates)||void 0===c?void 0:c.width,i),length:this.ensureMetric("distance",null===(u=e.dimensionEstimates)||void 0===u?void 0:u.depth,i),area:this.ensureMetric("area",null===(p=e.dimensionEstimates)||void 0===p?void 0:p.area,i),height:this.ensureMetric("distance",null===(m=e.dimensionEstimates)||void 0===m?void 0:m.height,i)||NaN,layerId:null!==(v=null===(g=e.layer)||void 0===g?void 0:g.id)&&void 0!==v?v:I.gi,keywords:e.keywords||[]}}return f}ensureMetric(t,e,i){if(null!=e&&null!=i){const s=i===b.nL.IMPERIAL,n="area"===t?_.W3:_._F;return s?n(e):e}return NaN}}var W=i(16747),j=i(65241);const z=new G.Z("MdsRoomBoundsStore");class $ extends V.u{constructor(t,e=!1,i=(()=>{})){super(t),this.writeRepairs=e,this.broadcast=i,this.queuedMutations=[],this.seenRooms=new Map,this.lastUpdates=new Map,this.deserializer=new U}async read(t){var e,i;const s={modelId:this.getViewId()},n=await this.query(B.GetRoomBounds,s,t);return(null===(i=null===(e=null==n?void 0:n.data)||void 0===e?void 0:e.model)||void 0===i?void 0:i.floors)?this.validateData(this.deserializer.deserialize(n.data.model)):null}async readClassifications({options:t,localizeFn:e}={}){var i;return((null===(i=(await this.query(B.GetRoomClassifications,{},t)).data)||void 0===i?void 0:i.roomClassifications)||[]).reduce(((t,i)=>(i&&(t[i.id]=e?e(i):i),t)),{})}get readonly(){return this.config.readonly}set readonly(t){this.config.readonly=t}queueRemoveEntity(t,e,i){this.queueDeleteRoomsAndBoundaryData(t,e,i)}queueAddNode(t){this.queuedMutations.push(`addBoundaryVertex(modelId: $modelId, id: "${t.id}", vertex: ${this.vertexDetailsFromNode(t)}) { id }`)}queueUpdateNode(t){this.updateEntity("node",t.id,`patchBoundaryVertex(modelId: $modelId, id: "${t.id}", vertex: ${this.vertexDetailsFromNode(t)}) { id }`)}vertexDetailsFromNode(t){return`{\n      floorId: "${t.floorId}",\n      position: { x: ${t.x} y: ${-t.z} }\n      ${this.layerId(t.layerId)}\n    }`}queueRemoveNode(t){this.queueRemoveEntity("node",t.id,t.layerId)}queueAddWall(t){this.queuedMutations.push(`addBoundaryEdge(modelId: $modelId, id: "${t.id}", edge: ${this.edgeDetailsFromWall(t)}) { id }`)}queueUpdateWall(t){this.updateEntity("wall",t.id,`patchBoundaryEdge(modelId: $modelId, id: "${t.id}" edge: ${this.edgeDetailsFromWall(t)}) { id }`)}edgeDetailsFromWall(t){return`\n    {\n      floorId: "${t.floorId}"\n      vertices: ["${t.from.id}", "${t.to.id}"]\n      thickness: ${t.width}\n      units: ${b.nL.METRIC}\n      centerLineBias: ${1-t.bias}\n      type: ${t.type===W.d.SOLID?b.Pb.WALL:b.Pb.INVISIBLE}\n      ${this.layerId(t.layerId)}\n    }\n    `}queueRemoveWall(t){this.queueRemoveEntity("wall",t.id,t.layerId)}queueAddOpening(t){this.queuedMutations.push(`addEdgeOpenings(modelId: $modelId, id: "${t.wallId}", openings: [${this.openingDetailsFromOpening(t)}]) { id }`)}queueUpdateOpening(t){this.updateEntity("opening",t.id,`patchEdgeOpening(modelId: $modelId, id: "${t.wallId}", opening: ${this.openingDetailsFromOpening(t)}) { id }`)}openingDetailsFromOpening(t){return`\n    {\n      id: "${t.id}"\n      relativeCenter: ${t.relativePos}\n      type: ${t.type}\n      width: ${t.width}\n      ${this.layerId(t.layerId)}\n      height: 0.1,\n      lowerElevation: 0.1\n    }\n    `}queueRemoveOpening(t){this.queuedMutations.push(`removeEdgeOpenings(\n        modelId: $modelId\n        id: "${t.wallId}"\n        openings: ["${t.id}"]\n        layerId: "${t.layerId}"\n      )`),this.clearEntityUpdate("opening",t.id)}queueAddRoom(t){this.queuedMutations.push(`addRoom(\n        modelId: $modelId,\n        id: "${t.id}",\n        room: ${this.getRoomDetailsFromRoom(t)}\n      ) { id }`)}queueUpdateRoom(t){const e=this.seenRooms.get(t.id);e&&e!==t&&this.clearEntityUpdate("room",t.id),this.seenRooms.set(t.id,t),this.updateEntity("room",t.id,`patchRoom(\n        modelId: $modelId,\n        id: "${t.id}",\n        room: ${this.getRoomDetailsFromRoom(t)}\n      ) { id }`)}getRoomDetailsFromRoom(t){const e=!t.showDimensions,i=!t.showHeight;return`\n    {\n      floorId: "${t.floorId}"\n      label: "${this.escapeUserString(t.name)}"\n      classifications: [${t.roomTypeIds.map((t=>`"${t}"`))}],\n      ${this.layerId(t.layerId)}\n      boundary: {\n        edges: [${Array.from(t.walls.values()).map((t=>`"${t.id}"`))}],\n      }\n      holes: [${Array.from(t.holes.values()).map((t=>`{ edges: [${Array.from(t.values()).map((t=>`"${t.id}"`))}] }`))}]\n      dimensionEstimates: {\n        room: "${t.id}",\n        area: ${Number.isNaN(t.area)?0:t.area},\n        areaIndoor: ${Number.isNaN(t.area)?null:t.area},\n        depth: ${Number.isNaN(t.length)||e?0:t.length}\n        width: ${Number.isNaN(t.width)||e?0:t.width}\n        height: ${Number.isNaN(t.height)||i?0:t.height}\n        units: ${b.nL.METRIC}\n      }\n      keywords: [${t.allKeywords().map((t=>`"${t}"`))}]\n    }\n    `}queueRemoveRoom(t){this.seenRooms.delete(t.id),this.queueRemoveEntity("room",t.id,t.layerId)}queueRemoveLegacyRooms(t){const e=t.map((t=>`"${t}"`));this.queuedMutations.push(`deleteRoomsAndBoundaryData(\n      modelId: $modelId,\n      selectedData: {\n        ids: [${e.join(",")}],\n        type: ${b.TS.ROOM}\n      }\n    )`)}queueUpdateRoomAssociations(t){if(0===t.length)return;this.queuedMutations.push(`bulkPatchRoomData(\n        modelId: $modelId,\n        updatedRoomAssociations: [${t.map((t=>(t=>`{ ${Object.keys(t).map((e=>`${e}: ${JSON.stringify(t[e])}`)).join(",")} }`)(t)))}]\n      )`)}peekQueuedMutations(){return this.queuedMutations.slice()}async submitQueuedMutations(){if(0===this.queuedMutations.length)return;const t=this.queuedMutations.slice();this.queuedMutations.length=0;for(const t of this.lastUpdates.values())t.clear();let e=0,i=0;for(;i<t.length;){e=i;let s=!1,n=!1;for(;i<t.length&&i-e<100&&!n;)s?n=t[i].startsWith("add"):s=t[i].startsWith("delete"),n||i++;const a=t.slice(e,i),o=F.gql`
        mutation updateRoomBoundaries($modelId: ID!) {
          ${a.map(((t,e)=>`op${e}: ${t}`)).join("\n")}
        }
      `;z.debug((0,N.S)(o)),await this.mutate(o,{modelId:this.getViewId()}),z.debug("Batch mutation successful")}}updateEntity(t,e,i){const s=this.lastUpdates.get(t)||new Map;this.lastUpdates.set(t,s);const n=s.get(e);void 0!==n?this.queuedMutations[n]=i:(this.queuedMutations.push(i),s.set(e,this.queuedMutations.length-1))}clearEntityUpdate(t,e){const i=this.lastUpdates.get(t);i&&i.delete(e)}queueDeleteRoomsAndBoundaryData(t,e,i){let s=b.TS.BOUNDARYVERTEX;switch(t){case"room":s=b.TS.ROOM;break;case"wall":s=b.TS.BOUNDARYEDGE}this.queuedMutations.push(`deleteRoomsAndBoundaryData(\n        modelId: $modelId,\n        selectedData: {\n          ids: ["${e}"],\n          type: ${s},\n          ${this.layerId(i)}\n        }\n      )`),this.clearEntityUpdate(t,e)}layerId(t){return t!==I.gi?`layerId: "${t}"`:""}escapeUserString(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}validateData(t){var e,i,s;const n=this.config.readonly||!this.writeRepairs;Object.values(t.floors).forEach((t=>{const e={};Object.values(t.edges).forEach((t=>{t.vertices&&t.vertices.forEach((t=>{e[t]=!0}))}));const i={};Object.entries(t.vertices).forEach((([t,s])=>{e[t]?i[t]=s:n?this.broadcast(new j.$(`validateData: Would delete orphan node: ${t}`)):(this.broadcast(new j.$(`validateData: Deleting orphan node: ${t}`)),this.queueRemoveEntity("node",t,s.layerId))})),t.vertices=i})),Object.values(t.floors).forEach((t=>{Object.entries(t.edges).forEach((([e,i])=>{var s;2===(null===(s=i.vertices)||void 0===s?void 0:s.length)&&t.vertices[i.vertices[0]]&&t.vertices[i.vertices[1]]||(delete t.edges[e],n?this.broadcast(new j.$(`validateData: Would delete invalid wall: ${e}`)):(this.broadcast(new j.$(`validateData: Deleting invalid wall: ${e}`)),this.queueRemoveEntity("wall",e,i.layerId)))}))}));for(const a of Object.values(t.floors))for(const t of Object.keys(a.rooms)){const o=a.rooms[t];let r=!1;const h=(null!==(e=o.edges)&&void 0!==e?e:[]).concat(null!==(s=null===(i=o.holes)||void 0===i?void 0:i.flat())&&void 0!==s?s:[]);for(const t of h){a.edges[t]||(r=!0)}r&&(n?this.broadcast(new j.$(`validateData: Would delete invalid room: ${t}`)):(this.broadcast(new j.$(`validateData: Deleting invalid room: ${t}`)),this.queueRemoveEntity("room",t,o.layerId)),delete a.rooms[t])}return Object.values(t.floors).forEach((t=>{const e=(e,i)=>{n?this.broadcast(new j.$(`validateData: Would delete existing wall: ${e}`)):(this.broadcast(new j.$(`validateData: Deleting existing wall: ${e}`)),this.queueRemoveEntity("wall",e,i)),delete t.edges[e]},i=new Set;Object.values(t.rooms).forEach((t=>{for(const e of t.edges||[])i.add(e)}));const s=new Map;Array.from(Object.entries(t.edges)).forEach((([n,a])=>{if(!a.vertices)return;const o=a.vertices.sort().join(":");if(s.has(o))if(i.has(n)){const a=s.get(o);if(a&&!i.has(a)){const i=t.edges[a];e(a,i.layerId),s.set(o,n)}}else e(n,a.layerId);else s.set(o,n)}))})),n||this.submitQueuedMutations(),t}}var q=i(62496),Y=i(3907);class Z extends Y.MU{constructor(t,e,i){const s=new X,n=new K;super({queue:t,path:`${e}/api/v1/jsonstore/model/room-bound-debug/${i}`,batchUpdate:!1,deserialize:t=>s.deserialize(t),serialize:t=>n.serialize(t)})}}class X{deserialize(t){if(!this.isValid(t))return null;return{mutations:t.mutations.slice(),error:t.error,localData:t.localData,actionList:t.actionList}}isValid(t){return!(!t||"object"!=typeof t)&&(t.mutations&&"object"==typeof t.mutations)}}class K{serialize(t,...e){return t}}var Q=i(49416);class J extends s.Y{constructor(){super(...arguments),this.name="room_bound_data",this.associationSources=[],this.modifiedFloors=new Set,this.afterFinalize=async()=>{if(!this.writeBindings)throw new Error("Not permitted to write room bounds data");const t=this.data.getSnapshot(),e=this.store.peekQueuedMutations();try{this.store.queueUpdateRoomAssociations(this.updateRoomAssociations()),await this.store.submitQueuedMutations(),this.data.getAndClearActionList()}catch(i){throw this.log.info("Error finalizing room bounds data",i),this.data.clearUndoBuffer(),this.appData.error=i,this.appData.commit(),this.debugStore.update({error:`${(new Date).toUTCString()} ${i.message}`,mutations:e,localData:t,actionList:this.data.getAndClearActionList()}),i}},this.addRoom=t=>{this.roomData.get(t.id)||this.roomData.add(new u.d({id:t.id,floorId:t.floorId,meshSubgroup:-1})),this.addToModifiedFloors(t),this.data.legacyRoomIds.length&&(this.store.queueRemoveLegacyRooms(this.data.legacyRoomIds),this.data.legacyRoomIds.forEach((t=>this.roomData.remove(t))),this.data.legacyRoomIds.length=0),this.store.queueAddRoom(t)},this.removeRoom=t=>{this.roomData.get(t.id)&&this.roomData.remove(t.id),this.addToModifiedFloors(t),this.store.queueRemoveRoom(t)}}async init(t,e){const[i,s,c,u]=await Promise.all([e.market.waitForData(l.R),e.market.waitForData(p.Z),e.market.waitForData(n.pu),e.market.waitForData(o.e)]);this.roomData=s,this.layersData=i,this.appData=c,this.issueCommand=e.commandBinder.issueCommand;const w=i.getBaseModelView();if(!w)return e.market.register(this,g.Z,new g.Z(null)),u.setProperty(y._h,!0),void e.broadcast(new j.x(new Error("Unable to find view for RoomBoundData")));this.localeModule=await e.getModuleBySymbol(d.e9),this.store=new $({viewId:w.id,context:i.mdsContext,readonly:!0,baseUrl:t.baseUrl},1===u.getOverrideParam("rbeRepair",0),e.broadcast.bind(e)),this.debugStore=new Z(t.requestQueue,t.baseUrl,w.id);const D=await this.store.read(),I=await this.store.readClassifications({localizeFn:t=>this.localizeRoomClassification(t)});k.Ds.forEach((t=>{const e=t.join(k.Rt);I[e]={id:e,label:t.map((t=>{var e;return(null===(e=I[t])||void 0===e?void 0:e.label)||[]})).join(k.X9),defaultKeywords:(0,v.Hc)(t.map((t=>I[t])))}})),this.data=new g.Z(D,I,e.broadcast.bind(e)),this.data.commit(),this.data.rooms.size>0&&u.setProperty(q.x,!0),this.data.clearUndoBuffer(),this.data.resetHistory(),t.readonly||(this.writeBindings=new h.V(this.data.onWallsChanged({onAdded:t=>{this.addToModifiedFloors(t),this.store.queueAddWall(t)},onUpdated:t=>{this.addToModifiedFloors(t),this.store.queueUpdateWall(t)},onRemoved:t=>{this.addToModifiedFloors(t),this.store.queueRemoveWall(t)}}),this.data.onNodesChanged({onAdded:t=>{this.addToModifiedFloors(t),this.store.queueAddNode(t)},onUpdated:t=>{this.addToModifiedFloors(t),this.store.queueUpdateNode(t)},onRemoved:t=>{this.addToModifiedFloors(t),this.store.queueRemoveNode(t)}}),this.data.onRoomsChanged({onAdded:this.addRoom,onUpdated:t=>{this.addToModifiedFloors(t),this.store.queueUpdateRoom(t)},onChildUpdated:t=>{this.addToModifiedFloors(t),this.store.queueUpdateRoom(t)},onRemoved:this.removeRoom}),this.data.onOpeningsChanged({onAdded:t=>{this.addToModifiedFloors(t),this.store.queueAddOpening(t)},onUpdated:t=>{this.addToModifiedFloors(t),this.store.queueUpdateOpening(t)},onRemoved:t=>{this.addToModifiedFloors(t),this.store.queueRemoveOpening(t)}}),this.data.afterFinalize((async()=>{await e.commandBinder.issueCommand(new m.V({dataTypes:[r.g.ROOM_BOUNDS],onCallback:this.afterFinalize,skipDirtyUpdate:!0}))}))),this.writeBindings.cancel()),await this.updateForApp(e,c.application),this.bindings.push(e.subscribe(a.bS,(t=>this.updateForApp(e,t.application))),e.commandBinder.addBinding(f.I,(t=>this.registerRoomAssociationSource(t.roomAssociation)))),async function(t,e){const[i,s,a,r,c,u]=await Promise.all([t.market.waitForData(n.pu),t.market.waitForData(l.R),t.market.waitForData(o.e),t.market.waitForData(E.i),t.getModuleBySymbol(d.e9),t.market.waitForData(A.e)]);let p=i.application===n.Mx.WORKSHOP;const m=(i,n,o,h=[])=>{const d=a.tryGetProperty(L.wY,!1),l=a.tryGetProperty(L.dF,!1),m=d||l||p,g=a.tryGetProperty(y.hW,!1)&&u.visibleInShowcase||p,v=0===h.length;if(!(g&&m&&v))return[];const f=[];for(const o of e.rooms.values()){const h=(0,R.LN)(o.id,c,e);if(i(o.name)||i(h)){const e=r.getFloor(o.floorId);if(!e)throw new Error("Unable to find floor for room while generating search results.");f.push(new O(t.commandBinder,s,a,n,o,h,e.id))}}return f.sort(((t,e)=>t.title.localeCompare(e.title)))},g=t=>{},v=t=>new h.V(e.onChanged(t),a.onPropertyChanged(S.F.UnitType,t),u.onChanged(t)),f={renew:()=>{t.commandBinder.issueCommandWhenBound(new T.c6({id:b.SF.MODELROOM,groupPhraseKey:P.Z.SHOWCASE.ROOMS.SEARCH_GROUP_HEADER,getSimpleMatches:m,registerChangeObserver:v,onSearchActivatedChanged:g,groupOrder:100,groupIcon:"edit-floorplan",batchSupported:!1}))},cancel:()=>{t.commandBinder.issueCommandWhenBound(new T.Pe(b.SF.MODELROOM))}},w=()=>{p=i.application===n.Mx.WORKSHOP,a.tryGetProperty(y.hW,!1)&&u.visibleInShowcase||p?f.renew():f.cancel()},D=i.onPropertyChanged("application",w),I=a.onPropertyChanged(y.hW,w),M=u.onChanged(w);return w(),new h.V(f,D,I,M)}(e,this.data).then((t=>this.bindings.push(t))),e.market.register(this,g.Z,this.data)}dispose(t){super.dispose(t),this.writeBindings&&this.writeBindings.cancel()}localizeRoomClassification(t){return this.localeModule?Object.assign(Object.assign({},t),{label:(0,R.Nw)(t,this.localeModule)}):t}trackRoomBoundsABTest(t,e){Promise.all([t.getModuleBySymbol(d.V6),t.market.waitForData(o.e)]).then((([t,i])=>{const s=this.data.rooms.size>0,a=e===n.Mx.WORKSHOP||s&&(0,y.G)(i);t.trackFeatures(`${y.A0}:${a}`)}))}async updateForApp(t,e){const i=e===n.Mx.SHOWCASE;if(this.trackRoomBoundsABTest(t,e),i!==this.store.readonly&&(this.store.readonly=i,this.writeBindings))if(i)this.writeBindings.cancel();else{this.writeBindings.renew(),await this.issueCommand(new c.fk);const t=this.layersData.getViewLayerIdForBaseView();if(!t)throw Error("Cannot edit rooms without base view view-data layer");this.data.defaultLayerId=t,this.data.validateGraph()}}addToModifiedFloors(t){this.modifiedFloors.add(t.floorId)}updateRoomAssociations(){const t=new Map;for(const e of this.associationSources)for(const i of e.getPositionId()){if(!i.floorId||!this.modifiedFloors.has(i.floorId)||!(0,Q.O)(this.data,this.layersData,i.layerId))continue;const s=this.data.findRoomIdForPosition(i.position,i.floorId,i.roomId);if(s!=i.roomId){const n=t.get(s)||(s?{roomId:s}:{resetRoom:!0}),a=n[e.type]||[];a.push(i.id),n[e.type]=a,t.set(s,n),e.updateRoomForId(i.id,s)}}return this.modifiedFloors.clear(),Array.from(t.values())}async registerRoomAssociationSource(t){this.associationSources.push(t)}}const tt=J},25869:(t,e,i)=>{"use strict";i.d(e,{$:()=>n,o:()=>s});var s,n,a=i(12529);!function(t){t[t.BASE=a.z.roomBounds]="BASE",t[t.OPENING_STENCIL=a.z.roomBounds+1]="OPENING_STENCIL",t[t.EDGE_STENCIL=a.z.roomBounds+2]="EDGE_STENCIL",t[t.ROOM=a.z.roomBounds+3]="ROOM",t[t.EDGE=a.z.roomBounds+4]="EDGE",t[t.HIGHLIGHTED_EDGE=a.z.roomBounds+5]="HIGHLIGHTED_EDGE",t[t.OPENING_LINES=a.z.roomBounds+6]="OPENING_LINES",t[t.NODE=a.z.roomBounds+7]="NODE"}(s||(s={})),function(t){t[t.OPENINGS=0]="OPENINGS",t[t.EDGE=1]="EDGE"}(n||(n={}))},89590:(t,e,i)=>{"use strict";i.d(e,{c:()=>x});var s=i(15462),n=i(27990),a=i(93411),o=i(69505),r=i(73121),h=i(23885);function d(t,e,i){const s=function(t){return t/180*Math.PI}(e||0);if(!i||0===i[0]&&0===i[1])return l(t,s);return l(t.map(((t,e)=>t-i[e])),s).map(((t,e)=>t+i[e]))}function l(t,e){return[t[0]*Math.cos(e)-t[1]*Math.sin(e),t[0]*Math.sin(e)+t[1]*Math.cos(e)]}function c(t,e,i){let s=[];for(let n=0,a=t.length;n<a;n++)s[n]=d(t[n],e,i);return s}var u=i(65661),p=i(81396),m=i(25869),g=i(89392),v=i(82403),f=i(3297);class y extends p.Mesh{constructor(t,e,i){const s=new Float32Array([-1,0,-1,1,1,1,1,0]),n=new Float32Array([0,1,0,0,1,0,0,1,0,0,1,0]),a=new p.BufferGeometry;a.setAttribute("offset",new p.Float32BufferAttribute(s,2)),a.setAttribute("normal",new p.Float32BufferAttribute(n,3)),a.setIndex([0,3,1,3,2,1]);super(a,new p.RawShaderMaterial({uniforms:p.UniformsUtils.clone(f.Ud.uniforms),vertexShader:f.Ud.vertexShader,fragmentShader:f.Ud.fragmentShader,side:p.FrontSide,transparent:!0,depthTest:!1})),this.updateState(t,e,i),this.renderOrder=m.o.EDGE,this.frustumCulled=!1,this.onBeforeRender=t=>{t.getSize(this.material.uniforms.screenSize.value)}}updateState(t,e,i){this.material.uniforms.tip.value.copy(t),this.material.uniforms.normal.value.copy(e),this.material.uniforms.height.value=i}updateOpacity(t){this.material.uniforms.opacity.value=t}updateMetersPerPx(t){this.material.uniforms.metersPerPx.value=t}raycast(t){}}var w,b=i(69877),S=i(10840),D=i(12529);!function(t){t[t.PIXELS=0]="PIXELS",t[t.METERS=1]="METERS"}(w||(w={}));class I extends p.Mesh{constructor(t,e,i){const s=new Float32Array([-9999,-9999,-9999,9999,9999,9999,-9999,-9999,-9999,9999,9999,9999,9999,9999,9999,-9999,-9999,-9999]),n=new Float32Array([1,1,0,0,-1,-1]),a=new Float32Array([0,1,0,1,0,1]),o=new p.BufferGeometry;o.setAttribute("position",new p.Float32BufferAttribute(s,3)),o.setAttribute("offsetDirection",new p.Float32BufferAttribute(n,1)),o.setAttribute("t",new p.Float32BufferAttribute(a,1)),o.setIndex([0,2,1,2,3,1,2,4,3,4,5,3]);let r={},h={};(null==i?void 0:i.dashUnits)===w.METERS&&(r={WORLDSPACE_DASH:!0},h={derivatives:!0});super(o,new p.RawShaderMaterial({uniforms:p.UniformsUtils.clone(S.T.screenline.uniforms),side:p.FrontSide,vertexShader:S.T.screenline.vertexShader,fragmentShader:S.T.screenline.fragmentShader,transparent:!0,depthTest:i.depthTest,depthWrite:i.depthWrite,depthFunc:i.depthFunc,defines:r,extensions:h})),this.updateEndpoints(t,e),this.geometry.computeBoundingBox(),this.geometry.computeBoundingSphere(),this.renderOrder=D.z.lines,this.onBeforeRender=t=>{t.getSize(this.material.uniforms.screenSize.value)},this.material.uniforms.dashed.value=+i.dashed,this.material.uniforms.dashSize.value=i.dashSize,this.material.uniforms.gapSize.value=i.gapSize,this.material.uniforms.lineWidth.value=i.lineWidth,this.material.uniforms.color.value.copy(i.color),this.material.uniforms.opacity.value=i.opacity}updateEndpoints(t,e){this.material.uniforms.start.value.copy(t),this.material.uniforms.end.value.copy(e)}opacity(t){this.material.uniforms.opacity.value=t}color(t){this.material.uniforms.color.value.copy(t)}}var P=i(59370),T=i(59279),M=i(28941);const E={baseState:{innerColor:s.I.LENS_GRAY,outerColor:s.I.WHITE,opacity:0},hoverState:{innerColor:s.I.WHITE,outerColor:s.I.WHITE,opacity:.25},selectState:{innerColor:s.I.NEPTUNE,outerColor:s.I.WHITE,opacity:0},dimState:{innerColor:s.I.LENS_GRAY,outerColor:s.I.WHITE,opacity:.5}};class x extends g.H{constructor(t,e,i,o,h,d){const l=x.getGeoFromRoom(t,e),c=new p.MeshBasicMaterial({color:s.I.LENS_GRAY,depthTest:!1,side:p.DoubleSide,transparent:!0,opacity:0,blending:p.NormalBlending,blendEquation:p.AddEquation,blendSrc:p.SrcAlphaFactor,blendDst:p.DstColorFactor,stencilRef:65535,stencilFuncMask:1<<m.$.EDGE,stencilFail:p.KeepStencilOp,stencilZFail:p.KeepStencilOp,stencilZPass:p.KeepStencilOp,stencilFunc:p.GreaterStencilFunc,stencilWrite:!0});super(t.id,l,c,i),this.baseHeight=e,this.labelManager=o,this.units=h,this.showInPerspective=d,this.potentialLabels=[],this.perimeterLabels=[],this.dimensionCarets=[new y(new p.Vector2,new p.Vector2,0),new y(new p.Vector2,new p.Vector2,0),new y(new p.Vector2,new p.Vector2,0),new y(new p.Vector2,new p.Vector2,0)],this.hideMaterial=new p.RawShaderMaterial({vertexShader:f.AK.vertexShader,fragmentShader:f.AK.fragmentShader,uniforms:p.UniformsUtils.clone(f.AK.uniforms),depthTest:!1,side:p.DoubleSide,transparent:!0}),this.perimeterLabelsVisible=!1,this.fullLabelVisible=!1,this.hoverTimer=null,this.labelHovered=!1,this.heightLines=new p.Group,this.heightLabels=[],this.intersectionPriority=M.e.Labels,this.getLabelPolygon=(()=>{const t=new p.Vector3,e=[0,0],i=[e,[0,0],[0,0],[0,0],e];return(e,s)=>{const a=this.cameraData.pose.position.distanceTo(s),o=(0,r._U)(a,this.cameraData.pose.projection.asThreeMatrix4(),this.cameraData.width),h=e.split("\n"),d=h.reduce(((t,e)=>Math.max(t,e.length)),0),l=(0,n.hJ)(d,0,0),c=s.x,u=s.z,p=.5*l.width*o,m=.5*l.height*o*h.length;return t.set(c-p,0,u-m),t.applyQuaternion(this.cameraData.pose.rotation),i[0][0]=c-p,i[0][1]=u-m,i[1][0]=c+p,i[1][1]=u-m,i[2][0]=c+p,i[2][1]=u+m,i[3][0]=c-p,i[3][1]=u+m,i}})(),this.standardMaterial=c,this.renderOrder=m.o.ROOM,this.roomLabel=this.labelManager.createRoomLabel(t.floorId,t.id),this.roomLabel.addTo(this),this.roomLabel.setVisible(!t.hide),this.room=t;for(const t of this.dimensionCarets)this.add(t);this.updateLabel(!0),this.colors=E,this.animation.onChanged((()=>{const t=this.prevColorState;this.standardMaterial.color.lerpColors(t.innerColor,this.targetColorScheme.innerColor,this.animation.value);const e=(0,a.t)(t.opacity,this.targetColorScheme.opacity,this.animation.value);this.opacity=e,this.hideMaterial.uniforms.opacity.value=this.roomLabel.label.opacity})),this.onBeforeRender=()=>{this.standardMaterial.opacity=this.opacity*(1-this.pitchFactor);const t=this.dimState.active?.5:1;for(const e of this.dimensionCarets)e.updateMetersPerPx(i.metersPerPx()),e.updateOpacity((1-this.pitchFactor)*t)},this.add(this.heightLines)}dispose(){this.labelManager.deleteLabel(this.roomLabel);for(const t of this.perimeterLabels)this.labelManager.deleteLabel(t),t.dispose();this.clearHoverTimer(),super.dispose()}hoverRoomLabel(t){const e=()=>{if(!t&&this.roomLabel.getDisplayingTooltip()===t||t&&this.fullLabelVisible)return void(t||(this.labelHovered=!1,this.updateMaterial()));this.roomLabel.setDisplayingTooltip(t),this.updateMaterial();const e=this.roomLabel.label.collider.material;e.opacity=t?.65:0,e.transparent=!0,this.updateLabel(!1),this.updateCarets()};this.labelHovered=t,this.updatePerimeterLabelVisibility(),t?null===this.hoverTimer&&(this.hoverTimer=window.setTimeout(e,v.qb),this.updateMaterial()):(e(),this.clearHoverTimer())}updateGeo(t,e,i){this.room=t,this.baseHeight=e;const s=x.getGeoFromRoom(t,e);this.geometry.dispose(),this.geometry=s,this.geometry.computeBoundingBox(),this.updateLabel(!1),this.units=i,this.perimeterLabelsVisible&&this.updatePerimeterLabels(),this.heightLines.children.length&&this.createHeightObjects()}updateLabelBillboard(){const{position:t,rotation:e,projection:i}=this.cameraData.pose,{height:s}=this.cameraData,n=this.cameraData.isOrtho()?this.cameraData.zoom():1,a=this.cameraData.aspect();this.roomLabel.label.quaternion.copy(e),this.roomLabel.label.scaleBillboard(t,e,i,n,s,a,.1),this.updateLabel(!1);for(const t of this.perimeterLabels)t.update();this.updateMaterial(),this.updateCarets()}updatePerimeterLabelVisibility(){const t=this.hoverState.active||this.labelHovered;this.perimeterLabelsVisible=this.selectState.active||t&&(0,P.Eb)(this.pitchFactor),this.perimeterLabelsVisible&&this.updatePerimeterLabels();for(const t of this.perimeterLabels)t.setVisible(this.perimeterLabelsVisible);this.updateCarets()}updateMaterial(){this.material=this.room.hide?this.hideMaterial:this.standardMaterial,super.updateMaterial();const t=this.hoverState.active||this.labelHovered,e=(0,P.Eb)(this.pitchFactor);e||!this.selectState.active&&!t?this.heightLines.clear():this.heightLines.children.length||this.createHeightObjects(),t&&e&&(this.targetColorScheme=this.colors.hoverState);const i=this.standardMaterial;if(this.prevColorState.innerColor.copy(i.color),this.prevColorState.opacity=i.opacity,this.animation.modifyAnimation(0,1,v.rP,h.hl),this.roomLabel.setDimmed(this.dimState.active),!this.showInPerspective&&this.cameraData.pose.pitchFactor()>.5){this.roomLabel.setVisible(!1);for(const t of this.perimeterLabels)t.setVisible(!1)}else{this.roomLabel.setVisible(!this.room.hide||t);for(const t of this.perimeterLabels)t.setVisible(this.perimeterLabelsVisible)}this.updateHeightLabels(),this.updatePerimeterLabelVisibility()}updateText(t,e){this.potentialLabels=t,this.updateLabel(e),this.updateCarets()}static getGeoFromRoom(t,e){const{points:i,faces:s}=t.getGeometry(),n=i.map((t=>[t.x,e,t.y])).flat(1),a=new p.BufferGeometry;return a.setAttribute("position",new p.BufferAttribute(new Float32Array(n),3)),a.setIndex(s.flat(1)),a}updateLabel(t){if(0===this.potentialLabels.length)return;const e=this.room.points.map((t=>[t.x,t.z]));e.push(e[0]);const i=(new p.Euler).setFromQuaternion(this.cameraData.pose.rotation).z,s=o.MN*i,n=[];if(!t){const{position:t}=this.roomLabel.label,e={x:t.x,y:t.z};this.room.holesCW.find((t=>{const i=t.map((t=>({x:t.x,y:t.z})));return(0,b.L)(e,i)}))||n.push(t.clone())}n.push(this.room.getViewCenter(new p.Vector3,1.5));for(const t of n){this.roomLabel.label.position.copy(t),this.roomLabel.label.position.y=this.baseHeight+v.mU;let i=!1;this.fullLabelVisible=!0;for(const a of this.potentialLabels){const o=c(this.getLabelPolygon(a,t),-s,[t.x,t.z]);if((0,u.D)(o,e)||this.roomLabel.getDisplayingTooltip()||t===n[n.length-1]&&"..."===a){this.roomLabel.label.text=a,i=!0;break}this.fullLabelVisible=!1}if(i)break}}updatePerimeterLabels(){const t=this.room.hide?[]:this.room.minimalInnerLoop;for(;t.length>this.perimeterLabels.length;)this.perimeterLabels.push(this.labelManager.createPerimeterLabel(this.room.floorId).addTo(this));for(;t.length<this.perimeterLabels.length;){const t=this.perimeterLabels.pop();if(!t)throw new Error("Label should exist!");this.labelManager.deleteLabel(t)}for(let e=0;e<t.length;e++){const i=t[e],s=i.start.clone();s.y=this.baseHeight;const n=i.end.clone();n.y=this.baseHeight,this.perimeterLabels[e].updateDimensions(s,n,0,this.units)}}updateCarets(){const t=this.cameraData.metersPerPx(),e=this.room.length/t,i=this.room.width/t,s=this.roomLabel.getSize(),n=s.width+2>e,a=s.height+2>i,o=this.fullLabelVisible&&(n||a);if(!this.room.canDisplayDimensions()||this.perimeterLabelsVisible||!this.fullLabelVisible||o)for(const t of this.dimensionCarets)t.visible=!1;else{const t=new p.Vector2;t.subVectors(this.room.w2,this.room.w1).normalize(),this.dimensionCarets[0].updateState(this.room.w1,t,this.baseHeight),this.dimensionCarets[0].visible=!0,t.subVectors(this.room.w1,this.room.w2).normalize(),this.dimensionCarets[1].updateState(this.room.w2,t,this.baseHeight),this.dimensionCarets[1].visible=!0,t.subVectors(this.room.l2,this.room.l1).normalize(),this.dimensionCarets[2].updateState(this.room.l1,t,this.baseHeight),this.dimensionCarets[2].visible=!0,t.subVectors(this.room.l1,this.room.l2).normalize(),this.dimensionCarets[3].updateState(this.room.l2,t,this.baseHeight),this.dimensionCarets[3].visible=!0}}clearHoverTimer(){null!==this.hoverTimer&&(clearTimeout(this.hoverTimer),this.hoverTimer=null)}createHeightObjects(){this.heightLines.clear();for(const t of this.heightLabels)this.labelManager.deleteLabel(t);this.heightLabels.length=0;const t={dashed:!1,dashSize:1,gapSize:1,lineWidth:(0,T.eY)("rb_linewidth",1),color:s.I.WHITE,dashUnits:w.METERS,depthWrite:!1,depthTest:!0,depthFunc:p.LessDepth,opacity:1},e={dashed:!0,dashSize:.08,gapSize:.04,lineWidth:(0,T.eY)("rb_linewidth",1),color:s.I.WHITE,dashUnits:w.METERS,depthWrite:!1,depthTest:!0,depthFunc:p.GreaterDepth,opacity:1},i=(i,s)=>{const n=new I(i,s,e),a=new I(i,s,t);this.heightLines.add(a),this.heightLines.add(n)},n=new p.Vector3,a=new p.Vector3,o=this.room.getInnerLoops();for(const t of o){const e=t.length;for(let s=0;s<e;s++){const o=this.labelManager.createPerimeterLabel(this.room.floorId);n.copy(t[s]),n.y=this.baseHeight,a.copy(t[(s+1)%e]),a.y=this.baseHeight,o.updateDimensions(n,a,0,this.units),o.addTo(this),i(n,a),this.room.canDisplayHeight()&&(n.y+=this.room.height,a.y+=this.room.height,i(n,a))}if(this.room.canDisplayHeight())for(const e of t){n.copy(e),n.y=this.baseHeight,a.copy(n),a.y+=this.room.height,i(n,a);const t=this.labelManager.createPerimeterLabel(this.room.floorId);t.labelGroupId=this.room.id,t.updateDimensions(n,a,0,this.units),t.setVisible(!0),t.addTo(this.heightLines),this.heightLabels.push(t)}}}updateHeightLabels(){const t=this.selectState.active&&!(0,P.Eb)(this.pitchFactor);for(const e of this.heightLabels)e.setVisible(t)}setPitchFactor(t){this.pitchFactor=t,this.raycastEnabled=(0,P.Eb)(this.pitchFactor)||this.showInPerspective}}},89392:(t,e,i)=>{"use strict";i.d(e,{H:()=>r});var s=i(59370),n=i(89557),a=i(81396),o=i(25869);class r extends a.Mesh{constructor(t,e,i,s){super(e,i),this.roomBoundsId=t,this.cameraData=s,this.animation=new n.z(0),this.prevColorState={opacity:1,innerColor:new a.Color,outerColor:new a.Color},this.pitchFactor=1,this.raycastEnabled=!0,this.opacity=1,this.selectState={active:!1,on:()=>{this.updateMaterial()},off:()=>{this.updateMaterial()}},this.hoverState={active:!1,on:()=>{this.updateMaterial()},off:()=>this.updateMaterial()},this.highlightState={active:!1,on:()=>this.updateMaterial(),off:()=>this.updateMaterial()},this.dimState={active:!1,on:()=>this.updateMaterial(),off:()=>this.updateMaterial()},this.name=t,this.renderOrder=o.o.BASE}updateMaterial(){var t;let e=this.colors.baseState;this.dimState.active&&(e=this.colors.dimState),this.hoverState.active&&(e=this.colors.hoverState),this.highlightState.active&&(e=null!==(t=this.colors.highlightState)&&void 0!==t?t:this.colors.selectState),this.selectState.active&&(e=this.colors.selectState);e!==this.targetColorScheme&&(this.targetColorScheme=e)}tickAnimations(t){this.animation.tick(t)}dispose(){}raycast(t,e){if(!this.raycastEnabled)return;const i=[];super.raycast(t,i),i.length>0&&e.push(i[0])}setPitchFactor(t){this.pitchFactor=t,this.raycastEnabled=(0,s.Eb)(this.pitchFactor)}}},13268:(t,e,i)=>{"use strict";i.r(e),i.d(e,{RoomBoundEdgeView:()=>m,RoomBoundEndHandleView:()=>M,RoomBoundHandleView:()=>f,RoomBoundOpeningHandleView:()=>P,RoomBoundOpeningView:()=>D,RoomBoundRenderer:()=>nt,RoomBoundStartHandleView:()=>T,RoomBoundView:()=>l.H,default:()=>ct});var s=i(15462),n=i(19098),a=i(93411),o=i(73121),r=i(23885),h=i(81396),d=i(25869),l=i(89392),c=i(82403),u=i(3297);const p={baseState:{innerColor:s.I.WHITE,outerColor:s.I.WHITE,opacity:1},hoverState:{innerColor:s.I.WHITE,outerColor:s.I.NEPTUNE,opacity:1},selectState:{innerColor:s.I.WHITE,outerColor:s.I.NEPTUNE,opacity:1},dimState:{innerColor:s.I.WHITE,outerColor:s.I.WHITE,opacity:.5},highlightState:{innerColor:s.I.WHITE,outerColor:s.I.NEPTUNE,opacity:1}};class m extends l.H{constructor(t,e,i,l){const m=new Float32Array([0,0,0,1,0,0,1,0,1,0,0,1,-1,0,1,-1,0,0,1,0,2,-1,0,2,-1,0,-1,1,0,-1]),g=new h.BufferGeometry;g.setAttribute("position",new h.Float32BufferAttribute(m,3)),g.setIndex([0,1,2,0,2,3,0,3,4,0,4,5,3,2,6,3,7,4,0,9,1,0,5,8]);const v=new h.RawShaderMaterial({uniforms:h.UniformsUtils.clone(u.LD.uniforms),vertexShader:u.LD.vertexShader,fragmentShader:u.LD.fragmentShader,transparent:!0,depthTest:!1,stencilRef:65535,stencilFuncMask:1<<d.$.OPENINGS,stencilFail:h.KeepStencilOp,stencilZFail:h.KeepStencilOp,stencilZPass:h.KeepStencilOp,stencilFunc:h.GreaterStencilFunc,stencilWrite:!0,extensions:{derivatives:!0}});super(t,g,v,e),this.lineLabel=i,this.isAddState=l,this.line3=new h.Line3(new h.Vector3,new h.Vector3),this.widthCache=.1,this.updateMaterial=()=>{super.updateMaterial(),this.targetColorScheme!==p.highlightState||this.isAddState()||(this.targetColorScheme=p.baseState);const t=this.material.uniforms;this.prevColorState.innerColor.copy(t.color.value),this.prevColorState.outerColor.copy(t.outlineColor.value),this.prevColorState.opacity=t.opacity.value;const e=this.selectState.active||this.highlightState.active,i=this.hoverState.active;this.renderOrder=e||i?d.o.HIGHLIGHTED_EDGE:d.o.EDGE,this.animation.modifyAnimation(0,1,c.rP,r.hl),this.lineLabel.setVisible(this.selectState.active||this.highlightState.active||this.hoverState.active)},this.raycast=(()=>{const t=new h.Vector3,e=new h.Vector2,i=new h.Vector2;return(s,n)=>{const a=this.line3.closestPointToPoint(s.ray.origin,!0,t);e.set(a.x,a.z),i.set(s.ray.origin.x,s.ray.origin.z);const r=e.distanceTo(i),d=s.ray.origin.distanceTo(this.line3.start),l=(0,o._U)(d,this.cameraData.pose.projection.asThreeMatrix4(),this.cameraData.width),u=c.Nh*l;r<.5*Math.max(this.widthCache,c.ZT)+u&&n.push({distance:d,object:this,point:a.clone(),face:{a:-1,b:-1,c:-1,materialIndex:-1,normal:new h.Vector3(0,1,0)}})}})(),this.onEdgePositionChanged=(()=>{const t=new h.Vector3,e=new h.Vector3,i=new h.Vector3,s=new h.Vector3,a=new h.Vector3,o=new h.Vector3,r=new h.Vector3;return(h,d,l)=>{const c=h.getWall(this.roomBoundsId);c.from.getVec3(t),c.to.getVec3(e);const u=this.geometry.getAttribute("position");c.getBiasAdjustmentVec(r),i.set(t.x,t.y+d,t.z),s.set(e.x,e.y+d,e.z),a.addVectors(i,r),o.addVectors(s,r),this.line3.start.copy(a),this.line3.end.copy(o),this.material.uniforms.lineStart.value.copy(a),this.material.uniforms.lineEnd.value.copy(o),this.material.uniforms.width.value=c.width,this.stencilMat.uniforms.lineStart.value.copy(a),this.stencilMat.uniforms.lineEnd.value.copy(o),this.stencilMat.uniforms.width.value=c.width,this.widthCache=c.width,this.lineLabel.updateDimensions(a,o,c.width,l),u.setXYZ(0,i.x,i.y,i.z),u.setXYZ(3,s.x,s.y,s.z);const{fromLeft:p,fromRight:m,toLeft:g,toRight:v}=(0,n.b)(c,h);u.setXYZ(1,m.primary.x,m.primary.y+d,m.primary.z),u.setXYZ(2,v.primary.x,v.primary.y+d,v.primary.z),u.setXYZ(4,g.primary.x,g.primary.y+d,g.primary.z),u.setXYZ(5,p.primary.x,p.primary.y+d,p.primary.z),u.setXYZ(9,m.bevel.x,m.bevel.y+d,m.bevel.z),u.setXYZ(6,v.bevel.x,v.bevel.y+d,v.bevel.z),u.setXYZ(7,g.bevel.x,g.bevel.y+d,g.bevel.z),u.setXYZ(8,p.bevel.x,p.bevel.y+d,p.bevel.z),u.needsUpdate=!0,this.geometry.computeBoundingBox(),this.geometry.computeBoundingSphere()}})(),this.renderOrder=d.o.EDGE,this.geometry.computeBoundingBox(),this.geometry.computeBoundingSphere(),this.colors=p,this.material.uniforms.selectedWidth.value=.03;const f=v.clone();f.stencilRef=65535,f.stencilFuncMask=1<<d.$.OPENINGS,f.stencilWriteMask=1<<d.$.EDGE,f.stencilFail=h.KeepStencilOp,f.stencilZFail=h.KeepStencilOp,f.stencilZPass=h.ReplaceStencilOp,f.stencilFunc=h.AlwaysStencilFunc,f.stencilWrite=!0,f.colorWrite=!1,this.stencilMat=f;const y=new h.Mesh(this.geometry,this.stencilMat);y.renderOrder=d.o.EDGE_STENCIL,this.stencilMesh=y;const w=s.I.WHITE.clone();this.animation.onChanged((()=>{const t=this.prevColorState,e=this.targetColorScheme;this.material.uniforms.outlineColor.value.lerpColors(t.outerColor,e.outerColor,this.animation.value);this.material.uniforms.color.value.lerpColors(t.innerColor,e.innerColor,this.animation.value);const i=(0,a.t)(t.opacity,e.opacity,this.animation.value);this.opacity=i,w.copy(s.I.WHITE).multiplyScalar(this.opacity)})),this.onBeforeRender=()=>{this.material.depthTest=1===this.pitchFactor,this.material.uniforms.opacity.value=(1-this.pitchFactor)*this.opacity,this.lineLabel.update()}}tickAnimations(t){super.tickAnimations(t),this.lineLabel.tickAnimations(t)}setLabelVisible(t){this.lineLabel.setVisible(t)}dispose(){super.dispose(),this.lineLabel.dispose()}}const g={baseState:{opacity:1,innerColor:s.I.MIRROR,outerColor:s.I.PORTAL},hoverState:{opacity:1,innerColor:s.I.MIRROR,outerColor:s.I.PORTAL},selectState:{opacity:1,innerColor:s.I.MP_BRAND,outerColor:s.I.WHITE},dimState:{opacity:1,innerColor:s.I.MIRROR,outerColor:s.I.PORTAL}};class v extends h.RawShaderMaterial{constructor(){super({fragmentShader:u.pr.fragmentShader,vertexShader:u.pr.vertexShader,uniforms:h.UniformsUtils.clone(u.pr.uniforms),name:"HandleMaterial",transparent:!0,depthTest:!1,extensions:{derivatives:!0}})}}class f extends l.H{constructor(t,e){const i=new Float32Array([-1,0,-1,1,0,-1,1,0,1,-1,0,1]),s=new h.BufferGeometry;s.setAttribute("position",new h.Float32BufferAttribute(i,3)),s.setIndex([0,2,1,0,3,2]),super(t,s,new v,e),this.targetRadius=.3,this.prevRadius=.3,this.renderOrder=d.o.NODE,this.colors=g,this.animation.onChanged((()=>this.onAnimationChange())),this.onBeforeRender=()=>{this.material.uniforms.opacity.value=this.opacity*(1-this.pitchFactor)}}onAnimationChange(){const t=this.prevColorState,e=this.targetColorScheme;this.material.uniforms.outlineColor.value.lerpColors(t.outerColor,e.outerColor,this.animation.value),this.material.uniforms.baseColor.value.lerpColors(t.innerColor,e.innerColor,this.animation.value),this.opacity=(0,a.t)(t.opacity,e.opacity,this.animation.value);const i=(0,a.t)(this.prevRadius,this.targetRadius,this.animation.value);this.setRadius(i)}setPitchFactor(t){super.setPitchFactor(t),this.visible=this.pitchFactor<1}raycast(t,e){const i=[];if(super.raycast(t,i),i.length>0){const s=i[0].point,n=t.ray.origin.distanceTo(this.position),a=(0,o._U)(n,this.cameraData.pose.projection.asThreeMatrix4(),this.cameraData.width),r=c.Nh*a;s.distanceTo(this.position)<c.pp+r&&e.push(i[0])}}updateMaterial(){this.prevRadius=this.material.uniforms.radius.value,this.targetRadius=c.pp+(this.hoverState.active?c.XG:0)+(this.selectState.active?c.XG:0),super.updateMaterial(),this.prevColorState.innerColor.copy(this.material.uniforms.baseColor.value),this.prevColorState.outerColor.copy(this.material.uniforms.outlineColor.value),this.prevColorState.opacity=this.material.opacity,this.animation.modifyAnimation(0,1,c.rP,r.hl)}setRadius(t){this.material.uniforms.radius.value=t}updatePosition(t){this.position.copy(t)}}var y=i(80978);class w extends h.RawShaderMaterial{constructor(){super({fragmentShader:u.z6.fragmentShader,vertexShader:u.z6.vertexShader,uniforms:h.UniformsUtils.clone(u.z6.uniforms),name:"OpeningMaterial",depthTest:!1,transparent:!0,stencilFunc:h.AlwaysStencilFunc,stencilWrite:!1})}}const b=new h.BoxGeometry(1,1,1);class S extends l.H{constructor(t,e,i){super(t,b.clone(),new w,i),this.floorId=e,this.onEdgePositionChanged=(()=>{const t=new h.Vector3;return(e,i,s,n)=>{t.subVectors(i,e),this.scale.set(t.length(),.05,s),this.stencilPrepass&&this.stencilPrepass.scale.set(t.length(),.05,s);const a=Math.atan2(i.x-e.x,i.z-e.z)+Math.PI/2;this.rotation.y=a,this.stencilPrepass&&(this.stencilPrepass.rotation.y=a);const o=t.addVectors(i,e).multiplyScalar(.5);this.position.copy(o),this.stencilPrepass&&this.stencilPrepass.position.copy(o),this.startHandle.position.copy(e),this.endHandle.position.copy(i);const r=n===y.u.DOOR?1:0;this.material.uniforms.isDoor.value=r,this.stencilPrepass&&(this.stencilPrepass.material.uniforms.isDoor.value=r)}})(),this.renderOrder=d.o.OPENING_LINES,this.startHandle=new T(t,i,this),this.endHandle=new M(t,i,this),this.animation.onChanged((()=>{this.material.uniforms.baseColor.value.lerpColors(s.I.WHITE,s.I.NEPTUNE,this.animation.value)}))}setPitchFactor(t){super.setPitchFactor(t),this.visible=this.pitchFactor<1,this.startHandle.setPitchFactor(t),this.endHandle.setPitchFactor(t),this.stencilPrepass&&this.stencilPrepass.setPitchFactor(t)}updateMaterial(){const t=this.selectState.active||this.hoverState.active||this.highlightState.active;this.animation.modifyAnimation(this.animation.value,t?1:0,c.rP,r.hl)}tickAnimations(t){super.tickAnimations(t),this.startHandle.tickAnimations(t),this.endHandle.tickAnimations(t)}}class D extends S{constructor(t,e,i){super(t,e,i),this.floorId=e,this.stencilPrepass=new I(t,e,i)}}class I extends S{constructor(t,e,i){super(t,e,i),this.floorId=e,this.renderOrder=d.o.OPENING_STENCIL,this.material.colorWrite=!1,this.material.stencilRef=65535,this.material.stencilFuncMask=1<<d.$.OPENINGS,this.material.stencilWriteMask=1<<d.$.OPENINGS,this.material.stencilFail=h.ReplaceStencilOp,this.material.stencilZFail=h.ReplaceStencilOp,this.material.stencilZPass=h.ReplaceStencilOp,this.material.stencilFunc=h.AlwaysStencilFunc,this.material.stencilWrite=!0}raycast(t,e){}}class P extends f{constructor(t,e,i){super(t,e),this.parentOpeningView=i}}class T extends P{}class M extends P{}var E=i(26158),x=i(69739),C=i(56159),R=i(46391);class A extends f{}var L=i(89590),O=i(82582),k=i.n(O),F=i(67971),N=i(37519),V=i(12529),B=i(90304),G=i(59370),_=i(27990),H=i(72119),U=i(89557);class W{constructor(){this.corner=[new h.Vector2,new h.Vector2,new h.Vector2,new h.Vector2],this.axis=[new h.Vector2,new h.Vector2],this.origin=[0,0],this.aabb=new h.Box2}set(t){if(4!==t.length)throw new Error("Obb needs four points!");for(let e=0;e<t.length;e++)this.corner[e].copy(t[e]);this.aabb.setFromPoints(this.corner),this.axis[0].subVectors(this.corner[1],this.corner[0]),this.axis[1].subVectors(this.corner[3],this.corner[0]);for(let t=0;t<2;t++)this.axis[t].divideScalar(this.axis[t].lengthSq()),this.origin[t]=this.corner[0].dot(this.axis[t])}overlaps(t){return this.overlaps1Way(t)&&t.overlaps1Way(this)}overlaps1Way(t){for(let e=0;e<2;e++){let i=t.corner[0].dot(this.axis[e]),s=i,n=i;for(let a=1;a<4;a++)i=t.corner[a].dot(this.axis[e]),s=Math.min(s,i),n=Math.max(n,i);if(s>1+this.origin[e]||n<this.origin[e])return!1}return!0}}var j,z=i(62944),$=i(28941);class q{constructor(t,e,i,s,n,a){this.cameraData=i,this.getCurrFloorId=s,this.isMultiFloor=n,this.debugContainer=a,this.labels=new Set,this.tree=new(k()),this.roomLabelMaker=new F.uc({assetBasePath:t,lang:e,color:"white",outline:!0,background:!1,backgroundColor:"#000000",backgroundColliderType:K,disableDepth:!0}),this.wallLabelMaker=new F.uc({assetBasePath:t,color:"black",lang:e,background:!0,backgroundColor:"#ffffff",backgroundColliderType:X,disableDepth:!0})}createRoomLabel(t,e){const i=new Y(j.ROOM,t,this.roomLabelMaker.createLabel());return i.label.collider.userData={roomId:e},this.labels.add(i),i}createWallLabel(t){const e=new Z(j.WALL,t,this.wallLabelMaker.createLabel(),this.cameraData);return this.labels.add(e),e}createPerimeterLabel(t){const e=new Z(j.PERIMETER,t,this.wallLabelMaker.createLabel(),this.cameraData);return this.labels.add(e),this.update(),e}deleteLabel(t){t.label.parent&&t.removeFrom(t.label.parent),this.labels.delete(t),t.dispose()}tickAnimations(t){for(const e of this.labels)e.tickAnimations(t)}update(){const t=new h.Vector2,e=new h.Vector3,i=new h.Vector3,s=[{x:-1,y:-1,screenPos:new h.Vector2},{x:1,y:-1,screenPos:new h.Vector2},{x:1,y:1,screenPos:new h.Vector2},{x:-1,y:1,screenPos:new h.Vector2}],n=[];this.debugContainer&&this.debugContainer.clear();const a=(0,o.Pp)(this.cameraData.pose.projection);for(const t of this.labels)t.update();const r=this.getCurrFloorId(),d=Array.from(this.labels).filter((t=>{const e=!this.isMultiFloor||r===t.floorId;return t.labelVisible&&t.fitsWall&&e})).map((t=>{const e=t.label;return{label:t,cameraDistance:a?Math.abs(e.position.y-this.cameraData.pose.position.y):this.cameraData.pose.position.distanceTo(e.position)}})).sort(((t,e)=>{const i=t.label,s=e.label,n=Number(!i.getDisplayingTooltip())-Number(!s.getDisplayingTooltip());if(0!==n)return n;const a=i.type-s.type;if(0!==a)return a;const o=Number(i.getDimmed())-Number(s.getDimmed());if(0!==o)return o;const r=t.cameraDistance-e.cameraDistance;if(r<0)return r;const h=s.length-i.length;return 0!==h?h:i.label.id-s.label.id})).map((a=>{const{label:r,cameraDistance:d}=a,l=r.label,u=(0,o._U)(d,this.cameraData.pose.projection.asThreeMatrix4(),this.cameraData.width)*c.bk;(0,G.q9)(this.cameraData,l.position,t,e);const{width:p,height:m}=l.getUnscaledSize();this.debugContainer&&(n.length=0),l.updateMatrixWorld();for(const t of s)i.set(p*t.x*.5+u*t.x,m*t.y*.5+(u+t.y),0),i.applyMatrix4(l.matrixWorld),this.debugContainer&&n.push(i.clone()),(0,G.q9)(this.cameraData,i,t.screenPos);if(this.debugContainer){const t=new h.Mesh(new h.ShapeGeometry(new h.Shape(n.map((t=>new h.Vector2(t.x,t.z))))).rotateX(Math.PI/2),new h.MeshBasicMaterial({color:"red",depthTest:!1,side:h.DoubleSide}));this.debugContainer.add(t)}const g=new W;g.set(s.map((t=>t.screenPos)));const v=g.aabb;return{label:r,oob:g,minX:v.min.x,minY:v.min.y,maxX:v.max.x,maxY:v.max.y,distance:d}})),l=new Set;this.tree.clear();for(const t of d){const e=this.tree.search(t).find((e=>e.oob.overlaps(t.oob))),i=t.label.labelGroupId,s=i&&l.has(i),n=!(e||s&&i);t.label.setCollides(!n),n&&(this.tree.insert(t),i&&l.add(i))}}}!function(t){t[t.ROOM=0]="ROOM",t[t.WALL=1]="WALL",t[t.PERIMETER=2]="PERIMETER"}(j||(j={}));class Y{constructor(t,e,i){this.type=t,this.floorId=e,this.label=i,this.length=0,this.labelGroupId=null,this.labelOpacityAnimation=new U.z(1),this.labelVisible=!1,this.collides=!1,this.dimmed=!1,this.displayingTooltip=!1,this.fitsWall=!0,this.label.setRenderOrder(V.z.labels),this.updateOpacityTarget(0)}addTo(t){return t.add(this.label),this}removeFrom(t){t.remove(this.label)}dispose(){this.label.dispose()}tickAnimations(t){this.labelOpacityAnimation.tick(t)}setVisible(t){t!==this.labelVisible&&(this.labelVisible=t,this.updateOpacityTarget())}setCollides(t){t!==this.collides&&(this.collides=t,this.updateOpacityTarget(t?0:c.rP))}setFitsWall(t){t!==this.fitsWall&&(this.fitsWall=t,this.updateOpacityTarget())}setDimmed(t){t!==this.dimmed&&(this.dimmed=t,this.updateOpacityTarget())}getDimmed(){return this.dimmed}setDisplayingTooltip(t){this.displayingTooltip=t,this.updateOpacityTarget()}getDisplayingTooltip(){return this.displayingTooltip}update(){this.updateOpacity()}getSize(){const t=this.label.text.split("\n"),e=t.reduce(((t,e)=>Math.max(t,e.length)),0),i=(0,_.hJ)(e,0,0);return{width:i.width,height:i.height*t.length}}updateOpacity(){this.label.opacity=this.labelOpacityAnimation.value}updateOpacityTarget(t=c.rP){const e=this.dimmed&&!this.displayingTooltip?.5:1,i=this.labelVisible&&!this.collides&&this.fitsWall?e:0;this.labelOpacityAnimation.modifyAnimation(t>0?this.labelOpacityAnimation.value:i,i,t)}}class Z extends Y{constructor(t,e,i,s){super(t,e,i),this.cameraData=s,this.start=new h.Vector3,this.end=new h.Vector3,this.width=.1,this.getLinePositions=(()=>{const t=new h.Vector3,e=new h.Vector3,i=new h.Vector3,s=new h.Vector3,n=new h.Vector3;return()=>{t.copy(this.start),e.copy(this.end);const a=(0,_.Ko)(t,e,this.cameraData),r=(0,_.hJ)(this.label.text.length),h=a.pixelDistance>r.width&&this.labelVisible;this.setFitsWall(h);const d=this.cameraData.isOrtho(),l=d?Math.abs(this.label.position.y-this.cameraData.pose.position.y):this.cameraData.pose.position.distanceTo(this.label.position);i.addVectors(t,e).multiplyScalar(.5),d?(n.subVectors(e,t),n.applyAxisAngle(B.fU.UP,.5*Math.PI).normalize()):n.copy(B.fU.UP);const c=(0,o._U)(l,this.cameraData.pose.projection.asThreeMatrix4(),this.cameraData.width),u=d?4:10;return s.copy(i),s.addScaledVector(n,.5*this.width+.75*this.label.scale.x+c*u),{start:t,end:e,center:i,labelPosition:s,lineRotation:a.rotation}}})()}updateDimensions(t,e,i,s){this.start.copy(t),this.end.copy(e),this.width=i;const n=this.start.distanceTo(this.end);this.length=n;const a=(0,z.up)(n,s);this.label.text=a}update(){super.update(),this.updateBillboard()}updateBillboard(){const{labelPosition:t,lineRotation:e}=this.getLinePositions(),{label:i}=this;i.setPosition(t),i.setOrientation(this.cameraData.pose.rotation,e);const{position:s,projection:n}=this.cameraData.pose,a=this.cameraData.aspect(),{height:r}=this.cameraData,h=this.cameraData.zoom(),d=(0,o.mY)(n,s,i.position,r,H.Hn.SCALE),l=(0,o.Pp)(n)?0:((0,N.uZ)(a,1,2.5)+h)*H.Hn.SCALE_ASPECT,c=1+H.Hn.SCALE_NDC-l,u=Math.max(Math.min(1/d*c,3),.001);i.scaleFactor=u}}class X extends h.Mesh{raycast(t,e){}}class K extends h.Mesh{constructor(){super(...arguments),this.intersectionPriority=$.e.Labels}}var Q=i(72996),J=i(23612),tt=i(86819),et=i(66777),it=i(31362),st=i(38399);class nt{constructor(t,e,i,s,n,a,o,r,d,c,u){this.readonly=t,this.data=e,this.scene=i,this.input=s,this.viewListener=n,this.isAdding=a,this.floorsViewData=o,this.cameraData=r,this.locale=d,this.settingsData=u,this.currentFloorId="",this.cameraDirty=!1,this.rootObjects={},this.viewMap=new Map,this.nodeMap=new Map,this.roomMap=new Map,this.openingMap=new Map,this.visibleWallLabels=new Set,this.hideCurrentFloorViews=()=>{if(this.currentFloorId){const t=this.rootObjects[this.currentFloorId];for(const e of t.children)e instanceof l.H&&this.unregisterViewToInput(e);this.scene.remove(t)}},this.showCurrentFloorViews=()=>{var t;const e=(null===(t=this.floorsViewData.singleFloor)||void 0===t?void 0:t.id)||this.floorsViewData.currentFloorId;if(e){const t=this.rootObjects[e];this.scene.add(t);for(const e of t.children)e instanceof l.H&&this.registerViewToInput(e);this.currentFloorId=e}},this.updateOpeningView=(()=>{const t=new h.Vector3,e=new h.Vector3,i=new h.Vector3,s=new h.Vector3,n=new h.Vector3,a=new h.Vector3;return o=>{const r=this.data.getWall(o.wallId),h=this.baseHeightForFloor(r.floorId);a.set(0,h,0),r.getBiasAdjustmentVec(n),r.from.getVec3(i).add(n).add(a),r.to.getVec3(s).add(n).add(a),e.subVectors(s,i).normalize(),t.copy(i),t.lerp(s,o.relativePos),i.copy(t).addScaledVector(e,-.5*o.width),s.copy(t).addScaledVector(e,.5*o.width);const d=this.openingMap.get(o.id);if(!d)throw new Error("Unable to find view for opening while updating");d.onEdgePositionChanged(i,s,r.width,o.type)}})(),this.onMoveEnd=()=>{for(const t of this.data.rooms.values())this.updateRoomView(t,!0)},this.floorsViewData.floors.iterate((t=>{this.rootObjects[t.id]=new h.Object3D})),this.bindings=[this.data.onWallsChanged({onRemoved:t=>this.removeViewForWall(t),onUpdated:t=>this.updateWallView(t),onChildUpdated:t=>this.updateWallView(t),onAdded:t=>this.makeWallView(t)}),this.data.onNodesChanged({onRemoved:t=>this.removeViewForNode(t),onUpdated:t=>this.updateNodeView(t),onChildUpdated:t=>this.updateNodeView(t),onAdded:t=>this.makeNodeView(t)}),this.data.onRoomsChanged({onRemoved:t=>this.removeViewForRoom(t),onUpdated:t=>this.updateRoomView(t,!1),onChildUpdated:t=>this.updateRoomView(t,!1),onAdded:t=>this.makeRoomView(t)}),this.data.onOpeningsChanged({onRemoved:t=>this.removeViewForOpening(t),onUpdated:t=>this.updateOpeningView(t),onChildUpdated:t=>this.updateOpeningView(t),onAdded:t=>this.makeOpeningView(t)}),this.data.afterFinalize(this.onMoveEnd),c.subscribe(C.S,this.hideCurrentFloorViews),c.subscribe(x.P,this.showCurrentFloorViews),r.onChanged((()=>this.cameraDirty=!0)),this.settingsData.onPropertyChanged(E.F.UnitType,(t=>{for(const t of this.data.rooms.values())this.updateRoomView(t,!0);for(const t of this.data.walls.values())this.updateWallView(t)}))],(0,it.Jm)()||this.bindings.push(this.input.registerMeshHandler(J.z,Q.s.isInstanceOf(K),((t,e)=>this.roomLabelHover(e,!0))),this.input.registerMeshHandler(J.A,Q.s.isInstanceOf(K),((t,e)=>this.roomLabelHover(e,!1)))),this.settingsData.tryGetProperty(et.eC,!1)&&this.bindings.push(this.input.registerMeshHandler(tt.Rd,Q.s.isInstanceOf(K),((t,e)=>this.roomLabelClick(e)))),this.bindings.forEach((t=>t.cancel())),this.labelManager=new q(u.tryGetProperty("assetBasePath",""),d.languageCode,r,(()=>o.currentFloorId),this.floorsViewData.isMultifloor())}init(){}render(t){this.labelManager.tickAnimations(t);const e=this.cameraData.pose.pitchFactor();for(const i of[this.viewMap,this.nodeMap,this.roomMap,this.openingMap])for(const s of i.values())s.setPitchFactor(e),s.tickAnimations(t)}dispose(){}beforeRender(){this.labelManager.update(),this.cameraDirty&&(this.cameraDirty=!1,this.updateLabels())}activate(t){if(!this.readonly)for(const[t,e]of this.data.nodes)this.makeNodeView(e);for(const[t,e]of this.data.walls)this.makeWallView(e);for(const[t,e]of this.data.rooms)this.makeRoomView(e);for(const[t,e]of this.data.wallOpenings)this.makeOpeningView(e);this.bindings.forEach((t=>t.renew())),this.showCurrentFloorViews()}deactivate(){this.bindings.forEach((t=>t.cancel()));for(const t in this.rootObjects){const e=this.rootObjects[t];this.scene.remove(e);for(const t of e.children.slice())t instanceof l.H&&(t.dispose(),this.unregisterViewToInput(t)),e.remove(t)}}updateLabels(){for(const t of this.roomMap.values())t.updateLabelBillboard()}registerViewToInput(...t){var e;for(const i of t)(null===(e=i.parent)||void 0===e?void 0:e.parent)&&(this.input.registerMesh(i,!1),this.viewListener.addView(i,i.roomBoundsId))}unregisterViewToInput(...t){for(const e of t)this.input.unregisterMesh(e),this.viewListener.removeView(e.roomBoundsId)}makeNodeView(t){const e=new A(t.id,this.cameraData),i=this.baseHeightForFloor(t.floorId);e.updatePosition(new h.Vector3(t.x,i,t.z)),this.nodeMap.set(t.id,e),this.rootObjects[t.floorId].add(e),this.registerViewToInput(e)}updateNodeView(t){const e=this.nodeMap.get(t.id);if(e){const i=this.baseHeightForFloor(t.floorId);e.updatePosition(new h.Vector3(t.x,i,t.z))}}removeViewForNode(t){const e=this.nodeMap.get(t.id);e&&(this.rootObjects[t.floorId].remove(e),this.nodeMap.delete(t.id),e.dispose(),this.unregisterViewToInput(e))}makeWallView(t){const e=this.labelManager.createWallLabel(t.floorId);e.updateDimensions(t.from.getVec3(),t.to.getVec3(),t.width,this.getUnits());const i=new m(t.id,this.cameraData,e,this.isAdding);this.viewMap.set(t.id,i),this.rootObjects[t.floorId].add(i,i.stencilMesh),e.addTo(this.rootObjects[t.floorId]),this.updateWallView(t),e.update(),this.registerViewToInput(i)}updateWallView(t){const e=this.viewMap.get(t.id);if(e){const i=this.baseHeightForFloor(t.floorId);e.onEdgePositionChanged(this.data,i,this.getUnits())}}removeViewForWall(t){const e=this.viewMap.get(t.id);e&&(this.rootObjects[t.floorId].remove(e,e.stencilMesh),this.labelManager.deleteLabel(e.lineLabel),this.viewMap.delete(t.id),this.visibleWallLabels.delete(e),e.dispose(),this.unregisterViewToInput(e))}makeRoomView(t){const e=this.rootObjects[t.floorId];this.roomMap.has(t.id)&&this.removeViewForRoom(t);const i=this.baseHeightForFloor(t.floorId),s=new L.c(t,i,this.cameraData,this.labelManager,this.getUnits(),!this.readonly||this.settingsData.tryGetProperty(et.eC,!1));e.add(s),this.roomMap.set(t.id,s),this.registerViewToInput(s),this.updateRoomView(t,!0)}updateRoomView(t,e){const i=this.roomMap.get(t.id);if(!i)throw new Error("Unable to find view for room while updating.");const s=this.baseHeightForFloor(t.floorId);i.updateGeo(t,s,this.getUnits()),i.updateLabelBillboard();const n=this.data.getPotentialRoomCanvasLabels(t.id,this.locale.t(st.Z.SHOWCASE.ROOMS.DEFAULT_NAME),this.getUnits());i.updateText(n,e)}removeViewForRoom(t){const e=this.roomMap.get(t.id);if(!e)throw new Error("Unable to find view for room while deleting.");this.rootObjects[t.floorId].remove(e),this.roomMap.delete(t.id),e.dispose(),this.input.unregisterMesh(e),this.viewListener.removeView(t.id)}makeOpeningView(t){const e=this.data.getWall(t.wallId),i=new D(t.id,e.floorId,this.cameraData);this.rootObjects[e.floorId].add(i,i.stencilPrepass,i.startHandle,i.endHandle),this.openingMap.set(t.id,i),this.registerViewToInput(i,i.startHandle,i.endHandle),this.updateOpeningView(t)}removeViewForOpening(t){const e=this.openingMap.get(t.id);if(!e)throw new Error("Unable to find view for opening while deleting.");this.rootObjects[e.floorId].remove(e,e.stencilPrepass,e.startHandle,e.endHandle),this.openingMap.delete(t.id),e.dispose(),this.unregisterViewToInput(e,e.startHandle,e.endHandle)}isPeekabooDollhouse(){return!(0,G.Eb)(this.cameraData.pose.pitchFactor())&&!this.settingsData.tryGetProperty(et.eC,!1)}roomLabelHover(t,e){if(this.isPeekabooDollhouse())return;const i=t.userData.roomId,s=this.roomMap.get(i);s&&s instanceof L.c&&s.hoverRoomLabel(e)}roomLabelClick(t){return!!this.isPeekabooDollhouse()}getUnits(){return this.settingsData.tryGetProperty(E.F.UnitType,R.M.IMPERIAL)}baseHeightForFloor(t){var e;return(null===(e=this.floorsViewData.floors.getFloor(t))||void 0===e?void 0:e.medianSweepFloorHeight())||0}}var at=i(97542),ot=i(77161),rt=i(92810),ht=i(79727),dt=i(9037),lt=i(34029);class ct extends at.Y{constructor(){super(...arguments),this.name="room-bound-renderer"}async init(t,e){this.engine=e,[this.data,this.renderer,this.input,this.floorsViewData,this.cameraData,this.locale,this.settingsData]=await Promise.all([e.market.waitForData(ot.Z),e.getModuleBySymbol(rt.Aj),e.getModuleBySymbol(rt.PZ),e.market.waitForData(ht.c),e.market.waitForData(dt.M),e.getModuleBySymbol(rt.e9),e.market.waitForData(lt.e)])}async dispose(t){this.roomBoundRenderer&&this.engine.removeComponent(this,this.roomBoundRenderer)}startRendering(t,e,i){if(this.roomBoundRenderer)throw new Error("Already rendering!!");this.roomBoundRenderer=new nt(t,this.data,this.renderer.getScene(),this.input,e,i,this.floorsViewData,this.cameraData,this.locale,this.engine,this.settingsData),this.engine.addComponent(this,this.roomBoundRenderer)}stopRendering(){if(!this.roomBoundRenderer)throw new Error("Not rendering!");this.engine.removeComponent(this,this.roomBoundRenderer),this.roomBoundRenderer.dispose(),this.roomBoundRenderer=null}}},82403:(t,e,i)=>{"use strict";i.d(e,{Nh:()=>n,XG:()=>o,ZT:()=>a,bk:()=>h,mU:()=>l,pp:()=>s,qb:()=>d,rP:()=>r});const s=.06,n=7,a=.1,o=.02,r=100,h=8,d=500,l=.9},3297:(t,e,i)=>{"use strict";i.d(e,{AK:()=>C,LD:()=>E,Ud:()=>R,pr:()=>M,z6:()=>x});var s=i(15462),n=i(81396),a=i(67498),o=i.n(a),r=i(73293),h=i.n(r),d=i(40134),l=i.n(d),c=i(93670),u=i.n(c),p=i(86242),m=i.n(p),g=i(14536),v=i.n(g),f=i(47706),y=i.n(f),w=i(72292),b=i.n(w),S=i(25888),D=i.n(S),I=i(73868),P=i.n(I),T=i(82403);const M={uniforms:{outlineColor:{type:"v4",value:s.I.BLACK},baseColor:{type:"v4",value:s.I.SINE},outlinePct:{type:"f",value:.8},radius:{type:"f",value:T.pp},opacity:{type:"f",value:1}},vertexShader:o(),fragmentShader:h()},E={uniforms:{outlineColor:{type:"v3",value:s.I.WHITE},color:{type:"v3",value:s.I.WHITE},lineStart:{type:"v3",value:new n.Vector3},lineEnd:{type:"v3",value:new n.Vector3},width:{type:"f",value:1},selectedWidth:{type:"f",value:.01},opacity:{type:"f",value:1}},vertexShader:l(),fragmentShader:u()},x={uniforms:{baseColor:{type:"v4",value:s.I.WHITE},isDoor:{type:"f",value:1},opacity:{type:"f",value:1}},vertexShader:m(),fragmentShader:v()},C={uniforms:{opacity:{type:"f",value:1},centerSpacing:{type:"f",value:24},radius:{type:"f",value:4},color:{type:"v4",value:s.I.LENS_GRAY}},vertexShader:y(),fragmentShader:b()},R={uniforms:{tip:{type:"v2",value:new n.Vector2},normal:{type:"v2",value:new n.Vector2},height:{type:"f",value:0},color:{type:"v4",value:s.I.WHITE},opacity:{type:"f",value:1},outline:{type:"f",value:1},outlineColor:{type:"v4",value:s.I.BLACK},screenSize:{type:"v2",value:new n.Vector2},paddingPx:{type:"f",value:2},widthPx:{type:"f",value:12},heightPx:{type:"f",value:6},aaPaddingPx:{type:"f",value:2},metersPerPx:{type:"f",value:.01}},vertexShader:D(),fragmentShader:P()}},18544:(t,e,i)=>{"use strict";i.d(e,{e:()=>a});var s=i(67992),n=i(32683);class a extends s.V{constructor(t,e,i){super(),this.data=t,this.editorState=e,this.visibleInShowcase=i,this.name="room-bound-view-data",this.roomBoundsVisible=!1}getSelectedRoom(t){if(!t)return null;const e=this.data.getEntity(t);return e instanceof n.JJ?e:null}setRoomBoundVisible(t){this.roomBoundsVisible=t,this.commit()}}},49653:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>Ht});var s=i(97542),n=i(54244),a=i(10374),o=i(34029),r=i(92810),h=i(59625),d=i(66777),l=i(77161),c=i(47309),u=i(76631),p=i(91380),m=i(50892),g=i(89549),v=i(65019),f=i(23254),y=i(31362),w=i(35895),b=i(89570);class S{constructor(t){this.map=new Map,t.forEach((t=>this.map.set(t.state,t.subs)))}renew(t){(this.map.get(t)||[]).forEach((t=>t.renew()))}cancel(t){if(t){(this.map.get(t)||[]).forEach((t=>t.cancel()))}else this.map.forEach((t=>t.forEach((t=>t.cancel()))))}update(t,e){this.map.set(t,e)}}var D=i(86819),I=i(23612),P=i(32683),T=i(89590),M=i(96154),E=i(72996),x=i(75287),C=i(2224),R=i(97998),A=i(37082),L=i(13693),O=i(98010);const k=new R.Z("editor");var F;!function(t){let e,i;t.DragAndDrop=class{constructor(t=new s){this.state=t,this.events=new L.Y,this.bindings=[],this.validator=null,this.state.commit(),this.bindings.push(this.state.onPropertyChanged("toolState",(()=>{k.debug("toolState:",{from:this.state.previousToolState,to:this.state.toolState},this.state),this.events.broadcast(new i.StateChange(this.state.toolState,this.state.previousToolState))})),this.onSelectedChanged(((t,e)=>{this.events.broadcast(new i.SelectChange(t,e))})),this.onHoveredChanged(((t,e)=>{this.events.broadcast(new i.HoverChange(t,e))})),this.state.onCurrentIdChanged(((t,e)=>{this.events.broadcast(new i.CurrentChange(t,e))}))),this.bindings.forEach((t=>t.cancel()))}subscribe(t,e){return this.events.subscribe(t,e)}setState(t){t!==this.state.toolState&&(this.state.previousToolState=this.state.toolState,this.state.toolState=t,this.state.commit())}start(){const{toolState:t}=this.state;if(t===e.CLOSED)this.bindings.forEach((t=>t.renew()));else if(t!==e.READ_ONLY)throw new n("Editor already started");this.setState(e.IDLE),this.events.broadcast(new i.Start)}startReadOnly(){const{toolState:t}=this.state;if(t===e.CLOSED)this.bindings.forEach((t=>t.renew()));else if(t!==e.IDLE)throw new n("Editor already started");this.setState(e.READ_ONLY)}setEnabled(t){if(this.state.toolState===e.CLOSED)throw new n("Editor not started");if(t)this.state.toolState===e.DISABLED&&this.setState(e.IDLE);else switch(this.state.toolState){case e.DISABLED:break;case e.IDLE:this.setState(e.DISABLED);break;default:this.unhover({commit:!1}),this.deselect({commit:!1}),this.discard(),this.setState(e.DISABLED)}}exit(){this.state.toolState!==e.CLOSED&&(this.state.toolState!==e.DISABLED&&(this.unhover({commit:!1}),this.deselect({commit:!1}),this.discard()),this.setState(e.CLOSED),this.bindings.forEach((t=>t.cancel())),this.events.broadcast(new i.Exit))}setValidator(t){this.validator=t}onDiscard(t){this.state.onBeforeDiscard=t||null}edit(t){if(this.assertActive(),null!==this.state.pendingEdit)throw k.error(`Trying to edit asset ${t}, but currently editing ${this.state.pendingEdit}`),new n("Edit in progress");this.state.pendingEdit=t,this.setState(e.EDITING),this.events.broadcast(new i.EditStart(t,!1))}waitForAdd(){if(this.assertActive(),null!==this.state.pendingAdd)throw k.error(`Entering wait for add state, but already adding ${this.state.pendingAdd}`),new n("Add in progress");null!==this.state.selected&&(k.debug(`Entering the wait for add state, deselecting previous ${this.state.selected}`),this.deselect({commit:!1})),this.events.broadcast(new i.WaitForAddStart),this.setState(e.WAIT_FOR_ADD)}add(t){if(this.assertActive(),null!==this.state.pendingAdd)throw k.error(`Trying to add new asset ${t}, but already adding ${this.state.pendingAdd}`),new n("Add in progress");null!==this.state.selected&&(k.debug(`Add ${t} called, deselecting previous ${this.state.selected}`),this.deselect({commit:!1})),this.state.pendingAdd=t,this.events.broadcast(new i.AddStart(t)),this.setState(e.ADDING)}place(t){this.assertActive();const{pendingAdd:s,pendingEdit:n}=this.state,a=s||n;a&&t===a||(this.state.pendingEdit=t),this.setState(e.PLACING),this.events.broadcast(new i.EditStart(t,!0))}move(t){this.assertActive(),this.validate(t);const{pendingAdd:e,pendingEdit:s,selected:n}=this.state,a=e||s||n;a&&this.events.broadcast(new i.Move(a,t)),this.state.ndcPosition.value=t.clientPosition}highlight(...t){this.assertActive();for(const e of t)this.state.highlighted.add(e),this.events.broadcast(new i.HighlightAdd(e))}clearHighlight(...t){this.assertActive();for(const e of t)this.state.highlighted.delete(e),this.events.broadcast(new i.HighlightClear(e))}clearAllHighlights(){this.assertActive();for(const t of this.state.highlighted)this.events.broadcast(new i.HighlightClear(t));this.state.highlighted.clear()}dim(t){this.assertActive(),this.state.dimmed.add(t),this.events.broadcast(new i.DimAdd(t))}clearDim(t){this.assertActive(),this.state.dimmed.delete(t),this.events.broadcast(new i.DimClear(t))}clearAllDims(){this.assertActive();for(const t of this.state.dimmed)this.events.broadcast(new i.DimClear(t));this.state.dimmed.clear()}select(t,i){this.assertActive(),this.state.pendingAdd||this.state.pendingEdit||(this.state.previousSelected=this.state.selected,this.state.selected=t,this.state.commit(),i&&i.transitionTo?this.setState(i.transitionTo):this.state.isReadonly()?this.setState(e.SELECTED_READ_ONLY):this.setState(e.SELECTED))}toggleSelect(t){this.state.selected===t?this.deselect():this.select(t)}deselect(t={commit:!0}){this.assertActive(),this.state.pendingAdd||this.state.pendingEdit||null!==this.state.selected&&(this.state.previousSelected=this.state.selected,this.state.selected=null,t.commit&&this.state.commit(),t&&t.transitionTo?this.setState(t.transitionTo):this.state.toolState===e.SELECTED_READ_ONLY?this.setState(e.READ_ONLY):this.setState(e.IDLE))}hover(t){this.assertActive(),t!==this.state.hovered&&(this.state.previousHovered=this.state.hovered,this.state.hovered=t,this.state.commit())}unhover(t={commit:!0}){this.state.hovered&&(this.state.previousHovered=this.state.hovered,this.state.hovered=null,t.commit&&this.state.commit())}tryCommit(){this.assertActive();const t=this.state.pendingAdd,e=this.state.pendingEdit;if(!1===this.state.pendingValid)return k.warn("nop, pendingValid",this.state.pendingValid),!1;let s=!1;const n=this.state.selected,a=this.state.pendingAdd||this.state.pendingEdit;if(a){const o=null===this.state.pendingValid||!0===this.state.pendingValid;o&&(this.state.pendingAdd=null,this.state.pendingEdit=null,this.state.pendingValid=null,this.state.onBeforeDiscard=null,e&&this.events.broadcast(new i.EditConfirm(e)),t&&this.events.broadcast(new i.AddConfirm(t)),this.state.pendingAdd||this.state.pendingEdit||n!==this.state.selected||this.select(a)),s=o}return s}discard(){const s=this.state.pendingAdd||this.state.pendingEdit,n=this.state.pendingAdd,a=this.state.pendingEdit;s&&(this.state.onBeforeDiscard&&this.state.onBeforeDiscard(),a&&(this.state.pendingEdit=null,this.events.broadcast(new i.EditDiscard(a))),n&&(this.state.pendingAdd=null,this.events.broadcast(new i.AddDiscard(n))),this.events.broadcast(new t.Events.ValidChange(null,null)),this.state.pendingValid=null,this.state.onBeforeDiscard=null),this.unhover({commit:!1}),this.deselect({commit:!1}),this.state.isReadonly()?this.setState(e.READ_ONLY):this.state.toolState!==e.IDLE&&this.setState(e.IDLE),this.state.commit()}validate(e){const i=this.state.pendingAdd||this.state.pendingEdit||this.state.selected;if(i){const s=this.state.pendingValid;let n=!0;this.validator?(n=this.validator.validate(i,e),this.state.pendingValid=n):this.state.pendingValid=null,null!==this.state.pendingValid&&s!==this.state.pendingValid&&this.events.broadcast(new t.Events.ValidChange(n?i:null,n?null:i))}}assertActive(){if(this.state.toolState===t.ToolState.CLOSED)throw new n("Editor closed");if(this.state.toolState===t.ToolState.DISABLED)throw new n("Editor disabled")}onSelectedChanged(t){return this.state.onPropertyChanged("selected",(()=>t(this.state.selected,this.state.previousSelected)))}onHoveredChanged(t){return this.state.onPropertyChanged("hovered",(()=>t(this.state.hovered,this.state.previousHovered)))}},function(t){t.CLOSED="CLOSED",t.READ_ONLY="READ_ONLY",t.DISABLED="DISABLED",t.IDLE="IDLE",t.SELECTED="SELECTED",t.SELECTED_READ_ONLY="SELECTED_READ_ONLY",t.WAIT_FOR_ADD="WAIT_FOR_ADD",t.ADDING="ADDING",t.EDITING="EDITING",t.PLACING="PLACING"}(e=t.ToolState||(t.ToolState={}));class s extends x.T{constructor(){super(...arguments),this.toolState=e.CLOSED,this.previousToolState=e.CLOSED,this.pendingAdd=null,this.pendingEdit=null,this.selected=null,this.previousSelected=null,this.hovered=null,this.previousHovered=null,this.highlighted=new Set,this.dimmed=new Set,this.ndcPosition=new A.f(null),this.onBeforeDiscard=null,this.pendingValid=null}get currentId(){return this.pendingAdd||this.pendingEdit||this.selected||null}onCurrentIdChanged(t,e=!1){let i=this.currentId;const s=()=>{this.currentId!==i&&(t(this.currentId,i),i=this.currentId)},n=new b.V(this.onPropertyChanged("selected",s),this.onPropertyChanged("pendingAdd",s),this.onPropertyChanged("pendingEdit",s));return e?n.renew():n.cancel(),n}isReadonly(){return this.toolState===e.READ_ONLY||this.toolState===e.SELECTED_READ_ONLY}}t.State=s;class n extends C.y{constructor(t="invalid state"){super(t),this.name="invalid state"}}t.EditorException=n,function(t){class e extends O.v0{}e.type="edit",t.EditEvent=e;class i extends e{constructor(t){super(),this.target=t}}i.type="editt",t.EditTarget=i;class s extends e{constructor(t,e){super(),this.target=t,this.previous=e}}s.type="edittp",t.EditTargetAndPrev=s;class n extends e{constructor(t,e){super(),this.target=t,this.previous=e}}n.type="toolchange",t.StateChange=n;class a extends e{}a.type="editorstart",t.Start=a;class o extends e{}o.type="editorexit",t.Exit=o;class r extends e{}r.type="waitforaddstart",t.WaitForAddStart=r;class h extends i{}h.type="addstart",t.AddStart=h;class d extends i{constructor(t,e){super(t),this.placeOnly=e}}d.type="editstart",t.EditStart=d;class l extends i{}l.type="addconfirm",t.AddConfirm=l;class c extends i{}c.type="adddiscard",t.AddDiscard=c;class u extends i{}u.type="editconfirm",t.EditConfirm=u;class p extends i{}p.type="editdiscard",t.EditDiscard=p;class m extends i{}m.type="delete",t.Delete=m;class g extends i{constructor(t,e){super(t),this.target=t,this.ev=e}}g.type="move",t.Move=g;class v extends s{}v.type="target",t.CurrentChange=v;class f extends s{}f.type="hover",t.HoverChange=f;class y extends s{}y.type="select",t.SelectChange=y;class w extends i{}w.type="highlight",t.HighlightAdd=w;class b extends i{}b.type="highlightclear",t.HighlightClear=b;class S extends i{}S.type="dim",t.DimAdd=S;class D extends i{}D.type="dimclear",t.DimClear=D;class I extends s{}I.type="valid",t.ValidChange=I,t.hasTarget=function(t){return t instanceof i},t.hasTargetAndPrev=function(t){return t instanceof s}}(i=t.Events||(t.Events={}))}(F||(F={}));const N=new R.Z("room-bounds-editor");class V extends F.DragAndDrop{constructor(t,e,i,s){super(new F.State),this.messageBus=t,this.input=e,this.data=i,this.settingsData=s,this.allViewsComparator=E.s.is((t=>t instanceof T.c)),this.subscribe(F.Events.StateChange,(async({target:t})=>{this.inputStates||(this.inputStates=this.bindInputToEditorStates()),this.inputStates.cancel(),this.inputStates.renew(t)}))}bindInputToEditorStates(){const t=()=>this.setEnabled(!1),e=()=>this.setEnabled(!0),i=new b.V((0,w.k1)((()=>this.messageBus.subscribe(M.oR,t)),(()=>this.messageBus.unsubscribe(M.oR,t)),!1),(0,w.k1)((()=>this.messageBus.subscribe(M.NR,e)),(()=>this.messageBus.unsubscribe(M.NR,e)),!1)),s=(0,y.Jm)()?new b.V:new b.V(this.input.registerMeshHandler(I.z,this.allViewsComparator,((t,e)=>this.hover(e.roomBoundsId))),this.input.registerMeshHandler(I.A,this.allViewsComparator,(()=>this.unhover())));s.cancel();const n=new b.V(this.input.registerMeshHandler(D.Rd,this.allViewsComparator,this.onToggleSelectInput.bind(this)));n.cancel();const a=new b.V(i,s,n,(t=>{const e=N.debug;return(0,w.k1)((()=>e(`${t}.renew()`)),(()=>e(`${t}.cancel()`)),!1)})("ToolState.READ_ONLY -> bindings"));return new S([{state:F.ToolState.DISABLED,subs:[i]},{state:F.ToolState.READ_ONLY,subs:[a]},{state:F.ToolState.SELECTED,subs:[a]},{state:F.ToolState.SELECTED_READ_ONLY,subs:[a]},{state:F.ToolState.IDLE,subs:[a]}])}onToggleSelectInput(t,e){const i=e.roomBoundsId;if(i){setTimeout((()=>{if(e.parent){this.state.selected===i?t instanceof D.Rd&&(this.discard(),this.deselect()):(this.select(i),this.hover(i))}}),0);const s=this.data.tryGetEntity(i);return!(this.settingsData.tryGetProperty(d.eC,!1)&&s instanceof P.JJ)}return!0}}var B,G=i(16747);!function(t){const e={[F.Events.HoverChange.type]:"hoverState",[F.Events.SelectChange.type]:"selectState",[F.Events.ValidChange.type]:"validState",[F.Events.Move.type]:"mover",[F.Events.HighlightAdd.type]:"highlightState",[F.Events.HighlightClear.type]:"highlightState",[F.Events.DimAdd.type]:"dimState",[F.Events.DimClear.type]:"dimState"};t.EditorEntityAdapter=class{constructor(t){this.editor=t,this.bindings=[],this.entities=new Map,this.bindings.push(this.editor.subscribe(F.Events.HoverChange,(t=>this.bindTargetedCallback(F.Events.HoverChange.type,t))),this.editor.subscribe(F.Events.SelectChange,(t=>this.bindTargetedCallback(F.Events.SelectChange.type,t))),this.editor.subscribe(F.Events.ValidChange,(t=>this.bindTargetedCallback(F.Events.ValidChange.type,t))),this.editor.subscribe(F.Events.HighlightAdd,(t=>this.bindTargetedCallback(F.Events.HighlightAdd.type,t))),this.editor.subscribe(F.Events.HighlightClear,(t=>this.bindClearCallback(F.Events.HighlightClear.type,t))),this.editor.subscribe(F.Events.DimAdd,(t=>this.bindTargetedCallback(F.Events.DimAdd.type,t))),this.editor.subscribe(F.Events.DimClear,(t=>this.bindClearCallback(F.Events.DimClear.type,t))),this.editor.subscribe(F.Events.Move,(t=>this.bindMoveCallback(t))))}registerEntity(t,...e){let i=this.entities.get(t);i||(i=[]),i.push(...e),this.entities.set(t,i)}unregisterEntity(t){return this.entities.delete(t)}bindTargetedCallback(t,i){let s=null,n=null;F.Events.hasTargetAndPrev(i)?(s=i.target,n=i.previous):F.Events.hasTarget(i)&&(s=i.target);const a=e[t];if(!a)throw Error(`implement targetted callback for ${t}`);(this.entities.get(n)||[]).forEach((t=>{a in t&&(t[a].active=!1,t[a].off())})),(this.entities.get(s)||[]).forEach((t=>{a in t&&(t[a].active=!0,t[a].on())}))}bindMoveCallback(t){(this.entities.get(t.target)||[]).forEach((e=>{e.mover&&t.target&&e.mover.onMove(t.target,t.ev)}))}bindClearCallback(t,i){let s=null;const n=e[t];F.Events.hasTarget(i)&&(s=i.target),(this.entities.get(s)||[]).forEach((t=>{n in t&&(t[n].active=!1,t[n].off())}))}}}(B||(B={}));class _{constructor(t,e){this.editor=t,this.data=e,this.readBindings=[],this.mover={onMove:(t,e)=>{}},this.onCurrentChange=({target:t})=>{this.editor.state.toolState!==F.ToolState.ADDING&&this.clearHighlightedViews(),this.highlightView(t)},this.onSelectChange=({target:t})=>{this.setSelectedView(t)},this.onHoverChange=({target:t,previous:e})=>{if(this.editor.state.toolState===F.ToolState.ADDING)return;if(!t)return this.editor.clearAllDims(),this.editor.clearAllHighlights(),void(this.editor.state.selected&&this.setSelectedView(this.editor.state.selected));const i=this.data.getEntity(t);this.hoverView(i)},this.editableEntities=new B.EditorEntityAdapter(this.editor)}addEditableEntity(t,e){this.editableEntities.registerEntity(e,this,t)}removeEditableEntity(t){this.editableEntities.unregisterEntity(t)}activate(){0===this.readBindings.length?this.readBindings.push(this.editor.subscribe(F.Events.CurrentChange,this.onCurrentChange),this.editor.subscribe(F.Events.SelectChange,this.onSelectChange),this.editor.subscribe(F.Events.HoverChange,this.onHoverChange)):this.readBindings.forEach((t=>t.renew()))}deactivate(){this.readBindings.forEach((t=>t.cancel()))}clearHighlightedViews(){this.editor.clearAllHighlights()}highlightView(t){if(!t)return;const e=this.data.getEntity(t);e instanceof G.c&&this.editor.highlight(t,e.from.id,e.to.id)}setSelectedView(t){if(!t)return void this.editor.clearAllDims();const e=this.data.getEntity(t);e instanceof G.c&&this.editor.highlight(e.from.id,e.to.id),e instanceof P.JJ&&(this.clearHighlightedViews(),this.dimAllRooms(),this.selectRoom(e))}dimAllRooms(){this.editor.clearAllDims();for(const[,t]of this.data.rooms)this.dimRoom(t)}hoverView(t){t instanceof P.JJ&&this.hoverRoom(t)}hoverRoom(t){const e=t.id;this.editor.clearDim(e);for(const e of t.walls)this.editor.clearDim(e.id);for(const e of t.points)this.editor.clearDim(e.id)}selectRoom(t){const e=t.id;this.editor.clearDim(e);for(const e of t.walls)this.editor.clearDim(e.id);for(const e of t.points)this.editor.clearDim(e.id)}dimRoom(t){const e=t.id;this.editor.dim(e);for(const e of t.walls)this.editor.dim(e.id);for(const e of t.points)this.editor.dim(e.id)}}var H=i(18544),U=i(19663);class W extends U.m{constructor(t){super(),this.id="roombound_unselect",this.payload={id:t}}}class j extends U.m{constructor(t=!1){super(),this.id="set_roombound_visibility",this.payload={visible:t}}}class z extends U.m{constructor(t){super(),this.id="room_bound_set_allow_rendering",this.payload={allowRendering:t}}}var $=i(40964),q=i(91342);class Y{constructor(t,e,i){this.engine=t,this.toolsData=e,this.viewData=i,this.bindings=[],this.selectionChanged=async t=>{if(this.toolsData.activeToolName===u.w1.ROOM_BOUNDS)return;const e=this.viewData.getSelectedRoom(t);if(this.toolsData.activeToolName===u.w1.ROOM_VIEW!==!!e){const i=async()=>{t===this.viewData.editorState.selected&&(this.engine.commandBinder.issueCommand(new g.tT(u.w1.ROOM_VIEW,!!e)),this.engine.broadcast(e?new $.ro:new $.A))};e?setTimeout(i,q.u+25):i()}},this.bindings.push(this.viewData.editorState.onPropertyChanged("selected",this.selectionChanged))}async activate(){}async deactivate(){}async dispose(){this.bindings.forEach((t=>t.cancel()))}}var Z=i(85893),X=i(56843),K=i(33558),Q=i(38490),J=i(38399),tt=i(1358),et=i(67294);function it(t,e,i){const[s,n]=(0,et.useState)(null===t?i:t[e]);return(0,et.useEffect)((()=>{if(null===t)return;const i=t.onPropertyChanged(e,(t=>{n(t)}));return()=>i.cancel()}),[t,e,i]),s}const st=(0,i(45755).u)(H.e);function nt(){return it(function(){const t=st();return(null==t?void 0:t.editorState)||null}(),"selected",null)}function at(){const t=function(){const t=(0,tt.S)(),e=nt();return e&&t?t.getEntity(e):null}();return t&&t instanceof P.JJ?t:null}var ot=i(65428),rt=i(84426),ht=i(66102);function dt({room:t}){const e=(0,ht.b)().t(J.Z.WORKSHOP.ROOMS.ROOM_INACCESSIBLE_INFO);return t.accessible()?null:(0,Z.jsxs)("div",Object.assign({className:"room-inaccessible-container"},{children:[(0,Z.jsx)(rt.JO,{name:"public_symbols_exclamation-triangle",size:rt.Jh.SMALL}),(0,Z.jsx)("div",{children:e})]}))}var lt=i(94184),ct=i.n(lt),ut=i(56064),pt=i(46391),mt=i(78283),gt=i(62944);function vt({children:t,containerClass:e,titleKey:i,measurementsInMetric:s,measurementType:n,isHideable:a,visible:o,onToggle:r}){const h=(0,ut.O)(),d=(0,ht.b)();let l=[];if(n===gt.RV.AREA){const t=h===pt.M.IMPERIAL?gt.QK:gt.G8;l=s.filter((t=>!isNaN(t))).map(((e,i)=>{const s=h===pt.M.IMPERIAL?Math.round((0,mt.Nv)(e)):Math.round(10*e)/10;return(0,Z.jsx)(wt,{measure:s,unit:t},i)}))}else n===gt.RV.DISTANCE&&(l=s.filter((t=>!isNaN(t))).map(((t,e)=>{const i=[];if(e>0&&i.push((0,Z.jsx)(bt,{},`${e}_sep`)),h===pt.M.IMPERIAL){const{feet:s,inches:n}=(0,mt.XJ)(t);s>0?i.push((0,Z.jsx)(wt,{measure:s,unit:gt.B5},e),(0,Z.jsx)(wt,{measure:n,unit:gt.FP},`${e}_minor`)):i.push((0,Z.jsx)(wt,{measure:n,unit:gt.FP},e))}else i.push((0,Z.jsx)(wt,{measure:t.toFixed(2),unit:gt.Ep},e));return i})).flat());const c=o?"eye-show":"eye-hide",u=d.t(i);return(0,Z.jsxs)("div",Object.assign({className:ct()("room-detail",e)},{children:[(0,Z.jsx)("div",Object.assign({className:"p5"},{children:u})),(0,Z.jsxs)("div",Object.assign({className:"room-detail-info"},{children:[(0,Z.jsx)(ft,{measures:l,dimmed:!o}),a&&(0,Z.jsx)(rt.zx,{icon:c,active:!0,onClick:r})]})),t]}))}function ft({measures:t,dimmed:e}){return 0===t.length?(0,Z.jsx)(yt,{}):(0,Z.jsx)("div",Object.assign({className:("multi-pretty-measure-container "+(e?"dimmed":"")).trim()},{children:t}))}function yt(){return(0,Z.jsx)("div",Object.assign({className:"pretty-measure-container"},{children:(0,Z.jsx)("div",Object.assign({className:"p5"},{children:"n/a"}))}))}function wt({measure:t,unit:e}){return(0,Z.jsxs)("div",Object.assign({className:"pretty-measure-container"},{children:[(0,Z.jsx)("div",Object.assign({className:"h5 measure"},{children:t})),(0,Z.jsx)("div",Object.assign({className:"p5 unit"},{children:e}))]}))}function bt(){return(0,Z.jsx)("div",Object.assign({className:"p5 separator"},{children:gt.RQ}))}function St({room:t}){return(0,Z.jsx)(Dt,{warningKey:J.Z.WORKSHOP.ROOMS.ROOM_MISSING_INFO,missing:!t.canDisplayDimensions()||!t.canDisplayHeight()})}function Dt({missing:t,warningKey:e}){const i=(0,ht.b)().t(e);return t?(0,Z.jsx)("div",Object.assign({className:ct()("room-missing-info-warning","room-detail-child-container")},{children:i})):null}function It({room:t}){const e=(0,ht.b)().t(J.Z.SHOWCASE.ROOMS.ROOM_INFORMATION);return(0,Z.jsxs)("div",Object.assign({className:"room-size-info"},{children:[(0,Z.jsx)("div",Object.assign({className:"room-info-title"},{children:e})),(0,Z.jsxs)("div",Object.assign({className:"room-info-detail-container"},{children:[(0,Z.jsx)(vt,{titleKey:J.Z.SHOWCASE.ROOMS.ROOM_AREA,measurementsInMetric:[t.area],measurementType:gt.RV.AREA,isHideable:!1,visible:!0}),(0,Z.jsx)(vt,{titleKey:J.Z.SHOWCASE.ROOMS.ROOM_DIMENSIONS,measurementsInMetric:t.canDisplayDimensions()?[t.width,t.length]:[],measurementType:gt.RV.DISTANCE,isHideable:!1,visible:!0}),(0,Z.jsx)(vt,{titleKey:J.Z.SHOWCASE.ROOMS.ROOM_HEIGHT,measurementsInMetric:t.canDisplayHeight()?[t.height]:[],measurementType:gt.RV.DISTANCE,isHideable:!1,visible:!0}),(0,Z.jsx)(vt,{titleKey:J.Z.SHOWCASE.ROOMS.ROOM_PERIMETER,measurementsInMetric:[t.perimeter],measurementType:gt.RV.DISTANCE,isHideable:!1,visible:!0}),(0,Z.jsx)(St,{room:t})]}))]}))}var Pt=i(84784);function Tt({room:t}){const e=(0,tt.S)(),i=(0,ht.b)(),s=(0,Pt.LN)(t.id,i,e);return(0,Z.jsx)("div",Object.assign({className:"room-title"},{children:s}))}function Mt(){const{locale:t,commandBinder:e}=(0,et.useContext)(K.I),i=(0,ot.A)(),s=at(),n=i&&(i===u.w1.SEARCH||i===u.w1.LAYERS);async function a(){n?(await e.issueCommand(new g.cR),await e.issueCommand(new g.qy(!1))):s&&e.issueCommand(new W(s.id))}const o=t.t(n?J.Z.SHOWCASE.ROOMS.BACK:J.Z.SHOWCASE.ROOMS.CLOSE),r=n?"back":"close";return(0,Z.jsxs)(X.J,Object.assign({open:!!s,onClose:a},{children:[(0,Z.jsx)("div",Object.assign({className:"detail-panel-header"},{children:(0,Z.jsx)(Q.P,{label:o,className:"return-btn",icon:r,size:rt.qE.SMALL,onClose:a})})),(0,Z.jsx)("div",Object.assign({className:"room-view"},{children:s&&(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(Tt,{room:s}),(0,Z.jsx)(It,{room:s}),(0,Z.jsx)(dt,{room:s})]})}))]}))}var Et=i(94526),xt=i(95882),Ct=i(33272),Rt=i(96403),At=i(51978);const Lt=(0,i(73085).M)(H.e,"visibleInShowcase",!0);var Ot=i(57370),kt=i(89478),Ft=i(98025),Nt=i(91418);const{ROOMS:Vt}=J.Z.SHOWCASE;function Bt(){const t=(0,Et.B)(),e=(0,Rt.B)(),i=(0,ht.b)(),{analytics:s,commandBinder:n}=(0,et.useContext)(K.I),a=(0,At.y)(c.hW,!1),o=(0,Ot.q)(),r=Lt(),h=(0,At.y)(d.eC,!1),l=t===xt.Ey.Floorplan||t===xt.Ey.Dollhouse&&h;if(!a||0===o.length||!l||e&&[u.w1.MEASUREMENTS,u.w1.NOTES].includes(e))return null;const p=i.t(Vt.SETTINGS),m=i.t(Vt.ROOM_BOUNDS_TOGGLE_LABEL);return(0,Z.jsx)("div",Object.assign({className:"overlay grid-overlay"},{children:(0,Z.jsx)("div",Object.assign({className:"overlay-top-right"},{children:(0,Z.jsx)(kt.P,Object.assign({icon:"settings",tooltip:p,theme:"overlay",variant:rt.Wu.FAB,tooltipPlacement:"bottom",analytic:"showcase_settings_click",className:"showcase-settings",preventOverflow:{padding:20}},{children:(0,Z.jsxs)(Ft.J,{children:[(0,Z.jsx)(Nt.w,{onToggle:()=>{n.issueCommand(new j(!r)),s.trackToolGuiEvent("rooms","rooms_showcase_toggle_"+(r?"off":"on"))},toggled:r,label:m,enabled:!0}),(0,Z.jsx)(Ct.Q,{})]})}))}))}))}class Gt{constructor(){}renderPanel(){return(0,Z.jsx)(Mt,{})}renderPersistentOverlay(){return(0,Z.jsx)(Bt,{},"room-bound")}}const _t=[u.w1.SEARCH,u.w1.LAYERS,u.w1.ROOM_VIEW];class Ht extends s.Y{constructor(){super(...arguments),this.name="room_bound",this.active=!1,this.allowRendering=!0,this.isActivatingOrDeactivating=!1,this.viewListener={addView:(t,e)=>this.inputManager.addEditableEntity(t,e),removeView:t=>this.inputManager.removeEditableEntity(t)},this.activate=async()=>{this.isActivatingOrDeactivating=!0,this.active||(this.active=!0,this.editor.startReadOnly(),this.renderer.startRendering(!0,this.viewListener,(()=>!1)),this.viewData.setRoomBoundVisible(!0),this.inputManager.activate()),this.isActivatingOrDeactivating=!1},this.deactivate=async()=>{this.isActivatingOrDeactivating=!0,this.active&&(this.active=!1,this.editor.exit(),this.renderer.stopRendering(),this.viewData.setRoomBoundVisible(!1),this.inputManager.deactivate()),this.isActivatingOrDeactivating=!1},this.unselect=async t=>{t&&(await this.updateShowcaseVisibility(),this.active&&this.editor.deselect())}}async init(t,e){this.engine=e;const[i,s,d,c,u,m,g]=await Promise.all([e.getModuleBySymbol(r.PZ),e.market.waitForData(l.Z),e.market.waitForData(o.e),e.market.waitForData(f.O),e.market.waitForData(n.pu),e.market.waitForData(p.t),e.getModuleBySymbol(r.Ay)]);this.data=s,this.data.onActionError=t=>{this.applicationData.error=t,this.applicationData.commit()},this.viewmodeData=c,this.applicationData=u,this.toolsData=m,this.settingsData=d,this.renderer=g,this.editor=new V(e.msgBus,i,s,d);const v=d.tryGetProperty(h.gx.RoomBounds,!1);this.viewData=new H.e(this.data,this.editor.state,v),this.addToolPanel(),this.inputManager=new _(this.editor,s),this.bindings.push(e.commandBinder.addBinding(W,(async t=>this.unselect(t.id))),e.commandBinder.addBinding(j,(async t=>this.toggleVisibilityForViewer(t.visible))),e.commandBinder.addBinding(z,(async t=>await this.setAllowRendering(t.allowRendering))),c.currentModeObservable.onChanged((()=>this.updateShowcaseVisibility())),this.applicationData.onChanged((()=>this.updateShowcaseVisibility())),this.toolsData.onPropertyChanged("activeToolName",(()=>this.updateShowcaseVisibility())),this.settingsData.onPropertyChanged(h.gx.RoomBounds,(()=>this.updateShowcaseAvailability())),e.subscribe(a.pB,(t=>{this.updateShowcaseVisibility(),t.application===n.Mx.SHOWCASE&&this.addToolPanel()}))),e.market.register(this,H.e,this.viewData),this.updateShowcaseAvailability()}dispose(t){super.dispose(t),t.market.unregister(this,H.e)}toggleVisibilityForViewer(t){this.viewData.visibleInShowcase=t,this.viewData.commit(),this.updateShowcaseVisibility()}async updateShowcaseAvailability(){const t=this.settingsData.tryGetProperty(h.gx.RoomBounds,!1),e=this.settingsData.tryGetProperty(v.dF,!1),i=t&&e;this.settingsData.setProperty(c.hW,i),i&&!this.viewData.visibleInShowcase?this.toggleVisibilityForViewer(!0):this.updateShowcaseVisibility()}async updateShowcaseVisibility(){const t=this.toolsData.activeToolName===u.w1.ROOM_BOUNDS;if(this.isActivatingOrDeactivating||t)return;const{application:e}=this.applicationData,i=e===n.Mx.SHOWCASE,s=e===n.Mx.WORKSHOP,a=this.settingsData.tryGetProperty(c.hW,!1),o=this.settingsData.tryGetProperty(d.eC,!1),r=i&&this.viewData.visibleInShowcase&&a||s,h=null==this.toolsData.activeToolName||_t.includes(this.toolsData.activeToolName),l=this.viewmodeData.isFloorplan(),p=o&&this.viewmodeData.isDollhouse(),m=this.data.rooms.size>0,g=this.applicationData.phase===n.nh.PLAYING;r&&(l||p)&&h&&m&&g&&this.allowRendering?await this.activate():await this.deactivate()}async addToolPanel(){if(!this.toolsData.getTool(u.w1.ROOM_VIEW)){const t=new Y(this.engine,this.toolsData,this.viewData),e=new m.U({id:u.w1.ROOM_VIEW,panel:!(0,y.tq)(),enabled:!0,dimmed:!1,manager:t,ui:new Gt,analytic:"rooms_view"});await this.engine.commandBinder.issueCommand(new g.MV([e]))}}async setAllowRendering(t){this.allowRendering=t,this.updateShowcaseVisibility()}}},73992:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>g});var s=i(97542),n=i(97998),a=i(75287);class o extends a.T{equals(t){return this.id===t.id}copy(t){return this.id=t.id,this.name=t.name,this.vendor=t.vendor,this.model=t.model,this.captureMode=t.captureMode,this.depthCameraType=t.depthCameraType,this.cameraTypes=t.cameraTypes.slice(),this.sensorSerialNumbers=t.sensorSerialNumbers.slice(),this.serialNumber=t.serialNumber,this.mountCalibrationVersion=t.mountCalibrationVersion,this.softwareVersion=t.softwareVersion,this}}class r extends a.T{constructor(t){super(),t&&(t.id&&(this.id=t.id),void 0!==t.index&&(this.index=t.index),this.name=t.name||"",this.created=t.created||"",this.alignment=t.alignment||"",this.options=t.options||[],this.camera=t.camera||new o)}equals(t){return this.id===t.id}copy(t){return this.id=t.id,this.index=t.index,this.name=t.name,this.created=t.created,this.alignment=t.alignment,this.options=t.options.slice(),this.camera=(new o).copy(t.camera),this}}const h=new n.Z("mds-scaninfo-serializer");class d{deserialize(t){if(!t||!this.validate(t))return h.debug("Deserialized invalid ScanInfo data from MDS",t),null;const e=t,i=new r;i.id=e.id,i.anchorId=e.anchor&&e.anchor.id||"",i.index=e.index||-1,i.name=e.name||"",i.created=e.created||"",i.alignment=e.alignment||"",i.url=e.url||"",i.timeOfDay=e.timeOfDay||"",i.options=e.options||[];const s=e.camera;return i.camera=new o,i.camera.id=s&&s.id||"",i.camera.name=s&&s.name||"",i.camera.vendor=s&&s.vendor||"",i.camera.model=s&&s.model||"",i.camera.captureMode=s&&s.captureMode||"",i.camera.depthCameraType=s&&s.depthCameraType||"",i.camera.cameraTypes=((null==s?void 0:s.cameraTypes)||[]).filter((t=>t)),i.camera.sensorSerialNumbers=((null==s?void 0:s.sensorSerialNumbers)||[]).filter((t=>t)),i.camera.serialNumber=s&&s.serialNumber||"",i.camera.mountCalibrationVersion=(null==s?void 0:s.mountCalibrationVersion)?s.mountCalibrationVersion:void 0,i.camera.softwareVersion=s&&s.softwareVersion||"",i}validate(t){if(!t)return!1;return["id"].every((e=>e in t))}}var l=i(17788),c=i(68512);class u{}class p extends l.u{constructor(){super(...arguments),this.deserializer=new d}async fetch(){const t=this.getViewId();return this.query(c.GetScans,{modelId:t},{fetchPolicy:"no-cache"}).then((t=>{var e,i,s;const n=(null===(s=null===(i=null===(e=t.data)||void 0===e?void 0:e.model)||void 0===i?void 0:i.assets)||void 0===s?void 0:s.scans)||[],a={},o={};for(const t of n){const e=this.deserializer.deserialize(t);e&&(a[e.id]=e,e.anchorId&&(o[e.anchorId]=e))}const r=new u;return r.scansById=a,r.scansByAnchor=o,r}))}async read(){return this.scans||(this.scans=this.fetch()),this.scans}async refresh(){return this.scans=this.fetch(),this.scans}}var m=i(80742);class g extends s.Y{constructor(){super(...arguments),this.name="scaninfo-data",this.getScanInfo=t=>this.store.read().then((e=>e?e.scansByAnchor[t]:void 0)),this.getScanDownloadURL=t=>this.store.refresh().then((e=>{if(e){const i=e.scansByAnchor[t];return i?i.url:void 0}}))}async init(t,e){const i=await e.market.waitForData(m.R);this.store=new p({context:i.mdsContext,readonly:!0})}}},10777:(t,e,i)=>{"use strict";i.d(e,{F:()=>n});var s=i(19663);class n extends s.m{constructor(t){super(),this.id="SET_NAV_PANO_SIZE",this.payload={navSize:t}}}},31975:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>A});var s=i(97542),n=i(81396),a=i(72996),o=i(94046),r=i(82814),h=i(8948),d=i(45881),l=i(19663);class c extends l.m{constructor(t){super(),this.id="TOGGLE_PUCK_EDITING",this.payload={enabled:t}}}var u=i(10699),p=i(16782),m=i(67678),g=i(86819),v=i(34029),f=i(9037),y=i(21973),w=i(23254),b=i(95882),S=i(12039),D=i(5135),I=i(66777),P=i(92810);const T=[{outerRadius:1,innerRadius:.97,color:0,opacity:.1},{outerRadius:.97,innerRadius:.65,color:16777215,opacity:.64},{outerRadius:.65,innerRadius:.62,color:0,opacity:.13}];var M=i(18930),E=i(35895),x=i(38987),C=i(34956);const R={enabled:T,enabledHover:T};class A extends s.Y{constructor(){super(...arguments),this.name="sweep-pucks",this.IDLE_OPACITY=.3,this.EDITING_OPACITY=.9,this.IDLE_COLOR=new n.Color("white"),this.SELECTION_COLOR=new n.Color(16724312),this.defaultCheckRenderModes=()=>!0,this.unselectingSweep=null,this.editingEnabled=!1,this.selectionBindings=[],this.unselectionBindings=[],this.isHoveringPuck=!1,this.selectionHandled=!0,this.unselectionHandled=!0,this.handlePuckClickedMessage=t=>{if(!t)throw new Error("SweepPucks -> on PuckClickedMessage: Tried to move to invalid sweep id.");this.canSelectPuck()||this.sweepViewData.data.canTransition()&&this.cameraData.canTransition()&&this.engine.commandBinder.issueCommand(new S.ju({transition:I.y4[this.interactionmodeData.mode],sweep:t}))},this.handlePuckHoverMessage=({hovered:t})=>{this.isHoveringPuck=t,this.engine.commandBinder.issueCommand(new x.u(t?C.C.FINGER:null))},this.cursorVisibilityRule=()=>!this.isHoveringPuck,this.handleSweepSelectionChange=()=>{this.updateHighlightOnSelectedPuck(!1),this.selectedSweep=this.sweepViewData.selectedSweep,this.updateHighlightOnSelectedPuck(!0),this.updateHandlers()},this.updateViewmode=()=>{this.viewmode=this.viewmodeData.currentMode,this.updateHighlightOnSelectedPuck(this.editingEnabled),this.updateHandlers()},this.updateHandlers=()=>{this.selectionHandled=this.toggleHandlers(this.canSelectPuck(),this.selectionBindings,this.selectionHandled),this.unselectionHandled=this.toggleHandlers(this.canUnselectPuck(),this.unselectionBindings,this.unselectionHandled)},this.unselectPuck=()=>{this.unselectingSweep=null,this.sweepViewData.setSelectedSweep(null)},this.onUnselectPuck=t=>{const e=this.sweepViewData.selectedSweep;return e&&(t.down?this.unselectingSweep=e:this.unselectingSweep&&this.unselectPuck()),!0},this.onPointerOnPuck=(t,e)=>{if(t.button!==p.M.PRIMARY)return;const i=this.renderer.getSweepId(e.id);i&&(t.down?i===this.selectedSweep?this.unselectingSweep=i:(this.unselectingSweep=null,this.sweepViewData.setSelectedSweep(i)):(i===this.unselectingSweep&&this.unselectPuck(),this.unselectingSweep=null))}}async init(t,e){this.engine=e,void 0!==t.checkRenderModes&&(this.defaultCheckRenderModes=t.checkRenderModes);const i=(await e.getModuleBySymbol(P.Aj)).getScene(),s=await e.getModuleBySymbol(P.PZ),n=await e.getModuleBySymbol(P.Lk),[l,u,p,b,S]=await Promise.all([e.market.waitForData(v.e),e.market.waitForData(f.M),e.market.waitForData(y.D),e.market.waitForData(w.O),e.market.waitForData(D.Z)]);this.viewmodeData=b,this.viewmode=b.currentMode,this.sweepViewData=p,this.cameraData=u,this.interactionmodeData=S;this.renderer=new h.C(i.scene,s,l,p,R,!0,this.defaultCheckRenderModes,this.IDLE_COLOR,this.SELECTION_COLOR,this.IDLE_OPACITY,this.EDITING_OPACITY,void 0,void 0,e.claimRenderLayer(this.name)),e.addComponent(this,this.renderer),this.bindings.push(e.commandBinder.addBinding(c,(async t=>this.togglePuckEditing(t.enabled))),e.subscribe(d.Z,(t=>this.handlePuckClickedMessage(t.sweepId))),e.subscribe(M.Z,this.handlePuckHoverMessage),(0,E.k1)((()=>n.addVisibilityRule(this.cursorVisibilityRule)),(()=>n.removeVisibilityRule(this.cursorVisibilityRule)))),this.selectionBindings.push(s.registerMeshHandler(m.er,a.s.isType(h.Y),this.onPointerOnPuck),s.registerHandler(m.mE,(()=>{this.unselectingSweep=null}))),this.selectionHandled=this.toggleHandlers(!1,this.selectionBindings,this.selectionHandled),this.selectionSub=this.sweepViewData.onSelectedSweepChanged(this.handleSweepSelectionChange),this.selectionSub.cancel(),this.unselectionBindings.push(s.registerPriorityHandler(g.Rd,r.S,(()=>!0)),s.registerPriorityHandler(m.er,r.S,this.onUnselectPuck),s.registerPriorityHandler(m.er,o.i,this.onUnselectPuck)),this.unselectionHandled=this.toggleHandlers(!1,this.unselectionBindings,this.unselectionHandled)}dispose(t){for(const t of this.bindings)t.cancel();this.bindings=[],this.togglePuckEditing(!1),super.dispose(t)}updatePuckImagery(t={}){const e=Object.assign(Object.assign({},R),t);e.disabled&&!e.disabledHover&&(e.disabledHover=e.disabled),this.renderer.updatePuckImagery(e)}updateCheckRenderModes(t){this.renderer.updateCheckRenderModes(t||this.defaultCheckRenderModes)}updateHighlightOnSelectedPuck(t){const e=this.selectedSweep;if(e){if(!this.sweepViewData.isSweepAligned(e))return;const i=this.viewmode===b.Ey.Dollhouse||this.viewmode===b.Ey.Floorplan;this.renderer.renderPuckHighlight(e,this.editingEnabled&&i&&t)}}togglePuckEditing(t){this.editingEnabled=t,this.selectedSweep=this.sweepViewData.selectedSweep,this.updateViewmode(),this.renderer.toggleEditingEnabled(t),t?(this.engine.subscribe(u.Z,this.updateViewmode),this.selectionSub.renew()):(this.engine.unsubscribe(u.Z,this.updateViewmode),this.selectionSub.cancel())}canUnselectPuck(){const t=this.viewmode===b.Ey.Dollhouse||this.viewmode===b.Ey.Floorplan;return this.editingEnabled&&!!this.selectedSweep&&t}canSelectPuck(){const t=this.viewmode===b.Ey.Dollhouse||this.viewmode===b.Ey.Floorplan;return this.editingEnabled&&t}toggleHandlers(t,e,i){if(t!==i)for(const i of e)t?i.renew():i.cancel();return t}}},95806:(t,e,i)=>{"use strict";i.r(e),i.d(e,{FAST_FORWARD_FACTOR:()=>rt,default:()=>ht});var s=i(26256),n=i(14439),a=i(97542),o=i(95882),r=i(60699),h=i(2473),d=i(65397),l=i(23254),c=i(64918),u=i(96154),p=i(67678),m=i(12250),g=i(89553),v=i(6667),f=i(54244),y=i(10374),w=i(34029),b=i(9037),S=i(69505),D=i(2569),I=i(14715),P=i(59625),T=i(96042),M=i(17686);class E{constructor(t=-1){this.active=!1,this.type=M.Aq.Nop,this.promise=Promise.resolve(),this.stop=()=>Promise.resolve(),this.duration=0,this.started=-1,this.stopped=-1,this.toIndex=t}}var x=i(39880);class C{constructor(t){this.type=M.Aq.Delay,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.currentTransitionPromise=null,this.cancelDelay=()=>null,this.duration=t}get active(){return null!==this.currentTransitionPromise}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(this.cancelDelay(),this.currentTransitionPromise=null,this.stopped=Date.now())}start(t,e){if(this.active)throw Error("Transition already active");void 0!==t.duration&&(this.duration=t.duration);const i=(0,x.Ig)(this.duration);return this.cancelDelay=()=>{i.cancel()},this.currentTransitionPromise=i.promise.then((()=>this.stop())),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}}class R{constructor(t,e,i,s=!1){this.zoom=t,this.stopZooming=e,this.peekabooActive=s,this.type=M.Aq.Zoom,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.currentTransitionPromise=null,this.onStopRequested=()=>Promise.resolve(),this.duration=i}get active(){return null!==this.currentTransitionPromise}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(await this.onStopRequested(),await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now())}start(t,e){if(this.active)throw Error("Transition already active");void 0!==t.duration&&(this.duration=t.duration);const i=this.peekabooActive?-5:-5e-4;return this.currentTransitionPromise=this.zoom(this.duration,i).then((()=>{this.currentTransitionPromise=null,this.stopped=Date.now()})),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this.onStopRequested=async()=>{await this.stopZooming()},this}}var A=i(81396),L=i(90304),O=i(21646);class k{constructor(t,e,i){this.settingsData=t,this.rotate=e,this.stopRotating=i,this.type=M.Aq.Burns,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.currentTransitionPromise=null,this.onStopRequested=()=>Promise.resolve(),this.getPanDirection=(t,e)=>{let i=M.kw.Right;if(t&&t.metadata.scanId&&t.metadata.cameraQuaternion&&t.metadata.cameraPosition&&e&&e.metadata.scanId&&e.metadata.cameraPosition&&e.metadata.cameraQuaternion){const s=L.fU.FORWARD.clone().applyQuaternion(t.metadata.cameraQuaternion),n=e.metadata.cameraPosition,a=t.metadata.cameraPosition;let o=n.clone().sub(a).normalize();o.lengthSq()<O.Z.epsilon&&(o=L.fU.FORWARD.clone().applyQuaternion(e.metadata.cameraQuaternion)),s.cross(o).y>0&&(i=M.kw.Left)}return i}}get active(){return null!==this.currentTransitionPromise}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(await this.onStopRequested(),await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now())}start(t,e,i){if(this.active)throw Error("Transition already active");if(!t)throw Error("Tour pan requires two snapshots");if(!t.snapshot||!t.nextSnapshot)return this.currentTransitionPromise=Promise.resolve(),this.toIndex=e,this.started=Date.now(),this.stopped=Date.now(),this;const{deferred:s}=this.build(t.snapshot,t.nextSnapshot,t.panOverrides,i);return this.currentTransitionPromise=s.then((()=>{this.currentTransitionPromise=null,this.stopped=Date.now()})),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}build(t,e,i,s){const{panDirection:n,panAngle:a}=i,o=et.getPanValues(this.settingsData,!1,n,a);let r=o.direction;void 0!==r&&r!==M.kw.Auto||(r=this.getPanDirection(t,e)),this.onStopRequested=async()=>{await this.stopRotating()},this.duration=void 0!==s?s:o.ms;return{deferred:this.rotate(this.duration,new A.Vector2(r*o.radiansPerMs,0))}}}var F=i(23885),N=i(46950);const V=.001,B=(t,e,i,s)=>{const n=Math.max(.75,Math.min(t.distanceTo(e),5)),a=n*(1/s)*1e3;let o=a;const r=i/a;if(r>V){o+=o*((r-V)/V)}const h=Math.abs(t.clone().setX(0).setZ(0).distanceTo(e.clone().setX(0).setZ(0))/Math.max(n,1));if(h>.1){o*=.9+.75*h}return o};class G{constructor(t,e,i,s,n,a){this.settingsData=t,this.cameraPose=e,this.moveToSweep=i,this.updateTransitionSpeed=s,this.setRestrictedSweeps=n,this.generators=a,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.currentGenerator=null,this.currentTransitionPromise=null,this.type=M.Aq.Move}get active(){return null!==this.currentTransitionPromise||null!==this.currentGenerator}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(await this.onStopRequested(),await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now()),this.currentGenerator&&(this.generators.stopGenerator(this.currentGenerator),this.currentGenerator=null)}start(t,e){if(this.active)throw Error("Transition already active");const{generator:i,deferred:s}=this.build(t.path,t.orientations);return this.generators.startGenerator(i),this.currentGenerator=i,this.currentTransitionPromise=s.nativePromise(),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}build(t,e){const i=new N.Q,n=this;let a=!1;return this.onStopRequested=async()=>{a=!0,await this.updateTransitionSpeed(rt)},{generator:function*(){let o=1;for(;o<t.length&&!a;){const i=o-1,a=t[o],r=a.position,h=t[i],d=e[o],l=(0,D.zW)(n.cameraPose.rotation,d),u=et.getTransitionSpeed(n.settingsData),p=B(h.position,r,l,u),m={transitionType:c.n.Interpolate,sweepId:a.id,rotation:d,transitionTime:p,easing:F.vG};n.duration=p,n.setRestrictedSweeps(t,i);const g=n.moveToSweep(m);yield new s.M8(g.nativePromise()),o++}n.setRestrictedSweeps(null),i.resolve(),n.currentTransitionPromise=null,n.stop()},deferred:i}}}var _=i(79727);const H=new(i(97998).Z)("tours");class U{constructor(t,e,i,n,a,o,r,h,d){this.settingsData=t,this.cameraPose=e,this.cameraTransition=i,this.sweepTransition=n,this.sweepControl=a,this.cameraControl=o,this.generators=r,this.setRestrictedSweeps=h,this.getCurve=d,this.type=M.Aq.Path,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.currentTransitionGenerator=null,this.currentTransitionPromise=null,this.canceling=!1,this.buildTransition=(t,e)=>{if(t.length<=2)throw H.debug(`invalid path: ${t}`),new Error("smooth path requires more than 2 stops");const i=this,n=new N.Q;i.setRestrictedSweeps(t);const a=t.map((t=>t.position)),o=i.getCurve(a),r=F.vG,h=F.Fs,d=F.to,l=et.getTransitionSpeed(i.settingsData);this.duration=((t,e,i)=>{let s=0;for(let n=0;n<t.length-1;n++){const a=(0,D.zW)(e[n],e[n+1]);s+=B(t[n].position,t[n+1].position,a,i)}return s})(t,e,l),H.debug(`path duration: ${this.duration.toFixed(0)}ms, at speed: ${l}m/s`),i.cameraControl.beginExternalTransition();return{generator:function*(){n.notify(0);const a=new A.Vector3,u=new A.Quaternion;let p=0,m=0,g=0,v=1,f=t[v].id;yield new s.M8(i.sweepControl.activateSweepUnsafe({sweepId:f}).then((()=>{i.sweepControl.beginSweepTransition({sweepId:f,transitionTime:i.duration,internalProgress:!1})}))),t.length>2&&i.sweepControl.activateSweepUnsafe({sweepId:t[2].id});let y=i.cameraPose.rotation.clone();for(;p<i.duration&&!i.canceling;){const l=p/i.duration;g>0&&(y=e[g].clone());const c=e[v],w=o.normalSourceDistances[g],b=o.normalSourceDistances[v],S=t[v];if(m=(0,D.et)(l,w,b,0,1),m<=1){const t=d(m,0,1,1);i.sweepTransition.progress.modifyAnimation(t,1,0);const e=h(m,0,1,1);u.copy(y).slerp(c,e)}l<1&&a.copy(o.curve.getPointAt(r(l,0,1,1))),i.cameraControl.updateCameraPosition(a),i.cameraControl.updateCameraRotation(u),l>=b&&(i.sweepControl.endSweepTransition({sweepId:S.id}),g++,v++,f=t[g+1].id,yield new s.M8(i.sweepControl.activateSweepUnsafe({sweepId:f}).then((()=>{i.sweepControl.beginSweepTransition({sweepId:f,transitionTime:i.duration,internalProgress:!1})}))),t.length>g+2&&i.sweepControl.activateSweepUnsafe({sweepId:t[g+2].id})),n.notify(l),p=Date.now()-i.cameraTransition.startTime,yield new s.Jj}if(i.cameraControl.endExternalTransition(),i.canceling){i.canceling=!1;const e=t[v].position,n=B(i.cameraPose.position,e,0,l),a=i.cameraControl.moveTo({transitionTime:n/rt,transitionType:c.n.Interpolate,pose:{position:e}});a.progress((t=>{const e=(0,D.et)(t,0,1,m,1);i.sweepTransition.progress.modifyAnimation(e,1,0)})),yield new s.M8(a.nativePromise())}i.sweepControl.endSweepTransition({sweepId:f}),i.setRestrictedSweeps(null),n.notify(1),n.resolve()},deferred:n}}}get active(){return null!==this.currentTransitionPromise||null!==this.currentTransitionGenerator}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.canceling=!0,this.currentTransitionPromise&&(await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now()),this.currentTransitionGenerator&&(this.generators.stopGenerator(this.currentTransitionGenerator),this.currentTransitionGenerator=null)}start(t,e){if(H.debug(`starting smooth transition with ${t.path.length-1} stops`),this.active)throw Error("Transition already active");this.canceling=!1;const{generator:i,deferred:s}=this.buildTransition(t.path,t.orientations);return this.generators.startGenerator(i),this.currentTransitionGenerator=i,this.currentTransitionPromise=s.nativePromise().then((()=>{this.currentTransitionPromise=null,this.currentTransitionGenerator=null,this.stopped=Date.now()})),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}}var W=i(53954),j=i(66777);class z{constructor(t,e,i,s,n,a,o,r){this.settingsData=t,this.cameraPose=e,this.viewmodeData=i,this.cameraControl=s,this.sweepControl=n,this.switchToMode=a,this.setRestrictedSweeps=o,this.generators=r,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.type=M.Aq.Move,this.currentTransitionPromise=null,this.onStopRequested=()=>Promise.resolve()}get active(){return null!==this.currentTransitionPromise}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(await this.onStopRequested(),await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now())}start(t,e){if(this.active)throw Error("Transition already active");if(!t.snapshot)return this.currentTransitionPromise=Promise.resolve(),this;const{deferred:i}=this.build(t.snapshot,t.currentSweep,t.transitionType);return this.currentTransitionPromise=i.then((()=>{this.currentTransitionPromise=null,this.stopped=Date.now()})),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}build(t,e,i){let s=Promise.resolve();const n=t.metadata.cameraMode,a=t.metadata.cameraQuaternion,r=t.metadata.scanId,h=this.cameraPose.rotation,d=(0,D.zW)(h,a),l=t.metadata.cameraPosition,c=this.settingsData.tryGetProperty(j.eC,!1),u=!t.is360,p={position:l,rotation:a,sweepID:r,zoom:t.metadata.orthoZoom};let m=et.getOtherModeTransitionTime(this.settingsData,d,i);const g=this.cameraPose.pitchFactor(),v=this.viewmodeData.isDollhouse()&&!(this.viewmodeData.isDollhouse()&&g<=.9),f=this.viewmodeData.isFloorplan()||this.viewmodeData.isDollhouse()&&g<=1e-5,y=n===o.Ey.Floorplan,w=n===o.Ey.Dollhouse,b=c&&(w&&!v||y&&!f);if(n!==this.viewmodeData.currentMode||b)s=this.switchToMode(n,i,p,m);else{if(!!r&&this.viewmodeData.isInside()){!e||r!==e?(m=et.getTransitionTime(this.settingsData),s=this.standardTransitionSweepMovePromise(p,r,u,m)):(m=et.getSamePanoTransitionTime(this.settingsData,d),s=this.standardTransitionSameSweepRotationPromise(p,d,m))}else s=this.cameraControl.moveTo({transitionType:i,pose:p,transitionTime:m}).nativePromise()}return this.duration=void 0!==m?m:0,{deferred:s}}standardTransitionSameSweepRotationPromise(t,e,i){if(!t.rotation)throw Error("Rotation transition requires a rotation");return e<.01?Promise.resolve():(this.setRestrictedSweeps(null),this.cameraControl.moveTo({transitionType:c.n.Interpolate,pose:{rotation:t.rotation},transitionTime:i}).nativePromise())}standardTransitionSweepMovePromise(t,e,i,n){if(!t.position)throw Error("Push transition requires a position");const a=t.position.clone().sub(this.cameraPose.position).normalize(),o=this.cameraPose.position.clone().add(a.multiplyScalar(.15)),r=new N.Q;this.setRestrictedSweeps(null);const h=this;return this.generators.startGenerator((function*(){yield new s.M8(h.sweepControl.activateSweepUnsafe({sweepId:e}));const a=new A.Vector3,d=h.cameraPose.position.clone(),l=Date.now();let u,p=0,m=!1,g=!1;for(;p<n;){const r=(0,F.FG)(p,0,p/n,n);i&&h.cameraControl.updateCameraPosition(a.copy(d).lerp(o,r)),r>=.3&&!g&&(u=h.cameraControl.moveTo({transitionType:c.n.FadeToBlack,pose:t,transitionTime:n,blackoutTime:.5*n}).progress((t=>{t>=.5&&!m&&(h.sweepControl.instantSweepTransition(e),m=!0)})),g=!0),yield new s.Jj,p=Date.now()-l}u&&(yield new s.M8(u.nativePromise())),r.resolve()})),r.nativePromise()}}class ${constructor(t,e,i,s){this.moveToSweep=t,this.viewmodeData=e,this.cameraControl=i,this.switchToMode=s,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.type=M.Aq.Move,this.currentTransitionPromise=null,this.onStopRequested=()=>Promise.resolve()}get active(){return null!==this.currentTransitionPromise}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(await this.onStopRequested(),await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now())}start(t,e){if(this.active)throw Error("Transition already active");if(!t.snapshot)return this.currentTransitionPromise=Promise.resolve(),this.started=Date.now(),this.stopped=Date.now(),this;const{deferred:i}=this.build(t.snapshot,t.currentSweep);return this.currentTransitionPromise=i.then((()=>{this.currentTransitionPromise=null,this.stopped=Date.now()})),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}build(t,e){let i=Promise.resolve();const s=t.metadata.cameraMode,n=t.metadata.cameraQuaternion,a=t.metadata.scanId,o={position:t.metadata.cameraPosition,rotation:n,sweepID:a,zoom:t.metadata.orthoZoom},r=s!==this.viewmodeData.currentMode,h=!!a&&this.viewmodeData.isInside();if(r)i=this.switchToMode(s,c.n.Instant,o);else if(h){const t={transitionType:c.n.Instant,sweepId:a,rotation:n};i=this.moveToSweep(t).nativePromise()}else i=this.cameraControl.moveTo({transitionType:c.n.Instant,pose:o}).nativePromise();return{deferred:i}}}var q=i(52498),Y=i(33324);class Z{constructor(t,e){this.issueCommand=t,this.currentFloorId=e,this.type=M.Aq.FloorChange,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=q.cw,this.currentTransitionPromise=null,this.onStopRequested=()=>Promise.resolve()}get active(){return null!==this.currentTransitionPromise}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(await this.onStopRequested(),await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now())}start(t,e){if(this.active)throw Error("Transition already active");let i=Promise.resolve();const s=t.targetSnapshot.metadata.floorId,n=s!==this.currentFloorId(),a=t.targetSnapshot.metadata.cameraMode;return!(0,o.Bw)(a)&&a!==o.Ey.Outdoor&&n&&(i=this.issueCommand(new Y.Vw(s,!0,this.duration))),this.currentTransitionPromise=i.then((()=>{this.currentTransitionPromise=null,this.stopped=Date.now()})),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}}class X{constructor(t,e,i){this.settingsData=t,this.rotate=e,this.stopRotating=i,this.type=M.Aq.Burns,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.currentTransitionPromise=null,this.onStopRequested=()=>Promise.resolve()}get active(){return null!==this.currentTransitionPromise}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(await this.onStopRequested(),await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now())}start(t,e,i){if(this.active)throw Error("Transition already active");if(!t.snapshot||!t.nextSnapshot)return this.currentTransitionPromise=Promise.resolve(),this;const{deferred:s}=this.build(t.snapshot,t.nextSnapshot,t.panOverrides,i);return this.currentTransitionPromise=s.then((()=>{this.currentTransitionPromise=null,this.stopped=Date.now()})),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}build(t,e,i,s){const{panDirection:n,panAngle:a}=i,o=et.getPanValues(this.settingsData,!0,n,a);let r=-1*o.radiansPerMs;this.duration=void 0!==s?s:o.ms;const h=o.direction;if(void 0!==h&&h!==M.kw.Auto)r*=h;else if(t&&t.metadata.cameraQuaternion&&e&&e.metadata.cameraQuaternion){const i=L.fU.FORWARD.clone().applyQuaternion(t.metadata.cameraQuaternion),s=L.fU.FORWARD.clone().applyQuaternion(e.metadata.cameraQuaternion),n=Math.sign(i.cross(s).y);r=0!==n?r*n:r}this.onStopRequested=async()=>{await this.stopRotating()};return{deferred:this.rotate(this.duration,new A.Vector2(r,0))}}}class K{constructor(t,e,i,s,n){this.settingsData=t,this.cameraPose=e,this.rotate=i,this.stopRotating=s,this.getCurve=n,this.type=M.Aq.Burns,this.toIndex=-1,this.started=-1,this.stopped=-1,this.duration=0,this.currentTransitionPromise=null,this.onStopRequested=()=>Promise.resolve()}get active(){return null!==this.currentTransitionPromise}get promise(){return this.currentTransitionPromise?this.currentTransitionPromise:Promise.resolve()}async stop(){this.currentTransitionPromise&&(await this.onStopRequested(),await this.promise,this.currentTransitionPromise=null,this.stopped=Date.now())}start(t,e,i){if(this.active)throw Error("Transition already active");const{deferred:s}=this.build(t.path,t.snapshot,t.nextSnapshot,t.panOverrides,i);return this.currentTransitionPromise=s.then((()=>{this.currentTransitionPromise=null,this.stopped=Date.now()})),this.toIndex=e,this.started=Date.now(),this.stopped=-1,this}build(t,e,i,s,n){this.onStopRequested=async()=>{await this.stopRotating()};const{panDirection:a,panAngle:r}=s,h=et.getPanValues(this.settingsData,!1,a,r),d=h.direction;let l=h.radiansPerMs;if(void 0!==d&&d!==M.kw.Auto)l*=d;else if(t){const e=this.cameraPose.position.clone().setY(0),i=t.map((t=>t.position)),s=this.getCurve(i).curve.getPointAt(.1).setY(0).clone().sub(e).normalize(),n=L.fU.FORWARD.clone().applyQuaternion(this.cameraPose.rotation),a=Math.sign(n.cross(s).y);l=0!==a?l*a:l}else if(i&&e){if(!(0,o.Bw)(e.metadata.cameraMode)&&(l=-l),e.metadata.scanId===i.metadata.scanId){const t=L.fU.FORWARD.clone().applyQuaternion(this.cameraPose.rotation),e=L.fU.FORWARD.clone().applyQuaternion(i.metadata.cameraQuaternion),s=Math.sign(t.cross(e).y);l=0!==s?l*s:l}}this.duration=void 0!==n?n:h.ms;return{deferred:this.rotate(this.duration,new A.Vector2(l,0))}}}const Q={sharpTurnDotThreshold:.65,directionWeightFactorStd:.75,directionWeightFactorSharp:.2,positionalWeightFactorStd:.4,positionalWeightFactorSharp:.2,finalWalkingNodeDirectionWeight:5,lookAheadNodes:3};class J{constructor(t=Q){this.settings=t}getOrientationsForPath(t,e){const i=[];for(let s=0;s<t.length;s++){const n=new A.Vector3;this.getLookVectorsForPathNode(t,s,e,n);const a=(new A.Matrix4).lookAt(t[s],n,L.fU.UP);i[s]=(new A.Quaternion).setFromRotationMatrix(a)}return i.push(e),i}getLookVectorsForPathNode(t,e,i,s){const n=new A.Vector3,a=new A.Vector3,o=new A.Vector3,r=new A.Vector3,h=t.length;if(e>=h)return!1;let d=1,l=1;const c=new A.Vector3;let u;for(let s=e;s<e+this.settings.lookAheadNodes&&s<h;s++){if(u=t[s],this.getOrientationForPathNode(t,s,i,o),s===e&&n.copy(o),s>e){const t=n.dot(o)<this.settings.sharpTurnDotThreshold;d*=t?this.settings.directionWeightFactorSharp:this.settings.directionWeightFactorStd,l*=t?this.settings.positionalWeightFactorSharp:this.settings.positionalWeightFactorStd}s===h-1&&(d=this.settings.finalWalkingNodeDirectionWeight,l=1),c.copy(o),o.multiplyScalar(d),a.add(o),r.lerp(u,l)}return a.normalize(),s.copy(r),s.add(a),!0}getOrientationForPathNode(t,e,i,s){if(e>=t.length)return!1;if(e===t.length-1)s.copy(L.fU.FORWARD).applyQuaternion(i);else{const i=t[e],n=t[e+1];s.copy(n).sub(i)}return s.normalize(),!0}}var tt=i(92810);class et{constructor(t,e,i,s,a){this.engine=t,this.tourData=e,this.cameraData=i,this.settingsData=s,this.viewmodeData=a,this.init=async()=>{this.sweepModule=await this.engine.getModuleBySymbol(tt.l),this.sweepData=await this.engine.market.waitForData(W.Z),this.cameraModule=await this.engine.getModuleBySymbol(tt.kg),this.pathModule=await this.engine.getModuleBySymbol(tt.An),this.floorsViewData=await this.engine.market.waitForData(_.c),this.commonControlsModule=await this.engine.getModuleBySymbol(tt.Ng),this.viewmodeModule=await this.engine.getModuleBySymbol(tt.XT),this.pathOrientHelper=new J,this.setRestrictedSweeps=this.pathModule.setRestrictedSweeps.bind(this.pathModule),this.getCurveForPath=this.pathModule.getCurveForPath.bind(this.pathModule),this.moveToSweep=this.sweepModule.moveToSweep.bind(this.sweepModule),this.updateTransitionSpeed=this.cameraModule.updateTransitionSpeed.bind(this.cameraModule),this.switchToMode=this.viewmodeModule.switchToMode.bind(this.viewmodeModule),this.startRotateTransition=this.commonControlsModule.startRotateTransition.bind(this.commonControlsModule),this.stopCamera=this.commonControlsModule.stop.bind(this.commonControlsModule),this.startZoomTransition=this.commonControlsModule.startZoomTransition.bind(this.commonControlsModule),this.issueCommand=this.engine.commandBinder.issueCommand.bind(this.engine.commandBinder)},this.getValidWalkingPath=t=>{const e=t.metadata.scanId,i=this.sweepData.currentSweep,s=this.tourData.getTourCurrentSnapshotIndex();let a=null;if(s>=0){const t=this.tourData.getTourSnapshotSid(s);a=this.tourData.getSnapshot(t)}if(this.viewmodeData.currentMode!==o.Ey.Panorama||!i||!e||e===i||t.is360||!a||a.is360)return null;const r=this.pathModule.findShortestPath(i,e,n.Xd.walkingTourIncludeExtraPanosDistance,n.Xd.walkingStageMinimumDistance,n.Xd.maxWalkingSweepsBetweenSnapshots)||[];return 0===r.length?null:r}}static isDelayTransition(t,e,i){return!(0,n.dF)(t)||e!==M.BZ.Zoom&&0===et.getPanDegrees(t,i.panAngle)}static getTourBurnsStyle(t,e,i){return et.isDelayTransition(t,e,i)?M.BZ.Delay:e}static getPanDegrees(t,e){let i;return i=(0,n.dF)(t)?-1!==t.getOverrideParam(n.xs,-1)?(0,n.y)(t):void 0!==e?e:t.tryGetProperty(P.gx.PanAngle,n.BA):0,Math.max(i,0)}static getPanDirection(t,e){return void 0!==e?e:t.tryGetProperty(P.gx.PanDirection,I.y6)}static getDelayDuration(t){return-1!==t.getOverrideParam(n.lY,-1)||0===t.getOverrideParam(n._c,-1)?(0,n.g_)(t):n.mS}static getZoomDuration(t){return-1!==t.getOverrideParam(n.lY,-1)?(0,n.g_)(t):t.tryGetProperty(P.gx.ZoomDuration,n.i7)}static getPanRadiansPerMs(t,e,i){if(-1!==t.getOverrideParam(n.lY,-1)){const e=(0,n.g_)(t);return e>0?i/e:0}const s=e?t.tryGetProperty(P.gx.DollhousePanSpeed,n.su):t.tryGetProperty(P.gx.PanSpeed,n.pn);return(0,T.DA)(s)}static getPanValues(t,e,i,s){const a=et.getPanDirection(t,i),o=et.getPanDegrees(t,s),r=(0,S.Id)(o),h=et.getPanRadiansPerMs(t,e,r);let d=h>0?r/h:0;return e&&void 0===s&&(d=(0,n.g_)(t)),{degrees:o,radiansPerMs:h,ms:d,direction:a}}static getTransitionSpeed(t){if(-1!==t.getOverrideParam("wts",-1))return t.tryGetProperty(n.EU,n.Im);const e=t.tryGetProperty(P.gx.TransitionSpeed,n.Mk);return(0,T.mf)(e/1e3)}static getTransitionTime(t){const e=t.tryGetProperty(P.gx.TransitionTime,n.mL);return Math.min(Math.max(n.eu,e),n.NY)}static getOtherModeTransitionTime(t,e,i){if(i===c.n.FadeToBlack){return et.getTransitionTime(t)+n.HJ}let s=n.Cp,a=n.f7;if(-1===t.getOverrideParam("wts",-1)){const e=1e3*et.getTransitionSpeed(t);s=Math.sqrt(2*(e-n.Pv))+45,e!==n.Mk&&(a=n._D)}const o=1e3*e/(S.Ue*s);return Math.max(o,a)}static getSamePanoTransitionTime(t,e){const i=t.tryGetProperty(P.gx.PanSpeed,n.pn);if(i===n.pn)return Math.max(1e3*e/(S.Ue*n.O2),n.gS);{const t=(0,D.dS)(i,n.z$,n.b_,n.rM,n.z8),s=(0,T.DA)(t);return s>0?e/s:0}}getFloorTransition(t){const e=this.tourData.getTourSnapshotSid(t),i=this.tourData.getSnapshot(e);if(!i){return new E(t)}return new Z(this.issueCommand,(()=>this.floorsViewData.currentFloorId)).start({targetSnapshot:i},t)}getMainTransition(t,e){const i=this.tourData.getTourSnapshotSid(t),s=this.tourData.getSnapshot(i);if(!s){return new E(t)}let n=null;if(e===c.n.Interpolate){const e=s.metadata.cameraQuaternion,i=this.getValidWalkingPath(s);if(i){const s=i.map((t=>t.position)),a=this.pathOrientHelper.getOrientationsForPath(s,(0,D.Z)(e));n=i.length>2?new U(this.settingsData,this.cameraData.pose,this.cameraData.transition,this.sweepData.transition,this.sweepModule,this.cameraModule,this.engine,this.setRestrictedSweeps,this.getCurveForPath).start({path:i,orientations:a},t):new G(this.settingsData,this.cameraData.pose,this.moveToSweep,this.updateTransitionSpeed,this.setRestrictedSweeps,this.engine).start({path:i,orientations:a},t)}}else if(e===c.n.Instant){const e=this.sweepData.currentSweep;n=new $(this.moveToSweep,this.viewmodeData,this.cameraModule,this.switchToMode).start({snapshot:s,currentSweep:e},t)}if(!n){const i=this.sweepData.currentSweep;n=new z(this.settingsData,this.cameraData.pose,this.viewmodeData,this.cameraModule,this.sweepModule,this.switchToMode,this.setRestrictedSweeps,this.engine).start({snapshot:s,currentSweep:i,transitionType:e},t)}return n}getBurnsTransition(t,e,i){const s=this.tourData.getTourSnapshotSid(t),n=(t+1)%this.tourData.getSnapshotCount(),a=this.tourData.getTourSnapshotSid(n),o=this.tourData.getTourStop(s),r={};o.reelEntry&&o.reelEntry.overrides&&(r.panDirection=o.reelEntry.overrides.panDirection,r.panAngle=o.reelEntry.overrides.panAngle);const h=this.tourData.getSnapshot(a);let d=new E(t);switch(e){case M.BZ.Pan:if(h){const e=this.getValidWalkingPath(h);d=new K(this.settingsData,this.cameraData.pose,this.startRotateTransition,this.stopCamera,this.getCurveForPath).start({path:e,snapshot:o.snapshot,nextSnapshot:h,panOverrides:r},t,i)}d.type===M.Aq.Nop&&(d=new k(this.settingsData,this.startRotateTransition,this.stopCamera).start({snapshot:o.snapshot,nextSnapshot:h,panOverrides:r},t,i));break;case M.BZ.PanDollhouse:d=new X(this.settingsData,this.startRotateTransition,this.stopCamera).start({snapshot:o.snapshot,nextSnapshot:h,panOverrides:r},t,i);break;case M.BZ.Zoom:d=new R(this.startZoomTransition,this.stopCamera,et.getZoomDuration(this.settingsData),this.settingsData.tryGetProperty(j.eC,!1)).start({duration:i},t);break;case M.BZ.Delay:const e=et.getDelayDuration(this.settingsData);d=new C(e).start({duration:i},t);break;case M.BZ.None:d=new E(t);break;default:throw Error("unhandled TourBurnsStyle")}return d}}var it=i(71239),st=i(96767),nt=i(33397),at=i(97478),ot=i(80742);const rt=5;class ht extends a.Y{constructor(){super(...arguments),this.name="tours-controls",this.getBurnsStyleForSnapshot=(t,e)=>{const i=this.data,s=i.getTourSnapshotSid(t),n=i.getTourStop(s),a=i.getSnapshotCount();if(!n||!n.snapshot)return M.BZ.None;if(t===a-1&&this.isLastStopStatic(e))return this.toursViewData.getTourStoryMode()?M.BZ.Delay:M.BZ.None;const r=n.snapshot.metadata.cameraMode,h=r===o.Ey.Dollhouse?M.BZ.PanDollhouse:r===o.Ey.Floorplan?M.BZ.Zoom:M.BZ.Pan;return et.getTourBurnsStyle(this.settingsData,h,e)},this.canChangeTourLocation=()=>{const t=this.data.getTourState(),e=this.data.isTourTransitionActive(),i=this.cameraData.canTransition();return t===r.Vs.Inactive&&!e&&!(this.viewmodeData.transition&&this.viewmodeData.transition.active)&&i}}async init(t,e){this.engine=e,[this.cameraData,this.viewmodeData,this.settingsData,this.data,this.toursViewData]=await Promise.all([e.market.waitForData(b.M),e.market.waitForData(l.O),e.market.waitForData(w.e),e.market.waitForData(h.k),e.market.waitForData(d.T)]);const i=await e.market.waitForData(ot.R);e.getModuleBySymbol(tt.PZ).then((t=>{t.registerHandler(p.er,(()=>{this.handleTourInputInterrupt()})),t.registerHandler(m.a,(()=>{this.handleTourInputInterrupt()})),t.registerHandler(g.e,(t=>{t.state===v.M.DOWN&&this.handleTourInputInterrupt()}))})),this.transitionFactory=new et(e,this.data,this.cameraData,this.settingsData,this.viewmodeData),await this.transitionFactory.init(),this.setupAutoPlay(e),this.bindings.push(e.commandBinder.addBinding(st.TH,(async t=>this.startTour(t.index,t.steps,t.loop)))),this.bindings.push(e.commandBinder.addBinding(st.vy,(async t=>this.stopTour(t.willResume)))),this.bindings.push(e.commandBinder.addBinding(st.rU,(async t=>this.tourGoTo(t.index,t.instant?c.n.Instant:void 0)))),this.bindings.push(e.commandBinder.addBinding(st.HW,(async t=>t.forward?this.tourGoNext(t.instant):this.tourGoPrevious(t.instant)))),this.bindings.push(e.commandBinder.addBinding(st.r2,(async t=>{const e=this.data.tourPlaying;try{e&&await this.stopTour(!0),await this.tourGoNext(!1),e&&await this.startTour()}catch(t){this.log.debug(t)}}))),this.bindings.push(e.commandBinder.addBinding(st.Ri,(async t=>{const e=this.data.tourPlaying;try{e&&await this.stopTour(!0),await this.tourGoPrevious(!1),e&&await this.startTour()}catch(t){this.log.debug(t)}}))),this.bindings.push(i.onPropertyChanged("currentViewId",(()=>this.stopTour(!1))))}handleTourInputInterrupt(){this.data.getTourState()!==r.Vs.Inactive&&this.stopTour()}canTourProceed(t,e,i){const s=this.data.getSnapshotCount();if(0===s||0===i||this.data.getTourState()!==r.Vs.Inactive)return!1;if(void 0!==e){if(e<-1)return!1;if(!0!==t&&e>s-1)return!1}return!0}shouldTourContinue(t,e,i,s){return void 0!==s?i<s:e<t-1}startTour(t,e,i){if(!this.canChangeTourLocation())throw new nt.Y("Cannot start tour at this time, another transition is active");const n=void 0!==i?i:this.data.isLooping();if(!this.canTourProceed(n,t,e))return;this.data.setLooping(n);const a=this.data.getActiveReelTourMode(),o=(0,at.Cf)(this.settingsData,a),h=this.data.getTourCurrentSnapshotIndex(),d=this.data.getSnapshotCount(),l=this.data.transition,c=h===d-1&&(M.c5.includes(l.type)||!o)&&l.toIndex===d-1&&l.stopped-l.started>=l.duration;let p,m=0;p=void 0!==t?t-1:c?-1:Math.max(h-1,-1);const g=this.settingsData.tryGetProperty(it.YS,null);this.settingsData.setProperty(it.YS,null);const v=this;this.tourGenerator=function*(){for(;v.shouldTourContinue(d,p,m,e);){const t=p+1,e=v.composeTourTransition(t);yield new s.M8(e),p=t,p===d-1&&n&&(p=-1),m++}v.stopTour(),v.settingsData.setProperty(it.YS,g)},this.engine.startGenerator(this.tourGenerator),this.data.setTourState(r.Vs.Active),this.data.tourEnded=!1,this.data.tourWillResume=!1,this.data.tourPlaying=!0,this.data.commit(),this.engine.broadcast(new u.oR)}async composeTourTransition(t,e,i){var s,a,h;const d=this.data.getTourSnapshotSid(t),l=this.data.getTourStop(d);if(!l.snapshot)throw Error(`Highlight not found for reel index ${t}`);const p=this.data.getTourCurrentSnapshotSid()===d,m=this.cameraData.transition.startTime>this.data.transition.stopped;let g=0;const v=this.data.transition.duration;if(p){const{started:t,stopped:e}=this.data.transition;g=(e-t)/v||0,g=Math.min(1,g)}if(m||!p){let e=this.settingsData.tryGetProperty(I.gj,c.n.Interpolate);const n=l.snapshot.metadata.cameraMode,h=n===o.Ey.Dollhouse||n===o.Ey.Floorplan,d=(0,o.Bw)(n),p=!this.viewmodeData.isInside()&&d,m=this.viewmodeData.isInside()&&h,g=!this.viewmodeData.isInside()&&h,v=this.viewmodeData.isInside()&&d;(p||m||g)&&(e=c.n.Interpolate),void 0!==(null===(a=null===(s=l.reelEntry)||void 0===s?void 0:s.overrides)||void 0===a?void 0:a.transitionType)&&(e=l.reelEntry.overrides.transitionType),v&&0===t&&(e=c.n.FadeToBlack),void 0!==i&&(e=i),this.engine.broadcast(new u.dW(t));const f=this.transitionFactory.getFloorTransition(t);if(this.data.useTransition(f),await f.promise,this.data.getTourState()===r.Vs.StopScheduled)return;const y=this.transitionFactory.getMainTransition(t,e);this.data.useTransition(y),await y.promise,this.data.setTourCurrentSnapshotByIndex(t),this.engine.broadcast(new u.Vx(t))}if(this.data.getTourState()===r.Vs.StopScheduled)return;const f={};(null===(h=l.reelEntry)||void 0===h?void 0:h.overrides)&&(f.panAngle=l.reelEntry.overrides.panAngle,f.panDirection=l.reelEntry.overrides.panDirection);const y=e||this.getBurnsStyleForSnapshot(t,f);let w;if(this.toursViewData.getTourStoryMode()){t===this.data.getSnapshotCount()-1&&this.isLastStopStatic(f)&&(w=n.GS)}g>0&&(w=(1-g)*v);const b=this.transitionFactory.getBurnsTransition(t,y,w);this.engine.broadcast(new u._3(t,b.type,b.duration)),this.data.useTransition(b),await b.promise}isLastStopStatic(t){const e=et.getPanDirection(this.settingsData,t.panDirection),i=et.getPanDegrees(this.settingsData,t.panAngle),s=this.data.getActiveReelTourMode(),n=(0,at.Cf)(this.settingsData,s);return 0===i||!n&&e===M.kw.Auto}async stopTour(t=!1){this.data.getTourState()===r.Vs.Active&&(this.data.setTourState(r.Vs.StopScheduled),this.data.tourWillResume=t,this.data.tourPlaying=!1,await this.data.stopTourTransition(),this.tourGenerator&&this.engine.stopGenerator(this.tourGenerator),this.engine.broadcast(new u.NR(t)),this.data.getTourCurrentSnapshotIndex()!==this.data.getSnapshotCount()-1||this.data.isLooping()||(this.data.tourEnded=!0,this.engine.broadcast(new u.Mt)),this.data.setTourState(r.Vs.Inactive),this.data.commit())}async tourGoNext(t){let e=this.data.getTourCurrentSnapshotIndex()+1;e>=this.data.getSnapshotCount()&&(e=0);const i=t?c.n.Instant:void 0;return this.tourGoTo(e,i)}async tourGoPrevious(t){let e=this.data.getTourCurrentSnapshotIndex();e<0&&(e=0);let i=e-1;i<0&&(i=this.data.getSnapshotCount()-1);const s=t?c.n.Instant:void 0;return this.tourGoTo(i,s)}async tourGoTo(t,e=c.n.FadeToBlack){if(!this.canChangeTourLocation())throw new nt.z("Cannot change tour location at this time, another transition is active");if(this.viewmodeData.transition&&this.viewmodeData.transition.active)throw new nt.z("Cannot go to tour location during viewmode transition");if(this.data.getTourState()!==r.Vs.Inactive)throw new nt.z("Cannot jump to tour location while tour is active");try{this.data.setTourState(r.Vs.Active),await this.composeTourTransition(t,M.BZ.None,e)}catch(t){this.log.error(t)}finally{this.data.setTourState(r.Vs.Inactive)}}setupAutoPlay(t){const e=1e3*(0,n.zi)(this.settingsData),i=()=>{let t=!0;const i=this.cameraData.pose.onChanged((()=>{t=!1,i.cancel()}));setTimeout((()=>{t&&this.startTour(),i.cancel()}),e)};if(e>=0){const e=t.market.tryGetData(f.pu);if(e&&e.phase===f.nh.PLAYING)i();else{const e=s=>{s.phase===f.nh.PLAYING&&(i(),t.unsubscribe(y.LZ,e))};t.subscribe(y.LZ,e)}}}}},87152:(t,e,i)=>{"use strict";i.r(e),i.d(e,{HighlightReel:()=>f,ToggleForceShowStoryTextCommand:()=>G.iy,ToggleHighlightReelOpenCommand:()=>G.Av,TourAddPosition:()=>B.VH,TourChangeDescriptionCommand:()=>G.K1,TourChangeTitleCommand:()=>G.Ty,TourData:()=>s.k,TourMode:()=>B.zz,TourRenameCommand:()=>G.CI,TourSetTourModeCommand:()=>G.Te,TourState:()=>B.Vs,ToursViewData:()=>n.T,default:()=>tt});var s=i(2473),n=i(65397),a=i(97542),o=i(34029),r=i(95882),h=i(53954),d=i(42418),l=i(59625),c=i(65019),u=i(17788),p=i(32082),m=i(97998),g=i(10385),v=i(75287);class f extends v.T{constructor(t){super(),this.reel=(0,g.C)([]),this.modified=new Date,this.mode=t,this.commit()}replace(t){this.atomic((()=>{this.sid=t.sid,this.reel.replace(Array.from(t.reel.values())),this.mode=t.mode,this.modified=t.modified}))}}var y=i(64918),w=i(17686),b=i(37519),S=i(29282),D=i(5823);const I=new m.Z("mds-reel-element-serializer"),P={[D.y8.FADE_TO_BLACK]:y.n.FadeToBlack,[D.y8.INSTANT]:y.n.Instant,[D.y8.INTERPOLATE]:y.n.Interpolate},T={[D.Y6.LEFT]:w.kw.Left,[D.Y6.RIGHT]:w.kw.Right,[D.Y6.AUTO]:w.kw.Auto};class M{deserialize(t){var e;if(!t||!(null===(e=null==t?void 0:t.asset)||void 0===e?void 0:e.id))return I.debug("Deserialized invalid highlight reel entry from MDS",t),null;const i=t.overrides,s={},n=(null==i?void 0:i.transitionType)?P[null==i?void 0:i.transitionType]:void 0,a=(null==i?void 0:i.panDirection)?T[null==i?void 0:i.panDirection]:void 0,o=null==i?void 0:i.panAngle;(0,S.r)(n)&&(s.transitionType=n),(0,S.r)(a)&&(s.panDirection=a),(0,b.hj)(o)&&(s.panAngle=o);const r={sid:t.asset.id,overrides:s};return t.title&&(r.title=t.title),t.description&&(r.description=t.description),r}}const E=new m.Z("mds-highlight-reel-serializer");class x{constructor(t){this.defaultTourMode=t,this.reelEntrySerializer=new M}deserialize(t){if(!t||!this.validate(t))return E.debug("Deserialized invalid active reel data from MDS",t),null;const e=new f(t.mode||this.defaultTourMode);if(e.sid=t.id,t.reel)for(const i of t.reel){if(!i)continue;const t=this.reelEntrySerializer.deserialize(i);t&&e.reel.push(t)}return e}validate(t){return["id"].every((e=>e in t))}}const C={[y.n.FadeToBlack]:D.y8.FADE_TO_BLACK,[y.n.Instant]:D.y8.INSTANT,[y.n.Interpolate]:D.y8.INTERPOLATE,[y.n.MoveToBlack]:D.y8.FADE_TO_BLACK},R={[w.kw.Left]:D.Y6.LEFT,[w.kw.Right]:D.Y6.RIGHT,[w.kw.Auto]:D.Y6.AUTO};class A{serialize(t){const e={id:t.sid};return t.title&&(e.title=t.title),t.description&&(e.description=t.description),t.overrides&&(e.overrides={},void 0!==t.overrides.transitionType&&(e.overrides.transitionType=C[t.overrides.transitionType]),void 0!==t.overrides.panDirection&&(e.overrides.panDirection=R[t.overrides.panDirection]),void 0!==t.overrides.panAngle&&(e.overrides.panAngle=t.overrides.panAngle)),e}}const L=new m.Z("mds-highlight-reel-store");class O extends u.u{constructor(t,e,i){super(t),this.baseModelId=i,this.serializer=new A,this.prefetchKey="data.model.activeHighlightReel",this.deserializer=new x(e)}async read(t){const e={modelId:this.getViewId(),prefetchKey:this.prefetchKey};return this.query(p.GetHighlightReel,e,t).then((t=>{var e,i;const s=null===(i=null===(e=null==t?void 0:t.data)||void 0===e?void 0:e.model)||void 0===i?void 0:i.activeHighlightReel;return this.deserializer.deserialize(s)}))}async update(t){const e=this.getViewId(),i=t.reel.map((t=>this.serializer.serialize(t))),s=t.mode;return this.mutate(p.PutActiveReel,{modelId:e,elements:i,mode:s}).then((async t=>{L.debug(t)}))}async fetchAllTourSweeps(){var t,e;const i={modelId:this.baseModelId},s=null===(e=null===(t=(await this.query(p.GetHighlightReelSweeps,i,{fetchPolicy:"no-cache"})).data)||void 0===t?void 0:t.model)||void 0===e?void 0:e.views,n=[];return s&&s.forEach((t=>{var e,i;const s=null===(i=null===(e=t.model)||void 0===e?void 0:e.activeHighlightReel)||void 0===i?void 0:i.reel;s&&s.forEach((e=>{var i,s,a;const o=null===(a=null===(s=null===(i=e.asset)||void 0===i?void 0:i.snapshotLocation)||void 0===s?void 0:s.anchor)||void 0===a?void 0:a.id;o&&n.push({viewId:t.id,sweepId:o})}))})),n}}var k=i(86090),F=i(2541),N=i(79728),V=i(635),B=i(60699),G=i(89264),_=i(97478),H=i(92810),U=i(54244),W=i(89570),j=i(38399),z=i(71166),$=i(35221),q=i(56163),Y=i(96767);const{HLR:Z}=j.Z.WORKSHOP;class X extends q.K{constructor(t,e,i,s,n,a){if(super(t,void 0,e),this.reelEntry=i,this.index=s,this.tourMode=n,this.locale=a,this.id=this.reelEntry.id,this.title=this.reelEntry.title||"",this.description=this.reelEntry.description||"",this.icon="icon-toolbar-hlr",this.typeId=D.SF.HIGHLIGHTREEL,this.floorId="",this.roomId="",this.dateBucket=(0,$.f)(this.reelEntry.snapshot.created),this.onSelect=async()=>{super.onSelect(),await this.commandBinder.issueCommand(new Y.rU(this.index)),this.tourMode===B.zz.STORIES?await this.commandBinder.issueCommand(new G.iy(!0)):this.commandBinder.issueCommand(new G.Av(!0))},n===B.zz.STORIES){const t=!!i.title,e=!!i.snapshot.name;this.title=t&&e?i.snapshot.name+" - "+i.title:t||e?i.title?i.title:i.snapshot.name:this.getDefaultName()}else this.title=i.snapshot.name?i.snapshot.name:this.getDefaultName(),this.description="";this.imgUrl=this.reelEntry.snapshot.thumbnailUrl}supportsBatchDelete(){return!1}getDefaultName(){return this.locale.t(Z.SEARCH_HIGHLIGHT_DEFAULTNAME)+" "+(this.index+1)}}const{HLR:K}=j.Z.WORKSHOP;var Q=i(21359),J=i(80742);class tt extends a.Y{constructor(){super(...arguments),this.name="tours-data",this.defaultModes=[r.Ey.Panorama,r.Ey.Outdoor],this.fetchAllTourSweeps=async()=>{const t=await this.store.fetchAllTourSweeps();this.viewData.setSweepsInToursAcrossViews(t)},this.updateTourMode=()=>{this.viewData.currentTourMode=this.getCurrentTourMode(),this.viewData.setTourModeSetting(this.getTourModeSetting()),this.viewData.commit()},this.closeReelIfEmpty=()=>{0===this.tourData.getSnapshotCount()&&this.viewData.reelOpen&&this.toggleReel(!1)},this.saveTourModeChange=async t=>{const{tourMode:e}=t;e===B.zz.STORIES?this.tourData.setActiveReelTourMode(D.Z1.STORY):e===B.zz.LEGACY&&this.tourData.setActiveReelTourMode(D.Z1.REEL),this.updateTourMode()},this.onToggleReel=async t=>{this.toggleReel(t.open)},this.onForceShowStoryText=async t=>{this.engine.broadcast(new Q.Q)},this.onUpdateSnapshots=()=>{this.tourData.updateSnapshots(this.snapshotsData.collection)}}async init(t,e){const{readonly:i,baseUrl:a,storyToursFeature:r,baseModelId:c}=t;this.engine=e,this.config=t;const[u,p,m,g]=await Promise.all([e.market.waitForData(o.e),e.market.waitForData(h.Z),e.market.waitForData(d.P),e.market.waitForData(J.R)]);this.settingsData=u,this.snapshotsData=m;const v=this.getFilterModes(this.defaultModes),y=r?D.Z1.STORY:D.Z1.REEL;if(this.tourData=new s.k(this.snapshotsData.collection,new f(y),v,p.getSweepList(),t.looping,this.log),this.viewData=new n.T(this.tourData,this.getTourModeSetting(),this.getCurrentTourMode()),!1===i){const t=await e.getModuleBySymbol(H.Lx),i=this.tourData.getReel();this.monitor=new k.u(i,{aggregationType:F.E.NextFrame},e),this.monitor.onChanged((()=>{this.monitor.hasDiffRecord()&&this.engine.commandBinder.issueCommand(new V.V({dataTypes:[N.g.HIGHLIGHTS]}))})),this.bindings.push(t.onSave((()=>this.save()),{dataType:N.g.HIGHLIGHTS}),t.onSave((()=>this.fetchAllTourSweeps()),{dataType:N.g.SWEEPS})),this.bindings.push(e.commandBinder.addBinding(G.Te,this.saveTourModeChange),this.settingsData.onPropertyChanged(l.gx.TourButtons,this.updateTourMode),this.settingsData.onPropertyChanged(l.gx.HighlightReel,this.updateTourMode))}this.store=new O({context:g.mdsContext,readonly:i,baseUrl:a},y,c),this.bindings.push(this.store.onNewData((async t=>{this.tourData.atomic((()=>{var e;this.tourData.setHighlightReel(t||new f(y)),null===(e=this.monitor)||void 0===e||e.clearDiffRecord()})),this.updateTourMode(),this.closeReelIfEmpty()}))),await this.store.refresh(),this.bindings.push(e.commandBinder.addBinding(G.Av,this.onToggleReel),e.commandBinder.addBinding(G.iy,this.onForceShowStoryText),this.snapshotsData.onChanged(this.onUpdateSnapshots)),async function(t,e,i){const s=await t.market.waitForData(U.pu),n=await t.getModuleBySymbol(H.e9),a=(s,a,o,r=[])=>{const h=[],d=e.getCurrentTourState().highlights,l=i.currentTourMode;if(r.length>0||l===B.zz.NONE)return h;const c=l===B.zz.STORIES;let u=0;return d.forEach((e=>{const i=e.title&&c?e.title:n.t(K.SEARCH_HIGHLIGHT_DEFAULTNAME)+" "+(u+1);if(s(i)||e.snapshot.name&&s(e.snapshot.name)||c&&e.description&&s(e.description)){const i=new X(t.commandBinder,a,e,u,l,n);h.push(i)}u++})),h},o=t=>new W.V(e.onChanged(t),i.onTourModeSettingChanged(t)),r=()=>{t.commandBinder.issueCommandWhenBound(new z.c6({id:D.SF.HIGHLIGHTREEL,groupPhraseKey:K.SEARCH_TOUR_HEADER,getSimpleMatches:a,registerChangeObserver:o,groupOrder:10,groupIcon:"toolbar-hlr",batchSupported:!1}))},h=()=>{t.commandBinder.issueCommandWhenBound(new z.Pe(D.SF.HIGHLIGHTREEL))},d={renew:r,cancel:h},l=t=>{h(),r()},c=s.onPropertyChanged("application",l);l(s.application),new W.V(d,c)}(e,this.tourData,this.viewData),e.market.register(this,s.k,this.tourData),i?e.market.register(this,n.T,this.viewData):this.fetchAllTourSweeps().then((()=>{e.market.register(this,n.T,this.viewData)}))}dispose(t){this.store.dispose(),super.dispose(t)}async save(){if(!this.monitor||this.config.readonly)return void this.log.warn("Tour changes will NOT be saved");const t=this.tourData.getReel();await this.store.update(t),this.fetchAllTourSweeps(),this.monitor.clearDiffRecord()}getCurrentTourMode(){return(0,_.aW)(this.settingsData,this.tourData.getActiveReelTourMode())}getTourModeSetting(){return(0,_.w7)(this.settingsData,this.tourData.getActiveReelTourMode())}toggleReel(t){this.viewData.reelOpen=t,this.viewData.commit()}getFilterModes(t){return this.settingsData.tryGetProperty(c.wY,!1)&&t.push(r.Ey.Dollhouse),this.settingsData.tryGetProperty(c.dF,!1)&&t.push(r.Ey.Floorplan),t}}},10805:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>m});var s=i(61864),n=i(92810),a=i(97542),o=i(96767),r=i(96154),h=i(31362),d=i(50575);class l{constructor(){this._dateNow=Date.now,this._performanceNow=performance.now,this.nowOverride=0}slowTime(t){this.fps=t,this.nowOverride=Date.now(),Date.now=()=>this.nowOverride,performance.now=()=>this.nowOverride}tick(){this.nowOverride+=1e3/this.fps}resetTime(){Date.now=this._dateNow,performance.now=this._performanceNow}}var c,u=i(38319),p=i(26256);!function(t){t[t.STOPPED=0]="STOPPED",t[t.RECORDING=1]="RECORDING"}(c||(c={}));class m extends a.Y{constructor(){super(...arguments),this.name="video-recorder-module",this.state=c.STOPPED,this.frostMage=new l}async init(t,e){this.settingsModule=await e.getModuleBySymbol(s.Ak),this.canvasModule=await e.getModuleBySymbol(n.iM),this.engine=e,this.settingsModule.registerButton("Tour Recorder (Chrome Only)","Download 1080p @ 60",(()=>{this.state===c.STOPPED&&this.record(1920,1080,60)})),this.settingsModule.registerButton("Tour Recorder (Chrome Only)","Download 720p @ 30",(()=>{this.state===c.STOPPED&&this.record(1280,720,30)})),this.settingsModule.registerButton("Tour Recorder (Chrome Only)","Download instagram",(()=>{this.state===c.STOPPED&&this.record(1080,1080,30)})),this.settingsModule.registerButton("Tour Recorder (Chrome Only)","Download instagram story",(()=>{this.state===c.STOPPED&&this.record(1080,1920,30)})),this.settingsModule.registerButton("Tour Recorder (Chrome Only)","Stop & download current",(()=>{this.state===c.RECORDING&&this.stop()}))}async record(t,e,s){if(this.state!==c.STOPPED)return void this.log.warn("Can't start recording... we're already recording!");this.log.info("Starting recording of tour. Now is a good time to get a coffee :)"),this.state=c.RECORDING;const n=await Promise.all([i.e(764),i.e(511),i.e(718)]).then(i.bind(i,15730));this.encoder=new n.WebMWriter({quality:.95,frameRate:s}),this.frostMage.slowTime(s),await this.engine.commandBinder.issueCommand(new d.M({resizeDimensions:[{property:d.P.width,setDimension:t,duration:0},{property:d.P.height,setDimension:e,duration:0}]})),await this.engine.commandBinder.issueCommand(new o.TH);const a=this.engine.subscribe(r.NR,(()=>{a.cancel(),this.state===c.RECORDING&&this.stop()})),h=this,l=this.canvasModule.element;this.engine.startGenerator((function*(){for(;h.state===c.RECORDING;)h.encoder.addFrame(l),yield new p.Jj,h.frostMage.tick(),yield new p.Jj}))}async stop(){if(this.state!==c.RECORDING)return void this.log.warn("Can't stop recording, we weren't recording at all");this.frostMage.resetTime(),this.state=c.STOPPED,await this.engine.commandBinder.issueCommand(new d.M((0,u.lb)(0))),this.log.info("Encoding tour to video...");const t=await this.encoder.complete();this.log.info("Tour encoded! Prompting user to download."),(0,h.Hx)(t,"tour.webm")}}},41211:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>dt});var s=i(81396),n=i(97542),a=i(92810),o=i(89570),r=i(69505),h=i(2569),d=i(90304),l=i(9037),c=i(85992),u=i(10777),p=i(76609),m=i(23254),g=i(41326),v=i(59370);const f=new s.Vector3(0,-100,0),y=new s.Vector2(-2,-2),w=new s.Vector3(-1,-1,-1),b=new s.Vector3(1,1,1);class S{constructor(t){this.raycaster=t,this.currentRay=new s.Ray,this.origin=f,this.direction=new s.Vector3,this.pointerNdc=y,this.cameraCache={position:new s.Vector3,quaternion:new s.Quaternion,camera:void 0},this.cast=t=>{const e=this.currentRay,i=this.raycaster.cast(e.origin,e.direction,t).slice();return i.length&&(this.lastHit=i[0]),i},this.update3D=(t,e,i)=>{t instanceof s.Vector3&&this.origin.copy(t),e instanceof s.Vector3&&this.direction.copy(e),this.cameraCache.camera=i,this.currentRay.set(this.origin,this.direction)},this.updateNDCPosition=()=>{const t=this.lastHit;if(this.cameraCache.camera&&t&&t.point){const e=this.cameraCache.camera;this.cameraCache.position.setFromMatrixPosition(e.matrixWorld),this.cameraCache.quaternion.setFromRotationMatrix(e.matrixWorld);const i=(0,v.D_)(t.point,this.cameraCache.position,this.cameraCache.quaternion,e.projectionMatrix);i.clamp(w,b),this.pointerNdc.set(i.x,i.y)}},this.updatePointer=()=>{}}get pointerRay(){return this.currentRay}get ndcPosition(){return this.updateNDCPosition(),this.pointerNdc}}var D=i(98010);class I extends D.v0{}class P extends I{constructor(t){super(),this.trackedCamera=t}}class T extends I{}class M extends I{}var E,x=i(81456),C=i(13693),R=i(17878);!function(t){t[t.Mono=0]="Mono",t[t.Stereo=1]="Stereo",t[t.SixDof=2]="SixDof",t[t.__length=3]="__length"}(E||(E={}));const A={controllers:Object.freeze([0,1]),rotationDegrees:25};var L;!function(t){t[t.touchpadX=0]="touchpadX",t[t.touchpadY=1]="touchpadY",t[t.thumbstickX=2]="thumbstickX",t[t.thumbstickY=3]="thumbstickY"}(L||(L={}));const O={x:0,y:0,z:0,w:1};class k extends C.Y{constructor(t,e,i,n){super(),this.renderer=t,this.webglScene=e,this.cameraData=i,this.cameraModule=n,this.trackingStyle=E.Mono,this.session=null,this.rotations={initialYawOffset:new s.Quaternion,yawOffset:new s.Quaternion,invYawOffset:new s.Quaternion,trackingOffset:new s.Quaternion},this.setTrackingStyle=(t,e=!0)=>{t<E.__length&&(this.trackingStyle=t,e&&this.resetInitialRotation())},this.offsetRotation=t=>{this.rotations.invYawOffset.multiply(t),this.rotations.yawOffset.copy(this.rotations.invYawOffset).invert()},this.onPresentStart=t=>{t.cameras[0].layers.mask=R.o.ALL.mask,t.cameras[1].layers.mask=R.o.ALL.mask,t.cameras[0].far=t.far,t.cameras[1].far=t.far,t.layers.mask=R.o.ALL.mask,this.resetInitialRotation(),this.broadcast(new T)},this.resetInitialRotation=()=>{const t=this.cameraData.pose.rotation.clone();(0,h.Rq)(t,this.rotations.initialYawOffset),this.rotations.yawOffset.copy(this.rotations.initialYawOffset),this.rotations.invYawOffset.copy(this.rotations.initialYawOffset).invert()},this.onPresentEnd=()=>{this.broadcast(new M)},this.applyTrackingOverrides=(t,e,i)=>{if(t.xr.isPresenting&&i instanceof s.ArrayCamera){t.clear();const e=i.cameras[0],s=i.cameras[1];if(this.frame=this.renderer.xr.getFrame(),this.session||(this.session=t.xr.getSession(),this.onPresentStart(i)),this.frame&&this.session){this.rotations.trackingOffset.copy(this.rotations.yawOffset).multiply(i.quaternion),this.cameraModule.updateCameraRotation(this.rotations.trackingOffset);const t=this.webglScene.camera.parent;if(t){const e=this.getReferenceSpace(this.frame,t);this.updateCameras(i,this.frame,e,t),this.updateControllers(this.session,this.frame,e,t)}switch(this.trackingStyle){case E.Mono:s.matrixWorld.copy(e.matrixWorld),s.matrixWorldInverse.copy(s.matrixWorld),s.matrixWorldInverse.invert()}this.broadcast(new P(e))}}else this.session&&(this.session=null,this.onPresentEnd())},this.getReferenceSpace=(t,e)=>{const i=this.renderer.xr.getReferenceSpace(),s=t.getViewerPose(i).views[0].transform.position,n=this.trackingStyle===E.SixDof?O:s,a=new XRRigidTransform({x:n.x-e.position.x,y:n.y-e.position.y,z:n.z-e.position.z}),o=i.getOffsetReferenceSpace(a),r=this.rotations.invYawOffset,h=this.cameraData.pose.position,d=new XRRigidTransform({x:h.x,y:h.y,z:h.z,w:1},{x:r.x,y:r.y,z:r.z,w:r.w});return o.getOffsetReferenceSpace(d)},this.updateCameras=(t,e,i,s)=>{const n=e.getViewerPose(i);if(n){const e=n.views;for(let i=0;i<e.length;i++){const n=e[i],a=t.cameras[i];a.matrix.fromArray(n.transform.matrix),a.projectionMatrix.fromArray(n.projectionMatrix),a.projectionMatrixInverse.copy(a.projectionMatrix),a.projectionMatrixInverse.invert(),a.matrixWorld.multiplyMatrices(s.matrixWorld,a.matrix),a.matrixWorldInverse.copy(a.matrixWorld),a.matrixWorldInverse.invert(),a.matrix.decompose(a.position,a.quaternion,a.scale),0===i&&(t.matrix.copy(a.matrix),t.matrix.decompose(t.position,t.quaternion,t.scale),t.matrixWorld.copy(a.matrixWorld),t.matrixWorldInverse.copy(a.matrixWorldInverse),t.projectionMatrix.copy(a.projectionMatrix),t.projectionMatrixInverse.copy(a.projectionMatrixInverse))}}},this.updateControllers=(t,e,i,s)=>{for(let n=0;n<2;n++){const a=this.renderer.xr.getController(n),o=this.renderer.xr.getControllerGrip(n),r=t.inputSources[n];let h=null,d=null;r&&(a&&(h=e.getPose(r.targetRaySpace,i),h&&(a.matrix.fromArray(h.transform.matrix),a.matrixWorld.multiplyMatrices(s.matrixWorld,a.matrix),a.matrix.decompose(a.position,a.quaternion,a.scale)),a.visible=!!h),o&&r.gripSpace&&(d=e.getPose(r.gripSpace,i),d&&(o.matrix.fromArray(d.transform.matrix),o.matrixWorld.multiplyMatrices(s.matrixWorld,o.matrix),o.children.forEach((t=>{t.updateMatrixWorld()})),o.matrix.decompose(o.position,o.quaternion,o.scale)))),o&&(o.visible=!!d)}},this.webglScene.scene.onBeforeRender=this.applyTrackingOverrides}}var F=i(37082),N=i(87928),V=i(46319),B=i(48786),G=i(20043),_=i(35826),H=i(8948),U=i(53954),W=i(5135),j=i(13310),z=i(87926);const $=i.p+"images/selected_sweep_glow.png";class q{constructor(t){this.meshQuery=t,this.active=!1,this.target=new F.f(null),this.previousSweep=null,this.activate=()=>{this.active||(this.updateLoopSub.renew(),this.selectionChangeSub.renew(),this.ray.toggle(!0),this.targetDecoration.toggle(!0),this.active=!0)},this.deactivate=()=>{this.active&&(this.target.value=null,this.selectionChangeSub.cancel(),this.updateLoopSub.cancel(),this.ray.toggle(!1),this.targetDecoration.toggle(!1),this.active=!1)},this.getTargetSweep=(t,e,i)=>{let s=null;if(e.object instanceof H.Y)return s=t.getSweep(e.object.userData.sid),s;const n=(0,G.bG)(t,!0,e.intersection,this.meshQuery);return s=n.length>0?n[0].sweep:null,s}}get container(){return this._container}async init(t){this.engine=t,this._container=new s.Group,this._container.name="XRNavigationVisuals",this.ray=new Y(this.container),this.targetDecoration=new Z(this.container);const e=await this.engine.market.waitForData(c.P);return this.updateLoopSub=e.onChanged((()=>this.update(e))),this.updateLoopSub.cancel(),this.selectionChangeSub=this.target.onChanged((t=>{null!==this.previousSweep&&this.engine.commandBinder.issueCommand(new _.kR(this.previousSweep.id,!1,200)),null!==t&&(this.engine.commandBinder.issueCommand(new _.kR(t.id,!0,200)),this.targetDecoration.updateTargetPosition(t.floorPosition)),this.previousSweep=t})),this.selectionChangeSub.cancel(),this}update(t){const e=this.engine.market.tryGetData(U.Z),i=this.engine.market.tryGetData(l.M),s=this.engine.market.tryGetData(m.O),n=this.engine.market.tryGetData(j.Y),a=this.engine.market.tryGetData(W.Z);if(!(e&&i&&s&&a&&n))return;if(!a.isVR())return;if(!s.isInside())return;const{hit:o,pointerDirection:r,pointerOrigin:h}=t;if(!o)return;const d=this.getTargetSweep(e,o,r);this.target.value=d,this.ray.update(h,o.point,n.opacity.value),this.targetDecoration.update(i.pose.rotation,n.opacity.value)}}class Y{constructor(t){this.container=t,this.styles={ray:{color:"white",transparent:!0,opacity:.3,linewidth:2,depthWrite:!1},hit:{color:"white",transparent:!0,opacity:.3},hitScale:.02},this.update=(t,e,i)=>{this.ray.updatePositions(t,e).opacity(Math.min(i,this.styles.ray.opacity)),this.hitMarker.position.copy(e),this.hitMarker.material.opacity=Math.min(i,this.styles.hit.opacity)},this.toggle=t=>{t?(this.container.add(...this.ray.children),this.container.add(this.hitMarker)):(this.container.remove(...this.ray.children),this.container.remove(this.hitMarker))};const{ray:e,hit:i}=this.styles,n=(0,V.makeLineMaterial)(e.color,!1,e);this.ray=new B.c(new s.Vector3,new s.Vector3,n,{}),this.ray.updateResolution(window.innerWidth,window.innerHeight),this.hitMarker=new N.E(new s.SphereGeometry(this.styles.hitScale),new s.MeshBasicMaterial(i)),this.hitMarker.name="hit"}}class Z{constructor(t){this.container=t,this.styles={scale:.46,animationSpeed:1,plane:{color:"white",transparent:!0,opacity:.6,depthWrite:!1,depthTest:!1,map:(0,z.p)($)}},this.position=new s.Vector3,this.quaternion=new s.Quaternion,this.updateTargetPosition=t=>{this.position.copy((0,h.Xv)(t,d.fU.UP,.05))},this.update=(t,e)=>{const i=(0,h.Rq)(t,this.quaternion);this.target.quaternion.copy(i),this.target.position.lerp(this.position,this.styles.animationSpeed),this.target.material.opacity=Math.min(e,this.styles.plane.opacity)},this.toggle=t=>{t?this.container.add(this.target):this.container.remove(this.target)};const e=new s.PlaneGeometry(1),i=new s.Matrix4;i.makeRotationFromEuler(new s.Euler(-Math.PI/2,0,0,"XYZ")),e.applyMatrix4(i),this.target=new N.E(e,new s.MeshBasicMaterial(this.styles.plane)),this.target.name="Destination",this.target.scale.set(this.styles.scale,this.styles.scale,this.styles.scale)}}class X{constructor(t){this.controllers=t,this._lastInputWas=0}focus(t){t!==this._lastInputWas&&(A.controllers.forEach((e=>{this.controllers.controller(e).grip.visible=e!==t})),this._lastInputWas=t)}}var K=i(35895);class Q{constructor(t){this.renderer=t,this._defaultController=0,this.controllerGroups=[],this.bindings=[],this.cancel=()=>{this.bindings.forEach((t=>t.cancel())),this.bindings.length=0},this.container=new s.Group,this.container.name="XRControllerMesh",A.controllers.forEach((t=>{const e=this.createControllerGroup(t);this.controllerGroups.push(e),this.container.add(e.grip,e.pointer)})),this.connectControllerModel(),this.container.matrixAutoUpdate=!1}controller(t=this._defaultController){return this.controllerGroups[t]}setDefault(t){this._defaultController=t}async connectControllerModel(){const t=new((await Promise.all([i.e(217),i.e(376)]).then(i.bind(i,92583))).XRControllerModelFactory);A.controllers.forEach((e=>{const i=this.controller(e).grip;i.add(t.createControllerModel(i))}))}createControllerGroup(t){const e=this.renderer.xr.getController(t);e.name=`Controller Ray ${t}`;const i=this.renderer.xr.getControllerGrip(t);i.name=`Controller Grip ${t}`,i.visible=!1;const s={index:t,pointer:e,grip:i,connected:!1,hand:"none"},n=t=>{s.hand=t.data.handedness,s.connected=!0},a=()=>{s.hand="none",s.connected=!1};return this.bindings.push((0,K.k1)((()=>e.addEventListener("connected",n)),(()=>e.removeEventListener("connected",n))),(0,K.k1)((()=>e.addEventListener("disconnected",a)),(()=>e.removeEventListener("disconnected",a)))),s}}const J=new(i(97998).Z)("xr-input-forwarding");class tt{constructor(t){this.options=t,this.dispatchPointerDown=t=>{this.forwardEvent("pointerdown",this.mockPointerEventInit(t))},this.dispatchPointerUp=t=>{this.forwardEvent("pointerup",this.mockPointerEventInit(t))},this.target=t.forwardToElement}dispatchPointerMove(t){this.forwardEvent("pointermove",this.mockPointerEventInit(t))}mockPointerEventInit(t){let e=0,i=0;if(this.options.getPointerScreenPosition){const t=this.options.getPointerScreenPosition();e=t.x,i=t.y}return{pointerType:"gamepad",pointerId:t,clientX:e,clientY:i}}forwardEvent(t,e){let i;try{i=window.PointerEvent?new PointerEvent(t,e):new MouseEvent(t,e),i&&this.target.dispatchEvent(i)}catch(t){J.error(t)}}}class et extends s.EventDispatcher{constructor(t,e){super(),this.renderer=t,this.options={forwardNativeXrEvents:!0,dispatchToControllerGroup:!1,axisMoveTriggerThreshold:.5},this.previousGamepad=new Map,this.forwardedEvents=["selectstart","select","selectend","squeeze","squeezestart","squeezeend"],this.active=!1,this.renew=()=>{!this.active&&this.options.forwardNativeXrEvents&&(this.addSessionListeners(),this.active=!0)},this.cancel=()=>{this.active&&this.options.forwardNativeXrEvents&&(this.removeSessionListeners(),this.active=!1)},this.updateFromGamepads=()=>{if(!this.active)return;const t=this.renderer.xr.getSession();if(t)for(const e of A.controllers){const i=t.inputSources[e];if(!i||!i.gamepad)continue;const s=this.renderer.xr.getController(e),n=this.previousGamepad.get(i),a={buttons:i.gamepad.buttons.map((t=>t.value)),axes:new Float32Array(i.gamepad.axes.slice())},o={controllerIndex:e,inputSource:i,axes:a.axes};n&&(a.buttons.forEach(((t,e)=>{t!==n.buttons[e]&&(1===t?this.sendGamepadEvent(s,Object.assign(Object.assign({},o),{type:"buttondown",value:t,index:e,target:s})):0===t&&this.sendGamepadEvent(s,Object.assign(Object.assign({},o),{type:"buttonup",value:t,index:e,target:s})))})),a.axes.forEach(((t,e)=>{const i=n.axes[e];if(t!==i){this.sendGamepadEvent(s,Object.assign(Object.assign({},o),{type:"axesmove",value:t,index:e,target:s})),0===i&&this.sendGamepadEvent(s,Object.assign(Object.assign({},o),{type:"axesmovestart",value:t,index:e,target:s}));const n=this.options.axisMoveTriggerThreshold;Math.abs(i)<n&&Math.abs(t)>n&&this.sendGamepadEvent(s,Object.assign(Object.assign({},o),{type:"axestriggered",value:t,index:e,target:s})),0===t&&this.sendGamepadEvent(s,Object.assign(Object.assign({},o),{type:"axesmoveend",value:t,index:e,target:s}))}}))),this.previousGamepad.set(i,a)}},this.onGamepadEvent=(t,e)=>(0,K.k1)((()=>super.addEventListener(t,e)),(()=>super.removeEventListener(t,e))),this.onSessionEvent=(t,e)=>(0,K.k1)((()=>super.addEventListener(t,e)),(()=>super.removeEventListener(t,e))),this.sendGamepadEvent=(t,e)=>{this.dispatchEvent(e),this.options.dispatchToControllerGroup&&t.dispatchEvent(e)},this.sendSessionEvent=(t,e)=>{this.dispatchEvent({type:t,controllerIndex:e})},this.addSessionListeners=()=>{A.controllers.forEach((t=>{const e=this.renderer.xr.getController(t);for(const i of this.forwardedEvents)e.addEventListener(i,(e=>this.sendSessionEvent(e.type,t)))}))},this.removeSessionListeners=()=>{A.controllers.forEach((t=>{const e=this.renderer.xr.getController(t);for(const i of this.forwardedEvents)e.removeEventListener(i,(e=>this.sendSessionEvent(e.type,t)))}))},e&&(this.options=Object.assign(Object.assign({},this.options),e)),this.renew()}}var it=i(11642),st=i(44578),nt=i(31362);var at=i(8807);const ot=(new s.Quaternion).setFromAxisAngle(d.fU.UP,r.Ue*A.rotationDegrees),rt=(new s.Quaternion).setFromAxisAngle(d.fU.UP,r.Ue*-A.rotationDegrees);class ht extends n.Y{constructor(){super(...arguments),this.name="webxr",this.framebufferScaledTo=1,this.framebufferScale=0,this.ray={forward:new s.Vector3,origin:new s.Vector3},this.onXrPresentBegin=()=>{const t=this.renderer.xr.getSession();t&&(this.log.info(`Session framebuffer: ${t.renderState.baseLayer}`),this.engine.commandBinder.issueCommand(new u.F(p.SL.HIGH)),this.viewmodeData.isInside()||this.engine.commandBinder.issueCommand(new g._i(g.BD.INSIDE)),this.xrPointer=new S(this.raycaster.picking),this.raycaster.setOverridePointer(this.xrPointer),this.xrNavVisuals.activate())},this.onXrPresentEnd=()=>{this.engine.commandBinder.issueCommand(new u.F(null)),this.xrNavVisuals.deactivate(),this.raycaster.setOverridePointer(null),this.cameraModule.updateCameraRotation((0,h.Rq)(this.cameraData.pose.rotation,new s.Quaternion)),this.webglScene.setCameraDirty()},this.onXrTrackingApplied=t=>{const e=this.controllerMesh.controller();if(e.connected){const i=this.ray.forward.copy(d.fU.FORWARD).applyQuaternion(e.pointer.quaternion),s=this.ray.origin.setFromMatrixPosition(e.pointer.matrixWorld);this.xrPointer.update3D(s,i,t.trackedCamera),this.xrPointerInput.dispatchPointerMove(e.index)}this.xrGamepadInput.updateFromGamepads()},this.tryEndSession=async()=>{var t;await(null===(t=this.activeXrSession)||void 0===t?void 0:t.end()),this.activeXrSession=void 0},this.requestSession=async(t,e)=>{var i;if(await(0,it.pl)(this.config.xrBrowsersUnlocked)!==it.bk.webxr)return null;if(this.renderer.xr.isPresenting)return this.renderer.xr.getSession();if(st.Z.apiExists()){const s=await(null===(i=navigator.xr)||void 0===i?void 0:i.requestSession(t,{optionalFeatures:e}));if(!s)return null;this.activeXrSession=s;const n=XRWebGLLayer.getNativeFramebufferScaleFactor(s);return this.framebufferScaledTo=this.framebufferScale*(n-1)+1,this.log.info("Scaling framebuffer by:",this.framebufferScaledTo,"native size:",n," * factor:",this.framebufferScale),0!==this.framebufferScale&&this.renderer.xr.setFramebufferScaleFactor(this.framebufferScaledTo),this.renderer.xr.setSession(s),s}return null}}async init(t,e){this.config=t,this.engine=e;const[i,s]=await Promise.all([e.getModuleBySymbol(a.Aj),e.getModuleBySymbol(a.hi)]);this.renderer=i.threeRenderer,this.webglScene=i.getScene(),this.bindings.push(e.commandBinder.addBinding(x.j,(t=>this.requestSession(t.type,t.features))),e.commandBinder.addBinding(x.A,(()=>this.tryEndSession())));if(await(0,it.pl)(t.xrBrowsersUnlocked)!==it.bk.webxr)return;[this.canvasModule,this.cameraModule,this.raycaster]=await Promise.all([e.getModuleBySymbol(a.iM),e.getModuleBySymbol(a.kg),e.getModuleBySymbol(a.fQ)]),[this.cameraData,this.raycasterData,this.viewmodeData,this.policyData]=await Promise.all([e.market.waitForData(l.M),e.market.waitForData(c.P),e.market.waitForData(m.O),e.market.waitForData(at.n)]),this.renderer.xr.setReferenceSpaceType("local"),this.framebufferScale=this.config.framebufferScaling||function(t){const e=(0,nt.tq)();return!e||e&&function(t){return/Adreno \(TM\) (540|[6-9]\d\d)/.test(t.renderer)}(t)?1:0}(i.gpuInfo);const n=new k(this.renderer,this.webglScene,this.cameraData,this.cameraModule);n.setTrackingStyle(t.tracking),this.bindings.push(n.subscribe(T,this.onXrPresentBegin),n.subscribe(M,this.onXrPresentEnd),n.subscribe(P,this.onXrTrackingApplied)),this.controllerMesh=new Q(this.renderer),this.webglScene.add(this.controllerMesh.container);const r=new X(this.controllerMesh),h=t.enableEventPositions?()=>this.raycasterData.pointerScreenPosition:void 0;this.xrPointerInput=new tt({forwardToElement:this.canvasModule.element,getPointerScreenPosition:h}),this.xrGamepadInput=new et(this.renderer);const d=[this.xrGamepadInput.onGamepadEvent("axestriggered",(t=>{if(t.index===L.thumbstickX||t.index===L.touchpadX){this.log.debug(`${t.inputSource.handedness} ${L[t.index]} axis.value over threshold, do the rotate!`);Math.sign(t.value)>0?n.offsetRotation(ot):n.offsetRotation(rt)}r.focus(t.controllerIndex),this.controllerMesh.setDefault(t.controllerIndex)})),this.xrGamepadInput.onSessionEvent("selectstart",(t=>{this.xrPointerInput.dispatchPointerDown(t.controllerIndex),r.focus(t.controllerIndex),this.controllerMesh.setDefault(t.controllerIndex)})),this.xrGamepadInput.onSessionEvent("selectend",(t=>{this.xrPointerInput.dispatchPointerUp(t.controllerIndex),r.focus(t.controllerIndex),this.controllerMesh.setDefault(t.controllerIndex)})),this.xrGamepadInput.onSessionEvent("squeezestart",(t=>{r.focus(t.controllerIndex),this.controllerMesh.setDefault(t.controllerIndex)}))];this.policyData.hasPolicy("spaces.sdk.qa")&&d.push(this.xrGamepadInput.onGamepadEvent("buttondown",(e=>{4===e.index?(t.tracking=(t.tracking+1)%E.__length,n.setTrackingStyle(t.tracking,!1)):5===e.index&&(t.tracking=(E.__length+t.tracking-1)%E.__length,n.setTrackingStyle(t.tracking,!1)),r.focus(e.controllerIndex),this.controllerMesh.setDefault(e.controllerIndex)})));const u=new o.V(...d);this.bindings.push(u,this.xrGamepadInput),this.xrNavVisuals=new q(s),this.xrNavVisuals.init(e),this.webglScene.add(this.xrNavVisuals.container)}}const dt=ht},83402:(t,e,i)=>{"use strict";i.d(e,{l1:()=>o,r2:()=>d});var s=i(81396),n=i(69505),a=i(90304);var o;!function(t){t[t.Axes=1]="Axes",t[t.PlanarAxes=2]="PlanarAxes",t[t.EdgesAndPlanarAxes=3]="EdgesAndPlanarAxes",t[t.Edges=4]="Edges",t[t.Free=5]="Free",t[t.Locked=6]="Locked"}(o||(o={}));const r={[a.eD.UP]:{dir:Object.freeze(a.fU.UP.clone())},[a.eD.DOWN]:{dir:Object.freeze(a.fU.DOWN.clone())},[a.eD.BACK]:{dir:Object.freeze(a.fU.BACK.clone())},[a.eD.LEFT]:{dir:Object.freeze(a.fU.LEFT.clone())},[a.eD.RIGHT]:{dir:Object.freeze(a.fU.RIGHT.clone())},[a.eD.FORWARD]:{dir:Object.freeze(a.fU.FORWARD.clone())}},h={[a.eD.HORIZONTAL_PLANE]:{dir:Object.freeze(a.fU.HORIZONTAL_PLANE.clone())}},d=(()=>{let t=0;const e=new s.Vector3,i=new s.Vector3,d=new s.Vector3,l=new s.Vector3,c=999,u=Object.freeze(a.fU.ZERO.clone()),p=Object.keys(r).map((t=>Object.assign(Object.assign({},r[t]),{name:t,angleTo:c}))),m=Object.keys(h).map((t=>Object.assign(Object.assign({},h[t]),{name:t,angleTo:c}))),g=t=>(t+3)%2;let v=p[0];return(s,r,h=o.Axes)=>{if(h===o.Free)return{position:r.clone(),constrainedAxis:u,axisName:a.eD.NONE};if(p.forEach((t=>t.angleTo=c)),m.forEach((t=>t.angleTo=c)),d.copy(r).sub(s),t=d.length(),t<.05)return{position:r.clone(),constrainedAxis:u,axisName:a.eD.NONE};l.copy(d).normalize();const f=.05*Math.min(t,1);if(h!==o.Locked){for(const t of p){const e=(0,n.ZY)(l.angleTo(t.dir));e<v.angleTo&&(v=t),t.angleTo=e}if(v.angleTo>20){const t=Math.abs(d.y);t*t<f&&(v=m[0])}}e.set(s.x*g(v.dir.x),s.y*g(v.dir.y),s.z*g(v.dir.z)),i.set(r.x*Math.abs(v.dir.x),r.y*Math.abs(v.dir.y),r.z*Math.abs(v.dir.z)).add(e);return h===o.Locked||i.distanceToSquared(r)<f?{position:i.clone(),constrainedAxis:v.dir,axisName:v.name}:{position:r.clone(),constrainedAxis:u,axisName:a.eD.NONE}}})()},67971:(t,e,i)=>{"use strict";i.d(e,{$7:()=>l,uc:()=>u,N3:()=>d});var s=i(81396),n=i(4679),a=i(59370),o=i(69505),r=i(49827),h=i(73121);var d;(0,n.Dy)({unicodeFontsURL:"https://static.matterport.com/webgl-vendors/unicode-font-resolver/1.0.1/"}),function(t){t.WORLD="world",t.NDC="ndc"}(d||(d={}));class l extends s.Object3D{constructor(t,e=d.WORLD){var i,a,o,r;super(),this.config=t,this.scaleType=e,this.unscaledWidth=0,this.unscaledHeight=0,this.labelTextMaterial=new s.MeshBasicMaterial,this.bindings=[];const h=this.config.background||this.config.backgroundAsCollider;if(h){this.config.backgroundOpacity=void 0!==this.config.backgroundOpacity?this.config.backgroundOpacity:1,this.config.backgroundOpacity=this.config.background?this.config.backgroundOpacity:0;const e=new s.BoxGeometry(1,1,.01),i=new s.MeshBasicMaterial({color:t.backgroundColor,transparent:!0,depthTest:this.config.backgroundOpacity>0&&!t.disableDepth,depthWrite:this.config.backgroundOpacity>0&&!t.disableDepth,opacity:this.config.backgroundOpacity,stencilRef:1,stencilFail:s.KeepStencilOp,stencilZFail:s.KeepStencilOp,stencilZPass:s.ReplaceStencilOp,stencilFunc:s.AlwaysStencilFunc,stencilWrite:!0});this.labelBackgroundMesh=new this.config.backgroundColliderType(e,i),this.labelBackgroundMesh.position.z=-.01,this.labelBackgroundMesh.name="Label Background",this.collider=this.labelBackgroundMesh,this.add(this.labelBackgroundMesh)}const l=this.labelTextMesh=new n.xv;l.material=this.labelTextMaterial,l.name="Label Text",l.text=t.text||"",l.renderOrder=10,l.font=`${null!==(i=t.assetBasePath)&&void 0!==i?i:""}${t.fontPath}`,l.lang=t.lang,l.fontSize=1,l.fontWeight=700,l.anchorX="50%",l.anchorY="50%",l.outlineWidth=t.outline?t.outlineWidth:0,l.maxWidth=t.wordWrapWidth,l.textAlign=t.align,l.depthOffset=t.depthOffset||0,h&&(l.raycast=()=>{}),l.addEventListener("synccomplete",(()=>{var t;const[e,i,s,n]=l.textRenderInfo.visibleBounds;let a=s-e,o=n-i;h&&(a+=this.config.backgroundBorderWidth,o+=this.config.backgroundBorderHeight,this.labelBackgroundMesh.scale.set(a,o,1)),this.unscaledWidth=a,this.unscaledHeight=o,this.aspect=a/Math.max(o,.001),l.position.set((e+s)/-2,(i+n)/-2,0),null===(t=this._onGeomUpdate)||void 0===t||t.call(this)})),this.scaleFactor=null!==(a=t.scale)&&void 0!==a?a:1,this.opacity=null!==(o=t.opacity)&&void 0!==o?o:1,this.setColor(null!==(r=t.color)&&void 0!==r?r:0),this.add(l),l.sync(),this.name="Label Container"}dispose(){this.bindings.forEach((t=>t.cancel())),this.labelTextMesh.dispose()}onGeomUpdate(t){this._onGeomUpdate=t}get text(){return this.config.text}set text(t){this.config.text=t,this.labelTextMesh.text=t,this.labelTextMesh.sync()}get mesh(){return this.labelTextMesh}getUnscaledSize(){return{width:this.unscaledWidth,height:this.unscaledHeight}}get scaleFactor(){return this.config.scale}set scaleFactor(t){this.config.scale=t,this.scale.setScalar(t)}get opacity(){return void 0!==this.config.opacity?this.config.opacity:1}set opacity(t){if(t!==this.config.opacity){this.config.opacity=t;const e=t>0&&!this.config.disableDepth;if(this.config.background){const i=this.labelBackgroundMesh.material;i.opacity=Math.min(this.config.backgroundOpacity||1,t),i.depthWrite=t>.15,i.depthTest=e}const i=this.labelTextMaterial;i.opacity=t,i.depthTest=e,this.visible=t>0}}setColor(t){this.labelTextMaterial.color.set(t)}setRenderLayer(t){this.labelTextMesh.layers.mask=t.mask,this.labelBackgroundMesh&&(this.labelBackgroundMesh.layers.mask=t.mask)}setRenderOrder(t){this.renderOrder=t,this.labelTextMesh.renderOrder=t,this.labelBackgroundMesh&&(this.labelBackgroundMesh.renderOrder=t)}setPosition(t,e=(t=>t)){this.position.copy(e(t))}setOrientation(t,e=0){this.quaternion.copy(t),0!==e&&this.rotateZ(-e*o.Ue)}scaleBillboard(t,e,i,s,n,o,l=c.SCALE_DEFAULT){if(0!==i.elements[15])this.scaleFactor=.2*l*s*(c.ORTHO_IDEAL_HEIGHT/n);else{const u=(0,a.D_)(this.position,t,e,i.asThreeMatrix4()),p=Math.abs(u.x);if(p<1){const e=(0,h.mY)(i,t,this.position,n,l),a=((0,r.uZ)(o,1,2.5)+s)*c.SCALE_ASPECT,u=1+c.SCALE_NDC-p*c.SCALE_NDC-a,m=Math.max(Math.min(1/e*u,3),.001);this.scaleType===d.NDC?this.scaleFactor=m:this.scaleFactor=Math.min(m*c.NDC_MULT,l*c.SCALE_WORLD)}else this.scaleFactor=.001}}}const c={SCALE_DEFAULT:.1,SCALE_WORLD:4,SCALE_NDC:.5,SCALE_ASPECT:.035,DEPTH_WRITE_THRESHOD:.15,ORTHO_IDEAL_HEIGHT:1500,NDC_MULT:1.15};class u{constructor(t){this.currentTextConfig=u.defaultTextConfig,t?this.updateTextStyle(t):this.updateTextStyle(u.defaultTextConfig)}updateTextStyle(t){this.currentTextConfig=Object.assign(Object.assign({},this.currentTextConfig),t)}createLabel(t={text:""}){return new l(Object.assign(Object.assign({},this.currentTextConfig),t))}async preload(t={text:""}){const e=Object.assign(Object.assign({},this.currentTextConfig),t);return new Promise((t=>{var i;(0,n.C5)({font:`${null!==(i=e.assetBasePath)&&void 0!==i?i:""}${e.fontPath}`,characters:e.text},t)}))}static makeConfig(t){return Object.assign(Object.assign({},u.defaultTextConfig),t)}}u.defaultTextConfig={text:"",fontPath:"fonts/roboto-700.woff",align:"center",wordWrapWidth:void 0,color:"black",backgroundColor:"white",backgroundBorderWidth:.9,backgroundBorderHeight:.7,background:!0,backgroundAsCollider:!0,backgroundColliderType:s.Mesh,scale:1,outline:!1,outlineWidth:.06}},73868:t=>{t.exports="precision highp float;uniform float paddingPx;uniform float aaPaddingPx;uniform vec3 color;uniform float opacity;varying vec2 vOffsetPx;uniform float outline;uniform vec3 outlineColor;varying vec2 scaledWidthHeightPx;float sdTriangle(vec2 p,vec2 p0,vec2 p1,vec2 p2){vec2 e0=p1-p0;vec2 e1=p2-p1;vec2 e2=p0-p2;vec2 v0=p-p0;vec2 v1=p-p1;vec2 v2=p-p2;vec2 pq0=v0-e0*clamp(dot(v0,e0)/dot(e0,e0),0.,1.);vec2 pq1=v1-e1*clamp(dot(v1,e1)/dot(e1,e1),0.,1.);vec2 pq2=v2-e2*clamp(dot(v2,e2)/dot(e2,e2),0.,1.);float s=e0.x*e2.y-e0.y*e2.x;vec2 d=min(min(vec2(dot(pq0,pq0),s*(v0.x*e0.y-v0.y*e0.x)),vec2(dot(pq1,pq1),s*(v1.x*e1.y-v1.y*e1.x))),vec2(dot(pq2,pq2),s*(v2.x*e2.y-v2.y*e2.x)));return-sqrt(d.x)*sign(d.y);}void main(){float padding=aaPaddingPx+outline;float halfWidth=scaledWidthHeightPx.x/2.;vec2 p0=vec2(-halfWidth,scaledWidthHeightPx.y+padding);vec2 p1=vec2(halfWidth,scaledWidthHeightPx.y+padding);vec2 p2=vec2(0.,padding);float sd=sdTriangle(vOffsetPx,p0,p1,p2);float aaOpacity=1.-smoothstep(outline,outline+0.5,sd);float colorMix=smoothstep(0.,outline,sd);vec3 colorWithOutline=mix(color,outlineColor,colorMix);gl_FragColor=vec4(colorWithOutline,aaOpacity*opacity);}"},25888:t=>{t.exports="precision highp float;uniform vec2 screenSize;vec2 rotate90(vec2 v){return vec2(-v.y,v.x);}vec2 ndcToScreen(vec4 pt){return pt.xy*screenSize/2.;}vec2 screenToNdc(vec2 pt){return pt*2./screenSize;}\n#define MIN_SCALE  0.4\n#define MIN_METERS_PER_PX  0.006\n#define MAX_METERS_PER_PX  0.012\nuniform float paddingPx;uniform float widthPx;uniform float heightPx;uniform float aaPaddingPx;uniform float metersPerPx;uniform float outline;uniform vec2 tip;uniform vec2 normal;uniform float height;attribute vec2 offset;uniform mat4 projectionMatrix;uniform mat4 modelViewMatrix;varying vec2 vOffsetPx;varying vec2 scaledWidthHeightPx;vec2 scaleByZoom(){float t=clamp((metersPerPx-MIN_METERS_PER_PX)/(MAX_METERS_PER_PX-MIN_METERS_PER_PX),0.,1.);float scale=mix(1.,MIN_SCALE,t);return vec2(widthPx,heightPx)*scale;}void main(){vec2 yAxis=normal;vec2 xAxis=rotate90(yAxis);vec4 tipWorld=vec4(tip.x,height,tip.y,1.);vec4 xOffsetWorld=tipWorld+vec4(xAxis.x,0.,xAxis.y,0.);vec4 yOffsetWorld=tipWorld+vec4(yAxis.x,0.,yAxis.y,0.);vec4 ndcTip=projectionMatrix*modelViewMatrix*tipWorld;vec2 tipScreen=ndcToScreen(ndcTip/ndcTip.w);vec4 ndcXOffset=projectionMatrix*modelViewMatrix*xOffsetWorld;vec2 xOffsetScreen=ndcToScreen(ndcXOffset/ndcXOffset.w);vec4 ndcYOffset=projectionMatrix*modelViewMatrix*yOffsetWorld;vec2 yOffsetScreen=ndcToScreen(ndcYOffset/ndcYOffset.w);vec2 xAxisScreen=normalize(xOffsetScreen-tipScreen);vec2 yAxisScreen=normalize(yOffsetScreen-tipScreen);float padding=aaPaddingPx+outline;scaledWidthHeightPx=scaleByZoom();float halfWidth=scaledWidthHeightPx.x/2.+padding;float quadHeight=scaledWidthHeightPx.y+padding*2.;vec2 vertexScreen=(tipScreen+yAxisScreen*paddingPx)+xAxisScreen*halfWidth*offset.x+yAxisScreen*quadHeight*offset.y;vec2 ndcVert=screenToNdc(vertexScreen);vOffsetPx=vec2(offset.x*halfWidth,offset.y*quadHeight);gl_Position=vec4(ndcVert*ndcTip.w,ndcTip.z,ndcTip.w);}"},72292:t=>{t.exports="precision highp float;uniform float opacity;uniform float centerSpacing;uniform float radius;uniform vec3 color;void main(){vec2 center=mod(gl_FragCoord.xy,vec2(centerSpacing))-vec2(centerSpacing*0.5);float polkaDot=1.-smoothstep(radius-(radius*0.2),radius,length(center));gl_FragColor=vec4(color,opacity*polkaDot);}"},47706:t=>{t.exports="precision highp float;uniform mat4 projectionMatrix;uniform mat4 modelViewMatrix;attribute vec3 position;void main(){gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}"},73293:t=>{t.exports="#define ANTIALIAS_WIDTH  1.0\nprecision highp float;uniform vec3 outlineColor;uniform vec3 baseColor;uniform float radius;uniform float opacity;uniform float outlinePct;varying vec3 vPosition;void main(){float fragRadius=length(vPosition.xz);float outlineRadius=radius*outlinePct;float smoothAmt=fwidth(fragRadius)*ANTIALIAS_WIDTH;float mixAmt=smoothstep(outlineRadius-smoothAmt,outlineRadius,fragRadius);gl_FragColor=vec4(mix(baseColor,outlineColor,mixAmt),1.);gl_FragColor.a=opacity*(1.-smoothstep(radius-smoothAmt,radius,fragRadius));}"},67498:t=>{t.exports="precision highp float;uniform mat4 projectionMatrix;uniform mat4 modelViewMatrix;attribute vec3 position;varying vec3 vPosition;void main(){vPosition=position;gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}"},14536:t=>{t.exports="precision highp float;uniform vec3 baseColor;uniform float isDoor;varying vec3 vPosition;void main(){const float lineWidth=0.15;if(isDoor>0.){const float startZ=0.15;const float endZ=startZ+lineWidth;float alpha=(abs(vPosition.z)>startZ&&abs(vPosition.z)<endZ)?1.:0.;gl_FragColor=vec4(baseColor,alpha);}else{float alpha=(abs(vPosition.z)<lineWidth*0.5)?1.:0.;gl_FragColor=vec4(baseColor,alpha);}}"},86242:t=>{t.exports="precision highp float;uniform mat4 projectionMatrix;uniform mat4 modelViewMatrix;attribute vec3 position;varying vec3 vPosition;void main(){vPosition=position;gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}"},93670:t=>{t.exports="#define ANTIALIAS_WIDTH  1.0\nprecision highp float;uniform float selectedWidth;uniform vec3 outlineColor;uniform vec3 color;uniform float opacity;uniform float width;uniform vec3 lineStart;uniform vec3 lineEnd;varying vec3 vWorldPos;float distanceBetween(vec2 l1,vec2 l2,vec2 p){float D=length(l2-l1);float N=abs((l2.x-l1.x)*(l1.y-p.y)-(l1.x-p.x)*(l2.y-l1.y));return N/D;}void main(){float distanceToLine=distanceBetween(lineStart.xz,lineEnd.xz,vWorldPos.xz);float aaWidth=fwidth(distanceToLine)*ANTIALIAS_WIDTH;float lineLerp=smoothstep(selectedWidth-aaWidth,selectedWidth,distanceToLine);float halfWidth=width*0.5;float aaOpacity=smoothstep(halfWidth,halfWidth-aaWidth,distanceToLine);gl_FragColor=mix(vec4(outlineColor,opacity*aaOpacity),vec4(color,opacity*aaOpacity),lineLerp);if(gl_FragColor.a<0.01){discard;}}"},40134:t=>{t.exports="precision highp float;uniform mat4 projectionMatrix;uniform mat4 modelViewMatrix;attribute vec3 position;varying vec3 vWorldPos;void main(){vWorldPos=position;gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}"},22999:t=>{var e={kind:"Document",definitions:[{kind:"OperationDefinition",operation:"query",name:{kind:"Name",value:"GetRoomBounds"},variableDefinitions:[{kind:"VariableDefinition",variable:{kind:"Variable",name:{kind:"Name",value:"modelId"}},type:{kind:"NonNullType",type:{kind:"NamedType",name:{kind:"Name",value:"ID"}}},directives:[]}],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"model"},arguments:[{kind:"Argument",name:{kind:"Name",value:"id"},value:{kind:"Variable",name:{kind:"Name",value:"modelId"}}}],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"floors"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"layer"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"vertices"},arguments:[{kind:"Argument",name:{kind:"Name",value:"includeUsed"},value:{kind:"BooleanValue",value:!0}}],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"layer"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"position"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"x"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"y"},arguments:[],directives:[]}]}}]}},{kind:"Field",name:{kind:"Name",value:"edges"},arguments:[{kind:"Argument",name:{kind:"Name",value:"includeUsed"},value:{kind:"BooleanValue",value:!0}}],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"layer"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"type"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"vertices"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"centerLineBias"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"thickness"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"openings"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"width"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"relativeCenter"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"type"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"height"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"lowerElevation"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"layer"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}}]}}]}}]}},{kind:"Field",name:{kind:"Name",value:"rooms"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"layer"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"floor"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"classifications"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"confidence"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"label"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"boundary"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"edges"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}}]}},{kind:"Field",name:{kind:"Name",value:"holes"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"edges"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}}]}},{kind:"Field",name:{kind:"Name",value:"dimensionEstimates"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"area"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"areaIndoor"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"width"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"depth"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"height"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"units"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"label"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"keywords"},arguments:[],directives:[]}]}}]}}]}},{kind:"OperationDefinition",operation:"query",name:{kind:"Name",value:"GetRoomClassifications"},variableDefinitions:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"roomClassifications"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"label"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"defaultKeywords"},arguments:[],directives:[]}]}}]}}],loc:{start:0,end:1037}};e.loc.source={body:"query GetRoomBounds($modelId: ID!) {\n  model(id: $modelId) {\n    floors {\n      id\n      layer { id }\n      vertices(includeUsed: true) {\n        id\n        layer { id }\n        position {\n          x\n          y\n        }\n      }\n      edges(includeUsed: true) {\n        id\n        layer { id }\n        type\n        vertices { id }\n        centerLineBias\n        thickness\n        openings {\n          id\n          width\n          relativeCenter\n          type\n          height\n          lowerElevation\n          layer { id }\n        }\n      }\n    }\n    rooms {\n      id\n      layer { id }\n      floor { id }\n      classifications {\n        id\n        confidence\n        label\n      }\n      boundary {\n        edges { id }\n      }\n      holes {\n        edges { id }\n      }\n      dimensionEstimates {\n        area\n        areaIndoor\n        width\n        depth\n        height\n        units\n      }\n      label\n      keywords\n    }\n  }\n}\n\nquery GetRoomClassifications {\n   roomClassifications {\n    id\n    label\n    defaultKeywords\n  }\n}",name:"GraphQL request",locationOffset:{line:1,column:1}};function i(t,e){if("FragmentSpread"===t.kind)e.add(t.name.value);else if("VariableDefinition"===t.kind){var s=t.type;"NamedType"===s.kind&&e.add(s.name.value)}t.selectionSet&&t.selectionSet.selections.forEach((function(t){i(t,e)})),t.variableDefinitions&&t.variableDefinitions.forEach((function(t){i(t,e)})),t.definitions&&t.definitions.forEach((function(t){i(t,e)}))}var s={};function n(t,e){for(var i=0;i<t.definitions.length;i++){var s=t.definitions[i];if(s.name&&s.name.value==e)return s}}function a(t,e){var i={kind:t.kind,definitions:[n(t,e)]};t.hasOwnProperty("loc")&&(i.loc=t.loc);var a=s[e]||new Set,o=new Set,r=new Set;for(a.forEach((function(t){r.add(t)}));r.size>0;){var h=r;r=new Set,h.forEach((function(t){o.has(t)||(o.add(t),(s[t]||new Set).forEach((function(t){r.add(t)})))}))}return o.forEach((function(e){var s=n(t,e);s&&i.definitions.push(s)})),i}e.definitions.forEach((function(t){if(t.name){var e=new Set;i(t,e),s[t.name.value]=e}})),t.exports=e,t.exports.GetRoomBounds=a(e,"GetRoomBounds"),t.exports.GetRoomClassifications=a(e,"GetRoomClassifications")},68512:t=>{var e={kind:"Document",definitions:[{kind:"OperationDefinition",operation:"query",name:{kind:"Name",value:"GetScans"},variableDefinitions:[{kind:"VariableDefinition",variable:{kind:"Variable",name:{kind:"Name",value:"modelId"}},type:{kind:"NonNullType",type:{kind:"NamedType",name:{kind:"Name",value:"ID"}}},directives:[]}],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"model"},arguments:[{kind:"Argument",name:{kind:"Name",value:"id"},value:{kind:"Variable",name:{kind:"Name",value:"modelId"}}}],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"assets"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"scans"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"FragmentSpread",name:{kind:"Name",value:"ScanDetails"},directives:[]}]}}]}}]}}]}},{kind:"FragmentDefinition",name:{kind:"Name",value:"ScanDetails"},typeCondition:{kind:"NamedType",name:{kind:"Name",value:"Scan"}},directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"index"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"name"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"created"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"alignment"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"options"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"url"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"timeOfDay"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"anchor"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]}]}},{kind:"Field",name:{kind:"Name",value:"camera"},arguments:[],directives:[],selectionSet:{kind:"SelectionSet",selections:[{kind:"Field",name:{kind:"Name",value:"id"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"name"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"vendor"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"model"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"captureMode"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"depthCameraType"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"cameraTypes"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"sensorSerialNumbers"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"serialNumber"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"mountCalibrationVersion"},arguments:[],directives:[]},{kind:"Field",name:{kind:"Name",value:"softwareVersion"},arguments:[],directives:[]}]}}]}}],loc:{start:0,end:452}};e.loc.source={body:"query GetScans($modelId: ID!) {\n  model(id: $modelId) {\n    id\n    assets {\n      scans {\n        ...ScanDetails\n      }\n    }\n  }\n}\n\nfragment ScanDetails on Scan {\n  id\n  index\n  name\n  created\n  alignment\n  options\n  url\n  timeOfDay\n  anchor {\n    id\n  }\n  camera {\n    id\n    name\n    vendor\n    model\n    captureMode\n    depthCameraType\n    cameraTypes\n    sensorSerialNumbers\n    serialNumber\n    mountCalibrationVersion\n    softwareVersion\n  }\n}\n",name:"GraphQL request",locationOffset:{line:1,column:1}};function i(t,e){if("FragmentSpread"===t.kind)e.add(t.name.value);else if("VariableDefinition"===t.kind){var s=t.type;"NamedType"===s.kind&&e.add(s.name.value)}t.selectionSet&&t.selectionSet.selections.forEach((function(t){i(t,e)})),t.variableDefinitions&&t.variableDefinitions.forEach((function(t){i(t,e)})),t.definitions&&t.definitions.forEach((function(t){i(t,e)}))}var s={};function n(t,e){for(var i=0;i<t.definitions.length;i++){var s=t.definitions[i];if(s.name&&s.name.value==e)return s}}function a(t,e){var i={kind:t.kind,definitions:[n(t,e)]};t.hasOwnProperty("loc")&&(i.loc=t.loc);var a=s[e]||new Set,o=new Set,r=new Set;for(a.forEach((function(t){r.add(t)}));r.size>0;){var h=r;r=new Set,h.forEach((function(t){o.has(t)||(o.add(t),(s[t]||new Set).forEach((function(t){r.add(t)})))}))}return o.forEach((function(e){var s=n(t,e);s&&i.definitions.push(s)})),i}e.definitions.forEach((function(t){if(t.name){var e=new Set;i(t,e),s[t.name.value]=e}})),t.exports=e,t.exports.GetScans=a(e,"GetScans"),t.exports.ScanDetails=a(e,"ScanDetails")}}]);