var DracoDecoderModule=(()=>{var r="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return"undefined"!=typeof __filename&&(r=r||__filename),function(e={}){var i,t,f=void 0!==e?e:{};f.ready=new Promise((function(r,e){i=r,t=e}));var a=!1,n=!1;f.onRuntimeInitialized=function(){a=!0,n&&"function"==typeof f.onModuleLoaded&&f.onModuleLoaded(f)},f.onModuleParsed=function(){n=!0,a&&"function"==typeof f.onModuleLoaded&&f.onModuleLoaded(f)},f.isVersionSupported=function(r){if("string"!=typeof r)return!1;const e=r.split(".");return!(e.length<2||e.length>3)&&(1==e[0]&&e[1]>=0&&e[1]<=5||!(0!=e[0]||e[1]>10))};var A,o,b,u=Object.assign({},f),c="object"==typeof window,k="function"==typeof importScripts,_="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,s="";if(_){var p=require("fs"),l=require("path");s=k?l.dirname(s)+"/":__dirname+"/",A=(r,e)=>{var i=q(r);return i?e?i:i.toString():(r=X(r)?new URL(r):l.normalize(r),p.readFileSync(r,e?void 0:"utf8"))},b=r=>{var e=A(r,!0);return e.buffer||(e=new Uint8Array(e)),e},o=(r,e,i)=>{var t=q(r);t&&e(t),r=X(r)?new URL(r):l.normalize(r),p.readFile(r,(function(r,t){r?i(r):e(t.buffer)}))},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),(r,e)=>{if(v)throw process.exitCode=r,e;!function(r){if(r instanceof H)return;d("exiting due to exception: "+r)}(e),process.exit(r)},f.inspect=function(){return"[Emscripten Module object]"}}else(c||k)&&(k?s=self.location.href:"undefined"!=typeof document&&document.currentScript&&(s=document.currentScript.src),r&&(s=r),s=0!==s.indexOf("blob:")?s.substr(0,s.replace(/[?#].*/,"").lastIndexOf("/")+1):"",A=r=>{try{var e=new XMLHttpRequest;return e.open("GET",r,!1),e.send(null),e.responseText}catch(e){var i=q(r);if(i)return function(r){for(var e=[],i=0;i<r.length;i++){var t=r[i];t>255&&(t&=255),e.push(String.fromCharCode(t))}return e.join("")}(i);throw e}},k&&(b=r=>{try{var e=new XMLHttpRequest;return e.open("GET",r,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}catch(e){var i=q(r);if(i)return i;throw e}}),o=(r,e,i)=>{var t=new XMLHttpRequest;t.open("GET",r,!0),t.responseType="arraybuffer",t.onload=()=>{if(200==t.status||0==t.status&&t.response)e(t.response);else{var f=q(r);f?e(f.buffer):i()}},t.onerror=i,t.send(null)},r=>document.title=r);f.print||console.log.bind(console);var y,d=f.printErr||console.warn.bind(console);Object.assign(f,u),u=null,f.arguments&&f.arguments,f.thisProgram&&f.thisProgram,f.quit&&f.quit,f.wasmBinary&&(y=f.wasmBinary);var m,v=f.noExitRuntime||!0,h={Memory:function(r){this.buffer=new ArrayBuffer(65536*r.initial)},Module:function(r){},Instance:function(r,e){this.exports=function(r){for(var e,i=new Uint8Array(123),t=25;t>=0;--t)i[48+t]=52+t,i[65+t]=t,i[97+t]=26+t;function f(r,e,t){for(var f,a,n=0,A=e,o=t.length,b=e+(3*o>>2)-("="==t[o-2])-("="==t[o-1]);n<o;n+=4)f=i[t.charCodeAt(n+1)],a=i[t.charCodeAt(n+2)],r[A++]=i[t.charCodeAt(n)]<<2|f>>4,A<b&&(r[A++]=f<<4|a>>2),A<b&&(r[A++]=a<<6|i[t.charCodeAt(n+3)])}i[43]=62,i[47]=63;var a=new ArrayBuffer(16),n=new Int32Array(a),A=new Float32Array(a);new Float64Array(a);function o(){throw new Error("abort")}function b(r){A[2]=r}function u(r){return n[r]}return function(r){var i=r.a,t=i.a,a=t.buffer;t.grow=function(r){r|=0;var i=0|Et(),f=i+r|0;if(i<f&&f<65536){var o=new ArrayBuffer(y(f,65536));new Int8Array(o).set(n),n=new Int8Array(o),A=new Int16Array(o),c=new Int32Array(o),k=new Uint8Array(o),_=new Uint16Array(o),s=new Uint32Array(o),p=new Float32Array(o),l=new Float64Array(o),a=o,t.buffer=a,e=k}return i};var n=new Int8Array(a),A=new Int16Array(a),c=new Int32Array(a),k=new Uint8Array(a),_=new Uint16Array(a),s=new Uint32Array(a),p=new Float32Array(a),l=new Float64Array(a),y=Math.imul,d=Math.fround,m=Math.abs,v=Math.clz32,h=Math.min,R=Math.max,N=Math.floor,T=Math.ceil,V=(Math.trunc,Math.sqrt),U=i.b,W=i.c,D=i.d,G=i.e,Z=77808,E=0;function F(r,e,i,t){var f=0,a=0,A=0,o=0,b=0,u=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,E=0,F=0;if(!r)return 1;r:if(!((0|(f=c[i+20>>2]))>=(0|(v=c[i+12>>2]))&(A=c[i+16>>2])>>>0>=s[i+8>>2]|(0|f)>(0|v))){v=k[A+c[i>>2]|0],f=(A=A+1|0)?f:f+1|0,c[i+16>>2]=A,c[i+20>>2]=f;e:switch(0|v){case 0:f=r,a=e,A=t,r=0,t=0,Z=u=Z+-64|0,c[u+56>>2]=0,c[u+48>>2]=0,c[u+52>>2]=0,c[u+40>>2]=0,c[u+44>>2]=0,c[u+32>>2]=0,c[u+36>>2]=0,c[u+24>>2]=0,c[u+28>>2]=0,c[u+16>>2]=0,c[u+20>>2]=0,c[u+8>>2]=0,c[u+12>>2]=0;i:if(or(u+8|0,i)&&!(!jr(u+8|0,i)|(c[u+20>>2]?0:f))){if(bi(i,0,0),f)for(T=a<<2,V=c[u+36>>2],U=c[u+48>>2],G=c[u+24>>2],l=c[u+56>>2],b=c[u+52>>2];;){t:if(!(l>>>0>16383))for(;;){if((0|b)<=0)break t;if(b=b-1|0,c[u+52>>2]=b,l=k[b+U|0]|l<<8,c[u+56>>2]=l,!(l>>>0<16384))break}if(N=c[((r=4095&l)<<2)+G>>2],l=(y(c[(e=(N<<3)+V|0)>>2],l>>>12|0)+r|0)-c[e+4>>2]|0,c[u+56>>2]=l,(0|a)>0){if(r=0,!k[i+36|0]|N>>>0>32)break i;if(v=t+a|0,N)for(E=-2&N,F=1&N,e=c[i+32>>2],o=c[i+28>>2],R=c[i+24>>2];;){if(p=0,r=e,d=0,m=0,1!=(0|N))for(;(h=R+(r>>>3|0)|0)>>>0>=o>>>0?h=0:(h=k[0|h],e=r+1|0,c[i+32>>2]=e,h=h>>>(7&r)&1,r=e),h=h<<p|d,d=0,(W=R+(r>>>3|0)|0)>>>0<o>>>0&&(d=k[0|W],e=r+1|0,c[i+32>>2]=e,d=d>>>(7&r)&1,r=e),W=1|p,p=p+2|0,d=h|d<<W,(0|E)!=(0|(m=m+2|0)););if(m=A+(t<<2)|0,F&&((h=R+(r>>>3|0)|0)>>>0<o>>>0?(h=k[0|h],e=r+1|0,c[i+32>>2]=e,r=h>>>(7&r)&1):r=0,d|=r<<p),c[m>>2]=d,(0|v)==(0|(t=t+1|0)))break}else Sr(A+(t<<2)|0,0,T);t=v}if(!(f>>>0>(D=a+D|0)>>>0))break}n[i+36|0]=0,a=c[i+20>>2],r=0,e=(d=(r=(d=c[i+32>>2]+7|0)>>>0<7?1:r)<<29|d>>>3)+c[i+16>>2]|0,r=(r>>>3|0)+a|0,c[i+16>>2]=e,c[i+20>>2]=e>>>0<d>>>0?r+1|0:r,r=1}return(e=c[u+36>>2])&&(c[u+40>>2]=e,er(e)),(e=c[u+24>>2])&&(c[u+28>>2]=e,er(e)),(e=c[u+8>>2])&&(c[u+12>>2]=e,er(e)),Z=u- -64|0,r;case 1:break e;default:break r}e=0;e:if(!((0|(f=c[i+20>>2]))>=(0|(v=c[i+12>>2]))&(A=c[i+16>>2])>>>0>=s[i+8>>2]|(0|f)>(0|v))){v=k[A+c[i>>2]|0],f=(A=A+1|0)?f:f+1|0,c[i+16>>2]=A,c[i+20>>2]=f;i:switch(v-1|0){case 8:v=r,N=t,Z=f=Z+-64|0,c[f+56>>2]=0,c[f+48>>2]=0,c[f+52>>2]=0,c[f+40>>2]=0,c[f+44>>2]=0,c[f+32>>2]=0,c[f+36>>2]=0,c[f+24>>2]=0,c[f+28>>2]=0,c[f+16>>2]=0,c[f+20>>2]=0,c[f+8>>2]=0,c[f+12>>2]=0,o=f+8|0;t:{f:if(_[i+38>>1]&&Ze(1,o+12|0,i)&&!((a=(e=c[i+8>>2])-(t=c[i+16>>2])|0)>>>0<(b=c[o+12>>2])>>>6>>>0&(0|(e=c[i+12>>2]-(c[i+20>>2]+(e>>>0<t>>>0)|0)|0))<=0|(0|e)<0)){if(e=c[o>>2],(r=c[o+4>>2]-e>>2)>>>0<b>>>0?(_e(o,b-r|0),b=c[o+12>>2]):r>>>0<=b>>>0||(c[o+4>>2]=e+(b<<2)),A=1,!b)break t;for(a=c[i+16>>2],t=c[i+20>>2],T=c[o>>2],u=c[i+8>>2],m=c[i+12>>2],e=0;;){if(A=0,(0|t)>=(0|m)&a>>>0>=u>>>0|(0|t)>(0|m))break t;A=c[i>>2],h=k[A+a|0],r=t,r=(a=a+1|0)?r:r+1|0,c[i+16>>2]=a,t=r,c[i+20>>2]=r,r=h>>>2|0,l=0;a:{n:{A:{o:switch(0|(V=3&h)){case 0:break n;case 3:break o;default:break A}if(A=0,(r=r+e|0)>>>0>=b>>>0)break t;Sr(T+(e<<2)|0,0,4+(252&h)|0),e=r;break a}for(;;){if((0|a)==(0|u)&(0|t)==(0|m))break f;if(b=k[a+A|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,r|=b<<(l<<3|6),(0|V)==(0|(l=l+1|0)))break}}c[T+(e<<2)>>2]=r}if(!((e=e+1|0)>>>0<(b=c[o+12>>2])>>>0))break}if(r=o+16|0,m=c[o>>2],t=c[o+16>>2],(e=c[o+20>>2]-t|0)>>>0<=32767?_e(r,8192-(e>>>2|0)|0):32768!=(0|e)&&(c[o+20>>2]=t+32768),e=c[(t=o+28|0)>>2],(a=c[o+32>>2]-e>>3)>>>0<b>>>0)ye(t,b-a|0),e=c[t>>2];else if(a>>>0>b>>>0&&(c[o+32>>2]=(b<<3)+e),!b)break f;for(u=c[r>>2],a=0,t=0;;){if(l=c[(A=m+(a<<2)|0)>>2],r=t,c[(o=(a<<3)+e|0)+4>>2]=r,c[o>>2]=l,(t=(A=c[A>>2])+r|0)>>>0>8192)break f;if(!(r>>>0>=t>>>0)){if(l=0,o=7&A)for(;c[u+(r<<2)>>2]=a,r=r+1|0,(0|o)!=(0|(l=l+1|0)););if(!(A-1>>>0<=6))for(;c[(A=u+(r<<2)|0)>>2]=a,c[A+28>>2]=a,c[A+24>>2]=a,c[A+20>>2]=a,c[A+16>>2]=a,c[A+12>>2]=a,c[A+8>>2]=a,c[A+4>>2]=a,(0|t)!=(0|(r=r+8|0)););}if((0|b)==(0|(a=a+1|0)))break}p=8192==(0|t)}A=p}if(!(!A|(c[f+20>>2]?0:v))){t=0,Z=p=Z-16|0;t:if(Re(1,p+8|0,i)&&(A=(r=c[i+8>>2])-(a=c[i+16>>2])|0,u=c[p+12>>2],b=c[i+20>>2],!((0|u)==(0|(r=c[i+12>>2]-(b+(r>>>0<a>>>0)|0)|0))&A>>>0<(e=c[p+8>>2])>>>0|r>>>0<u>>>0||(r=b+u|0,r=(A=e+a|0)>>>0<a>>>0?r+1|0:r,c[i+16>>2]=A,c[i+20>>2]=r,(0|e)<=0)))){r=a+c[i>>2]|0,c[f+48>>2]=r;f:if((A=k[0|(a=(i=e-1|0)+r|0)])>>>0<=63)c[f+52>>2]=i,r=63&k[0|a];else{a:switch((A>>>6|0)-1|0){case 0:if(e>>>0<2)break t;e=e-2|0,c[f+52>>2]=e,r=k[(r=r+e|0)+1|0]<<8&16128|k[0|r];break f;case 1:if(e>>>0<3)break t;e=e-3|0,c[f+52>>2]=e,r=k[(r=r+e|0)+1|0]<<8|k[r+2|0]<<16&4128768|k[0|r];break f;default:break a}e=e-4|0,c[f+52>>2]=e,r=1073741823&(k[0|(r=r+e|0)]|k[r+1|0]<<8|k[r+2|0]<<16|k[r+3|0]<<24)}c[f+56>>2]=r+32768,t=r>>>0<8355840}if(Z=p+16|0,t)if(v)for(e=c[f+52>>2],r=c[f+56>>2],i=c[f+36>>2],t=c[f+48>>2],a=c[f+24>>2];;){t:if(!(r>>>0>32767))for(;;){if((0|e)<=0)break t;if(e=e-1|0,c[f+52>>2]=e,r=k[e+t|0]|r<<8,c[f+56>>2]=r,!(r>>>0<32768))break}if(p=c[a+((d=8191&r)<<2)>>2],r=(y(c[(A=i+(p<<3)|0)>>2],r>>>13|0)+d|0)-c[A+4>>2]|0,c[f+56>>2]=r,c[N+(R<<2)>>2]=p,d=1,(0|v)==(0|(R=R+1|0)))break}else d=1}(r=c[f+36>>2])&&(c[f+40>>2]=r,er(r)),(r=c[f+24>>2])&&(c[f+28>>2]=r,er(r)),(r=c[f+8>>2])&&(c[f+12>>2]=r,er(r)),Z=f- -64|0,e=d;break e;case 9:v=r,N=t,Z=A=Z+-64|0,c[A+56>>2]=0,c[A+48>>2]=0,c[A+52>>2]=0,c[A+40>>2]=0,c[A+44>>2]=0,c[A+32>>2]=0,c[A+36>>2]=0,c[A+24>>2]=0,c[A+28>>2]=0,c[A+16>>2]=0,c[A+20>>2]=0,c[A+8>>2]=0,c[A+12>>2]=0,o=A+8|0;t:{f:if(_[i+38>>1]&&Ze(1,o+12|0,i)&&!((a=(e=c[i+8>>2])-(t=c[i+16>>2])|0)>>>0<(b=c[o+12>>2])>>>6>>>0&(0|(e=c[i+12>>2]-(c[i+20>>2]+(e>>>0<t>>>0)|0)|0))<=0|(0|e)<0)){if(e=c[o>>2],(r=c[o+4>>2]-e>>2)>>>0<b>>>0?(_e(o,b-r|0),b=c[o+12>>2]):r>>>0<=b>>>0||(c[o+4>>2]=e+(b<<2)),f=1,!b)break t;for(a=c[i+16>>2],t=c[i+20>>2],T=c[o>>2],u=c[i+8>>2],m=c[i+12>>2],e=0;;){if(f=0,(0|t)>=(0|m)&a>>>0>=u>>>0|(0|t)>(0|m))break t;V=c[i>>2],h=k[V+a|0],f=t,f=(a=a+1|0)?f:f+1|0,c[i+16>>2]=a,t=f,c[i+20>>2]=f,r=h>>>2|0,l=0;a:{n:{A:{o:switch(0|(f=3&h)){case 0:break n;case 3:break o;default:break A}if(f=0,(r=r+e|0)>>>0>=b>>>0)break t;Sr(T+(e<<2)|0,0,4+(252&h)|0),e=r;break a}for(;;){if((0|a)==(0|u)&(0|t)==(0|m))break f;if(b=k[a+V|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,r|=b<<(l<<3|6),(0|f)==(0|(l=l+1|0)))break}}c[T+(e<<2)>>2]=r}if(!((e=e+1|0)>>>0<(b=c[o+12>>2])>>>0))break}if(r=o+16|0,m=c[o>>2],t=c[o+16>>2],(e=c[o+20>>2]-t|0)>>>0<=131071?_e(r,32768-(e>>>2|0)|0):131072!=(0|e)&&(c[o+20>>2]=t+131072),e=c[(t=o+28|0)>>2],(a=c[o+32>>2]-e>>3)>>>0<b>>>0)ye(t,b-a|0),e=c[t>>2];else if(a>>>0>b>>>0&&(c[o+32>>2]=(b<<3)+e),!b)break f;for(u=c[r>>2],a=0,t=0;;){if(l=c[(f=m+(a<<2)|0)>>2],r=t,c[(o=(a<<3)+e|0)+4>>2]=r,c[o>>2]=l,(t=(f=c[f>>2])+r|0)>>>0>32768)break f;if(!(r>>>0>=t>>>0)){if(l=0,o=7&f)for(;c[u+(r<<2)>>2]=a,r=r+1|0,(0|o)!=(0|(l=l+1|0)););if(!(f-1>>>0<=6))for(;c[(f=u+(r<<2)|0)>>2]=a,c[f+28>>2]=a,c[f+24>>2]=a,c[f+20>>2]=a,c[f+16>>2]=a,c[f+12>>2]=a,c[f+8>>2]=a,c[f+4>>2]=a,(0|t)!=(0|(r=r+8|0)););}if((0|b)==(0|(a=a+1|0)))break}p=32768==(0|t)}f=p}if(!(!f|(c[A+20>>2]?0:v))){t=0,Z=a=Z-16|0;t:if(Re(1,a+8|0,i)&&(p=(f=c[i+8>>2])-(e=c[i+16>>2])|0,u=c[a+12>>2],b=c[i+20>>2],!((0|u)==(0|(f=c[i+12>>2]-(b+(e>>>0>f>>>0)|0)|0))&p>>>0<(r=c[a+8>>2])>>>0|f>>>0<u>>>0||(f=b+u|0,f=(p=r+e|0)>>>0<e>>>0?f+1|0:f,c[i+16>>2]=p,c[i+20>>2]=f,(0|r)<=0)))){e=e+c[i>>2]|0,c[A+48>>2]=e;f:if((p=k[0|(f=(i=r-1|0)+e|0)])>>>0<=63)c[A+52>>2]=i,r=63&k[0|f];else{a:switch((p>>>6|0)-1|0){case 0:if(r>>>0<2)break t;r=r-2|0,c[A+52>>2]=r,r=k[(r=r+e|0)+1|0]<<8&16128|k[0|r];break f;case 1:if(r>>>0<3)break t;r=r-3|0,c[A+52>>2]=r,r=k[(r=r+e|0)+1|0]<<8|k[r+2|0]<<16&4128768|k[0|r];break f;default:break a}r=r-4|0,c[A+52>>2]=r,r=1073741823&(k[0|(r=r+e|0)]|k[r+1|0]<<8|k[r+2|0]<<16|k[r+3|0]<<24)}c[A+56>>2]=r+131072,t=r>>>0<33423360}if(Z=a+16|0,t)if(v)for(e=c[A+52>>2],r=c[A+56>>2],i=c[A+36>>2],t=c[A+48>>2],a=c[A+24>>2];;){t:if(!(r>>>0>131071))for(;;){if((0|e)<=0)break t;if(e=e-1|0,c[A+52>>2]=e,r=k[e+t|0]|r<<8,c[A+56>>2]=r,!(r>>>0<131072))break}if(f=c[a+((d=32767&r)<<2)>>2],r=(y(c[(p=i+(f<<3)|0)>>2],r>>>15|0)+d|0)-c[p+4>>2]|0,c[A+56>>2]=r,c[N+(R<<2)>>2]=f,d=1,(0|v)==(0|(R=R+1|0)))break}else d=1}(r=c[A+36>>2])&&(c[A+40>>2]=r,er(r)),(r=c[A+24>>2])&&(c[A+28>>2]=r,er(r)),(r=c[A+8>>2])&&(c[A+12>>2]=r,er(r)),Z=A- -64|0,e=d;break e;case 10:v=r,N=t,Z=A=Z+-64|0,c[A+56>>2]=0,c[A+48>>2]=0,c[A+52>>2]=0,c[A+40>>2]=0,c[A+44>>2]=0,c[A+32>>2]=0,c[A+36>>2]=0,c[A+24>>2]=0,c[A+28>>2]=0,c[A+16>>2]=0,c[A+20>>2]=0,c[A+8>>2]=0,c[A+12>>2]=0,o=A+8|0;t:{f:if(_[i+38>>1]&&Ze(1,o+12|0,i)&&!((a=(e=c[i+8>>2])-(t=c[i+16>>2])|0)>>>0<(b=c[o+12>>2])>>>6>>>0&(0|(e=c[i+12>>2]-(c[i+20>>2]+(e>>>0<t>>>0)|0)|0))<=0|(0|e)<0)){if(e=c[o>>2],(r=c[o+4>>2]-e>>2)>>>0<b>>>0?(_e(o,b-r|0),b=c[o+12>>2]):r>>>0<=b>>>0||(c[o+4>>2]=e+(b<<2)),f=1,!b)break t;for(a=c[i+16>>2],t=c[i+20>>2],T=c[o>>2],u=c[i+8>>2],m=c[i+12>>2],e=0;;){if(f=0,(0|t)>=(0|m)&a>>>0>=u>>>0|(0|t)>(0|m))break t;V=c[i>>2],h=k[V+a|0],r=t,r=(a=a+1|0)?r:r+1|0,c[i+16>>2]=a,t=r,c[i+20>>2]=r,r=h>>>2|0,l=0;a:{n:{A:{o:switch(0|(U=3&h)){case 0:break n;case 3:break o;default:break A}if(f=0,(r=r+e|0)>>>0>=b>>>0)break t;Sr(T+(e<<2)|0,0,4+(252&h)|0),e=r;break a}for(;;){if((0|a)==(0|u)&(0|t)==(0|m))break f;if(b=k[a+V|0],f=t,f=(a=a+1|0)?f:f+1|0,c[i+16>>2]=a,t=f,c[i+20>>2]=f,r|=b<<(l<<3|6),(0|U)==(0|(l=l+1|0)))break}}c[T+(e<<2)>>2]=r}if(!((e=e+1|0)>>>0<(b=c[o+12>>2])>>>0))break}if(r=o+16|0,m=c[o>>2],t=c[o+16>>2],(e=c[o+20>>2]-t|0)>>>0<=262143?_e(r,65536-(e>>>2|0)|0):262144!=(0|e)&&(c[o+20>>2]=t+262144),e=c[(t=o+28|0)>>2],(a=c[o+32>>2]-e>>3)>>>0<b>>>0)ye(t,b-a|0),e=c[t>>2];else if(a>>>0>b>>>0&&(c[o+32>>2]=(b<<3)+e),!b)break f;for(u=c[r>>2],a=0,t=0;;){if(l=c[(f=m+(a<<2)|0)>>2],r=t,c[(o=(a<<3)+e|0)+4>>2]=r,c[o>>2]=l,(t=(f=c[f>>2])+r|0)>>>0>65536)break f;if(!(r>>>0>=t>>>0)){if(l=0,o=7&f)for(;c[u+(r<<2)>>2]=a,r=r+1|0,(0|o)!=(0|(l=l+1|0)););if(!(f-1>>>0<=6))for(;c[(f=u+(r<<2)|0)>>2]=a,c[f+28>>2]=a,c[f+24>>2]=a,c[f+20>>2]=a,c[f+16>>2]=a,c[f+12>>2]=a,c[f+8>>2]=a,c[f+4>>2]=a,(0|t)!=(0|(r=r+8|0)););}if((0|b)==(0|(a=a+1|0)))break}p=65536==(0|t)}f=p}if(!(!f|(c[A+20>>2]?0:v))){t=0,Z=f=Z-16|0;t:if(Re(1,f+8|0,i)&&(p=(r=c[i+8>>2])-(a=c[i+16>>2])|0,u=c[f+12>>2],b=c[i+20>>2],!((0|u)==(0|(r=c[i+12>>2]-(b+(r>>>0<a>>>0)|0)|0))&p>>>0<(e=c[f+8>>2])>>>0|r>>>0<u>>>0||(r=b+u|0,r=(p=e+a|0)>>>0<a>>>0?r+1|0:r,c[i+16>>2]=p,c[i+20>>2]=r,(0|e)<=0)))){r=a+c[i>>2]|0,c[A+48>>2]=r;f:if((p=k[0|(a=(i=e-1|0)+r|0)])>>>0<=63)c[A+52>>2]=i,r=63&k[0|a];else{a:switch((p>>>6|0)-1|0){case 0:if(e>>>0<2)break t;e=e-2|0,c[A+52>>2]=e,r=k[(r=r+e|0)+1|0]<<8&16128|k[0|r];break f;case 1:if(e>>>0<3)break t;e=e-3|0,c[A+52>>2]=e,r=k[(r=r+e|0)+1|0]<<8|k[r+2|0]<<16&4128768|k[0|r];break f;default:break a}e=e-4|0,c[A+52>>2]=e,r=1073741823&(k[0|(r=r+e|0)]|k[r+1|0]<<8|k[r+2|0]<<16|k[r+3|0]<<24)}c[A+56>>2]=r+262144,t=r>>>0<66846720}if(Z=f+16|0,t)if(v)for(e=c[A+52>>2],r=c[A+56>>2],i=c[A+36>>2],t=c[A+48>>2],a=c[A+24>>2];;){t:if(!(r>>>0>262143))for(;;){if((0|e)<=0)break t;if(e=e-1|0,c[A+52>>2]=e,r=k[e+t|0]|r<<8,c[A+56>>2]=r,!(r>>>0<262144))break}if(f=c[a+((d=65535&r)<<2)>>2],r=(y(c[(p=i+(f<<3)|0)>>2],r>>>16|0)+d|0)-c[p+4>>2]|0,c[A+56>>2]=r,c[N+(R<<2)>>2]=f,d=1,(0|v)==(0|(R=R+1|0)))break}else d=1}(r=c[A+36>>2])&&(c[A+40>>2]=r,er(r)),(r=c[A+24>>2])&&(c[A+28>>2]=r,er(r)),(r=c[A+8>>2])&&(c[A+12>>2]=r,er(r)),Z=A- -64|0,e=d;break e;case 11:v=r,N=t,Z=A=Z+-64|0,c[A+56>>2]=0,c[A+48>>2]=0,c[A+52>>2]=0,c[A+40>>2]=0,c[A+44>>2]=0,c[A+32>>2]=0,c[A+36>>2]=0,c[A+24>>2]=0,c[A+28>>2]=0,c[A+16>>2]=0,c[A+20>>2]=0,c[A+8>>2]=0,c[A+12>>2]=0,o=A+8|0;t:{f:if(_[i+38>>1]&&Ze(1,o+12|0,i)&&!((a=(e=c[i+8>>2])-(t=c[i+16>>2])|0)>>>0<(b=c[o+12>>2])>>>6>>>0&(0|(e=c[i+12>>2]-(c[i+20>>2]+(e>>>0<t>>>0)|0)|0))<=0|(0|e)<0)){if(e=c[o>>2],(r=c[o+4>>2]-e>>2)>>>0<b>>>0?(_e(o,b-r|0),b=c[o+12>>2]):r>>>0<=b>>>0||(c[o+4>>2]=e+(b<<2)),f=1,!b)break t;for(a=c[i+16>>2],t=c[i+20>>2],T=c[o>>2],u=c[i+8>>2],m=c[i+12>>2],e=0;;){if(f=0,(0|t)>=(0|m)&a>>>0>=u>>>0|(0|t)>(0|m))break t;V=c[i>>2],h=k[V+a|0],f=t,f=(a=a+1|0)?f:f+1|0,c[i+16>>2]=a,t=f,c[i+20>>2]=f,r=h>>>2|0,l=0;a:{n:{A:{o:switch(0|(f=3&h)){case 0:break n;case 3:break o;default:break A}if(f=0,(r=r+e|0)>>>0>=b>>>0)break t;Sr(T+(e<<2)|0,0,4+(252&h)|0),e=r;break a}for(;;){if((0|a)==(0|u)&(0|t)==(0|m))break f;if(b=k[a+V|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,r|=b<<(l<<3|6),(0|f)==(0|(l=l+1|0)))break}}c[T+(e<<2)>>2]=r}if(!((e=e+1|0)>>>0<(b=c[o+12>>2])>>>0))break}if(r=o+16|0,m=c[o>>2],t=c[o+16>>2],(e=c[o+20>>2]-t|0)>>>0<=1048575?_e(r,262144-(e>>>2|0)|0):1048576!=(0|e)&&(c[o+20>>2]=t- -1048576),e=c[(t=o+28|0)>>2],(a=c[o+32>>2]-e>>3)>>>0<b>>>0)ye(t,b-a|0),e=c[t>>2];else if(a>>>0>b>>>0&&(c[o+32>>2]=(b<<3)+e),!b)break f;for(u=c[r>>2],a=0,t=0;;){if(l=c[(f=m+(a<<2)|0)>>2],r=t,c[(o=(a<<3)+e|0)+4>>2]=r,c[o>>2]=l,(t=(f=c[f>>2])+r|0)>>>0>262144)break f;if(!(r>>>0>=t>>>0)){if(l=0,o=7&f)for(;c[u+(r<<2)>>2]=a,r=r+1|0,(0|o)!=(0|(l=l+1|0)););if(!(f-1>>>0<=6))for(;c[(f=u+(r<<2)|0)>>2]=a,c[f+28>>2]=a,c[f+24>>2]=a,c[f+20>>2]=a,c[f+16>>2]=a,c[f+12>>2]=a,c[f+8>>2]=a,c[f+4>>2]=a,(0|t)!=(0|(r=r+8|0)););}if((0|b)==(0|(a=a+1|0)))break}p=262144==(0|t)}f=p}if(!(!f|(c[A+20>>2]?0:v))){t=0,Z=a=Z-16|0;t:if(Re(1,a+8|0,i)&&(p=(f=c[i+8>>2])-(e=c[i+16>>2])|0,u=c[a+12>>2],b=c[i+20>>2],!((0|u)==(0|(f=c[i+12>>2]-(b+(e>>>0>f>>>0)|0)|0))&p>>>0<(r=c[a+8>>2])>>>0|f>>>0<u>>>0||(f=b+u|0,f=(p=r+e|0)>>>0<e>>>0?f+1|0:f,c[i+16>>2]=p,c[i+20>>2]=f,(0|r)<=0)))){e=e+c[i>>2]|0,c[A+48>>2]=e;f:if((p=k[0|(f=(i=r-1|0)+e|0)])>>>0<=63)c[A+52>>2]=i,r=63&k[0|f];else{a:switch((p>>>6|0)-1|0){case 0:if(r>>>0<2)break t;r=r-2|0,c[A+52>>2]=r,r=k[(r=r+e|0)+1|0]<<8&16128|k[0|r];break f;case 1:if(r>>>0<3)break t;r=r-3|0,c[A+52>>2]=r,r=k[(r=r+e|0)+1|0]<<8|k[r+2|0]<<16&4128768|k[0|r];break f;default:break a}r=r-4|0,c[A+52>>2]=r,r=1073741823&(k[0|(r=r+e|0)]|k[r+1|0]<<8|k[r+2|0]<<16|k[r+3|0]<<24)}c[A+56>>2]=r- -1048576,t=r>>>0<267386880}if(Z=a+16|0,t)if(v)for(e=c[A+52>>2],r=c[A+56>>2],i=c[A+36>>2],t=c[A+48>>2],a=c[A+24>>2];;){t:if(!(r>>>0>1048575))for(;;){if((0|e)<=0)break t;if(e=e-1|0,c[A+52>>2]=e,r=k[e+t|0]|r<<8,c[A+56>>2]=r,!(r>>>0<1048576))break}if(f=c[a+((d=262143&r)<<2)>>2],r=(y(c[(p=i+(f<<3)|0)>>2],r>>>18|0)+d|0)-c[p+4>>2]|0,c[A+56>>2]=r,c[N+(R<<2)>>2]=f,d=1,(0|v)==(0|(R=R+1|0)))break}else d=1}(r=c[A+36>>2])&&(c[A+40>>2]=r,er(r)),(r=c[A+24>>2])&&(c[A+28>>2]=r,er(r)),(r=c[A+8>>2])&&(c[A+12>>2]=r,er(r)),Z=A- -64|0,e=d;break e;case 12:v=r,N=t,Z=f=Z+-64|0,c[f+56>>2]=0,c[f+48>>2]=0,c[f+52>>2]=0,c[f+40>>2]=0,c[f+44>>2]=0,c[f+32>>2]=0,c[f+36>>2]=0,c[f+24>>2]=0,c[f+28>>2]=0,c[f+16>>2]=0,c[f+20>>2]=0,c[f+8>>2]=0,c[f+12>>2]=0,o=f+8|0;t:{f:if(_[i+38>>1]&&Ze(1,o+12|0,i)&&!((a=(e=c[i+8>>2])-(t=c[i+16>>2])|0)>>>0<(b=c[o+12>>2])>>>6>>>0&(0|(e=c[i+12>>2]-(c[i+20>>2]+(e>>>0<t>>>0)|0)|0))<=0|(0|e)<0)){if(e=c[o>>2],(r=c[o+4>>2]-e>>2)>>>0<b>>>0?(_e(o,b-r|0),b=c[o+12>>2]):r>>>0<=b>>>0||(c[o+4>>2]=e+(b<<2)),A=1,!b)break t;for(a=c[i+16>>2],t=c[i+20>>2],T=c[o>>2],u=c[i+8>>2],m=c[i+12>>2],e=0;;){if(A=0,(0|t)>=(0|m)&a>>>0>=u>>>0|(0|t)>(0|m))break t;A=c[i>>2],h=k[A+a|0],r=t,r=(a=a+1|0)?r:r+1|0,c[i+16>>2]=a,t=r,c[i+20>>2]=r,r=h>>>2|0,l=0;a:{n:{A:{o:switch(0|(V=3&h)){case 0:break n;case 3:break o;default:break A}if(A=0,(r=r+e|0)>>>0>=b>>>0)break t;Sr(T+(e<<2)|0,0,4+(252&h)|0),e=r;break a}for(;;){if((0|a)==(0|u)&(0|t)==(0|m))break f;if(b=k[a+A|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,r|=b<<(l<<3|6),(0|V)==(0|(l=l+1|0)))break}}c[T+(e<<2)>>2]=r}if(!((e=e+1|0)>>>0<(b=c[o+12>>2])>>>0))break}if(r=o+16|0,m=c[o>>2],t=c[o+16>>2],(e=c[o+20>>2]-t|0)>>>0<=2097151?_e(r,524288-(e>>>2|0)|0):2097152!=(0|e)&&(c[o+20>>2]=t+2097152),e=c[(t=o+28|0)>>2],(a=c[o+32>>2]-e>>3)>>>0<b>>>0)ye(t,b-a|0),e=c[t>>2];else if(a>>>0>b>>>0&&(c[o+32>>2]=(b<<3)+e),!b)break f;for(u=c[r>>2],a=0,t=0;;){if(l=c[(A=m+(a<<2)|0)>>2],r=t,c[(o=(a<<3)+e|0)+4>>2]=r,c[o>>2]=l,(t=(A=c[A>>2])+r|0)>>>0>524288)break f;if(!(r>>>0>=t>>>0)){if(l=0,o=7&A)for(;c[u+(r<<2)>>2]=a,r=r+1|0,(0|o)!=(0|(l=l+1|0)););if(!(A-1>>>0<=6))for(;c[(A=u+(r<<2)|0)>>2]=a,c[A+28>>2]=a,c[A+24>>2]=a,c[A+20>>2]=a,c[A+16>>2]=a,c[A+12>>2]=a,c[A+8>>2]=a,c[A+4>>2]=a,(0|t)!=(0|(r=r+8|0)););}if((0|b)==(0|(a=a+1|0)))break}p=524288==(0|t)}A=p}if(!(!A|(c[f+20>>2]?0:v))){t=0,Z=p=Z-16|0;t:if(Re(1,p+8|0,i)&&(A=(r=c[i+8>>2])-(a=c[i+16>>2])|0,u=c[p+12>>2],b=c[i+20>>2],!((0|u)==(0|(r=c[i+12>>2]-(b+(r>>>0<a>>>0)|0)|0))&A>>>0<(e=c[p+8>>2])>>>0|r>>>0<u>>>0||(r=b+u|0,r=(A=e+a|0)>>>0<a>>>0?r+1|0:r,c[i+16>>2]=A,c[i+20>>2]=r,(0|e)<=0)))){r=a+c[i>>2]|0,c[f+48>>2]=r;f:if((A=k[0|(a=(i=e-1|0)+r|0)])>>>0<=63)c[f+52>>2]=i,r=63&k[0|a];else{a:switch((A>>>6|0)-1|0){case 0:if(e>>>0<2)break t;e=e-2|0,c[f+52>>2]=e,r=k[(r=r+e|0)+1|0]<<8&16128|k[0|r];break f;case 1:if(e>>>0<3)break t;e=e-3|0,c[f+52>>2]=e,r=k[(r=r+e|0)+1|0]<<8|k[r+2|0]<<16&4128768|k[0|r];break f;default:break a}e=e-4|0,c[f+52>>2]=e,r=1073741823&(k[0|(r=r+e|0)]|k[r+1|0]<<8|k[r+2|0]<<16|k[r+3|0]<<24)}c[f+56>>2]=r+2097152,t=r>>>0<534773760}if(Z=p+16|0,t)if(v)for(e=c[f+52>>2],r=c[f+56>>2],i=c[f+36>>2],t=c[f+48>>2],a=c[f+24>>2];;){t:if(!(r>>>0>2097151))for(;;){if((0|e)<=0)break t;if(e=e-1|0,c[f+52>>2]=e,r=k[e+t|0]|r<<8,c[f+56>>2]=r,!(r>>>0<2097152))break}if(p=c[a+((d=524287&r)<<2)>>2],r=(y(c[(A=i+(p<<3)|0)>>2],r>>>19|0)+d|0)-c[A+4>>2]|0,c[f+56>>2]=r,c[N+(R<<2)>>2]=p,d=1,(0|v)==(0|(R=R+1|0)))break}else d=1}(r=c[f+36>>2])&&(c[f+40>>2]=r,er(r)),(r=c[f+24>>2])&&(c[f+28>>2]=r,er(r)),(r=c[f+8>>2])&&(c[f+12>>2]=r,er(r)),Z=f- -64|0,e=d;break e;case 17:e=L(r,i,t);break e;case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:if(Z=e=Z+-64|0,c[e+56>>2]=0,c[e+48>>2]=0,c[e+52>>2]=0,c[e+40>>2]=0,c[e+44>>2]=0,c[e+32>>2]=0,c[e+36>>2]=0,c[e+24>>2]=0,c[e+28>>2]=0,c[e+16>>2]=0,c[e+20>>2]=0,c[e+8>>2]=0,c[e+12>>2]=0,!(!or(e+8|0,i)|(c[e+20>>2]?0:r))&&jr(e+8|0,i))if(r)for(d=c[e+52>>2],i=c[e+56>>2],f=c[e+36>>2],A=c[e+48>>2],v=c[e+24>>2];;){t:if(!(i>>>0>16383))for(;;){if((0|d)<=0)break t;if(d=d-1|0,c[e+52>>2]=d,i=k[A+d|0]|i<<8,c[e+56>>2]=i,!(i>>>0<16384))break}if(u=c[v+((a=4095&i)<<2)>>2],i=(y(c[(N=f+(u<<3)|0)>>2],i>>>12|0)+a|0)-c[N+4>>2]|0,c[e+56>>2]=i,c[(p<<2)+t>>2]=u,a=1,(0|(p=p+1|0))==(0|r))break}else a=1;(r=c[e+36>>2])&&(c[e+40>>2]=r,er(r)),(r=c[e+24>>2])&&(c[e+28>>2]=r,er(r)),(r=c[e+8>>2])&&(c[e+12>>2]=r,er(r)),Z=e- -64|0,e=a;break e;case 13:case 14:case 15:case 16:break i;default:break e}e=L(r,i,t)}a=e}return a}function I(r,e,i,t){var f,a=0,b=0,u=0,_=0,p=0,l=0,y=0,d=0,m=0,v=0,h=0;if(Z=f=Z-80|0,a=c[i+36>>2],c[f+72>>2]=c[i+32>>2],c[f+76>>2]=a,b=c[i+28>>2],c[(a=f- -64|0)>>2]=c[i+24>>2],c[a+4>>2]=b,a=c[i+20>>2],c[f+56>>2]=c[i+16>>2],c[f+60>>2]=a,a=c[i+12>>2],c[f+48>>2]=c[i+8>>2],c[f+52>>2]=a,a=c[i+4>>2],c[f+40>>2]=c[i>>2],c[f+44>>2]=a,P(r,f+40|0,f+24|0),!c[r>>2])if(n[r+15|0]<0&&er(c[r+4>>2]),1==k[f+31|0]){Z=p=Z-16|0;r:{e:switch(k[f+32|0]){case 0:a=fi(vi(48)),c[a>>2]=9864,c[f+8>>2]=0,c[f+12>>2]=0,c[f>>2]=0,c[f+4>>2]=0,c[f+16>>2]=a;break r;case 1:a=fi(vi(52)),c[a+48>>2]=0,c[a>>2]=8176,c[f+8>>2]=0,c[f+12>>2]=0,c[f>>2]=0,c[f+4>>2]=0,c[f+16>>2]=a;break r;default:break e}b=vi(32),n[b+28|0]=0,a=k[1520]|k[1521]<<8|k[1522]<<16|k[1523]<<24,n[b+24|0]=a,n[b+25|0]=a>>>8,n[b+26|0]=a>>>16,n[b+27|0]=a>>>24,a=k[1516]|k[1517]<<8|k[1518]<<16|k[1519]<<24,u=k[1512]|k[1513]<<8|k[1514]<<16|k[1515]<<24,n[b+16|0]=u,n[b+17|0]=u>>>8,n[b+18|0]=u>>>16,n[b+19|0]=u>>>24,n[b+20|0]=a,n[b+21|0]=a>>>8,n[b+22|0]=a>>>16,n[b+23|0]=a>>>24,a=k[1508]|k[1509]<<8|k[1510]<<16|k[1511]<<24,u=k[1504]|k[1505]<<8|k[1506]<<16|k[1507]<<24,n[b+8|0]=u,n[b+9|0]=u>>>8,n[b+10|0]=u>>>16,n[b+11|0]=u>>>24,n[b+12|0]=a,n[b+13|0]=a>>>8,n[b+14|0]=a>>>16,n[b+15|0]=a>>>24,a=k[1500]|k[1501]<<8|k[1502]<<16|k[1503]<<24,u=k[1496]|k[1497]<<8|k[1498]<<16|k[1499]<<24,n[0|b]=u,n[b+1|0]=u>>>8,n[b+2|0]=u>>>16,n[b+3|0]=u>>>24,n[b+4|0]=a,n[b+5|0]=a>>>8,n[b+6|0]=a>>>16,n[b+7|0]=a>>>24,c[p>>2]=-1,me(a=4|p,b,28),l=n[p+15|0],c[f>>2]=c[p>>2],u=f+4|0,(0|l)>=0?(l=c[a+4>>2],c[u>>2]=c[a>>2],c[u+4>>2]=l,c[u+8>>2]=c[a+8>>2],c[f+16>>2]=0):(me(u,c[p+4>>2],c[p+8>>2]),a=n[p+15|0],c[f+16>>2]=0,(0|a)>=0||er(c[p+4>>2])),er(b)}Z=p+16|0;r:if(a=c[f>>2]){if(c[r>>2]=a,r=r+4|0,n[f+15|0]>=0){i=c[(e=4|f)+4>>2],c[r>>2]=c[e>>2],c[r+4>>2]=i,c[r+8>>2]=c[e+8>>2];break r}me(r,c[f+4>>2],c[f+8>>2])}else{a=c[f+16>>2],c[f+16>>2]=0,c[a+44>>2]=t,Z=b=Z-32|0,c[a+32>>2]=i,c[a+40>>2]=e,c[a+4>>2]=t,P(r,i,b+16|0);e:if(!c[r>>2])if(n[r+15|0]<0&&er(c[r+4>>2]),e=k[b+23|0],(0|Zt[c[c[a>>2]+8>>2]](a))==(0|e))if(i=k[b+21|0],n[a+36|0]=i,t=k[b+22|0],n[a+37|0]=t,2==(0|i))if((0|(e=e?2:3))==(0|t)){if(A[c[a+32>>2]+38>>1]=512|e,!(A[b+26>>1]>=0)){Z=p=Z-16|0,t=vi(36),c[(e=t)+4>>2]=0,c[e+8>>2]=0,c[e+24>>2]=0,c[e+28>>2]=0,c[(e=e+16|0)>>2]=0,c[e+4>>2]=0,c[t>>2]=t+4,c[t+32>>2]=0,c[t+12>>2]=e,c[p>>2]=0,i=c[a+32>>2],Z=l=Z-16|0,e=0;i:if(t&&(c[p>>2]=i,c[l+12>>2]=0,e=0,De(1,l+12|0,i))){if(m=c[l+12>>2])for(;;){t:{if(De(1,l+8|0,c[p>>2])){if(e=vi(28),c[e+4>>2]=0,c[e+8>>2]=0,c[(i=e+16|0)>>2]=0,c[i+4>>2]=0,c[e>>2]=e+4,c[e+12>>2]=i,c[e+24>>2]=c[l+8>>2],w(p,e))break t;Ai(e+12|0,c[e+16>>2]),mi(e,c[e+4>>2]),er(e)}e=0;break i}if(Z=u=Z-16|0,c[u+8>>2]=e,e){if((i=c[t+28>>2])>>>0<s[t+32>>2])c[u+8>>2]=0,c[i>>2]=e,c[t+28>>2]=i+4;else{i=0;t:{f:{a:{if(_=c[t+24>>2],(e=(d=c[t+28>>2]-_>>2)+1|0)>>>0<1073741824){if(y=(_=c[t+32>>2]-_|0)>>>1|0,_=_>>>0>=2147483644?1073741823:e>>>0<y>>>0?y:e){if(_>>>0>=1073741824)break a;i=vi(_<<2)}if(y=c[u+8>>2],c[u+8>>2]=0,c[(e=(d<<2)+i|0)>>2]=y,_=(_<<2)+i|0,d=e+4|0,(0|(i=c[t+28>>2]))==(0|(y=c[t+24>>2])))break f;for(;h=c[(i=i-4|0)>>2],c[i>>2]=0,c[(e=e-4|0)>>2]=h,(0|i)!=(0|y););if(c[t+32>>2]=_,_=c[t+28>>2],c[t+28>>2]=d,i=c[t+24>>2],c[t+24>>2]=e,(0|i)==(0|_))break t;for(;e=c[(_=_-4|0)>>2],c[_>>2]=0,e&&(Ai(e+12|0,c[e+16>>2]),mi(e,c[e+4>>2]),er(e)),(0|i)!=(0|_););break t}mt(),o()}Zi(),o()}c[t+32>>2]=_,c[t+28>>2]=d,c[t+24>>2]=e}i&&er(i)}e=c[u+8>>2],c[u+8>>2]=0,e&&(Ai(e+12|0,c[e+16>>2]),mi(e,c[e+4>>2]),er(e))}if(Z=u+16|0,(0|m)==(0|(v=v+1|0)))break}e=w(p,t)}if(Z=l+16|0,e?(i=c[a+4>>2],e=c[i+4>>2],c[i+4>>2]=t,e&&je(e),c[r>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[r+12>>2]=0):(e=vi(32),n[e+26|0]=0,i=k[1549]|k[1550]<<8,n[e+24|0]=i,n[e+25|0]=i>>>8,i=k[1545]|k[1546]<<8|k[1547]<<16|k[1548]<<24,u=k[1541]|k[1542]<<8|k[1543]<<16|k[1544]<<24,n[e+16|0]=u,n[e+17|0]=u>>>8,n[e+18|0]=u>>>16,n[e+19|0]=u>>>24,n[e+20|0]=i,n[e+21|0]=i>>>8,n[e+22|0]=i>>>16,n[e+23|0]=i>>>24,i=k[1537]|k[1538]<<8|k[1539]<<16|k[1540]<<24,u=k[1533]|k[1534]<<8|k[1535]<<16|k[1536]<<24,n[e+8|0]=u,n[e+9|0]=u>>>8,n[e+10|0]=u>>>16,n[e+11|0]=u>>>24,n[e+12|0]=i,n[e+13|0]=i>>>8,n[e+14|0]=i>>>16,n[e+15|0]=i>>>24,i=k[1529]|k[1530]<<8|k[1531]<<16|k[1532]<<24,u=k[1525]|k[1526]<<8|k[1527]<<16|k[1528]<<24,n[0|e]=u,n[e+1|0]=u>>>8,n[e+2|0]=u>>>16,n[e+3|0]=u>>>24,n[e+4|0]=i,n[e+5|0]=i>>>8,n[e+6|0]=i>>>16,n[e+7|0]=i>>>24,c[r>>2]=-1,me(r+4|0,e,26),er(e),c[p+8>>2]=0,je(t)),Z=p+16|0,c[r>>2])break e;n[r+15|0]>=0||er(c[r+4>>2])}if(0|Zt[c[c[a>>2]+12>>2]](a))if(0|Zt[c[c[a>>2]+20>>2]](a))if(0|Zt[c[c[a>>2]+24>>2]](a))c[r>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[r+12>>2]=0;else{if(e=Pe(b,1307),c[r>>2]=-1,i=r+4|0,n[e+11|0]>=0){t=c[e+4>>2],c[i>>2]=c[e>>2],c[i+4>>2]=t,c[i+8>>2]=c[e+8>>2];break e}if(me(i,c[e>>2],c[e+4>>2]),n[e+11|0]>=0)break e;er(c[e>>2])}else{if(e=Pe(b,1552),c[r>>2]=-1,i=r+4|0,n[e+11|0]>=0){t=c[e+4>>2],c[i>>2]=c[e>>2],c[i+4>>2]=t,c[i+8>>2]=c[e+8>>2];break e}if(me(i,c[e>>2],c[e+4>>2]),n[e+11|0]>=0)break e;er(c[e>>2])}else e=vi(48),n[e+33|0]=0,n[e+32|0]=k[1374],i=k[1370]|k[1371]<<8|k[1372]<<16|k[1373]<<24,t=k[1366]|k[1367]<<8|k[1368]<<16|k[1369]<<24,n[e+24|0]=t,n[e+25|0]=t>>>8,n[e+26|0]=t>>>16,n[e+27|0]=t>>>24,n[e+28|0]=i,n[e+29|0]=i>>>8,n[e+30|0]=i>>>16,n[e+31|0]=i>>>24,i=k[1362]|k[1363]<<8|k[1364]<<16|k[1365]<<24,t=k[1358]|k[1359]<<8|k[1360]<<16|k[1361]<<24,n[e+16|0]=t,n[e+17|0]=t>>>8,n[e+18|0]=t>>>16,n[e+19|0]=t>>>24,n[e+20|0]=i,n[e+21|0]=i>>>8,n[e+22|0]=i>>>16,n[e+23|0]=i>>>24,i=k[1354]|k[1355]<<8|k[1356]<<16|k[1357]<<24,t=k[1350]|k[1351]<<8|k[1352]<<16|k[1353]<<24,n[e+8|0]=t,n[e+9|0]=t>>>8,n[e+10|0]=t>>>16,n[e+11|0]=t>>>24,n[e+12|0]=i,n[e+13|0]=i>>>8,n[e+14|0]=i>>>16,n[e+15|0]=i>>>24,i=k[1346]|k[1347]<<8|k[1348]<<16|k[1349]<<24,t=k[1342]|k[1343]<<8|k[1344]<<16|k[1345]<<24,n[0|e]=t,n[e+1|0]=t>>>8,n[e+2|0]=t>>>16,n[e+3|0]=t>>>24,n[e+4|0]=i,n[e+5|0]=i>>>8,n[e+6|0]=i>>>16,n[e+7|0]=i>>>24,c[r>>2]=-1,me(r+4|0,e,33),er(e)}else e=vi(32),n[e+26|0]=0,i=k[1400]|k[1401]<<8,n[e+24|0]=i,n[e+25|0]=i>>>8,i=k[1396]|k[1397]<<8|k[1398]<<16|k[1399]<<24,t=k[1392]|k[1393]<<8|k[1394]<<16|k[1395]<<24,n[e+16|0]=t,n[e+17|0]=t>>>8,n[e+18|0]=t>>>16,n[e+19|0]=t>>>24,n[e+20|0]=i,n[e+21|0]=i>>>8,n[e+22|0]=i>>>16,n[e+23|0]=i>>>24,i=k[1388]|k[1389]<<8|k[1390]<<16|k[1391]<<24,t=k[1384]|k[1385]<<8|k[1386]<<16|k[1387]<<24,n[e+8|0]=t,n[e+9|0]=t>>>8,n[e+10|0]=t>>>16,n[e+11|0]=t>>>24,n[e+12|0]=i,n[e+13|0]=i>>>8,n[e+14|0]=i>>>16,n[e+15|0]=i>>>24,i=k[1380]|k[1381]<<8|k[1382]<<16|k[1383]<<24,t=k[1376]|k[1377]<<8|k[1378]<<16|k[1379]<<24,n[0|e]=t,n[e+1|0]=t>>>8,n[e+2|0]=t>>>16,n[e+3|0]=t>>>24,n[e+4|0]=i,n[e+5|0]=i>>>8,n[e+6|0]=i>>>16,n[e+7|0]=i>>>24,c[r>>2]=-5,me(r+4|0,e,26),er(e);else e=vi(32),n[e+26|0]=0,i=k[1427]|k[1428]<<8,n[e+24|0]=i,n[e+25|0]=i>>>8,i=k[1423]|k[1424]<<8|k[1425]<<16|k[1426]<<24,t=k[1419]|k[1420]<<8|k[1421]<<16|k[1422]<<24,n[e+16|0]=t,n[e+17|0]=t>>>8,n[e+18|0]=t>>>16,n[e+19|0]=t>>>24,n[e+20|0]=i,n[e+21|0]=i>>>8,n[e+22|0]=i>>>16,n[e+23|0]=i>>>24,i=k[1415]|k[1416]<<8|k[1417]<<16|k[1418]<<24,t=k[1411]|k[1412]<<8|k[1413]<<16|k[1414]<<24,n[e+8|0]=t,n[e+9|0]=t>>>8,n[e+10|0]=t>>>16,n[e+11|0]=t>>>24,n[e+12|0]=i,n[e+13|0]=i>>>8,n[e+14|0]=i>>>16,n[e+15|0]=i>>>24,i=k[1407]|k[1408]<<8|k[1409]<<16|k[1410]<<24,t=k[1403]|k[1404]<<8|k[1405]<<16|k[1406]<<24,n[0|e]=t,n[e+1|0]=t>>>8,n[e+2|0]=t>>>16,n[e+3|0]=t>>>24,n[e+4|0]=i,n[e+5|0]=i>>>8,n[e+6|0]=i>>>16,n[e+7|0]=i>>>24,c[r>>2]=-5,me(r+4|0,e,26),er(e);else e=vi(64),n[e+50|0]=0,i=k[1304]|k[1305]<<8,n[e+48|0]=i,n[e+49|0]=i>>>8,i=k[1300]|k[1301]<<8|k[1302]<<16|k[1303]<<24,t=k[1296]|k[1297]<<8|k[1298]<<16|k[1299]<<24,n[e+40|0]=t,n[e+41|0]=t>>>8,n[e+42|0]=t>>>16,n[e+43|0]=t>>>24,n[e+44|0]=i,n[e+45|0]=i>>>8,n[e+46|0]=i>>>16,n[e+47|0]=i>>>24,i=k[1292]|k[1293]<<8|k[1294]<<16|k[1295]<<24,t=k[1288]|k[1289]<<8|k[1290]<<16|k[1291]<<24,n[e+32|0]=t,n[e+33|0]=t>>>8,n[e+34|0]=t>>>16,n[e+35|0]=t>>>24,n[e+36|0]=i,n[e+37|0]=i>>>8,n[e+38|0]=i>>>16,n[e+39|0]=i>>>24,i=k[1284]|k[1285]<<8|k[1286]<<16|k[1287]<<24,t=k[1280]|k[1281]<<8|k[1282]<<16|k[1283]<<24,n[e+24|0]=t,n[e+25|0]=t>>>8,n[e+26|0]=t>>>16,n[e+27|0]=t>>>24,n[e+28|0]=i,n[e+29|0]=i>>>8,n[e+30|0]=i>>>16,n[e+31|0]=i>>>24,i=k[1276]|k[1277]<<8|k[1278]<<16|k[1279]<<24,t=k[1272]|k[1273]<<8|k[1274]<<16|k[1275]<<24,n[e+16|0]=t,n[e+17|0]=t>>>8,n[e+18|0]=t>>>16,n[e+19|0]=t>>>24,n[e+20|0]=i,n[e+21|0]=i>>>8,n[e+22|0]=i>>>16,n[e+23|0]=i>>>24,i=k[1268]|k[1269]<<8|k[1270]<<16|k[1271]<<24,t=k[1264]|k[1265]<<8|k[1266]<<16|k[1267]<<24,n[e+8|0]=t,n[e+9|0]=t>>>8,n[e+10|0]=t>>>16,n[e+11|0]=t>>>24,n[e+12|0]=i,n[e+13|0]=i>>>8,n[e+14|0]=i>>>16,n[e+15|0]=i>>>24,i=k[1260]|k[1261]<<8|k[1262]<<16|k[1263]<<24,t=k[1256]|k[1257]<<8|k[1258]<<16|k[1259]<<24,n[0|e]=t,n[e+1|0]=t>>>8,n[e+2|0]=t>>>16,n[e+3|0]=t>>>24,n[e+4|0]=i,n[e+5|0]=i>>>8,n[e+6|0]=i>>>16,n[e+7|0]=i>>>24,c[r>>2]=-1,me(r+4|0,e,50),er(e);Z=b+32|0,c[r>>2]||(n[r+15|0]<0&&er(c[r+4>>2]),c[r>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[r+12>>2]=0),Zt[c[c[a>>2]+4>>2]](a)}r=c[f+16>>2],c[f+16>>2]=0,r&&Zt[c[c[r>>2]+4>>2]](r),n[f+15|0]>=0||er(c[f+4>>2])}else e=vi(32),n[e+20|0]=0,i=k[1446]|k[1447]<<8|k[1448]<<16|k[1449]<<24,n[e+16|0]=i,n[e+17|0]=i>>>8,n[e+18|0]=i>>>16,n[e+19|0]=i>>>24,i=k[1442]|k[1443]<<8|k[1444]<<16|k[1445]<<24,t=k[1438]|k[1439]<<8|k[1440]<<16|k[1441]<<24,n[e+8|0]=t,n[e+9|0]=t>>>8,n[e+10|0]=t>>>16,n[e+11|0]=t>>>24,n[e+12|0]=i,n[e+13|0]=i>>>8,n[e+14|0]=i>>>16,n[e+15|0]=i>>>24,i=k[1434]|k[1435]<<8|k[1436]<<16|k[1437]<<24,t=k[1430]|k[1431]<<8|k[1432]<<16|k[1433]<<24,n[0|e]=t,n[e+1|0]=t>>>8,n[e+2|0]=t>>>16,n[e+3|0]=t>>>24,n[e+4|0]=i,n[e+5|0]=i>>>8,n[e+6|0]=i>>>16,n[e+7|0]=i>>>24,c[r>>2]=-1,me(r+4|0,e,20),er(e);Z=f+80|0}function Y(r){var e,i=0,t=0,f=0,a=0,n=0,A=0,o=0,b=0,u=0,_=0,p=0,l=0;Z=e=Z-16|0;r:{e:{i:{t:{f:{a:{n:{A:{o:{if((r|=0)>>>0<=244){if(3&(i=(A=c[2941])>>>(t=(o=r>>>0<11?16:r+11&-8)>>>3|0)|0)){i=(r=(t=t+(1&(-1^i))|0)<<3)+11804|0,f=c[r+11812>>2],(0|i)!=(0|(r=c[f+8>>2]))?(c[r+12>>2]=i,c[i+8>>2]=r):(p=11764,l=Yi(t)&A,c[p>>2]=l),r=f+8|0,i=t<<3,c[f+4>>2]=3|i,c[(i=i+f|0)+4>>2]=1|c[i+4>>2];break r}if((_=c[2943])>>>0>=o>>>0)break o;if(i){i=(r=(f=Xi(0-(r=(0-(r=2<<t)|r)&i<<t)&r))<<3)+11804|0,a=c[r+11812>>2],(0|i)!=(0|(r=c[a+8>>2]))?(c[r+12>>2]=i,c[i+8>>2]=r):(A=Yi(f)&A,c[2941]=A),c[a+4>>2]=3|o,f=(r=f<<3)-o|0,c[(t=a+o|0)+4>>2]=1|f,c[r+a>>2]=f,_&&(i=11804+(-8&_)|0,n=c[2946],(r=1<<(_>>>3))&A?r=c[i+8>>2]:(c[2941]=r|A,r=i),c[i+8>>2]=n,c[r+12>>2]=n,c[n+12>>2]=i,c[n+8>>2]=r),r=a+8|0,c[2946]=t,c[2943]=f;break r}if(!(u=c[2942]))break o;for(t=c[12068+(Xi(0-u&u)<<2)>>2],n=(-8&c[t+4>>2])-o|0,i=t;(r=c[i+16>>2])||(r=c[i+20>>2]);)n=(f=(i=(-8&c[r+4>>2])-o|0)>>>0<n>>>0)?i:n,t=f?r:t,i=r;if(b=c[t+24>>2],(0|(f=c[t+12>>2]))!=(0|t)){r=c[t+8>>2],c[r+12>>2]=f,c[f+8>>2]=r;break e}if(!(r=c[(i=t+20|0)>>2])){if(!(r=c[t+16>>2]))break A;i=t+16|0}for(;a=i,f=r,(r=c[(i=r+20|0)>>2])||(i=f+16|0,r=c[f+16>>2]););c[a>>2]=0;break e}if(o=-1,!(r>>>0>4294967231)&&(o=-8&(r=r+11|0),u=c[2942])){n=0-o|0,A=0,o>>>0<256||(A=31,o>>>0>16777215||(A=62+((o>>>38-(r=v(r>>>8|0))&1)-(r<<1)|0)|0));b:{u:{if(i=c[12068+(A<<2)>>2])for(r=0,t=o<<(31!=(0|A)?25-(A>>>1|0)|0:0);;){if(!((a=(-8&c[i+4>>2])-o|0)>>>0>=n>>>0||(f=i,n=a,a))){n=0,r=i;break u}if(a=c[i+20>>2],i=c[16+((t>>>29&4)+i|0)>>2],r=a?(0|a)==(0|i)?r:a:r,t<<=1,!i)break}else r=0;if(!(r|f)){if(f=0,!(r=(0-(r=2<<A)|r)&u))break o;r=c[12068+(Xi(r&0-r)<<2)>>2]}if(!r)break b}for(;n=(t=(i=(-8&c[r+4>>2])-o|0)>>>0<n>>>0)?i:n,f=t?r:f,r=(i=c[r+16>>2])||c[r+20>>2];);}if(!(!f|c[2943]-o>>>0<=n>>>0)){if(A=c[f+24>>2],(0|f)!=(0|(t=c[f+12>>2]))){r=c[f+8>>2],c[r+12>>2]=t,c[t+8>>2]=r;break i}if(!(r=c[(i=f+20|0)>>2])){if(!(r=c[f+16>>2]))break n;i=f+16|0}for(;a=i,t=r,(r=c[(i=r+20|0)>>2])||(i=t+16|0,r=c[t+16>>2]););c[a>>2]=0;break i}}}if((r=c[2943])>>>0>=o>>>0){f=c[2946],(i=r-o|0)>>>0>=16?(c[(t=f+o|0)+4>>2]=1|i,c[r+f>>2]=i,c[f+4>>2]=3|o):(c[f+4>>2]=3|r,c[(r=r+f|0)+4>>2]=1|c[r+4>>2],t=0,i=0),c[2943]=i,c[2946]=t,r=f+8|0;break r}if((b=c[2944])>>>0>o>>>0){i=b-o|0,c[2944]=i,r=(t=c[2947])+o|0,c[2947]=r,c[r+4>>2]=1|i,c[t+4>>2]=3|o,r=t+8|0;break r}if(r=0,u=o+47|0,c[3059]?t=c[3061]:(c[3062]=-1,c[3063]=-1,c[3060]=4096,c[3061]=4096,c[3059]=e+12&-16^1431655768,c[3064]=0,c[3052]=0,t=4096),(i=(a=u+t|0)&(n=0-t|0))>>>0<=o>>>0)break r;if((f=c[3051])&&f>>>0<(A=(t=c[3049])+i|0)>>>0|t>>>0>=A>>>0)break r;o:{if(!(4&k[12208])){b:{u:{c:{k:{if(f=c[2947])for(r=12212;;){if((t=c[r>>2])>>>0<=f>>>0&f>>>0<t+c[r+4>>2]>>>0)break k;if(!(r=c[r+8>>2]))break}if(-1==(0|(t=ni(0))))break b;if(A=i,(r=(f=c[3060])-1|0)&t&&(A=(i-t|0)+(r+t&0-f)|0),A>>>0<=o>>>0)break b;if((f=c[3051])&&f>>>0<(n=(r=c[3049])+A|0)>>>0|r>>>0>=n>>>0)break b;if((0|t)!=(0|(r=ni(A))))break c;break o}if((0|(t=ni(A=n&a-b)))==(c[r>>2]+c[r+4>>2]|0))break u;r=t}if(-1==(0|r))break b;if(o+48>>>0<=A>>>0){t=r;break o}if(-1==(0|ni(t=(t=c[3061])+(u-A|0)&0-t)))break b;A=t+A|0,t=r;break o}if(-1!=(0|t))break o}c[3052]=4|c[3052]}if(-1==(0|(t=ni(i)))|-1==(0|(r=ni(0)))|r>>>0<=t>>>0)break t;if((A=r-t|0)>>>0<=o+40>>>0)break t}r=c[3049]+A|0,c[3049]=r,r>>>0>s[3050]&&(c[3050]=r);o:{if(a=c[2947]){for(r=12212;;){if(((f=c[r>>2])+(i=c[r+4>>2])|0)==(0|t))break o;if(!(r=c[r+8>>2]))break}break a}for((r=c[2945])>>>0<=t>>>0&&r||(c[2945]=t),r=0,c[3054]=A,c[3053]=t,c[2949]=-1,c[2950]=c[3059],c[3056]=0;i=(f=r<<3)+11804|0,c[f+11812>>2]=i,c[f+11816>>2]=i,32!=(0|(r=r+1|0)););i=(f=A-40|0)-(r=t+8&7?-8-t&7:0)|0,c[2944]=i,r=r+t|0,c[2947]=r,c[r+4>>2]=1|i,c[4+(t+f|0)>>2]=40,c[2948]=c[3063];break f}if(8&k[r+12|0]|f>>>0>a>>>0|t>>>0<=a>>>0)break a;c[r+4>>2]=i+A,t=(r=a+8&7?-8-a&7:0)+a|0,c[2947]=t,r=(i=c[2944]+A|0)-r|0,c[2944]=r,c[t+4>>2]=1|r,c[4+(i+a|0)>>2]=40,c[2948]=c[3063];break f}f=0;break e}t=0;break i}s[2945]>t>>>0&&(c[2945]=t),i=t+A|0,r=12212;a:{n:{A:{o:{b:{u:{for(;;){if((0|i)!=c[r>>2]){if(r=c[r+8>>2])continue;break u}break}if(!(8&k[r+12|0]))break b}for(r=12212;;){if((i=c[r>>2])>>>0<=a>>>0&&(n=i+c[r+4>>2]|0)>>>0>a>>>0)break o;r=c[r+8>>2]}}if(c[r>>2]=t,c[r+4>>2]=c[r+4>>2]+A,c[(u=(t+8&7?-8-t&7:0)+t|0)+4>>2]=3|o,r=(A=i+(i+8&7?-8-i&7:0)|0)-(b=o+u|0)|0,(0|a)==(0|A)){c[2947]=b,r=c[2944]+r|0,c[2944]=r,c[b+4>>2]=1|r;break n}if(c[2946]==(0|A)){c[2946]=b,r=c[2943]+r|0,c[2943]=r,c[b+4>>2]=1|r,c[r+b>>2]=r;break n}if(1==(3&(n=c[A+4>>2]))){a=-8&n;b:if(n>>>0<=255){if(f=c[A+8>>2],i=n>>>3|0,(0|(t=c[A+12>>2]))==(0|f)){p=11764,l=c[2941]&Yi(i),c[p>>2]=l;break b}c[f+12>>2]=t,c[t+8>>2]=f}else{if(o=c[A+24>>2],(0|A)==(0|(t=c[A+12>>2])))if((i=c[(n=A+20|0)>>2])||(i=c[(n=A+16|0)>>2])){for(;f=n,(i=c[(n=(t=i)+20|0)>>2])||(n=t+16|0,i=c[t+16>>2]););c[f>>2]=0}else t=0;else i=c[A+8>>2],c[i+12>>2]=t,c[t+8>>2]=i;if(o){f=c[A+28>>2];u:{if(c[(i=12068+(f<<2)|0)>>2]==(0|A)){if(c[i>>2]=t,t)break u;p=11768,l=c[2942]&Yi(f),c[p>>2]=l;break b}if(c[o+(c[o+16>>2]==(0|A)?16:20)>>2]=t,!t)break b}c[t+24>>2]=o,(i=c[A+16>>2])&&(c[t+16>>2]=i,c[i+24>>2]=t),(i=c[A+20>>2])&&(c[t+20>>2]=i,c[i+24>>2]=t)}}n=c[(A=a+A|0)+4>>2],r=r+a|0}if(c[A+4>>2]=-2&n,c[b+4>>2]=1|r,c[r+b>>2]=r,r>>>0<=255){i=11804+(-8&r)|0,(t=c[2941])&(r=1<<(r>>>3))?r=c[i+8>>2]:(c[2941]=r|t,r=i),c[i+8>>2]=b,c[r+12>>2]=b,c[b+12>>2]=i,c[b+8>>2]=r;break n}if(n=31,r>>>0<=16777215&&(n=62+((r>>>38-(i=v(r>>>8|0))&1)-(i<<1)|0)|0),c[b+28>>2]=n,c[b+16>>2]=0,c[b+20>>2]=0,i=12068+(n<<2)|0,(f=c[2942])&(t=1<<n)){for(n=r<<(31!=(0|n)?25-(n>>>1|0)|0:0),t=c[i>>2];;){if(i=t,(-8&c[t+4>>2])==(0|r))break A;if(t=n>>>29|0,n<<=1,!(t=c[(f=(4&t)+i|0)+16>>2]))break}c[f+16>>2]=b}else c[2942]=t|f,c[i>>2]=b;c[b+24>>2]=i,c[b+12>>2]=b,c[b+8>>2]=b;break n}for(i=(f=A-40|0)-(r=t+8&7?-8-t&7:0)|0,c[2944]=i,r=r+t|0,c[2947]=r,c[r+4>>2]=1|i,c[4+(t+f|0)>>2]=40,c[2948]=c[3063],c[(f=(r=(n+(n-39&7?39-n&7:0)|0)-47|0)>>>0<a+16>>>0?a:r)+4>>2]=27,r=c[3056],c[f+16>>2]=c[3055],c[f+20>>2]=r,r=c[3054],c[f+8>>2]=c[3053],c[f+12>>2]=r,c[3055]=f+8,c[3054]=A,c[3053]=t,c[3056]=0,r=f+24|0;c[r+4>>2]=7,i=r+8|0,r=r+4|0,i>>>0<n>>>0;);if((0|f)==(0|a))break f;if(c[f+4>>2]=-2&c[f+4>>2],n=f-a|0,c[a+4>>2]=1|n,c[f>>2]=n,n>>>0<=255){i=11804+(-8&n)|0,(t=c[2941])&(r=1<<(n>>>3))?r=c[i+8>>2]:(c[2941]=r|t,r=i),c[i+8>>2]=a,c[r+12>>2]=a,c[a+12>>2]=i,c[a+8>>2]=r;break f}if(r=31,n>>>0<=16777215&&(r=62+((n>>>38-(r=v(n>>>8|0))&1)-(r<<1)|0)|0),c[a+28>>2]=r,c[a+16>>2]=0,c[a+20>>2]=0,i=12068+(r<<2)|0,(f=c[2942])&(t=1<<r)){for(r=n<<(31!=(0|r)?25-(r>>>1|0)|0:0),f=c[i>>2];;){if((0|n)==(-8&c[(i=f)+4>>2]))break a;if(t=r>>>29|0,r<<=1,!(f=c[(t=(4&t)+i|0)+16>>2]))break}c[t+16>>2]=a}else c[2942]=t|f,c[i>>2]=a;c[a+24>>2]=i,c[a+12>>2]=a,c[a+8>>2]=a;break f}r=c[i+8>>2],c[r+12>>2]=b,c[i+8>>2]=b,c[b+24>>2]=0,c[b+12>>2]=i,c[b+8>>2]=r}r=u+8|0;break r}r=c[i+8>>2],c[r+12>>2]=a,c[i+8>>2]=a,c[a+24>>2]=0,c[a+12>>2]=i,c[a+8>>2]=r}if(!((r=c[2944])>>>0<=o>>>0)){i=r-o|0,c[2944]=i,r=(t=c[2947])+o|0,c[2947]=r,c[r+4>>2]=1|i,c[t+4>>2]=3|o,r=t+8|0;break r}}c[2940]=48,r=0;break r}i:if(A){i=c[f+28>>2];t:{if(c[(r=12068+(i<<2)|0)>>2]==(0|f)){if(c[r>>2]=t,t)break t;u=Yi(i)&u,c[2942]=u;break i}if(c[A+(c[A+16>>2]==(0|f)?16:20)>>2]=t,!t)break i}c[t+24>>2]=A,(r=c[f+16>>2])&&(c[t+16>>2]=r,c[r+24>>2]=t),(r=c[f+20>>2])&&(c[t+20>>2]=r,c[r+24>>2]=t)}i:if(n>>>0<=15)r=n+o|0,c[f+4>>2]=3|r,c[(r=r+f|0)+4>>2]=1|c[r+4>>2];else if(c[f+4>>2]=3|o,c[(a=f+o|0)+4>>2]=1|n,c[a+n>>2]=n,n>>>0<=255)i=11804+(-8&n)|0,(t=c[2941])&(r=1<<(n>>>3))?r=c[i+8>>2]:(c[2941]=r|t,r=i),c[i+8>>2]=a,c[r+12>>2]=a,c[a+12>>2]=i,c[a+8>>2]=r;else{r=31,n>>>0<=16777215&&(r=62+((n>>>38-(r=v(n>>>8|0))&1)-(r<<1)|0)|0),c[a+28>>2]=r,c[a+16>>2]=0,c[a+20>>2]=0,i=12068+(r<<2)|0;t:{if((t=1<<r)&u){for(r=n<<(31!=(0|r)?25-(r>>>1|0)|0:0),o=c[i>>2];;){if((-8&c[(i=o)+4>>2])==(0|n))break t;if(t=r>>>29|0,r<<=1,!(o=c[(t=(4&t)+i|0)+16>>2]))break}c[t+16>>2]=a}else c[2942]=t|u,c[i>>2]=a;c[a+24>>2]=i,c[a+12>>2]=a,c[a+8>>2]=a;break i}r=c[i+8>>2],c[r+12>>2]=a,c[i+8>>2]=a,c[a+24>>2]=0,c[a+12>>2]=i,c[a+8>>2]=r}r=f+8|0;break r}e:if(b){i=c[t+28>>2];i:{if(c[(r=12068+(i<<2)|0)>>2]==(0|t)){if(c[r>>2]=f,f)break i;p=11768,l=Yi(i)&u,c[p>>2]=l;break e}if(c[b+(c[b+16>>2]==(0|t)?16:20)>>2]=f,!f)break e}c[f+24>>2]=b,(r=c[t+16>>2])&&(c[f+16>>2]=r,c[r+24>>2]=f),(r=c[t+20>>2])&&(c[f+20>>2]=r,c[r+24>>2]=f)}n>>>0<=15?(r=n+o|0,c[t+4>>2]=3|r,c[(r=r+t|0)+4>>2]=1|c[r+4>>2]):(c[t+4>>2]=3|o,c[(f=t+o|0)+4>>2]=1|n,c[f+n>>2]=n,_&&(i=11804+(-8&_)|0,a=c[2946],(r=1<<(_>>>3))&A?r=c[i+8>>2]:(c[2941]=r|A,r=i),c[i+8>>2]=a,c[r+12>>2]=a,c[a+12>>2]=i,c[a+8>>2]=r),c[2946]=f,c[2943]=n),r=t+8|0}return Z=e+16|0,0|r}function w(r,e){var i,t=0,f=0,a=0,A=0,b=0,u=0,_=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,E=0;Z=i=Z-32|0,m=vi(12),c[m+8>>2]=0,c[m+4>>2]=e,c[m>>2]=0,e=N=m+12|0;r:{e:{i:{for(;;){if(U=c[(e=e-12|0)+8>>2],s=c[e+4>>2],T=c[e>>2]){if((0|U)>1e3)break r;c[i+24>>2]=0,c[i+16>>2]=0,c[i+20>>2]=0,f=1,t=c[r>>2],a=c[t+8>>2];t:if(!((0|(u=c[t+12>>2]))<=(0|(b=c[t+20>>2]))&(A=c[t+16>>2])>>>0>=a>>>0|(0|b)>(0|u))){if(a=k[A+c[t>>2]|0],u=t,t=b,t=(A=A+1|0)?t:t+1|0,c[u+16>>2]=A,c[u+20>>2]=t,Tr(i+16|0,a),a){if(t=c[r>>2],d=di(i+16|0),v=c[t+8>>2],b=c[t+12>>2],u=c[t+20>>2],(0|b)<=(0|(u=(p=(A=c[t+16>>2])+a|0)>>>0<a>>>0?u+1|0:u))&p>>>0>v>>>0|(0|b)<(0|u))break t;hr(d,A+c[t>>2]|0,a),f=c[t+20>>2],f=(A=a)>>>0>(a=a+c[t+16>>2]|0)>>>0?f+1|0:f,c[t+16>>2]=a,c[t+20>>2]=f}s=vi(24),c[(t=s)+4>>2]=0,c[t+8>>2]=0,c[(t=t+16|0)>>2]=0,c[t+4>>2]=0,c[s>>2]=s+4,c[s+12>>2]=t,Z=a=Z-32|0;f:if((0|(V=te(u=T+12|0,t=i+16|0)))!=(0|(_=T+16|0)))s&&(Ai(s+12|0,c[s+16>>2]),mi(s,c[s+4>>2]),er(s));else{c[a+16>>2]=t;a:{n:{A:if(f=c[u+4>>2]){for(d=(b=(A=k[t+11|0])<<24>>24<0)?c[t>>2]:t,b=b?c[t+4>>2]:A;;){o:{b:{u:{c:{k:if(p=(v=(f=(A=(f=k[(t=f)+27|0])<<24>>24<0)?c[t+20>>2]:f)>>>0<b>>>0)?f:b){if(!(h=Ye(d,A=A?c[t+16>>2]:t+16|0,p))){if(f>>>0>b>>>0)break k;break c}if((0|h)>=0)break c}else if(f>>>0<=b>>>0)break u;if(A=t,f=c[t>>2])continue;break A}if(f=Ye(A,d,p))break b}if(v)break o;break n}if((0|f)>=0)break n}if(!(f=c[t+4>>2]))break}A=t+4|0}else t=A=u+4|0;d=(f=vi(32))+16|0,b=c[a+16>>2],n[b+11|0]>=0?(v=c[b+4>>2],c[d>>2]=c[b>>2],c[d+4>>2]=v,c[d+8>>2]=c[b+8>>2]):me(d,c[b>>2],c[b+4>>2]),c[f+8>>2]=t,c[f>>2]=0,c[f+4>>2]=0,c[f+28>>2]=0,c[A>>2]=f,t=f,(b=c[c[u>>2]>>2])&&(c[u>>2]=b,t=c[A>>2]),Ur(c[u+4>>2],t),c[u+8>>2]=c[u+8>>2]+1,t=1;break a}f=t,t=0}if(n[a+28|0]=t,c[a+24>>2]=f,f=c[a+24>>2],t=c[f+28>>2],c[f+28>>2]=s,!t)break f;Ai(t+12|0,c[t+16>>2]),mi(t,c[t+4>>2]),er(t)}Z=a+32|0,f=(0|_)!=(0|V)}if(n[i+27|0]<0&&er(c[i+16>>2]),f)break r}if(!s)break r;if(c[i+16>>2]=0,!De(1,i+16|0,c[r>>2]))break r;if(h=0,W=c[i+16>>2])for(;;){f=0,Z=_=Z-32|0,c[_+24>>2]=0,c[_+16>>2]=0,c[_+20>>2]=0,t=c[r>>2],A=c[t+8>>2];t:{f:{a:if(!((0|(u=c[t+12>>2]))<=(0|(b=c[t+20>>2]))&(a=c[t+16>>2])>>>0>=A>>>0|(0|b)>(0|u))){if(A=k[a+c[t>>2]|0],u=t,t=b,t=(a=a+1|0)?t:t+1|0,c[u+16>>2]=a,c[u+20>>2]=t,Tr(_+16|0,A),A){if(a=c[r>>2],d=di(_+16|0),v=c[a+8>>2],b=c[a+12>>2],t=c[a+20>>2],(p=(u=c[a+16>>2])+A|0)>>>0>v>>>0&(0|(t=p>>>0<A>>>0?t+1|0:t))>=(0|b)|(0|t)>(0|b))break a;hr(d,u+c[a>>2]|0,A),t=c[a+20>>2],t=(b=A)>>>0>(A=A+c[a+16>>2]|0)>>>0?t+1|0:t,c[a+16>>2]=A,c[a+20>>2]=t}if(c[_+12>>2]=0,De(1,_+12|0,c[r>>2])&&(A=c[_+12>>2])&&(a=c[r>>2],b=(t=c[a+8>>2])-(u=c[a+16>>2])|0,!((0|(t=c[a+12>>2]-(c[a+20>>2]+(t>>>0<u>>>0)|0)|0))<=0&A>>>0>b>>>0|(0|t)<0))){if(c[_+8>>2]=0,c[_>>2]=0,c[_+4>>2]=0,(0|A)<0)break f;f=vi(A),c[_>>2]=f,t=f+A|0,c[_+8>>2]=t,l=Sr(f,0,A),c[_+4>>2]=t,D=u=c[a+12>>2],v=c[a+8>>2],t=c[a+20>>2],V=b=A+(p=c[a+16>>2])|0,d=t=b>>>0<A>>>0?t+1|0:t;n:{if((0|t)<=(0|u)&b>>>0<=v>>>0|(0|t)<(0|u)){if(hr(l,c[a>>2]+p|0,A),f=c[a+20>>2],f=(t=A+c[a+16>>2]|0)>>>0<A>>>0?f+1|0:f,c[a+16>>2]=t,c[a+20>>2]=f,Z=u=Z-48|0,(0|(a=te(s,_+16|0)))!=(s+4|0)){if(t=c[a+4>>2])for(;f=t,t=c[t>>2];);else for(t=a;f=c[t+8>>2],A=c[f>>2]!=(0|t),t=f,A;);(0|a)==c[s>>2]&&(c[s>>2]=f),c[s+8>>2]=c[s+8>>2]-1,A=c[s+4>>2];A:{o:{if(b=a,a=c[(f=a)>>2]){if(!(t=c[b+4>>2]))break o;for(;f=t,t=c[t>>2];);}if(!(a=c[f+4>>2])){a=0,p=1;break A}}c[a+8>>2]=c[f+8>>2],p=0}l=c[f+8>>2];A:if((0|f)!=(0|(t=c[l>>2])))c[l+4>>2]=a;else{if(c[l>>2]=a,(0|f)==(0|A)){t=0,A=a;break A}t=c[l+4>>2]}R=!k[f+12|0],(0|f)!=(0|b)&&(l=c[b+8>>2],c[f+8>>2]=l,c[l+(((0|b)!=c[c[b+8>>2]>>2])<<2)>>2]=f,l=c[b>>2],c[f>>2]=l,c[l+8>>2]=f,l=c[b+4>>2],c[f+4>>2]=l,l&&(c[l+8>>2]=f),n[f+12|0]=k[b+12|0],A=(0|A)==(0|b)?f:A);A:if(!(R|!A)){if(p)for(;;){a=k[t+12|0];o:{if(f=c[t+8>>2],c[f>>2]!=(0|t)){a||(n[t+12|0]=1,n[f+12|0]=0,a=c[f+4>>2],p=c[a>>2],c[f+4>>2]=p,p&&(c[p+8>>2]=f),c[a+8>>2]=c[f+8>>2],p=c[f+8>>2],c[(((0|f)!=c[p>>2])<<2)+p>>2]=a,c[a>>2]=f,c[f+8>>2]=a,f=t,A=(0|(t=c[t>>2]))==(0|A)?f:A,t=c[t+4>>2]);b:{u:{f=c[t>>2];c:{if(k[f+12|0]||!f){if(a=c[t+4>>2],!k[a+12|0]&&a)break c;if(n[t+12|0]=0,(0|A)!=(0|(t=c[t+8>>2]))){if(k[t+12|0])break o}else t=A;n[t+12|0]=1;break A}if(!(a=c[t+4>>2]))break u}if(!k[a+12|0]){f=t;break b}}n[f+12|0]=1,n[t+12|0]=0,a=c[f+4>>2],c[t>>2]=a,a&&(c[a+8>>2]=t),c[f+8>>2]=c[t+8>>2],a=c[t+8>>2],c[((c[a>>2]!=(0|t))<<2)+a>>2]=f,c[f+4>>2]=t,c[t+8>>2]=f,a=t}t=c[f+8>>2],n[f+12|0]=k[t+12|0],n[t+12|0]=1,n[a+12|0]=1,f=c[t+4>>2],a=c[f>>2],c[t+4>>2]=a,a&&(c[a+8>>2]=t),c[f+8>>2]=c[t+8>>2],a=c[t+8>>2],c[(((0|t)!=c[a>>2])<<2)+a>>2]=f,c[f>>2]=t,c[t+8>>2]=f;break A}a||(n[t+12|0]=1,n[f+12|0]=0,a=c[t+4>>2],c[f>>2]=a,a&&(c[a+8>>2]=f),c[t+8>>2]=c[f+8>>2],a=c[f+8>>2],c[(((0|f)!=c[a>>2])<<2)+a>>2]=t,c[t+4>>2]=f,c[f+8>>2]=t,A=(0|f)==(0|A)?t:A,t=c[f>>2]);b:if(!(a=c[t>>2])|k[a+12|0]){if(f=c[t+4>>2],k[f+12|0]||!f){if(n[t+12|0]=0,(0|(t=c[t+8>>2]))!=(0|A)&&k[t+12|0])break o;n[t+12|0]=1;break A}if(a){if(!k[a+12|0]){f=t;break b}f=c[t+4>>2]}n[f+12|0]=1,n[t+12|0]=0,a=c[f>>2],c[t+4>>2]=a,a&&(c[a+8>>2]=t),c[f+8>>2]=c[t+8>>2],a=c[t+8>>2],c[((c[a>>2]!=(0|t))<<2)+a>>2]=f,c[f>>2]=t,c[t+8>>2]=f,a=t}else f=t;t=c[f+8>>2],n[f+12|0]=k[t+12|0],n[t+12|0]=1,n[a+12|0]=1,f=c[t>>2],a=c[f+4>>2],c[t>>2]=a,a&&(c[a+8>>2]=t),c[f+8>>2]=c[t+8>>2],a=c[t+8>>2],c[(((0|t)!=c[a>>2])<<2)+a>>2]=f,c[f+4>>2]=t,c[t+8>>2]=f;break A}f=t,t=c[t+8>>2],t=c[(((0|f)==c[t>>2])<<2)+t>>2]}n[a+12|0]=1}(t=c[b+28>>2])&&(c[b+32>>2]=t,er(t)),n[b+27|0]<0&&er(c[b+16>>2]),er(b)}c[u+8>>2]=0,c[u>>2]=0,c[u+4>>2]=0,A=(t=c[_+4>>2])-(f=c[_>>2])|0,a=0;A:{o:{if((0|t)!=(0|f)){if((0|A)<0)break o;b=(t=Sr(a=vi(A),0,A))+A|0,c[u+8>>2]=b,c[u+4>>2]=b,c[u>>2]=t,t=f}hr(a,t,A),n[_+27|0]>=0?(c[u+24>>2]=c[_+24>>2],t=c[_+20>>2],c[u+16>>2]=c[_+16>>2],c[u+20>>2]=t):me(u+16|0,c[_+16>>2],c[_+20>>2]),Me(u+28|0,u),t=A=u+16|0;b:{u:{c:if(f=c[s+4>>2]){for(p=(b=(a=k[t+11|0])<<24>>24<0)?c[t>>2]:t,b=b?c[t+4>>2]:a;;){k:{_:{s:{p:{l:if(R=(l=(f=(a=(f=k[(t=f)+27|0])<<24>>24<0)?c[t+20>>2]:f)>>>0<b>>>0)?f:b){if(!(G=Ye(p,a=a?c[t+16>>2]:t+16|0,R))){if(f>>>0>b>>>0)break l;break p}if((0|G)>=0)break p}else if(f>>>0<=b>>>0)break s;if(a=t,f=c[t>>2])continue;break c}if(f=Ye(a,p,R))break _}if(l)break k;break u}if((0|f)>=0)break u}if(!(f=c[t+4>>2]))break}a=t+4|0}else t=a=s+4|0;f=vi(40),c[f+24>>2]=c[A+8>>2],b=c[A+4>>2],c[f+16>>2]=c[A>>2],c[f+20>>2]=b,c[A>>2]=0,c[A+4>>2]=0,c[A+8>>2]=0,Me(f+28|0,A+12|0),c[f+8>>2]=t,c[f>>2]=0,c[f+4>>2]=0,c[a>>2]=f,t=f,(A=c[c[s>>2]>>2])&&(c[s>>2]=A,t=c[a>>2]),Ur(c[s+4>>2],t),c[s+8>>2]=c[s+8>>2]+1,t=1;break b}f=t,t=0}n[u+44|0]=t,c[u+40>>2]=f,(t=c[u+28>>2])&&(c[u+32>>2]=t,er(t)),n[u+27|0]<0&&er(c[u+16>>2]),(t=c[u>>2])&&(c[u+4>>2]=t,er(t)),Z=u+48|0;break A}mt(),o()}if(!(f=c[_>>2]))break n}c[_+4>>2]=f,er(f)}f=(0|d)<=(0|D)&v>>>0>=V>>>0|(0|d)<(0|D)}}n[_+27|0]<0&&er(c[_+16>>2]),Z=_+32|0;break t}mt(),o()}if(!f)break r;if((0|W)==(0|(h=h+1|0)))break}if(c[i+12>>2]=0,!De(1,i+12|0,c[r>>2]))break r;if(t=c[r>>2],(u=(a=c[t+8>>2])-(A=c[t+16>>2])|0)>>>0<(f=c[i+12>>2])>>>0&(0|(t=c[t+12>>2]-(c[t+20>>2]+(a>>>0<A>>>0)|0)|0))<=0|(0|t)<0)break r;if(f)for(h=0,u=(0!=(0|T))+U|0;;){if(e>>>0<N>>>0)c[e+8>>2]=u,c[e+4>>2]=0,c[e>>2]=s,e=e+12|0,f=c[i+12>>2];else{if((e=(b=(0|(t=e-m|0))/12|0)+1|0)>>>0>=357913942)break i;if(A=(a=(N-m|0)/12|0)<<1,a=a>>>0>=178956970?357913941:e>>>0<A>>>0?A:e){if(a>>>0>=357913942)break e;A=vi(y(a,12))}else A=0;e=A+y(b,12)|0,c[e+8>>2]=u,c[e+4>>2]=0,c[e>>2]=s,t=gr(e+y((0|t)/-12|0,12)|0,m,t),N=A+y(a,12)|0,e=e+12|0,m&&er(m),m=t}if(!((h=h+1|0)>>>0<f>>>0))break}if((0|e)==(0|m))break}E=1;break r}mt(),o()}Zi(),o()}return m&&er(m),Z=i+32|0,E}function j(r,e){var i,t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0,_=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0;Z=i=Z-32|0,A=c[c[r+4>>2]+44>>2],t=c[r+8>>2],f=c[t>>2],t=c[t+4>>2],c[i+24>>2]=0,c[i+16>>2]=0,c[i+20>>2]=0,f=(t-f>>2>>>0)/3|0,t=c[A+96>>2];r:{if(f>>>0>(n=(c[A+100>>2]-t|0)/12|0)>>>0){if((a=f-n|0)>>>0<=((b=c[A+104>>2])-(t=c[A+100>>2])|0)/12>>>0){if(a){if(f=t,b=1+(((n=y(a,12)-12|0)>>>0)/12|0)&3)for(;_=c[i+20>>2],c[f>>2]=c[i+16>>2],c[f+4>>2]=_,c[f+8>>2]=c[i+24>>2],f=f+12|0,(0|b)!=(0|(u=u+1|0)););if(t=y(a,12)+t|0,!(n>>>0<36))for(;n=c[i+20>>2],c[f>>2]=c[i+16>>2],c[f+4>>2]=n,c[f+8>>2]=c[i+24>>2],c[f+20>>2]=c[i+24>>2],n=c[i+20>>2],c[f+12>>2]=c[i+16>>2],c[f+16>>2]=n,c[f+32>>2]=c[i+24>>2],n=c[i+20>>2],c[f+24>>2]=c[i+16>>2],c[f+28>>2]=n,n=c[i+20>>2],c[f+36>>2]=c[i+16>>2],c[f+40>>2]=n,c[f+44>>2]=c[i+24>>2],(0|(f=f+48|0))!=(0|t););}c[A+100>>2]=t;break r}e:{if((f=(p=(t-(n=c[A+96>>2])|0)/12|0)+a|0)>>>0<357913942){if(b=(n=(b-n|0)/12|0)<<1,b=n>>>0>=178956970?357913941:f>>>0<b>>>0?b:f){if(b>>>0>=357913942)break e;_=vi(y(b,12))}if(f=n=y(p,12)+_|0,m=1+(((p=(a=y(a,12))-12|0)>>>0)/12|0)&3)for(;v=c[i+20>>2],c[f>>2]=c[i+16>>2],c[f+4>>2]=v,c[f+8>>2]=c[i+24>>2],f=f+12|0,(0|m)!=(0|(u=u+1|0)););if(a=a+n|0,p>>>0>=36)for(;u=c[i+20>>2],c[f>>2]=c[i+16>>2],c[f+4>>2]=u,c[f+8>>2]=c[i+24>>2],c[f+20>>2]=c[i+24>>2],u=c[i+20>>2],c[f+12>>2]=c[i+16>>2],c[f+16>>2]=u,c[f+32>>2]=c[i+24>>2],u=c[i+20>>2],c[f+24>>2]=c[i+16>>2],c[f+28>>2]=u,u=c[i+20>>2],c[f+36>>2]=c[i+16>>2],c[f+40>>2]=u,c[f+44>>2]=c[i+24>>2],(0|a)!=(0|(f=f+48|0)););if((0|(u=c[A+96>>2]))!=(0|t)){for(;p=c[(t=t-12|0)+4>>2],c[(f=n=n-12|0)>>2]=c[t>>2],c[f+4>>2]=p,c[f+8>>2]=c[t+8>>2],(0|t)!=(0|u););t=c[A+96>>2]}c[A+104>>2]=y(b,12)+_,c[A+100>>2]=a,c[A+96>>2]=n,t&&er(t);break r}mt(),o()}Zi(),o()}f>>>0>=n>>>0||(c[A+100>>2]=t+y(f,12))}if(c[r+216>>2]!=c[r+220>>2]){f=0,c[i+24>>2]=0,c[i+16>>2]=0,c[i+20>>2]=0,_=c[r+8>>2],t=c[_>>2],A=c[_+4>>2],c[i+8>>2]=0,c[i>>2]=0,c[i+4>>2]=0,e=0;r:{e:{i:{t:{f:{a:{if((0|t)!=(0|A)){if((0|(t=A-t|0))<0)break a;e=vi(t),c[i>>2]=e,c[i+8>>2]=(-4&t)+e,N=i,T=Sr(e,0,t)+t|0,c[N+4>>2]=T}if(t=c[_+24>>2],(c[_+28>>2]-t|0)<4)break e;for(n=0;;){n:if(-1!=(0|(A=c[(d<<2)+t>>2]))){A:if(!(c[c[r+120>>2]+(d>>>3&536870908)>>2]>>>d&1)&&(0|(p=c[r+216>>2]))!=(0|(t=c[r+220>>2])))for(a=A+2|0,m=(b=(A>>>0)%3|0)?A-1|0:a,v=(t=(t-p|0)/144|0)>>>0<=1?1:t,u=0,R=0!=(0|b)|-1!=(0|a);;){if(h=A<<2,b=y(u,144)+p|0,t=c[h+c[c[b+68>>2]>>2]>>2],c[c[b+16>>2]+(t>>>3&536870908)>>2]>>>t&1&&(t=-1,R&&(t=-1,-1!=(0|(a=c[c[_+12>>2]+(m<<2)>>2]))&&(t=a-1|0,(a>>>0)%3|0||(t=a+2|0))),(0|A)!=(0|t)))for(a=h,h=c[b+32>>2],b=c[a+h>>2];;){if(a=0,-1==(0|t))break r;if((0|b)!=c[h+(t<<2)>>2]){A=t;break A}o:{if((t>>>0)%3|0)a=t-1|0;else if(s=-1,-1==(0|(a=t+2|0)))break o;s=-1,-1!=(0|(t=c[c[_+12>>2]+(a<<2)>>2]))&&(s=t-1|0,(t>>>0)%3|0||(s=t+2|0))}if((0|A)==(0|(t=s)))break}if((0|v)==(0|(u=u+1|0)))break}if(a=(b=k-n|0)>>2,c[(A<<2)+e>>2]=a,k>>>0<l>>>0)c[k>>2]=A,k=k+4|0,c[i+20>>2]=k;else{if((t=a+1|0)>>>0>=1073741824)break f;if(k=(f=l-n|0)>>>1|0,t=f>>>0>=2147483644?1073741823:t>>>0<k>>>0?k:t){if(t>>>0>=1073741824)break t;f=vi(t<<2)}else f=0;c[(a=f+(a<<2)|0)>>2]=A,l=(s=t<<2)+(t=gr(f,n,b))|0,c[i+24>>2]=l,k=a+4|0,c[i+20>>2]=k,c[i+16>>2]=t,n&&(er(n),_=c[r+8>>2]),n=t}if(-1!=(0|A)){if((A>>>0)%3|0)t=A-1|0;else if(-1==(0|(t=A+2|0)))break n;if(-1!=(0|(t=c[c[_+12>>2]+(t<<2)>>2]))&&-1!=(0|(t=t+((t>>>0)%3|0?-1:2)|0))&&(a=A,(0|t)!=(0|A)))for(;;){b=t;A:{o:if((0|(t=c[r+220>>2]))!=(0|(u=c[r+216>>2]))){for(p=(t=(t-u|0)/144|0)>>>0<=1?1:t,t=0;;){if(m=c[32+(u+y(t,144)|0)>>2],c[m+(v=b<<2)>>2]==c[m+(a<<2)>>2]){if((0|p)!=(0|(t=t+1|0)))continue;break o}break}if(a=(u=k-f|0)>>2,c[e+v>>2]=a,k>>>0<l>>>0){c[k>>2]=b,k=k+4|0,c[i+20>>2]=k,n=f;break A}if((t=a+1|0)>>>0>=1073741824)break i;if(k=(n=l-f|0)>>>1|0,t=n>>>0>=2147483644?1073741823:t>>>0<k>>>0?k:t){if(t>>>0>=1073741824)break t;n=vi(t<<2)}else n=0;if(c[(a=n+(a<<2)|0)>>2]=b,l=(s=t<<2)+(t=gr(n,f,u))|0,c[i+24>>2]=l,k=a+4|0,c[i+20>>2]=k,c[i+16>>2]=t,!f){f=t;break A}er(f),_=c[r+8>>2],f=t;break A}c[(b<<2)+e>>2]=c[(a<<2)+e>>2]}if(-1==(0|b))break n;if((b>>>0)%3|0)t=b-1|0;else if(-1==(0|(t=b+2|0)))break n;if(-1==(0|(t=c[c[_+12>>2]+(t<<2)>>2])))break n;if(-1==(0|(t=t+((t>>>0)%3|0?-1:2)|0)))break n;if(a=b,(0|t)==(0|A))break}}}if(d=d+1|0,t=c[_+24>>2],!((0|d)<c[_+28>>2]-t>>2))break}break e}mt(),o()}mt(),o()}Zi(),o()}mt(),o()}if(b=c[r+4>>2],r=c[b+44>>2],(0|(t=c[r+100>>2]))!=(0|(r=c[r+96>>2]))){if(_=1&(n=(A=(t-r|0)/12|0)>>>0<=1?1:A),t=0,A>>>0>=2)for(u=-2&n,A=0;a=y(t,12),l=c[(n=a+e|0)>>2],d=c[n+4>>2],c[(a=r+a|0)+8>>2]=c[n+8>>2],c[a>>2]=l,c[a+4>>2]=d,a=y(1|t,12),l=c[(n=a+e|0)>>2],d=c[n+4>>2],c[(a=r+a|0)+8>>2]=c[n+8>>2],c[a>>2]=l,c[a+4>>2]=d,t=t+2|0,(0|u)!=(0|(A=A+2|0)););_&&(A=y(t,12),n=c[(t=A+e|0)>>2],a=c[t+4>>2],c[(r=r+A|0)+8>>2]=c[t+8>>2],c[r>>2]=n,c[r+4>>2]=a)}c[c[b+4>>2]+80>>2]=k-f>>2,a=1}t=a,e&&er(e),f&&(c[i+20>>2]=f,er(f))}else{if(u=c[r+4>>2],t=c[u+44>>2],(0|(f=c[t+100>>2]))!=(0|(n=c[t+96>>2])))for(l=(t=(f-n|0)/12|0)>>>0<=1?1:t,t=0;;){f=c[r+8>>2],b=n+y(t,12)|0;r:{if(-1!=(0|(A=y(t,3))))if(a=-1,k=c[c[f>>2]+(A<<2)>>2],-1!=(0|(_=A+1|0))){if(a=c[c[f>>2]+(_<<2)>>2],s=-1,-1==(0|(A=A+2|0)))break r}else A=0;else a=c[4+(c[f>>2]+(A<<2)|0)>>2],k=-1,A=1;s=c[c[f>>2]+(A<<2)>>2]}if(c[b+8>>2]=s,c[b+4>>2]=a,c[b>>2]=k,(0|l)==(0|(t=t+1|0)))break}c[c[u+4>>2]+80>>2]=e,t=1}return Z=i+32|0,t}function J(r,e){e|=0;var i,t=0,f=0,a=0,A=0,b=0,u=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0;Z=i=Z-96|0,m=c[(r|=0)+4>>2],f=c[m+32>>2],p=c[f+8>>2];r:if(!((0|(a=s=c[f+12>>2]))<=(0|(t=c[f+20>>2]))&(A=c[f+16>>2])>>>0>=p>>>0|(0|t)>(0|a)||(v=c[f>>2],u=k[v+A|0],a=(b=A+1|0)?t:t+1|0,c[f+16>>2]=b,c[f+20>>2]=a,(0|a)>=(0|s)&b>>>0>=p>>>0|(0|a)>(0|s)))){h=k[b+v|0],a=(b=A+2|0)>>>0<2?t+1|0:t,c[f+16>>2]=b,c[f+20>>2]=a;e:{if((0|(d=u<<24>>24))>=0){if(l=c[r+216>>2],u>>>0>=(c[r+220>>2]-l|0)/144>>>0)break r;if(l=l+y(u,144)|0,c[l>>2]<0)break e;break r}if(c[r+212>>2]>=0)break r;l=r+212|0}c[l>>2]=e;e:{i:{t:{f:{a:{n:{A:{if((65535&((l=_[m+36>>1])<<8|l>>>8))>>>0>=258){if((0|a)>=(0|s)&b>>>0>=p>>>0|(0|a)>(0|s))break r;if(a=k[b+v|0],t=(A=A+3|0)>>>0<3?t+1|0:t,c[f+16>>2]=A,c[f+20>>2]=t,a>>>0>1)break r;if(f=a>>>0<2?a:0,!h)break A;if(!f)break n;break r}if(h)break a;f=0}if((0|d)<0?a=r+184|0:(t=c[r+216>>2]+y(u,144)|0,n[t+100|0]=0,a=t+104|0),1!=(0|f))break t;if(Z=t=Z-112|0,b=c[c[r+4>>2]+44>>2],f=vi(120),c[f>>2]=8924,c[f+4>>2]=0,c[f+116>>2]=0,c[f+112>>2]=a,c[f+108>>2]=b,c[f+12>>2]=0,c[f+16>>2]=0,c[f+20>>2]=0,c[f+24>>2]=0,c[f+28>>2]=0,c[f+32>>2]=0,c[f+36>>2]=0,c[f+40>>2]=0,c[f+44>>2]=0,c[f+48>>2]=0,c[f+52>>2]=0,c[f+56>>2]=0,c[f+60>>2]=0,c[f+8>>2]=9136,c[(A=f- -64|0)>>2]=0,c[A+4>>2]=0,c[f+72>>2]=0,c[f+76>>2]=0,c[f+80>>2]=0,c[f+84>>2]=0,c[f+88>>2]=0,c[f+104>>2]=0,c[f+96>>2]=0,c[f+100>>2]=0,A=c[r+8>>2],c[t+48>>2]=0,c[t+52>>2]=0,c[t+40>>2]=0,c[t+44>>2]=0,c[(p=t+32|0)>>2]=0,c[p+4>>2]=0,c[t+24>>2]=0,c[t+28>>2]=0,c[(u=t- -64|0)>>2]=0,c[u+4>>2]=0,c[t+72>>2]=0,c[t+76>>2]=0,c[t+80>>2]=0,c[t+84>>2]=0,c[t+88>>2]=0,c[t+104>>2]=0,c[t+16>>2]=0,c[t+20>>2]=0,c[t+56>>2]=0,c[t+60>>2]=0,c[t+8>>2]=9136,c[t+96>>2]=0,c[t+100>>2]=0,c[t+12>>2]=A,u=c[A>>2],s=c[A+4>>2],n[t+111|0]=0,xr(l=p,(s-u>>2>>>0)/3|0,p=t+111|0),u=c[t+12>>2],s=c[u+28>>2],u=c[u+24>>2],n[t+111|0]=0,xr(t+44|0,s-u>>2,p),c[t+28>>2]=f,c[t+24>>2]=b,c[t+20>>2]=a,c[t+16>>2]=A,Br(A=f+8|0,a=t+8|0),(0|a)!=(0|A)){Pr(f+56|0,c[a+48>>2],c[a+52>>2]),Pr(f+68|0,c[a+60>>2],c[a- -64>>2]),Pr(f+80|0,c[a+72>>2],c[a+76>>2]),c[f+92>>2]=c[a+84>>2];A:if((a=(s=(u=c[a+92>>2])-(p=c[a+88>>2])|0)>>2)>>>0<=(A=c[f+104>>2])-(b=c[f+96>>2])>>2>>>0){if(A=(s=c[f+100>>2]-b|0)+p|0,d=(s=a>>>0>(m=s>>2)>>>0?A:u)-p|0,(0|s)!=(0|p)&&gr(b,p,d),a>>>0>m>>>0){if(a=c[f+100>>2],(0|u)!=(0|s))for(;c[a>>2]=c[A>>2],a=a+4|0,(0|u)!=(0|(A=A+4|0)););c[f+100>>2]=a;break A}c[f+100>>2]=b+d}else b&&(c[f+100>>2]=b,er(b),c[f+104>>2]=0,c[f+96>>2]=0,c[f+100>>2]=0,A=0),(0|s)<0||(b=A>>>1|0,(a=A>>>0>=2147483644?1073741823:a>>>0<b>>>0?b:a)>>>0>=1073741824)?(mt(),o()):(a=vi(A=a<<2),c[f+96>>2]=a,c[f+104>>2]=a+A,(0|u)!=(0|p)&&(a=hr(A=a,p,a=4+(s-4&-4)|0)+a|0),c[f+100>>2]=a)}else c[f+92>>2]=c[a+84>>2];c[t+8>>2]=9136,(a=c[t+96>>2])&&(c[t+100>>2]=a,er(a)),(a=c[t+80>>2])&&(c[t+84>>2]=a,er(a)),(a=c[t+68>>2])&&(c[t+72>>2]=a,er(a)),(a=c[t+56>>2])&&(c[t+60>>2]=a,er(a)),c[t+8>>2]=9372,(a=c[t+44>>2])&&er(a),(a=c[t+32>>2])&&er(a),Z=t+112|0;break i}if((0|d)>=0)break f;break r}if((0|d)<0)break r}a=c[r+216>>2],t=c[m+44>>2],f=vi(80),c[f>>2]=9684,c[f+4>>2]=0,c[f+76>>2]=0,c[f+68>>2]=t,c[f+8>>2]=8624,c[f+12>>2]=0,c[f+16>>2]=0,c[f+20>>2]=0,c[f+24>>2]=0,c[f+28>>2]=0,c[f+32>>2]=0,c[f+36>>2]=0,c[f+40>>2]=0,c[f+44>>2]=0,c[f+48>>2]=0,c[f+52>>2]=0,A=(a=a+y(u,144)|0)+104|0,c[f+72>>2]=A,c[f- -64>>2]=0,c[f+56>>2]=0,c[f+60>>2]=0,c[i+24>>2]=t,c[(t=i)+68>>2]=0,c[t+72>>2]=0,c[t+60>>2]=0,c[t+64>>2]=0,c[t+52>>2]=0,c[t+56>>2]=0,c[t+44>>2]=0,c[t+48>>2]=0,c[t+84>>2]=0,c[t+88>>2]=0,c[t+76>>2]=0,c[t+80>>2]=0,c[t+28>>2]=f,b=c[t+28>>2],c[t+8>>2]=c[t+24>>2],c[t+12>>2]=b,c[t+20>>2]=A,A=a+4|0,c[t+16>>2]=A,c[t+36>>2]=0,c[t+40>>2]=0,c[t+32>>2]=8624,a=c[t+20>>2],c[t>>2]=c[t+16>>2],c[t+4>>2]=a,Te(a=t+32|0,A,t),Br(t=f+8|0,a),(0|t)!=(0|a)&&Pr(f+56|0,c[a+48>>2],c[a+52>>2]),_i(a);break e}Z=t=Z+-64|0,b=c[c[r+4>>2]+44>>2],f=vi(80),c[f>>2]=9392,c[f+4>>2]=0,c[f+76>>2]=0,c[f+72>>2]=a,c[f+68>>2]=b,c[f+8>>2]=9556,c[f+12>>2]=0,c[f+16>>2]=0,c[f+20>>2]=0,c[f+24>>2]=0,c[f+28>>2]=0,c[f+32>>2]=0,c[f+36>>2]=0,c[f+40>>2]=0,c[f+44>>2]=0,c[f+48>>2]=0,c[f+52>>2]=0,c[f- -64>>2]=0,c[(A=p=f+56|0)>>2]=0,c[A+4>>2]=0,A=c[r+8>>2],c[t+40>>2]=0,c[t+44>>2]=0,c[t+32>>2]=0,c[t+36>>2]=0,c[(u=t+24|0)>>2]=0,c[u+4>>2]=0,c[t+16>>2]=0,c[t+20>>2]=0,c[t+56>>2]=0,c[t+8>>2]=0,c[t+12>>2]=0,c[t+48>>2]=0,c[t+52>>2]=0,c[t>>2]=9556,c[t+4>>2]=A,s=c[A>>2],d=c[A+4>>2],n[t+63|0]=0,xr(l=u,(d-s>>2>>>0)/3|0,u=t+63|0),s=c[t+4>>2],d=c[s+28>>2],s=c[s+24>>2],n[t+63|0]=0,xr(t+36|0,d-s>>2,u),c[t+20>>2]=f,c[t+16>>2]=b,c[t+12>>2]=a,c[t+8>>2]=A,Br(f+8|0,t),Pr(p,c[t+48>>2],c[t+52>>2]),c[t>>2]=9556,(a=c[t+48>>2])&&(c[t+52>>2]=a,er(a)),c[t>>2]=9372,(a=c[t+36>>2])&&er(a),(a=c[t+24>>2])&&er(a),Z=t- -64|0}if(!f)break r}f=He(vi(64),f),t=c[r+4>>2],r=f;e:{i:{if((0|(f=e))>=0){if(b=t+8|0,!((0|(a=(e=c[t+12>>2])-(p=c[t+8>>2])>>2))>(0|f)))if(A=f+1|0,f>>>0>=a>>>0)Or(b,A-a|0);else if(!(a>>>0<=A>>>0)){if((0|(A=p+(A<<2)|0))!=(0|e))for(;a=c[(e=e-4|0)>>2],c[e>>2]=0,a&&Zt[c[c[a>>2]+4>>2]](a),(0|e)!=(0|A););c[t+12>>2]=A}if(t=c[b>>2]+(f<<2)|0,e=c[t>>2],c[t>>2]=r,e)break i;break e}if(e=r,!r)break e}Zt[c[c[e>>2]+4>>2]](e)}R=(-1^f)>>>31|0}return Z=i+96|0,0|R}function B(r,e,i,t){var f=0,a=0,o=0,b=0,u=0,s=0,y=0,v=0,h=d(0),R=d(0),T=0;r:{e:if(t){i:switch(c[r+28>>2]-1|0){case 0:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(A[(o<<1)+t>>1]=n[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 1:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(A[(o<<1)+t>>1]=k[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 2:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(A[(o<<1)+t>>1]=_[e>>1],e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 3:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)return 0;if((0|(f=A[e>>1]))<0)break e;if(A[(o<<1)+t>>1]=f,e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 4:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if((f=c[e>>2])+32768>>>0>65535)break e;if(A[(o<<1)+t>>1]=f,e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 5:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if((f=c[e>>2])>>>0>32767)break e;if(A[(o<<1)+t>>1]=f,e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 6:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,y=c[f+4>>2];;){if(e>>>0>=y>>>0)break e;if(b=c[e+4>>2],!(b=(u=(f=c[e>>2])+32768|0)>>>0<32768?b+1|0:b)&u>>>0>65535|b)break e;if(A[(o<<1)+t>>1]=f,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 7:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(!(y=c[e+4>>2])&(f=c[e>>2])>>>0>32767|y)break e;if(A[(o<<1)+t>>1]=f,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 8:t:{f:if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i){if(f=c[r>>2],o=s=c[f>>2],a=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),o=(e=e+a|0)+o|0,f=(a=c[f+4>>2])-s|0,!k[r+32|0]){if(s=0,(0|e)>=(0|f))break t;for(e=0;;){if((h=p[o>>2])>=d(32767)|h<d(-32768)|h!=h)break t;if((R=d(m(h)))==d(1/0))break t;if(f=(e<<1)+t|0,u=R<d(2147483648)?~~h:-2147483648,A[f>>1]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+4|0)>>>0))break}break t}if(s=0,(0|e)>=(0|f))break t;for(e=0;;){if((h=p[o>>2])>=d(32767)|h<d(-32768)|d(m(h))==d(1/0)|h!=h)break t;if(h<d(0)|h>d(1))break t;if(f=(e<<1)+t|0,v=N(32767*+h+.5),u=m(v)<2147483648?~~v:-2147483648,A[f>>1]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+4|0)>>>0))break}break t}s=1,i>>>0<=f>>>0||Sr((f<<1)+t|0,0,i-f<<1)}return s;case 9:t:{f:if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i){if(f=c[r>>2],o=s=c[f>>2],a=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),o=(e=e+a|0)+o|0,f=(a=c[f+4>>2])-s|0,!k[r+32|0]){if(s=0,(0|e)>=(0|f))break t;for(e=0;;){if((v=l[o>>3])>=32767|v<-32768|v!=v)break t;if((T=m(v))==1/0)break t;if(u=T<2147483648?~~v:-2147483648,A[(f=(e<<1)+t|0)>>1]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+8|0)>>>0))break}break t}if(s=0,(0|e)>=(0|f))break t;for(e=0;;){if((v=l[o>>3])>=32767|v<-32768|m(v)==1/0|v!=v)break t;if(v<0|v>1)break t;if(f=(e<<1)+t|0,v=N(32767*v+.5),u=m(v)<2147483648?~~v:-2147483648,A[f>>1]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+8|0)>>>0))break}break t}s=1,i>>>0<=f>>>0||Sr((f<<1)+t|0,0,i-f<<1)}return s;case 10:break i;default:break e}if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(A[(o<<1)+t>>1]=k[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}s=1,f>>>0>=a>>>0||Sr((f<<1)+t|0,0,(255&i)-f<<1)}return s}return Sr((f<<1)+t|0,0,(255&i)-f<<1),1}function M(r,e,i,t){var f=0,a=0,o=0,b=0,u=0,s=0,y=0,v=0,h=d(0),R=d(0),T=0;r:{e:if(t){i:switch(c[r+28>>2]-1|0){case 0:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=n[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 1:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=k[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 2:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=A[e>>1],e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 3:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=_[e>>1],e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 4:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=c[e>>2],e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 5:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)return 0;if((0|(f=c[e>>2]))<0)break e;if(c[(o<<2)+t>>2]=f,e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 6:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,y=c[f+4>>2];;){if(e>>>0>=y>>>0)break e;if(b=c[e+4>>2],(f=c[e>>2])- -2147483648>>>0<2147483648?b+1|0:b)break e;if(c[(o<<2)+t>>2]=f,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 7:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(!(y=c[e+4>>2])&(f=c[e>>2])>>>0>2147483647|y)break e;if(c[(o<<2)+t>>2]=f,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(s=1,f>>>0>=a>>>0)break e;break r;case 8:t:{f:if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i){if(f=c[r>>2],o=s=c[f>>2],a=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),o=(e=e+a|0)+o|0,f=(a=c[f+4>>2])-s|0,!k[r+32|0]){if(s=0,(0|e)>=(0|f))break t;for(e=0;;){if((h=p[o>>2])>=d(2147483648)|h<d(-2147483648)|h!=h)break t;if((R=d(m(h)))==d(1/0))break t;if(f=(e<<2)+t|0,u=R<d(2147483648)?~~h:-2147483648,c[f>>2]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+4|0)>>>0))break}break t}if(s=0,(0|e)>=(0|f))break t;for(e=0;;){if((h=p[o>>2])>=d(2147483648)|h<d(-2147483648)|d(m(h))==d(1/0)|h!=h)break t;if(h<d(0)|h>d(1))break t;if(f=(e<<2)+t|0,v=N(2147483647*+h+.5),u=m(v)<2147483648?~~v:-2147483648,c[f>>2]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+4|0)>>>0))break}break t}s=1,i>>>0<=f>>>0||Sr((f<<2)+t|0,0,i-f<<2)}return s;case 9:t:{f:if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i){if(f=c[r>>2],o=s=c[f>>2],a=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),o=(e=e+a|0)+o|0,f=(a=c[f+4>>2])-s|0,!k[r+32|0]){if(s=0,(0|e)>=(0|f))break t;for(e=0;;){if((v=l[o>>3])>=2147483647|v<-2147483648|v!=v)break t;if((T=m(v))==1/0)break t;if(u=T<2147483648?~~v:-2147483648,c[(f=(e<<2)+t|0)>>2]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+8|0)>>>0))break}break t}if(s=0,(0|e)>=(0|f))break t;for(e=0;;){if((v=l[o>>3])>=2147483647|v<-2147483648|m(v)==1/0|v!=v)break t;if(v<0|v>1)break t;if(f=(e<<2)+t|0,v=N(2147483647*v+.5),u=m(v)<2147483648?~~v:-2147483648,c[f>>2]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+8|0)>>>0))break}break t}s=1,i>>>0<=f>>>0||Sr((f<<2)+t|0,0,i-f<<2)}return s;case 10:break i;default:break e}if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=k[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}s=1,f>>>0>=a>>>0||Sr((f<<2)+t|0,0,(255&i)-f<<2)}return s}return Sr((f<<2)+t|0,0,(255&i)-f<<2),1}function Q(r,e,i,t){var f=0,a=0,o=0,b=0,u=0,s=0,y=0,v=0,h=d(0);r:{e:if(t){i:switch(c[r+28>>2]-1|0){case 0:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)return 0;if((0|(f=n[0|e]))<0)break e;if(A[(o<<1)+t>>1]=255&f,e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(v=1,f>>>0>=a>>>0)break e;break r;case 1:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(A[(o<<1)+t>>1]=k[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(v=1,f>>>0>=a>>>0)break e;break r;case 2:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)return 0;if((0|(f=A[e>>1]))<0)break e;if(A[(o<<1)+t>>1]=f,e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(v=1,f>>>0>=a>>>0)break e;break r;case 3:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(A[(o<<1)+t>>1]=_[e>>1],e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(v=1,f>>>0>=a>>>0)break e;break r;case 4:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if((f=c[e>>2])>>>0>65535)break e;if(A[(o<<1)+t>>1]=f,e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(v=1,f>>>0>=a>>>0)break e;break r;case 5:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if((f=c[e>>2])>>>0>65535)break e;if(A[(o<<1)+t>>1]=f,e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(v=1,f>>>0>=a>>>0)break e;break r;case 6:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(!(y=c[e+4>>2])&(f=c[e>>2])>>>0>65535|y)break e;if(A[(o<<1)+t>>1]=f,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(v=1,f>>>0>=a>>>0)break e;break r;case 7:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(!(y=c[e+4>>2])&(f=c[e>>2])>>>0>65535|y)break e;if(A[(o<<1)+t>>1]=f,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(v=1,f>>>0>=a>>>0)break e;break r;case 8:t:{f:if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i){if(f=c[r>>2],o=v=c[f>>2],a=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),o=(e=e+a|0)+o|0,f=(a=c[f+4>>2])-v|0,!k[r+32|0]){if(v=0,(0|e)>=(0|f))break t;for(e=0;;){if((h=p[o>>2])>=d(65535)|h<d(0)|d(m(h))==d(1/0)|h!=h)break t;if(f=(e<<1)+t|0,u=h<d(4294967296)&h>=d(0)?~~h>>>0:0,A[f>>1]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+4|0)>>>0))break}break t}if(v=0,(0|e)>=(0|f))break t;for(e=0;;){if((h=p[o>>2])>=d(65535)|h<d(0)|d(m(h))==d(1/0)|h!=h)break t;if(h>d(1))break t;if(f=(e<<1)+t|0,u=(s=N(65535*+h+.5))<4294967296&s>=0?~~s>>>0:0,A[f>>1]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+4|0)>>>0))break}break t}v=1,i>>>0<=f>>>0||Sr((f<<1)+t|0,0,i-f<<1)}return v;case 9:t:{f:if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i){if(f=c[r>>2],o=v=c[f>>2],a=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),o=(e=e+a|0)+o|0,f=(a=c[f+4>>2])-v|0,!k[r+32|0]){if(v=0,(0|e)>=(0|f))break t;for(e=0;;){if((s=l[o>>3])>=65535|s<0|m(s)==1/0|s!=s)break t;if(u=s<4294967296&s>=0?~~s>>>0:0,A[(f=(e<<1)+t|0)>>1]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+8|0)>>>0))break}break t}if(v=0,(0|e)>=(0|f))break t;for(e=0;;){if((s=l[o>>3])>=65535|s<0|m(s)==1/0|s!=s)break t;if(s>1)break t;if(f=(e<<1)+t|0,u=(s=N(65535*s+.5))<4294967296&s>=0?~~s>>>0:0,A[f>>1]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+8|0)>>>0))break}break t}v=1,i>>>0<=f>>>0||Sr((f<<1)+t|0,0,i-f<<1)}return v;case 10:break i;default:break e}if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],y=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+y|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(A[(o<<1)+t>>1]=k[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}v=1,f>>>0>=a>>>0||Sr((f<<1)+t|0,0,(255&i)-f<<1)}return v}return Sr((f<<1)+t|0,0,(255&i)-f<<1),1}function g(r,e,i,t){var f=0,a=0,o=0,b=0,u=0,s=0,y=d(0),v=0,R=0,V=d(0),U=0;r:if(t){e:{i:switch(c[r+28>>2]-1|0){case 0:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(f=(o<<3)+t|0,u=n[0|e],c[f>>2]=u,c[f+4>>2]=u>>31,e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 1:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(c[(f=(o<<3)+t|0)>>2]=k[0|e],c[f+4>>2]=0,e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 2:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(f=(o<<3)+t|0,u=A[e>>1],c[f>>2]=u,c[f+4>>2]=u>>31,e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 3:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(c[(f=(o<<3)+t|0)>>2]=_[e>>1],c[f+4>>2]=0,e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 4:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(f=(o<<3)+t|0,u=c[e>>2],c[f>>2]=u,c[f+4>>2]=u>>31,e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 5:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(c[(f=(o<<3)+t|0)>>2]=c[e>>2],c[f+4>>2]=0,e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 6:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(u=c[e+4>>2],c[(f=(o<<3)+t|0)>>2]=c[e>>2],c[f+4>>2]=u,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 7:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(f=c[e>>2],(0|(u=c[e+4>>2]))<0)break r;if(c[(s=(o<<3)+t|0)>>2]=f,c[s+4>>2]=u,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 8:t:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a){if(k[r+32|0])break r;if(f=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=e+f|0,f=c[r>>2],(0|e)>=((u=c[f+4>>2])-(f=c[f>>2])|0))break r;for(o=e+f|0,b=255&i,e=0;;){if((y=p[o>>2])>=d(0x8000000000000000)|y<d(-0x8000000000000000)|y!=y)break r;if((V=d(m(y)))==d(1/0))break r;if(f=(e<<3)+t|0,V<d(0x8000000000000000)?(s=d(m(y))>=d(1)?~~(y>d(0)?d(h(d(N(d(y*d(2.3283064365386963e-10)))),d(4294967296))):d(T(d(d(y-d(~~y>>>0>>>0))*d(2.3283064365386963e-10)))))>>>0:0,R=~~y>>>0):(s=-2147483648,R=0),c[f>>2]=R,c[f+4>>2]=s,(e=e+1|0)>>>0>=((f=k[r+24|0])>>>0<b>>>0?f:b)>>>0)break t;if(!(u>>>0>(o=o+4|0)>>>0))break}break r}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 9:t:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a){if(k[r+32|0])break r;if(f=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=e+f|0,f=c[r>>2],(0|e)>=((u=c[f+4>>2])-(f=c[f>>2])|0))break r;for(o=e+f|0,b=255&i,e=0;;){if((v=l[o>>3])>=0x8000000000000000|v<-0x8000000000000000|v!=v)break r;if((U=m(v))==1/0)break r;if(f=(e<<3)+t|0,U<0x8000000000000000?(s=m(v)>=1?~~(v>0?h(N(2.3283064365386963e-10*v),4294967295):T(2.3283064365386963e-10*(v-+(~~v>>>0>>>0))))>>>0:0,R=~~v>>>0):(s=-2147483648,R=0),c[f>>2]=R,c[f+4>>2]=s,(e=e+1|0)>>>0>=((f=k[r+24|0])>>>0<b>>>0?f:b)>>>0)break t;if(!(u>>>0>(o=o+8|0)>>>0))break}break r}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0;break e;case 10:break i;default:break r}if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],u=c[r+48>>2],s=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break r;if(c[(f=(o<<3)+t|0)>>2]=k[0|e],c[f+4>>2]=0,e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(f>>>0>=a>>>0)break r;t=(f<<3)+t|0,r=(255&i)-f|0}Sr(t,0,r<<3)}}function O(r,e,i,t){var f=0,a=0,o=0,b=0,u=0,s=0,y=0,v=0,h=d(0);r:{e:if(t){i:switch(c[r+28>>2]-1|0){case 0:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=n[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 1:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=k[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 2:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=A[e>>1],e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 3:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=_[e>>1],e=e+2|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 4:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=c[e>>2],e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 5:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=c[e>>2],e=e+4|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 6:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(f=c[e>>2],c[e+4>>2])break e;if(c[(o<<2)+t>>2]=f,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 7:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(f=c[e>>2],c[e+4>>2])break e;if(c[(o<<2)+t>>2]=f,e=e+8|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 8:t:{f:if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i){if(f=c[r>>2],o=y=c[f>>2],a=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),o=(e=e+a|0)+o|0,f=(a=c[f+4>>2])-y|0,!k[r+32|0]){if(y=0,(0|e)>=(0|f))break t;for(e=0;;){if((h=p[o>>2])>=d(4294967296)|h<d(0)|d(m(h))==d(1/0)|h!=h)break t;if(f=(e<<2)+t|0,u=h<d(4294967296)&h>=d(0)?~~h>>>0:0,c[f>>2]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+4|0)>>>0))break}break t}if(y=0,(0|e)>=(0|f))break t;for(e=0;;){if((h=p[o>>2])>=d(4294967296)|h<d(0)|d(m(h))==d(1/0)|h!=h)break t;if(h>d(1))break t;if(f=(e<<2)+t|0,u=(s=N(4294967295*+h+.5))<4294967296&s>=0?~~s>>>0:0,c[f>>2]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+4|0)>>>0))break}break t}y=1,i>>>0<=f>>>0||Sr((f<<2)+t|0,0,i-f<<2)}return y;case 9:t:{f:if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i){if(f=c[r>>2],o=y=c[f>>2],a=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),o=(e=e+a|0)+o|0,f=(a=c[f+4>>2])-y|0,!k[r+32|0]){if(y=0,(0|e)>=(0|f))break t;for(e=0;;){if((s=l[o>>3])>=4294967295|s<0|m(s)==1/0|s!=s)break t;if(u=s<4294967296&s>=0?~~s>>>0:0,c[(f=(e<<2)+t|0)>>2]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+8|0)>>>0))break}break t}if(y=0,(0|e)>=(0|f))break t;for(e=0;;){if((s=l[o>>3])>=4294967295|s<0|m(s)==1/0|s!=s)break t;if(s>1)break t;if(f=(e<<2)+t|0,u=(s=N(4294967295*s+.5))<4294967296&s>=0?~~s>>>0:0,c[f>>2]=u,(e=e+1|0)>>>0>=(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0)break f;if(!(a>>>0>(o=o+8|0)>>>0))break}break t}y=1,i>>>0<=f>>>0||Sr((f<<2)+t|0,0,i-f<<2)}return y;case 10:break i;default:break e}if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],b=c[f>>2],v=c[r+48>>2],u=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+v|0)+b|0,b=c[f+4>>2];;){if(e>>>0>=b>>>0)break e;if(c[(o<<2)+t>>2]=k[0|e],e=e+1|0,!((o=o+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}y=1,f>>>0>=a>>>0||Sr((f<<2)+t|0,0,(255&i)-f<<2)}return y}return Sr((f<<2)+t|0,0,(255&i)-f<<2),1}function C(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0,_=0;r:{e:{i:{t:{f:if(c[r+92>>2]!=c[r+88>>2]){if((0|(i=c[r+52>>2]))==c[r+56>>2]){if((a=(t=(n=i-(A=c[r+48>>2])|0)>>2)+1|0)>>>0>=1073741824)break r;if(f=n>>>1|0,n=n>>>0>=2147483644?1073741823:a>>>0<f>>>0?f:a){if(n>>>0>=1073741824)break t;f=vi(n<<2)}else f=0;if(c[(a=f+(t<<2)|0)>>2]=e,t=a+4|0,(0|i)!=(0|A))for(;i=i-4|0,c[(a=a-4|0)>>2]=c[i>>2],(0|i)!=(0|A););c[r+56>>2]=f+(n<<2),c[r+52>>2]=t,c[r+48>>2]=a,A&&er(A)}else c[i>>2]=e,c[r+52>>2]=i+4;c[r+84>>2]=0,i=-1,f=-1;a:if(-1!=(0|e)){if(t=c[r+4>>2],-1!=(0|(f=((f=e+1|0)>>>0)%3|0?f:e-2|0))&&(i=c[c[t>>2]+(f<<2)>>2]),(e>>>0)%3|0)_=e-1|0;else if(f=-1,-1==(0|(_=e+2|0)))break a;f=c[c[t>>2]+(_<<2)>>2]}if(b=f>>>3&536870908,t=c[r+36>>2],(n=c[(A=t+(i>>>3&536870908)|0)>>2])&(a=1<<i)||(c[A>>2]=a|n,mr(a=r+8|0,i,t=-1!=(0|e)?((t=e+1|0)>>>0)%3|0?t:e-2|0:-1),t=c[r+36>>2]),(t=c[(a=t+b|0)>>2])&(i=1<<f)||(c[a>>2]=i|t,i=-1,-1!=(0|e)&&(i=e-1|0,(e>>>0)%3|0||(i=e+2|0)),mr(t=r+8|0,f,i)),i=-1,i=-1!=(0|e)?c[c[c[r+4>>2]>>2]+(e<<2)>>2]:i,a=c[r+36>>2]+(i>>>3&536870908)|0,(t=c[a>>2])&(f=1<<i)||(c[a>>2]=t|f,mr(r+8|0,i,e)),!((0|(t=c[r+84>>2]))>2))for(;;){if(f=y(t,12)+r|0,(0|(e=c[f+52>>2]))==c[f+48>>2]){if(3!=(0|(t=t+1|0)))continue;break f}if(i=c[(e=e-4|0)>>2],c[f+52>>2]=e,c[r+84>>2]=t,-1==(0|i))break f;a=c[r+24>>2];a:if(!(c[a+((e=(i>>>0)/3|0)>>>3&268435452)>>2]>>>e&1)){n:{for(;;){c[(e=((k=(i>>>0)/3|0)>>>3&268435452)+a|0)>>2]=c[e>>2]|1<<k,t=-1;A:{o:{b:{u:{c:{k:{_:{s:{if(t=-1!=(0|i)?c[c[c[r+4>>2]>>2]+(i<<2)>>2]:t,a=c[r+36>>2]+(t>>>3&536870908)|0,!((f=c[a>>2])&(e=1<<t))){if(c[a>>2]=e|f,b=c[(c[c[r+16>>2]+96>>2]+y(k,12)|0)+((i>>>0)%3<<2)>>2],_=c[c[r+20>>2]+4>>2],(0|(a=c[_+4>>2]))==c[_+8>>2]){if((f=(n=(A=a-(u=c[_>>2])|0)>>2)+1|0)>>>0>=1073741824)break s;if(e=A>>>1|0,A=A>>>0>=2147483644?1073741823:e>>>0>f>>>0?e:f){if(A>>>0>=1073741824)break t;f=vi(A<<2)}else f=0;if(c[(e=f+(n<<2)|0)>>2]=b,n=e+4|0,(0|a)!=(0|u))for(;a=a-4|0,c[(e=e-4|0)>>2]=c[a>>2],(0|a)!=(0|u););c[_+8>>2]=f+(A<<2),c[_+4>>2]=n,c[_>>2]=e,u&&er(u)}else c[a>>2]=b,c[_+4>>2]=a+4;if(u=c[r+12>>2],(0|(a=c[u+4>>2]))==c[u+8>>2]){if((f=(n=(A=a-(b=c[u>>2])|0)>>2)+1|0)>>>0>=1073741824)break _;if(e=A>>>1|0,A=A>>>0>=2147483644?1073741823:e>>>0>f>>>0?e:f){if(A>>>0>=1073741824)break t;f=vi(A<<2)}else f=0;if(c[(e=f+(n<<2)|0)>>2]=i,n=e+4|0,(0|a)!=(0|b))for(;a=a-4|0,c[(e=e-4|0)>>2]=c[a>>2],(0|a)!=(0|b););c[u+8>>2]=f+(A<<2),c[u+4>>2]=n,c[u>>2]=e,b&&er(b)}else c[a>>2]=i,c[u+4>>2]=a+4;e=c[r+12>>2],c[c[e+12>>2]+(t<<2)>>2]=c[e+24>>2],c[e+24>>2]=c[e+24>>2]+1}if(-1==(0|i))break n;n=c[r+4>>2],a=-1,-1!=(0|(e=((e=i+1|0)>>>0)%3|0?e:i-2|0))&&(a=c[c[n+12>>2]+(e<<2)>>2]);p:{if((0|y(k,3))==(0|i)){if(t=i+2|0,i=-1,-1==(0|t))break p}else t=i-1|0;i=c[c[n+12>>2]+(t<<2)>>2]}if(t=-1==(0|i),f=(i>>>0)/3|0,-1!=(0|a)){if(e=(a>>>0)/3|0,e=c[c[r+24>>2]+(e>>>3&268435452)>>2]&1<<e,t)break k;_=0!=(0|e);break c}if(_=1,!t)break c;break n}mt(),o()}mt(),o()}if(!e)break u;break n}if(e=t?-1:f,!(c[c[r+24>>2]+(e>>>3&536870908)>>2]>>>e&1)){if(k=0,e=c[c[n>>2]+(i<<2)>>2],c[c[r+36>>2]+(e>>>3&536870908)>>2]>>>e&1||(e=c[r+88>>2]+(e<<2)|0,f=c[e>>2],c[e>>2]=f+1,k=(0|f)<=0?2:1),c[r+84>>2]>=(0|k)&_)break o;if(u=y(k,12)+r|0,(0|(e=c[u+52>>2]))==c[u+56>>2]){if((n=(t=(A=e-(b=c[u+48>>2])|0)>>2)+1|0)>>>0>=1073741824)break i;if(f=A>>>1|0,n=A>>>0>=2147483644?1073741823:f>>>0>n>>>0?f:n){if(n>>>0>=1073741824)break t;f=vi(n<<2)}else f=0;if(c[(t=f+(t<<2)|0)>>2]=i,i=t+4|0,(0|e)!=(0|b))for(;e=e-4|0,c[(t=t-4|0)>>2]=c[e>>2],(0|e)!=(0|b););c[u+48>>2]=t,c[u+52>>2]=i,c[u+56>>2]=f+(n<<2),b&&er(b)}else c[e>>2]=i,c[u+52>>2]=e+4;c[r+84>>2]<=(0|k)||(c[r+84>>2]=k)}if(_)break n;if(i=-1,-1==(0|a))break b}i=c[c[c[r+4>>2]>>2]+(a<<2)>>2]}if(e=0,c[c[r+36>>2]+(i>>>3&536870908)>>2]>>>i&1||(e=c[r+88>>2]+(i<<2)|0,i=c[e>>2],c[e>>2]=i+1,e=(0|i)<=0?2:1),c[r+84>>2]<(0|e))break A;i=a}a=c[r+24>>2];continue}break}if(k=y(e,12)+r|0,(0|(i=c[k+52>>2]))==c[k+56>>2]){if((n=(t=(A=i-(b=c[k+48>>2])|0)>>2)+1|0)>>>0>=1073741824)break e;if(f=A>>>1|0,n=A>>>0>=2147483644?1073741823:f>>>0>n>>>0?f:n){if(n>>>0>=1073741824)break t;f=vi(n<<2)}else f=0;if(c[(t=f+(t<<2)|0)>>2]=a,a=t+4|0,(0|i)!=(0|b))for(;i=i-4|0,c[(t=t-4|0)>>2]=c[i>>2],(0|i)!=(0|b););c[k+48>>2]=t,c[k+52>>2]=a,c[k+56>>2]=f+(n<<2),b&&er(b)}else c[i>>2]=a,c[k+52>>2]=i+4;if((0|(t=c[r+84>>2]))<=(0|e))break a;c[r+84>>2]=e,t=e;break a}t=c[r+84>>2]}if(!((0|t)<3))break}}return 1}Zi(),o()}mt(),o()}mt(),o()}mt(),o()}function z(r){var e,i=0,t=0,f=0,a=0,n=0;Z=e=Z-16|0,c[e+12>>2]=r;r:if(r>>>0<=211)f=c[Ce(10352,10544,e+12|0)>>2];else for(r>>>0>=4294967292&&(U(),o()),f=y(a=(r>>>0)/210|0,210),c[e+8>>2]=r-f,n=Ce(10544,10736,e+8|0)-10544>>2;;){for(f=c[10544+(n<<2)>>2]+f|0,r=5;;){e:if(47!=(0|r)){if((i=c[10352+(r<<2)>>2])>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if(r=r+1|0,(0|y(i,t))!=(0|f))continue}else for(r=211;;){if((i=(f>>>0)/(r>>>0)|0)>>>0<r>>>0)break r;if((0|y(r,i))==(0|f))break e;if((i=r+10|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+12|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+16|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+18|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+22|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+28|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+30|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+36|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+40|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+42|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+46|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+52|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+58|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+60|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+66|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+70|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+72|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+78|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+82|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+88|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+96|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+100|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+102|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+106|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+108|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+112|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+120|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+126|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+130|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+136|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+138|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+142|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+148|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+150|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+156|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+162|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+166|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+168|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+172|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+178|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+180|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+186|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+190|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+192|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+196|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+198|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if((0|y(i,t))==(0|f))break e;if((i=r+208|0)>>>0>(t=(f>>>0)/(i>>>0)|0)>>>0)break r;if(r=r+210|0,(0|y(i,t))==(0|f))break}break}n=(r=48==(0|(f=n+1|0)))?0:f,f=y(a=r+a|0,210)}return Z=e+16|0,f}function X(r,e,i,t){var f=0,a=0,o=0,b=0,u=0,y=d(0),m=0;r:if(t){e:{i:switch(c[r+28>>2]-1|0){case 0:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2],u=k[r+32|0];;){if(e>>>0>=o>>>0)break r;if(y=d(n[0|e]),p[(b<<2)+t>>2]=u?d(y/d(127)):y,e=e+1|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 1:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2],u=k[r+32|0];;){if(e>>>0>=o>>>0)break r;if(y=d(k[0|e]),p[(b<<2)+t>>2]=u?d(y/d(255)):y,e=e+1|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 2:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2],u=k[r+32|0];;){if(e>>>0>=o>>>0)break r;if(y=d(A[e>>1]),p[(b<<2)+t>>2]=u?d(y/d(32767)):y,e=e+2|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 3:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2],u=k[r+32|0];;){if(e>>>0>=o>>>0)break r;if(y=d(_[e>>1]),p[(b<<2)+t>>2]=u?d(y/d(65535)):y,e=e+2|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 4:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2],u=k[r+32|0];;){if(e>>>0>=o>>>0)break r;if(y=d(c[e>>2]),p[(b<<2)+t>>2]=u?d(y*d(4.656612873077393e-10)):y,e=e+4|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 5:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2],u=k[r+32|0];;){if(e>>>0>=o>>>0)break r;if(y=d(s[e>>2]),p[(b<<2)+t>>2]=u?d(y*d(2.3283064365386963e-10)):y,e=e+4|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 6:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2],u=k[r+32|0];;){if(e>>>0>=o>>>0)break r;if(y=d(+s[e>>2]+4294967296*+c[e+4>>2]),p[(b<<2)+t>>2]=u?d(y*d(10842021724855044e-35)):y,e=e+8|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 7:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2],u=k[r+32|0];;){if(e>>>0>=o>>>0)break r;if(y=d(+s[e>>2]+4294967296*+s[e+4>>2]),p[(b<<2)+t>>2]=u?d(y*d(5.421010862427522e-20)):y,e=e+8|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 8:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2];;){if(e>>>0>=o>>>0)break r;if(p[(b<<2)+t>>2]=p[e>>2],e=e+4|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 9:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2];;){if(e>>>0>=o>>>0)break r;if(p[(b<<2)+t>>2]=l[e>>3],e=e+8|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0;break e;case 10:break i;default:break r}if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[r>>2],o=c[f>>2],u=c[r+48>>2],e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+u|0)+o|0,o=c[f+4>>2];;){if(e>>>0>=o>>>0)break r;if(p[(b<<2)+t>>2]=k[0|e]?d(1):d(0),e=e+1|0,!((b=b+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(m=1,f>>>0>=a>>>0)break r;t=(f<<2)+t|0,r=(255&i)-f|0}Sr(t,0,r<<2)}return m}function S(r,e,i,t){var f=0,a=0,A=0,o=0,b=0,u=0,y=0,v=d(0),h=d(0);r:{e:if(t){i:switch(c[r+28>>2]-1|0){case 0:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(n[t+A|0]=k[0|e],e=e+1|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 1:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)return 0;if((0|(f=n[0|e]))<0)break e;if(n[t+A|0]=f,e=e+1|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 2:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(((f=_[e>>1])+128&65535)>>>0>255)break e;if(n[t+A|0]=f,e=e+2|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 3:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((f=_[e>>1])>>>0>127)break e;if(n[t+A|0]=f,e=e+2|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 4:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((f=c[e>>2])+128>>>0>255)break e;if(n[t+A|0]=f,e=e+4|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 5:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((f=c[e>>2])>>>0>127)break e;if(n[t+A|0]=f,e=e+4|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 6:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(b=c[e+4>>2],!(b=(o=(f=c[e>>2])+128|0)>>>0<128?b+1|0:b)&o>>>0>255|b)break e;if(n[t+A|0]=f,e=e+8|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 7:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(!(b=c[e+4>>2])&(f=c[e>>2])>>>0>127|b)break e;if(n[t+A|0]=f,e=e+8|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 8:t:{if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i)for(f=c[c[r>>2]>>2],a=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+a|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break t;if((v=p[e>>2])>=d(127)|v<d(-128)|v!=v)break t;if((h=d(m(v)))==d(1/0))break t;f=t+A|0;f:{a:{if(k[r+32|0]){if(v<d(0)|v>d(1))break t;if(u=N(127*+v+.5),!(m(u)<2147483648))break a;o=~~u;break f}if(h<d(2147483648)){o=~~v;break f}}o=-2147483648}if(n[0|f]=o,e=e+4|0,!((A=A+1|0)>>>0<(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0))break}y=1,i>>>0<=f>>>0||Sr(t+f|0,0,i-f|0)}return y;case 9:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((u=l[e>>3])>=127|u<-128|m(u)==1/0|u!=u)break e;if(f=t+A|0,k[r+32|0]){if(u<0|u>1)break e;u=N(127*u+.5)}if(o=m(u)<2147483648?~~u:-2147483648,n[0|f]=o,e=e+8|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 10:break i;default:break e}if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(n[t+A|0]=k[0|e],e=e+1|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}y=1,f>>>0>=a>>>0||Sr(t+f|0,0,(255&i)-f|0)}return y}return Sr(t+f|0,0,(255&i)-f|0),1}function H(r,e,i,t){var f=0,a=0,A=0,o=0,b=0,u=0,y=0,v=d(0);r:{e:if(t){i:switch(c[r+28>>2]-1|0){case 0:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)return 0;if((0|(f=n[0|e]))<0)break e;if(n[t+A|0]=f,e=e+1|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 1:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(n[t+A|0]=k[0|e],e=e+1|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 2:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((f=_[e>>1])>>>0>255)break e;if(n[t+A|0]=f,e=e+2|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 3:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((f=_[e>>1])>>>0>255)break e;if(n[t+A|0]=f,e=e+2|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 4:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((f=c[e>>2])>>>0>255)break e;if(n[t+A|0]=f,e=e+4|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 5:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((f=c[e>>2])>>>0>255)break e;if(n[t+A|0]=f,e=e+4|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 6:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(!(b=c[e+4>>2])&(f=c[e>>2])>>>0>255|b)break e;if(n[t+A|0]=f,e=e+8|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 7:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(!(b=c[e+4>>2])&(f=c[e>>2])>>>0>255|b)break e;if(n[t+A|0]=f,e=e+8|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 8:t:{if((i&=255)>>>0>(f=k[r+24|0])>>>0?f:i)for(f=c[c[r>>2]>>2],a=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+a|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break t;if((v=p[e>>2])>=d(255)|v<d(0)|d(m(v))==d(1/0)|v!=v)break t;f=t+A|0;f:{a:{if(k[r+32|0]){if(v>d(1))break t;if(!((u=N(255*+v+.5))<4294967296&u>=0))break a;o=~~u>>>0;break f}if(v<d(4294967296)&v>=d(0)){o=~~v>>>0;break f}}o=0}if(n[0|f]=o,e=e+4|0,!((A=A+1|0)>>>0<(i>>>0>(f=k[r+24|0])>>>0?f:i)>>>0))break}y=1,i>>>0<=f>>>0||Sr(t+f|0,0,i-f|0)}return y;case 9:if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if((u=l[e>>3])>=255|u<0|m(u)==1/0|u!=u)break e;if(f=t+A|0,k[r+32|0]){if(u>1)break e;u=N(255*u+.5)}if(o=u<4294967296&u>=0?~~u>>>0:0,n[0|f]=o,e=e+8|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}if(y=1,f>>>0>=a>>>0)break e;break r;case 10:break i;default:break e}if((f=k[r+24|0])>>>0<(a=255&i)>>>0?f:a)for(f=c[c[r>>2]>>2],b=c[r+48>>2],o=e=$e(c[r+40>>2],c[r+44>>2],e,0),e=(e=e+b|0)+f|0;;){if(s[c[r>>2]+4>>2]<=e>>>0)break e;if(n[t+A|0]=k[0|e],e=e+1|0,!((A=A+1|0)>>>0<((f=k[r+24|0])>>>0<a>>>0?f:a)>>>0))break}y=1,f>>>0>=a>>>0||Sr(t+f|0,0,(255&i)-f|0)}return y}return Sr(t+f|0,0,(255&i)-f|0),1}function P(r,e,i){var t,f=0,a=0,b=0,u=0,p=0;Z=t=Z-48|0,a=_[5053]|_[5054]<<16,f=_[5051]|_[5052]<<16,A[t+38>>1]=f,A[t+40>>1]=f>>>16,A[t+42>>1]=a,A[t+44>>1]=a>>>16,f=c[2525],c[t+32>>2]=c[2524],c[t+36>>2]=f,f=c[2523],c[t+24>>2]=c[2522],c[t+28>>2]=f,f=c[2521],c[t+16>>2]=c[2520],c[t+20>>2]=f,b=c[e+8>>2],p=c[e+12>>2],u=c[e+20>>2];r:{e:if(b>>>0<(a=(f=c[e+16>>2])+5|0)>>>0&(0|(u=a>>>0<5?u+1|0:u))>=(0|p)|(0|u)>(0|p)){if((f=Xe(t+16|0))>>>0>=2147483632)break r;i:{if(f>>>0>=11)i=vi(e=1+(15|f)|0),c[t+8>>2]=-2147483648|e,c[t>>2]=i,c[t+4>>2]=f,e=i+f|0;else if(n[t+11|0]=f,e=f+t|0,i=t,!f)break i;hr(i,t+16|0,f)}if(n[0|e]=0,c[r>>2]=-2,e=r+4|0,n[t+11|0]>=0){r=c[t+4>>2],c[e>>2]=c[t>>2],c[e+4>>2]=r,c[e+8>>2]=c[t+8>>2];break e}if(me(e,c[t>>2],c[t+4>>2]),n[t+11|0]>=0)break e;er(c[t>>2])}else if(a=f+c[e>>2]|0,f=k[0|a]|k[a+1|0]<<8|k[a+2|0]<<16|k[a+3|0]<<24,n[0|i]=f,n[i+1|0]=f>>>8,n[i+2|0]=f>>>16,n[i+3|0]=f>>>24,n[i+4|0]=k[a+4|0],f=c[e+20>>2],f=(a=c[e+16>>2]+5|0)>>>0<5?f+1|0:f,c[e+16>>2]=a,c[e+20>>2]=f,Ye(i,1250,5))f=vi(32),n[f+17|0]=0,n[f+16|0]=k[1494],i=k[1490]|k[1491]<<8|k[1492]<<16|k[1493]<<24,e=k[1486]|k[1487]<<8|k[1488]<<16|k[1489]<<24,n[f+8|0]=e,n[f+9|0]=e>>>8,n[f+10|0]=e>>>16,n[f+11|0]=e>>>24,n[f+12|0]=i,n[f+13|0]=i>>>8,n[f+14|0]=i>>>16,n[f+15|0]=i>>>24,i=k[1482]|k[1483]<<8|k[1484]<<16|k[1485]<<24,e=k[1478]|k[1479]<<8|k[1480]<<16|k[1481]<<24,n[0|f]=e,n[f+1|0]=e>>>8,n[f+2|0]=e>>>16,n[f+3|0]=e>>>24,n[f+4|0]=i,n[f+5|0]=i>>>8,n[f+6|0]=i>>>16,n[f+7|0]=i>>>24,c[r>>2]=-1,me(r+4|0,f,17),er(f);else if((0|(b=c[e+12>>2]))<=(0|f)&s[e+8>>2]<=a>>>0|(0|f)>(0|b)){if((f=Xe(t+16|0))>>>0>=2147483632)break r;i:{if(f>>>0>=11)i=vi(e=1+(15|f)|0),c[t+8>>2]=-2147483648|e,c[t>>2]=i,c[t+4>>2]=f,e=i+f|0;else if(n[t+11|0]=f,e=f+t|0,i=t,!f)break i;hr(i,t+16|0,f)}if(n[0|e]=0,c[r>>2]=-2,e=r+4|0,n[t+11|0]>=0){r=c[t+4>>2],c[e>>2]=c[t>>2],c[e+4>>2]=r,c[e+8>>2]=c[t+8>>2];break e}if(me(e,c[t>>2],c[t+4>>2]),n[t+11|0]>=0)break e;er(c[t>>2])}else if(n[i+5|0]=k[a+c[e>>2]|0],b=c[e+20>>2],b=(f=c[e+16>>2]+1|0)?b:b+1|0,c[e+16>>2]=f,c[e+20>>2]=b,(0|(a=c[e+12>>2]))<=(0|b)&s[e+8>>2]<=f>>>0|(0|b)>(0|a)){if((f=Xe(t+16|0))>>>0>=2147483632)break r;i:{if(f>>>0>=11)i=vi(e=1+(15|f)|0),c[t+8>>2]=-2147483648|e,c[t>>2]=i,c[t+4>>2]=f,e=i+f|0;else if(n[t+11|0]=f,e=f+t|0,i=t,!f)break i;hr(i,t+16|0,f)}if(n[0|e]=0,c[r>>2]=-2,e=r+4|0,n[t+11|0]>=0){r=c[t+4>>2],c[e>>2]=c[t>>2],c[e+4>>2]=r,c[e+8>>2]=c[t+8>>2];break e}if(me(e,c[t>>2],c[t+4>>2]),n[t+11|0]>=0)break e;er(c[t>>2])}else if(n[i+6|0]=k[f+c[e>>2]|0],u=c[e+20>>2],u=(f=c[e+16>>2]+1|0)?u:u+1|0,c[e+16>>2]=f,c[e+20>>2]=u,(0|(a=c[e+12>>2]))<=(0|u)&s[e+8>>2]<=f>>>0|(0|a)<(0|u)){if((f=Xe(t+16|0))>>>0>=2147483632)break r;i:{if(f>>>0>=11)i=vi(e=1+(15|f)|0),c[t+8>>2]=-2147483648|e,c[t>>2]=i,c[t+4>>2]=f,e=i+f|0;else if(n[t+11|0]=f,e=f+t|0,i=t,!f)break i;hr(i,t+16|0,f)}if(n[0|e]=0,c[r>>2]=-2,e=r+4|0,n[t+11|0]>=0){r=c[t+4>>2],c[e>>2]=c[t>>2],c[e+4>>2]=r,c[e+8>>2]=c[t+8>>2];break e}if(me(e,c[t>>2],c[t+4>>2]),n[t+11|0]>=0)break e;er(c[t>>2])}else if(n[i+7|0]=k[f+c[e>>2]|0],b=c[e+20>>2],b=(f=c[e+16>>2]+1|0)?b:b+1|0,c[e+16>>2]=f,c[e+20>>2]=b,(0|(a=c[e+12>>2]))<=(0|b)&s[e+8>>2]<=f>>>0|(0|b)>(0|a)){if(i=Pe(t,t+16|0),c[r>>2]=-2,e=r+4|0,n[i+11|0]>=0){r=c[i+4>>2],c[e>>2]=c[i>>2],c[e+4>>2]=r,c[e+8>>2]=c[i+8>>2];break e}if(me(e,c[i>>2],c[i+4>>2]),n[i+11|0]>=0)break e;er(c[i>>2])}else if(n[i+8|0]=k[f+c[e>>2]|0],f=c[e+20>>2],p=(a=(b=c[e+16>>2])+1|0)?f:f+1|0,c[e+16>>2]=a,c[e+20>>2]=p,(b=b+3|0)>>>0>(p=c[e+8>>2])>>>0&(0|(f=b>>>0<3?f+1|0:f))>=(0|(u=c[e+12>>2]))|(0|f)>(0|u)){if(i=Pe(t,t+16|0),c[r>>2]=-2,e=r+4|0,n[i+11|0]>=0){r=c[i+4>>2],c[e>>2]=c[i>>2],c[e+4>>2]=r,c[e+8>>2]=c[i+8>>2];break e}if(me(e,c[i>>2],c[i+4>>2]),n[i+11|0]>=0)break e;er(c[i>>2])}else f=i,i=c[e>>2]+a|0,A[f+10>>1]=k[0|i]|k[i+1|0]<<8,b=c[e+20>>2],b=(i=c[e+16>>2]+2|0)>>>0<2?b+1|0:b,c[e+16>>2]=i,c[e+20>>2]=b,c[r+8>>2]=0,c[r+12>>2]=0,c[r>>2]=0,c[r+4>>2]=0;return void(Z=t+48|0)}yt(),o()}function x(r,e,i){e|=0,i|=0;var t,f=0,a=0,A=0,b=0,u=0,_=0,s=0,p=0,l=0,y=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0;Z=t=Z-96|0,a=c[(r|=0)+16>>2],n[t+92|0]=1,c[t+88>>2]=e,c[t+84>>2]=e,c[t+80>>2]=a,_=c[r+20>>2],f=c[_>>2];r:{e:{if((a=c[c[a+28>>2]+(e<<2)>>2])>>>0<c[_+4>>2]-f>>2>>>0){if(f=c[c[r+8>>2]+(c[f+(a<<2)>>2]<<2)>>2],a=c[r+4>>2],k[a+84|0]||(f=c[c[a+68>>2]+(f<<2)>>2]),c[t+72>>2]=0,c[t+76>>2]=0,c[(_=t- -64|0)>>2]=0,c[_+4>>2]=0,c[t+56>>2]=0,c[t+60>>2]=0,g(a,f,n[a+24|0],t+56|0),-1!=(0|e))for(_=((a=e+1|0)>>>0)%3|0?a:e-2|0,l=((e>>>0)%3|0?-1:2)+e|0;;){if(f=_,a=l,c[r+28>>2]&&(f=((a=e+1|0)>>>0)%3|0?a:e-2|0,a=e-1|0,(e>>>0)%3|0||(a=e+2|0)),y=c[r+20>>2],e=c[y>>2],(f=c[c[c[r+16>>2]+28>>2]+(f<<2)>>2])>>>0>=c[y+4>>2]-e>>2>>>0)break e;if(f=c[c[r+8>>2]+(c[e+(f<<2)>>2]<<2)>>2],e=c[r+4>>2],k[e+84|0]||(f=c[c[e+68>>2]+(f<<2)>>2]),c[t+48>>2]=0,c[t+52>>2]=0,c[t+40>>2]=0,c[t+44>>2]=0,c[t+32>>2]=0,c[t+36>>2]=0,g(e,f,n[e+24|0],t+32|0),f=c[r+20>>2],e=c[f>>2],(a=c[c[c[r+16>>2]+28>>2]+(a<<2)>>2])>>>0>=c[f+4>>2]-e>>2>>>0)break r;f=c[c[r+8>>2]+(c[e+(a<<2)>>2]<<2)>>2],e=c[r+4>>2],k[e+84|0]||(f=c[c[e+68>>2]+(f<<2)>>2]),c[t+24>>2]=0,c[t+28>>2]=0,c[t+16>>2]=0,c[t+20>>2]=0,c[t+8>>2]=0,c[t+12>>2]=0,g(e,f,n[e+24|0],t+8|0),f=(A=c[t+8>>2])-(e=c[t+56>>2])|0,m=c[t+60>>2],N=c[t+12>>2]-(m+(e>>>0>A>>>0)|0)|0,y=(b=c[t+40>>2])-(a=c[t+64>>2])|0,T=c[t+68>>2],A=$e(f,N,y,W=c[t+44>>2]-(T+(a>>>0>b>>>0)|0)|0),U=u-(E+(A>>>0>d>>>0)|0)|0,d=(u=V=d-A|0)+(e=$e(A=(b=c[t+16>>2])-a|0,T=c[t+20>>2]-((a>>>0>b>>>0)+T|0)|0,b=(s=c[t+32>>2])-e|0,V=c[t+36>>2]-((e>>>0>s>>>0)+m|0)|0))|0,u=E+U|0,u=e>>>0>d>>>0?u+1|0:u,e=p,p=f,m=N,f=(s=c[t+48>>2])-(a=c[t+72>>2])|0,N=c[t+76>>2],s=e+(p=$e(p,m,f,U=c[t+52>>2]-(N+(a>>>0>s>>>0)|0)|0))|0,e=E+v|0,e=s>>>0<p>>>0?e+1|0:e,p=s-(v=$e(m=(p=c[t+24>>2])-a|0,a=c[t+28>>2]-((a>>>0>p>>>0)+N|0)|0,b,V))|0,v=e-(E+(s>>>0<v>>>0)|0)|0,f=h-(e=$e(A,T,f,U))|0,e=R-(E+(e>>>0>h>>>0)|0)|0,R=$e(m,a,y,W),e=E+e|0,R=(h=R+f|0)>>>0<R>>>0?e+1|0:e,e=c[t+88>>2],a=c[t+80>>2];i:if(k[t+92|0]){t:{if(-1==(0|e)||-1==(0|(e=((f=e+1|0)>>>0)%3|0?f:e-2|0))|c[c[a>>2]+(e>>>3&536870908)>>2]>>>e&1||-1==(0|(e=c[c[c[a+64>>2]+12>>2]+(e<<2)>>2])))c[t+88>>2]=-1;else if(e=((f=e+1|0)>>>0)%3|0?f:e-2|0,c[t+88>>2]=e,-1!=(0|e))break t;f=-1;f:if(-1!=(0|(e=c[t+84>>2]))){if((e>>>0)%3|0)e=e-1|0;else if(f=-1,-1==(0|(e=e+2|0)))break f;f=-1,c[c[a>>2]+(e>>>3&536870908)>>2]>>>e&1||(f=-1,-1!=(0|(e=c[c[c[a+64>>2]+12>>2]+(e<<2)>>2]))&&(f=e-1|0,(e>>>0)%3|0||(f=e+2|0)))}n[t+92|0]=0,c[t+88>>2]=f;break i}if((0|e)!=c[t+84>>2])break i;c[t+88>>2]=-1}else{f=-1;t:if(-1!=(0|e)){if((e>>>0)%3|0)e=e-1|0;else if(f=-1,-1==(0|(e=e+2|0)))break t;f=-1,c[c[a>>2]+(e>>>3&536870908)>>2]>>>e&1||(f=-1,-1!=(0|(e=c[c[c[a+64>>2]+12>>2]+(e<<2)>>2]))&&(f=e-1|0,(e>>>0)%3|0||(f=e+2|0)))}c[t+88>>2]=f}if(-1==(0|(e=c[t+88>>2])))break}f=(a=(e=R>>31)^h)-e|0,e=(e^R)-((e>>>0>a>>>0)+e|0)|0,l=-1,a=2147483647,s=-1^(_=(b=(A=v>>31)^p)-A|0),A=2147483647^(b=y=(A^v)-((b>>>0<A>>>0)+A|0)|0),y=u;i:{if(c[r+28>>2]){if(!((0|e)==(0|A)&f>>>0>s>>>0|e>>>0>A>>>0)&&(e=e+b|0,e=(r=f+_|0)>>>0<_>>>0?e+1|0:e,s=u,u=(b=(f=u>>31)^d)-f|0,!((0|(A=2147483647^(_=(f^s)-((f>>>0>b>>>0)+f|0)|0)))==(0|e)&(f=r)>>>0>(-1^(r=u))>>>0|e>>>0>A>>>0)&&(e=e+_|0,a=e=(l=r+f|0)>>>0<r>>>0?e+1|0:e,!e&l>>>0<536870913)))break i;e=a>>>29|0,r=(536870911&a)<<3|l>>>29}else{if((0|e)==(0|A)&f>>>0>s>>>0|e>>>0>A>>>0)break i;if(e=e+b|0,e=(r=f+_|0)>>>0<_>>>0?e+1|0:e,a=r,u=r=(l=(f=r=(A=u)>>31)^d)-f|0,f=(f^A)-((f>>>0>l>>>0)+f|0)|0,r=r+a|0,!(0&(u=(0|(f^=2147483647))==(0|e)&(-1^u)>>>0<a>>>0|e>>>0>f>>>0))&(0|(r=u?-1:r))<=536870912|(0|r)<536870912)break i;e=0,r=r>>>29|0}d=Ke(d,y,r,e),p=Ke(p,v,r,e),h=Ke(h,R,r,e)}return c[i+8>>2]=d,c[i+4>>2]=p,c[i>>2]=h,void(Z=t+96|0)}dt(),o()}dt(),o()}dt(),o()}function L(r,e,i){var t,f=0,a=0,n=0,A=0,o=0,b=0,u=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0;Z=t=Z+-64|0,c[t+56>>2]=0,c[t+48>>2]=0,c[t+52>>2]=0,c[t+40>>2]=0,c[t+44>>2]=0,c[t+32>>2]=0,c[t+36>>2]=0,c[t+24>>2]=0,c[t+28>>2]=0,c[t+16>>2]=0,c[t+20>>2]=0,c[t+8>>2]=0,c[t+12>>2]=0,A=t+8|0;r:{e:if(_[e+38>>1]&&Ze(1,A+12|0,e)&&!((b=(a=c[e+8>>2])-(n=c[e+16>>2])|0)>>>0<(u=c[A+12>>2])>>>6>>>0&(0|(a=c[e+12>>2]-(c[e+20>>2]+(a>>>0<n>>>0)|0)|0))<=0|(0|a)<0)){if(a=c[A>>2],(f=c[A+4>>2]-a>>2)>>>0<u>>>0?(_e(A,u-f|0),u=c[A+12>>2]):f>>>0<=u>>>0||(c[A+4>>2]=a+(u<<2)),o=1,!u)break r;for(a=c[e+16>>2],f=c[e+20>>2],h=c[A>>2],s=c[e+8>>2],d=c[e+12>>2],b=0;;){if(o=0,(0|f)>=(0|d)&a>>>0>=s>>>0|(0|f)>(0|d))break r;o=c[e>>2],m=k[o+a|0],f=(a=a+1|0)?f:f+1|0,c[e+16>>2]=a,c[e+20>>2]=f,n=m>>>2|0,p=0;i:{t:{f:{a:switch(0|(R=3&m)){case 3:break a;case 0:break t;default:break f}if(o=0,(n=n+b|0)>>>0>=u>>>0)break r;Sr(h+(b<<2)|0,0,4+(252&m)|0),b=n;break i}for(;;){if((0|a)==(0|s)&(0|f)==(0|d))break e;if(u=k[a+o|0],f=(a=a+1|0)?f:f+1|0,c[e+16>>2]=a,c[e+20>>2]=f,n|=u<<(p<<3|6),(0|R)==(0|(p=p+1|0)))break}}c[h+(b<<2)>>2]=n}if(!((b=b+1|0)>>>0<(u=c[A+12>>2])>>>0))break}if(f=A+16|0,d=c[A>>2],n=c[A+16>>2],(a=c[A+20>>2]-n|0)>>>0<=4194303?_e(f,1048576-(a>>>2|0)|0):4194304!=(0|a)&&(c[A+20>>2]=n+4194304),b=c[(a=A+28|0)>>2],(n=c[A+32>>2]-b>>3)>>>0<u>>>0)ye(a,u-n|0),b=c[a>>2];else if(n>>>0>u>>>0&&(c[A+32>>2]=(u<<3)+b),!u)break e;for(s=c[f>>2],f=0,o=0;;){if(A=c[(a=d+(f<<2)|0)>>2],n=o,c[(p=(f<<3)+b|0)+4>>2]=n,c[p>>2]=A,(o=(a=c[a>>2])+n|0)>>>0>1048576)break e;if(!(n>>>0>=o>>>0)){if(p=0,A=7&a)for(;c[s+(n<<2)>>2]=f,n=n+1|0,(0|A)!=(0|(p=p+1|0)););if(!(a-1>>>0<=6))for(;c[(a=s+(n<<2)|0)>>2]=f,c[a+28>>2]=f,c[a+24>>2]=f,c[a+20>>2]=f,c[a+16>>2]=f,c[a+12>>2]=f,c[a+8>>2]=f,c[a+4>>2]=f,(0|o)!=(0|(n=n+8|0)););}if((0|u)==(0|(f=f+1|0)))break}l=1048576==(0|o)}o=l}if(!(!o|(c[t+20>>2]?0:r))){o=0,Z=b=Z-16|0;r:if(Re(1,b+8|0,e)&&(s=(f=c[e+8>>2])-(n=c[e+16>>2])|0,l=c[b+12>>2],A=c[e+20>>2],!((0|l)==(0|(f=c[e+12>>2]-(A+(f>>>0<n>>>0)|0)|0))&(a=c[b+8>>2])>>>0>s>>>0|f>>>0<l>>>0||(f=A+l|0,f=(s=a+n|0)>>>0<n>>>0?f+1|0:f,c[e+16>>2]=s,c[e+20>>2]=f,(0|a)<=0)))){e=n+c[e>>2]|0,c[t+48>>2]=e;e:if((s=k[0|(n=(f=a-1|0)+e|0)])>>>0<=63)c[t+52>>2]=f,e=63&k[0|n];else{i:switch((s>>>6|0)-1|0){case 0:if(a>>>0<2)break r;f=a-2|0,c[t+52>>2]=f,e=k[(e=e+f|0)+1|0]<<8&16128|k[0|e];break e;case 1:if(a>>>0<3)break r;f=a-3|0,c[t+52>>2]=f,e=k[(e=e+f|0)+1|0]<<8|k[e+2|0]<<16&4128768|k[0|e];break e;default:break i}f=a-4|0,c[t+52>>2]=f,e=1073741823&(k[0|(e=e+f|0)]|k[e+1|0]<<8|k[e+2|0]<<16|k[e+3|0]<<24)}c[t+56>>2]=e+4194304,o=e>>>0<1069547520}if(Z=b+16|0,o)if(r)for(e=c[t+52>>2],n=c[t+56>>2],f=c[t+36>>2],a=c[t+48>>2],b=c[t+24>>2];;){r:if(!(n>>>0>4194303))for(;;){if((0|e)<=0)break r;if(e=e-1|0,c[t+52>>2]=e,n=k[e+a|0]|n<<8,c[t+56>>2]=n,!(n>>>0<4194304))break}if(s=c[b+((o=1048575&n)<<2)>>2],n=(y(c[(l=f+(s<<3)|0)>>2],n>>>20|0)+o|0)-c[l+4>>2]|0,c[t+56>>2]=n,c[(v<<2)+i>>2]=s,N=1,(0|(v=v+1|0))==(0|r))break}else N=1}return(r=c[t+36>>2])&&(c[t+40>>2]=r,er(r)),(r=c[t+24>>2])&&(c[t+28>>2]=r,er(r)),(r=c[t+8>>2])&&(c[t+12>>2]=r,er(r)),Z=t- -64|0,N}function K(r,e){var i=0,t=0,f=0,a=0,A=0,b=0,u=0;if((a=((i=c[r+4>>2])-(f=c[r>>2])|0)/144|0)>>>0<e>>>0)if(f=r,(e=e-a|0)>>>0<=((b=c[r+8>>2])-(i=c[r+4>>2])|0)/144>>>0){if(e){if(r=i,a=7&e)for(;ue(r),r=r+144|0,(0|a)!=(0|(t=t+1|0)););if(i=y(e,144)+i|0,!((e-1&268435455)>>>0<7))for(;ue(r),ue(r+144|0),ue(r+288|0),ue(r+432|0),ue(r+576|0),ue(r+720|0),ue(r+864|0),ue(r+1008|0),(0|i)!=(0|(r=r+1152|0)););}c[f+4>>2]=i}else{r:{e:{i:{if((r=(u=((r=i)-(i=c[f>>2])|0)/144|0)+e|0)>>>0<29826162){if(a=(i=(b-i|0)/144|0)<<1,a=i>>>0>=14913080?29826161:r>>>0<a>>>0?a:r){if(a>>>0>=29826162)break i;A=vi(y(a,144))}if(r=i=y(u,144)+A|0,b=7&e)for(;ue(r),r=r+144|0,(0|b)!=(0|(t=t+1|0)););if(b=y(e,144)+i|0,(e-1&268435455)>>>0>=7)for(;ue(r),ue(r+144|0),ue(r+288|0),ue(r+432|0),ue(r+576|0),ue(r+720|0),ue(r+864|0),ue(r+1008|0),(0|b)!=(0|(r=r+1152|0)););if(e=y(a,144)+A|0,(0|(t=c[f+4>>2]))==(0|(a=c[f>>2])))break e;for(;r=t=t-144|0,c[(i=i-144|0)>>2]=c[r>>2],c[i+4>>2]=c[r+4>>2],c[i+8>>2]=c[r+8>>2],c[i+12>>2]=c[r+12>>2],c[r+12>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[i+16>>2]=c[r+16>>2],c[i+20>>2]=c[r+20>>2],c[i+24>>2]=c[r+24>>2],c[r+24>>2]=0,c[r+16>>2]=0,c[r+20>>2]=0,A=k[r+28|0],c[i+40>>2]=0,c[i+32>>2]=0,c[i+36>>2]=0,n[i+28|0]=A,c[i+32>>2]=c[r+32>>2],c[i+36>>2]=c[r+36>>2],c[i+40>>2]=c[r+40>>2],c[r+40>>2]=0,c[r+32>>2]=0,c[r+36>>2]=0,c[i+52>>2]=0,c[i+44>>2]=0,c[i+48>>2]=0,c[i+44>>2]=c[r+44>>2],c[i+48>>2]=c[r+48>>2],c[i+52>>2]=c[r+52>>2],c[r+52>>2]=0,c[r+44>>2]=0,c[r+48>>2]=0,c[(A=i- -64|0)>>2]=0,c[i+56>>2]=0,c[i+60>>2]=0,c[i+56>>2]=c[r+56>>2],c[i+60>>2]=c[r+60>>2],u=A,A=r- -64|0,c[u>>2]=c[A>>2],c[A>>2]=0,c[r+56>>2]=0,c[r+60>>2]=0,c[i+68>>2]=c[r+68>>2],A=c[r+72>>2],c[i+84>>2]=0,c[i+76>>2]=0,c[i+80>>2]=0,c[i+72>>2]=A,c[i+76>>2]=c[r+76>>2],c[i+80>>2]=c[r+80>>2],c[i+84>>2]=c[r+84>>2],c[r+84>>2]=0,c[r+76>>2]=0,c[r+80>>2]=0,c[i+96>>2]=0,c[i+88>>2]=0,c[i+92>>2]=0,c[i+88>>2]=c[r+88>>2],c[i+92>>2]=c[r+92>>2],c[i+96>>2]=c[r+96>>2],c[r+96>>2]=0,c[r+88>>2]=0,c[r+92>>2]=0,A=k[r+100|0],c[i+112>>2]=0,c[i+104>>2]=0,c[i+108>>2]=0,n[i+100|0]=A,c[i+104>>2]=c[r+104>>2],c[i+108>>2]=c[r+108>>2],c[i+112>>2]=c[r+112>>2],c[r+112>>2]=0,c[r+104>>2]=0,c[r+108>>2]=0,c[i+124>>2]=0,c[i+116>>2]=0,c[i+120>>2]=0,c[i+116>>2]=c[r+116>>2],c[i+120>>2]=c[r+120>>2],c[i+124>>2]=c[r+124>>2],c[r+124>>2]=0,c[r+116>>2]=0,c[r+120>>2]=0,A=c[r+128>>2],c[i+140>>2]=0,c[i+132>>2]=0,c[i+136>>2]=0,c[i+128>>2]=A,c[i+132>>2]=c[r+132>>2],c[i+136>>2]=c[r+136>>2],c[i+140>>2]=c[r+140>>2],c[r+140>>2]=0,c[r+132>>2]=0,c[r+136>>2]=0,(0|r)!=(0|a););if(c[f+8>>2]=e,r=c[f+4>>2],c[f+4>>2]=b,t=c[f>>2],c[f>>2]=i,(0|r)==(0|t))break r;for(;(i=c[(e=r-144|0)+132>>2])&&(c[r-8>>2]=i,er(i)),(i=c[r-28>>2])&&(c[r-24>>2]=i,er(i)),(i=c[r-40>>2])&&(c[r-36>>2]=i,er(i)),Oe(r-140|0),(0|t)!=(0|(r=e)););break r}mt(),o()}Zi(),o()}c[f+8>>2]=e,c[f+4>>2]=b,c[f>>2]=i}t&&er(t)}else if(e>>>0<a>>>0){if((0|(f=f+y(e,144)|0))!=(0|i))for(;(t=c[(e=i-144|0)+132>>2])&&(c[i-8>>2]=t,er(t)),(t=c[i-28>>2])&&(c[i-24>>2]=t,er(t)),(t=c[i-40>>2])&&(c[i-36>>2]=t,er(t)),Oe(i-140|0),(0|f)!=(0|(i=e)););c[r+4>>2]=f}}function q(r){var e=0,i=0,t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0,_=0;if(c[r+56>>2]=c[r+52>>2],c[r+44>>2]=c[r+40>>2],e=c[r+64>>2],(0|(i=c[e+24>>2]))==c[e+28>>2])return 1;r:{e:{i:{for(;;){n=b;t:if(-1!=(0|(b=c[(k<<2)+i>>2]))){if((0|(e=c[r+56>>2]))==c[r+60>>2]){if((i=(A=(f=e-(t=c[r+52>>2])|0)>>2)+1|0)>>>0>=1073741824)break i;if(a=f>>>1|0,a=f>>>0>=2147483644?1073741823:i>>>0<a>>>0?a:i){if(a>>>0>=1073741824)break e;f=vi(a<<2)}else f=0;if(c[(i=f+(A<<2)|0)>>2]=n,A=i+4|0,(0|e)!=(0|t))for(;e=e-4|0,c[(i=i-4|0)>>2]=c[e>>2],(0|e)!=(0|t););c[r+60>>2]=f+(a<<2),c[r+56>>2]=A,c[r+52>>2]=i,t&&er(t)}else c[e>>2]=n,c[r+56>>2]=e+4;f:{if(c[c[r+12>>2]+(k>>>3&536870908)>>2]>>>k&1&&!(-1==(0|(f=((f=b+1|0)>>>0)%3|0?f:b-2|0))|c[c[r>>2]+(f>>>3&536870908)>>2]>>>f&1)&&-1!=(0|(f=c[c[c[r+64>>2]+12>>2]+(f<<2)>>2]))&&-1!=(0|(e=((e=f+1|0)>>>0)%3|0?e:f-2|0))){for(i=c[r+64>>2],a=c[r>>2];f=e,e=-1,-1==(0|(t=((t=f+1|0)>>>0)%3|0?t:f-2|0))|c[a+(t>>>3&536870908)>>2]>>>t&1||-1!=(0|(t=c[c[i+12>>2]+(t<<2)>>2]))&&(e=((e=t+1|0)>>>0)%3|0?e:t-2|0),(0|e)!=(0|b);)if(-1==(0|e))break f;return 0}f=b}if(c[c[r+28>>2]+(f<<2)>>2]=n,(0|(e=c[r+44>>2]))==c[r+48>>2]){if((i=(A=(b=e-(t=c[r+40>>2])|0)>>2)+1|0)>>>0>=1073741824)break r;if(a=b>>>1|0,a=b>>>0>=2147483644?1073741823:i>>>0<a>>>0?a:i){if(a>>>0>=1073741824)break e;b=vi(a<<2)}else b=0;if(c[(i=b+(A<<2)|0)>>2]=f,A=i+4|0,(0|e)!=(0|t))for(;e=e-4|0,c[(i=i-4|0)>>2]=c[e>>2],(0|e)!=(0|t););c[r+48>>2]=b+(a<<2),c[r+44>>2]=A,c[r+40>>2]=i,t&&er(t)}else c[e>>2]=f,c[r+44>>2]=e+4;if(b=n+1|0,e=c[r+64>>2],-1!=(0|f)){if((f>>>0)%3|0)i=f-1|0;else if(-1==(0|(i=f+2|0)))break t;if(-1!=(0|(t=c[c[e+12>>2]+(i<<2)>>2]))&&!(-1==(0|(a=t+((t>>>0)%3|0?-1:2)|0))|(0|f)==(0|a)))for(;;){if(e=((e=a+1|0)>>>0)%3|0?e:a-2|0,c[c[r>>2]+(e>>>3&536870908)>>2]>>>e&1){if((0|(e=c[r+56>>2]))==c[r+60>>2]){if((i=(u=(n=e-(t=c[r+52>>2])|0)>>2)+1|0)>>>0>=1073741824)break i;if(A=n>>>1|0,A=n>>>0>=2147483644?1073741823:i>>>0<A>>>0?A:i){if(A>>>0>=1073741824)break e;n=vi(A<<2)}else n=0;if(c[(i=n+(u<<2)|0)>>2]=b,u=i+4|0,(0|e)!=(0|t))for(;e=e-4|0,c[(i=i-4|0)>>2]=c[e>>2],(0|e)!=(0|t););c[r+60>>2]=n+(A<<2),c[r+56>>2]=u,c[r+52>>2]=i,t&&er(t)}else c[e>>2]=b,c[r+56>>2]=e+4;if(t=b+1|0,(0|(e=c[r+44>>2]))==c[r+48>>2]){if((i=(_=(n=e-(A=c[r+40>>2])|0)>>2)+1|0)>>>0>=1073741824)break r;if(u=n>>>1|0,u=n>>>0>=2147483644?1073741823:i>>>0<u>>>0?u:i){if(u>>>0>=1073741824)break e;n=vi(u<<2)}else n=0;if(c[(i=n+(_<<2)|0)>>2]=a,_=i+4|0,(0|e)!=(0|A))for(;e=e-4|0,c[(i=i-4|0)>>2]=c[e>>2],(0|e)!=(0|A););c[r+48>>2]=n+(u<<2),c[r+44>>2]=_,c[r+40>>2]=i,A&&er(A)}else c[e>>2]=a,c[r+44>>2]=e+4;n=b,b=t}if(c[c[r+28>>2]+(a<<2)>>2]=n,e=c[r+64>>2],(a>>>0)%3|0)i=a-1|0;else if(-1==(0|(i=a+2|0)))break t;if(-1==(0|(t=c[c[e+12>>2]+(i<<2)>>2])))break t;if(-1==(0|(a=t+((t>>>0)%3|0?-1:2)|0)))break t;if((0|f)==(0|a))break}}}else b=n;if(k=k+1|0,i=c[e+24>>2],!(k>>>0<c[e+28>>2]-i>>2>>>0))break}return 1}mt(),o()}Zi(),o()}mt(),o()}function $(r,e,i){e|=0,i|=0;var t,f=0,a=0,A=0,b=0,u=0,_=0,s=0,p=0,l=0,y=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0;if(Z=t=Z-96|0,a=c[(r|=0)+16>>2],n[t+92|0]=1,c[t+88>>2]=e,c[t+84>>2]=e,c[t+80>>2]=a,-1!=(0|e)&&(_=c[r+20>>2],f=c[_>>2],!((a=c[c[a>>2]+(e<<2)>>2])>>>0>=c[_+4>>2]-f>>2>>>0))){a=c[c[r+8>>2]+(c[f+(a<<2)>>2]<<2)>>2],f=c[r+4>>2],k[f+84|0]||(a=c[c[f+68>>2]+(a<<2)>>2]),c[t+72>>2]=0,c[t+76>>2]=0,c[(_=t- -64|0)>>2]=0,c[_+4>>2]=0,c[t+56>>2]=0,c[t+60>>2]=0,g(f,a,n[f+24|0],t+56|0),_=((a=e+1|0)>>>0)%3|0?a:e-2|0,y=((e>>>0)%3|0?-1:2)+e|0;r:{e:{for(;;){if(f=_,a=y,c[r+28>>2]&&(f=((a=e+1|0)>>>0)%3|0?a:e-2|0,a=e-1|0,(e>>>0)%3|0||(a=e+2|0)),-1==(0|f))break r;if(l=c[r+20>>2],e=c[l>>2],(f=c[c[c[r+16>>2]>>2]+(f<<2)>>2])>>>0>=c[l+4>>2]-e>>2>>>0)break r;if(f=c[c[r+8>>2]+(c[(f<<2)+e>>2]<<2)>>2],e=c[r+4>>2],k[e+84|0]||(f=c[c[e+68>>2]+(f<<2)>>2]),c[t+48>>2]=0,c[t+52>>2]=0,c[t+40>>2]=0,c[t+44>>2]=0,c[t+32>>2]=0,c[t+36>>2]=0,g(e,f,n[e+24|0],t+32|0),-1==(0|a))break e;if(f=c[r+20>>2],e=c[f>>2],(a=c[c[c[r+16>>2]>>2]+(a<<2)>>2])>>>0>=c[f+4>>2]-e>>2>>>0)break e;if(f=c[c[r+8>>2]+(c[e+(a<<2)>>2]<<2)>>2],e=c[r+4>>2],k[e+84|0]||(f=c[c[e+68>>2]+(f<<2)>>2]),c[t+24>>2]=0,c[t+28>>2]=0,c[t+16>>2]=0,c[t+20>>2]=0,c[t+8>>2]=0,c[t+12>>2]=0,g(e,f,n[e+24|0],t+8|0),f=(A=c[t+8>>2])-(e=c[t+56>>2])|0,m=c[t+60>>2],N=c[t+12>>2]-(m+(e>>>0>A>>>0)|0)|0,l=(u=c[t+40>>2])-(a=c[t+64>>2])|0,T=c[t+68>>2],A=$e(f,N,l,W=c[t+44>>2]-(T+(a>>>0>u>>>0)|0)|0),U=b-(E+(A>>>0>d>>>0)|0)|0,d=(b=V=d-A|0)+(e=$e(A=(u=c[t+16>>2])-a|0,T=c[t+20>>2]-((a>>>0>u>>>0)+T|0)|0,u=(s=c[t+32>>2])-e|0,V=c[t+36>>2]-((e>>>0>s>>>0)+m|0)|0))|0,b=E+U|0,b=e>>>0>d>>>0?b+1|0:b,e=p,p=f,m=N,f=(s=c[t+48>>2])-(a=c[t+72>>2])|0,N=c[t+76>>2],s=e+(p=$e(p,m,f,U=c[t+52>>2]-(N+(a>>>0>s>>>0)|0)|0))|0,e=E+v|0,e=s>>>0<p>>>0?e+1|0:e,p=s-(v=$e(m=(p=c[t+24>>2])-a|0,a=c[t+28>>2]-((a>>>0>p>>>0)+N|0)|0,u,V))|0,v=e-(E+(s>>>0<v>>>0)|0)|0,f=h-(e=$e(A,T,f,U))|0,e=R-(E+(e>>>0>h>>>0)|0)|0,R=$e(m,a,l,W),e=E+e|0,R=(h=R+f|0)>>>0<R>>>0?e+1|0:e,Mr(t+80|0),-1==(0|(e=c[t+88>>2])))break}f=(a=(e=R>>31)^h)-e|0,e=(e^R)-((e>>>0>a>>>0)+e|0)|0,y=-1,a=2147483647,s=-1^(_=(u=(A=l=v>>31)^p)-A|0),A=2147483647^(u=l=(A^v)-((u>>>0<A>>>0)+A|0)|0),l=b;i:{if(c[r+28>>2]){if(!((0|e)==(0|A)&f>>>0>s>>>0|e>>>0>A>>>0)&&(e=e+u|0,e=(r=f+_|0)>>>0<_>>>0?e+1|0:e,s=b,b=(u=(A=b>>=31)^d)-A|0,!((0|(A=2147483647^(_=(A^s)-((A>>>0>u>>>0)+A|0)|0)))==(0|e)&(f=r)>>>0>(-1^(r=b))>>>0|e>>>0>A>>>0)&&(e=e+_|0,a=e=(y=r+f|0)>>>0<r>>>0?e+1|0:e,!e&y>>>0<536870913)))break i;e=a>>>29|0,r=(536870911&a)<<3|y>>>29}else{if((0|e)==(0|A)&f>>>0>s>>>0|e>>>0>A>>>0)break i;if(e=e+u|0,e=(r=f+_|0)>>>0<_>>>0?e+1|0:e,a=r,b=r=(y=(f=r=(A=b)>>31)^d)-f|0,f=(f^A)-((f>>>0>y>>>0)+f|0)|0,r=r+a|0,!(0&(b=(0|(f^=2147483647))==(0|e)&(-1^b)>>>0<a>>>0|e>>>0>f>>>0))&(0|(r=b?-1:r))<=536870912|(0|r)<536870912)break i;e=0,r=r>>>29|0}d=Ke(d,l,r,e),p=Ke(p,v,r,e),h=Ke(h,R,r,e)}return c[i+8>>2]=d,c[i+4>>2]=p,c[i>>2]=h,void(Z=t+96|0)}dt(),o()}dt(),o()}dt(),o()}function rr(r,e){r|=0;var i=0,t=0,f=0,a=0,n=0,A=0,o=0,b=0;if(!((0|(e|=0))<0||(i=c[r+12>>2])-(t=c[r+8>>2])>>2>>>0<=e>>>0)){if(f=c[(t=t+(e<<2)|0)>>2],o=c[f+60>>2],a=c[f+56>>2],(0|(f=t+4|0))!=(0|i)){for(;A=c[f>>2],c[f>>2]=0,n=c[t>>2],c[t>>2]=A,n&&xe(n),t=t+4|0,(0|(f=f+4|0))!=(0|i););i=c[r+12>>2]}if((0|i)!=(0|t))for(;f=c[(i=i-4|0)>>2],c[i>>2]=0,f&&xe(f),(0|i)!=(0|t););c[r+12>>2]=t;r:if(!(!(n=c[r+4>>2])|(0|o)<0)&&(0|(i=c[n+24>>2]))!=(0|(t=c[n+28>>2])))for(;;){if((0|o)==c[c[i>>2]+24>>2]){if((0|(t=i+4|0))!=(0|(o=c[n+28>>2]))){for(;A=c[t>>2],c[t>>2]=0,f=c[i>>2],c[i>>2]=A,f&&(Ai(f+12|0,c[f+16>>2]),mi(f,c[f+4>>2]),er(f)),i=i+4|0,(0|o)!=(0|(t=t+4|0)););t=c[n+28>>2]}if((0|i)!=(0|t))for(;f=c[(t=t-4|0)>>2],c[t>>2]=0,f&&(Ai(f+12|0,c[f+16>>2]),mi(f,c[f+4>>2]),er(f)),(0|i)!=(0|t););c[n+28>>2]=i;break r}if((0|t)==(0|(i=i+4|0)))break}r:if(!((0|a)>4)){e:if(f=y(a,12)+r|0,(0|(i=c[f+20>>2]))!=(0|(t=c[f+24>>2]))){for(;;){if(c[i>>2]==(0|e))break e;if((0|t)==(0|(i=i+4|0)))break}break r}(0|i)!=(0|t)&&(gr(a=i,i=i+4|0,t-i|0),c[f+24>>2]=t-4)}if((0|(i=c[r+24>>2]))!=(0|(t=c[r+20>>2]))){if(o=1&(n=(i=(f=i-t|0)>>2)>>>0<=1?1:i),i=0,f>>>0>=8)for(n&=-2,f=0;(0|(b=c[(A=(a=i<<2)+t|0)>>2]))>(0|e)&&(c[A>>2]=b-1),(0|(A=c[(a=t+(4|a)|0)>>2]))>(0|e)&&(c[a>>2]=A-1),i=i+2|0,(0|n)!=(0|(f=f+2|0)););o&&((0|(t=c[(i=t+(i<<2)|0)>>2]))<=(0|e)||(c[i>>2]=t-1))}if((0|(i=c[r+36>>2]))!=(0|(t=c[r+32>>2]))){if(o=1&(n=(i=(f=i-t|0)>>2)>>>0<=1?1:i),i=0,f>>>0>=8)for(n&=-2,f=0;(0|(b=c[(A=(a=i<<2)+t|0)>>2]))>(0|e)&&(c[A>>2]=b-1),(0|(A=c[(a=t+(4|a)|0)>>2]))>(0|e)&&(c[a>>2]=A-1),i=i+2|0,(0|n)!=(0|(f=f+2|0)););o&&((0|(t=c[(i=t+(i<<2)|0)>>2]))<=(0|e)||(c[i>>2]=t-1))}if((0|(i=c[r+48>>2]))!=(0|(t=c[r+44>>2]))){if(o=1&(n=(i=(f=i-t|0)>>2)>>>0<=1?1:i),i=0,f>>>0>=8)for(n&=-2,f=0;(0|(b=c[(A=(a=i<<2)+t|0)>>2]))>(0|e)&&(c[A>>2]=b-1),(0|(A=c[(a=t+(4|a)|0)>>2]))>(0|e)&&(c[a>>2]=A-1),i=i+2|0,(0|n)!=(0|(f=f+2|0)););o&&((0|(t=c[(i=t+(i<<2)|0)>>2]))<=(0|e)||(c[i>>2]=t-1))}if((0|(i=c[r+60>>2]))!=(0|(t=c[r+56>>2]))){if(o=1&(n=(i=(f=i-t|0)>>2)>>>0<=1?1:i),i=0,f>>>0>=8)for(n&=-2,f=0;(0|(b=c[(A=(a=i<<2)+t|0)>>2]))>(0|e)&&(c[A>>2]=b-1),(0|(A=c[(a=t+(4|a)|0)>>2]))>(0|e)&&(c[a>>2]=A-1),i=i+2|0,(0|n)!=(0|(f=f+2|0)););o&&((0|(t=c[(i=t+(i<<2)|0)>>2]))<=(0|e)||(c[i>>2]=t-1))}if((0|(i=c[r+72>>2]))!=(0|(r=c[r+68>>2]))){if(n=1&(f=(i=(t=i-r|0)>>2)>>>0<=1?1:i),i=0,t>>>0>=8)for(t=-2&f,f=0;(0|(A=c[(a=(o=i<<2)+r|0)>>2]))>(0|e)&&(c[a>>2]=A-1),(0|(a=c[(o=r+(4|o)|0)>>2]))>(0|e)&&(c[o>>2]=a-1),i=i+2|0,(0|t)!=(0|(f=f+2|0)););n&&((0|(a=e))>=(0|(e=c[(r=r+(i<<2)|0)>>2]))||(c[r>>2]=e-1))}}}function er(r){var e=0,i=0,t=0,f=0,a=0,n=0,A=0,o=0,b=0;r:if(r|=0){a=(t=r-8|0)+(r=-8&(e=c[r-4>>2]))|0;e:if(!(1&e)){if(!(3&e))break r;if((t=t-(e=c[t>>2])|0)>>>0<s[2945])break r;if(r=r+e|0,c[2946]==(0|t)){if(3==(3&(e=c[a+4>>2])))return c[2943]=r,c[a+4>>2]=-2&e,c[t+4>>2]=1|r,void(c[r+t>>2]=r)}else{if(e>>>0<=255){if(f=c[t+8>>2],e=e>>>3|0,(0|(i=c[t+12>>2]))==(0|f)){o=11764,b=c[2941]&Yi(e),c[o>>2]=b;break e}c[f+12>>2]=i,c[i+8>>2]=f;break e}if(A=c[t+24>>2],(0|t)==(0|(e=c[t+12>>2])))if((i=c[(f=t+20|0)>>2])||(i=c[(f=t+16|0)>>2])){for(;n=f,(i=c[(f=(e=i)+20|0)>>2])||(f=e+16|0,i=c[e+16>>2]););c[n>>2]=0}else e=0;else i=c[t+8>>2],c[i+12>>2]=e,c[e+8>>2]=i;if(!A)break e;f=c[t+28>>2];i:{if(c[(i=12068+(f<<2)|0)>>2]==(0|t)){if(c[i>>2]=e,e)break i;o=11768,b=c[2942]&Yi(f),c[o>>2]=b;break e}if(c[A+(c[A+16>>2]==(0|t)?16:20)>>2]=e,!e)break e}if(c[e+24>>2]=A,(i=c[t+16>>2])&&(c[e+16>>2]=i,c[i+24>>2]=e),!(i=c[t+20>>2]))break e;c[e+20>>2]=i,c[i+24>>2]=e}}if(!(t>>>0>=a>>>0)&&1&(e=c[a+4>>2])){e:{if(!(2&e)){if(c[2947]==(0|a)){if(c[2947]=t,r=c[2944]+r|0,c[2944]=r,c[t+4>>2]=1|r,c[2946]!=(0|t))break r;return c[2943]=0,void(c[2946]=0)}if(c[2946]==(0|a))return c[2946]=t,r=c[2943]+r|0,c[2943]=r,c[t+4>>2]=1|r,void(c[r+t>>2]=r);r=(-8&e)+r|0;i:if(e>>>0<=255){if(f=c[a+8>>2],e=e>>>3|0,(0|(i=c[a+12>>2]))==(0|f)){o=11764,b=c[2941]&Yi(e),c[o>>2]=b;break i}c[f+12>>2]=i,c[i+8>>2]=f}else{if(A=c[a+24>>2],(0|a)==(0|(e=c[a+12>>2])))if((i=c[(f=a+20|0)>>2])||(i=c[(f=a+16|0)>>2])){for(;n=f,(i=c[(f=(e=i)+20|0)>>2])||(f=e+16|0,i=c[e+16>>2]););c[n>>2]=0}else e=0;else i=c[a+8>>2],c[i+12>>2]=e,c[e+8>>2]=i;if(A){f=c[a+28>>2];t:{if(c[(i=12068+(f<<2)|0)>>2]==(0|a)){if(c[i>>2]=e,e)break t;o=11768,b=c[2942]&Yi(f),c[o>>2]=b;break i}if(c[A+(c[A+16>>2]==(0|a)?16:20)>>2]=e,!e)break i}c[e+24>>2]=A,(i=c[a+16>>2])&&(c[e+16>>2]=i,c[i+24>>2]=e),(i=c[a+20>>2])&&(c[e+20>>2]=i,c[i+24>>2]=e)}}if(c[t+4>>2]=1|r,c[r+t>>2]=r,c[2946]!=(0|t))break e;return void(c[2943]=r)}c[a+4>>2]=-2&e,c[t+4>>2]=1|r,c[r+t>>2]=r}if(r>>>0<=255)return e=11804+(-8&r)|0,(i=c[2941])&(r=1<<(r>>>3))?r=c[e+8>>2]:(c[2941]=r|i,r=e),c[e+8>>2]=t,c[r+12>>2]=t,c[t+12>>2]=e,void(c[t+8>>2]=r);f=31,r>>>0<=16777215&&(f=62+((r>>>38-(e=v(r>>>8|0))&1)-(e<<1)|0)|0),c[t+28>>2]=f,c[t+16>>2]=0,c[t+20>>2]=0,n=12068+(f<<2)|0;e:{i:{if((i=c[2942])&(e=1<<f)){for(f=r<<(31!=(0|f)?25-(f>>>1|0)|0:0),e=c[n>>2];;){if(i=e,(-8&c[e+4>>2])==(0|r))break i;if(e=f>>>29|0,f<<=1,!(e=c[(n=i+(4&e)|0)+16>>2]))break}c[n+16>>2]=t,c[t+24>>2]=i}else c[2942]=e|i,c[n>>2]=t,c[t+24>>2]=n;c[t+12>>2]=t,c[t+8>>2]=t;break e}r=c[i+8>>2],c[r+12>>2]=t,c[i+8>>2]=t,c[t+24>>2]=0,c[t+12>>2]=i,c[t+8>>2]=r}r=c[2949]-1|0,c[2949]=r||-1}}}function ir(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0;if(-1==(0|e))return 1;if(n=(e>>>0)/3|0,!(c[c[r+24>>2]+(n>>>3&268435452)>>2]>>>n&1)){if(f=c[r+48>>2],c[r+52>>2]=f,(0|f)==c[r+56>>2]?(t=vi(4),c[t>>2]=e,i=t+4|0,c[r+56>>2]=i,c[r+52>>2]=i,c[r+48>>2]=t,f&&er(f)):(c[f>>2]=e,c[r+52>>2]=f+4),b=((i=e+1|0)>>>0)%3|0?i:e-2|0,i=c[c[r+4>>2]+28>>2],-1==(0|(k=c[(b<<2)+i>>2])))return 0;if(f=(e-y(n,3)|0?-1:2)+e|0,-1==(0|(u=c[i+(f<<2)>>2])))return 0;if(e=c[r+36>>2],(t=c[(n=e+(k>>>3&536870908)|0)>>2])&(i=1<<k)||(c[n>>2]=i|t,mr(r+8|0,k,b),e=c[r+36>>2]),(i=c[(t=(u>>>3&536870908)+e|0)>>2])&(e=1<<u)||(c[t>>2]=e|i,mr(r+8|0,u,f)),(0|(a=c[r+52>>2]))==c[r+48>>2])return 1;for(k=r+8|0;;){r:{e:if(-1!=(0|(e=c[(a=a-4|0)>>2]))&&(i=(e>>>0)/3|0,n=c[r+24>>2]+(i>>>3&268435452)|0,!((t=c[n>>2])&(i=1<<i)))){if(c[n>>2]=i|t,A=c[r+4>>2],-1==(0|(i=c[c[A+28>>2]+(e<<2)>>2])))return 0;for(;;){t=e;i:{if(u=c[r+36>>2]+(i>>>3&536870908)|0,!((b=c[u>>2])&(f=1<<i)))if(-1==(0|(n=c[c[A+40>>2]+(i<<2)>>2]))||-1==(0|(e=((e=n+1|0)>>>0)%3|0?e:n-2|0))|c[c[A>>2]+(e>>>3&536870908)>>2]>>>e&1||-1==(0|(n=c[c[c[A+64>>2]+12>>2]+(e<<2)>>2])))c[u>>2]=f|b,mr(k,i,t),A=c[r+4>>2];else if(c[u>>2]=f|b,mr(k,i,t),A=c[r+4>>2],-1!=(0|(((e=n+1|0)>>>0)%3|0?e:n-2|0))){e=-1,-1!=(0|t)&&(-1==(0|(i=((i=t+1|0)>>>0)%3|0?i:t-2|0))|c[c[A>>2]+(i>>>3&536870908)>>2]>>>i&1||(e=c[c[c[A+64>>2]+12>>2]+(i<<2)>>2])),t=1<<(i=(e>>>0)/3|0),a=c[r+24>>2],u=c[a+((f=i>>>5|0)<<2)>>2];break i}t:{if(-1!=(0|t)){i=-1,-1==(0|(e=((e=t+1|0)>>>0)%3|0?e:t-2|0))|c[c[A>>2]+(e>>>3&536870908)>>2]>>>e&1||(i=c[c[c[A+64>>2]+12>>2]+(e<<2)>>2]);f:{if((t>>>0)%3|0)a=t-1|0;else if(e=-1,-1==(0|(a=t+2|0)))break f;e=-1,c[c[A>>2]+(a>>>3&536870908)>>2]>>>a&1||(e=c[c[c[A+64>>2]+12>>2]+(a<<2)>>2])}if(b=(n=-1==(0|e))?-1:(e>>>0)/3|0,-1!=(0|i)&&(a=c[r+24>>2],!((u=c[a+((f=(t=(i>>>0)/3|0)>>>5|0)<<2)>>2])&(t=1<<t))))break t;if(!(n||(t=1<<b,a=c[r+24>>2],t&(u=c[a+((f=b>>>5|0)<<2)>>2]))))break i}a=c[r+52>>2]-4|0,c[r+52>>2]=a;break r}if(n)e=i;else if(c[(b>>>3&536870908)+a>>2]>>>b&1)e=i;else{if(A=c[r+52>>2],c[A-4>>2]=e,c[r+56>>2]!=(0|A)){c[A>>2]=i,a=A+4|0;break e}t:{if((t=(n=(f=A-(b=c[r+48>>2])|0)>>2)+1|0)>>>0<1073741824){if(e=f>>>1|0,f=f>>>0>=2147483644?1073741823:e>>>0>t>>>0?e:t){if(f>>>0>=1073741824)break t;t=vi(f<<2)}else t=0;if(c[(e=t+(n<<2)|0)>>2]=i,a=e+4|0,(0|A)!=(0|b))for(;A=A-4|0,c[(e=e-4|0)>>2]=c[A>>2],(0|A)!=(0|b););if(c[r+56>>2]=t+(f<<2),c[r+52>>2]=a,c[r+48>>2]=e,!b)break r;er(b),a=c[r+52>>2];break r}mt(),o()}Zi(),o()}}if(c[(f<<2)+a>>2]=t|u,-1==(0|(i=c[c[A+28>>2]+(e<<2)>>2])))break}return 0}c[r+52>>2]=a}if(c[r+48>>2]==(0|a))break}}return 1}function tr(r,e,i,t){var f,a=0,A=0,b=0,u=0,k=d(0),_=0,l=0,y=d(0);f=c[i>>2];r:{e:if(A=c[e+4>>2]){i:{if((b=Ii(A))>>>0>=2){if(A>>>0<=(a=f)>>>0&&(a=(f>>>0)%(A>>>0)|0),!(i=c[c[e>>2]+(a<<2)>>2]))break e;if(b>>>0<=1)break i;for(;;){if(!(i=c[i>>2]))break e;if((0|(b=c[i+4>>2]))!=(0|f)&&(A>>>0<=b>>>0&&(b=(b>>>0)%(A>>>0)|0),(0|a)!=(0|b)))break e;if(c[i+8>>2]==(0|f))break}e=0;break r}if(a=A-1&f,!(i=c[c[e>>2]+(a<<2)>>2]))break e}for(u=A-1|0;;){if(!(i=c[i>>2]))break e;if((0|(b=c[i+4>>2]))!=(0|f)&(b&u)!=(0|a))break e;if(c[i+8>>2]==(0|f))break}e=0;break r}if(i=vi(16),t=c[c[t>>2]>>2],c[i+12>>2]=0,c[i+8>>2]=t,c[i+4>>2]=f,c[i>>2]=0,y=d(c[e+12>>2]+1>>>0),k=p[e+16>>2],y>d(k*d(A>>>0))||!A){a=2,1!=(0|(t=(t=0!=(A-1&A)|A>>>0<3|A<<1)>>>0>(b=(k=d(T(d(y/k))))<d(4294967296)&k>=d(0)?~~k>>>0:0)>>>0?t:b))&&(t&t-1?(a=z(t),A=c[e+4>>2]):a=t);e:{if(a>>>0<=A>>>0){if(a>>>0>=A>>>0)break e;if(b=A>>>0<3,t=(k=d(T(d(d(s[e+12>>2])/p[e+16>>2]))))<d(4294967296)&k>=d(0)?~~k>>>0:0,A>>>0<=(a=(t=b||Ii(A)>>>0>1?z(t):t>>>0<2?t:1<<32-v(t-1|0))>>>0<a>>>0?a:t)>>>0)break e}A=0,b=0,u=a;i:{t:{f:{a:{if(a){if(u>>>0>=1073741824)break a;if(t=vi(u<<2),a=c[e>>2],c[e>>2]=t,a&&er(a),c[e+4>>2]=u,t=0,u>>>0>=4)for(a=-4&u;c[(_=t<<2)+c[e>>2]>>2]=0,c[c[e>>2]+(4|_)>>2]=0,c[c[e>>2]+(8|_)>>2]=0,c[c[e>>2]+(12|_)>>2]=0,t=t+4|0,(0|a)!=(0|(b=b+4|0)););if(a=3&u)for(;c[c[e>>2]+(t<<2)>>2]=0,t=t+1|0,(0|a)!=(0|(A=A+1|0)););if(!(a=c[e+8>>2]))break i;if(t=e+8|0,A=c[a+4>>2],(b=Ii(u))>>>0<2)break f;if(A=A>>>0>=u>>>0?(A>>>0)%(u>>>0)|0:A,c[c[e>>2]+(A<<2)>>2]=t,!(t=c[a>>2]))break i;if(b>>>0<=1)break t;for(;u>>>0<=(b=c[t+4>>2])>>>0&&(b=(b>>>0)%(u>>>0)|0),(0|A)!=(0|b)?(_=(l=b<<2)+c[e>>2]|0,c[_>>2]?(c[a>>2]=c[t>>2],c[t>>2]=c[c[l+c[e>>2]>>2]>>2],c[c[l+c[e>>2]>>2]>>2]=t):(c[_>>2]=a,a=t,A=b)):a=t,t=c[a>>2];);break i}t=c[e>>2],c[e>>2]=0,t&&er(t),c[e+4>>2]=0;break i}Zi(),o()}if(A&=u-1,c[c[e>>2]+(A<<2)>>2]=t,!(t=c[a>>2]))break i}for(_=u-1|0;(0|(b=_&c[t+4>>2]))!=(0|A)?(u=(l=b<<2)+c[e>>2]|0,c[u>>2]?(c[a>>2]=c[t>>2],c[t>>2]=c[c[l+c[e>>2]>>2]>>2],c[c[l+c[e>>2]>>2]>>2]=t):(c[u>>2]=a,a=t,A=b)):a=t,t=c[a>>2];);}}a=(t=(A=c[e+4>>2])-1|0)&A?A>>>0>f>>>0?f:(f>>>0)%(A>>>0)|0:t&f}a=c[e>>2]+(a<<2)|0;e:{if(t=c[a>>2])c[i>>2]=c[t>>2];else{if(t=e+8|0,c[i>>2]=c[t>>2],c[e+8>>2]=i,c[a>>2]=t,!(t=c[i>>2]))break e;t=c[t+4>>2],(a=A-1|0)&A?t>>>0<A>>>0||(t=(t>>>0)%(A>>>0)|0):t&=a,t=c[e>>2]+(t<<2)|0}c[t>>2]=i}c[e+12>>2]=c[e+12>>2]+1,e=1}n[r+4|0]=e,c[r>>2]=i}function fr(r,e,i){var t,f=0,a=0,n=0,A=0,b=0,u=0,k=0,_=0,s=0,p=0,l=0,d=0;t=y(e,12)+r|0,c[t+12>>2]=c[t+8>>2],s=-1==(0|i)?-1:(i>>>0)/3|0,f=1,k=i;r:{e:{i:{for(;;){t:{if(_=f,!f){if(-1==(0|k))break t;if(-1==(0|zr(r,((k>>>0)%3|0?-1:2)+k|0)))break r;if(-1==(0|(f=((i=k+1|0)>>>0)%3|0?i:k-2|0)))break r;if(-1==(0|(i=((i=f+1|0)>>>0)%3|0?i:f-2|0)))break r;if(-1==(0|(f=c[c[c[r+4>>2]+12>>2]+(i<<2)>>2])))break r;if(-1==(0|(i=((i=f+1|0)>>>0)%3|0?i:f-2|0)))break r;s=(i>>>0)/3|0}if(f=c[r+56>>2]+(s>>>3&536870908)|0,!((b=c[f>>2])&(a=1<<s))){for(n=0;;){if(c[f>>2]=a|b,(0|(f=c[t+12>>2]))==c[t+16>>2]){if((u=(a=(b=f-(p=c[t+8>>2])|0)>>2)+1|0)>>>0>=1073741824)break i;if(A=b>>>1|0,u=b>>>0>=2147483644?1073741823:u>>>0<A>>>0?A:u){if(u>>>0>=1073741824)break e;A=vi(u<<2)}else A=0;if(c[(b=A+(a<<2)|0)>>2]=s,a=b+4|0,(0|f)!=(0|p))for(;f=f-4|0,c[(b=b-4|0)>>2]=c[f>>2],(0|f)!=(0|p););c[t+8>>2]=b,c[t+12>>2]=a,c[t+16>>2]=A+(u<<2),p&&er(p)}else c[f>>2]=s,c[t+12>>2]=f+4;A=n+1|0;f:{a:{if(n)if(1&A){if(-1==(0|i)){i=-1;break f}i=((f=i+1|0)>>>0)%3|0?f:i-2|0}else{if(k=_?k:i,-1==(0|i)){i=-1;break f}if((i>>>0)%3|0){f=i-1|0;break a}i=i+2|0}if(f=i,i=-1,-1==(0|f))break f}if(i=c[c[c[r+4>>2]+12>>2]+(f<<2)>>2],b=-1,n=-1,(0|(a=((a=f+1|0)>>>0)%3|0?a:f-2|0))>=0&&(n=(a>>>0)/3|0,n=c[(c[c[r>>2]+96>>2]+y(n,12)|0)+(a-y(n,3)<<2)>>2]),-1!=(0|i)&&((0|(u=((i>>>0)%3|0?-1:2)+i|0))<0||(a=(u>>>0)/3|0,b=c[(c[c[r>>2]+96>>2]+y(a,12)|0)+(u-y(a,3)<<2)>>2])),(0|n)==(0|b)){a:{n:{if((0|(n=((f>>>0)%3|0?-1:2)+f|0))>=0){if(f=(n>>>0)/3|0,-1!=(0|i))break n;i=-1;break f}if(f=-1,-1!=(0|i))break a;i=-1;break f}f=c[(c[c[r>>2]+96>>2]+y(f,12)|0)+(n-y(f,3)<<2)>>2]}if((0|(a=((n=i+1|0)>>>0)%3|0?n:i-2|0))>=0?(n=(a>>>0)/3|0,n=c[(c[c[r>>2]+96>>2]+y(n,12)|0)+(a-y(n,3)<<2)>>2]):n=-1,(0|n)==(0|f)){if(n=A,s=(i>>>0)/3|0,f=c[r+56>>2]+(s>>>3&268435452)|0,!((b=c[f>>2])&(a=1<<s)))continue}else i=-1}else i=-1}break}if(!(_|!(1&A))){_=c[t+12>>2]-4|0,A=c[_>>2],f=c[r+56>>2]+(A>>>3&536870908)|0,i=c[f>>2],l=f,d=Yi(A)&i,c[l>>2]=d,c[t+12>>2]=_;break r}}if(f=0,_)continue;break r}break}k=-1,zr(r,-1);break r}mt(),o()}Zi(),o()}if(c[44+((e<<2)+r|0)>>2]=k,(0|(e=c[t+12>>2]))!=(0|(u=c[t+8>>2]))){if(k=1&(e=(e=(i=e-u|0)>>2)>>>0<=1?1:e),a=c[r+56>>2],f=0,i>>>0>=8)for(n=-2&e,i=0;A=c[(_=f<<2)+u>>2],r=c[(e=a+(A>>>3&536870908)|0)>>2],l=e,d=Yi(A)&r,c[l>>2]=d,A=c[u+(4|_)>>2],r=c[(e=a+(A>>>3&536870908)|0)>>2],l=e,d=Yi(A)&r,c[l>>2]=d,f=f+2|0,(0|n)!=(0|(i=i+2|0)););k&&(i=c[u+(f<<2)>>2],r=c[(e=a+(i>>>3&536870908)|0)>>2],l=e,d=Yi(i)&r,c[l>>2]=d)}}function ar(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,b=0,u=0;if(-1==(0|e))return 1;if(n=(e>>>0)/3|0,!(c[c[r+24>>2]+(n>>>3&268435452)>>2]>>>n&1)){if(a=c[r+48>>2],c[r+52>>2]=a,(0|a)==c[r+56>>2]?(t=vi(4),c[t>>2]=e,i=t+4|0,c[r+56>>2]=i,c[r+52>>2]=i,c[r+48>>2]=t,a&&er(a)):(c[a>>2]=e,c[r+52>>2]=a+4),f=-1,t=c[r+4>>2],-1!=(0|(b=((i=e+1|0)>>>0)%3|0?i:e-2|0))&&(f=c[c[t>>2]+(b<<2)>>2]),A=e-y(n,3)|0)i=e-1|0;else if(-1==(0|(i=e+2|0)))return 0;if(-1==(0|f))return 0;if(-1==(0|(u=c[c[t>>2]+(i<<2)>>2])))return 0;if(i=c[r+36>>2],(n=c[(a=i+(f>>>3&536870908)|0)>>2])&(t=1<<f)||(c[a>>2]=t|n,mr(r+8|0,f,b),i=c[r+36>>2]),(t=c[(n=(u>>>3&536870908)+i|0)>>2])&(i=1<<u)||(c[n>>2]=i|t,mr(r+8|0,u,(A?-1:2)+e|0)),(0|(i=c[r+52>>2]))==c[r+48>>2])return 1;for(u=r+8|0;;){r:{e:if(-1!=(0|(e=c[(i=i-4|0)>>2]))&&(t=(e>>>0)/3|0,a=c[r+24>>2]+(t>>>3&268435452)|0,!((n=c[a>>2])&(t=1<<t)))){for(c[a>>2]=t|n;;){if(b=c[r+4>>2],-1==(0|(f=c[c[b>>2]+(e<<2)>>2])))return 0;i:{if(A=c[r+36>>2]+(f>>>3&536870908)|0,!((a=c[A>>2])&(n=1<<f)))if(-1!=(0|(t=c[c[b+24>>2]+(f<<2)>>2]))&&-1!=(0|(i=((i=t+1|0)>>>0)%3|0?i:t-2|0))&&-1!=(0|(t=c[c[b+12>>2]+(i<<2)>>2]))){if(c[A>>2]=a|n,mr(u,f,e),-1!=(0|(((i=t+1|0)>>>0)%3|0?i:t-2|0))){i=e-2|0,t=e+1|0,e=-1,-1!=(0|(i=(t>>>0)%3|0?t:i))&&(e=c[c[c[r+4>>2]+12>>2]+(i<<2)>>2]),t=1<<(i=(e>>>0)/3|0),f=c[r+24>>2],b=c[f+((a=i>>>5|0)<<2)>>2];break i}}else c[A>>2]=a|n,mr(u,f,e);i=-1,n=c[r+4>>2],-1!=(0|(t=((t=e+1|0)>>>0)%3|0?t:e-2|0))&&(i=c[c[n+12>>2]+(t<<2)>>2]);t:{if((e>>>0)%3|0)f=e-1|0;else if(f=e+2|0,e=-1,-1==(0|f))break t;e=c[c[n+12>>2]+(f<<2)>>2]}if(A=(n=-1==(0|e))?-1:(e>>>0)/3|0,-1==(0|i)||(f=c[r+24>>2],(b=c[f+((a=(t=(i>>>0)/3|0)>>>5|0)<<2)>>2])&(t=1<<t))){if(!(n||(t=1<<A,f=c[r+24>>2],t&(b=c[f+((a=A>>>5|0)<<2)>>2]))))break i;i=c[r+52>>2]-4|0,c[r+52>>2]=i;break r}if(n)e=i;else if(c[(A>>>3&536870908)+f>>2]>>>A&1)e=i;else{if(f=c[r+52>>2],c[f-4>>2]=e,c[r+56>>2]!=(0|f)){c[f>>2]=i,i=f+4|0;break e}t:{if((t=(n=(a=f-(A=c[r+48>>2])|0)>>2)+1|0)>>>0<1073741824){if(e=a>>>1|0,a=a>>>0>=2147483644?1073741823:e>>>0>t>>>0?e:t){if(a>>>0>=1073741824)break t;t=vi(a<<2)}else t=0;if(c[(e=t+(n<<2)|0)>>2]=i,i=e+4|0,(0|f)!=(0|A))for(;f=f-4|0,c[(e=e-4|0)>>2]=c[f>>2],(0|f)!=(0|A););if(c[r+56>>2]=t+(a<<2),c[r+52>>2]=i,c[r+48>>2]=e,!A)break r;er(A),i=c[r+52>>2];break r}mt(),o()}Zi(),o()}}if(c[(a<<2)+f>>2]=t|b,-1==(0|e))break}return 0}c[r+52>>2]=i}if(c[r+48>>2]==(0|i))break}}return 1}function nr(r,e){var i,t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0,_=0,s=0;Z=i=Z-32|0;r:{if(!(c[r+8>>2]<<5>>>0>=e>>>0)){if((0|e)<0)break r;t=vi((e=1+(e-1>>>5|0)|0)<<2),c[i+24>>2]=e,c[i+20>>2]=0,c[i+16>>2]=t,e=c[r>>2],c[i+12>>2]=0,c[i+8>>2]=e,t=c[r+4>>2],c[i+4>>2]=31&t,c[i>>2]=e+(t>>>3&536870908),Z=a=Z-32|0,t=(e=((b=c[i+4>>2])-(A=c[i+12>>2])|0)+((u=c[i>>2])-(f=c[i+8>>2])<<3)|0)+(n=c[i+20>>2])|0,c[i+20>>2]=t,(t-1^n-1)>>>0<32&&n||(c[c[i+16>>2]+((t>>>0>=33?t-1>>>5|0:0)<<2)>>2]=0),t=c[i+16>>2]+(n>>>3&536870908)|0;e:if((0|(n&=31))!=(0|A)){if(c[a+28>>2]=A,c[a+24>>2]=f,c[a+20>>2]=b,c[a+16>>2]=u,c[a+12>>2]=n,c[a+8>>2]=t,e=c[a+28>>2],t=c[a+24>>2],(0|(A=(c[a+20>>2]-e|0)+(c[a+16>>2]-t<<3)|0))<=0)e=c[a+12>>2],f=c[a+8>>2];else{if(e?(b=(n=(0|A)<(0|(k=32-e|0))?A:k)>>>0>(u=32-(f=c[a+12>>2])|0)>>>0?u:n,_=c[a+8>>2],s=c[_>>2]&(-1<<f&-1>>>u-b^-1),u=c[t>>2]&-1<<e&-1>>>k-n,c[_>>2]=s|(e>>>0<f>>>0?u<<f-e:u>>>e-f|0),e=31&(t=f+b|0),c[a+12>>2]=e,f=_+(t>>>3&536870908)|0,c[a+8>>2]=f,(0|(t=n-b|0))>0&&(c[f>>2]=c[f>>2]&(-1>>>32-t^-1)|u>>>b+c[a+28>>2],c[a+12>>2]=t,e=t),A=A-n|0,t=c[a+24>>2]+4|0,c[a+24>>2]=t):e=c[a+12>>2],b=-1<<e,n=32-e|0,(0|A)>=32)for(u=-1^b;f=c[a+8>>2],t=c[t>>2],c[f>>2]=u&c[f>>2]|t<<e,c[a+8>>2]=f+4,c[f+4>>2]=b&c[f+4>>2]|t>>>n,t=c[a+24>>2]+4|0,c[a+24>>2]=t,f=A>>>0>63,A=A-32|0,f;);f=c[a+8>>2],(0|A)<=0||(u=n,n=(0|A)>(0|n)?n:A,u=c[f>>2]&(b&-1>>>u-n^-1),b=c[t>>2]&-1>>>32-A,c[f>>2]=u|b<<e,t=31&(e=e+n|0),c[a+12>>2]=t,f=(e>>>3&536870908)+f|0,c[a+8>>2]=f,(0|(e=A-n|0))<=0?e=t:(c[f>>2]=c[f>>2]&(-1>>>32-e^-1)|b>>>n,c[a+12>>2]=e))}c[a+4>>2]=e,c[a>>2]=f}else{if((0|e)<=0)break e;if(A&&(b=-1<<A&-1>>>(b=32-A|0)-(n=(0|e)<(0|b)?e:b),c[t>>2]=c[t>>2]&(-1^b)|b&c[f>>2],f=f+4|0,t=(A+n>>>3&536870908)+t|0,e=e-n|0),A=(0|e)/32|0,e+31>>>0>=63&&gr(t,f,A<<2),(0|(e=e-(A<<5)|0))<=0)break e;e=-1>>>32-e|0,c[(A=(n=t)+(t=A<<2)|0)>>2]=c[A>>2]&(-1^e)|e&c[t+f>>2]}Z=a+32|0,e=c[r>>2],c[r>>2]=c[i+16>>2],c[i+16>>2]=e,t=c[r+4>>2],c[r+4>>2]=c[i+20>>2],c[i+20>>2]=t,t=c[r+8>>2],c[r+8>>2]=c[i+24>>2],c[i+24>>2]=t,e&&er(e)}return void(Z=i+32|0)}mt(),o()}function Ar(r,e,i){e|=0,i|=0;var t,f=0,a=0,A=0,o=0,b=0,u=0,_=0,s=0,p=0,l=0,d=0;r:if(!((0|(t=0|Zt[c[c[(r|=0)>>2]+44>>2]](r)))<=0)&&(u=c[e+4>>2]-c[e>>2]>>2,Z=a=Z+-64|0,A=ai(a),f=y(c[2541],t),yi(A,c[c[r+8>>2]+56>>2],255&t,5,0,f,f>>31),A=ee(vi(96),A),n[A+84|0]=1,c[A+72>>2]=c[A+68>>2],ie(A,u),c[A+60>>2]=c[c[r+8>>2]+60>>2],f=c[r+16>>2],c[r+16>>2]=A,f&&xe(f),Z=a- -64|0,b=c[r+16>>2],c[b+80>>2]&&(_=c[c[b>>2]>>2])&&!((0|(a=l=c[i+12>>2]))<=(0|(f=c[i+20>>2]))&(o=c[i+8>>2])>>>0<=(s=c[i+16>>2])>>>0|(0|f)>(0|a)))){p=y(u,t),u=_+c[b+48>>2]|0,b=c[i>>2],_=k[b+s|0],A=(a=s+1|0)?f:f+1|0,c[i+16>>2]=a,c[i+20>>2]=A;e:{i:{if(_){if(F(p,t,i,u))break i;break r}if((0|A)>=(0|l)&a>>>0>=o>>>0|(0|A)>(0|l))break r;if(o=k[a+b|0],f=(A=s+2|0)>>>0<2?f+1|0:f,c[i+16>>2]=A,c[i+20>>2]=f,f=c[c[r+16>>2]+64>>2],f=c[f+4>>2]-c[f>>2]|0,(0|o)!=c[2541]){if(f>>>0<y(o,p)>>>0)break r;if(a=(f=c[i+8>>2])-(A=c[i+16>>2])|0,l=f>>>0<A>>>0,f=c[i+20>>2],s=c[i+12>>2]-(l+f|0)|0,(l=$e(o,0,p,0)>>>0>a>>>0)&(0|(a=E))>=(0|s)|(0|a)>(0|s))break r;if(a=1,!p)break e;for(b=0;;){if((a=A+o|0)>>>0>(s=c[i+8>>2])>>>0&(0|(f=a>>>0<o>>>0?f+1|0:f))>=(0|(_=c[i+12>>2]))|(0|f)>(0|_))return 0;if(hr(u+(b<<2)|0,c[i>>2]+A|0,o),f=c[i+20>>2],f=(A=o+c[i+16>>2]|0)>>>0<o>>>0?f+1|0:f,c[i+16>>2]=A,c[i+20>>2]=f,(0|p)==(0|(b=b+1|0)))break}}else{if((a=p<<2)>>>0>f>>>0)break r;if(o=c[i+8>>2],s=c[i+12>>2],_=c[i+20>>2],(A=a+(f=c[i+16>>2])|0)>>>0>o>>>0&(0|(_=A>>>0<a>>>0?_+1|0:_))>=(0|s)|(0|_)>(0|s))break r;hr(u,f+c[i>>2]|0,a),A=c[i+20>>2],A=(f=a+c[i+16>>2]|0)>>>0<a>>>0?A+1|0:A,c[i+16>>2]=f,c[i+20>>2]=A}}if(a=1,p&&!((f=c[r+20>>2])&&(a=0,0|Zt[c[c[f>>2]+32>>2]](f)))){if(o=0,b=0,!((0|p)<=0)){if(1!=(0|p))for(A=-2&p;f=c[(a=o<<2)+u>>2],c[a+u>>2]=0-(1&f)^f>>>1,a=c[(f=4|a)+u>>2],c[f+u>>2]=0-(1&a)^a>>>1,o=o+2|0,(0|A)!=(0|(b=b+2|0)););1&p&&(A=c[(f=o<<2)+u>>2],c[f+u>>2]=0-(1&A)^A>>>1)}a=0}}if(f=a,A=c[r+20>>2]){if(!(0|Zt[c[c[A>>2]+40>>2]](A,i)))break r;if(!(f||(r=c[r+20>>2],0|Zt[c[c[r>>2]+44>>2]](r,u,u,p,t,c[e>>2]))))break r}d=1}return 0|d}function or(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,o=0,b=0,u=0,s=0,p=0,l=0,y=0;r:if(_[e+38>>1]&&Ze(1,r+12|0,e)&&!((n=(t=c[e+8>>2])-(f=c[e+16>>2])|0)>>>0<(a=c[r+12>>2])>>>6>>>0&(0|(t=c[e+12>>2]-(c[e+20>>2]+(t>>>0<f>>>0)|0)|0))<=0|(0|t)<0)){if(t=c[r>>2],(i=c[r+4>>2]-t>>2)>>>0<a>>>0?(_e(r,a-i|0),a=c[r+12>>2]):i>>>0<=a>>>0||(c[r+4>>2]=t+(a<<2)),!a)return 1;for(t=c[e+16>>2],i=c[e+20>>2],s=c[r>>2],b=c[e+8>>2],o=c[e+12>>2],n=0;;){if((0|i)>=(0|o)&t>>>0>=b>>>0|(0|i)>(0|o))return 0;p=c[e>>2],u=k[p+t|0],i=(t=t+1|0)?i:i+1|0,c[e+16>>2]=t,c[e+20>>2]=i,f=u>>>2|0,A=0;e:{i:{t:{f:switch(0|(l=3&u)){case 3:break f;case 0:break i;default:break t}if((f=f+n|0)>>>0>=a>>>0)return 0;Sr(s+(n<<2)|0,0,4+(252&u)|0),n=f;break e}for(;;){if((0|t)==(0|b)&(0|i)==(0|o))break r;if(a=k[t+p|0],i=(t=t+1|0)?i:i+1|0,c[e+16>>2]=t,c[e+20>>2]=i,f|=a<<(A<<3|6),(0|l)==(0|(A=A+1|0)))break}}c[s+(n<<2)>>2]=f}if(!((a=c[r+12>>2])>>>0>(n=n+1|0)>>>0))break}if(e=r+16|0,b=c[r>>2],t=c[r+16>>2],(i=c[r+20>>2]-t|0)>>>0<=16383?_e(e,4096-(i>>>2|0)|0):16384!=(0|i)&&(c[r+20>>2]=t+16384),n=c[(i=r+28|0)>>2],(t=c[r+32>>2]-n>>3)>>>0<a>>>0)ye(i,a-t|0),n=c[i>>2];else if(t>>>0>a>>>0&&(c[r+32>>2]=(a<<3)+n),!a)break r;for(t=c[e>>2],e=0,r=0;;){if(A=c[(i=b+(e<<2)|0)>>2],f=r,c[(o=(e<<3)+n|0)+4>>2]=r,c[o>>2]=A,(r=(i=c[i>>2])+r|0)>>>0>4096)break r;if(!(r>>>0<=f>>>0)){if(A=0,o=7&i)for(;c[t+(f<<2)>>2]=e,f=f+1|0,(0|o)!=(0|(A=A+1|0)););if(!(i-1>>>0<=6))for(;c[(i=t+(f<<2)|0)>>2]=e,c[i+28>>2]=e,c[i+24>>2]=e,c[i+20>>2]=e,c[i+16>>2]=e,c[i+12>>2]=e,c[i+8>>2]=e,c[i+4>>2]=e,(0|(f=f+8|0))!=(0|r););}if((0|a)==(0|(e=e+1|0)))break}y=4096==(0|r)}return y}function br(r,e){r|=0;var i,t=0,f=0,a=0,n=0,A=0,o=0,b=0,u=0,_=0,s=0,p=0,l=0,d=0,m=0;Z=i=Z-80|0;r:if(Ge(1,i+76|0,e|=0)&&(u=c[i+76>>2])&&!((t=$e((t=c[e+8>>2])-(a=c[e+16>>2])|0,c[e+12>>2]-(c[e+20>>2]+(t>>>0<a>>>0)|0)|0,5,0))>>>0<u>>>0&(0|(a=E))<=0|(0|a)<0)){for(t=c[r+4>>2],(f=c[r+8>>2]-t>>2)>>>0<u>>>0?_e(r+4|0,u-f|0):f>>>0<=u>>>0||(c[r+8>>2]=t+(u<<2)),d=r+16|0,_=c[r+32>>2];;){if((0|(t=n=c[e+12>>2]))<=(0|(f=c[e+20>>2]))&(A=c[e+8>>2])>>>0<=(a=c[e+16>>2])>>>0|(0|t)<(0|f)){f=0;break r}if(s=c[e>>2],m=k[s+a|0],t=f,t=(o=a+1|0)?t:t+1|0,c[e+16>>2]=o,c[e+20>>2]=t,A>>>0<=o>>>0&(0|t)>=(0|n)|(0|t)>(0|n)){f=0;break r}if(o=k[o+s|0],t=f,t=(b=a+2|0)>>>0<2?t+1|0:t,c[e+16>>2]=b,c[e+20>>2]=t,A>>>0<=b>>>0&(0|t)>=(0|n)|(0|t)>(0|n)){f=0;break r}if(b=k[b+s|0],t=f,t=(p=a+3|0)>>>0<3?t+1|0:t,c[e+16>>2]=p,c[e+20>>2]=t,A>>>0<=p>>>0&(0|t)>=(0|n)|(0|t)>(0|n)){f=0;break r}if(A=k[s+p|0],t=f,t=(f=a+4|0)>>>0<4?t+1|0:t,c[e+16>>2]=f,c[e+20>>2]=t,m>>>0>4){f=0;break r}if((o-12&255)>>>0<245){f=0;break r}if(!b){f=0;break r}if(t=ai(i+8|0),n=0!=(0|A),f=(f=o-1|0)>>>0<=10?c[10148+(f<<2)>>2]:-1,yi(t,m,b,o,n,f=y(f,b),f>>31),Ge(1,i+4|0,e)){if(a=c[i+4>>2],c[i+68>>2]=a,f=ee(vi(96),t),Zt[c[c[_>>2]+8>>2]](_,c[_+12>>2]-c[_+8>>2]>>2,f),f=(c[_+12>>2]-c[_+8>>2]>>2)-1|0,c[c[(A=f<<2)+c[_+8>>2]>>2]+60>>2]=a,c[c[r+4>>2]+(l<<2)>>2]=f,t=c[r+16>>2],(0|(a=c[r+20>>2]-t>>2))>(0|f)||(c[i>>2]=-1,(f=f+1|0)>>>0>a>>>0?(Nr(d,f-a|0,i),t=c[d>>2]):f>>>0>=a>>>0||(c[r+20>>2]=(f<<2)+t)),c[t+A>>2]=l,f=1,(0|(l=l+1|0))!=(0|u))continue;break r}break}f=0}return Z=i+80|0,0|f}function ur(r,e,i){r|=0,e|=0,i|=0;var t,f=0,a=0,n=0,A=0,b=0,u=0,k=0,_=0,s=0;Z=t=Z-16|0,c[t+8>>2]=i;r:if(!((0|(A=(b=c[r+12>>2])-(f=c[r+8>>2])>>2))>(0|e)))if((a=e+1|0)>>>0>A>>>0){if((_=a-A|0)>>>0<=(n=c[r+16>>2])-(f=c[r+12>>2])>>2>>>0){_&&(f=Sr(a=f,0,f=_<<2)+f|0),c[r+12>>2]=f;break r}e:{i:{t:{if((u=(A=f-(s=c[r+8>>2])>>2)+_|0)>>>0<1073741824){if(n=(a=n-s|0)>>>1|0,a=a>>>0>=2147483644?1073741823:n>>>0>u>>>0?n:u){if(a>>>0>=1073741824)break t;k=vi(a<<2)}if(A=(n=_<<2)+(u=Sr(b=(A<<2)+k|0,0,n))|0,a=(a<<2)+k|0,(0|f)==(0|s))break i;for(;n=c[(f=f-4|0)>>2],c[f>>2]=0,c[(b=b-4|0)>>2]=n,(0|f)!=(0|s););if(c[r+16>>2]=a,a=c[r+12>>2],c[r+12>>2]=A,f=c[r+8>>2],c[r+8>>2]=b,(0|f)==(0|a))break e;for(;n=c[(a=a-4|0)>>2],c[a>>2]=0,n&&xe(n),(0|f)!=(0|a););break e}mt(),o()}Zi(),o()}c[r+16>>2]=a,c[r+12>>2]=A,c[r+8>>2]=u}f&&er(f)}else if(!(a>>>0>=A>>>0)){if((0|(f=f+(a<<2)|0))!=(0|b)){for(;i=c[(b=b-4|0)>>2],c[b>>2]=0,i&&xe(i),(0|f)!=(0|b););i=c[t+8>>2]}c[r+12>>2]=f}r:{e:{if(!((0|(f=c[i+56>>2]))>4))if(k=y(f,12)+r|0,(0|(f=c[k+24>>2]))==c[k+28>>2]){if((a=(n=(A=f-(u=c[k+20>>2])|0)>>2)+1|0)>>>0>=1073741824)break e;if(f=A>>>1|0,a=A>>>0>=2147483644?1073741823:f>>>0>a>>>0?f:a){if(a>>>0>=1073741824)break r;f=vi(a<<2)}else f=0;c[(n=f+(n<<2)|0)>>2]=e,f=gr(f,u,A),c[k+20>>2]=f,c[k+24>>2]=n+4,c[k+28>>2]=f+(a<<2),u&&er(u)}else c[f>>2]=e,c[k+24>>2]=f+4;return c[i+60>>2]=e,r=c[r+8>>2],c[t+8>>2]=0,e=c[(r=r+(e<<2)|0)>>2],c[r>>2]=i,e&&xe(e),r=c[t+8>>2],c[t+8>>2]=0,r&&xe(r),void(Z=t+16|0)}mt(),o()}Zi(),o()}function cr(r,e){var i,t,f=0,a=0,n=0,A=0,b=0,u=0,k=0,_=0,s=0,p=0;Z=i=Z-16|0,c[i>>2]=e,A=-1,-1!=(0|e)?(A=e+1|0,c[i+4>>2]=(A>>>0)%3|0?A:e-2|0,A=(e>>>0)%3|0?e-1|0:e+2|0):c[i+4>>2]=-1,c[i+8>>2]=A,t=(e>>>0)/3|0;r:{e:{i:{for(;;){t:if(-1!=(0|(k=c[(s<<2)+i>>2]))&&-1!=(0|(A=c[c[c[r+8>>2]+12>>2]+(k<<2)>>2]))){if(!(-1==(0|e)|(A>>>0)/3>>>0<t>>>0)&&(A=0,c[r+220>>2]!=c[r+216>>2]))for(;;){if(Se(c[r+368>>2]+(A<<4)|0))if(b=c[r+216>>2]+y(A,144)|0,(a=c[b+136>>2])>>>0<(f=c[b+140>>2])>>>0)c[a>>2]=k,c[b+136>>2]=a+4;else{if((u=(n=(_=(n=a)-(a=c[b+132>>2])|0)>>2)+1|0)>>>0>=1073741824)break r;if(p=n<<2,n=(f=f-a|0)>>>1|0,u=f>>>0>=2147483644?1073741823:u>>>0<n>>>0?n:u){if(u>>>0>=1073741824)break e;f=vi(u<<2)}else f=0;c[(n=p+f|0)>>2]=k,f=gr(f,a,_),c[b+132>>2]=f,c[b+136>>2]=n+4,c[b+140>>2]=f+(u<<2),a&&er(a)}if(!((A=A+1|0)>>>0<(c[r+220>>2]-c[r+216>>2]|0)/144>>>0))break}}else{if(A=0,(0|(b=c[r+216>>2]))==c[r+220>>2])break t;for(;;){if(b=y(A,144)+b|0,(a=c[b+136>>2])>>>0<(f=c[b+140>>2])>>>0)c[a>>2]=k,c[b+136>>2]=a+4;else{if((u=(n=(_=(n=a)-(a=c[b+132>>2])|0)>>2)+1|0)>>>0>=1073741824)break i;if(p=n<<2,n=(f=f-a|0)>>>1|0,u=f>>>0>=2147483644?1073741823:u>>>0<n>>>0?n:u){if(u>>>0>=1073741824)break e;f=vi(u<<2)}else f=0;c[(n=p+f|0)>>2]=k,f=gr(f,a,_),c[b+132>>2]=f,c[b+136>>2]=n+4,c[b+140>>2]=f+(u<<2),a&&er(a)}if(A=A+1|0,b=c[r+216>>2],!(A>>>0<(c[r+220>>2]-b|0)/144>>>0))break}}if(3==(0|(s=s+1|0)))break}return Z=i+16|0,1}mt(),o()}Zi(),o()}mt(),o()}function kr(r,e){var i,t=0,f=0,a=0,A=0,b=0,u=0,p=0,l=0,d=0,m=0;Z=i=Z-16|0,m=-1;r:{e:{i:if(Ie(1,i+12|0,e)){if(p=c[i+12>>2]){if(t=c[r+8>>2],(c[t+4>>2]-c[t>>2]>>2>>>0)/3>>>0<p>>>0)break i;for(;;){if(!Ie(1,i+8|0,e))break i;if(t=c[i+8>>2],!Ie(1,i+8|0,e))break i;if((b=t+b|0)>>>0<(t=c[i+8>>2])>>>0)break i;if(a=b-t|0,(0|(t=c[r+40>>2]))==c[r+44>>2]){if((A=(f=(0|(d=(f=t)-(t=c[r+36>>2])|0))/12|0)+1|0)>>>0>=357913942)break e;if(u=f<<1,A=f>>>0>=178956970?357913941:A>>>0<u>>>0?u:A){if(A>>>0>=357913942)break r;u=vi(y(A,12))}else u=0;f=u+y(f,12)|0,c[f+4>>2]=b,c[f>>2]=a,a=gr(f+y((0|d)/-12|0,12)|0,t,d),c[r+44>>2]=u+y(A,12),c[r+40>>2]=f+12,c[r+36>>2]=a,t&&er(t)}else c[t+4>>2]=b,c[t>>2]=a,c[r+40>>2]=t+12,p=c[i+12>>2];if(!((l=l+1|0)>>>0<p>>>0))break}if(b=0,bi(e,0,0),p)for(;;){t=k[e+36|0];t:{f:{if((65535&((f=_[c[r+4>>2]+36>>1])<<8|f>>>8))>>>0<=513){if(!t)break t;if(a=0,(t=(l=(f=c[e+32>>2])>>>3|0)+(A=c[e+24>>2])|0)>>>0>=(u=c[e+28>>2])>>>0?t=f:(a=k[0|t],t=f+1|0,c[e+32>>2]=t,l=t>>>3|0,a=a>>>(7&f)&1),u>>>0>A+l>>>0)break f;break t}if(!t)break t;if(a=0,t=c[e+32>>2],(f=c[e+24>>2]+(t>>>3|0)|0)>>>0>=s[e+28>>2])break t;a=k[0|f]>>>(7&t)&1}c[e+32>>2]=t+1}if(t=c[r+36>>2]+y(b,12)|0,n[t+8|0]=254&k[t+8|0]|1&a,(0|p)==(0|(b=b+1|0)))break}n[e+36|0]=0,f=c[e+20>>2],r=0,t=(a=(r=(a=c[e+32>>2]+7|0)>>>0<7?1:r)<<29|a>>>3)+c[e+16>>2]|0,r=(r>>>3|0)+f|0,c[e+16>>2]=t,c[e+20>>2]=t>>>0<a>>>0?r+1|0:r}m=c[e+16>>2]}return Z=i+16|0,m}mt(),o()}Zi(),o()}function _r(r,e,i,t){var f,a,n,A=0,o=0,b=0,u=0,k=0,_=0,s=0;if(f=c[e+16>>2],u=c[i+4>>2]-f|0,A=c[i>>2]-f|0,c[i>>2]=A,o=u,c[i+4>>2]=o,n=(a=c[e+16>>2])>>>0>=(b=(u^(o>>=31))-o|0)+(((o=A>>31)^A)-o|0)>>>0)o=u;else{r:{e:{if((0|A)>=0){if(b=1,_=1,(0|u)>=0)break r;if(k=1,b=-1,_=-1,A)break e;break r}if(k=-1,b=-1,_=-1,(0|u)<=0)break r}b=(0|u)<=0?-1:1,_=k}o=(A<<1)-(s=y(_,a))|0,o=(((k=(0|y(b,_))>=0)?0-o|0:o)+(A=y(b,a))|0)/2|0,c[i+4>>2]=o,A=(u<<1)-A|0,A=((k?0-A|0:A)+s|0)/2|0,c[i>>2]=A}r:{e:{i:{t:{f:{a:{n:{if(A){if((0|A)<0)break n;if((0|o)>=0)break a;break i}if(o)break f;_=1,b=0,o=0,k=0;break r}if(_=1,(0|o)>0)break t;k=(0|o)>0?3:0,b=o,o=A;break r}b=0-o|0,o=0-A|0,k=2;break e}if((0|o)<=0)break i}o=0-o|0,b=A,k=3;break e}b=0-A|0,k=1}c[i>>2]=o,c[i+4>>2]=b,_=0}(0|(A=c[t>>2]+o|0))>(0|(u=c[e+16>>2]))?A=A-c[e+4>>2]|0:(0-u|0)<=(0|A)||(A=c[e+4>>2]+A|0),(0|u)<(0|(i=c[t+4>>2]+b|0))?i=i-c[e+4>>2]|0:(0-u|0)<=(0|i)||(i=c[e+4>>2]+i|0);r:if(_)e=i;else{e=i;e:{i:{t:switch(((t=4-k|0)>>>0<4?t:0-k|0)-1|0){case 2:break e;case 1:break i;case 0:break t;default:break r}e=0-A|0,A=i;break r}e=0-i|0,A=0-A|0;break r}e=A,A=0-i|0}if(n)i=e;else{r:{e:{if((0|A)>=0){if(i=1,o=1,(0|e)>=0)break r;if(t=1,i=-1,o=-1,A)break e;break r}if(t=-1,i=-1,o=-1,(0|e)<=0)break r}i=(0|e)<=0?-1:1,o=t}t=(t=A<<1)-(A=y(o,u))|0,i=((b=(o=(0|y(i,o))>=0)?0-t|0:t)+(t=y(i,u))|0)/2|0,e=(e<<1)-t|0,A=(A+(o?0-e|0:e)|0)/2|0}c[(e=r)>>2]=A+f,c[e+4>>2]=i+f}function sr(r,e){r|=0;var i,t=0,f=0,a=0,A=0,o=0,b=0,u=0,_=0,p=0,l=0;if(Z=i=Z-16|0,t=c[(e|=0)+20>>2],t=(a=(f=c[e+16>>2])+4|0)>>>0<4?t+1|0:t,o=c[e+12>>2],!(s[e+8>>2]<a>>>0&(0|o)<=(0|t)|(0|t)>(0|o))&&(f=f+c[e>>2]|0,b=k[0|f]|k[f+1|0]<<8|k[f+2|0]<<16|k[f+3|0]<<24,c[e+16>>2]=a,c[e+20>>2]=t,!((0|b)<0)&&(Zr(r+76|0,b),c[(t=i)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,Er(t,e)))){if(b)for(o=1;f=1<<u,a=Se(t),A=c[r+76>>2]+(u>>>3&536870908)|0,1&(a^=o)?f=c[A>>2]&(-1^f):f|=c[A>>2],o=1^a,c[A>>2]=f,(0|b)!=(0|(u=u+1|0)););u=0,t=c[e+8>>2],A=a=c[e+12>>2],o=a=c[e+20>>2],b=f=(p=c[e+16>>2])+4|0,f>>>0>t>>>0&(0|(a=f>>>0<4?a+1|0:a))>=(0|A)|(0|a)>(0|A)||(l=c[e>>2],_=k[0|(f=l+p|0)]|k[f+1|0]<<8|k[f+2|0]<<16|k[f+3|0]<<24,c[e+16>>2]=b,c[e+20>>2]=a,f=t,t=o,f>>>0<(a=p+8|0)>>>0&(0|(t=a>>>0<8?t+1|0:t))>=(0|A)|(0|t)>(0|A)||(f=k[0|(f=b+l|0)]|k[f+1|0]<<8|k[f+2|0]<<16|k[f+3|0]<<24,c[e+16>>2]=a,c[e+20>>2]=t,(0|f)<(0|_)||(c[r+16>>2]=f,c[r+12>>2]=_,!(t=(f>>31)-((_>>31)+(f>>>0<_>>>0)|0)|0)&(e=f-_|0)>>>0>2147483646|t||(u=1,t=e+1|0,c[r+20>>2]=t,e=t>>>1|0,c[r+24>>2]=e,c[r+28>>2]=0-e,1&t||(c[r+24>>2]=e-1)))))}return Z=i+16|0,0|u}function pr(r,e,i,t){r|=0,i|=0,t|=0;var f,a,A=0,b=0,u=0,_=0,s=0,p=0,l=0,d=0,m=0,v=0;r=0,Z=a=Z-16|0,f=c[(e|=0)+80>>2],A=k[i+24|0],e=y(f,A);r:{e:{i:{t:{if(b=c[i+28>>2],!(!k[i+84|0]|5!=(0|b)&6!=(0|b))){if(A=c[i+48>>2],i=c[c[i>>2]>>2],c[a+8>>2]=0,c[a>>2]=0,c[a+4>>2]=0,e){if((0|e)<0)break t;u=hr(r=vi(e<<=2),i+A|0,e)+e|0}(e=c[t>>2])&&(c[t+4>>2]=e,er(e)),c[t+8>>2]=u,c[t+4>>2]=u,c[t>>2]=r,_=1;break r}if(A&&Sr(r=vi(b=A<<2),0,b),s=c[t>>2],(b=c[t+4>>2]-s>>2)>>>0<e>>>0?_e(t,e-b|0):e>>>0>=b>>>0||(c[t+4>>2]=s+(e<<2)),!f){_=1;break i}if(!A){for(e=0;;){if(!M(i,k[i+84|0]?e:c[c[i+68>>2]+(e<<2)>>2],n[i+24|0],r))break i;if(_=f>>>0<=(e=e+1|0)>>>0,(0|e)==(0|f))break}break i}for(m=252&A,l=3&A,v=A>>>0<4,A=0;;){if(!M(i,k[i+84|0]?A:c[c[i+68>>2]+(A<<2)>>2],n[i+24|0],r))break i;if(d=c[t>>2],p=0,e=0,_=0,!v)for(;s=e<<2,c[(b=(u<<2)+d|0)>>2]=c[s+r>>2],c[b+4>>2]=c[(4|s)+r>>2],c[b+8>>2]=c[(8|s)+r>>2],c[b+12>>2]=c[(12|s)+r>>2],e=e+4|0,u=u+4|0,(0|m)!=(0|(_=_+4|0)););if(l)for(;c[(u<<2)+d>>2]=c[(e<<2)+r>>2],e=e+1|0,u=u+1|0,(0|(p=p+1|0))!=(0|l););if(_=f>>>0<=(A=A+1|0)>>>0,(0|A)==(0|f))break}break e}mt(),o()}if(!r)break r}er(r)}return Z=a+16|0,0|_}function lr(r,e){var i=0,t=0,f=0,a=0,n=0;a=-1,t=-1,-1!=(0|e)&&(a=((t=e+1|0)>>>0)%3|0?t:e-2|0,t=e-1|0,(e>>>0)%3|0||(t=e+2|0));r:{e:{i:switch(c[r+168>>2]){case 0:case 1:f=c[r+148>>2],i=1,n=(e=c[r+156>>2])+((-1==(0|a)?-1:c[c[f>>2]+(a<<2)>>2])<<2)|0,c[n>>2]=c[n>>2]+1,e=((-1==(0|t)?-1:c[c[f>>2]+(t<<2)>>2])<<2)+e|0;break e;case 5:f=c[r+148>>2],i=-1,i=(-1!=(0|e)?c[c[f>>2]+(e<<2)>>2]:i)<<2,e=c[r+156>>2],c[(i=i+e|0)>>2]=c[i>>2]+1,i=((-1==(0|a)?-1:c[c[f>>2]+(a<<2)>>2])<<2)+e|0,c[i>>2]=c[i>>2]+1,i=2,e=((-1==(0|t)?-1:c[c[f>>2]+(t<<2)>>2])<<2)+e|0;break e;case 3:f=c[r+148>>2],i=-1,i=(-1!=(0|e)?c[c[f>>2]+(e<<2)>>2]:i)<<2,e=c[r+156>>2],c[(i=i+e|0)>>2]=c[i>>2]+1,i=((-1==(0|a)?-1:c[c[f>>2]+(a<<2)>>2])<<2)+e|0,c[i>>2]=c[i>>2]+2,i=1,e=((-1==(0|t)?-1:c[c[f>>2]+(t<<2)>>2])<<2)+e|0;break e;case 7:break i;default:break r}f=c[r+148>>2],i=-1,i=(-1!=(0|e)?c[c[f>>2]+(e<<2)>>2]:i)<<2,e=c[r+156>>2],c[(i=i+e|0)>>2]=c[i>>2]+2,i=((-1==(0|a)?-1:c[c[f>>2]+(a<<2)>>2])<<2)+e|0,c[i>>2]=c[i>>2]+2,i=2,e=((-1==(0|t)?-1:c[c[f>>2]+(t<<2)>>2])<<2)+e|0}c[e>>2]=c[e>>2]+i}i=r,e=c[c[r+156>>2]+((-1==(0|a)?-1:c[c[c[r+148>>2]>>2]+(a<<2)>>2])<<2)>>2],t=c[r+180>>2],r=c[r+176>>2],c[i+172>>2]=(0|r)<=(0|e)?((0|e)<(0|t)?e:t)-r|0:0}function yr(r,e,i){var t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0;if((t=c[r+8>>2])-(f=c[r+4>>2])>>2>>>0>=e>>>0){if(e){if(t=f,n=7&e)for(;c[t>>2]=c[i>>2],t=t+4|0,(0|n)!=(0|(a=a+1|0)););if(f=(e<<2)+f|0,!((e-1&1073741823)>>>0<7))for(;c[t>>2]=c[i>>2],c[t+4>>2]=c[i>>2],c[t+8>>2]=c[i>>2],c[t+12>>2]=c[i>>2],c[t+16>>2]=c[i>>2],c[t+20>>2]=c[i>>2],c[t+24>>2]=c[i>>2],c[t+28>>2]=c[i>>2],(0|f)!=(0|(t=t+32|0)););}c[r+4>>2]=f}else{r:{if((A=(a=f-(b=c[r>>2])>>2)+e|0)>>>0<1073741824){if(t=(u=t-b|0)>>>1|0,A=u>>>0>=2147483644?1073741823:t>>>0>A>>>0?t:A){if(A>>>0>=1073741824)break r;k=vi(A<<2)}if(t=a=(a<<2)+k|0,u=7&e)for(;c[t>>2]=c[i>>2],t=t+4|0,(0|u)!=(0|(n=n+1|0)););if(n=(e<<2)+a|0,(e-1&1073741823)>>>0>=7)for(;c[t>>2]=c[i>>2],c[t+4>>2]=c[i>>2],c[t+8>>2]=c[i>>2],c[t+12>>2]=c[i>>2],c[t+16>>2]=c[i>>2],c[t+20>>2]=c[i>>2],c[t+24>>2]=c[i>>2],c[t+28>>2]=c[i>>2],(0|n)!=(0|(t=t+32|0)););if((0|f)!=(0|b))for(;f=f-4|0,c[(a=a-4|0)>>2]=c[f>>2],(0|f)!=(0|b););return c[r+8>>2]=(A<<2)+k,c[r+4>>2]=n,c[r>>2]=a,void(b&&er(b))}mt(),o()}Zi(),o()}}function dr(r,e,i){var t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0;if((t=c[r+8>>2])-(f=c[r>>2])>>2>>>0>=e>>>0){if(b=e>>>0>(A=(a=c[r+4>>2])-f>>2)>>>0?A:e){if(t=f,u=7&(n=b))for(;c[t>>2]=c[i>>2],n=n-1|0,t=t+4|0,(0|(k=k+1|0))!=(0|u););if(!(b>>>0<8))for(;c[t>>2]=c[i>>2],c[t+4>>2]=c[i>>2],c[t+8>>2]=c[i>>2],c[t+12>>2]=c[i>>2],c[t+16>>2]=c[i>>2],c[t+20>>2]=c[i>>2],c[t+24>>2]=c[i>>2],c[t+28>>2]=c[i>>2],t=t+32|0,n=n-8|0;);}if(e>>>0>A>>>0){for(e=(e-A<<2)+a|0;c[a>>2]=c[i>>2],(0|e)!=(0|(a=a+4|0)););return void(c[r+4>>2]=e)}c[r+4>>2]=f+(e<<2)}else if(f&&(c[r+4>>2]=f,er(f),c[r+8>>2]=0,c[r>>2]=0,c[r+4>>2]=0,t=0),e>>>0>=1073741824||(f=t>>>1|0,(t=t>>>0>=2147483644?1073741823:e>>>0<f>>>0?f:e)>>>0>=1073741824))mt(),o();else{if(f=vi(t<<=2),c[r>>2]=f,c[r+8>>2]=t+f,i=c[i>>2],t=f,n=7&e)for(;c[t>>2]=i,t=t+4|0,(0|n)!=(0|(a=a+1|0)););if(f=f+(e<<2)|0,(e-1&1073741823)>>>0>=7)for(;c[t+28>>2]=i,c[t+24>>2]=i,c[t+20>>2]=i,c[t+16>>2]=i,c[t+12>>2]=i,c[t+8>>2]=i,c[t+4>>2]=i,c[t>>2]=i,(0|f)!=(0|(t=t+32|0)););c[r+4>>2]=f}}function mr(r,e,i){var t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0;a=(i>>>0)/3|0,u=c[(c[c[r+8>>2]+96>>2]+y(a,12)|0)+(i-y(a,3)<<2)>>2];r:if(A=c[c[r+12>>2]+4>>2],(0|(f=c[A+4>>2]))==c[A+8>>2]){e:{if((t=(n=(a=f-(b=c[A>>2])|0)>>2)+1|0)>>>0<1073741824){if(k=n<<2,n=a>>>1|0,n=a>>>0>=2147483644?1073741823:t>>>0<n>>>0?n:t){if(n>>>0>=1073741824)break e;a=vi(n<<2)}else a=0;if(c[(t=k+a|0)>>2]=u,u=t+4|0,(0|f)!=(0|b))for(;f=f-4|0,c[(t=t-4|0)>>2]=c[f>>2],(0|f)!=(0|b););c[A+8>>2]=a+(n<<2),c[A+4>>2]=u,c[A>>2]=t,b&&er(b);break r}mt(),o()}Zi(),o()}else c[f>>2]=u,c[A+4>>2]=f+4;r:{e:{if(A=c[r+4>>2],(0|(f=c[A+4>>2]))==c[A+8>>2]){if((t=(u=(a=f-(b=c[A>>2])|0)>>2)+1|0)>>>0>=1073741824)break e;if(n=a>>>1|0,n=a>>>0>=2147483644?1073741823:t>>>0<n>>>0?n:t){if(n>>>0>=1073741824)break r;a=vi(n<<2)}else a=0;if(c[(t=a+(u<<2)|0)>>2]=i,i=t+4|0,(0|f)!=(0|b))for(;f=f-4|0,c[(t=t-4|0)>>2]=c[f>>2],(0|f)!=(0|b););c[A+8>>2]=a+(n<<2),c[A+4>>2]=i,c[A>>2]=t,b&&er(b)}else c[f>>2]=i,c[A+4>>2]=f+4;return r=c[r+4>>2],c[c[r+12>>2]+(e<<2)>>2]=c[r+24>>2],void(c[r+24>>2]=c[r+24>>2]+1)}mt(),o()}Zi(),o()}function vr(r,e,i,t){var f,a=0,A=0,b=0,u=0,_=0,s=0;if(!((0|(f=t-i|0))<=0)){r:{if(((a=c[r+8>>2])-(u=c[r+4>>2])|0)>=(0|f)){if((0|(_=u-e|0))>=(0|f)){A=u,b=t;break r}if(A=u,(0|(b=i+_|0))!=(0|t))for(a=b;n[0|A]=k[0|a],A=A+1|0,(0|(a=a+1|0))!=(0|t););if(c[r+4>>2]=A,(0|_)>0)break r;return}if((0|(b=(u-(s=c[r>>2])|0)+f|0))>=0)return a=(A=a-s|0)<<1,b=(_=e-s|0)+(a=(A=A>>>0>=1073741823?2147483647:a>>>0>b>>>0?a:b)?vi(A):0)|0,(0|i)!=(0|t)&&(b=hr(b,i,f)+f|0),t=gr(a,s,_),e=gr(b,e,i=u-e|0),c[r+8>>2]=a+A,c[r+4>>2]=e+i,c[r>>2]=t,void(s&&er(s));mt(),o()}if(u>>>0>(t=(a=A)-f|0)>>>0)for(;n[0|a]=k[0|t],a=a+1|0,u>>>0>(t=t+1|0)>>>0;);if(c[r+4>>2]=a,(0|(r=e+f|0))!=(0|A)&&gr(A-(r=A-r|0)|0,e,r),(0|i)!=(0|b)){if(A=(-1^i)+b|0,r=b-i&7)for(t=0,a=e;n[0|a]=k[0|i],a=a+1|0,i=i+1|0,(0|r)!=(0|(t=t+1|0)););else a=e;if(!(A>>>0<7))for(;n[0|a]=k[0|i],n[a+1|0]=k[i+1|0],n[a+2|0]=k[i+2|0],n[a+3|0]=k[i+3|0],n[a+4|0]=k[i+4|0],n[a+5|0]=k[i+5|0],n[a+6|0]=k[i+6|0],n[a+7|0]=k[i+7|0],a=a+8|0,(0|b)!=(0|(i=i+8|0)););}}}function hr(r,e,i){var t,f=0,a=0;if(i>>>0>=512)return G(0|r,0|e,0|i),r;t=r+i|0;r:if(3&(r^e))if(t>>>0<4)i=r;else if((f=t-4|0)>>>0<r>>>0)i=r;else for(i=r;n[0|i]=k[0|e],n[i+1|0]=k[e+1|0],n[i+2|0]=k[e+2|0],n[i+3|0]=k[e+3|0],e=e+4|0,f>>>0>=(i=i+4|0)>>>0;);else{e:if(3&r)if(i)for(i=r;;){if(n[0|i]=k[0|e],e=e+1|0,!(3&(i=i+1|0)))break e;if(!(i>>>0<t>>>0))break}else i=r;else i=r;if(!((f=-4&t)>>>0<64||(a=f+-64|0)>>>0<i>>>0))for(;c[i>>2]=c[e>>2],c[i+4>>2]=c[e+4>>2],c[i+8>>2]=c[e+8>>2],c[i+12>>2]=c[e+12>>2],c[i+16>>2]=c[e+16>>2],c[i+20>>2]=c[e+20>>2],c[i+24>>2]=c[e+24>>2],c[i+28>>2]=c[e+28>>2],c[i+32>>2]=c[e+32>>2],c[i+36>>2]=c[e+36>>2],c[i+40>>2]=c[e+40>>2],c[i+44>>2]=c[e+44>>2],c[i+48>>2]=c[e+48>>2],c[i+52>>2]=c[e+52>>2],c[i+56>>2]=c[e+56>>2],c[i+60>>2]=c[e+60>>2],e=e- -64|0,a>>>0>=(i=i- -64|0)>>>0;);if(i>>>0>=f>>>0)break r;for(;c[i>>2]=c[e>>2],e=e+4|0,f>>>0>(i=i+4|0)>>>0;);}if(i>>>0<t>>>0)for(;n[0|i]=k[0|e],e=e+1|0,(0|t)!=(0|(i=i+1|0)););return r}function Rr(r){var e,i=0,t=0,f=0,a=0;if(c[(r|=0)>>2]=8336,(i=c[(e=r+232|0)+196>>2])&&(c[e+200>>2]=i,er(i)),t=c[e+184>>2]){if((0|(i=t))!=(0|(f=c[e+188>>2]))){for(;(a=c[(i=f-12|0)>>2])&&(c[f-8>>2]=a,er(a)),f=i,(0|i)!=(0|t););i=c[e+184>>2]}c[e+188>>2]=t,er(i)}if((i=c[e+156>>2])&&(c[e+160>>2]=i,er(i)),t=c[e+136>>2],c[e+136>>2]=0,t){if(i=c[(f=t-4|0)>>2])for(i=t+(i<<4)|0;(0|t)!=(0|(i=i-16|0)););er(f)}if(we(r+216|0),(i=c[r+196>>2])&&(c[r+200>>2]=i,er(i)),(i=c[r+184>>2])&&(c[r+188>>2]=i,er(i)),(i=c[r+172>>2])&&(c[r+176>>2]=i,er(i)),(i=c[r+160>>2])&&(c[r+164>>2]=i,er(i)),i=c[r+144>>2])for(;t=c[i>>2],er(i),i=t;);return i=c[r+136>>2],c[r+136>>2]=0,i&&er(i),(i=c[r+120>>2])&&er(i),(i=c[r+108>>2])&&er(i),(i=c[r+96>>2])&&er(i),(i=c[r+72>>2])&&(c[r+76>>2]=i,er(i)),(i=c[r+60>>2])&&er(i),(i=c[r+48>>2])&&(c[r+52>>2]=i,er(i)),(i=c[r+36>>2])&&(c[r+40>>2]=i,er(i)),(i=c[r+24>>2])&&(c[r+28>>2]=i,er(i)),(i=c[r+12>>2])&&(c[r+16>>2]=i,er(i)),i=c[r+8>>2],c[r+8>>2]=0,i&&ge(i),0|r}function Nr(r,e,i){var t=0,f=0,a=0,n=0,A=0,b=0,u=0,k=0;if((t=c[r+8>>2])-(f=c[r+4>>2])>>2>>>0>=e>>>0){if(e){if(t=f,a=7&e)for(;c[t>>2]=c[i>>2],t=t+4|0,(0|a)!=(0|(A=A+1|0)););if(f=(e<<2)+f|0,!((e-1&1073741823)>>>0<7))for(;c[t>>2]=c[i>>2],c[t+4>>2]=c[i>>2],c[t+8>>2]=c[i>>2],c[t+12>>2]=c[i>>2],c[t+16>>2]=c[i>>2],c[t+20>>2]=c[i>>2],c[t+24>>2]=c[i>>2],c[t+28>>2]=c[i>>2],(0|f)!=(0|(t=t+32|0)););}c[r+4>>2]=f}else{r:{if((n=(a=(u=f-(b=c[r>>2])|0)>>2)+e|0)>>>0<1073741824){if(f=(t=t-b|0)>>>1|0,n=t>>>0>=2147483644?1073741823:f>>>0>n>>>0?f:n){if(n>>>0>=1073741824)break r;k=vi(n<<2)}if(t=a=(a<<2)+k|0,f=7&e)for(;c[t>>2]=c[i>>2],t=t+4|0,(0|f)!=(0|(A=A+1|0)););if(f=a+(e<<2)|0,(e-1&1073741823)>>>0>=7)for(;c[t>>2]=c[i>>2],c[t+4>>2]=c[i>>2],c[t+8>>2]=c[i>>2],c[t+12>>2]=c[i>>2],c[t+16>>2]=c[i>>2],c[t+20>>2]=c[i>>2],c[t+24>>2]=c[i>>2],c[t+28>>2]=c[i>>2],(0|f)!=(0|(t=t+32|0)););return e=gr(k,b,u),c[r+4>>2]=f,c[r>>2]=e,c[r+8>>2]=e+(n<<2),void(b&&er(b))}mt(),o()}Zi(),o()}}function Tr(r,e){var i=0,t=0,f=0,a=0,A=0,b=0,u=0,_=0;if((t=k[r+11|0]>>>7|0?c[r+4>>2]:127&k[r+11|0])>>>0<e>>>0){if(Z=b=Z-16|0,e=e-t|0){for(A=k[r+11|0]>>>7|0?(2147483647&c[r+8>>2])-1|0:10,u=(t=k[r+11|0]>>>7|0?c[r+4>>2]:127&k[r+11|0])+e|0,A-t>>>0<e>>>0&&(Z=f=Z-16|0,(i=u-A|0)>>>0<=2147483631-A>>>0?(a=k[r+11|0]>>>7|0?c[r>>2]:r,A>>>0<1073741799?(c[f+12>>2]=A<<1,c[f>>2]=i+A,Z=i=Z-16|0,Z=i+16|0,i=f+12|0,i=(i=(i=c[(s[f>>2]<s[i>>2]?i:f)>>2])>>>0>=11?11==(0|(i=(_=i+16&-16)-1|0))?_:i:10)+1|0):i=2147483631,Ci(f,i),i=c[f>>2],t&&le(i,a,t),10!=(0|A)&&er(a),c[r>>2]=i,c[r+8>>2]=-2147483648&c[r+8>>2]|2147483647&c[f+4>>2],c[r+8>>2]=-2147483648|c[r+8>>2],Z=f+16|0):(yt(),o())),a=(a=t)+(t=k[r+11|0]>>>7|0?c[r>>2]:r)|0,Z=f=Z-16|0,n[f+15|0]=0;e;)n[0|a]=k[f+15|0],e=e-1|0,a=a+1|0;Z=f+16|0,Vi(r,u),n[b+15|0]=0,n[t+u|0]=k[b+15|0]}Z=b+16|0}else t=k[r+11|0]>>>7|0?c[r>>2]:r,Z=a=Z-16|0,Vi(r,e),n[a+15|0]=0,n[e+t|0]=k[a+15|0],Z=a+16|0}function Vr(r,e){var i,t=0,f=0,a=0,A=0,b=0;Z=i=Z-16|0;r:{e:{if(e){if(c[r+88>>2]=0,c[r+92>>2]=0,f=c[r+84>>2],c[r+84>>2]=0,f&&er(f),c[r+76>>2]=0,c[r+80>>2]=0,f=c[r+72>>2],c[r+72>>2]=0,f&&er(f),f=c[e>>2],t=c[e+4>>2],n[i+15|0]=0,xr(r,t-f>>2,i+15|0),f=c[e+28>>2],t=c[e+24>>2],n[i+14|0]=0,xr(r+12|0,f-t>>2,i+14|0),dr(r+28|0,c[e+4>>2]-c[e>>2]>>2,10284),A=(t=c[e+28>>2]-c[e+24>>2]|0)>>2,a=c[r+52>>2],!(A>>>0<=c[r+60>>2]-a>>2>>>0)){if((0|t)<0)break e;if(f=c[r+56>>2],A=(t=vi(t))+(A<<2)|0,t=b=t+(f-a&-4)|0,(0|f)!=(0|a))for(;f=f-4|0,c[(t=t-4|0)>>2]=c[f>>2],(0|f)!=(0|a););c[r+60>>2]=A,c[r+56>>2]=b,c[r+52>>2]=t,a&&er(a)}if(A=(t=c[e+28>>2]-c[e+24>>2]|0)>>2,a=c[r+40>>2],!(A>>>0<=c[r+48>>2]-a>>2>>>0)){if((0|t)<0)break r;if(f=c[r+44>>2],A=(t=vi(t))+(A<<2)|0,t=b=t+(f-a&-4)|0,(0|f)!=(0|a))for(;f=f-4|0,c[(t=t-4|0)>>2]=c[f>>2],(0|f)!=(0|a););c[r+48>>2]=A,c[r+44>>2]=b,c[r+40>>2]=t,a&&er(a)}n[r+24|0]=1,c[r+64>>2]=e}return void(Z=i+16|0)}mt(),o()}mt(),o()}function Ur(r,e){var i=0,t=0,f=0;i=(0|r)==(0|e),n[e+12|0]=i;r:if(!i)for(;;){if(t=c[e+8>>2],k[t+12|0])break r;e:{if(i=c[t+8>>2],(0|(f=c[i>>2]))==(0|t)){if(!(!(f=c[i+4>>2])|k[f+12|0]))break e;return c[t>>2]!=(0|e)?(e=c[t+4>>2],r=c[e>>2],c[t+4>>2]=r,r&&(c[r+8>>2]=t,i=c[t+8>>2]),c[e+8>>2]=i,r=c[t+8>>2],c[((c[r>>2]!=(0|t))<<2)+r>>2]=e,c[e>>2]=t,c[t+8>>2]=e,i=c[e+8>>2],t=c[i>>2]):e=t,n[e+12|0]=1,n[i+12|0]=0,r=c[t+4>>2],c[i>>2]=r,r&&(c[r+8>>2]=i),c[t+8>>2]=c[i+8>>2],r=c[i+8>>2],c[((c[r>>2]!=(0|i))<<2)+r>>2]=t,c[t+4>>2]=i,void(c[i+8>>2]=t)}if(k[f+12|0]|!f){c[t>>2]==(0|e)?(r=c[e+4>>2],c[t>>2]=r,r&&(c[r+8>>2]=t,i=c[t+8>>2]),c[e+8>>2]=i,r=c[t+8>>2],c[((c[r>>2]!=(0|t))<<2)+r>>2]=e,c[e+4>>2]=t,c[t+8>>2]=e,i=c[e+8>>2]):e=t,n[e+12|0]=1,n[i+12|0]=0,r=c[i+4>>2],e=c[r>>2],c[i+4>>2]=e,e&&(c[e+8>>2]=i),c[r+8>>2]=c[i+8>>2],e=c[i+8>>2],c[((c[e>>2]!=(0|i))<<2)+e>>2]=r,c[r>>2]=i,c[i+8>>2]=r;break r}}if(n[t+12|0]=1,n[i+12|0]=(0|r)==(0|i),n[f+12|0]=1,e=i,(0|i)==(0|r))break}}function Wr(r,e,i,t){var f=0,a=0,n=0,A=0,o=0,b=0,u=0,c=0,k=0;r:{e:{i:{t:{f:{a:{n:{A:{o:{b:{u:{if(e){if(!i)break u;if(!t)break b;if((f=v(t)-v(e)|0)>>>0<=31)break o;break i}if(1==(0|t)|t>>>0>1)break i;E=0,r=(r>>>0)/(i>>>0)|0;break r}if(!r)break A;if(!t|t-1&t)break n;r=e>>>Xi(t)|0,E=0;break r}if(!(i-1&i))break a;n=0-(A=(v(i)+33|0)-v(e)|0)|0;break t}A=f+1|0,n=63-f|0;break t}E=0,r=(e>>>0)/(t>>>0)|0;break r}if((f=v(t)-v(e)|0)>>>0<31)break f;break i}if(1==(0|i))break e;i=31&(t=Xi(i)),(63&t)>>>0>=32?r=e>>>i|0:(f=e>>>i|0,r=((1<<i)-1&e)<<32-i|r>>>i),E=f;break r}A=f+1|0,n=63-f|0}if(a=31&(f=63&A),f>>>0>=32?(f=0,o=e>>>a|0):(f=e>>>a|0,o=((1<<a)-1&e)<<32-a|r>>>a),a=31&(n&=63),n>>>0>=32?(e=r<<a,r=0):(e=(1<<a)-1&r>>>32-a|e<<a,r<<=a),A)for(a=t-1|0,k=-1!=(0|(n=i-1|0))?a+1|0:a;b=f<<1|o>>>31,o=(f=o<<1|e>>>31)-(u=i&(a=k-(b+(f>>>0>n>>>0)|0)>>31))|0,f=b-((t&a)+(f>>>0<u>>>0)|0)|0,e=e<<1|r>>>31,r=c|r<<1,c=1&a,A=A-1|0;);E=e<<1|r>>>31,r=c|r<<1;break r}r=0,e=0}E=e}return r}function Dr(r,e,i,t){var f=0,a=0,n=0,A=0,b=0,u=0;r:{e:{i:if(e){if((0|t)<0)break r;t:if(!((0|t)<=0&i>>>0<=(n=(f=c[r+4>>2])-(a=c[r>>2])|0)>>>0|(0|t)<0))if(i>>>0>n>>>0){if((t=i-n|0)>>>0<=(A=c[r+8>>2])-f>>>0){b=r,u=Sr(f,0,t)+t|0,c[b+4>>2]=u;break t}if((0|i)<0)break e;if(A=(f=A-a|0)<<1,Sr((A=vi(f=f>>>0>=1073741823?2147483647:i>>>0<A>>>0?A:i))+n|0,0,t),t=gr(A,a,n),c[r+8>>2]=t+f,c[r+4>>2]=i+t,c[r>>2]=t,!a)break t;er(a)}else i>>>0>=n>>>0||(c[r+4>>2]=i+a);i&&gr(c[r>>2],e,i)}else{if((0|t)<0)break r;if(i>>>0>(t=(a=c[r+4>>2])-(e=c[r>>2])|0)>>>0){if((n=i-t|0)>>>0<=(f=c[r+8>>2])-a>>>0){b=r,u=Sr(a,0,n)+n|0,c[b+4>>2]=u;break i}if((0|i)<0)break e;if(f=(a=f-e|0)<<1,Sr((f=vi(a=a>>>0>=1073741823?2147483647:i>>>0<f>>>0?f:i))+t|0,0,n),t=gr(f,e,t),c[r+8>>2]=t+a,c[r+4>>2]=i+t,c[r>>2]=t,!e)break i;er(e);break i}if(i>>>0>=t>>>0)break i;c[r+4>>2]=e+i}e=c[r+28>>2],e=(i=c[r+24>>2]+1|0)?e:e+1|0,c[r+24>>2]=i,c[r+28>>2]=e,n=1;break r}mt(),o()}return n}function Gr(r,e,i){r|=0,e|=0,i|=0;var t,f=0,a=0,n=0,A=0,o=0,b=d(0),u=0,_=0,s=0,l=0,y=0,m=0,v=0,h=0,R=0,N=0,T=0;if(Z=t=Z-16|0,9==c[i+28>>2]){if(f=c[r+4>>2],n=vi(a=(o=k[i+24|0])<<2),c[(_=t+8|0)>>2]=1065353216,b=p[r+20>>2],(0|(f=-1<<f^-1))>0&&(p[_>>2]=b/d(0|f)),(y=(0|f)>0)&&(u=c[i+80>>2]))if(o)for(m=c[c[e>>2]>>2]+c[e+48>>2]|0,N=254&o,T=1&o,e=0;;){if(s=c[r+8>>2],b=p[_>>2],f=0,l=0,1!=(0|o))for(;v=(e<<2)+m|0,p[(A=f<<2)+n>>2]=d(b*d(c[v>>2]))+p[A+s>>2],p[(A|=4)+n>>2]=d(b*d(c[v+4>>2]))+p[A+s>>2],f=f+2|0,e=e+2|0,(0|N)!=(0|(l=l+2|0)););if(T&&(p[(f<<=2)+n>>2]=d(b*d(c[(e<<2)+m>>2]))+p[f+s>>2],e=e+1|0),hr(c[c[i+64>>2]>>2]+h|0,n,a),h=a+h|0,(0|(R=R+1|0))==(0|u))break}else{if(e=0,1!=(0|u))for(r=-2&u,f=0;hr(c[c[i+64>>2]>>2]+e|0,n,a),hr((e=e+a|0)+c[c[i+64>>2]>>2]|0,n,a),e=e+a|0,(0|r)!=(0|(f=f+2|0)););1&u&&hr(c[c[i+64>>2]>>2]+e|0,n,a)}er(n)}return Z=t+16|0,0|y}function Zr(r,e){var i,t=0,f=0,a=0,n=0,A=0,b=0;Z=i=Z-16|0;r:{e:if((a=c[r+4>>2])>>>0<e>>>0){if((f=e-a|0)>>>0>(n=(t=c[r+8>>2])<<5)>>>0|a>>>0>n-f>>>0){if(c[i+8>>2]=0,c[i>>2]=0,c[i+4>>2]=0,(0|e)<0)break r;nr(i,e=n>>>0<=1073741822?(e=e+31&-32)>>>0<(t<<=6)>>>0?t:e:2147483647),a=c[r+4>>2],c[i+4>>2]=a+f,b=c[r>>2],e=c[i>>2],(0|a)<=0||(t=a>>>5|0,a>>>0>=32&&gr(e,b,t<<2),e=(n=t<<2)+e|0,(A=31&a)&&(t=-1>>>32-A|0,c[e>>2]=c[e>>2]&(-1^t)|c[b+n>>2]&t),b=c[r>>2]),c[r>>2]=c[i>>2],c[i>>2]=b,t=c[r+4>>2],c[r+4>>2]=c[i+4>>2],c[i+4>>2]=t,t=c[r+8>>2],c[r+8>>2]=c[i+8>>2],c[i+8>>2]=t,b&&er(b)}else c[r+4>>2]=e,A=31&a,e=c[r>>2]+(a>>>3&536870908)|0;if(!f)break e;if(A&&(r=(t=32-A|0)>>>0<f>>>0?t:f,c[e>>2]=c[e>>2]&(-1<<A&-1>>>t-r^-1),f=f-r|0,e=e+4|0),r=f>>>5|0,f>>>0>=32&&Sr(e,0,r<<2),(-32&f)==(0|f))break e;c[(r=(r<<2)+e|0)>>2]=c[r>>2]&(-1>>>32-(31&f)^-1)}else c[r+4>>2]=e;return void(Z=i+16|0)}mt(),o()}function Er(r,e){var i,t=0,f=0,a=0,A=0,o=0,b=0,u=0,_=0;Z=i=Z-16|0;r:if(!((0|(A=c[e+20>>2]))>=(0|(b=c[e+12>>2]))&(t=c[e+16>>2])>>>0>=s[e+8>>2]|(0|A)>(0|b))&&(n[r+12|0]=k[t+c[e>>2]|0],t=c[e+20>>2],t=(A=c[e+16>>2]+1|0)?t:t+1|0,c[e+16>>2]=A,c[e+20>>2]=t,Ee(1,i+12|0,e)&&(o=(b=c[e+8>>2])-(A=c[e+16>>2])|0,t=c[i+12>>2],f=A>>>0>b>>>0,b=c[e+20>>2],!(o>>>0<t>>>0&(0|(a=c[e+12>>2]-(f+b|0)|0))<=0|(0|a)<0|(0|t)<=0)))){o=A+c[e>>2]|0,c[r>>2]=o;e:{i:{if((f=k[0|(u=(a=t-1|0)+o|0)])>>>0<=63)c[r+4>>2]=a,f=63&k[0|u];else{t:switch((f>>>6|0)-1|0){case 1:break i;case 0:break t;default:break r}if(t>>>0<2)break r;a=t-2|0,c[r+4>>2]=a,f=k[(o=o+a|0)+1|0]<<8&16128|k[0|o]}c[r+8>>2]=f+4096;break e}if(t>>>0<3)break r;if(a=t-3|0,c[r+4>>2]=a,f=r,r=k[(r=o+a|0)+1|0]<<8|k[r+2|0]<<16&4128768|k[0|r],c[f+8>>2]=r+4096,r>>>0>1044479)break r}r=b,r=(f=t)>>>0>(t=t+A|0)>>>0?r+1|0:r,c[e+16>>2]=t,c[e+20>>2]=r,_=1}return Z=i+16|0,_}function Fr(r,e,i){e|=0,i|=0;var t=0,f=0,a=0,n=0,A=0,o=0,b=0;return(t=(f=c[(r|=0)+12>>2])-(o=c[r+8>>2])>>2)>>>0<(e=k[e+24|0])>>>0?(_e(r+8|0,e-t|0),o=c[r+8>>2],f=c[r+12>>2]):e>>>0>=t>>>0||(f=(e<<2)+o|0,c[r+12>>2]=f),e=0,a=c[i+8>>2],A=c[i+12>>2],b=c[i+20>>2],a>>>0<(n=(f=f-o|0)+(t=c[i+16>>2])|0)>>>0&(0|A)<=(0|(b=f>>>0>n>>>0?b+1|0:b))|(0|A)<(0|b)||(hr(o,t+c[i>>2]|0,f),t=c[i+20>>2],t=(n=f)>>>0>(f=f+c[i+16>>2]|0)>>>0?t+1|0:t,c[i+16>>2]=f,c[i+20>>2]=t,(a=c[i+8>>2])>>>0<(n=f+4|0)>>>0&(0|(t=n>>>0<4?t+1|0:t))>=(0|(A=c[i+12>>2]))|(0|t)>(0|A)||(t=f+c[i>>2]|0,c[r+20>>2]=k[0|t]|k[t+1|0]<<8|k[t+2|0]<<16|k[t+3|0]<<24,n=t=c[i+20>>2],a=t,a=(t=(f=c[i+16>>2])+4|0)>>>0<4?a+1|0:a,c[i+16>>2]=t,c[i+20>>2]=a,(0|a)>=(0|(A=c[i+12>>2]))&t>>>0>=s[i+8>>2]|(0|a)>(0|A)||(a=k[t+c[i>>2]|0],t=n,t=(f=f+5|0)>>>0<5?t+1|0:t,c[i+16>>2]=f,c[i+20>>2]=t,a-1>>>0>29||(c[r+4>>2]=a,e=1)))),0|e}function Ir(r,e,i,t){var f,a=0,n=0,A=0,o=0,b=0,u=0,k=0,_=0;b=+p[e>>2],u=+p[e+4>>2],k=+p[e+8>>2],(A=m(b)+m(u)+m(k))>1e-6?(u*=A=1/A,b*=A,a=A*k<0):(b=1,u=0,a=0),f=c[r+16>>2],A=N(b*(k=+(0|f))+.5),o=((n=(_=m(A)<2147483648?~~A:-2147483648)>>31)^_)-n|0,A=N(u*k+.5),o=(0|(e=f-(o+(((n=m(A)<2147483648?~~A:-2147483648)^(e=n>>31))-e|0)|0)|0))>0?e:0,a=a?0-o|0:o,n=n+(e>>31&((0|n)>0?e:0-e|0))|0,(0|_)>=0?(e=a+f|0,r=c[r+8>>2],a=f+n|0):(e=((e=n>>31)^n)-e|0,r=c[r+8>>2],e=(0|a)<0?e:r-e|0,a=(0|n)<0?o:r-o|0),e|a&&(0|r)!=(0|e)|a&&(0|r)!=(0|a)|e?(0|e)<=(0|f)|a?(0|r)!=(0|a)|(0|e)>=(0|f)?(0|r)!=(0|e)|(0|a)>=(0|f)?e?r=a:(e=0,r=(0|a)<=(0|f)?a:(f<<1)-a|0):(e=r,r=(f<<1)-a|0):e=(f<<1)-e|0:(e=(f<<1)-e|0,r=0):e=r,c[i>>2]=r,c[t>>2]=e}function Yr(r,e){var i,t,f,a=0,A=0,o=0;t=c[r>>2],c[(a=t+(e>>>3&536870908)|0)>>2]=c[a>>2]|1<<e,i=c[r+64>>2],A=-1,(o=-1==(0|e))||(A=-1,-1!=(0|(a=((a=e+1|0)>>>0)%3|0?a:e-2|0))&&(A=c[c[i>>2]+(a<<2)>>2])),a=c[r+12>>2],c[(f=(A>>>3&536870908)+a|0)>>2]=c[f>>2]|1<<A;r:{if(o)r=a+536870908|0,e=c[a+536870908>>2],a=-2147483648;else{e:{if((e>>>0)%3|0)o=e-1|0;else if(A=-1,-1==(0|(o=e+2|0)))break e;A=c[c[i>>2]+(o<<2)>>2]}if(c[(o=(A>>>3&536870908)+a|0)>>2]=c[o>>2]|1<<A,A=-1,-1==(0|(e=c[c[i+12>>2]+(e<<2)>>2])))break r;n[r+24|0]=0,c[(r=(e>>>3&536870908)+t|0)>>2]=c[r>>2]|1<<e,-1!=(0|(r=((r=e+1|0)>>>0)%3|0?r:e-2|0))&&(A=c[c[i>>2]+(r<<2)>>2]),c[(r=a+(A>>>3&536870908)|0)>>2]=c[r>>2]|1<<A;e:{if((e>>>0)%3|0)e=e-1|0;else if(r=-1,-1==(0|(e=e+2|0)))break e;r=c[c[i>>2]+(e<<2)>>2]}e=1<<r,a=c[(r=a+(r>>>3&536870908)|0)>>2]}c[r>>2]=e|a}}function wr(r,e,i){r|=0,e|=0,i|=0;var t=0,f=d(0),a=d(0),A=d(0),o=d(0),_=d(0),s=0,p=d(0),l=d(0),y=d(0),v=d(0),h=0;if(!(9!=c[i+28>>2]|3!=k[i+24|0])&&!((r=c[r+4>>2])-2>>>0>28)&&(h=1,s=c[i+80>>2]))for(p=d(d(2)/d((1<<r)-2|0)),i=c[c[i>>2]>>2]+c[i+48>>2]|0,r=c[c[e>>2]>>2]+c[e+48>>2]|0,e=0;A=d(0),l=d(0),y=d(0),f=d(d(d(c[r>>2])*p)+d(-1)),a=d(d(d(c[r+4>>2])*p)+d(-1)),_=d(d(d(1)-d(m(f)))-d(m(a))),o=d(R(d(-_),d(0))),v=d(-o),a=d(a+(a<d(0)?o:v)),f=d(f+(f<d(0)?o:v)),+(o=d(d(a*a)+d(d(_*_)+d(f*f))))<1e-6||(A=d(d(1)/d(V(o))),y=d(a*A),l=d(f*A),A=d(_*A)),r=r+8|0,b(y),t=u(2),n[i+8|0]=t,n[i+9|0]=t>>>8,n[i+10|0]=t>>>16,n[i+11|0]=t>>>24,b(l),t=u(2),n[i+4|0]=t,n[i+5|0]=t>>>8,n[i+6|0]=t>>>16,n[i+7|0]=t>>>24,b(A),t=u(2),n[0|i]=t,n[i+1|0]=t>>>8,n[i+2|0]=t>>>16,n[i+3|0]=t>>>24,i=i+12|0,(0|s)!=(0|(e=e+1|0)););return 0|h}function jr(r,e){var i,t=0,f=0,a=0,n=0,A=0,o=0,b=0;Z=i=Z-16|0;r:if(Re(1,i+8|0,e)&&(n=(f=c[e+8>>2])-(a=c[e+16>>2])|0,A=c[i+12>>2],t=f>>>0<a>>>0,f=c[e+20>>2],!((0|A)==(0|(o=c[e+12>>2]-(t+f|0)|0))&(t=c[i+8>>2])>>>0>n>>>0|A>>>0>o>>>0||(f=f+A|0,f=(n=t+a|0)>>>0<a>>>0?f+1|0:f,c[e+16>>2]=n,c[e+20>>2]=f,(0|t)<=0)))){e=c[e>>2]+a|0,c[r+40>>2]=e;e:if((n=k[0|(f=e+(a=t-1|0)|0)])>>>0<=63)c[r+44>>2]=a,e=63&k[0|f];else{i:switch((n>>>6|0)-1|0){case 0:if(t>>>0<2)break r;t=t-2|0,c[r+44>>2]=t,e=k[(e=e+t|0)+1|0]<<8&16128|k[0|e];break e;case 1:if(t>>>0<3)break r;t=t-3|0,c[r+44>>2]=t,e=k[(e=e+t|0)+1|0]<<8|k[e+2|0]<<16&4128768|k[0|e];break e;default:break i}t=t-4|0,c[r+44>>2]=t,e=1073741823&(k[0|(e=e+t|0)]|k[e+1|0]<<8|k[e+2|0]<<16|k[e+3|0]<<24)}c[r+48>>2]=e+16384,b=e>>>0<4177920}return Z=i+16|0,b}function Jr(r){var e,i=0,t=0,f=0;if(c[(r|=0)>>2]=8284,e=c[r+368>>2],c[r+368>>2]=0,e){if(i=c[(f=e-4|0)>>2])for(t=(i<<4)+e|0;(0|e)!=(0|(t=t-16|0)););er(f)}if(we(r+216|0),(i=c[r+196>>2])&&(c[r+200>>2]=i,er(i)),(i=c[r+184>>2])&&(c[r+188>>2]=i,er(i)),(i=c[r+172>>2])&&(c[r+176>>2]=i,er(i)),(i=c[r+160>>2])&&(c[r+164>>2]=i,er(i)),t=c[r+144>>2])for(;i=c[t>>2],er(t),t=i,i;);return i=c[r+136>>2],c[r+136>>2]=0,i&&er(i),(i=c[r+120>>2])&&er(i),(i=c[r+108>>2])&&er(i),(i=c[r+96>>2])&&er(i),(i=c[r+72>>2])&&(c[r+76>>2]=i,er(i)),(i=c[r+60>>2])&&er(i),(i=c[r+48>>2])&&(c[r+52>>2]=i,er(i)),(i=c[r+36>>2])&&(c[r+40>>2]=i,er(i)),(i=c[r+24>>2])&&(c[r+28>>2]=i,er(i)),(i=c[r+12>>2])&&(c[r+16>>2]=i,er(i)),i=c[r+8>>2],c[r+8>>2]=0,i&&ge(i),0|r}function Br(r,e){var i=0,t=0;i=c[e+8>>2],c[r+4>>2]=c[e+4>>2],c[r+8>>2]=i,c[r+20>>2]=c[e+20>>2],i=c[e+16>>2],c[r+12>>2]=c[e+12>>2],c[r+16>>2]=i;r:{e:{if((0|r)!=(0|e)){if(i=c[e+28>>2]){if(t=c[r+24>>2],c[r+32>>2]<<5>>>0<i>>>0){if(t&&(er(t),c[r+32>>2]=0,c[r+24>>2]=0,c[r+28>>2]=0,i=c[e+28>>2]),(0|i)<0)break e;t=vi((i=1+(i-1>>>5|0)|0)<<2),c[r+32>>2]=i,c[r+28>>2]=0,c[r+24>>2]=t,i=c[e+28>>2]}gr(t,c[e+24>>2],4+(i-1>>>3&536870908)|0),i=c[e+28>>2]}else i=0;if(c[r+28>>2]=i,i=c[e+40>>2]){if(t=c[r+36>>2],c[r+44>>2]<<5>>>0<i>>>0){if(t&&(er(t),c[r+44>>2]=0,c[r+36>>2]=0,c[r+40>>2]=0,i=c[e+40>>2]),(0|i)<0)break r;t=vi((i=1+(i-1>>>5|0)|0)<<2),c[r+44>>2]=i,c[r+40>>2]=0,c[r+36>>2]=t,i=c[e+40>>2]}gr(t,c[e+36>>2],4+(i-1>>>3&536870908)|0),e=c[e+40>>2]}else e=0;c[r+40>>2]=e}return}mt(),o()}mt(),o()}function Mr(r){var e,i=0,t=0;i=c[r+8>>2],e=c[r>>2];r:{if(k[r+12|0]){e:{if(-1!=(0|i)&&-1!=(0|(i=((t=i+1|0)>>>0)%3|0?t:i-2|0))&&-1!=(0|(i=c[c[e+12>>2]+(i<<2)>>2]))){if(i=((t=i+1|0)>>>0)%3|0?t:i-2|0,c[r+8>>2]=i,-1!=(0|i))break e}else c[r+8>>2]=-1;i=-1;i:if(-1!=(0|(t=c[r+4>>2]))){if((t>>>0)%3|0)t=t-1|0;else if(i=-1,-1==(0|(t=t+2|0)))break i;i=-1,-1!=(0|(t=c[c[e+12>>2]+(t<<2)>>2]))&&(i=t-1|0,(t>>>0)%3|0||(i=t+2|0))}return n[r+12|0]=0,void(c[r+8>>2]=i)}if((0|i)!=c[r+4>>2])break r;return void(c[r+8>>2]=-1)}t=-1;e:if(-1!=(0|i)){if((i>>>0)%3|0)i=i-1|0;else if(t=-1,-1==(0|(i=i+2|0)))break e;t=-1,-1!=(0|(i=c[c[e+12>>2]+(i<<2)>>2]))&&(t=i-1|0,(i>>>0)%3|0||(t=i+2|0))}c[r+8>>2]=t}}function Qr(r){var e,i=0,t=0;e=vi(32),n[e+26|0]=0,i=k[1475]|k[1476]<<8,n[e+24|0]=i,n[e+25|0]=i>>>8,i=k[1471]|k[1472]<<8|k[1473]<<16|k[1474]<<24,t=k[1467]|k[1468]<<8|k[1469]<<16|k[1470]<<24,n[e+16|0]=t,n[e+17|0]=t>>>8,n[e+18|0]=t>>>16,n[e+19|0]=t>>>24,n[e+20|0]=i,n[e+21|0]=i>>>8,n[e+22|0]=i>>>16,n[e+23|0]=i>>>24,i=k[1463]|k[1464]<<8|k[1465]<<16|k[1466]<<24,t=k[1459]|k[1460]<<8|k[1461]<<16|k[1462]<<24,n[e+8|0]=t,n[e+9|0]=t>>>8,n[e+10|0]=t>>>16,n[e+11|0]=t>>>24,n[e+12|0]=i,n[e+13|0]=i>>>8,n[e+14|0]=i>>>16,n[e+15|0]=i>>>24,i=k[1455]|k[1456]<<8|k[1457]<<16|k[1458]<<24,t=k[1451]|k[1452]<<8|k[1453]<<16|k[1454]<<24,n[0|e]=t,n[e+1|0]=t>>>8,n[e+2|0]=t>>>16,n[e+3|0]=t>>>24,n[e+4|0]=i,n[e+5|0]=i>>>8,n[e+6|0]=i>>>16,n[e+7|0]=i>>>24,c[r>>2]=-1,me(r+4|0,e,26),er(e)}function gr(r,e,i){var t=0,f=0;r:if((0|r)!=(0|e)){if(e-(f=r+i|0)>>>0<=0-(i<<1)>>>0)return hr(r,e,i);if(t=3&(r^e),r>>>0<e>>>0){if(t)t=r;else{if(3&r)for(t=r;;){if(!i)break r;if(n[0|t]=k[0|e],e=e+1|0,i=i-1|0,!(3&(t=t+1|0)))break}else t=r;if(!(i>>>0<=3))for(;c[t>>2]=c[e>>2],e=e+4|0,t=t+4|0,(i=i-4|0)>>>0>3;);}if(i)for(;n[0|t]=k[0|e],t=t+1|0,e=e+1|0,i=i-1|0;);}else{if(!t){if(3&f)for(;;){if(!i)break r;if(n[0|(t=(i=i-1|0)+r|0)]=k[e+i|0],!(3&t))break}if(!(i>>>0<=3))for(;c[(i=i-4|0)+r>>2]=c[e+i>>2],i>>>0>3;);}if(!i)break r;for(;n[(i=i-1|0)+r|0]=k[e+i|0],i;);}}return r}function Or(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,b=0;if((t=c[r+8>>2])-(i=c[r+4>>2])>>2>>>0>=e>>>0)return e&&(i=Sr(i,0,e<<=2)+e|0),void(c[r+4>>2]=i);r:{e:{i:{if((f=(a=i-(n=c[r>>2])>>2)+e|0)>>>0<1073741824){if(A=(t=t-n|0)>>>1|0,f=t>>>0>=2147483644?1073741823:f>>>0<A>>>0?A:f){if(f>>>0>=1073741824)break i;b=vi(f<<2)}if(a=(e=Sr(t=(a<<2)+b|0,0,a=e<<2))+a|0,f=(f<<2)+b|0,(0|i)==(0|n))break e;for(;e=c[(i=i-4|0)>>2],c[i>>2]=0,c[(t=t-4|0)>>2]=e,(0|i)!=(0|n););if(c[r+8>>2]=f,e=c[r+4>>2],c[r+4>>2]=a,i=c[r>>2],c[r>>2]=t,(0|e)==(0|i))break r;for(;r=c[(e=e-4|0)>>2],c[e>>2]=0,r&&Zt[c[c[r>>2]+4>>2]](r),(0|e)!=(0|i););break r}mt(),o()}Zi(),o()}c[r+8>>2]=f,c[r+4>>2]=a,c[r>>2]=e}i&&er(i)}function Cr(r,e){r|=0;var i,t,f,a,n=0,A=0,o=0,b=0,u=0,_=0;return o=c[(e|=0)+8>>2],i=A=c[e+12>>2],a=A=c[e+20>>2],f=n=(t=c[e+16>>2])+4|0,n>>>0>o>>>0&(0|(A=n>>>0<4?A+1|0:A))>=(0|i)|(0|A)>(0|i)||(u=c[e>>2],b=k[0|(n=u+t|0)]|k[n+1|0]<<8|k[n+2|0]<<16|k[n+3|0]<<24,c[e+16>>2]=f,c[e+20>>2]=A,n=o,o=a,n>>>0<(A=t+8|0)>>>0&(0|(o=A>>>0<8?o+1|0:o))>=(0|i)|(0|o)>(0|i)||(n=k[0|(n=f+u|0)]|k[n+1|0]<<8|k[n+2|0]<<16|k[n+3|0]<<24,c[e+16>>2]=A,c[e+20>>2]=o,(0|n)<(0|b)||(c[r+16>>2]=n,c[r+12>>2]=b,!(A=(n>>31)-((b>>31)+(n>>>0<b>>>0)|0)|0)&(o=n-b|0)>>>0>2147483646|A||(A=o+1|0,c[r+20>>2]=A,o=A>>>1|0,c[r+24>>2]=o,c[r+28>>2]=0-o,1&A||(c[r+24>>2]=o-1),_=Er(r+112|0,e))))),0|_}function zr(r,e){var i=0,t=0,f=0,a=0;t=-1,f=-1,a=-1;r:{e:if(-1!=(0|e)){if(f=c[c[c[r+4>>2]+12>>2]+(e<<2)>>2],(0|(i=((i=e+1|0)>>>0)%3|0?i:e-2|0))>=0&&(a=(i>>>0)/3|0,a=c[(c[c[r>>2]+96>>2]+y(a,12)|0)+(i-y(a,3)<<2)>>2]),-1!=(0|f)&&((0|(i=((f>>>0)%3|0?-1:2)+f|0))<0||(t=(i>>>0)/3|0,t=c[(c[c[r>>2]+96>>2]+y(t,12)|0)+(i-y(t,3)<<2)>>2])),i=-1,(0|t)!=(0|a))break r;if(a=-1,(0|(e=((e>>>0)%3|0?-1:2)+e|0))>=0){if(t=(e>>>0)/3|0,t=c[(c[c[r>>2]+96>>2]+y(t,12)|0)+(e-y(t,3)<<2)>>2],-1==(0|f))break e}else if(t=-1,-1==(0|f))break e;(0|(e=((e=f+1|0)>>>0)%3|0?e:f-2|0))<0||(i=c[c[r>>2]+96>>2],a=c[(i+y(r=(e>>>0)/3|0,12)|0)+(e-y(r,3)<<2)>>2])}i=(0|t)!=(0|a)?-1:f}return i}function Xr(r,e){var i,t,f=0;Z=i=Z+-64|0,f=c[r>>2],t=c[f-4>>2],f=c[f-8>>2],c[i+32>>2]=0,c[i+36>>2]=0,c[i+40>>2]=0,c[i+44>>2]=0,c[i+48>>2]=0,c[i+52>>2]=0,n[i+55|0]=0,n[i+56|0]=0,n[i+57|0]=0,n[i+58|0]=0,n[i+59|0]=0,n[i+60|0]=0,n[i+61|0]=0,n[i+62|0]=0,c[i+24>>2]=0,c[i+28>>2]=0,c[i+20>>2]=0,c[i+16>>2]=11020,c[i+12>>2]=r,c[i+8>>2]=e,r=r+f|0,f=0;r:if(Be(t,e,0))c[i+56>>2]=1,Zt[c[c[t>>2]+20>>2]](t,i+8|0,r,r,1,0),f=1==c[i+32>>2]?r:0;else{Zt[c[c[t>>2]+24>>2]](t,i+8|0,r,1,0);e:switch(c[i+44>>2]){case 0:f=1==c[i+48>>2]&&1==c[i+36>>2]&&1==c[i+40>>2]?c[i+28>>2]:0;break r;case 1:break e;default:break r}1!=c[i+32>>2]&&c[i+48>>2]|1!=c[i+36>>2]|1!=c[i+40>>2]||(f=c[i+24>>2])}return Z=i- -64|0,f}function Sr(r,e,i){var t=0,f=0,a=0,A=0;if(i&&(n[0|r]=e,n[(t=r+i|0)-1|0]=e,!(i>>>0<3||(n[r+2|0]=e,n[r+1|0]=e,n[t-3|0]=e,n[t-2|0]=e,i>>>0<7||(n[r+3|0]=e,n[t-4|0]=e,i>>>0<9||(f=(t=0-r&3)+r|0,e=y(255&e,16843009),c[f>>2]=e,c[(i=(t=i-t&-4)+f|0)-4>>2]=e,t>>>0<9||(c[f+8>>2]=e,c[f+4>>2]=e,c[i-8>>2]=e,c[i-12>>2]=e,t>>>0<25||(c[f+24>>2]=e,c[f+20>>2]=e,c[f+16>>2]=e,c[f+12>>2]=e,c[i-16>>2]=e,c[i-20>>2]=e,c[i-24>>2]=e,c[i-28>>2]=e,(i=t-(A=4&f|24)|0)>>>0<32))))))))for(t=$e(e,0,1,1),a=E,e=f+A|0;c[e+24>>2]=t,c[e+28>>2]=a,c[e+16>>2]=t,c[e+20>>2]=a,c[e+8>>2]=t,c[e+12>>2]=a,c[e>>2]=t,c[e+4>>2]=a,e=e+32|0,(i=i-32|0)>>>0>31;);return r}function Hr(r,e,i){var t,f,a=0,n=0,A=0,o=0,b=0,u=0,_=0;Z=t=Z-16|0;r:{if((0|(n=c[r+24>>2]))!=(0|(f=c[r+28>>2])))for(;;){c[t+8>>2]=0,c[t>>2]=0,c[t+4>>2]=0,r=ti(c[n>>2],e,t),o=(A=k[t+11|0])<<24>>24,b=3;e:{i:{t:if(r&&(b=0,a=(r=k[i+11|0])<<24>>24,(0|(u=(0|o)<0?c[t+4>>2]:A))==(0|((0|a)<0?c[i+4>>2]:r)))){r=(0|a)<0?c[i>>2]:i;f:if(a=(0|o)<0){if(u&&Ye(a?c[t>>2]:t,r,u))break i}else{if(a=t,!o)break f;for(;;){if(k[0|a]!=k[0|r])break t;if(r=r+1|0,a=a+1|0,!(A=A-1|0))break}}_=c[n>>2],b=1}if((0|o)>=0)break e}er(c[t>>2])}e:switch(0|b){case 0:case 3:break e;default:break r}if((0|f)==(0|(n=n+4|0)))break}_=0}return Z=t+16|0,_}function Pr(r,e,i){var t,f=0,a=0,n=0,A=0,b=0;if((t=(n=i-e|0)>>2)>>>0<=(f=c[r+8>>2])-(a=c[r>>2])>>2>>>0){if(n=(A=(f=c[r+4>>2])-a|0)+e|0,(0|(A=(b=A>>2)>>>0<t>>>0?n:i))!=(0|e))for(;c[a>>2]=c[e>>2],a=a+4|0,(0|A)!=(0|(e=e+4|0)););if(t>>>0>b>>>0){if((0|i)!=(0|A))for(;c[f>>2]=c[n>>2],f=f+4|0,(0|(n=n+4|0))!=(0|i););return void(c[r+4>>2]=f)}c[r+4>>2]=a}else{if(a&&(c[r+4>>2]=a,er(a),c[r+8>>2]=0,c[r>>2]=0,c[r+4>>2]=0,f=0),!((0|n)<0||(a=f>>>1|0,(f=f>>>0>=2147483644?1073741823:a>>>0>t>>>0?a:t)>>>0>=1073741824)))return f=vi(a=f<<2),c[r>>2]=f,c[r+8>>2]=f+a,(0|e)!=(0|i)&&(f=hr(f,i=e,e=4+(n-4&-4)|0)+e|0),void(c[r+4>>2]=f);mt(),o()}}function xr(r,e,i){var t,f=0,a=0;Z=t=Z-16|0,c[r+4>>2]=0;r:{e:if(e){if((f=(a=c[r+8>>2])<<5)>>>0>=e>>>0)c[r+4>>2]=e;else{if(c[t+8>>2]=0,c[t>>2]=0,c[t+4>>2]=0,(0|e)<0)break r;nr(t,f=f>>>0<=1073741822?(f=e+31&-32)>>>0<(a<<=6)>>>0?a:f:2147483647),a=c[r>>2],c[r>>2]=c[t>>2],c[t>>2]=a,f=c[r+4>>2],c[r+4>>2]=e,c[t+4>>2]=f,f=c[r+8>>2],c[r+8>>2]=c[t+8>>2],c[t+8>>2]=f,a&&er(a)}if(f=e>>>5|0,r=c[r>>2],k[0|i]){if(e>>>0>=32&&Sr(r,255,f<<2),(-32&e)==(0|e))break e;c[(r=r+(f<<2)|0)>>2]=c[r>>2]|-1>>>32-(31&e)}else e>>>0>=32&&Sr(r,0,f<<2),(-32&e)!=(0|e)&&(c[(r=r+(f<<2)|0)>>2]=c[r>>2]&(-1>>>32-(31&e)^-1))}return void(Z=t+16|0)}mt(),o()}function Lr(r,e){var i=0,t=0,f=0,a=0,n=0,A=0;if((0|(i=c[r+4>>2]))!=c[r+8>>2])return f=c[e+4>>2],c[i>>2]=c[e>>2],c[i+4>>2]=f,c[i+8>>2]=c[e+8>>2],void(c[r+4>>2]=i+12);r:{if((f=(t=(i-(n=c[r>>2])|0)/12|0)+1|0)>>>0<357913942){if(a=t<<1,a=t>>>0>=178956970?357913941:f>>>0<a>>>0?a:f){if(a>>>0>=357913942)break r;f=vi(y(a,12))}else f=0;if(t=f+y(t,12)|0,A=c[e+4>>2],c[t>>2]=c[e>>2],c[t+4>>2]=A,c[t+8>>2]=c[e+8>>2],e=t+12|0,(0|i)!=(0|n)){for(;A=c[(i=i-12|0)+4>>2],c[(t=t-12|0)>>2]=c[i>>2],c[t+4>>2]=A,c[t+8>>2]=c[i+8>>2],(0|i)!=(0|n););i=c[r>>2]}return c[r+8>>2]=f+y(a,12),c[r+4>>2]=e,c[r>>2]=t,void(i&&er(i))}mt(),o()}Zi(),o()}function Kr(r,e,i,t,f,a,A){var b,u=0,_=0;if(Z=b=Z-16|0,2147483631+(-1^e)>>>0>=i>>>0)return u=k[r+11|0]>>>7|0?c[r>>2]:r,e>>>0<1073741799?(c[b+12>>2]=e<<1,c[b>>2]=e+i,Z=i=Z-16|0,Z=i+16|0,i=b+12|0,i=(i=(i=c[(s[b>>2]<s[i>>2]?i:b)>>2])>>>0>=11?11==(0|(i=(_=i+16&-16)-1|0))?_:i:10)+1|0):i=2147483631,Ci(b,i),i=c[b>>2],a&&le(i,A,a),A=t-f|0,(0|t)!=(0|f)&&le(i+a|0,f+u|0,A),10!=(0|e)&&er(u),c[r>>2]=i,c[r+8>>2]=-2147483648&c[r+8>>2]|2147483647&c[b+4>>2],c[r+8>>2]=-2147483648|c[r+8>>2],e=r,r=a+A|0,c[e+4>>2]=r,n[b+12|0]=0,n[r+i|0]=k[b+12|0],void(Z=b+16|0);yt(),o()}function qr(r,e,i){var t=0,f=0,a=0,n=0;r:{if(a=e>>>0<1431655766&(e|i)>=0){if(dr(r,e=y(e,3),10224),dr(r+12|0,e,10228),t=c[r+24>>2],!(c[r+32>>2]-t>>2>>>0>=i>>>0)){if(i>>>0>=1073741824)break r;if(e=c[r+28>>2],f=(i=vi(f=i<<2))+f|0,i=n=i+(e-t&-4)|0,(0|e)!=(0|t))for(;e=e-4|0,c[(i=i-4|0)>>2]=c[e>>2],(0|e)!=(0|t););c[r+32>>2]=f,c[r+28>>2]=n,c[r+24>>2]=i,t&&er(t)}c[r+80>>2]=0,c[r+84>>2]=0,e=c[r+76>>2],c[r+76>>2]=0,e&&er(e),c[r+68>>2]=0,c[r+72>>2]=0,r=c[(e=r- -64|0)>>2],c[e>>2]=0,r&&er(r)}return a}mt(),o()}function $r(r){var e=0,i=0,t=0,f=0,a=0;a=1;r:if(!((0|(i=c[r+140>>2]))<=0)){for(e=i<<4,t=vi(i>>>0>268435455?-1:4|e),c[t>>2]=i,i=(t=t+4|0)+e|0,e=t;c[e>>2]=0,c[e+4>>2]=0,n[e+5|0]=0,n[e+6|0]=0,n[e+7|0]=0,n[e+8|0]=0,n[e+9|0]=0,n[e+10|0]=0,n[e+11|0]=0,n[e+12|0]=0,(0|i)!=(0|(e=e+16|0)););if(f=c[r+136>>2],c[r+136>>2]=t,f){if(t=c[(i=f-4|0)>>2])for(e=(t<<4)+f|0;(0|f)!=(0|(e=e-16|0)););er(i)}if(e=0,!(c[r+140>>2]<=0))for(;;){if(!(a=Er(c[r+136>>2]+(e<<4)|0,r)))break r;if(!((0|(e=e+1|0))<c[r+140>>2]))break}}return a}function re(r,e){r|=0;var i,t,f,a=0,n=0,A=0,o=0,b=0;return n=c[(e|=0)+8>>2],i=a=c[e+12>>2],f=a=c[e+20>>2],n>>>0<(o=(t=c[e+16>>2])+4|0)>>>0&(0|(a=o>>>0<4?a+1|0:a))>=(0|i)|(0|a)>(0|i)||(A=t+c[e>>2]|0,A=k[0|A]|k[A+1|0]<<8|k[A+2|0]<<16|k[A+3|0]<<24,c[e+16>>2]=o,c[e+20>>2]=a,o=n,n=f,(a=t+8|0)>>>0>o>>>0&(0|(n=a>>>0<8?n+1|0:n))>=(0|i)|(0|n)>(0|i)||(c[e+16>>2]=a,c[e+20>>2]=n,1&A&&((n=31^v(A))-1>>>0>28||(c[r+8>>2]=n+1,a=-2^(n=-2<<n),c[r+16>>2]=a,c[r+12>>2]=-1^n,c[r+24>>2]=a>>1,p[r+20>>2]=d(2)/d(0|a),b=Er(r+96|0,e))))),0|b}function ee(r,e){var i=0;return i=c[e+4>>2],c[r>>2]=c[e>>2],c[r+4>>2]=i,i=c[e+60>>2],c[r+56>>2]=c[e+56>>2],c[r+60>>2]=i,i=c[e+52>>2],c[r+48>>2]=c[e+48>>2],c[r+52>>2]=i,i=c[e+44>>2],c[r+40>>2]=c[e+40>>2],c[r+44>>2]=i,i=c[e+36>>2],c[r+32>>2]=c[e+32>>2],c[r+36>>2]=i,i=c[e+28>>2],c[r+24>>2]=c[e+24>>2],c[r+28>>2]=i,i=c[e+20>>2],c[r+16>>2]=c[e+16>>2],c[r+20>>2]=i,i=c[e+12>>2],c[r+8>>2]=c[e+8>>2],c[r+12>>2]=i,c[r+88>>2]=0,c[r+64>>2]=0,c[r+68>>2]=0,c[r+72>>2]=0,c[r+76>>2]=0,n[r+77|0]=0,n[r+78|0]=0,n[r+79|0]=0,n[r+80|0]=0,n[r+81|0]=0,n[r+82|0]=0,n[r+83|0]=0,n[r+84|0]=0,r}function ie(r,e){var i,t,f=0,a=0,n=0;return c[r+64>>2]||(f=vi(32),c[f+16>>2]=0,c[f+20>>2]=0,c[f+8>>2]=0,c[f>>2]=0,c[f+4>>2]=0,c[f+24>>2]=0,c[f+28>>2]=0,a=c[r+64>>2],c[r+64>>2]=f,a&&((f=c[a>>2])&&(c[a+4>>2]=f,er(f)),er(a))),a=c[r+64>>2],f=(f=c[r+28>>2]-1|0)>>>0<=10?c[10148+(f<<2)>>2]:-1,(t=Dr(a,0,$e(f=y(f,k[r+24|0]),i=f>>31,e,0),E))&&(a=c[r+64>>2],c[r>>2]=a,n=c[a+20>>2],c[r+8>>2]=c[a+16>>2],c[r+12>>2]=n,n=c[a+24>>2],a=c[a+28>>2],c[r+48>>2]=0,c[r+52>>2]=0,c[r+40>>2]=f,c[r+44>>2]=i,c[r+16>>2]=n,c[r+20>>2]=a,c[r+80>>2]=e),t}function te(r,e){var i,t=0,f=0,a=0,n=0,A=0,o=0;i=r+4|0;r:{e:if(r=c[r+4>>2]){for(n=(t=(f=k[e+11|0])<<24>>24<0)?c[e>>2]:e,f=t?c[e+4>>2]:f,e=i;(o=(A=(a=(t=(a=k[r+27|0])<<24>>24<0)?c[r+20>>2]:a)>>>0>f>>>0)?f:a)&&(t=Ye(t?c[r+16>>2]:r+16|0,n,o))||(t=f>>>0>a>>>0?-1:A),e=(t=(0|t)<0)?e:r,r=c[(t?r+4|0:r)>>2];);if((0|e)!=(0|i)){if(!(a=(t=(r=(t=k[e+27|0])<<24>>24<0)?c[e+20>>2]:t)>>>0<f>>>0?t:f)||!(r=Ye(n,r?c[e+16>>2]:e+16|0,a))){if(t>>>0>f>>>0)break e;break r}if((0|r)>=0)break r}}e=i}return e}function fe(r,e){e|=0;var i=0,t=0,f=0,a=0;if((0|(i=c[(r|=0)+216>>2]))!=c[r+220>>2])for(;;){r:if(!((0|(i=c[y(f,144)+i>>2]))<0||(t=c[r+4>>2],a=c[t+8>>2],(0|i)>=c[t+12>>2]-a>>2||(t=0,i=c[(i<<2)+a>>2],(0|Zt[c[c[i>>2]+24>>2]](i))<=0)))){for(;;){if((0|Zt[c[c[i>>2]+20>>2]](i,t))!=(0|e)){if(t=t+1|0,(0|Zt[c[c[i>>2]+24>>2]](i))>(0|t))continue;break r}break}return r=c[r+216>>2]+y(f,144)|0,0|(k[r+100|0]?r+4|0:0)}if(f=f+1|0,i=c[r+216>>2],!(f>>>0<(c[r+220>>2]-i|0)/144>>>0))break}return 0}function ae(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,b=0;if((i=c[r+8>>2])-(t=c[r+4>>2])>>2>>>0>=e>>>0)return e&&(t=Sr(t,0,e<<=2)+e|0),void(c[r+4>>2]=t);r:{if((f=(n=t-(a=c[r>>2])>>2)+e|0)>>>0<1073741824){if(A=(i=i-a|0)>>>1|0,f=i>>>0>=2147483644?1073741823:f>>>0<A>>>0?A:f){if(f>>>0>=1073741824)break r;b=vi(f<<2)}if(e=Sr(i=(n<<2)+b|0,0,e<<=2)+e|0,(0|t)!=(0|a))for(;t=t-4|0,c[(i=i-4|0)>>2]=c[t>>2],(0|t)!=(0|a););return c[r+8>>2]=(f<<2)+b,c[r+4>>2]=e,c[r>>2]=i,void(a&&er(a))}mt(),o()}Zi(),o()}function ne(r){var e=0,i=0,t=0,f=0,a=0;if(t=c[r+8>>2],!k[t+84|0]&&!(!(e=c[r+16>>2])|!k[e+84|0]||(i=c[t+72>>2],f=c[t+68>>2],n[e+84|0]=0,i=i-f>>2,a=c[e+68>>2],i>>>0>(f=c[e+72>>2]-a>>2)>>>0?(yr(e+68|0,i-f|0,2004),t=c[r+8>>2]):i>>>0>=f>>>0||(c[e+72>>2]=a+(i<<2)),k[t+84|0]||(0|(i=c[t+68>>2]))==c[t+72>>2])))for(f=c[c[r+16>>2]+68>>2],e=0;c[(a=e<<2)+f>>2]=c[i+a>>2],e=e+1|0,i=c[t+68>>2],e>>>0<c[t+72>>2]-i>>2>>>0;);return c[r+16>>2]}function Ae(r,e){e|=0;var i=0,t=0,f=0,a=0;if((0|(i=c[(r|=0)+216>>2]))!=c[r+220>>2])for(;;){r:if(!((0|(i=c[y(f,144)+i>>2]))<0||(t=c[r+4>>2],a=c[t+8>>2],(0|i)>=c[t+12>>2]-a>>2||(t=0,i=c[(i<<2)+a>>2],(0|Zt[c[c[i>>2]+24>>2]](i))<=0)))){for(;;){if((0|Zt[c[c[i>>2]+20>>2]](i,t))!=(0|e)){if(t=t+1|0,(0|Zt[c[c[i>>2]+24>>2]](i))>(0|t))continue;break r}break}return 104+(c[r+216>>2]+y(f,144)|0)|0}if(f=f+1|0,i=c[r+216>>2],!(f>>>0<(c[r+220>>2]-i|0)/144>>>0))break}return r+184|0}function oe(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,b=0;r:{e:if((t=(i=c[r+4>>2])-(f=c[r>>2])|0)>>>0<e>>>0){if((n=e-t|0)>>>0<=(a=c[r+8>>2])-i>>>0){A=r,b=Sr(i,0,n)+n|0,c[A+4>>2]=b;break e}if((0|e)<0)break r;if(a=(i=a-f|0)<<1,Sr((a=vi(i=i>>>0>=1073741823?2147483647:e>>>0<a>>>0?a:e))+t|0,0,n),t=gr(a,f,t),c[r+8>>2]=t+i,c[r+4>>2]=e+t,c[r>>2]=t,!f)break e;er(f)}else e>>>0>=t>>>0||(c[r+4>>2]=e+f);return i=e=c[r+28>>2],t=e+1|0,f=(e=c[r+24>>2]+1|0)?i:t,c[r+24>>2]=e,void(c[r+28>>2]=f)}mt(),o()}function be(r,e){var i=0,t=0,f=0,a=0,n=0,A=0;if((0|(f=c[r+4>>2]))!=c[r+8>>2])return c[f>>2]=c[e>>2],void(c[r+4>>2]=f+4);r:{if((t=(i=(a=f-(n=c[r>>2])|0)>>2)+1|0)>>>0<1073741824){if(A=i<<2,i=a>>>1|0,i=a>>>0>=2147483644?1073741823:i>>>0>t>>>0?i:t){if(i>>>0>=1073741824)break r;a=vi(i<<2)}else a=0;if(c[(t=A+a|0)>>2]=c[e>>2],e=t+4|0,(0|f)!=(0|n))for(;f=f-4|0,c[(t=t-4|0)>>2]=c[f>>2],(0|f)!=(0|n););return c[r+8>>2]=a+(i<<2),c[r+4>>2]=e,c[r>>2]=t,void(n&&er(n))}mt(),o()}Zi(),o()}function ue(r){c[r>>2]=-1,c[r+4>>2]=0,c[r+8>>2]=0,c[r+32>>2]=0,c[r+36>>2]=0,n[r+28|0]=1,c[r+20>>2]=0,c[r+24>>2]=0,c[r+12>>2]=0,c[r+16>>2]=0,c[r+40>>2]=0,c[r+44>>2]=0,c[r+48>>2]=0,c[r+52>>2]=0,c[r+56>>2]=0,c[r+60>>2]=0,c[r+64>>2]=0,c[r+68>>2]=0,c[r+76>>2]=0,c[r+80>>2]=0,c[r+84>>2]=0,c[r+88>>2]=0,c[r+92>>2]=0,c[r+96>>2]=0,c[r+72>>2]=r+4,c[r+104>>2]=0,c[r+108>>2]=0,n[r+100|0]=1,c[r+112>>2]=0,c[r+116>>2]=0,c[r+120>>2]=0,c[r+124>>2]=0,c[r+128>>2]=0,c[r+132>>2]=0,c[r+136>>2]=0,c[r+140>>2]=0}function ce(r,e){var i=0,t=0,f=0,a=0;t=c[r+12>>2],(i=c[r+16>>2]-t>>2)>>>0<e>>>0?_e(r+12|0,e-i|0):e>>>0>=i>>>0||(c[r+16>>2]=t+(e<<2));r:{if(i=c[r>>2],!(c[r+8>>2]-i>>2>>>0>=e>>>0)){if(e>>>0>=1073741824)break r;if(t=c[r+4>>2],f=(e=vi(f=e<<2))+f|0,e=a=e+(t-i&-4)|0,(0|i)!=(0|t))for(;t=t-4|0,c[(e=e-4|0)>>2]=c[t>>2],(0|i)!=(0|t););c[r+8>>2]=f,c[r+4>>2]=a,c[r>>2]=e,i&&er(i)}return}mt(),o()}function ke(r){var e=0,i=0,t=0;if(c[(r|=0)>>2]=10300,(e=c[r+68>>2])&&(c[r+72>>2]=e,er(e)),(e=c[r+56>>2])&&(c[r+60>>2]=e,er(e)),(e=c[r+44>>2])&&(c[r+48>>2]=e,er(e)),(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),(e=c[r+20>>2])&&(c[r+24>>2]=e,er(e)),e=c[r+8>>2]){if(t=e,(0|e)!=(0|(i=c[r+12>>2]))){for(;t=c[(i=i-4|0)>>2],c[i>>2]=0,t&&xe(t),(0|e)!=(0|i););t=c[r+8>>2]}c[r+12>>2]=e,er(t)}return e=c[r+4>>2],c[r+4>>2]=0,e&&je(e),0|r}function _e(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,b=0;if((f=c[r+8>>2])-(i=c[r+4>>2])>>2>>>0>=e>>>0)return e&&(i=Sr(i,0,e<<=2)+e|0),void(c[r+4>>2]=i);r:{if((t=(A=(n=(a=i)-(i=c[r>>2])|0)>>2)+e|0)>>>0<1073741824){if(a=(f=f-i|0)>>>1|0,t=f>>>0>=2147483644?1073741823:t>>>0<a>>>0?a:t){if(t>>>0>=1073741824)break r;b=vi(t<<2)}return f=Sr((A<<2)+b|0,0,e<<=2),a=t<<2,t=gr(b,i,n),c[r+8>>2]=a+t,c[r+4>>2]=e+f,c[r>>2]=t,void(i&&er(i))}mt(),o()}Zi(),o()}function se(r,e){var i=0,t=0,f=0,a=0;if((0|(i=r+4|0))!=(0|(r=te(r,e)))){for(e=r+28|0,e=n[r+39|0]<0?c[e>>2]:e;e=(r=e)+1|0,32==(0|(i=n[0|r]))|i-9>>>0<5;);r:{e:{i:switch((i=n[0|r])-43|0){case 0:break e;case 2:break i;default:break r}f=1}i=n[0|e],r=e}if(i-48>>>0<10)for(;t=48+(y(t,10)-n[0|r]|0)|0,e=n[r+1|0],r=r+1|0,e-48>>>0<10;);-1!=(0|(r=f?t:0-t|0))&&(a=0!=(0|r))}return a}function pe(r,e){var i=0,t=0,f=0,a=0,n=0,A=0;if(r=c[r>>2],(i=c[r+4>>2])>>>0<(f=c[r+8>>2])>>>0)return c[i>>2]=c[e>>2],void(c[r+4>>2]=i+4);r:{if((a=(t=(n=(t=i)-(i=c[r>>2])|0)>>2)+1|0)>>>0<1073741824){if(A=t<<2,t=(f=f-i|0)>>>1|0,a=f>>>0>=2147483644?1073741823:a>>>0<t>>>0?t:a){if(a>>>0>=1073741824)break r;f=vi(a<<2)}else f=0;return c[(t=A+f|0)>>2]=c[e>>2],e=gr(f,i,n),c[r+8>>2]=e+(a<<2),c[r+4>>2]=t+4,c[r>>2]=e,void(i&&er(i))}mt(),o()}Zi(),o()}function le(r,e,i){var t,f,a,n,A=0;Z=A=(Z=t=(Z=f=Z-16|0)-32|0)-16|0,c[A+12>>2]=e,c[A+8>>2]=e+i,c[t+24>>2]=c[A+12>>2],c[t+28>>2]=c[A+8>>2],Z=i=(Z=A+16|0)-16|0,a=(n=c[t+28>>2])-(A=c[t+24>>2])|0,(0|A)!=(0|n)&&gr(r,A,a),c[i+12>>2]=A+a,c[i+8>>2]=r+a,c[t+16>>2]=c[i+12>>2],c[t+20>>2]=c[i+8>>2],Z=i+16|0,c[t+12>>2]=(c[t+16>>2]-e|0)+e,c[t+8>>2]=(c[t+20>>2]-r|0)+r,c[f+8>>2]=c[t+12>>2],c[f+12>>2]=c[t+8>>2],Z=t+32|0,Z=f+16|0}function ye(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,b=0;if((f=c[r+8>>2])-(i=c[r+4>>2])>>3>>>0>=e>>>0)return e&&(i=Sr(i,0,e<<=3)+e|0),void(c[r+4>>2]=i);r:{if((t=(A=(n=(a=i)-(i=c[r>>2])|0)>>3)+e|0)>>>0<536870912){if(a=(f=f-i|0)>>>2|0,t=f>>>0>=2147483640?536870911:t>>>0<a>>>0?a:t){if(t>>>0>=536870912)break r;b=vi(t<<3)}return f=Sr((A<<3)+b|0,0,e<<=3),a=t<<3,t=gr(b,i,n),c[r+8>>2]=a+t,c[r+4>>2]=e+f,c[r>>2]=t,void(i&&er(i))}mt(),o()}Zi(),o()}function de(r,e){var i=0,t=0,f=0,a=0,n=0,A=0;if((f=c[r+8>>2])-(i=c[r+4>>2])>>1>>>0>=e>>>0)return e&&(i=Sr(i,0,e<<=1)+e|0),void(c[r+4>>2]=i);r:{if((0|(t=(a=(n=(a=i)-(i=c[r>>2])|0)>>1)+e|0))>=0){if(t=(f=f-i|0)>>>0>=2147483646?2147483647:t>>>0<f>>>0?f:t){if((0|t)<0)break r;A=vi(t<<1)}return f=Sr((a<<1)+A|0,0,e<<=1),a=t<<1,t=gr(A,i,n),c[r+8>>2]=a+t,c[r+4>>2]=e+f,c[r>>2]=t,void(i&&er(i))}mt(),o()}Zi(),o()}function me(r,e,i){var t,f=0,a=0;Z=t=Z-16|0;r:{if(i>>>0<11)f=r,n[r+11|0]=128&k[r+11|0]|i,n[r+11|0]=127&k[r+11|0];else{if(i>>>0>2147483631)break r;Ci(t+8|0,(f=i>>>0>=11?11==(0|(f=(a=i+16&-16)-1|0))?a:f:10)+1|0),f=c[t+8>>2],c[r>>2]=f,c[r+8>>2]=-2147483648&c[r+8>>2]|2147483647&c[t+12>>2],c[r+8>>2]=-2147483648|c[r+8>>2],c[r+4>>2]=i}return le(f,e,i+1|0),void(Z=t+16|0)}yt(),o()}function ve(r,e){var i=0,t=0,f=0;t=(i=c[r+4>>2])+e|0,c[r+4>>2]=t,(t-1^i-1)>>>0<32&&i||(c[c[r>>2]+((t>>>0>=33?t-1>>>5|0:0)<<2)>>2]=0),e&&(r=c[r>>2]+(i>>>3&536870908)|0,(i&=31)&&(f=e>>>0>(t=32-i|0)>>>0?t:e,c[r>>2]=c[r>>2]&(-1<<i&-1>>>t-f^-1),e=e-f|0,r=r+4|0),i=e>>>5|0,e>>>0>=32&&Sr(r,0,i<<2),(-32&e)!=(0|e)&&(c[(r=(i<<2)+r|0)>>2]=c[r>>2]&(-1>>>32-(31&e)^-1)))}function he(r,e,i){var t=0,f=0,a=0,A=0;r:if(!(r>>>0>10||(0|(t=c[i+20>>2]))>=(0|(a=c[i+12>>2]))&(f=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|a))){if(a=n[f+c[i>>2]|0],t=(f=f+1|0)?t:t+1|0,c[i+16>>2]=f,c[i+20>>2]=t,(0|(t=a))<0){if(!he(r+1|0,e,i))break r;t=127&t|(r=c[e>>2])<<7,r=c[e+4>>2]<<7|r>>>25}else t&=255,r=0;c[e>>2]=t,c[e+4>>2]=r,A=1}return A}function Re(r,e,i){var t=0,f=0,a=0,A=0;r:if(!(r>>>0>10||(0|(t=c[i+20>>2]))>=(0|(a=c[i+12>>2]))&(f=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|a))){if(a=n[f+c[i>>2]|0],t=(f=f+1|0)?t:t+1|0,c[i+16>>2]=f,c[i+20>>2]=t,(0|(t=a))<0){if(!Re(r+1|0,e,i))break r;t=127&t|(r=c[e>>2])<<7,r=c[e+4>>2]<<7|r>>>25}else t&=255,r=0;c[e>>2]=t,c[e+4>>2]=r,A=1}return A}function Ne(r,e,i,t){n[r+53|0]=1;r:if(c[r+4>>2]==(0|i)){n[r+52|0]=1;e:{if(!(i=c[r+16>>2])){if(c[r+36>>2]=1,c[r+24>>2]=t,c[r+16>>2]=e,1!=(0|t))break r;if(1==c[r+48>>2])break e;break r}if((0|e)==(0|i)){if(2==(0|(i=c[r+24>>2]))&&(c[r+24>>2]=t,i=t),1!=c[r+48>>2])break r;if(1==(0|i))break e;break r}c[r+36>>2]=c[r+36>>2]+1}n[r+54|0]=1}}function Te(r,e,i){e|=0,i|=0;var t,f=0;Z=t=Z-16|0,c[(r|=0)+4>>2]=e,e=c[e+64>>2],f=c[e>>2],e=c[e+4>>2],n[t+15|0]=0,xr(r+24|0,(e-f>>2>>>0)/3|0,t+15|0),e=c[r+4>>2],f=c[e+56>>2],e=c[e+52>>2],n[t+14|0]=0,xr(r+36|0,f-e>>2,t+14|0),e=c[i+12>>2],c[r+16>>2]=c[i+8>>2],c[r+20>>2]=e,e=c[i+4>>2],c[r+8>>2]=c[i>>2],c[r+12>>2]=e,Z=t+16|0}function Ve(r,e){var i=0,t=0;r:{if((0|(i=c[r+4>>2]))==(t=c[r+8>>2])<<5){if((i+1|0)<0)break r;nr(r,i=i>>>0<=1073741822?(i=32+(-32&i)|0)>>>0<(t<<=6)>>>0?t:i:2147483647),i=c[r+4>>2]}return c[r+4>>2]=i+1,t=1<<i,r=c[r>>2]+(i>>>3&536870908)|0,k[0|e]?void(c[r>>2]=t|c[r>>2]):void(c[r>>2]=c[r>>2]&(-1^t))}mt(),o()}function Ue(r){var e;return c[r>>2]=0,c[r+4>>2]=0,c[r+56>>2]=0,c[r+48>>2]=0,c[r+52>>2]=0,c[r+40>>2]=0,c[r+44>>2]=0,c[r+32>>2]=0,c[r+36>>2]=0,c[r+24>>2]=0,c[r+28>>2]=0,c[r+16>>2]=0,c[r+20>>2]=0,c[r+8>>2]=0,c[r+12>>2]=0,c[(e=r- -64|0)>>2]=0,c[e+4>>2]=0,c[r+72>>2]=0,c[r+76>>2]=0,c[r+80>>2]=0,c[r+84>>2]=0,c[r+60>>2]=r,r}function We(r,e,i){var t=0,f=0,a=0,n=0;r:if(!(r>>>0>5||(0|(t=c[i+20>>2]))>=(0|(f=c[i+12>>2]))&(a=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|f))){if(f=k[c[i>>2]+a|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,(0|(t=f<<24>>24))<0){if(!We(r+1|0,e,i))break r;f=127&t|c[e>>2]<<7}c[e>>2]=f,n=1}return n}function De(r,e,i){var t=0,f=0,a=0,n=0;r:if(!(r>>>0>5||(0|(t=c[i+20>>2]))>=(0|(f=c[i+12>>2]))&(a=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|f))){if(f=k[c[i>>2]+a|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,(0|(t=f<<24>>24))<0){if(!De(r+1|0,e,i))break r;f=127&t|c[e>>2]<<7}c[e>>2]=f,n=1}return n}function Ge(r,e,i){var t=0,f=0,a=0,n=0;r:if(!(r>>>0>5||(0|(t=c[i+20>>2]))>=(0|(f=c[i+12>>2]))&(a=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|f))){if(f=k[c[i>>2]+a|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,(0|(t=f<<24>>24))<0){if(!Ge(r+1|0,e,i))break r;f=127&t|c[e>>2]<<7}c[e>>2]=f,n=1}return n}function Ze(r,e,i){var t=0,f=0,a=0,n=0;r:if(!(r>>>0>5||(0|(t=c[i+20>>2]))>=(0|(f=c[i+12>>2]))&(a=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|f))){if(f=k[c[i>>2]+a|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,(0|(t=f<<24>>24))<0){if(!Ze(r+1|0,e,i))break r;f=127&t|c[e>>2]<<7}c[e>>2]=f,n=1}return n}function Ee(r,e,i){var t=0,f=0,a=0,n=0;r:if(!(r>>>0>5||(0|(t=c[i+20>>2]))>=(0|(f=c[i+12>>2]))&(a=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|f))){if(f=k[c[i>>2]+a|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,(0|(t=f<<24>>24))<0){if(!Ee(r+1|0,e,i))break r;f=127&t|c[e>>2]<<7}c[e>>2]=f,n=1}return n}function Fe(r,e,i){var t=0,f=0,a=0,n=0;r:if(!(r>>>0>5||(0|(t=c[i+20>>2]))>=(0|(f=c[i+12>>2]))&(a=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|f))){if(f=k[c[i>>2]+a|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,(0|(t=f<<24>>24))<0){if(!Fe(r+1|0,e,i))break r;f=127&t|c[e>>2]<<7}c[e>>2]=f,n=1}return n}function Ie(r,e,i){var t=0,f=0,a=0,n=0;r:if(!(r>>>0>5||(0|(t=c[i+20>>2]))>=(0|(f=c[i+12>>2]))&(a=c[i+16>>2])>>>0>=s[i+8>>2]|(0|t)>(0|f))){if(f=k[c[i>>2]+a|0],t=(a=a+1|0)?t:t+1|0,c[i+16>>2]=a,c[i+20>>2]=t,(0|(t=f<<24>>24))<0){if(!Ie(r+1|0,e,i))break r;f=127&t|c[e>>2]<<7}c[e>>2]=f,n=1}return n}function Ye(r,e,i){var t=0,f=0;r:{e:{if(i>>>0>=4){if(3&(r|e))break e;for(;;){if(c[r>>2]!=c[e>>2])break e;if(e=e+4|0,r=r+4|0,!((i=i-4|0)>>>0>3))break}}if(!i)break r}for(;;){if((0|(t=k[0|r]))==(0|(f=k[0|e]))){if(e=e+1|0,r=r+1|0,i=i-1|0)continue;break r}break}return t-f|0}return 0}function we(r){var e,i=0,t=0,f=0;if(e=c[r>>2]){if(f=e,(0|e)!=(0|(t=c[r+4>>2]))){for(;(i=c[(f=t-144|0)+132>>2])&&(c[t-8>>2]=i,er(i)),(i=c[t-28>>2])&&(c[t-24>>2]=i,er(i)),(i=c[t-40>>2])&&(c[t-36>>2]=i,er(i)),Oe(t-140|0),(0|e)!=(0|(t=f)););f=c[r>>2]}c[r+4>>2]=e,er(f)}}function je(r){var e=0,i=0,t=0;if(r){if(t=c[r+24>>2]){if((0|(e=t))!=(0|(i=c[r+28>>2]))){for(;e=c[(i=i-4|0)>>2],c[i>>2]=0,e&&(Ai(e+12|0,c[e+16>>2]),mi(e,c[e+4>>2]),er(e)),(0|i)!=(0|t););e=c[r+24>>2]}c[r+28>>2]=t,er(e)}Ai(r+12|0,c[r+16>>2]),mi(r,c[r+4>>2]),er(r)}}function Je(r,e,i){r|=0,e|=0;var t,f,a,n,A=0,o=0;return(n=c[(i|=0)+8>>2])>>>0>(f=c[i+16>>2])>>>0&(0|(A=a=c[i+12>>2]))>=(0|(t=c[i+20>>2]))|(0|t)<(0|A)&&(e=k[c[i>>2]+f|0],A=(o=f+1|0)?t:t+1|0,c[i+16>>2]=o,c[i+20>>2]=A,c[r+4>>2]=e),f>>>0<n>>>0&(0|t)<=(0|a)|(0|t)<(0|a)}function Be(r,e,i){var t=0;if(!i)return c[r+4>>2]==c[e+4>>2];if((0|r)==(0|e))return 1;t=c[r+4>>2],r=k[0|t],i=c[e+4>>2];r:if(!(!r|(0|(e=k[0|i]))!=(0|r)))for(;;){if(e=k[i+1|0],!(r=k[t+1|0]))break r;if(i=i+1|0,t=t+1|0,(0|r)!=(0|e))break}return(0|r)==(0|e)}function Me(r,e){var i=0,t=0,f=0;c[r+8>>2]=0,c[r>>2]=0,c[r+4>>2]=0;r:{if((0|(i=c[e+4>>2]))!=(0|(t=c[e>>2]))){if((0|(i=i-t|0))<0)break r;t=(t=i)+(i=Sr(f=vi(i),0,i))|0,c[r+8>>2]=t,c[r+4>>2]=t,c[r>>2]=i,i=c[e>>2],r=c[e+4>>2]}else r=i;return void hr(f,i,r-i|0)}mt(),o()}function Qe(r){var e,i=0,t=0,f=0;if((0|(t=c[r+4>>2]))!=(0|(e=c[r>>2])))for(;(i=c[(f=t-144|0)+132>>2])&&(c[t-8>>2]=i,er(i)),(i=c[t-28>>2])&&(c[t-24>>2]=i,er(i)),(i=c[t-40>>2])&&(c[t-36>>2]=i,er(i)),Oe(t-140|0),(0|e)!=(0|(t=f)););c[r+4>>2]=e}function ge(r){var e=0;r&&((e=c[r+76>>2])&&(c[r+80>>2]=e,er(e)),(e=c[r- -64>>2])&&(c[r+68>>2]=e,er(e)),(e=c[r+48>>2])&&(c[r+52>>2]=e,er(e)),(e=c[r+24>>2])&&(c[r+28>>2]=e,er(e)),(e=c[r+12>>2])&&(c[r+16>>2]=e,er(e)),(e=c[r>>2])&&(c[r+4>>2]=e,er(e)),er(r))}function Oe(r){var e=0;(e=c[r+84>>2])&&(c[r+88>>2]=e,er(e)),(e=c[r+72>>2])&&(c[r+76>>2]=e,er(e)),(e=c[r+52>>2])&&(c[r+56>>2]=e,er(e)),(e=c[r+40>>2])&&(c[r+44>>2]=e,er(e)),(e=c[r+28>>2])&&(c[r+32>>2]=e,er(e)),(e=c[r+12>>2])&&er(e),(r=c[r>>2])&&er(r)}function Ce(r,e,i){var t,f,a=0,n=0;for(Z=t=(Z=f=Z-16|0)-16|0,e=e-r>>2;e;)c[t+12>>2]=r,a=e>>>1|0,c[t+12>>2]=c[t+12>>2]+(a<<2),n=(-1^a)+e|0,e=a,e=(a=s[c[t+12>>2]>>2]<s[i>>2])?n:e,r=a?c[t+12>>2]+4|0:r;return Z=t+16|0,Z=f+16|0,r}function ze(r,e){var i,t;return t=vi(40),c[t>>2]=-1,c[(i=t+8|0)+16>>2]=0,c[i+20>>2]=0,c[i+8>>2]=0,c[i>>2]=0,c[i+4>>2]=0,c[i+24>>2]=0,c[i+28>>2]=0,Zt[c[c[r>>2]+16>>2]](r,t),r=c[e+88>>2],c[e+88>>2]=t,r&&((e=c[r+8>>2])&&(c[r+12>>2]=e,er(e)),er(r)),1}function Xe(r){var e=0,i=0,t=0;r:{if(3&(e=r))for(;;){if(!k[0|e])break r;if(!(3&(e=e+1|0)))break}for(;i=e,e=e+4|0,!((-1^(t=c[i>>2]))&t-16843009&-2139062144););for(;i=(e=i)+1|0,k[0|e];);}return e-r|0}function Se(r){var e,i,t=0,f=0,a=0;return a=k[r+12|0],(f=c[r+8>>2])>>>0>4095||(0|(t=c[r+4>>2]))<=0||(t=t-1|0,c[r+4>>2]=t,f=k[t+c[r>>2]|0]|f<<8),t=y(a=0-a&255,f>>>8|0),i=(e=255&f)>>>0<a>>>0,c[r+8>>2]=i?t+e|0:f-(t+a|0)|0,i}function He(r,e){return c[r+4>>2]=0,c[r+8>>2]=0,c[r>>2]=1776,c[r+12>>2]=0,c[r+16>>2]=0,c[r+20>>2]=0,c[r+24>>2]=0,c[r+28>>2]=0,c[r+32>>2]=0,c[r+36>>2]=0,c[r+40>>2]=0,c[r>>2]=2016,c[r+60>>2]=e,c[r+44>>2]=0,c[r+48>>2]=0,c[r+52>>2]=0,c[r+56>>2]=0,r}function Pe(r,e){var i,t=0,f=0;if((i=Xe(e))>>>0<2147483632){r:{if(i>>>0>=11)f=vi(t=1+(15|i)|0),c[r+8>>2]=-2147483648|t,c[r>>2]=f,c[r+4>>2]=i,t=i+f|0;else if(n[r+11|0]=i,t=r+i|0,f=r,!i)break r;gr(f,e,i)}return n[0|t]=0,r}yt(),o()}function xe(r){var e=0,i=0;(r|=0)&&(e=c[r+88>>2],c[r+88>>2]=0,e&&((i=c[e+8>>2])&&(c[e+12>>2]=i,er(i)),er(e)),(e=c[r+68>>2])&&(c[r+72>>2]=e,er(e)),e=c[r+64>>2],c[r+64>>2]=0,e&&((i=c[e>>2])&&(c[e+4>>2]=i,er(i)),er(e)),er(r))}function Le(r,e){var i=0,t=0,f=0;r:{if(i=c[r>>2],!(c[r+8>>2]-i>>2>>>0>=e>>>0)){if(e>>>0>=1073741824)break r;t=c[r+4>>2]-i|0,e=gr(vi(f=e<<2),i,t),c[r+8>>2]=e+f,c[r+4>>2]=e+t,c[r>>2]=e,i&&er(i)}return}mt(),o()}function Ke(r,e,i,t){var f,a=0,n=0;return f=(n=e^t)>>31,n>>=31,r=Wr((r^=a=e>>31)-a|0,a=(e^a)-((r>>>0<a>>>0)+a|0)|0,(e=i^(r=t>>31))-r|0,(r^t)-((r>>>0>e>>>0)+r|0)|0)^n,E=(f^E)-((r>>>0<n>>>0)+f|0)|0,e=r-n|0}function qe(r,e,i){var t,f=0;Z=t=Z-16|0,(f=2147483647&c[r+8>>2])>>>0>i>>>0?(f=c[r>>2],c[r+4>>2]=i,le(f,e,i),n[t+15|0]=0,n[i+f|0]=k[t+15|0]):Kr(r,f-1|0,1+(i-f|0)|0,r=c[r+4>>2],r,i,e),Z=t+16|0}function $e(r,e,i,t){var f,a,n,A,o=0,b=0;return A=y(o=i>>>16|0,b=r>>>16|0),o=(65535&(b=((n=y(f=65535&i,a=65535&r))>>>16|0)+y(b,f)|0))+y(o,a)|0,E=(y(e,i)+A|0)+y(r,t)+(b>>>16)+(o>>>16)|0,65535&n|o<<16}function ri(r,e,i){var t;Z=t=Z-16|0,i>>>0<=10?(n[r+11|0]=128&k[r+11|0]|i,n[r+11|0]=127&k[r+11|0],le(r,e,i),n[t+15|0]=0,n[r+i|0]=k[t+15|0]):Kr(r,10,i-10|0,r=127&k[r+11|0],r,i,e),Z=t+16|0}function ei(r,e,i){var t;if(!(t=c[r+16>>2]))return c[r+36>>2]=1,c[r+24>>2]=i,void(c[r+16>>2]=e);r:{if((0|e)==(0|t)){if(2!=c[r+24>>2])break r;return void(c[r+24>>2]=i)}n[r+54|0]=1,c[r+24>>2]=2,c[r+36>>2]=c[r+36>>2]+1}}function ii(r,e){var i,t=0;if((0|(i=c[r+8>>2]))!=(0|(r=c[r+12>>2])))for(t=(r=r-i>>2)>>>0<=1?1:r,r=0;;){if(c[c[(r<<2)+i>>2]+60>>2]==(0|e))return r;if((0|t)==(0|(r=r+1|0)))break}return-1}function ti(r,e,i){var t=0,f=0;return(0|(t=r+4|0))!=(0|(r=te(r,e)))&&(0|(e=c[r+32>>2]))!=(0|(t=c[r+28>>2]))&&(Tr(i,e-t|0),hr(i=di(i),e=c[r+28>>2],c[r+32>>2]-e|0),f=1),f}function fi(r){return c[r+40>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[r>>2]=10032,c[r+12>>2]=0,c[r+16>>2]=0,c[r+20>>2]=0,c[r+24>>2]=0,c[r+28>>2]=0,c[r+32>>2]=0,A[r+36>>1]=0,c[r+44>>2]=0,c[r>>2]=8080,r}function ai(r){return c[r+8>>2]=0,c[r+12>>2]=0,c[r>>2]=0,c[r+40>>2]=0,c[r+44>>2]=0,c[r+28>>2]=9,n[r+24|0]=1,c[r+56>>2]=-1,c[r+60>>2]=0,c[r+16>>2]=0,c[r+20>>2]=0,c[r+48>>2]=0,c[r+52>>2]=0,r}function ni(r){var e,i;return(r=(e=c[2909])+(i=r+7&-8)|0)>>>0<=e>>>0&&i||r>>>0>Et()<<16>>>0&&!(0|D(0|r))?(c[2940]=48,-1):(c[2909]=r,e)}function Ai(r,e){e&&(Ai(r,c[e>>2]),Ai(r,c[e+4>>2]),r=c[e+28>>2],c[e+28>>2]=0,r&&(Ai(r+12|0,c[r+16>>2]),mi(r,c[r+4>>2]),er(r)),n[e+27|0]<0&&er(c[e+16>>2]),er(e))}function oi(r,e){var i,t,f,a;return c[r>>2]=11356,c[r>>2]=11468,t=vi((i=Xe(e))+13|0),c[t+8>>2]=0,c[t+4>>2]=i,c[t>>2]=i,f=r,a=hr(t+12|0,e,i+1|0),c[f+4>>2]=a,r}function bi(r,e,i){return e&&(e=0,!he(1,i,r))||(n[r+36|0]=1,c[r+32>>2]=0,i=(e=c[r+16>>2])+c[r>>2]|0,c[r+24>>2]=i,c[r+28>>2]=i+(c[r+8>>2]-e|0),e=1),e}function ui(r){var e=0;return!c[(r|=0)- -64>>2]|!c[r+68>>2]|!c[r+44>>2]|!c[r+48>>2]||!c[r+52>>2]|!c[r+56>>2]||(e=-1!=c[r+92>>2]),0|e}function ci(r){var e=0;return c[(r|=0)>>2]=2136,e=c[r+20>>2],c[r+20>>2]=0,e&&Zt[c[c[e>>2]+4>>2]](e),c[r>>2]=1920,e=c[r+16>>2],c[r+16>>2]=0,e&&xe(e),0|r}function ki(r){var e=0;return!c[(r|=0)+48>>2]|!c[r+52>>2]|!c[r+28>>2]|!c[r+32>>2]||!c[r+36>>2]|!c[r+40>>2]||(e=-1!=c[r+76>>2]),0|e}function _i(r){var e=0;return c[(r|=0)>>2]=8624,(e=c[r+48>>2])&&(c[r+52>>2]=e,er(e)),c[r>>2]=8876,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),0|r}function si(r){var e=0;c[(r|=0)>>2]=2136,e=c[r+20>>2],c[r+20>>2]=0,e&&Zt[c[c[e>>2]+4>>2]](e),c[r>>2]=1920,e=c[r+16>>2],c[r+16>>2]=0,e&&xe(e),er(r)}function pi(r){return c[r+8>>2]=0,c[r+12>>2]=0,c[r>>2]=0,c[r+16>>2]=0,c[r+20>>2]=0,c[r+32>>2]=0,c[r+24>>2]=0,c[r+28>>2]=0,A[r+38>>1]=0,n[r+36|0]=0,r}function li(r,e){var i=0;return i=-1,-1==(0|e)|(0|e)>4||(e=y(e,12)+r|0,r=c[e+20>>2],(c[e+24>>2]-r|0)<=0||(i=c[r>>2])),i}function yi(r,e,i,t,f,a,A){c[r>>2]=0,c[r+56>>2]=e,c[r+48>>2]=0,c[r+52>>2]=0,c[r+40>>2]=a,c[r+44>>2]=A,n[r+32|0]=f,c[r+28>>2]=t,n[r+24|0]=i}function di(r){return(k[r+11|0]>>>7|0?c[r+4>>2]:127&k[r+11|0])||(Mi(1222),o()),k[r+11|0]>>>7|0&&(r=c[r>>2]),r}function mi(r,e){e&&(mi(r,c[e>>2]),mi(r,c[e+4>>2]),(r=c[e+28>>2])&&(c[e+32>>2]=r,er(r)),n[e+27|0]<0&&er(c[e+16>>2]),er(e))}function vi(r){var e=0;r=r||1;r:{for(;;){if(e=Y(r))break r;if(!(e=c[3065]))break;Zt[0|e]()}U(),o()}return e}function hi(r,e){e&&(hi(r,c[e>>2]),hi(r,c[e+4>>2]),n[e+39|0]<0&&er(c[e+28>>2]),n[e+27|0]<0&&er(c[e+16>>2]),er(e))}function Ri(r){var e,i;return c[(r|=0)>>2]=11468,e=c[r+4>>2]-12|0,i=c[e+8>>2]-1|0,c[e+8>>2]=i,(0|i)<0&&er(e),0|r}function Ni(r,e,i){return e|=0,i|=0,c[(r|=0)+4>>2]=e,e=c[c[c[e+4>>2]+8>>2]+(i<<2)>>2],c[r+12>>2]=i,c[r+8>>2]=e,1}function Ti(r){var e=0;return!c[(r|=0)+60>>2]|!c[r+44>>2]|!c[r+48>>2]|!c[r+52>>2]||(e=0!=c[r+56>>2]),0|e}function Vi(r,e){k[r+11|0]>>>7|0?c[r+4>>2]=e:(n[r+11|0]=128&k[r+11|0]|e,n[r+11|0]=127&k[r+11|0])}function Ui(r){var e=0;return!c[(r|=0)+52>>2]|!c[r+44>>2]|!c[r+48>>2]||(e=0!=c[r+56>>2]),0|e}function Wi(r,e){r|=0;var i=0;return c[(e|=0)+56>>2]|!e|3!=k[e+24|0]||(c[r+60>>2]=e,i=1),0|i}function Di(r,e){r|=0;var i=0;return c[(e|=0)+56>>2]|3!=k[e+24|0]||(c[r- -64>>2]=e,i=1),0|i}function Gi(r,e){r|=0;var i=0;return c[(e|=0)+56>>2]|3!=k[e+24|0]||(c[r+48>>2]=e,i=1),0|i}function Zi(){var r;r=ft(4),c[r>>2]=11356,c[r>>2]=11316,c[r>>2]=11336,W(0|r,11448,14),o()}function Ei(r){var e;c[(r|=0)>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),er(r)}function Fi(r,e){e&&(Fi(r,c[e>>2]),Fi(r,c[e+4>>2]),hi(e+20|0,c[e+24>>2]),er(e))}function Ii(r){for(var e=0;r;)r&=r-1,e=e+1|0;return e}function Yi(r){var e;return(-1>>>(e=31&r)&-2)<<e|(-1<<(r=0-r&31)&-2)>>>r}function wi(){var r;return r=vi(12),c[r>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,0|r}function ji(r){var e=0;(r|=0)&&((e=c[r>>2])&&(c[r+4>>2]=e,er(e)),er(r))}function Ji(r,e){return r|=0,(e|=0)>>>0<=1&&(c[r+28>>2]=e),e>>>0<2|0}function Bi(r,e){return e|=0,0|Zt[c[c[(r|=0)>>2]+12>>2]](r,e)}function Mi(r){r=oi(ft(8),r),c[r>>2]=11568,W(0|r,11600,1),o()}function Qi(r){r=oi(ft(8),r),c[r>>2]=11516,W(0|r,11548,1),o()}function gi(r,e){return e|=0,c[c[(r|=0)>>2]+(e<<2)>>2]}function Oi(r,e,i){return 0|Ar(r|=0,e|=0,i|=0)}function Ci(r,e){var i;i=vi(e),c[r+4>>2]=e,c[r>>2]=i}function zi(r){return c[r>>2]=10300,Sr(r+4|0,0,80),r}function Xi(r){return r?31-v(r-1^r)|0:32}function Si(r){return c[(r|=0)+12>>2]-c[r+8>>2]>>2}function Hi(r){(r|=0)&&Zt[c[c[r>>2]+4>>2]](r)}function Pi(r,e){return e|=0,c[(r|=0)+4>>2]=e,1}function xi(r){return c[(r|=0)+4>>2]-c[r>>2]>>1}function Li(r){return c[(r|=0)+4>>2]-c[r>>2]>>2}function Ki(r){return c[(r|=0)+4>>2]-c[r>>2]|0}function qi(r){return c[(r|=0)+80>>2]}function $i(r){return c[(r|=0)+28>>2]}function rt(r){return c[(r|=0)+8>>2]}function et(r,e){return 0,0,-1}function it(r,e){return 0,0,6}function tt(r){return c[(r|=0)+4>>2]}function ft(r){return Y(r+80|0)+80|0}function at(r,e){return 0,0,0}function nt(r,e){return 0,0,1}function At(r){return c[(r|=0)>>2]}function ot(r){(r|=0)&&er(r)}function bt(r){Ri(r|=0),er(r)}function ut(r){return 0|(r|=0)}function ct(r){return 0,1}function kt(r){return 0,4}function _t(r){return 0,5}function st(r){return 0,2}function pt(r){return 0,0}function lt(r){return 0,6}function yt(){Qi(1222),o()}function dt(){Mi(1154),o()}function mt(){Qi(1154),o()}function vt(r){er(r|=0)}function ht(r){o()}function Rt(){return-1}function Nt(){return 1}function Tt(){return 0}function Vt(){return 3}function Ut(){return 4}function Wt(){return 2}function Dt(r){0}f(e=k,1028,"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"),f(e,8900,"AQAAAAMAAAAFAAAABwAAAAAAAACYIwAA+QAAAPoAAAD7AAAA/AAAAE41ZHJhY28yMk1lc2hUcmF2ZXJzYWxTZXF1ZW5jZXJJTlNfMjhNYXhQcmVkaWN0aW9uRGVncmVlVHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzJfRUVFRUVFAE41ZHJhY28xNVBvaW50c1NlcXVlbmNlckUAAAAAsCsAAHMjAADYKwAA7CIAAJAjAAD/////AAAAAIgkAAD9AAAA/gAAAP8AAABONWRyYWNvMjhNYXhQcmVkaWN0aW9uRGVncmVlVHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQBONWRyYWNvMTNUcmF2ZXJzZXJCYXNlSU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQAAsCsAACUkAADYKwAAvCMAAIAkAAAAAAAAgCQAAAABAAABAQAA/wAAAAAAAABAJQAAAgEAAAMBAAAEAQAABQEAAE41ZHJhY28yMk1lc2hUcmF2ZXJzYWxTZXF1ZW5jZXJJTlNfMTlEZXB0aEZpcnN0VHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzJfRUVFRUVFAAAA2CsAAMAkAACQIwAAAAAAAMAlAAAGAQAABwEAAP8AAABONWRyYWNvMTlEZXB0aEZpcnN0VHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQDYKwAAYCUAAIAkAAAAAAAAcCYAAAgBAAAJAQAACgEAAAsBAABONWRyYWNvMjJNZXNoVHJhdmVyc2FsU2VxdWVuY2VySU5TXzE5RGVwdGhGaXJzdFRyYXZlcnNlcklOU18yNE1lc2hBdHRyaWJ1dGVDb3JuZXJUYWJsZUVOU18zNk1lc2hBdHRyaWJ1dGVJbmRpY2VzRW5jb2RpbmdPYnNlcnZlcklTMl9FRUVFRUUAANgrAADkJQAAkCM="),f(e,9860,"3CYAAMwAAAAMAQAAzgAAAM8AAAANAQAA0AAAANEAAADSAAAA0wAAANQAAADVAAAA1gAAAA4BAABONWRyYWNvMjFNZXNoU2VxdWVudGlhbERlY29kZXJFANgrAAC8JgAA3B8AAAAAAAAcJwAADwEAABABAAARAQAAEgEAAE41ZHJhY28xNUxpbmVhclNlcXVlbmNlckUAAADYKwAAACcAAJAjAAAAAAAAnCcAAMwAAAATAQAAFAEAAM8AAAAjAAAAFQEAANEAAADSAAAA0w=="),f(e,10080,"RmFpbGVkIHRvIHBhcnNlIERyYWNvIGhlYWRlci4ATjVkcmFjbzE3UG9pbnRDbG91ZERlY29kZXJFAAAAsCsAAH4nAAABAAAAAQAAAAIAAAACAAAABAAAAAQAAAAIAAAACAAAAAQAAAAIAAAAAQ=="),f(e,10211,"wAAAAMAAAADAAAAAwP//////////AAAAACAoAAAWAQAAFwEAABgBAAAZAQAATjVkcmFjbzRNZXNoRQAAANgrAAAQKAAAZCgAAP////8AAAAAAAAAAGQoAAAaAQAAGwEAABwBAAAdAQAATjVkcmFjbzEwUG9pbnRDbG91ZEUAAAAAsCsAAEwo"),f(e,10356,"AgAAAAMAAAAFAAAABwAAAAsAAAANAAAAEQAAABMAAAAXAAAAHQAAAB8AAAAlAAAAKQAAACsAAAAvAAAANQAAADsAAAA9AAAAQwAAAEcAAABJAAAATwAAAFMAAABZAAAAYQAAAGUAAABnAAAAawAAAG0AAABxAAAAfwAAAIMAAACJAAAAiwAAAJUAAACXAAAAnQAAAKMAAACnAAAArQAAALMAAAC1AAAAvwAAAMEAAADFAAAAxwAAANMAAAABAAAACwAAAA0AAAARAAAAEwAAABcAAAAdAAAAHwAAACUAAAApAAAAKwAAAC8AAAA1AAAAOwAAAD0AAABDAAAARwAAAEkAAABPAAAAUwAAAFkAAABhAAAAZQAAAGcAAABrAAAAbQAAAHEAAAB5AAAAfwAAAIMAAACJAAAAiwAAAI8AAACVAAAAlwAAAJ0AAACjAAAApwAAAKkAAACtAAAAswAAALUAAAC7AAAAvwAAAMEAAADFAAAAxwAAANEAAAAAAAAACgAAAGQAAADoAwAAECcAAKCGAQBAQg8AgJaYAADh9QUAypo7AAAAAAAAAAAwMDAxMDIwMzA0MDUwNjA3MDgwOTEwMTExMjEzMTQxNTE2MTcxODE5MjAyMTIyMjMyNDI1MjYyNzI4MjkzMDMxMzIzMzM0MzUzNjM3MzgzOTQwNDE0MjQzNDQ0NTQ2NDc0ODQ5NTA1MTUyNTM1NDU1NTY1NzU4NTk2MDYxNjI2MzY0NjU2NjY3Njg2OTcwNzE3MjczNzQ3NTc2Nzc3ODc5ODA4MTgyODM4NDg1ODY4Nzg4ODk5MDkxOTI5Mzk0OTU5Njk3OTg5OU4xMF9fY3h4YWJpdjExNl9fc2hpbV90eXBlX2luZm9FAAAAANgrAADoKgAAbC0AAE4xMF9fY3h4YWJpdjExN19fY2xhc3NfdHlwZV9pbmZvRQAAANgrAAAYKwAADCsAAE4xMF9fY3h4YWJpdjExN19fcGJhc2VfdHlwZV9pbmZvRQAAANgrAABIKwAADCsAAE4xMF9fY3h4YWJpdjExOV9fcG9pbnRlcl90eXBlX2luZm9FANgrAAB4KwAAbCsAAAAAAAA8KwAAHgEAAB8BAAAgAQAAIQEAACIBAAAjAQAAJAEAACUBAAAAAAAAICwAAB4BAAAmAQAAIAEAACEBAAAiAQAAJwEAACgBAAApAQAATjEwX19jeHhhYml2MTIwX19zaV9jbGFzc190eXBlX2luZm9FAAAAANgrAAD4KwAAPCsAAAAAAACQLAAADgAAACoBAAArAQAAAAAAALgsAAAOAAAALAEAAC0BAAAAAAAAeCwAAA4AAAAuAQAALwEAAFN0OWV4Y2VwdGlvbgAAAACwKwAAaCwAAFN0OWJhZF9hbGxvYwAAAADYKwAAgCwAAHgsAABTdDIwYmFkX2FycmF5X25ld19sZW5ndGgAAAAA2CsAAJwsAACQLAAAAAAAAOgsAAABAAAAMAEAADEBAABTdDExbG9naWNfZXJyb3IA2CsAANgsAAB4LAAAAAAAABwtAAABAAAAMgEAADEBAABTdDEybGVuZ3RoX2Vycm9yAAAAANgrAAAILQAA6CwAAAAAAABQLQAAAQAAADMBAAAxAQAAU3QxMm91dF9vZl9yYW5nZQAAAADYKwAAPC0AAOgsAABTdDl0eXBlX2luZm8AAAAAsCsAAFwt"),f(e,11636,"8C8B");var Gt,Zt=((Gt=[null,Ri,ut,vt,st,function(r,e){r|=0;var i=0;return!(e=c[88+(e|=0)>>2])|2!=c[e>>2]||(i=r,r=c[e+8>>2],c[i+4>>2]=k[0|r]|k[r+1|0]<<8|k[r+2|0]<<16|k[r+3|0]<<24,i=1),0|i},function(r,e){r|=0;var i,t=0;c[(e|=0)>>2]=2,t=c[e+8>>2],(i=c[e+12>>2]-t|0)>>>0<=4294967291&&(oe(e+8|0,i+4|0),t=c[e+8>>2]),e=t+i|0,r=c[r+4>>2],n[0|e]=r,n[e+1|0]=r>>>8,n[e+2|0]=r>>>16,n[e+3|0]=r>>>24},function(r,e,i,t){r|=0,e|=0,i|=0;var f,a,n=0,A=0,o=0,b=0,u=0,_=0,s=0;if(o=c[80+(t|=0)>>2],Z=f=Z-48|0,!((a=(r=c[r+4>>2])-2|0)>>>0>28))if(u=c[c[t>>2]>>2]+c[t+48>>2]|0,c[f+16>>2]=r,r=-1<<r,c[f+20>>2]=-1^r,r=-2-r|0,c[f+24>>2]=r,c[f+32>>2]=(0|r)/2,p[f+28>>2]=d(2)/d(0|r),(0|(n=c[i>>2]))==c[i+4>>2]){if(o)for(t=0,r=0;_=f+36|0,s=c[c[e>>2]>>2],b=c[e+48>>2],A=n=$e(i=c[e+40>>2],c[e+44>>2],k[e+84|0]?r:c[c[e+68>>2]+(r<<2)>>2],0),hr(_,(n=n+b|0)+s|0,i),Ir(f+16|0,_,f+12|0,f+8|0),c[(i=t<<2)+u>>2]=c[f+12>>2],c[(4|i)+u>>2]=c[f+8>>2],t=t+2|0,(0|o)!=(0|(r=r+1|0)););}else for(r=0,t=0;A=c[(t<<2)+n>>2],o=f+36|0,_=c[c[e>>2]>>2],s=c[e+48>>2],n=c[e+40>>2],b=c[e+44>>2],k[e+84|0]||(A=c[c[e+68>>2]+(A<<2)>>2]),b=A=$e(n,b,A,0),hr(o,(A=A+s|0)+_|0,n),Ir(f+16|0,o,f+12|0,f+8|0),c[(n=r<<2)+u>>2]=c[f+12>>2],c[(4|n)+u>>2]=c[f+8>>2],r=r+2|0,t=t+1|0,n=c[i>>2],t>>>0<c[i+4>>2]-n>>2>>>0;);return Z=f+48|0,a>>>0<29|0},wr,function(r,e){e|=0;var i,t=0;return Z=i=Z-16|0,-1!=(0|(r=c[4+(r|=0)>>2]))&&(n[i+15|0]=r,t=c[e+20>>2],!!c[e+16>>2]&(0|t)>=0|(0|t)>0||vr(e,c[e+4>>2],i+15|0,i+16|0)),Z=i+16|0,-1!=(0|r)|0},Je,function(r,e,i){e|=0,i|=0;var t,f,a,A,o,b=0;return Z=t=Z+-64|0,b=0|Zt[c[c[(r|=0)>>2]+44>>2]](r,e),r=0|Zt[c[c[r>>2]+40>>2]](r,e),f=ai(t),a=c[e+56>>2],A=255&b,o=r,r=(r=r-1|0)>>>0<=10?c[10148+(r<<2)>>2]:-1,yi(f,a,A,o,0,b=y(r,b),b>>31),ie(r=ee(vi(96),f),i),n[r+84|0]=1,c[r+72>>2]=c[r+68>>2],c[r+60>>2]=c[e+60>>2],Z=t- -64|0,0|r},it,function(r,e){return 2},ut,function(r){var e;return c[(r|=0)>>2]=1624,(e=c[r+8>>2])&&(c[r+12>>2]=e,er(e)),0|r},function(r){var e;c[(r|=0)>>2]=1624,(e=c[r+8>>2])&&(c[r+12>>2]=e,er(e)),er(r)},ct,function(r,e){r|=0;var i=0,t=0,f=0,a=0,n=0,A=0,o=0,b=0,u=0;if(!(!(i=c[88+(e|=0)>>2])|1!=c[i>>2])){if(f=c[i+8>>2],c[r+4>>2]=k[0|f]|k[f+1|0]<<8|k[f+2|0]<<16|k[f+3|0]<<24,a=r+8|0,t=k[e+24|0],A=c[r+8>>2],t>>>0>(n=c[r+12>>2]-A>>2)>>>0?(_e(a,t-n|0),t=k[e+24|0],f=c[i+8>>2]):t>>>0>=n>>>0||(c[r+12>>2]=A+(t<<2)),t){if(A=3&t,a=c[a>>2],t-1>>>0<3)e=4,t=0;else for(u=252&t,t=0,e=4;i=e+f|0,c[(n=t<<2)+a>>2]=k[0|i]|k[i+1|0]<<8|k[i+2|0]<<16|k[i+3|0]<<24,c[a+(4|n)>>2]=k[i+4|0]|k[i+5|0]<<8|k[i+6|0]<<16|k[i+7|0]<<24,c[a+(8|n)>>2]=k[i+8|0]|k[i+9|0]<<8|k[i+10|0]<<16|k[i+11|0]<<24,c[a+(12|n)>>2]=k[i+12|0]|k[i+13|0]<<8|k[i+14|0]<<16|k[i+15|0]<<24,t=t+4|0,e=e+16|0,(0|u)!=(0|(o=o+4|0)););if(A)for(;i=e+f|0,c[a+(t<<2)>>2]=k[0|i]|k[i+1|0]<<8|k[i+2|0]<<16|k[i+3|0]<<24,t=t+1|0,e=e+4|0,(0|A)!=(0|(b=b+1|0)););}else e=4;t=r,r=e+f|0,c[t+20>>2]=k[0|r]|k[r+1|0]<<8|k[r+2|0]<<16|k[r+3|0]<<24,t=1}return 0|t},function(r,e){r|=0;var i,t=0,f=0,a=0,A=0;if(c[(e|=0)>>2]=1,i=e+8|0,t=c[e+8>>2],(f=c[e+12>>2]-t|0)>>>0<=4294967291&&(oe(i,f+4|0),t=c[i>>2]),t=t+f|0,f=c[r+4>>2],n[0|t]=f,n[t+1|0]=f>>>8,n[t+2|0]=f>>>16,n[t+3|0]=f>>>24,(0|(t=c[r+8>>2]))!=c[r+12>>2])for(f=0;A=(f<<2)+t|0,t=c[e+8>>2],(a=c[e+12>>2]-t|0)>>>0<=4294967291&&(oe(i,a+4|0),t=c[i>>2]),t=t+a|0,a=c[A>>2],n[0|t]=a,n[t+1|0]=a>>>8,n[t+2|0]=a>>>16,n[t+3|0]=a>>>24,f=f+1|0,t=c[r+8>>2],f>>>0<c[r+12>>2]-t>>2>>>0;);(t=(t=c[e+12>>2])-(e=c[e+8>>2])|0)>>>0<=4294967291&&(oe(i,t+4|0),e=c[i>>2]),e=e+t|0,r=c[r+20>>2],n[0|e]=r,n[e+1|0]=r>>>8,n[e+2|0]=r>>>16,n[e+3|0]=r>>>24},function(r,e,i,t){r|=0,e|=0,i|=0,t|=0;var f=d(0),a=0,n=0,A=0,o=0,b=0,u=0,_=0,s=0,l=0,y=d(0),v=0,h=0,R=0,T=0,V=0,U=0,W=0,D=0,G=0;if(c[i>>2]==c[i+4>>2]){if(s=c[t+80>>2],Z=U=Z-16|0,n=c[r+4>>2],u=k[e+24|0],A=c[t+48>>2],l=c[c[t>>2]>>2],c[(i=U+8|0)>>2]=1065353216,t=i,p[i>>2]=d(-1<<n^-1)/p[r+20>>2],i=vi(u<<2),!(!s|!u))if(v=A+l|0,y=p[t>>2],l=c[r+8>>2],W=c[e>>2],t=c[e+48>>2],n=c[e+40>>2],D=c[e+44>>2],k[e+84|0])for(T=254&u,V=1&u,r=0;;){if(b=hr(i,(e=c[W>>2])+(A=$e(n,D,o,_)+t|0)|0,n),e=0,h=0,1!=(0|u))for(;A=v+(r<<2)|0,f=d(N(d(d(y*d(p[(a=e<<2)+b>>2]-p[a+l>>2]))+d(.5)))),R=d(m(f))<d(2147483648)?~~f:-2147483648,c[A>>2]=R,f=d(N(d(d(y*d(p[(a|=4)+b>>2]-p[a+l>>2]))+d(.5)))),a=d(m(f))<d(2147483648)?~~f:-2147483648,c[A+4>>2]=a,e=e+2|0,r=r+2|0,(0|T)!=(0|(h=h+2|0)););if(V&&(A=v+(r<<2)|0,f=d(N(d(d(y*d(p[(e<<=2)+b>>2]-p[e+l>>2]))+d(.5)))),e=d(m(f))<d(2147483648)?~~f:-2147483648,c[A>>2]=e,r=r+1|0),e=_,_=e=(o=o+1|0)?e:e+1|0,!((0|o)!=(0|s)|e))break}else for(a=c[e+68>>2],T=254&u,V=1&u,r=0;;){if(A=hr(i,(e=c[W>>2])+(_=$e(n,D,c[a+(o<<2)>>2],0)+t|0)|0,n),e=0,h=0,1!=(0|u))for(;_=v+(r<<2)|0,f=d(N(d(d(y*d(p[(b=e<<2)+A>>2]-p[l+b>>2]))+d(.5)))),R=d(m(f))<d(2147483648)?~~f:-2147483648,c[_>>2]=R,f=d(N(d(d(y*d(p[(b|=4)+A>>2]-p[l+b>>2]))+d(.5)))),b=d(m(f))<d(2147483648)?~~f:-2147483648,c[_+4>>2]=b,e=e+2|0,r=r+2|0,(0|T)!=(0|(h=h+2|0)););if(V&&(_=v+(r<<2)|0,f=d(N(d(d(y*d(p[(e<<=2)+A>>2]-p[e+l>>2]))+d(.5)))),e=d(m(f))<d(2147483648)?~~f:-2147483648,c[_>>2]=e,r=r+1|0),(0|s)==(0|(o=o+1|0)))break}return er(i),Z=U+16|0,1}if(Z=b=Z-16|0,s=c[r+4>>2],o=k[e+24|0],n=c[t+48>>2],A=c[c[t>>2]>>2],c[(t=b+8|0)>>2]=1065353216,_=t,p[t>>2]=d(-1<<s^-1)/p[r+20>>2],t=vi(o<<2),!(!o|(0|(s=c[i+4>>2]))==(0|(h=c[i>>2]))))if(l=A+n|0,U=(i=s-h>>2)>>>0<=1?1:i,y=p[_>>2],A=c[r+8>>2],W=c[e>>2],_=c[e+48>>2],s=c[e+40>>2],D=c[e+44>>2],k[e+84|0])for(T=254&o,V=1&o,r=0,i=0;;){if(v=hr(t,(e=c[W>>2])+(n=$e(s,D,c[h+(i<<2)>>2],0)+_|0)|0,s),e=0,u=0,1!=(0|o))for(;n=l+(r<<2)|0,f=d(N(d(d(y*d(p[(a=e<<2)+v>>2]-p[A+a>>2]))+d(.5)))),R=d(m(f))<d(2147483648)?~~f:-2147483648,c[n>>2]=R,f=d(N(d(d(y*d(p[(a|=4)+v>>2]-p[A+a>>2]))+d(.5)))),a=d(m(f))<d(2147483648)?~~f:-2147483648,c[n+4>>2]=a,e=e+2|0,r=r+2|0,(0|T)!=(0|(u=u+2|0)););if(V&&(n=l+(r<<2)|0,f=d(N(d(d(y*d(p[(e<<=2)+v>>2]-p[e+A>>2]))+d(.5)))),e=d(m(f))<d(2147483648)?~~f:-2147483648,c[n>>2]=e,r=r+1|0),(0|U)==(0|(i=i+1|0)))break}else for(T=c[e+68>>2],V=254&o,G=1&o,r=0,i=0;;){if(v=hr(t,(e=c[W>>2])+(n=$e(s,D,c[T+(c[h+(i<<2)>>2]<<2)>>2],0)+_|0)|0,s),e=0,u=0,1!=(0|o))for(;n=l+(r<<2)|0,f=d(N(d(d(y*d(p[(a=e<<2)+v>>2]-p[A+a>>2]))+d(.5)))),R=d(m(f))<d(2147483648)?~~f:-2147483648,c[n>>2]=R,f=d(N(d(d(y*d(p[(a|=4)+v>>2]-p[A+a>>2]))+d(.5)))),a=d(m(f))<d(2147483648)?~~f:-2147483648,c[n+4>>2]=a,e=e+2|0,r=r+2|0,(0|V)!=(0|(u=u+2|0)););if(G&&(n=l+(r<<2)|0,f=d(N(d(d(y*d(p[(e<<=2)+v>>2]-p[e+A>>2]))+d(.5)))),e=d(m(f))<d(2147483648)?~~f:-2147483648,c[n>>2]=e,r=r+1|0),(0|U)==(0|(i=i+1|0)))break}return er(t),Z=b+16|0,1},Gr,function(r,e){e|=0;var i,t,f=0,a=0;return Z=i=Z-16|0,-1!=(0|(t=c[4+(r|=0)>>2]))&&(f=c[e+20>>2],!!c[e+16>>2]&(0|f)>=0|(0|f)>0||(vr(e,c[e+4>>2],c[r+8>>2],c[r+12>>2]),f=c[e+20>>2],!!c[e+16>>2]&(0|f)>=0|(0|f)>0||(vr(e,c[e+4>>2],r+20|0,r+24|0),f=c[e+20>>2],a=c[e+16>>2],n[i+15|0]=c[r+4>>2],!!a&(0|f)>=0|(0|f)>0||vr(e,c[e+4>>2],i+15|0,i+16|0)))),Z=i+16|0,-1!=(0|t)|0},Fr,it,function(r,e){return k[24+(e|=0)|0]},function(r){var e=0;return c[(r|=0)>>2]=1776,(e=c[r+16>>2])&&(c[r+20>>2]=e,er(e)),(e=c[r+4>>2])&&(c[r+8>>2]=e,er(e)),0|r},ht,function(r,e,i){return e|=0,i|=0,c[32+(r|=0)>>2]=i,c[r+28>>2]=e,1},br,function(r,e){e|=0;var i=0;return 0|Zt[c[c[(r|=0)>>2]+36>>2]](r,e)&&0|Zt[c[c[r>>2]+40>>2]](r,e)&&(i=0|Zt[c[c[r>>2]+44>>2]](r)),0|i},function(r,e){return e|=0,c[c[4+(r|=0)>>2]+(e<<2)>>2]},function(r){return c[8+(r|=0)>>2]-c[r+4>>2]>>2},$i,at,function(){U(),o()},nt,ct,function(r){var e;return c[(r|=0)>>2]=1920,e=c[r+16>>2],c[r+16>>2]=0,e&&xe(e),0|r},function(r){var e;c[(r|=0)>>2]=1920,e=c[r+16>>2],c[r+16>>2]=0,e&&xe(e),er(r)},Ni,function(r,e){return e|=0,c[12+(r|=0)>>2]=-1,c[r+8>>2]=e,1},function(r,e,i){e|=0,i|=0;var t,f=0;return t=c[8+(r|=0)>>2],k[t+24|0]&&ie(t,c[e+4>>2]-c[e>>2]>>2)&&(f=0|Zt[c[c[r>>2]+32>>2]](r,e,i)),0|f},function(r,e,i){return 1},nt,function(r,e){r|=0;var i=0,t=0,f=0,a=0,n=0,A=0;f=1;r:if(!((0|Zt[c[c[(e|=0)>>2]+20>>2]](e))<=0))for(;;){if(f=0,-1==(0|(t=li(c[c[r+4>>2]+4>>2],0|Zt[c[c[e>>2]+24>>2]](e,a)))))break r;if(n=c[r+4>>2],i=0,(0|t)<0||(A=c[n+4>>2],(0|t)>=c[A+12>>2]-c[A+8>>2]>>2||(i=c[c[n+8>>2]+(c[c[n+20>>2]+(t<<2)>>2]<<2)>>2],i=0|Zt[c[c[i>>2]+32>>2]](i,t))),!i)break r;if(!(0|Zt[c[c[e>>2]+28>>2]](e,i)))break r;if(f=1,a=a+1|0,!((0|Zt[c[c[e>>2]+20>>2]](e))>(0|a)))break}return 0|f},function(r,e,i){r|=0,i|=0;var t,f,a=0,n=0,A=0,o=0,b=0,u=0,k=0,_=0;if(A=c[(e|=0)>>2],e=c[e+4>>2],t=a=c[c[r+8>>2]+40>>2],f=vi((0|a)<0?-1:a),n=1,!((0|(u=e-A|0))<4||(e=0,k=a,A=(o=c[i+16>>2])+a|0,a=0+c[i+20>>2]|0,a=A>>>0<k>>>0?a+1|0:a,b=c[i+12>>2],n=0,s[i+8>>2]<A>>>0&(0|a)>=(0|b)|(0|a)>(0|b)))){for(u=(0|(n=u>>2))<=1?1:n;o=hr(f,c[i>>2]+o|0,t),c[i+16>>2]=A,c[i+20>>2]=a,hr(c[c[c[r+8>>2]+64>>2]>>2]+e|0,o,t),(0|u)!=(0|(_=_+1|0))&&(e=e+t|0,a=0+c[i+20>>2]|0,(0|(a=(A=k+(o=c[i+16>>2])|0)>>>0<k>>>0?a+1|0:a))<=(0|(b=c[i+12>>2]))&s[i+8>>2]>=A>>>0|(0|a)<(0|b)););n=(0|n)<=(0|_)}return er(f),0|n},function(r){var e,i=0,t=0;if(c[(r|=0)>>2]=2016,i=c[r+60>>2],c[r+60>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(i=c[r+48>>2])&&(c[r+52>>2]=i,er(i)),e=c[r+36>>2]){if((0|(t=c[r+40>>2]))!=(0|(i=e))){for(;i=c[(t=t-4|0)>>2],c[t>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(0|t)!=(0|e););i=c[r+36>>2]}c[r+40>>2]=e,er(i)}return c[r>>2]=1776,(i=c[r+16>>2])&&(c[r+20>>2]=i,er(i)),(i=c[r+4>>2])&&(c[r+8>>2]=i,er(i)),0|r},function(r){var e,i=0,t=0;if(c[(r|=0)>>2]=2016,i=c[r+60>>2],c[r+60>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(i=c[r+48>>2])&&(c[r+52>>2]=i,er(i)),e=c[r+36>>2]){if((0|(t=c[r+40>>2]))!=(0|(i=e))){for(;i=c[(t=t-4|0)>>2],c[t>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(0|t)!=(0|e););i=c[r+36>>2]}c[r+40>>2]=e,er(i)}c[r>>2]=1776,(i=c[r+16>>2])&&(c[r+20>>2]=i,er(i)),(i=c[r+4>>2])&&(c[r+8>>2]=i,er(i)),er(r)},function(r,e){var i=0,t=0,f=0,a=0,n=0,A=0,o=0,b=0,u=0,_=0,p=0;r:if(br(r|=0,e|=0)){if(A=r+36|0,(n=0|Zt[c[c[r>>2]+24>>2]](r))>>>0>(i=(f=c[r+40>>2])-(t=c[r+36>>2])>>2)>>>0)Or(A,n-i|0);else if(!(i>>>0<=n>>>0)){if((0|(t=t+(n<<2)|0))!=(0|f))for(;i=c[(f=f-4|0)>>2],c[f>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(0|t)!=(0|f););c[r+40>>2]=t}if(i=1,!((0|n)<=0)){for(f=0;;){if(!((0|(i=c[e+20>>2]))>=(0|(a=c[e+12>>2]))&(t=c[e+16>>2])>>>0>=s[e+8>>2]|(0|i)>(0|a))&&(a=k[c[e>>2]+t|0],i=(t=t+1|0)?i:i+1|0,c[e+16>>2]=t,c[e+20>>2]=i,t=0|Zt[c[c[r>>2]+48>>2]](r,a),o=(a=f<<2)+c[r+36>>2]|0,i=c[o>>2],c[o>>2]=t,i&&Zt[c[c[i>>2]+4>>2]](i),(i=c[c[A>>2]+a>>2])&&(u=i,_=0|Zt[c[c[r>>2]+28>>2]](r),p=0|Zt[c[c[r>>2]+20>>2]](r,f),b=c[c[i>>2]+8>>2],0|Zt[b](0|u,0|_,0|p)))){if(i=1,(0|n)!=(0|(f=f+1|0)))continue;break r}break}i=0}}return 0|i},function(r,e){e|=0;var i=0,t=0,f=0,a=0,n=0;if((i=c[60+(r|=0)>>2])&&(c[i+4>>2]=r+48,0|Zt[c[c[i>>2]+12>>2]](i))){r:if(!((0|(i=0|Zt[c[c[r>>2]+24>>2]](r)))<=0)){for(;;){if(a=c[4+(0|Zt[c[c[r>>2]+28>>2]](r))>>2],n=0|Zt[c[c[r>>2]+20>>2]](r,t),f=c[r+60>>2],0|Zt[c[c[f>>2]+8>>2]](f,c[c[a+8>>2]+(n<<2)>>2])){if((0|i)!=(0|(t=t+1|0)))continue;break r}break}return 0}t=0,0|Zt[c[c[r>>2]+36>>2]](r,e)&&0|Zt[c[c[r>>2]+40>>2]](r,e)&&(t=0|Zt[c[c[r>>2]+44>>2]](r))}return 0|t},function(r,e){e|=0;var i,t=0;return i=c[16+(r|=0)>>2],t=0,c[r+20>>2]-i>>2<=(0|e)||(t=0,(0|(e=c[(e<<2)+i>>2]))<0||(t=ne(c[c[r+36>>2]+(e<<2)>>2]))),0|t},function(r,e){e|=0;var i,t=0,f=0,a=0,n=0;if(t=1,!((0|(i=0|Zt[c[c[(r|=0)>>2]+24>>2]](r)))<=0)&&(f=c[c[r+36>>2]>>2],n=r+48|0,t=0,0|Zt[c[c[f>>2]+16>>2]](f,n,e))){for(;(0|i)!=(0|(a=a+1|0))&&(f=c[c[r+36>>2]+(a<<2)>>2],0|Zt[c[c[f>>2]+16>>2]](f,n,e)););t=(0|a)>=(0|i)}return 0|t},function(r,e){e|=0;var i,t=0,f=0,a=0,n=0;if(t=1,!((0|(i=0|Zt[c[c[(r|=0)>>2]+24>>2]](r)))<=0)&&(f=c[c[r+36>>2]>>2],n=r+48|0,t=0,0|Zt[c[c[f>>2]+20>>2]](f,n,e))){for(;(0|i)!=(0|(a=a+1|0))&&(f=c[c[r+36>>2]+(a<<2)>>2],0|Zt[c[c[f>>2]+20>>2]](f,n,e)););t=(0|a)>=(0|i)}return 0|t},function(r){var e,i,t=0,f=0,a=0,A=0,b=0,u=0,_=0,s=0,p=0,l=0,y=0,d=0,m=0,v=0,h=0;Z=e=Z-16|0,b=1;r:if(!((0|(i=0|Zt[c[c[(r|=0)>>2]+24>>2]](r)))<=0))for(h=r+48|0,b=0;;){e:{if(c[40+(0|Zt[c[c[r>>2]+28>>2]](r))>>2]&&(a=c[(d=l<<2)+c[r+36>>2]>>2],f=c[a+8>>2],A=ne(a))){u=c[40+(0|Zt[c[c[r>>2]+28>>2]](r))>>2],c[e+12>>2]=c[f+56>>2],a=vi(32),c[e>>2]=a,c[e+4>>2]=24,c[e+8>>2]=-2147483616,f=k[1196]|k[1197]<<8|k[1198]<<16|k[1199]<<24,t=k[1192]|k[1193]<<8|k[1194]<<16|k[1195]<<24,n[a+16|0]=t,n[a+17|0]=t>>>8,n[a+18|0]=t>>>16,n[a+19|0]=t>>>24,n[a+20|0]=f,n[a+21|0]=f>>>8,n[a+22|0]=f>>>16,n[a+23|0]=f>>>24,f=k[1188]|k[1189]<<8|k[1190]<<16|k[1191]<<24,t=k[1184]|k[1185]<<8|k[1186]<<16|k[1187]<<24,n[a+8|0]=t,n[a+9|0]=t>>>8,n[a+10|0]=t>>>16,n[a+11|0]=t>>>24,n[a+12|0]=f,n[a+13|0]=f>>>8,n[a+14|0]=f>>>16,n[a+15|0]=f>>>24,f=k[1180]|k[1181]<<8|k[1182]<<16|k[1183]<<24,t=k[1176]|k[1177]<<8|k[1178]<<16|k[1179]<<24,n[0|a]=t,n[a+1|0]=t>>>8,n[a+2|0]=t>>>16,n[a+3|0]=t>>>24,n[a+4|0]=f,n[a+5|0]=f>>>8,n[a+6|0]=f>>>16,n[a+7|0]=f>>>24,n[a+24|0]=0;i:{if(t=c[(f=u+16|0)>>2]){for(_=c[e+12>>2],a=f;a=(p=(0|_)>c[t+16>>2])?a:t,t=c[(p?t+4|0:t)>>2];);if(!((0|f)==(0|a)|(0|_)<c[a+16>>2])&&(t=c[a+24>>2]))for(_=a+20|0,p=(f=(a=k[e+11|0])<<24>>24<0)?c[e>>2]:e,a=f?c[e+4>>2]:a;;){t:{f:{a:{n:{A:{o:{if(y=(m=(f=(s=(f=k[t+27|0])<<24>>24<0)?c[t+20>>2]:f)>>>0<a>>>0)?f:a){if(v=Ye(p,s=s?c[t+16>>2]:t+16|0,y))break o;if(f>>>0<=a>>>0)break A;break t}if(f>>>0<=a>>>0)break n;break t}if((0|v)<0)break t}if(f=Ye(s,p,y))break a}if(m)break f;a=se(_,e);break i}if(!((0|f)<0)){a=se(_,e);break i}}t=t+4|0}if(!(t=c[t>>2]))break}}a=se(u,e)}if(n[e+11|0]<0&&er(c[e>>2]),a){a=0,f=c[c[d+c[r+36>>2]>>2]+8>>2],c[f+64>>2]||(t=vi(32),c[t+16>>2]=0,c[t+20>>2]=0,c[t+8>>2]=0,c[t>>2]=0,c[t+4>>2]=0,c[t+24>>2]=0,c[t+28>>2]=0,b=c[f+64>>2],c[f+64>>2]=t,b&&((t=c[b>>2])&&(c[b+4>>2]=t,er(t)),er(b),t=c[f+64>>2]),c[f>>2]=t,b=c[t+20>>2],c[f+8>>2]=c[t+16>>2],c[f+12>>2]=b,b=c[t+24>>2],t=c[t+28>>2],c[f+48>>2]=0,c[f+52>>2]=0,c[f+40>>2]=0,c[f+44>>2]=0,c[f+16>>2]=b,c[f+20>>2]=t);i:if(n[f+24|0]=k[A+24|0],c[f+28>>2]=c[A+28>>2],n[f+32|0]=k[A+32|0],t=c[A+44>>2],c[f+40>>2]=c[A+40>>2],c[f+44>>2]=t,t=c[A+52>>2],c[f+48>>2]=c[A+48>>2],c[f+52>>2]=t,c[f+56>>2]=c[A+56>>2],t=c[A+12>>2],c[f+8>>2]=c[A+8>>2],c[f+12>>2]=t,t=c[A+20>>2],c[f+16>>2]=c[A+16>>2],c[f+20>>2]=t,c[f+60>>2]=c[A+60>>2],(b=c[A>>2])?(t=0,(u=c[f>>2])&&(Dr(u,t=c[b>>2],b=c[b+4>>2]-t|0,0),t=1)):(c[f>>2]=0,t=1),t){n[f+84|0]=k[A+84|0],c[f+80>>2]=c[A+80>>2],(0|f)!=(0|A)&&Pr(f+68|0,c[A+68>>2],c[A+72>>2]);t:{f:{if(u=c[A+88>>2]){if(b=vi(40),A=c[u>>2],c[b+16>>2]=0,c[b+8>>2]=0,c[b+12>>2]=0,c[b>>2]=A,(0|(A=c[u+12>>2]))!=(0|(t=c[u+8>>2]))){if((0|(t=A-t|0))<0)break t;if(A=vi(t),c[b+12>>2]=A,c[b+8>>2]=A,c[b+16>>2]=t+A,(0|(t=c[u+8>>2]))!=(0|(_=c[u+12>>2]))){if(p=_+(-1^t)|0,s=_-t&7)for(;n[0|A]=k[0|t],A=A+1|0,t=t+1|0,(0|s)!=(0|(a=a+1|0)););if(!(p>>>0<7))for(;n[0|A]=k[0|t],n[A+1|0]=k[t+1|0],n[A+2|0]=k[t+2|0],n[A+3|0]=k[t+3|0],n[A+4|0]=k[t+4|0],n[A+5|0]=k[t+5|0],n[A+6|0]=k[t+6|0],n[A+7|0]=k[t+7|0],A=A+8|0,(0|_)!=(0|(t=t+8|0)););}c[b+12>>2]=A}if(a=c[u+36>>2],c[b+32>>2]=c[u+32>>2],c[b+36>>2]=a,a=c[u+28>>2],c[b+24>>2]=c[u+24>>2],c[b+28>>2]=a,A=c[f+88>>2],c[f+88>>2]=b,A)break f;break i}if(A=c[f+88>>2],c[f+88>>2]=0,!A)break i}(a=c[A+8>>2])&&(c[A+12>>2]=a,er(a)),er(A);break i}mt(),o()}break e}}if(a=c[c[r+36>>2]+(l<<2)>>2],!(0|Zt[c[c[a>>2]+24>>2]](a,h)))break r}if(b=(0|i)<=(0|(l=l+1|0)),(0|l)==(0|i))break}return Z=e+16|0,0|b},function(r,e){r|=0,r=0;r:switch(0|(e|=0)){case 0:return r=vi(20),c[r+12>>2]=-1,c[r+16>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[r>>2]=1920,0|r;case 1:return r=vi(24),c[r+12>>2]=-1,c[r+16>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[r>>2]=1920,c[r+20>>2]=0,c[r>>2]=2136,0|r;case 2:return r=vi(48),c[r+12>>2]=-1,c[r+16>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[r>>2]=1920,c[r+20>>2]=0,c[r>>2]=2136,c[r+24>>2]=1624,c[r>>2]=7948,c[r+32>>2]=0,c[r+36>>2]=0,c[r+28>>2]=-1,c[r+40>>2]=0,c[r+44>>2]=0,0|r;case 3:r=vi(32),c[r+12>>2]=-1,c[r+16>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,c[r>>2]=1920,c[r+20>>2]=0,c[r>>2]=2136,c[r+24>>2]=1032,c[r>>2]=5812,c[r+28>>2]=-1}return 0|r},ci,si,Ni,function(r,e){return e|=0,0|Zt[c[c[(r|=0)>>2]+48>>2]](r,c[e+4>>2]-c[e>>2]>>2)},function(r,e,i){r|=0,e|=0;var t,f,a=0,A=0,o=0,b=0,u=0,k=0,_=0;r:if(!((0|(o=t=c[12+(i|=0)>>2]))<=(0|(A=c[i+20>>2]))&(f=c[i+8>>2])>>>0<=(b=c[i+16>>2])>>>0|(0|A)>(0|o))){if(u=c[i>>2],k=n[u+b|0],a=A,a=(o=b+1|0)?a:a+1|0,c[i+16>>2]=o,c[i+20>>2]=a,-2!=(0|k)){if((0|a)>=(0|t)&o>>>0>=f>>>0|(0|a)>(0|t))break r;if(a=n[o+u|0],A=(b=b+2|0)>>>0<2?A+1|0:A,c[i+16>>2]=b,c[i+20>>2]=A,(a-4&255)>>>0<251)break r;A=0|Zt[c[c[r>>2]+40>>2]](r,k,a),a=c[r+20>>2],c[r+20>>2]=A,a&&Zt[c[c[a>>2]+4>>2]](a)}(!(a=c[r+20>>2])||0|Zt[c[c[r>>2]+28>>2]](r,a))&&(_=0|Zt[c[c[r>>2]+36>>2]](r,e,i))}return 0|_},Ar,function(r,e,i){r|=0,e|=0;var t,f=0,a=0,A=0,b=0,u=0,k=0,_=0,s=0;if(Z=t=Z-48|0,1==(0|(i|=0))){u=c[r+4>>2],i=c[r+12>>2],c[t+40>>2]=0,c[t+32>>2]=0,c[t+36>>2]=0,c[t+24>>2]=0,c[t+28>>2]=0,c[t+16>>2]=0,c[t+20>>2]=0,c[t+8>>2]=0,c[t+12>>2]=0,f=t+8|0;r:if(-2!=(0|e)){if(_=c[c[c[u+4>>2]+8>>2]+(i<<2)>>2],1==(0|Zt[c[c[u>>2]+8>>2]](u))){Z=k=Z-32|0,s=c[c[c[u+4>>2]+8>>2]+(i<<2)>>2];e:{i:{if(!(1!=(0|Zt[c[c[u>>2]+8>>2]](u))|e-1>>>0>5||!(b=0|Zt[c[c[u>>2]+36>>2]](u))|!(A=0|Zt[c[c[u>>2]+44>>2]](u,i))))if(i=0|Zt[c[c[u>>2]+40>>2]](u,i)){r=c[u+44>>2],c[k+12>>2]=i,c[k+8>>2]=r,c[k+20>>2]=A,c[k+16>>2]=A+12,A=k+8|0,r=0;t:{f:switch(e-1|0){case 0:if(e=vi(60),c[e+4>>2]=s,c[e>>2]=2960,r=c[f+4>>2],c[e+8>>2]=c[f>>2],c[e+12>>2]=r,r=c[f+12>>2],c[e+16>>2]=c[f+8>>2],c[e+20>>2]=r,r=c[f+20>>2],c[e+24>>2]=c[f+16>>2],c[e+28>>2]=r,c[e+40>>2]=0,c[e+32>>2]=0,c[e+36>>2]=0,(0|(r=c[f+24>>2]))!=(0|(b=c[f+28>>2]))){if((0|(i=b-r|0))<0)break i;for(a=vi(i),c[e+32>>2]=a,c[e+40>>2]=(-4&i)+a;c[a>>2]=c[r>>2],a=a+4|0,(0|b)!=(0|(r=r+4|0)););c[e+36>>2]=a}r=c[A+4>>2],c[e+44>>2]=c[A>>2],c[e+48>>2]=r,r=c[A+12>>2],c[e+52>>2]=c[A+8>>2],c[e+56>>2]=r,c[e>>2]=2252,r=e;break t;case 3:if(e=vi(112),c[e+4>>2]=s,c[e>>2]=2960,r=c[f+4>>2],c[e+8>>2]=c[f>>2],c[e+12>>2]=r,r=c[f+12>>2],c[e+16>>2]=c[f+8>>2],c[e+20>>2]=r,r=c[f+20>>2],c[e+24>>2]=c[f+16>>2],c[e+28>>2]=r,c[e+40>>2]=0,c[e+32>>2]=0,c[e+36>>2]=0,(0|(r=c[f+24>>2]))!=(0|(b=c[f+28>>2]))){if((0|(i=b-r|0))<0)break i;for(a=vi(i),c[e+32>>2]=a,c[e+40>>2]=(-4&i)+a;c[a>>2]=c[r>>2],a=a+4|0,(0|b)!=(0|(r=r+4|0)););c[e+36>>2]=a}r=c[A+4>>2],c[e+44>>2]=c[A>>2],c[e+48>>2]=r,r=c[A+12>>2],c[e+52>>2]=c[A+8>>2],c[e+56>>2]=r,c[e+60>>2]=0,c[e+64>>2]=0,c[e>>2]=3016,c[e+68>>2]=0,c[e+72>>2]=0,c[e+76>>2]=0,c[e+80>>2]=0,c[e+84>>2]=0,c[e+88>>2]=0,c[e+92>>2]=0,c[e+96>>2]=0,c[e+100>>2]=0,c[e+104>>2]=0,c[e+108>>2]=0,r=e;break t;case 4:if(e=vi(104),c[e+4>>2]=s,c[e>>2]=2960,r=c[f+4>>2],c[e+8>>2]=c[f>>2],c[e+12>>2]=r,r=c[f+12>>2],c[e+16>>2]=c[f+8>>2],c[e+20>>2]=r,r=c[f+20>>2],c[e+24>>2]=c[f+16>>2],c[e+28>>2]=r,c[e+40>>2]=0,c[e+32>>2]=0,c[e+36>>2]=0,(0|(r=c[f+24>>2]))!=(0|(b=c[f+28>>2]))){if((0|(i=b-r|0))<0)break i;for(a=vi(i),c[e+32>>2]=a,c[e+40>>2]=(-4&i)+a;c[a>>2]=c[r>>2],a=a+4|0,(0|b)!=(0|(r=r+4|0)););c[e+36>>2]=a}r=c[A+4>>2],c[e+44>>2]=c[A>>2],c[e+48>>2]=r,r=c[A+12>>2],c[e+52>>2]=c[A+8>>2],c[e+56>>2]=r,c[e+84>>2]=0,c[e+76>>2]=0,c[e+80>>2]=0,c[e+60>>2]=0,c[e+64>>2]=0,c[e>>2]=3264,r=c[A+4>>2],c[e+88>>2]=c[A>>2],c[e+92>>2]=r,r=c[A+12>>2],c[e+96>>2]=c[A+8>>2],c[e+100>>2]=r,r=e;break t;case 5:break f;default:break t}r=vi(128),c[r+4>>2]=s,c[r>>2]=2960,e=c[f+4>>2],c[r+8>>2]=c[f>>2],c[r+12>>2]=e,e=c[f+12>>2],c[r+16>>2]=c[f+8>>2],c[r+20>>2]=e,e=c[f+20>>2],c[r+24>>2]=c[f+16>>2],c[r+28>>2]=e,c[r+40>>2]=0,c[r+32>>2]=0,c[r+36>>2]=0;f:{a:{if((0|(i=c[f+28>>2]))!=(0|(e=c[f+24>>2]))){if((0|(i=i-e|0))<0)break a;if(e=vi(i),c[r+36>>2]=e,c[r+32>>2]=e,c[r+40>>2]=(-4&i)+e,(0|(a=c[f+24>>2]))!=(0|(i=c[f+28>>2])))for(;c[e>>2]=c[a>>2],e=e+4|0,(0|i)!=(0|(a=a+4|0)););c[r+36>>2]=e}c[r>>2]=2904,e=c[A+4>>2],c[r+44>>2]=c[A>>2],c[r+48>>2]=e,e=c[A+12>>2],c[r+52>>2]=c[A+8>>2],c[r+56>>2]=e,c[(e=r- -64|0)>>2]=0,c[e+4>>2]=0,c[r+60>>2]=4128,c[r>>2]=3500,e=c[A+4>>2],c[r+72>>2]=c[A>>2],c[r+76>>2]=e,e=c[A+12>>2],c[r+80>>2]=c[A+8>>2],c[r+84>>2]=e,c[r+104>>2]=1065353216,c[r+108>>2]=-1,c[r+96>>2]=-1,c[r+100>>2]=-1,c[r+88>>2]=1,c[r+92>>2]=-1,c[r+60>>2]=3736,c[r+112>>2]=0,c[r+116>>2]=0,n[r+117|0]=0,n[r+118|0]=0,n[r+119|0]=0,n[r+120|0]=0,n[r+121|0]=0,n[r+122|0]=0,n[r+123|0]=0,n[r+124|0]=0;break f}mt(),o()}}a=r}else{r=c[u+44>>2],c[k+12>>2]=b,c[k+8>>2]=r,c[k+20>>2]=A,c[k+16>>2]=A+12,A=k+8|0,r=0;t:{f:switch(e-1|0){case 0:if(e=vi(60),c[e+4>>2]=s,c[e>>2]=2960,r=c[f+4>>2],c[e+8>>2]=c[f>>2],c[e+12>>2]=r,r=c[f+12>>2],c[e+16>>2]=c[f+8>>2],c[e+20>>2]=r,r=c[f+20>>2],c[e+24>>2]=c[f+16>>2],c[e+28>>2]=r,c[e+40>>2]=0,c[e+32>>2]=0,c[e+36>>2]=0,(0|(r=c[f+24>>2]))!=(0|(b=c[f+28>>2]))){if((0|(i=b-r|0))<0)break i;for(a=vi(i),c[e+32>>2]=a,c[e+40>>2]=(-4&i)+a;c[a>>2]=c[r>>2],a=a+4|0,(0|b)!=(0|(r=r+4|0)););c[e+36>>2]=a}r=c[A+4>>2],c[e+44>>2]=c[A>>2],c[e+48>>2]=r,r=c[A+12>>2],c[e+52>>2]=c[A+8>>2],c[e+56>>2]=r,c[e>>2]=4156,r=e;break t;case 3:if(e=vi(112),c[e+4>>2]=s,c[e>>2]=2960,r=c[f+4>>2],c[e+8>>2]=c[f>>2],c[e+12>>2]=r,r=c[f+12>>2],c[e+16>>2]=c[f+8>>2],c[e+20>>2]=r,r=c[f+20>>2],c[e+24>>2]=c[f+16>>2],c[e+28>>2]=r,c[e+40>>2]=0,c[e+32>>2]=0,c[e+36>>2]=0,(0|(r=c[f+24>>2]))!=(0|(b=c[f+28>>2]))){if((0|(i=b-r|0))<0)break i;for(a=vi(i),c[e+32>>2]=a,c[e+40>>2]=(-4&i)+a;c[a>>2]=c[r>>2],a=a+4|0,(0|b)!=(0|(r=r+4|0)););c[e+36>>2]=a}r=c[A+4>>2],c[e+44>>2]=c[A>>2],c[e+48>>2]=r,r=c[A+12>>2],c[e+52>>2]=c[A+8>>2],c[e+56>>2]=r,c[e+60>>2]=0,c[e+64>>2]=0,c[e>>2]=4580,c[e+68>>2]=0,c[e+72>>2]=0,c[e+76>>2]=0,c[e+80>>2]=0,c[e+84>>2]=0,c[e+88>>2]=0,c[e+92>>2]=0,c[e+96>>2]=0,c[e+100>>2]=0,c[e+104>>2]=0,c[e+108>>2]=0,r=e;break t;case 4:if(e=vi(104),c[e+4>>2]=s,c[e>>2]=2960,r=c[f+4>>2],c[e+8>>2]=c[f>>2],c[e+12>>2]=r,r=c[f+12>>2],c[e+16>>2]=c[f+8>>2],c[e+20>>2]=r,r=c[f+20>>2],c[e+24>>2]=c[f+16>>2],c[e+28>>2]=r,c[e+40>>2]=0,c[e+32>>2]=0,c[e+36>>2]=0,(0|(r=c[f+24>>2]))!=(0|(b=c[f+28>>2]))){if((0|(i=b-r|0))<0)break i;for(a=vi(i),c[e+32>>2]=a,c[e+40>>2]=(-4&i)+a;c[a>>2]=c[r>>2],a=a+4|0,(0|b)!=(0|(r=r+4|0)););c[e+36>>2]=a}r=c[A+4>>2],c[e+44>>2]=c[A>>2],c[e+48>>2]=r,r=c[A+12>>2],c[e+52>>2]=c[A+8>>2],c[e+56>>2]=r,c[e+84>>2]=0,c[e+76>>2]=0,c[e+80>>2]=0,c[e+60>>2]=0,c[e+64>>2]=0,c[e>>2]=4816,r=c[A+4>>2],c[e+88>>2]=c[A>>2],c[e+92>>2]=r,r=c[A+12>>2],c[e+96>>2]=c[A+8>>2],c[e+100>>2]=r,r=e;break t;case 5:break f;default:break t}r=vi(128),c[r+4>>2]=s,c[r>>2]=2960,e=c[f+4>>2],c[r+8>>2]=c[f>>2],c[r+12>>2]=e,e=c[f+12>>2],c[r+16>>2]=c[f+8>>2],c[r+20>>2]=e,e=c[f+20>>2],c[r+24>>2]=c[f+16>>2],c[r+28>>2]=e,c[r+40>>2]=0,c[r+32>>2]=0,c[r+36>>2]=0;f:{a:{if((0|(i=c[f+28>>2]))!=(0|(e=c[f+24>>2]))){if((0|(i=i-e|0))<0)break a;if(e=vi(i),c[r+36>>2]=e,c[r+32>>2]=e,c[r+40>>2]=(-4&i)+e,(0|(a=c[f+24>>2]))!=(0|(i=c[f+28>>2])))for(;c[e>>2]=c[a>>2],e=e+4|0,(0|i)!=(0|(a=a+4|0)););c[r+36>>2]=e}c[r>>2]=4524,e=c[A+4>>2],c[r+44>>2]=c[A>>2],c[r+48>>2]=e,e=c[A+12>>2],c[r+52>>2]=c[A+8>>2],c[r+56>>2]=e,c[(e=r- -64|0)>>2]=0,c[e+4>>2]=0,c[r+60>>2]=5624,c[r>>2]=5040,e=c[A+4>>2],c[r+72>>2]=c[A>>2],c[r+76>>2]=e,e=c[A+12>>2],c[r+80>>2]=c[A+8>>2],c[r+84>>2]=e,c[r+104>>2]=1065353216,c[r+108>>2]=-1,c[r+96>>2]=-1,c[r+100>>2]=-1,c[r+88>>2]=1,c[r+92>>2]=-1,c[r+60>>2]=5260,c[r+112>>2]=0,c[r+116>>2]=0,n[r+117|0]=0,n[r+118|0]=0,n[r+119|0]=0,n[r+120|0]=0,n[r+121|0]=0,n[r+122|0]=0,n[r+123|0]=0,n[r+124|0]=0;break f}mt(),o()}}a=r}Z=k+32|0;break e}mt(),o()}if(a)break r}a=vi(44),c[a+4>>2]=_,c[a>>2]=2960,r=c[f+4>>2],c[a+8>>2]=c[f>>2],c[a+12>>2]=r,r=c[f+12>>2],c[a+16>>2]=c[f+8>>2],c[a+20>>2]=r,r=c[f+20>>2],c[a+24>>2]=c[f+16>>2],c[a+28>>2]=r,c[a+40>>2]=0,c[a+32>>2]=0,c[a+36>>2]=0;e:{if((0|(i=c[f+24>>2]))!=(0|(e=c[f+28>>2]))){if((0|(r=e-i|0))<0)break e;for(_=vi(r),c[a+32>>2]=_,c[a+40>>2]=(-4&r)+_;c[_>>2]=c[i>>2],_=_+4|0,(0|e)!=(0|(i=i+4|0)););c[a+36>>2]=_}c[a>>2]=5652;break r}mt(),o()}_=a,(r=c[t+32>>2])&&(c[t+36>>2]=r,er(r))}return Z=t+48|0,0|_},function(r){return k[c[8+(r|=0)>>2]+24|0]},function(r,e){e|=0;var i,t=0,f=0,a=0,o=0,b=0,u=0,_=0,s=0,p=0,l=0,y=0,d=0;i=r|=0;r:{e:{i:{t:{f:{a:{n:{A:switch(r=c[r+8>>2],c[r+28>>2]-1|0){case 4:break i;case 5:break t;case 2:break f;case 3:break a;case 0:break n;case 1:break A;default:break r}if(t=vi(o=k[r+24|0]),r=c[i+16>>2],b=c[r+80>>2]?c[c[r>>2]>>2]+c[r+48>>2]|0:0,!e)break e;if(o){for(d=252&o,p=3&o,u=o>>>0<4;;){if(r=0,a=0,!u)for(;s=b+(f<<2)|0,n[r+t|0]=c[s>>2],n[(1|r)+t|0]=c[s+4>>2],n[(2|r)+t|0]=c[s+8>>2],n[(3|r)+t|0]=c[s+12>>2],r=r+4|0,f=f+4|0,(0|d)!=(0|(a=a+4|0)););if(a=0,p)for(;n[r+t|0]=c[b+(f<<2)>>2],r=r+1|0,f=f+1|0,(0|p)!=(0|(a=a+1|0)););if(hr(c[c[c[i+8>>2]+64>>2]>>2]+l|0,t,o),l=o+l|0,(0|(y=y+1|0))==(0|e))break}break e}if(r=0,1!=(0|e))for(b=-2&e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,o),hr((r=r+o|0)+c[c[c[i+8>>2]+64>>2]>>2]|0,t,o),r=r+o|0,(0|b)!=(0|(f=f+2|0)););if(!(1&e))break e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,o);break e}if(t=vi(o=k[r+24|0]),r=c[i+16>>2],b=c[r+80>>2]?c[c[r>>2]>>2]+c[r+48>>2]|0:0,!e)break e;if(o){for(d=252&o,p=3&o,u=o>>>0<4;;){if(r=0,a=0,!u)for(;s=b+(f<<2)|0,n[r+t|0]=c[s>>2],n[(1|r)+t|0]=c[s+4>>2],n[(2|r)+t|0]=c[s+8>>2],n[(3|r)+t|0]=c[s+12>>2],r=r+4|0,f=f+4|0,(0|d)!=(0|(a=a+4|0)););if(a=0,p)for(;n[r+t|0]=c[b+(f<<2)>>2],r=r+1|0,f=f+1|0,(0|p)!=(0|(a=a+1|0)););if(hr(c[c[c[i+8>>2]+64>>2]>>2]+l|0,t,o),l=o+l|0,(0|(y=y+1|0))==(0|e))break}break e}if(r=0,1!=(0|e))for(b=-2&e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,o),hr((r=r+o|0)+c[c[c[i+8>>2]+64>>2]>>2]|0,t,o),r=r+o|0,(0|b)!=(0|(f=f+2|0)););if(!(1&e))break e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,o);break e}if(t=vi(_=(u=k[r+24|0])<<1),r=c[i+16>>2],b=c[r+80>>2]?c[c[r>>2]>>2]+c[r+48>>2]|0:0,!e)break e;if(u){for(d=252&u,p=3&u,u=u>>>0<4;;){if(r=0,a=0,!u)for(;s=b+(f<<2)|0,A[(o=r<<1)+t>>1]=c[s>>2],A[(2|o)+t>>1]=c[s+4>>2],A[(4|o)+t>>1]=c[s+8>>2],A[(6|o)+t>>1]=c[s+12>>2],r=r+4|0,f=f+4|0,(0|d)!=(0|(a=a+4|0)););if(a=0,p)for(;A[(r<<1)+t>>1]=c[b+(f<<2)>>2],r=r+1|0,f=f+1|0,(0|p)!=(0|(a=a+1|0)););if(hr(c[c[c[i+8>>2]+64>>2]>>2]+y|0,t,_),y=_+y|0,(0|(l=l+1|0))==(0|e))break}break e}if(r=0,1!=(0|e))for(b=-2&e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,_),hr((r=r+_|0)+c[c[c[i+8>>2]+64>>2]>>2]|0,t,_),r=r+_|0,(0|b)!=(0|(f=f+2|0)););if(!(1&e))break e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,_);break e}if(t=vi(_=(u=k[r+24|0])<<1),r=c[i+16>>2],b=c[r+80>>2]?c[c[r>>2]>>2]+c[r+48>>2]|0:0,!e)break e;if(u){for(d=252&u,p=3&u,u=u>>>0<4;;){if(r=0,a=0,!u)for(;s=b+(f<<2)|0,A[(o=r<<1)+t>>1]=c[s>>2],A[(2|o)+t>>1]=c[s+4>>2],A[(4|o)+t>>1]=c[s+8>>2],A[(6|o)+t>>1]=c[s+12>>2],r=r+4|0,f=f+4|0,(0|d)!=(0|(a=a+4|0)););if(a=0,p)for(;A[(r<<1)+t>>1]=c[b+(f<<2)>>2],r=r+1|0,f=f+1|0,(0|p)!=(0|(a=a+1|0)););if(hr(c[c[c[i+8>>2]+64>>2]>>2]+y|0,t,_),y=_+y|0,(0|(l=l+1|0))==(0|e))break}break e}if(r=0,1!=(0|e))for(b=-2&e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,_),hr((r=r+_|0)+c[c[c[i+8>>2]+64>>2]>>2]|0,t,_),r=r+_|0,(0|b)!=(0|(f=f+2|0)););if(!(1&e))break e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,_);break e}if(t=vi(_=(u=k[r+24|0])<<2),r=c[i+16>>2],b=c[r+80>>2]?c[c[r>>2]>>2]+c[r+48>>2]|0:0,!e)break e;if(u){for(d=252&u,p=3&u,u=u>>>0<4;;){if(r=0,a=0,!u)for(;s=b+(f<<2)|0,c[(o=r<<2)+t>>2]=c[s>>2],c[(4|o)+t>>2]=c[s+4>>2],c[(8|o)+t>>2]=c[s+8>>2],c[(12|o)+t>>2]=c[s+12>>2],r=r+4|0,f=f+4|0,(0|d)!=(0|(a=a+4|0)););if(a=0,p)for(;c[(r<<2)+t>>2]=c[b+(f<<2)>>2],r=r+1|0,f=f+1|0,(0|p)!=(0|(a=a+1|0)););if(hr(c[c[c[i+8>>2]+64>>2]>>2]+y|0,t,_),y=_+y|0,(0|(l=l+1|0))==(0|e))break}break e}if(r=0,1!=(0|e))for(b=-2&e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,_),hr((r=r+_|0)+c[c[c[i+8>>2]+64>>2]>>2]|0,t,_),r=r+_|0,(0|b)!=(0|(f=f+2|0)););if(!(1&e))break e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,_);break e}if(t=vi(_=(u=k[r+24|0])<<2),r=c[i+16>>2],b=c[r+80>>2]?c[c[r>>2]>>2]+c[r+48>>2]|0:0,e)if(u)for(d=252&u,p=3&u,u=u>>>0<4;;){if(r=0,a=0,!u)for(;s=b+(f<<2)|0,c[(o=r<<2)+t>>2]=c[s>>2],c[(4|o)+t>>2]=c[s+4>>2],c[(8|o)+t>>2]=c[s+8>>2],c[(12|o)+t>>2]=c[s+12>>2],r=r+4|0,f=f+4|0,(0|d)!=(0|(a=a+4|0)););if(a=0,p)for(;c[(r<<2)+t>>2]=c[b+(f<<2)>>2],r=r+1|0,f=f+1|0,(0|p)!=(0|(a=a+1|0)););if(hr(c[c[c[i+8>>2]+64>>2]>>2]+y|0,t,_),y=_+y|0,(0|(l=l+1|0))==(0|e))break}else{if(r=0,1!=(0|e))for(b=-2&e;hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,_),hr((r=r+_|0)+c[c[c[i+8>>2]+64>>2]>>2]|0,t,_),r=r+_|0,(0|b)!=(0|(f=f+2|0)););1&e&&hr(c[c[c[i+8>>2]+64>>2]>>2]+r|0,t,_)}}er(t),t=1}return 0|t},function(r){var e;return c[(r|=0)>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),0|r},Ei,ct,tt,Ui,pt,et,at,pt,ct,function(r,e){r|=0;var i,t,f,a,n=0,A=0,o=0,b=0,u=0,_=0;return A=c[8+(e|=0)>>2],i=o=c[e+12>>2],a=o=c[e+20>>2],f=n=(t=c[e+16>>2])+4|0,n>>>0>A>>>0&(0|(o=n>>>0<4?o+1|0:o))>=(0|i)|(0|o)>(0|i)||(u=c[e>>2],b=k[0|(n=u+t|0)]|k[n+1|0]<<8|k[n+2|0]<<16|k[n+3|0]<<24,c[e+16>>2]=f,c[e+20>>2]=o,n=A,A=a,n>>>0<(o=t+8|0)>>>0&(0|(A=o>>>0<8?A+1|0:A))>=(0|i)|(0|A)>(0|i)||(n=k[0|(n=f+u|0)]|k[n+1|0]<<8|k[n+2|0]<<16|k[n+3|0]<<24,c[e+16>>2]=o,c[e+20>>2]=A,(0|n)<(0|b)||(c[r+16>>2]=n,c[r+12>>2]=b,!(A=(n>>31)-((b>>31)+(n>>>0<b>>>0)|0)|0)&(e=n-b|0)>>>0>2147483646|A||(_=1,A=e+1|0,c[r+20>>2]=A,e=A>>>1|0,c[r+24>>2]=e,c[r+28>>2]=0-e,1&A||(c[r+24>>2]=e-1))))),0|_},function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A,b,u,k,_=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0;if(c[8+(r|=0)>>2]=f,s=c[(A=r+32|0)>>2],(a=c[r+36>>2]-s>>2)>>>0<f>>>0?(_e(A,f-a|0),t=c[r+8>>2]):(t=f)>>>0>=a>>>0||(c[r+36>>2]=s+(f<<2),t=f),k=c[r+52>>2],b=c[r+48>>2],a=0,n=Sr(vi(s=f>>>0>1073741823?-1:f<<2),0,s),!((0|t)<=0)){for(_=c[r+32>>2];(0|(s=c[(t=a<<2)+n>>2]))>(0|(p=c[r+16>>2]))?c[t+_>>2]=p:(t=t+_|0,p=c[r+12>>2],c[t>>2]=(0|p)>(0|s)?p:s),(0|(t=c[r+8>>2]))>(0|(a=a+1|0)););if(!((0|t)<=0))for(a=0;;){t=(s=a<<2)+i|0,s=c[e+s>>2]+c[_+s>>2]|0,c[t>>2]=s;r:{if((0|s)>c[r+16>>2])s=s-c[r+20>>2]|0;else{if((0|s)>=c[r+12>>2])break r;s=s+c[r+20>>2]|0}c[t>>2]=s}if(!((0|(t=c[r+8>>2]))>(0|(a=a+1|0))))break}}if(a=c[r+56>>2],u=c[a>>2],(0|(a=c[a+4>>2]-u|0))>=5)for(R=(m=a>>>2|0)>>>0<=2?2:m,N=-2&f,T=1&f,s=1;;){r:{e:{if((0|s)!=(0|m)){if(h=y(f,s),-1==(0|(a=c[(s<<2)+u>>2]))|c[c[b>>2]+(a>>>3&536870908)>>2]>>>a&1)break e;if(-1==(0|(a=c[c[c[b+64>>2]+12>>2]+(a<<2)>>2])))break e;if(p=c[k>>2],_=c[b+28>>2],(0|(d=c[p+(c[_+(a<<2)>>2]<<2)>>2]))>=(0|s))break e;if((0|(l=c[p+(c[_+((((l=a+1|0)>>>0)%3|0?l:a-2|0)<<2)>>2]<<2)>>2]))>=(0|s))break e;if((0|(a=c[p+(c[_+(a+((a>>>0)%3|0?-1:2)<<2)>>2]<<2)>>2]))>=(0|s))break e;if(!((0|f)<=0)){if(_=y(f,a),p=y(f,l),d=y(f,d),a=0,v=0,1!=(0|f))for(;c[(a<<2)+n>>2]=(c[(a+_<<2)+i>>2]+c[(a+p<<2)+i>>2]|0)-c[(a+d<<2)+i>>2],c[((l=1|a)<<2)+n>>2]=(c[(_+l<<2)+i>>2]+c[(p+l<<2)+i>>2]|0)-c[(d+l<<2)+i>>2],a=a+2|0,(0|N)!=(0|(v=v+2|0)););T&&(c[(a<<2)+n>>2]=(c[(a+_<<2)+i>>2]+c[(a+p<<2)+i>>2]|0)-c[(a+d<<2)+i>>2])}if((0|t)<=0)break r;for(p=c[A>>2],a=0;(0|(_=c[(t=a<<2)+n>>2]))>(0|(d=c[r+16>>2]))?c[t+p>>2]=d:(t=t+p|0,d=c[r+12>>2],c[t>>2]=(0|d)>(0|_)?d:_),(0|(t=c[r+8>>2]))>(0|(a=a+1|0)););if(a=0,(0|t)<=0)break r;for(d=(t=h<<2)+i|0,l=e+t|0;;){t=(_=a<<2)+d|0,_=c[_+l>>2]+c[_+p>>2]|0,c[t>>2]=_;i:{if((0|_)>c[r+16>>2])_=_-c[r+20>>2]|0;else{if((0|_)>=c[r+12>>2])break i;_=_+c[r+20>>2]|0}c[t>>2]=_}if(!((0|(t=c[r+8>>2]))>(0|(a=a+1|0))))break}break r}dt(),o()}if(!((0|t)<=0)){for(d=(y(s-1|0,f)<<2)+i|0,p=c[A>>2],a=0;(0|(_=c[(t=a<<2)+d>>2]))>(0|(l=c[r+16>>2]))?c[t+p>>2]=l:(t=t+p|0,l=c[r+12>>2],c[t>>2]=(0|l)>(0|_)?l:_),(0|(t=c[r+8>>2]))>(0|(a=a+1|0)););if(a=0,!((0|t)<=0))for(d=(t=h<<2)+i|0,l=e+t|0;;){t=(_=a<<2)+d|0,_=c[_+l>>2]+c[_+p>>2]|0,c[t>>2]=_;e:{if((0|_)>c[r+16>>2])_=_-c[r+20>>2]|0;else{if((0|_)>=c[r+12>>2])break e;_=_+c[r+20>>2]|0}c[t>>2]=_}if(!((0|(t=c[r+8>>2]))>(0|(a=a+1|0))))break}}}if((0|R)==(0|(s=s+1|0)))break}return er(n),1},ht,ht,function(r){var e=0;return c[(r|=0)>>2]=3016,(e=c[r+96>>2])&&er(e),(e=c[r+84>>2])&&er(e),(e=c[r+72>>2])&&er(e),(e=c[r+60>>2])&&er(e),c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),0|r},function(r){var e=0;c[(r|=0)>>2]=3016,(e=c[r+96>>2])&&er(e),(e=c[r+84>>2])&&er(e),(e=c[r+72>>2])&&er(e),(e=c[r+60>>2])&&er(e),c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),er(r)},kt,Ui,function(r,e){r|=0;var i,t=0,f=0,a=0,A=0,o=0,b=0,u=0,_=0,s=0,p=0;Z=i=Z-32|0;r:{e:if(Fe(1,i+28|0,e|=0)&&(f=c[i+28>>2],t=c[c[r+48>>2]+64>>2],!(f>>>0>c[t+4>>2]-c[t>>2]>>2>>>0)))i:{if(f){if(Zr(r+60|0,f),c[(t=i+8|0)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,!Er(t,e))break i;for(;A=1<<a,u=Se(t),o=c[r+60>>2]+(a>>>3&536870908)|0,b=u?A|c[o>>2]:c[o>>2]&(-1^A),c[o>>2]=b,(0|f)!=(0|(a=a+1|0)););}if(!Fe(1,i+28|0,e))break e;if(f=c[i+28>>2],t=c[c[r+48>>2]+64>>2],f>>>0>c[t+4>>2]-c[t>>2]>>2>>>0)break e;if(f){if(a=0,Zr(r+72|0,f),c[(t=i+8|0)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,!Er(t,e))break i;for(;A=1<<a,u=Se(t),o=c[r+72>>2]+(a>>>3&536870908)|0,b=u?A|c[o>>2]:c[o>>2]&(-1^A),c[o>>2]=b,(0|f)!=(0|(a=a+1|0)););}if(!Fe(1,i+28|0,e))break e;if(f=c[i+28>>2],t=c[c[r+48>>2]+64>>2],f>>>0>c[t+4>>2]-c[t>>2]>>2>>>0)break e;if(f){if(a=0,Zr(r+84|0,f),c[(t=i+8|0)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,!Er(t,e))break i;for(;A=1<<a,u=Se(t),o=c[r+84>>2]+(a>>>3&536870908)|0,b=u?A|c[o>>2]:c[o>>2]&(-1^A),c[o>>2]=b,(0|f)!=(0|(a=a+1|0)););}if(!Fe(1,i+28|0,e))break e;if(f=c[i+28>>2],t=c[c[r+48>>2]+64>>2],f>>>0>c[t+4>>2]-c[t>>2]>>2>>>0)break e;if(f){if(a=0,Zr(r+96|0,f),c[(t=i+8|0)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,!Er(t,e))break i;for(;A=1<<a,u=Se(t),o=c[r+96>>2]+(a>>>3&536870908)|0,b=u?A|c[o>>2]:c[o>>2]&(-1^A),c[o>>2]=b,(0|f)!=(0|(a=a+1|0)););}if(a=0,t=c[e+8>>2],A=c[e+12>>2],f=t,b=t=c[e+20>>2],f>>>0<(u=(o=c[e+16>>2])+4|0)>>>0&(0|(t=u>>>0<4?t+1|0:t))>=(0|A)|(0|t)>(0|A))break r;if(p=c[e>>2],s=k[0|(_=p+o|0)]|k[_+1|0]<<8|k[_+2|0]<<16|k[_+3|0]<<24,c[e+16>>2]=u,c[e+20>>2]=t,_=f,f=A,t=b,(A=o+8|0)>>>0>_>>>0&(0|(t=A>>>0<8?t+1|0:t))>=(0|f)|(0|t)>(0|f))break r;if(f=k[0|(f=u+p|0)]|k[f+1|0]<<8|k[f+2|0]<<16|k[f+3|0]<<24,c[e+16>>2]=A,c[e+20>>2]=t,(0|f)<(0|s))break r;if(c[r+16>>2]=f,c[r+12>>2]=s,!(t=(f>>31)-((s>>31)+(f>>>0<s>>>0)|0)|0)&(e=f-s|0)>>>0>2147483646|t)break r;if(a=1,e=e+1|0,c[r+20>>2]=e,t=e>>>1|0,c[r+24>>2]=t,c[r+28>>2]=0-t,1&e)break r;c[r+24>>2]=t-1;break r}a=0}return Z=i+32|0,0|a},function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A,b,u,k,_=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,E=0,F=0,I=0,Y=0,w=0,j=0,J=0,B=0,M=0,Q=0,g=0,O=0;Z=n=Z+-64|0,c[8+(r|=0)>>2]=f,a=c[(b=r+32|0)>>2];r:{if((t=c[r+36>>2]-a>>2)>>>0<f>>>0)_e(b,f-t|0),c[n+56>>2]=0,c[n+60>>2]=0,c[n+48>>2]=0,c[n+52>>2]=0,c[n+40>>2]=0,c[n+44>>2]=0,c[n+32>>2]=0,c[n+36>>2]=0,c[n+24>>2]=0,c[n+28>>2]=0,c[n+16>>2]=0,c[n+20>>2]=0,c[n>>2]=0;else if(t>>>0>f>>>0&&(c[r+36>>2]=a+(f<<2)),c[n+56>>2]=0,c[n+60>>2]=0,c[n+48>>2]=0,c[n+52>>2]=0,c[n+40>>2]=0,c[n+44>>2]=0,c[n+32>>2]=0,c[n+36>>2]=0,c[n+24>>2]=0,c[n+28>>2]=0,c[n+16>>2]=0,c[n+20>>2]=0,c[n>>2]=0,t=0,!f)break r;Nr(n+16|0,f,n),_=c[n+28>>2],t=c[n+32>>2]}c[n>>2]=0;r:if((t=t-_>>2)>>>0>=f>>>0){if(t>>>0<=f>>>0)break r;c[n+32>>2]=(f<<2)+_}else Nr(n+16|12,f-t|0,n);c[n>>2]=0,a=c[n+40>>2];r:if((t=c[n+44>>2]-a>>2)>>>0>=f>>>0){if(t>>>0<=f>>>0)break r;c[n+44>>2]=a+(f<<2)}else Nr(n+40|0,f-t|0,n);c[n>>2]=0,a=c[n+52>>2];r:if((t=c[n+56>>2]-a>>2)>>>0>=f>>>0){if(t>>>0<=f>>>0)break r;c[n+56>>2]=a+(f<<2)}else Nr(n+52|0,f-t|0,n);if(!(c[r+8>>2]<=0)){for(s=c[n+16>>2],p=c[r+32>>2],_=0;(0|(a=c[(t=_<<2)+s>>2]))>(0|(m=c[r+16>>2]))?c[t+p>>2]=m:(t=t+p|0,m=c[r+12>>2],c[t>>2]=(0|m)>(0|a)?m:a),(0|(_=_+1|0))<(0|(t=c[r+8>>2])););if(!((0|t)<=0))for(t=0;;){a=(s=t<<2)+i|0,s=c[e+s>>2]+c[p+s>>2]|0,c[a>>2]=s;r:{if((0|s)>c[r+16>>2])s=s-c[r+20>>2]|0;else{if((0|s)>=c[r+12>>2])break r;s=s+c[r+20>>2]|0}c[a>>2]=s}if(!((0|(t=t+1|0))<c[r+8>>2]))break}}k=c[r+52>>2],A=c[r+48>>2],u=vi(16),c[(t=u)>>2]=0,c[t+4>>2]=0,c[t+8>>2]=0,c[t+12>>2]=0,c[n+8>>2]=0,c[n>>2]=0,c[n+4>>2]=0;r:{if(f){if(f>>>0>=1073741824)break r;T=vi(t=f<<2),c[n>>2]=T,c[n+8>>2]=t+T,Sr(T,0,t)}G=1,t=c[r+56>>2],E=c[t>>2];e:if(!((0|(t=c[t+4>>2]-E|0))<8))for(j=(0|(W=t>>2))<=2?2:W,J=W>>>0<=1?1:W,F=-2&f,I=1&f,B=-4&f,Y=3&f,w=f-1|0,M=f<<2,Q=f>>>0<4,G=0,m=1;;){i:{t:{f:{a:{if((0|m)!=(0|J)){n:{A:if(-1!=(0|(a=c[(m<<2)+E>>2]))){l=1,t=a+2|0,V=1<<(D=(p=(a>>>0)%3|0)?a-1|0:t),g=(v=c[A>>2])+(D>>>3&536870908)|0,s=0,O=0!=(0|p)|-1!=(0|t),t=a;o:{for(;;){if(!(c[v+(t>>>3&536870908)>>2]>>>t&1||-1==(0|(p=c[c[c[A+64>>2]+12>>2]+(t<<2)>>2]))||(d=c[k>>2],_=c[A+28>>2],(0|(R=c[d+(c[_+(p<<2)>>2]<<2)>>2]))>=(0|m)||(0|(N=c[d+(c[_+((((N=p+1|0)>>>0)%3|0?N:p-2|0)<<2)>>2]<<2)>>2]))>=(0|m)||(0|(_=c[d+(c[_+(p+((p>>>0)%3|0?-1:2)<<2)>>2]<<2)>>2]))>=(0|m)))){if(f){if(p=c[(n+16|0)+y(s,12)>>2],d=y(f,_),N=y(f,N),R=y(f,R),_=0,h=0,w)for(;c[p+(_<<2)>>2]=(c[(_+d<<2)+i>>2]+c[(_+N<<2)+i>>2]|0)-c[(_+R<<2)+i>>2],c[p+((U=1|_)<<2)>>2]=(c[(d+U<<2)+i>>2]+c[(N+U<<2)+i>>2]|0)-c[(R+U<<2)+i>>2],_=_+2|0,(0|F)!=(0|(h=h+2|0)););I&&(c[p+(_<<2)>>2]=(c[(_+d<<2)+i>>2]+c[(_+N<<2)+i>>2]|0)-c[(_+R<<2)+i>>2])}if(p=4,4==(0|(s=s+1|0)))break o}b:if(1&l){if(_=t-2|0,p=t+1|0,t=-1,-1==(0|(p=(p>>>0)%3|0?p:_))|c[v+(p>>>3&536870908)>>2]>>>p&1)break b;if(-1==(0|(p=c[c[c[A+64>>2]+12>>2]+(p<<2)>>2])))break b;t=((t=p+1|0)>>>0)%3|0?t:p-2|0}else{if((t>>>0)%3|0)_=t-1|0;else if(_=t+2|0,t=-1,-1==(0|_))break b;t=-1,c[v+(_>>>3&536870908)>>2]>>>_&1||-1!=(0|(p=c[c[c[A+64>>2]+12>>2]+(_<<2)>>2]))&&(t=(p>>>0)%3|0?p-1|0:p+2|0)}b:if((0|t)!=(0|a)){if(-1==(0|t)&l){if(!O|V&c[g>>2])break b;if(-1==(0|(t=c[c[c[A+64>>2]+12>>2]+(D<<2)>>2])))break b;l=0,t=(t>>>0)%3|0?t-1|0:t+2|0}if(-1!=(0|t))continue}break}if((0|(p=s))<=0)break A}for(f&&Sr(T,0,M),N=((t=p-1|0)<<2)+u|0,U=t=y(t,12)+r|0,D=c[t- -64>>2],l=0,t=c[n>>2],a=0;;){if(s=c[N>>2],c[N>>2]=s+1,s>>>0>=D>>>0)break e;if(!(c[c[U+60>>2]+(s>>>3&536870908)>>2]>>>s&1)&&(a=a+1|0,f)){if(v=c[(n+16|0)+y(l,12)>>2],s=0,_=0,R=0,!Q)for(;c[(h=(d=_<<2)+t|0)>>2]=c[d+v>>2]+c[h>>2],c[(V=(h=4|d)+t|0)>>2]=c[v+h>>2]+c[V>>2],c[(V=(h=8|d)+t|0)>>2]=c[v+h>>2]+c[V>>2],c[(h=(d|=12)+t|0)>>2]=c[d+v>>2]+c[h>>2],_=_+4|0,(0|B)!=(0|(R=R+4|0)););if(Y)for(;c[(R=(d=_<<2)+t|0)>>2]=c[d+v>>2]+c[R>>2],_=_+1|0,(0|Y)!=(0|(s=s+1|0)););}if((0|(l=l+1|0))==(0|p))break}if(s=y(f,m),!a)break n;if(!f)break t;if(_=0,t=0,w)break a;break f}s=y(f,m)}if(c[r+8>>2]<=0)break i;for(l=(y(m-1|0,f)<<2)+i|0,p=c[b>>2],_=0;(0|(a=c[(t=_<<2)+l>>2]))>(0|(v=c[r+16>>2]))?c[t+p>>2]=v:(t=t+p|0,v=c[r+12>>2],c[t>>2]=(0|v)>(0|a)?v:a),(0|(_=_+1|0))<(0|(a=c[r+8>>2])););if(t=0,(0|a)<=0)break i;for(_=(a=s<<2)+i|0,l=e+a|0;;){a=(s=t<<2)+_|0,s=c[s+l>>2]+c[p+s>>2]|0,c[a>>2]=s;n:{if((0|s)>c[r+16>>2])s=s-c[r+20>>2]|0;else{if((0|s)>=c[r+12>>2])break n;s=s+c[r+20>>2]|0}c[a>>2]=s}if(!((0|(t=t+1|0))<c[r+8>>2]))break}break i}dt(),o()}for(;c[(l=(p=_<<2)+T|0)>>2]=c[l>>2]/(0|a),c[(p=(4|p)+T|0)>>2]=c[p>>2]/(0|a),_=_+2|0,(0|F)!=(0|(t=t+2|0)););}I&&(c[(t=(_<<2)+T|0)>>2]=c[t>>2]/(0|a))}if(!(c[r+8>>2]<=0)){for(p=c[b>>2],_=0;(0|(a=c[(t=_<<2)+T>>2]))>(0|(l=c[r+16>>2]))?c[t+p>>2]=l:(t=t+p|0,l=c[r+12>>2],c[t>>2]=(0|l)>(0|a)?l:a),(0|(_=_+1|0))<(0|(a=c[r+8>>2])););if(t=0,!((0|a)<=0))for(_=(a=s<<2)+i|0,l=e+a|0;;){a=(s=t<<2)+_|0,s=c[s+l>>2]+c[p+s>>2]|0,c[a>>2]=s;t:{if((0|s)>c[r+16>>2])s=s-c[r+20>>2]|0;else{if((0|s)>=c[r+12>>2])break t;s=s+c[r+20>>2]|0}c[a>>2]=s}if(!((0|(t=t+1|0))<c[r+8>>2]))break}}}if(G=(0|W)<=(0|(m=m+1|0)),(0|m)==(0|j))break}return(r=c[n>>2])&&er(r),er(u),(r=c[n+52>>2])&&(c[n+56>>2]=r,er(r)),(r=c[n+40>>2])&&(c[n+44>>2]=r,er(r)),(r=c[n+28>>2])&&(c[n+32>>2]=r,er(r)),(r=c[n+16>>2])&&(c[n+20>>2]=r,er(r)),Z=n- -64|0,0|G}mt(),o()},function(r){var e=0;return c[(r|=0)>>2]=3264,(e=c[r+76>>2])&&er(e),c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),0|r},function(r){var e=0;c[(r|=0)>>2]=3264,(e=c[r+76>>2])&&er(e),c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),er(r)},_t,Ti,ct,at,Wi,sr,function(r,e,i,t,f,a){r|=0,e|=0,i|=0,t|=0,a|=0;var A=0,b=0,u=0,_=0,s=0,p=0,l=0,y=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,F=0,I=0,Y=0,w=0,j=0,J=0,B=0,M=0,Q=0,O=0,C=0,z=0,X=0,S=0,H=0,P=0,x=0,L=0,K=0,q=0,$=0,rr=0,er=0;r:{if(2==(0|(f|=0))&&(c[r+8>>2]=2,c[r- -64>>2]=a,f=c[(Q=r+32|0)>>2],(t=c[r+36>>2]-f|0)>>>0<=7?_e(Q,2-(t>>>2|0)|0):8!=(0|t)&&(c[r+36>>2]=f+8),m=1,t=c[r+56>>2],!((0|(t=c[t+4>>2]-c[t>>2]|0))<=0)))for(d=r+60|0,q=(t=t>>>2|0)>>>0<=1?1:t,$=r+68|0,t=0;;){if(f=c[r+56>>2],b=c[f>>2],c[f+4>>2]-b>>2>>>0<=t>>>0)break r;Z=s=Z-80|0,a=-1,f=-1,-1!=(0|(b=c[b+(t<<2)>>2]))&&(a=((f=b+1|0)>>>0)%3|0?f:b-2|0,f=b-1|0,(b>>>0)%3|0||(f=b+2|0)),A=c[d+36>>2],b=c[A>>2];e:{i:{t:{f:{if(A=c[A+4>>2]-b>>2,u=a<<2,a=c[c[d+32>>2]+28>>2],!(A>>>0<=(_=c[u+a>>2])>>>0||(f=c[a+(f<<2)>>2])>>>0>=A>>>0)){a:{if(!((0|(p=c[b+(f<<2)>>2]))>=(0|t)|(0|(a=c[b+(_<<2)>>2]))>=(0|t))){if(V=c[4+(b=(p<<3)+i|0)>>2],f=c[4+(A=(a<<3)+i|0)>>2],!((0|(_=c[b>>2]))!=(0|(b=c[A>>2]))|(0|f)!=(0|V))){c[d+8>>2]=b,c[d+12>>2]=f;break a}if(m=c[c[d+4>>2]+(t<<2)>>2],c[s+72>>2]=0,c[s+76>>2]=0,c[(A=s- -64|0)>>2]=0,c[A+4>>2]=0,c[s+56>>2]=0,c[s+60>>2]=0,A=c[d>>2],k[A+84|0]||(m=c[c[A+68>>2]+(m<<2)>>2]),g(A,m,n[A+24|0],s+56|0),m=c[c[d+4>>2]+(a<<2)>>2],c[s+48>>2]=0,c[s+52>>2]=0,c[s+40>>2]=0,c[s+44>>2]=0,c[s+32>>2]=0,c[s+36>>2]=0,A=c[d>>2],k[A+84|0]||(m=c[c[A+68>>2]+(m<<2)>>2]),g(A,m,n[A+24|0],s+32|0),m=c[c[d+4>>2]+(p<<2)>>2],c[s+24>>2]=0,c[s+28>>2]=0,c[s+16>>2]=0,c[s+20>>2]=0,c[s+8>>2]=0,c[s+12>>2]=0,A=c[d>>2],k[A+84|0]||(m=c[c[A+68>>2]+(m<<2)>>2]),g(A,m,n[A+24|0],s+8|0),U=(A=c[s+16>>2])-(y=c[s+40>>2])|0,O=c[s+44>>2],w=A=c[s+20>>2]-(O+(A>>>0<y>>>0)|0)|0,p=$e(U,A,U,A),v=E,G=(A=c[s+8>>2])-(D=c[s+32>>2])|0,C=c[s+36>>2],j=A=c[s+12>>2]-(C+(A>>>0<D>>>0)|0)|0,A=(u=p)+(p=$e(G,A,G,A))|0,u=E+v|0,u=A>>>0<p>>>0?u+1|0:u,I=(p=c[s+24>>2])-(F=c[s+48>>2])|0,z=c[s+52>>2],J=p=c[s+28>>2]-(z+(p>>>0<F>>>0)|0)|0,l=A,A=$e(I,p,I,p),u=E+u|0,(R=A>>>0>(h=l+A|0)>>>0?u+1|0:u)|h){if(m=0,Y=Wr(-1,2147483647,h,R),S=a=b>>31,X=b,b=(v=b^(A=u=a>>31))-A|0,A=a=(a^A)-((A>>>0>v>>>0)+A|0)|0,H=a=f>>31,l=(v=(B=f)^(f=a>>31))-f|0,(0|(p=E))==(0|(f=(a=(0|A)==(0|(f=((u=a>>31)^a)-((f>>>0>v>>>0)+u|0)|0))&b>>>0>l>>>0|f>>>0<A>>>0)?A:f))&(b=a?b:l)>>>0>Y>>>0|f>>>0>p>>>0)break e;if(f=$e((b=c[s+64>>2])-y|0,(P=c[s+68>>2])-((b>>>0<y>>>0)+O|0)|0,U,w),a=E,p=$e((A=c[s+56>>2])-D|0,(x=c[s+60>>2])-((A>>>0<D>>>0)+C|0)|0,G,j),u=E+a|0,u=(f=p+f|0)>>>0<p>>>0?u+1|0:u,p=(a=f)+(f=$e((l=c[s+72>>2])-F|0,(L=c[s+76>>2])-((l>>>0<F>>>0)+z|0)|0,I,J))|0,a=E+u|0,v=f>>>0>p>>>0?a+1|0:a,Y=(f=_)-X|0,K=f=(f>>31)-((f>>>0<X>>>0)+S|0)|0,a=(W=(_=f>>31)^Y)-_|0,u=f=((u=f>>31)^f)-((_>>>0>W>>>0)+u|0)|0,W=V-B|0,V=f=(V>>31)-((V>>>0<B>>>0)+H|0)|0,_=a,(a=Wr(-1,2147483647,(a=(0|u)==(0|(f=((a=f>>31)^f)-(((N=f>>31)>>>0>(T=N^W)>>>0)+a|0)|0))&_>>>0>(M=T-N|0)>>>0|f>>>0<u>>>0)?_:M,a?u:f)>>>0<p>>>0)&(0|(f=E))<=(0|v)|(0|f)<(0|v))break e;if(a=f=j>>31,f=(_=f^G)-f|0,M=f=(u=(0|(a=(a^j)-((a>>>0>_>>>0)+a|0)|0))==(0|(_=((u=w>>31)^w)-((u>>>0>(N=u^U)>>>0)+u|0)|0))&f>>>0>(T=N-u|0)>>>0|a>>>0>_>>>0)?f:T,(a=Wr(-1,2147483647,(f=(0|(a=u?a:_))==(0|(_=((u=J>>31)^J)-((u>>>0>(N=u^I)>>>0)+u|0)|0))&f>>>0>(T=N-u|0)>>>0|a>>>0>_>>>0)?M:T,f?a:_)>>>0<p>>>0)&(0|(f=E))<=(0|v)|(0|f)<(0|v))break e;if(_=1,f=0,a=y,y=Ke($e(p,v,U,w),E,h,R),u=E+O|0,u=(a=a+y|0)>>>0<y>>>0?u+1|0:u,y=$e(y=b-a|0,a=P-((a>>>0>b>>>0)+u|0)|0,y,a),U=E,a=A,u=Ke($e(p,v,G,j),E,h,R),A=E+C|0,A=(b=u+D|0)>>>0<u>>>0?A+1|0:A,A=$e(u=a-b|0,a=x-((a>>>0<b>>>0)+A|0)|0,u,a),a=E+U|0,a=(b=A+y|0)>>>0<A>>>0?a+1|0:a,y=b,A=Ke($e(p,v,I,J),E,h,R),u=E+z|0,u=(b=A+F|0)>>>0<A>>>0?u+1|0:u,l=$e(A=l-b|0,b=L-((b>>>0>l>>>0)+u|0)|0,A,b),A=E+a|0,a=$e(b=l+y|0,b>>>0<l>>>0?A+1|0:A,h,R),l=b=E,!b&a>>>0<=1)break f;for(u=a;A=f<<1|_>>>31,_<<=1,f=A,y=!b&u>>>0>7|0!=(0|b),u=(3&b)<<30|u>>>2,b=b>>>2|0,y;);break t}}if((0|t)>(0|a))f=a<<1;else{if((0|t)<=0){c[d+8>>2]=0,c[d+12>>2]=0;break a}f=(t<<1)-2|0}f=(f<<2)+i|0,c[d+8>>2]=c[f>>2],c[d+12>>2]=c[f+4>>2]}m=1;break e}dt(),o()}if(f=l,_=a,a-1|0)break i}for(;b=Wr(a,l,_,f),u=f+E|0,b=$e(_=(1&(u=(f=b+_|0)>>>0<_>>>0?u+1|0:u))<<31|f>>>1,f=u>>>1|0,_,f),(0|l)==(0|(A=E))&a>>>0<b>>>0|A>>>0>l>>>0;);}(a=c[d+20>>2])&&(A=a-1|0,u=c[c[d+16>>2]+(A>>>3&536870908)>>2],c[d+20>>2]=A,m=1,a=$e(p,v,W,V),b=E,l=(y=$e(h,R,B,H))+a|0,a=E+b|0,a=l>>>0<y>>>0?a+1|0:a,b=$e(_,f,Y,K),u=(A=u>>>A&1)?0-b|0:b,y=a,a=E,b=y+(A?0-(a+(0!=(0|b))|0)|0:a)|0,rr=d,er=Ke(l=u+l|0,u>>>0>l>>>0?b+1|0:b,h,R),c[rr+12>>2]=er,a=$e(p,v,Y,K),b=E,a=(p=$e(h,R,X,S))+a|0,u=E+b|0,b=0-(f=$e(_,f,W,V))|0,_=E,u=(a>>>0<p>>>0?u+1|0:u)+(A?_:0-((0!=(0|f))+_|0)|0)|0,rr=d,er=Ke(a=(b=A?f:b)+a|0,a>>>0<b>>>0?u+1|0:u,h,R),c[rr+8>>2]=er)}if(Z=s+80|0,!m)return 0;if(!(c[r+8>>2]<=0)){for(A=c[Q>>2],f=0;(0|(b=c[(a=f<<2)+$>>2]))>(0|(_=c[r+16>>2]))?c[a+A>>2]=_:(a=a+A|0,_=c[r+12>>2],c[a>>2]=(0|_)>(0|b)?_:b),(0|(f=f+1|0))<(0|(b=c[r+8>>2])););if(a=0,!((0|b)<=0))for(_=(f=t<<3)+i|0,p=e+f|0;;){f=(b=a<<2)+_|0,b=c[b+p>>2]+c[b+A>>2]|0,c[f>>2]=b;e:{if((0|b)>c[r+16>>2])u=b-c[r+20>>2]|0;else{if((0|b)>=c[r+12>>2])break e;u=b+c[r+20>>2]|0}c[f>>2]=u}if(!((0|(a=a+1|0))<c[r+8>>2]))break}}if((0|q)==(0|(t=t+1|0)))break}return 0|m}dt(),o()},function(r){var e;return c[(r|=0)>>2]=3500,c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),0|r},function(r){var e;c[(r|=0)>>2]=3500,c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),er(r)},lt,ui,ct,at,Di,Cr,function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A=0,b=0,u=0,k=0,_=0,s=0,p=0,l=0;Z=n=Z-32|0,c[68+(r|=0)>>2]=a,t=c[r+56>>2],f=c[t>>2],t=c[t+4>>2],c[n+24>>2]=0,c[n+16>>2]=0,c[n+20>>2]=0;r:{if((0|(t=t-f|0))>0)for(s=r+60|0,p=(t=t>>>2|0)>>>0<=1?1:t,l=r+112|0;;){if(f=c[r+56>>2],t=c[f>>2],c[f+4>>2]-t>>2>>>0<=u>>>0)break r;if(x(s,c[t+(u<<2)>>2],n+16|0),a=((t=(b=c[n+20>>2])>>31)^b)-t+(((f=(A=c[n+16>>2])>>31)^A)-f)|0,f=((t=(k=c[n+24>>2])>>31)^k)-t|0,t=0,(t=(_=f)>>>0>(f=f+a|0)>>>0?1:t)|f?(A=Ke($e(a=c[r+108>>2],_=a>>31,A,A>>31),E,f,t),c[n+16>>2]=A,t=Ke($e(a,_,b,b>>31),E,f,t),c[n+20>>2]=t,t=(f=((f=t)^(t>>=31))-t|0)+(((t=A>>31)^A)-t|0)|0,c[n+24>>2]=(0|k)>=0?a-t:t-a):c[n+16>>2]=c[r+108>>2],t=Se(l),a=c[n+16>>2],t?(c[n+24>>2]=0-c[n+24>>2],f=0-c[n+20>>2]|0,c[n+20>>2]=f,a=0-a|0,c[n+16>>2]=a):f=c[n+20>>2],(0|a)>=0?(t=(a=c[r+108>>2])+c[n+24>>2]|0,a=f+a|0):((0|f)<0?a=((t=c[n+24>>2])^(a=t>>31))-a|0:(a=(t=c[n+24>>2])>>31,a=c[r+100>>2]+(a-(t^a)|0)|0),(0|t)<0?t=((t=f>>31)^f)-t|0:(t=f>>31,t=c[r+100>>2]+(t-(t^f)|0)|0)),f=c[r+100>>2],t|a?(0|t)!=(0|f)|a?(0|f)!=(0|a)|t?a||(0|(b=c[r+108>>2]))>=(0|t)?(0|f)!=(0|a)||(0|(b=c[r+108>>2]))<=(0|t)?(0|t)!=(0|f)||(0|(f=c[r+108>>2]))<=(0|a)?t||(t=0,(0|(f=c[r+108>>2]))>=(0|a)||(a=(f<<1)-a|0)):a=(f<<1)-a|0:t=(b<<1)-t|0:(t=(b<<1)-t|0,a=0):t=a:a=t:a=t=f,c[n+12>>2]=t,c[n+8>>2]=a,!(c[r+8>>2]<=0)){for(b=c[r+32>>2],a=0;(0|(f=c[(t=a<<2)+(n+8|0)>>2]))>(0|(A=c[r+16>>2]))?c[t+b>>2]=A:(t=t+b|0,A=c[r+12>>2],c[t>>2]=(0|A)>(0|f)?A:f),(0|(a=a+1|0))<(0|(f=c[r+8>>2])););if(t=0,!((0|f)<=0))for(A=(f=u<<3)+i|0,k=e+f|0;;){f=(a=t<<2)+A|0,a=c[a+k>>2]+c[a+b>>2]|0,c[f>>2]=a;e:{if((0|a)>c[r+16>>2])a=a-c[r+20>>2]|0;else{if((0|a)>=c[r+12>>2])break e;a=a+c[r+20>>2]|0}c[f>>2]=a}if(!((0|(t=t+1|0))<c[r+8>>2]))break}}if((0|p)==(0|(u=u+1|0)))break}return Z=n+32|0,1}dt(),o()},ut,vt,Ji,$i,x,ht,Ei,ct,Ui,function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A,b,u,k,_=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0;if(c[8+(r|=0)>>2]=f,s=c[(A=r+32|0)>>2],(a=c[r+36>>2]-s>>2)>>>0<f>>>0?(_e(A,f-a|0),t=c[r+8>>2]):(t=f)>>>0>=a>>>0||(c[r+36>>2]=s+(f<<2),t=f),k=c[r+52>>2],b=c[r+48>>2],a=0,n=Sr(vi(s=f>>>0>1073741823?-1:f<<2),0,s),!((0|t)<=0)){for(_=c[r+32>>2];(0|(s=c[(t=a<<2)+n>>2]))>(0|(l=c[r+16>>2]))?c[t+_>>2]=l:(t=t+_|0,l=c[r+12>>2],c[t>>2]=(0|l)>(0|s)?l:s),(0|(t=c[r+8>>2]))>(0|(a=a+1|0)););if(!((0|t)<=0))for(a=0;;){t=(s=a<<2)+i|0,s=c[e+s>>2]+c[_+s>>2]|0,c[t>>2]=s;r:{if((0|s)>c[r+16>>2])p=s-c[r+20>>2]|0;else{if((0|s)>=c[r+12>>2])break r;p=s+c[r+20>>2]|0}c[t>>2]=p}if(!((0|(t=c[r+8>>2]))>(0|(a=a+1|0))))break}}if(a=c[r+56>>2],u=c[a>>2],(0|(a=c[a+4>>2]-u|0))>=5)for(R=(v=a>>>2|0)>>>0<=2?2:v,N=-2&f,T=1&f,s=1;;){r:{e:{if((0|s)!=(0|v)){if(h=y(f,s),-1==(0|(a=c[(s<<2)+u>>2])))break e;if(-1==(0|(a=c[c[b+12>>2]+(a<<2)>>2])))break e;l=c[k>>2],_=c[b>>2],d=c[l+(c[_+(a<<2)>>2]<<2)>>2],p=-1!=(0|(p=((p=a+1|0)>>>0)%3|0?p:a-2|0))?c[_+(p<<2)>>2]:-1;i:{if((a>>>0)%3|0)a=a-1|0;else if(m=-1,-1==(0|(a=a+2|0)))break i;m=c[_+(a<<2)>>2]}if((0|s)<=(0|d))break e;if((0|(a=c[(p<<2)+l>>2]))>=(0|s))break e;if((0|(_=c[l+(m<<2)>>2]))>=(0|s))break e;if(!((0|f)<=0)){if(_=y(f,_),l=y(f,a),d=y(f,d),a=0,m=0,1!=(0|f))for(;c[(a<<2)+n>>2]=(c[(a+_<<2)+i>>2]+c[(a+l<<2)+i>>2]|0)-c[(a+d<<2)+i>>2],c[((p=1|a)<<2)+n>>2]=(c[(_+p<<2)+i>>2]+c[(l+p<<2)+i>>2]|0)-c[(p+d<<2)+i>>2],a=a+2|0,(0|N)!=(0|(m=m+2|0)););T&&(c[(a<<2)+n>>2]=(c[(a+_<<2)+i>>2]+c[(a+l<<2)+i>>2]|0)-c[(a+d<<2)+i>>2])}if((0|t)<=0)break r;for(l=c[A>>2],a=0;(0|(_=c[(t=a<<2)+n>>2]))>(0|(d=c[r+16>>2]))?c[t+l>>2]=d:(t=t+l|0,d=c[r+12>>2],c[t>>2]=(0|d)>(0|_)?d:_),(0|(t=c[r+8>>2]))>(0|(a=a+1|0)););if(a=0,(0|t)<=0)break r;for(d=(t=h<<2)+i|0,p=e+t|0;;){t=(_=a<<2)+d|0,_=c[_+p>>2]+c[_+l>>2]|0,c[t>>2]=_;i:{if((0|_)>c[r+16>>2])m=_-c[r+20>>2]|0;else{if((0|_)>=c[r+12>>2])break i;m=_+c[r+20>>2]|0}c[t>>2]=m}if(!((0|(t=c[r+8>>2]))>(0|(a=a+1|0))))break}break r}dt(),o()}if(!((0|t)<=0)){for(d=(y(s-1|0,f)<<2)+i|0,l=c[A>>2],a=0;(0|(_=c[(t=a<<2)+d>>2]))>(0|(p=c[r+16>>2]))?c[t+l>>2]=p:(t=t+l|0,p=c[r+12>>2],c[t>>2]=(0|p)>(0|_)?p:_),(0|(t=c[r+8>>2]))>(0|(a=a+1|0)););if(a=0,!((0|t)<=0))for(d=(t=h<<2)+i|0,p=e+t|0;;){t=(_=a<<2)+d|0,_=c[_+p>>2]+c[_+l>>2]|0,c[t>>2]=_;e:{if((0|_)>c[r+16>>2])m=_-c[r+20>>2]|0;else{if((0|_)>=c[r+12>>2])break e;m=_+c[r+20>>2]|0}c[t>>2]=m}if(!((0|(t=c[r+8>>2]))>(0|(a=a+1|0))))break}}}if((0|R)==(0|(s=s+1|0)))break}return er(n),1},ht,function(r){var e=0;return c[(r|=0)>>2]=4580,(e=c[r+96>>2])&&er(e),(e=c[r+84>>2])&&er(e),(e=c[r+72>>2])&&er(e),(e=c[r+60>>2])&&er(e),c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),0|r},function(r){var e=0;c[(r|=0)>>2]=4580,(e=c[r+96>>2])&&er(e),(e=c[r+84>>2])&&er(e),(e=c[r+72>>2])&&er(e),(e=c[r+60>>2])&&er(e),c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),er(r)},kt,Ui,function(r,e){r|=0;var i,t=0,f=0,a=0,A=0,o=0,b=0,u=0,_=0,s=0,p=0;Z=i=Z-32|0;r:{e:if(Fe(1,i+28|0,e|=0)&&(f=c[i+28>>2],t=c[r+48>>2],!(f>>>0>c[t+4>>2]-c[t>>2]>>2>>>0)))i:{if(f){if(Zr(r+60|0,f),c[(t=i+8|0)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,!Er(t,e))break i;for(;A=1<<a,u=Se(t),o=c[r+60>>2]+(a>>>3&536870908)|0,b=u?A|c[o>>2]:c[o>>2]&(-1^A),c[o>>2]=b,(0|f)!=(0|(a=a+1|0)););}if(!Fe(1,i+28|0,e))break e;if(f=c[i+28>>2],t=c[r+48>>2],f>>>0>c[t+4>>2]-c[t>>2]>>2>>>0)break e;if(f){if(a=0,Zr(r+72|0,f),c[(t=i+8|0)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,!Er(t,e))break i;for(;A=1<<a,u=Se(t),o=c[r+72>>2]+(a>>>3&536870908)|0,b=u?A|c[o>>2]:c[o>>2]&(-1^A),c[o>>2]=b,(0|f)!=(0|(a=a+1|0)););}if(!Fe(1,i+28|0,e))break e;if(f=c[i+28>>2],t=c[r+48>>2],f>>>0>c[t+4>>2]-c[t>>2]>>2>>>0)break e;if(f){if(a=0,Zr(r+84|0,f),c[(t=i+8|0)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,!Er(t,e))break i;for(;A=1<<a,u=Se(t),o=c[r+84>>2]+(a>>>3&536870908)|0,b=u?A|c[o>>2]:c[o>>2]&(-1^A),c[o>>2]=b,(0|f)!=(0|(a=a+1|0)););}if(!Fe(1,i+28|0,e))break e;if(f=c[i+28>>2],t=c[r+48>>2],f>>>0>c[t+4>>2]-c[t>>2]>>2>>>0)break e;if(f){if(a=0,Zr(r+96|0,f),c[(t=i+8|0)>>2]=0,c[t+4>>2]=0,n[t+5|0]=0,n[t+6|0]=0,n[t+7|0]=0,n[t+8|0]=0,n[t+9|0]=0,n[t+10|0]=0,n[t+11|0]=0,n[t+12|0]=0,!Er(t,e))break i;for(;A=1<<a,u=Se(t),o=c[r+96>>2]+(a>>>3&536870908)|0,b=u?A|c[o>>2]:c[o>>2]&(-1^A),c[o>>2]=b,(0|f)!=(0|(a=a+1|0)););}if(a=0,t=c[e+8>>2],A=c[e+12>>2],f=t,b=t=c[e+20>>2],f>>>0<(u=(o=c[e+16>>2])+4|0)>>>0&(0|(t=u>>>0<4?t+1|0:t))>=(0|A)|(0|t)>(0|A))break r;if(p=c[e>>2],s=k[0|(_=p+o|0)]|k[_+1|0]<<8|k[_+2|0]<<16|k[_+3|0]<<24,c[e+16>>2]=u,c[e+20>>2]=t,_=f,f=A,t=b,(A=o+8|0)>>>0>_>>>0&(0|(t=A>>>0<8?t+1|0:t))>=(0|f)|(0|t)>(0|f))break r;if(f=k[0|(f=u+p|0)]|k[f+1|0]<<8|k[f+2|0]<<16|k[f+3|0]<<24,c[e+16>>2]=A,c[e+20>>2]=t,(0|f)<(0|s))break r;if(c[r+16>>2]=f,c[r+12>>2]=s,!(t=(f>>31)-((s>>31)+(f>>>0<s>>>0)|0)|0)&(e=f-s|0)>>>0>2147483646|t)break r;if(a=1,e=e+1|0,c[r+20>>2]=e,t=e>>>1|0,c[r+24>>2]=t,c[r+28>>2]=0-t,1&e)break r;c[r+24>>2]=t-1;break r}a=0}return Z=i+32|0,0|a},function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A,b,u,k,_=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,E=0,F=0,I=0,Y=0,w=0,j=0,J=0,B=0,M=0,Q=0;Z=n=Z+-64|0,c[8+(r|=0)>>2]=f,a=c[(A=r+32|0)>>2];r:{if((t=c[r+36>>2]-a>>2)>>>0<f>>>0)_e(A,f-t|0),c[n+56>>2]=0,c[n+60>>2]=0,c[n+48>>2]=0,c[n+52>>2]=0,c[n+40>>2]=0,c[n+44>>2]=0,c[n+32>>2]=0,c[n+36>>2]=0,c[n+24>>2]=0,c[n+28>>2]=0,c[n+16>>2]=0,c[n+20>>2]=0,c[n>>2]=0;else if(t>>>0>f>>>0&&(c[r+36>>2]=a+(f<<2)),c[n+56>>2]=0,c[n+60>>2]=0,c[n+48>>2]=0,c[n+52>>2]=0,c[n+40>>2]=0,c[n+44>>2]=0,c[n+32>>2]=0,c[n+36>>2]=0,c[n+24>>2]=0,c[n+28>>2]=0,c[n+16>>2]=0,c[n+20>>2]=0,c[n>>2]=0,t=0,!f)break r;Nr(n+16|0,f,n),s=c[n+28>>2],t=c[n+32>>2]}c[n>>2]=0;r:if((t=t-s>>2)>>>0>=f>>>0){if(t>>>0<=f>>>0)break r;c[n+32>>2]=(f<<2)+s}else Nr(n+16|12,f-t|0,n);c[n>>2]=0,a=c[n+40>>2];r:if((t=c[n+44>>2]-a>>2)>>>0>=f>>>0){if(t>>>0<=f>>>0)break r;c[n+44>>2]=a+(f<<2)}else Nr(n+40|0,f-t|0,n);c[n>>2]=0,a=c[n+52>>2];r:if((t=c[n+56>>2]-a>>2)>>>0>=f>>>0){if(t>>>0<=f>>>0)break r;c[n+56>>2]=a+(f<<2)}else Nr(n+52|0,f-t|0,n);if(!(c[r+8>>2]<=0)){for(_=c[n+16>>2],p=c[r+32>>2],s=0;(0|(a=c[(t=s<<2)+_>>2]))>(0|(m=c[r+16>>2]))?c[t+p>>2]=m:(t=t+p|0,m=c[r+12>>2],c[t>>2]=(0|m)>(0|a)?m:a),(0|(s=s+1|0))<(0|(t=c[r+8>>2])););if(!((0|t)<=0))for(t=0;;){a=(_=t<<2)+i|0,_=c[e+_>>2]+c[_+p>>2]|0,c[a>>2]=_;r:{if((0|_)>c[r+16>>2])_=_-c[r+20>>2]|0;else{if((0|_)>=c[r+12>>2])break r;_=_+c[r+20>>2]|0}c[a>>2]=_}if(!((0|(t=t+1|0))<c[r+8>>2]))break}}k=c[r+52>>2],u=c[r+48>>2],b=vi(16),c[(t=b)>>2]=0,c[t+4>>2]=0,c[t+8>>2]=0,c[t+12>>2]=0,c[n+8>>2]=0,c[n>>2]=0,c[n+4>>2]=0;r:{if(f){if(f>>>0>=1073741824)break r;U=vi(t=f<<2),c[n>>2]=U,c[n+8>>2]=t+U,Sr(U,0,t)}G=1,t=c[r+56>>2],E=c[t>>2];e:if(!((0|(t=c[t+4>>2]-E|0))<8))for(j=(0|(D=t>>2))<=2?2:D,J=D>>>0<=1?1:D,F=-2&f,I=1&f,B=-4&f,Y=3&f,w=f-1|0,M=f<<2,Q=f>>>0<4,G=0,m=1;;){i:{t:{f:{a:{if((0|m)!=(0|J)){n:{A:if(-1!=(0|(a=c[(m<<2)+E>>2]))){t=a+2|0,N=(v=c[u+12>>2])+(((_=(a>>>0)%3|0)?a-1|0:t)<<2)|0,p=0,W=0!=(0|_)|-1!=(0|t),l=1,t=a;o:{for(;;){if(-1!=(0|(_=c[v+(t<<2)>>2]))){d=-1,R=c[k>>2],T=c[u>>2],s=R+(c[T+(_<<2)>>2]<<2)|0,-1!=(0|(h=((h=_+1|0)>>>0)%3|0?h:_-2|0))&&(d=c[T+(h<<2)>>2]),h=c[s>>2];b:{if((_>>>0)%3|0)s=_-1|0;else if(V=-1,-1==(0|(s=_+2|0)))break b;V=c[T+(s<<2)>>2]}if(!((0|m)<=(0|h)||(0|(s=c[R+(d<<2)>>2]))>=(0|m)||(0|(d=c[R+(V<<2)>>2]))>=(0|m))){if(_=c[(n+16|0)+y(p,12)>>2],f){if(d=y(f,d),T=y(f,s),R=y(f,h),s=0,V=0,w)for(;c[_+(s<<2)>>2]=(c[(s+d<<2)+i>>2]+c[(s+T<<2)+i>>2]|0)-c[(s+R<<2)+i>>2],c[_+((h=1|s)<<2)>>2]=(c[(d+h<<2)+i>>2]+c[(h+T<<2)+i>>2]|0)-c[(h+R<<2)+i>>2],s=s+2|0,(0|F)!=(0|(V=V+2|0)););I&&(c[_+(s<<2)>>2]=(c[(s+d<<2)+i>>2]+c[(s+T<<2)+i>>2]|0)-c[(s+R<<2)+i>>2])}if(_=4,4==(0|(p=p+1|0)))break o}}b:if(1&l){if(_=-1,-1==(0|(t=((s=t+1|0)>>>0)%3|0?s:t-2|0)))break b;if(_=-1,-1==(0|(t=c[v+(t<<2)>>2])))break b;_=((_=t+1|0)>>>0)%3|0?_:t-2|0}else{if((t>>>0)%3|0)s=t-1|0;else if(_=-1,-1==(0|(s=t+2|0)))break b;_=-1,-1!=(0|(t=c[v+(s<<2)>>2]))&&(_=t-1|0,(t>>>0)%3|0||(_=t+2|0))}b:if((0|a)!=(0|(t=_))){if(-1==(0|t)&l){if(!W)break b;if(-1==(0|(t=c[N>>2])))break b;l=0,t=(t>>>0)%3|0?t-1|0:t+2|0}if(-1!=(0|t))continue}break}if((0|(_=p))<=0)break A}for(f&&Sr(U,0,M),T=((t=_-1|0)<<2)+b|0,h=t=y(t,12)+r|0,V=c[t- -64>>2],l=0,t=c[n>>2],a=0;;){if(p=c[T>>2],c[T>>2]=p+1,p>>>0>=V>>>0)break e;if(!(c[c[h+60>>2]+(p>>>3&536870908)>>2]>>>p&1)&&(a=a+1|0,f)){if(p=c[(n+16|0)+y(l,12)>>2],d=0,s=0,R=0,!Q)for(;c[(N=(v=s<<2)+t|0)>>2]=c[p+v>>2]+c[N>>2],c[(W=(N=4|v)+t|0)>>2]=c[p+N>>2]+c[W>>2],c[(W=(N=8|v)+t|0)>>2]=c[p+N>>2]+c[W>>2],c[(N=(v|=12)+t|0)>>2]=c[p+v>>2]+c[N>>2],s=s+4|0,(0|B)!=(0|(R=R+4|0)););if(Y)for(;c[(R=(v=s<<2)+t|0)>>2]=c[p+v>>2]+c[R>>2],s=s+1|0,(0|Y)!=(0|(d=d+1|0)););}if((0|(l=l+1|0))==(0|_))break}if(_=y(f,m),!a)break n;if(!f)break t;if(s=0,t=0,w)break a;break f}_=y(f,m)}if(c[r+8>>2]<=0)break i;for(l=(y(m-1|0,f)<<2)+i|0,p=c[A>>2],s=0;(0|(a=c[(t=s<<2)+l>>2]))>(0|(d=c[r+16>>2]))?c[t+p>>2]=d:(t=t+p|0,d=c[r+12>>2],c[t>>2]=(0|d)>(0|a)?d:a),(0|(s=s+1|0))<(0|(a=c[r+8>>2])););if(t=0,(0|a)<=0)break i;for(s=(a=_<<2)+i|0,l=e+a|0;;){a=(_=t<<2)+s|0,_=c[_+l>>2]+c[_+p>>2]|0,c[a>>2]=_;n:{if((0|_)>c[r+16>>2])_=_-c[r+20>>2]|0;else{if((0|_)>=c[r+12>>2])break n;_=_+c[r+20>>2]|0}c[a>>2]=_}if(!((0|(t=t+1|0))<c[r+8>>2]))break}break i}dt(),o()}for(;c[(l=(p=s<<2)+U|0)>>2]=c[l>>2]/(0|a),c[(p=(4|p)+U|0)>>2]=c[p>>2]/(0|a),s=s+2|0,(0|F)!=(0|(t=t+2|0)););}I&&(c[(t=(s<<2)+U|0)>>2]=c[t>>2]/(0|a))}if(!(c[r+8>>2]<=0)){for(p=c[A>>2],s=0;(0|(a=c[(t=s<<2)+U>>2]))>(0|(l=c[r+16>>2]))?c[t+p>>2]=l:(t=t+p|0,l=c[r+12>>2],c[t>>2]=(0|l)>(0|a)?l:a),(0|(s=s+1|0))<(0|(a=c[r+8>>2])););if(t=0,!((0|a)<=0))for(s=(a=_<<2)+i|0,l=e+a|0;;){a=(_=t<<2)+s|0,_=c[_+l>>2]+c[_+p>>2]|0,c[a>>2]=_;t:{if((0|_)>c[r+16>>2])_=_-c[r+20>>2]|0;else{if((0|_)>=c[r+12>>2])break t;_=_+c[r+20>>2]|0}c[a>>2]=_}if(!((0|(t=t+1|0))<c[r+8>>2]))break}}}if(G=(0|D)<=(0|(m=m+1|0)),(0|m)==(0|j))break}return(r=c[n>>2])&&er(r),er(b),(r=c[n+52>>2])&&(c[n+56>>2]=r,er(r)),(r=c[n+40>>2])&&(c[n+44>>2]=r,er(r)),(r=c[n+28>>2])&&(c[n+32>>2]=r,er(r)),(r=c[n+16>>2])&&(c[n+20>>2]=r,er(r)),Z=n- -64|0,0|G}mt(),o()},function(r){var e=0;return c[(r|=0)>>2]=4816,(e=c[r+76>>2])&&er(e),c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),0|r},function(r){var e=0;c[(r|=0)>>2]=4816,(e=c[r+76>>2])&&er(e),c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),er(r)},_t,Ti,ct,at,Wi,sr,function(r,e,i,t,f,a){r|=0,e|=0,i|=0,t|=0,a|=0;var A=0,b=0,u=0,_=0,s=0,p=0,l=0,y=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,F=0,I=0,Y=0,w=0,j=0,J=0,B=0,M=0,Q=0,O=0,C=0,z=0,X=0,S=0,H=0,P=0,x=0,L=0,K=0,q=0,$=0,rr=0,er=0;r:{if(2==(0|(f|=0))&&(c[r+8>>2]=2,c[r- -64>>2]=a,f=c[(Q=r+32|0)>>2],(t=c[r+36>>2]-f|0)>>>0<=7?_e(Q,2-(t>>>2|0)|0):8!=(0|t)&&(c[r+36>>2]=f+8),u=1,t=c[r+56>>2],!((0|(t=c[t+4>>2]-c[t>>2]|0))<=0)))for(d=r+60|0,q=(t=t>>>2|0)>>>0<=1?1:t,$=r+68|0,t=0;;){if(a=c[r+56>>2],f=c[a>>2],c[a+4>>2]-f>>2>>>0<=t>>>0)break r;if(Z=s=Z-80|0,a=-1,-1==(0|(f=c[f+(t<<2)>>2]))||(u=c[d+32>>2],-1!=(0|(A=((A=f+1|0)>>>0)%3|0?A:f-2|0))&&(a=c[c[u>>2]+(A<<2)>>2]),m=-1,-1!=(0|(f=f+((f>>>0)%3|0?-1:2)|0))&&(m=c[c[u>>2]+(f<<2)>>2]),u=c[d+36>>2],f=c[u>>2],(u=c[u+4>>2]-f>>2)>>>0<=a>>>0|u>>>0<=m>>>0))dt(),o();else{e:{i:{t:{f:{a:{if(!((0|(_=c[f+(m<<2)>>2]))>=(0|t)|(0|(a=c[f+(a<<2)>>2]))>=(0|t))){if(V=c[4+(u=(_<<3)+i|0)>>2],f=c[4+(A=(a<<3)+i|0)>>2],!((0|(p=c[u>>2]))!=(0|(u=c[A>>2]))|(0|f)!=(0|V))){c[d+8>>2]=u,c[d+12>>2]=f;break a}if(m=c[c[d+4>>2]+(t<<2)>>2],c[s+72>>2]=0,c[s+76>>2]=0,c[(A=s- -64|0)>>2]=0,c[A+4>>2]=0,c[s+56>>2]=0,c[s+60>>2]=0,A=c[d>>2],k[A+84|0]||(m=c[c[A+68>>2]+(m<<2)>>2]),g(A,m,n[A+24|0],s+56|0),m=c[c[d+4>>2]+(a<<2)>>2],c[s+48>>2]=0,c[s+52>>2]=0,c[s+40>>2]=0,c[s+44>>2]=0,c[s+32>>2]=0,c[s+36>>2]=0,A=c[d>>2],k[A+84|0]||(m=c[c[A+68>>2]+(m<<2)>>2]),g(A,m,n[A+24|0],s+32|0),m=c[c[d+4>>2]+(_<<2)>>2],c[s+24>>2]=0,c[s+28>>2]=0,c[s+16>>2]=0,c[s+20>>2]=0,c[s+8>>2]=0,c[s+12>>2]=0,A=c[d>>2],k[A+84|0]||(m=c[c[A+68>>2]+(m<<2)>>2]),g(A,m,n[A+24|0],s+8|0),U=(A=c[s+16>>2])-(y=c[s+40>>2])|0,O=c[s+44>>2],w=A=c[s+20>>2]-(O+(A>>>0<y>>>0)|0)|0,_=$e(U,A,U,A),v=E,G=(A=c[s+8>>2])-(D=c[s+32>>2])|0,C=c[s+36>>2],j=A=c[s+12>>2]-(C+(A>>>0<D>>>0)|0)|0,A=(b=_)+(_=$e(G,A,G,A))|0,b=E+v|0,b=A>>>0<_>>>0?b+1|0:b,I=(_=c[s+24>>2])-(F=c[s+48>>2])|0,z=c[s+52>>2],J=_=c[s+28>>2]-(z+(_>>>0<F>>>0)|0)|0,l=A,A=$e(I,_,I,_),b=E+b|0,(R=A>>>0>(h=l+A|0)>>>0?b+1|0:b)|h){if(m=0,Y=Wr(-1,2147483647,h,R),S=a=u>>31,X=u,u=(v=u^(A=b=a>>31))-A|0,A=a=(a^A)-((A>>>0>v>>>0)+A|0)|0,H=a=f>>31,l=(v=(B=f)^(f=a>>31))-f|0,(0|(_=E))==(0|(f=(a=(0|A)==(0|(f=((b=a>>31)^a)-((f>>>0>v>>>0)+b|0)|0))&u>>>0>l>>>0|f>>>0<A>>>0)?A:f))&(u=a?u:l)>>>0>Y>>>0|f>>>0>_>>>0)break e;if(f=$e((u=c[s+64>>2])-y|0,(P=c[s+68>>2])-((u>>>0<y>>>0)+O|0)|0,U,w),a=E,_=$e((A=c[s+56>>2])-D|0,(x=c[s+60>>2])-((A>>>0<D>>>0)+C|0)|0,G,j),b=E+a|0,b=(f=_+f|0)>>>0<_>>>0?b+1|0:b,_=(a=f)+(f=$e((l=c[s+72>>2])-F|0,(L=c[s+76>>2])-((l>>>0<F>>>0)+z|0)|0,I,J))|0,a=E+b|0,v=f>>>0>_>>>0?a+1|0:a,Y=(f=p)-X|0,K=f=(f>>31)-((f>>>0<X>>>0)+S|0)|0,a=(W=(p=f>>31)^Y)-p|0,b=f=((b=f>>31)^f)-((p>>>0>W>>>0)+b|0)|0,W=V-B|0,V=f=(V>>31)-((V>>>0<B>>>0)+H|0)|0,p=a,(a=Wr(-1,2147483647,(a=(0|b)==(0|(f=((a=f>>31)^f)-(((N=f>>31)>>>0>(T=N^W)>>>0)+a|0)|0))&p>>>0>(M=T-N|0)>>>0|f>>>0<b>>>0)?p:M,a?b:f)>>>0<_>>>0)&(0|(f=E))<=(0|v)|(0|f)<(0|v))break e;if(a=f=j>>31,f=(p=f^G)-f|0,M=f=(b=(0|(a=(a^j)-((a>>>0>p>>>0)+a|0)|0))==(0|(p=((b=w>>31)^w)-((b>>>0>(N=b^U)>>>0)+b|0)|0))&f>>>0>(T=N-b|0)>>>0|a>>>0>p>>>0)?f:T,(a=Wr(-1,2147483647,(f=(0|(a=b?a:p))==(0|(p=((b=J>>31)^J)-((b>>>0>(N=b^I)>>>0)+b|0)|0))&f>>>0>(T=N-b|0)>>>0|a>>>0>p>>>0)?M:T,f?a:p)>>>0<_>>>0)&(0|(f=E))<=(0|v)|(0|f)<(0|v))break e;if(p=1,f=0,a=y,y=Ke($e(_,v,U,w),E,h,R),b=E+O|0,b=(a=a+y|0)>>>0<y>>>0?b+1|0:b,y=$e(y=u-a|0,a=P-((a>>>0>u>>>0)+b|0)|0,y,a),U=E,a=A,b=Ke($e(_,v,G,j),E,h,R),A=E+C|0,A=b>>>0>(u=b+D|0)>>>0?A+1|0:A,A=$e(b=a-u|0,a=x-((a>>>0<u>>>0)+A|0)|0,b,a),a=E+U|0,a=A>>>0>(u=A+y|0)>>>0?a+1|0:a,y=u,A=Ke($e(_,v,I,J),E,h,R),b=E+z|0,b=A>>>0>(u=A+F|0)>>>0?b+1|0:b,l=$e(A=l-u|0,u=L-((u>>>0>l>>>0)+b|0)|0,A,u),A=E+a|0,a=$e(u=l+y|0,u>>>0<l>>>0?A+1|0:A,h,R),l=u=E,!u&a>>>0<=1)break f;for(b=a;A=f<<1|p>>>31,p<<=1,f=A,y=!u&b>>>0>7|0!=(0|u),b=(3&u)<<30|b>>>2,u=u>>>2|0,y;);break t}}if((0|t)>(0|a))f=a<<1;else{if((0|t)<=0){c[d+8>>2]=0,c[d+12>>2]=0;break a}f=(t<<1)-2|0}f=(f<<2)+i|0,c[d+8>>2]=c[f>>2],c[d+12>>2]=c[f+4>>2]}m=1;break e}if(f=l,p=a,a-1|0)break i}for(;u=Wr(a,l,p,f),b=f+E|0,u=$e(p=(1&(b=(f=u+p|0)>>>0<p>>>0?b+1|0:b))<<31|f>>>1,f=b>>>1|0,p,f),(0|l)==(0|(A=E))&a>>>0<u>>>0|A>>>0>l>>>0;);}(a=c[d+20>>2])&&(A=a-1|0,b=c[c[d+16>>2]+(A>>>3&536870908)>>2],c[d+20>>2]=A,m=1,a=$e(_,v,W,V),u=E,l=(y=$e(h,R,B,H))+a|0,a=E+u|0,a=l>>>0<y>>>0?a+1|0:a,u=$e(p,f,Y,K),b=(A=b>>>A&1)?0-u|0:u,y=a,a=E,u=y+(A?0-(a+(0!=(0|u))|0)|0:a)|0,rr=d,er=Ke(l=b+l|0,b>>>0>l>>>0?u+1|0:u,h,R),c[rr+12>>2]=er,a=$e(_,v,Y,K),u=E,a=(_=$e(h,R,X,S))+a|0,b=E+u|0,u=0-(f=$e(p,f,W,V))|0,p=E,b=(a>>>0<_>>>0?b+1|0:b)+(A?p:0-((0!=(0|f))+p|0)|0)|0,rr=d,er=Ke(a=(u=A?f:u)+a|0,a>>>0<u>>>0?b+1|0:b,h,R),c[rr+8>>2]=er)}Z=s+80|0,f=m}if(u=f,!f)return 0;if(!(c[r+8>>2]<=0)){for(p=c[Q>>2],f=0;(0|(A=c[(a=f<<2)+$>>2]))>(0|(_=c[r+16>>2]))?c[a+p>>2]=_:(a=a+p|0,_=c[r+12>>2],c[a>>2]=(0|_)>(0|A)?_:A),(0|(f=f+1|0))<(0|(A=c[r+8>>2])););if(a=0,!((0|A)<=0))for(_=(f=t<<3)+i|0,v=e+f|0;;){f=(A=a<<2)+_|0,A=c[A+v>>2]+c[A+p>>2]|0,c[f>>2]=A;e:{if((0|A)>c[r+16>>2])A=A-c[r+20>>2]|0;else{if((0|A)>=c[r+12>>2])break e;A=A+c[r+20>>2]|0}c[f>>2]=A}if(!((0|(a=a+1|0))<c[r+8>>2]))break}}if((0|q)==(0|(t=t+1|0)))break}return 0|u}dt(),o()},function(r){var e;return c[(r|=0)>>2]=5040,c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),0|r},function(r){var e;c[(r|=0)>>2]=5040,c[r>>2]=2960,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),er(r)},lt,ui,ct,at,Di,Cr,function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A=0,b=0,u=0,k=0,_=0,s=0,p=0,l=0;Z=n=Z-32|0,c[68+(r|=0)>>2]=a,t=c[r+56>>2],f=c[t>>2],t=c[t+4>>2],c[n+24>>2]=0,c[n+16>>2]=0,c[n+20>>2]=0;r:{if((0|(t=t-f|0))>0)for(s=r+60|0,p=(t=t>>>2|0)>>>0<=1?1:t,l=r+112|0;;){if(f=c[r+56>>2],t=c[f>>2],c[f+4>>2]-t>>2>>>0<=u>>>0)break r;if($(s,c[t+(u<<2)>>2],n+16|0),a=((t=(b=c[n+20>>2])>>31)^b)-t+(((f=(A=c[n+16>>2])>>31)^A)-f)|0,f=((t=(k=c[n+24>>2])>>31)^k)-t|0,t=0,(t=(_=f)>>>0>(f=f+a|0)>>>0?1:t)|f?(A=Ke($e(a=c[r+108>>2],_=a>>31,A,A>>31),E,f,t),c[n+16>>2]=A,t=Ke($e(a,_,b,b>>31),E,f,t),c[n+20>>2]=t,t=(f=((f=t)^(t>>=31))-t|0)+(((t=A>>31)^A)-t|0)|0,c[n+24>>2]=(0|k)>=0?a-t:t-a):c[n+16>>2]=c[r+108>>2],t=Se(l),a=c[n+16>>2],t?(c[n+24>>2]=0-c[n+24>>2],f=0-c[n+20>>2]|0,c[n+20>>2]=f,a=0-a|0,c[n+16>>2]=a):f=c[n+20>>2],(0|a)>=0?(t=(a=c[r+108>>2])+c[n+24>>2]|0,a=f+a|0):((0|f)<0?a=((t=c[n+24>>2])^(a=t>>31))-a|0:(a=(t=c[n+24>>2])>>31,a=c[r+100>>2]+(a-(t^a)|0)|0),(0|t)<0?t=((t=f>>31)^f)-t|0:(t=f>>31,t=c[r+100>>2]+(t-(t^f)|0)|0)),f=c[r+100>>2],t|a?(0|t)!=(0|f)|a?(0|f)!=(0|a)|t?a||(0|(b=c[r+108>>2]))>=(0|t)?(0|f)!=(0|a)||(0|(b=c[r+108>>2]))<=(0|t)?(0|t)!=(0|f)||(0|(f=c[r+108>>2]))<=(0|a)?t||(t=0,(0|(f=c[r+108>>2]))>=(0|a)||(a=(f<<1)-a|0)):a=(f<<1)-a|0:t=(b<<1)-t|0:(t=(b<<1)-t|0,a=0):t=a:a=t:a=t=f,c[n+12>>2]=t,c[n+8>>2]=a,!(c[r+8>>2]<=0)){for(b=c[r+32>>2],a=0;(0|(f=c[(t=a<<2)+(n+8|0)>>2]))>(0|(A=c[r+16>>2]))?c[t+b>>2]=A:(t=t+b|0,A=c[r+12>>2],c[t>>2]=(0|A)>(0|f)?A:f),(0|(a=a+1|0))<(0|(f=c[r+8>>2])););if(t=0,!((0|f)<=0))for(A=(f=u<<3)+i|0,k=e+f|0;;){f=(a=t<<2)+A|0,a=c[a+k>>2]+c[a+b>>2]|0,c[f>>2]=a;e:{if((0|a)>c[r+16>>2])a=a-c[r+20>>2]|0;else{if((0|a)>=c[r+12>>2])break e;a=a+c[r+20>>2]|0}c[f>>2]=a}if(!((0|(t=t+1|0))<c[r+8>>2]))break}}if((0|p)==(0|(u=u+1|0)))break}return Z=n+32|0,1}dt(),o()},ut,vt,Ji,$i,$,ht,Ei,pt,ct,function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A,o=0,b=0,u=0,k=0,_=0,s=0,p=0,l=0,y=0;if(c[8+(r|=0)>>2]=f,b=c[(n=r+32|0)>>2],(o=c[r+36>>2]-b>>2)>>>0<f>>>0?(_e(n,f-o|0),a=c[r+8>>2]):(a=f)>>>0>=o>>>0||(c[r+36>>2]=b+(f<<2),a=f),A=Sr(vi(o=f>>>0>1073741823?-1:f<<2),0,o),!((0|a)<=0)){for(b=c[r+32>>2];(0|(o=c[(a=u<<2)+A>>2]))>(0|(k=c[r+16>>2]))?c[a+b>>2]=k:(a=a+b|0,k=c[r+12>>2],c[a>>2]=(0|k)>(0|o)?k:o),(0|(a=c[r+8>>2]))>(0|(u=u+1|0)););if(!((0|a)<=0))for(u=0;;){a=(o=u<<2)+i|0,o=c[e+o>>2]+c[o+b>>2]|0,c[a>>2]=o;r:{if((0|o)>c[r+16>>2])o=o-c[r+20>>2]|0;else{if((0|o)>=c[r+12>>2])break r;o=o+c[r+20>>2]|0}c[a>>2]=o}if(!((0|(a=c[r+8>>2]))>(0|(u=u+1|0))))break}}if(!((0|t)<=(0|f)|(0|a)<=0))for(l=0-f<<2,o=f;;){if(!((0|a)<=0)){for(y=(p=(s=o<<2)+i|0)+l|0,k=c[n>>2],u=0;(0|(b=c[(a=u<<2)+y>>2]))>(0|(_=c[r+16>>2]))?c[a+k>>2]=_:(a=a+k|0,_=c[r+12>>2],c[a>>2]=(0|_)>(0|b)?_:b),(0|(a=c[r+8>>2]))>(0|(u=u+1|0)););if(u=0,!((0|a)<=0))for(s=e+s|0;;){a=(b=u<<2)+p|0,b=c[b+s>>2]+c[b+k>>2]|0,c[a>>2]=b;r:{if((0|b)>c[r+16>>2])b=b-c[r+20>>2]|0;else{if((0|b)>=c[r+12>>2])break r;b=b+c[r+20>>2]|0}c[a>>2]=b}if(!((0|(a=c[r+8>>2]))>(0|(u=u+1|0))))break}}if(!((0|(o=f+o|0))<(0|t)))break}return er(A),1},ci,si,function(r,e,i){e|=0,i|=0;var t=0;return c[4+(r|=0)>>2]=e,e=c[c[c[e+4>>2]+8>>2]+(i<<2)>>2],c[r+12>>2]=i,c[r+8>>2]=e,r=c[r+8>>2],3==k[r+24|0]&&(t=9==c[r+28>>2]),0|t},function(r,e,i){return e|=0,i|=0,k[c[4+(r|=0)>>2]+36|0]>=2&&(e=0,!Je(r+24|0,ne(r),i))||(e=ze(r+24|0,c[r+16>>2])),0|e},Oi,function(r,e,i){r|=0,e|=0;var t,f=0,a=0,A=0,o=0,b=0;Z=t=Z-32|0;r:if(3==(0|(i|=0))&&(i=c[r+4>>2],a=c[r+12>>2],c[t+24>>2]=-1,c[t+16>>2]=-1,c[t+20>>2]=1065353216,c[t+8>>2]=-1,c[t+12>>2]=-1,-2!=(0|e))){if(b=c[c[c[i+4>>2]+8>>2]+(a<<2)>>2],1==(0|Zt[c[c[i>>2]+8>>2]](i))){o=c[c[c[i+4>>2]+8>>2]+(a<<2)>>2];e:if(!(1!=(0|Zt[c[c[i>>2]+8>>2]](i))|e-1>>>0>5||!(A=0|Zt[c[c[i>>2]+36>>2]](i))|!(r=0|Zt[c[c[i>>2]+44>>2]](i,a)))){if(a=0|Zt[c[c[i>>2]+40>>2]](i,a)){if(6!=(0|e))break e;e=c[i+44>>2],f=vi(112),c[f+4>>2]=o,i=c[t+12>>2],c[f+8>>2]=c[t+8>>2],c[f+12>>2]=i,i=c[t+20>>2],c[f+16>>2]=c[t+16>>2],c[f+20>>2]=i,c[f+24>>2]=c[t+24>>2],c[f+40>>2]=r,i=r+12|0,c[f+36>>2]=i,c[f+32>>2]=a,c[f+28>>2]=e,c[f+68>>2]=r,c[f- -64>>2]=i,c[f+60>>2]=a,c[f+56>>2]=e,c[f+48>>2]=0,c[f+52>>2]=0,c[f>>2]=5928,c[f+88>>2]=1065353216,c[f+92>>2]=-1,c[f+80>>2]=-1,c[f+84>>2]=-1,c[f+72>>2]=1,c[f+76>>2]=-1,c[f+44>>2]=6492,r=f+96|0}else{if(6!=(0|e))break e;e=c[i+44>>2],f=vi(112),c[f+4>>2]=o,i=c[t+12>>2],c[f+8>>2]=c[t+8>>2],c[f+12>>2]=i,i=c[t+20>>2],c[f+16>>2]=c[t+16>>2],c[f+20>>2]=i,c[f+24>>2]=c[t+24>>2],c[f+40>>2]=r,i=r+12|0,c[f+36>>2]=i,c[f+32>>2]=A,c[f+28>>2]=e,c[f+68>>2]=r,c[f- -64>>2]=i,c[f+60>>2]=A,c[f+56>>2]=e,c[f+48>>2]=0,c[f+52>>2]=0,c[f>>2]=6932,c[f+88>>2]=1065353216,c[f+92>>2]=-1,c[f+80>>2]=-1,c[f+84>>2]=-1,c[f+72>>2]=1,c[f+76>>2]=-1,c[f+44>>2]=7352,r=f+96|0}c[r>>2]=0,c[r+4>>2]=0,n[r+5|0]=0,n[r+6|0]=0,n[r+7|0]=0,n[r+8|0]=0,n[r+9|0]=0,n[r+10|0]=0,n[r+11|0]=0,n[r+12|0]=0}if(f)break r}f=vi(28),c[f+4>>2]=b,r=c[t+12>>2],c[f+8>>2]=c[t+8>>2],c[f+12>>2]=r,r=c[t+20>>2],c[f+16>>2]=c[t+16>>2],c[f+20>>2]=r,c[f+24>>2]=c[t+24>>2],c[f>>2]=7764}return Z=t+32|0,0|f},st,function(r,e){return 0|wr(24+(r|=0)|0,ne(r),c[r+8>>2])},function(r){return c[(r|=0)>>2]=5928,0|r},function(r){c[(r|=0)>>2]=5928,er(r)},lt,tt,ki,ct,at,Gi,ct,function(r){return 3},re,function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A=0,b=0,u=0,k=0,_=0,s=0,l=0,y=0,m=0;Z=n=Z-48|0,(t=c[8+(r|=0)>>2])-2>>>0<=28&&(c[r+76>>2]=t,t=-2-(f=-1<<t)|0,c[r+84>>2]=t,c[r+80>>2]=-1^f,c[r+92>>2]=(0|t)/2,p[r+88>>2]=d(2)/d(0|t)),c[r+52>>2]=a,t=c[r+40>>2],f=c[t>>2],t=c[t+4>>2],c[n+16>>2]=0,c[n+8>>2]=0,c[n+12>>2]=0;r:{if((0|(t=t-f|0))>0)for(s=r+8|0,l=r+44|0,y=(t=t>>>2|0)>>>0<=1?1:t,m=r+96|0;;){if(f=c[r+40>>2],t=c[f>>2],c[f+4>>2]-t>>2>>>0<=u>>>0)break r;if(x(l,c[t+(u<<2)>>2],n+8|0),a=((t=(A=c[n+12>>2])>>31)^A)-t+(((f=(b=c[n+8>>2])>>31)^b)-f)|0,f=((t=(_=c[n+16>>2])>>31)^_)-t|0,t=0,(t=(k=f)>>>0>(f=f+a|0)>>>0?1:t)|f?(b=Ke($e(a=c[r+92>>2],k=a>>31,b,b>>31),E,f,t),c[n+8>>2]=b,t=Ke($e(a,k,A,A>>31),E,f,t),c[n+12>>2]=t,t=(f=(t^(f=t>>31))-f|0)+(((t=b>>31)^b)-t|0)|0,c[n+16>>2]=(0|_)>=0?a-t:t-a):c[n+8>>2]=c[r+92>>2],t=Se(m),a=c[n+8>>2],t?(c[n+16>>2]=0-c[n+16>>2],f=0-c[n+12>>2]|0,c[n+12>>2]=f,a=0-a|0,c[n+8>>2]=a):f=c[n+12>>2],(0|a)>=0?(t=(a=c[r+92>>2])+c[n+16>>2]|0,a=f+a|0):((0|f)<0?a=((t=c[n+16>>2])^(a=t>>31))-a|0:(a=(t=c[n+16>>2])>>31,a=c[r+84>>2]+(a-(t^a)|0)|0),(0|t)<0?t=((t=f>>31)^f)-t|0:(t=f>>31,t=c[r+84>>2]+(t-(t^f)|0)|0)),f=c[r+84>>2],t|a?(0|t)!=(0|f)|a?(0|f)!=(0|a)|t?a||(0|(A=c[r+92>>2]))>=(0|t)?(0|f)!=(0|a)||(0|(A=c[r+92>>2]))<=(0|t)?(0|t)!=(0|f)||(0|(f=c[r+92>>2]))<=(0|a)?t||(t=0,(0|(f=c[r+92>>2]))>=(0|a)||(a=(f<<1)-a|0)):a=(f<<1)-a|0:t=(A<<1)-t|0:(t=(A<<1)-t|0,a=0):t=a:a=t:a=t=f,b=c[(A=(f=u<<3)+e|0)>>2],A=c[A+4>>2],c[n+36>>2]=t,c[n+32>>2]=a,c[n+24>>2]=b,c[n+28>>2]=A,_r(n+40|0,s,n+32|0,n+24|0),c[(t=i+f|0)>>2]=c[n+40>>2],c[t+4>>2]=c[n+44>>2],(0|y)==(0|(u=u+1|0)))break}return Z=n+48|0,1}dt(),o()},ut,vt,Ji,$i,x,function(r){return c[(r|=0)>>2]=6932,0|r},function(r){c[(r|=0)>>2]=6932,er(r)},lt,ki,ct,at,Gi,re,function(r,e,i,t,f,a){e|=0,i|=0,t|=0,f|=0,a|=0;var n,A=0,b=0,u=0,k=0,_=0,s=0,l=0,y=0,m=0;Z=n=Z-48|0,(t=c[8+(r|=0)>>2])-2>>>0<=28&&(c[r+76>>2]=t,t=-2-(f=-1<<t)|0,c[r+84>>2]=t,c[r+80>>2]=-1^f,c[r+92>>2]=(0|t)/2,p[r+88>>2]=d(2)/d(0|t)),c[r+52>>2]=a,t=c[r+40>>2],f=c[t>>2],t=c[t+4>>2],c[n+16>>2]=0,c[n+8>>2]=0,c[n+12>>2]=0;r:{if((0|(t=t-f|0))>0)for(s=r+8|0,l=r+44|0,y=(t=t>>>2|0)>>>0<=1?1:t,m=r+96|0;;){if(f=c[r+40>>2],t=c[f>>2],c[f+4>>2]-t>>2>>>0<=u>>>0)break r;if($(l,c[t+(u<<2)>>2],n+8|0),a=((t=(A=c[n+12>>2])>>31)^A)-t+(((f=(b=c[n+8>>2])>>31)^b)-f)|0,f=((t=(_=c[n+16>>2])>>31)^_)-t|0,t=0,(t=(k=f)>>>0>(f=f+a|0)>>>0?1:t)|f?(b=Ke($e(a=c[r+92>>2],k=a>>31,b,b>>31),E,f,t),c[n+8>>2]=b,t=Ke($e(a,k,A,A>>31),E,f,t),c[n+12>>2]=t,t=(f=(t^(f=t>>31))-f|0)+(((t=b>>31)^b)-t|0)|0,c[n+16>>2]=(0|_)>=0?a-t:t-a):c[n+8>>2]=c[r+92>>2],t=Se(m),a=c[n+8>>2],t?(c[n+16>>2]=0-c[n+16>>2],f=0-c[n+12>>2]|0,c[n+12>>2]=f,a=0-a|0,c[n+8>>2]=a):f=c[n+12>>2],(0|a)>=0?(t=(a=c[r+92>>2])+c[n+16>>2]|0,a=f+a|0):((0|f)<0?a=((t=c[n+16>>2])^(a=t>>31))-a|0:(a=(t=c[n+16>>2])>>31,a=c[r+84>>2]+(a-(t^a)|0)|0),(0|t)<0?t=((t=f>>31)^f)-t|0:(t=f>>31,t=c[r+84>>2]+(t-(t^f)|0)|0)),f=c[r+84>>2],t|a?(0|t)!=(0|f)|a?(0|f)!=(0|a)|t?a||(0|(A=c[r+92>>2]))>=(0|t)?(0|f)!=(0|a)||(0|(A=c[r+92>>2]))<=(0|t)?(0|t)!=(0|f)||(0|(f=c[r+92>>2]))<=(0|a)?t||(t=0,(0|(f=c[r+92>>2]))>=(0|a)||(a=(f<<1)-a|0)):a=(f<<1)-a|0:t=(A<<1)-t|0:(t=(A<<1)-t|0,a=0):t=a:a=t:a=t=f,b=c[(A=(f=u<<3)+e|0)>>2],A=c[A+4>>2],c[n+36>>2]=t,c[n+32>>2]=a,c[n+24>>2]=b,c[n+28>>2]=A,_r(n+40|0,s,n+32|0,n+24|0),c[(t=i+f|0)>>2]=c[n+40>>2],c[t+4>>2]=c[n+44>>2],(0|y)==(0|(u=u+1|0)))break}return Z=n+48|0,1}dt(),o()},ut,vt,Ji,$i,$,ut,vt,pt,ct,pt,et,at,function(r,e){r|=0;var i,t,f,a=0,n=0,A=0,o=0,b=0;return a=c[8+(e|=0)>>2],i=n=c[e+12>>2],f=n=c[e+20>>2],a>>>0<(o=(t=c[e+16>>2])+4|0)>>>0&(0|(n=o>>>0<4?n+1|0:n))>=(0|i)|(0|n)>(0|i)||(A=t+c[e>>2]|0,A=k[0|A]|k[A+1|0]<<8|k[A+2|0]<<16|k[A+3|0]<<24,c[e+16>>2]=o,c[e+20>>2]=n,o=a,a=f,(n=t+8|0)>>>0>o>>>0&(0|(a=n>>>0<8?a+1|0:a))>=(0|i)|(0|a)>(0|i)||(c[e+16>>2]=n,c[e+20>>2]=a,1&A&&((e=31^v(A))-1>>>0>28||(b=1,c[r+8>>2]=e+1,a=-2^(e=-2<<e),c[r+16>>2]=a,c[r+12>>2]=-1^e,c[r+24>>2]=a>>1,p[r+20>>2]=d(2)/d(0|a))))),0|b},function(r,e,i,t,f,a){r|=0,e|=0,i|=0,t|=0,a|=0;var n=0,A=0,o=0,b=0,u=0,k=0,_=0,s=0;if(Z=a=Z-32|0,A=Sr(vi(A=(f|=0)>>>0>1073741823?-1:f<<2),0,A),n=c[e>>2],o=c[e+4>>2],u=c[A+4>>2],c[a+16>>2]=c[A>>2],c[a+20>>2]=u,c[a+8>>2]=n,c[a+12>>2]=o,_r(a+24|0,o=r+8|0,a+16|0,a+8|0),c[i>>2]=c[a+24>>2],c[i+4>>2]=c[a+28>>2],(0|t)>(0|f))for(u=0-f<<2,r=f;_=c[(b=(n=r<<2)+e|0)>>2],b=c[b+4>>2],s=c[4+(k=(n=i+n|0)+u|0)>>2],c[a+16>>2]=c[k>>2],c[a+20>>2]=s,c[a+8>>2]=_,c[a+12>>2]=b,_r(a+24|0,o,a+16|0,a+8|0),c[n>>2]=c[a+24>>2],c[n+4>>2]=c[a+28>>2],(0|t)>(0|(r=r+f|0)););return er(A),Z=a+32|0,1},function(r){var e=0;return c[24+(r|=0)>>2]=1624,c[r>>2]=7948,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),c[r>>2]=2136,e=c[r+20>>2],c[r+20>>2]=0,e&&Zt[c[c[e>>2]+4>>2]](e),c[r>>2]=1920,e=c[r+16>>2],c[r+16>>2]=0,e&&xe(e),0|r},function(r){var e=0;c[24+(r|=0)>>2]=1624,c[r>>2]=7948,(e=c[r+32>>2])&&(c[r+36>>2]=e,er(e)),c[r>>2]=2136,e=c[r+20>>2],c[r+20>>2]=0,e&&Zt[c[c[e>>2]+4>>2]](e),c[r>>2]=1920,e=c[r+16>>2],c[r+16>>2]=0,e&&xe(e),er(r)},function(r,e,i){var t;return e|=0,i|=0,c[4+(r|=0)>>2]=e,t=c[c[c[e+4>>2]+8>>2]+(i<<2)>>2],c[r+12>>2]=i,c[r+8>>2]=t,9==c[c[c[c[e+4>>2]+8>>2]+(i<<2)>>2]+28>>2]|0},function(r,e,i){return e|=0,k[c[4+(r|=0)>>2]+36|0]>=2&&(e=0,!(0|Zt[c[c[r>>2]+52>>2]](r)))||(e=ze(r+24|0,c[r+16>>2])),0|e},Oi,function(r,e){return e|=0,0|Zt[c[c[(r|=0)>>2]+56>>2]](r,e)},function(r){return 0|Fr(24+(r|=0)|0,ne(r)||c[r+8>>2],c[c[r+4>>2]+32>>2])},function(r,e){return 0|Gr(24+(r|=0)|0,ne(r),c[r+8>>2])},function(r){var e,i=0,t=0;if(c[(r|=0)>>2]=10032,(i=c[r+20>>2])&&(c[r+24>>2]=i,er(i)),e=c[r+8>>2]){if((0|(t=c[r+12>>2]))!=(0|(i=e))){for(;i=c[(t=t-4|0)>>2],c[t>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(0|t)!=(0|e););i=c[r+8>>2]}c[r+12>>2]=e,er(i)}return 0|r},ht,ct,ct,function(r){return c[44+(r|=0)>>2]?0|Zt[c[c[r>>2]+48>>2]](r):0},function(r){var e=0,i=0,t=0,f=0,a=0,n=0,A=0,o=0,b=0,u=0;r:{if(e=c[32+(r|=0)>>2],a=c[e+8>>2],f=0,!((0|(A=c[e+12>>2]))<=(0|(n=c[e+20>>2]))&(i=c[e+16>>2])>>>0>=a>>>0|(0|n)>(0|A))){a=k[c[e>>2]+i|0],f=e,e=n,e=(i=i+1|0)?e:e+1|0,c[f+16>>2]=i,c[f+20>>2]=e;e:if(a){for(;;){if(0|Zt[c[c[r>>2]+16>>2]](r,t)){if((0|a)!=(0|(t=t+1|0)))continue;break e}break}return 0}if((0|(t=c[r+8>>2]))!=(0|(e=c[r+12>>2])))for(;;){if(i=c[t>>2],!(0|Zt[c[c[i>>2]+8>>2]](i,r,c[r+4>>2])))break r;if((0|e)==(0|(t=t+4|0)))break}if(a){for(t=0;;){if(e=c[c[r+8>>2]+(t<<2)>>2],!(0|Zt[c[c[e>>2]+12>>2]](e,c[r+32>>2])))break r;if((0|a)==(0|(t=t+1|0)))break}if(a)for(o=r+20|0,e=0;;){if(t=0,i=c[(b=e<<2)+c[r+8>>2]>>2],(0|(u=0|Zt[c[c[i>>2]+24>>2]](i)))>0)for(;i=c[c[r+8>>2]+b>>2],i=0|Zt[c[c[i>>2]+20>>2]](i,t),f=c[r+20>>2],i>>>0<(n=c[r+24>>2]-f>>2)>>>0||((A=i+1|0)>>>0>n>>>0?(_e(o,A-n|0),f=c[o>>2]):n>>>0<=A>>>0||(c[r+24>>2]=(A<<2)+f)),c[(i<<2)+f>>2]=e,(0|u)!=(0|(t=t+1|0)););if((0|a)==(0|(e=e+1|0)))break}}f=0,0|Zt[c[c[r>>2]+28>>2]](r)&&(f=0|Zt[c[c[r>>2]+32>>2]](r))}return 0|f}return 0},function(r){var e,i=0,t=0;if((0|(i=c[8+(r|=0)>>2]))==(0|(e=c[r+12>>2])))return 1;for(;t=c[i>>2],(t=0|Zt[c[c[t>>2]+16>>2]](t,c[r+32>>2]))&&(0|e)!=(0|(i=i+4|0)););return 0|t},ct,pt,at,at,function(r){var e,i=0,t=0;if(c[(r|=0)>>2]=8176,i=c[r+48>>2],c[r+48>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),c[r>>2]=10032,(i=c[r+20>>2])&&(c[r+24>>2]=i,er(i)),e=c[r+8>>2]){if((0|(t=c[r+12>>2]))!=(0|(i=e))){for(;i=c[(t=t-4|0)>>2],c[t>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(0|t)!=(0|e););i=c[r+8>>2]}c[r+12>>2]=e,er(i)}return 0|r},function(r){var e,i=0,t=0;if(c[(r|=0)>>2]=8176,i=c[r+48>>2],c[r+48>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),c[r>>2]=10032,(i=c[r+20>>2])&&(c[r+24>>2]=i,er(i)),e=c[r+8>>2]){if((0|(t=c[r+12>>2]))!=(0|(i=e))){for(;i=c[(t=t-4|0)>>2],c[t>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(0|t)!=(0|e););i=c[r+8>>2]}c[r+12>>2]=e,er(i)}er(r)},function(r){var e=0,i=0,t=0,f=0;if(i=c[32+(r|=0)>>2],t=c[i+16>>2],f=c[i+12>>2],e=c[i+20>>2],s[i+8>>2]>t>>>0&(0|f)>=(0|e)|(0|e)<(0|f)){f=k[c[i>>2]+t|0],e=(t=t+1|0)?e:e+1|0,c[i+16>>2]=t,c[i+20>>2]=e,e=c[r+48>>2],c[r+48>>2]=0,e&&Zt[c[c[e>>2]+4>>2]](e);r:{e:{i:{t:switch(0|f){case 0:e=vi(384),c[e>>2]=8284,Sr(e+4|0,0,80),c[e+96>>2]=0,c[e+100>>2]=0,c[e+92>>2]=-1,c[e+84>>2]=-1,c[e+88>>2]=-1,c[e+104>>2]=0,c[e+108>>2]=0,c[e+112>>2]=0,c[e+116>>2]=0,c[e+120>>2]=0,c[e+124>>2]=0,c[e+128>>2]=0,c[e+132>>2]=0,c[e+136>>2]=0,c[e+140>>2]=0,c[e+144>>2]=0,c[e+148>>2]=0,c[e+156>>2]=0,c[e+160>>2]=0,c[e+152>>2]=1065353216,c[e+164>>2]=0,c[e+168>>2]=0,c[e+172>>2]=0,c[e+176>>2]=0,c[e+180>>2]=0,c[e+184>>2]=0,c[e+188>>2]=0,c[e+192>>2]=0,c[e+196>>2]=0,c[e+200>>2]=0,c[e+204>>2]=0,c[e+208>>2]=0,c[e+212>>2]=-1,c[e+216>>2]=0,c[e+220>>2]=0,c[e+224>>2]=0,pi(e+232|0),pi(e+272|0),c[(i=e+312|0)>>2]=0,c[i+4>>2]=0,n[i+5|0]=0,n[i+6|0]=0,n[i+7|0]=0,n[i+8|0]=0,n[i+9|0]=0,n[i+10|0]=0,n[i+11|0]=0,n[i+12|0]=0,pi(e+328|0),c[e+376>>2]=0,c[e+368>>2]=0,c[e+372>>2]=0;break i;case 2:break t;default:break e}e=vi(440),c[e>>2]=8336,Sr(e+4|0,0,80),c[e+96>>2]=0,c[e+100>>2]=0,c[e+92>>2]=-1,c[e+84>>2]=-1,c[e+88>>2]=-1,c[e+104>>2]=0,c[e+108>>2]=0,c[e+112>>2]=0,c[e+116>>2]=0,c[e+120>>2]=0,c[e+124>>2]=0,c[e+128>>2]=0,c[e+132>>2]=0,c[e+136>>2]=0,c[e+140>>2]=0,c[e+144>>2]=0,c[e+148>>2]=0,c[e+156>>2]=0,c[e+160>>2]=0,c[e+152>>2]=1065353216,c[e+164>>2]=0,c[e+168>>2]=0,c[e+172>>2]=0,c[e+176>>2]=0,c[e+180>>2]=0,c[e+184>>2]=0,c[e+188>>2]=0,c[e+192>>2]=0,c[e+196>>2]=0,c[e+200>>2]=0,c[e+204>>2]=0,c[e+208>>2]=0,c[e+212>>2]=-1,c[e+216>>2]=0,c[e+220>>2]=0,c[e+224>>2]=0,pi(e+232|0),pi(e+272|0),c[(i=e+312|0)>>2]=0,c[i+4>>2]=0,n[i+5|0]=0,n[i+6|0]=0,n[i+7|0]=0,n[i+8|0]=0,n[i+9|0]=0,n[i+10|0]=0,n[i+11|0]=0,n[i+12|0]=0,pi(e+328|0),c[e+392>>2]=0,c[e+396>>2]=0,c[e+384>>2]=0,c[e+388>>2]=0,c[e+376>>2]=0,c[e+380>>2]=0,c[e+368>>2]=0,c[e+372>>2]=0,c[e+416>>2]=0,c[e+420>>2]=0,c[e+408>>2]=2,c[e+412>>2]=7,c[e+400>>2]=-1,c[e+404>>2]=-1,c[e+424>>2]=0,c[e+428>>2]=0,c[e+432>>2]=0,c[e+436>>2]=0}if(i=c[r+48>>2],c[r+48>>2]=e,!i)break r;Zt[c[c[i>>2]+4>>2]](i)}if(!(e=c[r+48>>2]))return 0}r=0|Zt[c[c[e>>2]+8>>2]](e,r)}else r=0;return 0|r},function(r,e){return e|=0,r=c[48+(r|=0)>>2],0|Zt[c[c[r>>2]+20>>2]](r,e)},function(r){return r=c[48+(r|=0)>>2],0|Zt[c[c[r>>2]+28>>2]](r)},function(r){return r=c[48+(r|=0)>>2],0|Zt[c[c[r>>2]+36>>2]](r)},function(r,e){return e|=0,r=c[48+(r|=0)>>2],0|Zt[c[c[r>>2]+12>>2]](r,e)},function(r,e){return e|=0,r=c[48+(r|=0)>>2],0|Zt[c[c[r>>2]+16>>2]](r,e)},function(r){return r=c[48+(r|=0)>>2],0|Zt[c[c[r>>2]+24>>2]](r)},Jr,function(r){er(Jr(r|=0))},Pi,fe,Ae,J,function(r){var e,i=0,t=0,f=0,a=0,b=0,u=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,E=0,F=0,I=0,Y=0,w=0,J=0,B=0,M=0,Q=0,g=0;if(Z=e=Z+-64|0,c[132+(r|=0)>>2]=0,c[r+148>>2]){if(t=c[r+144>>2])for(;i=c[t>>2],er(t),t=i,i;);if(t=0,c[r+144>>2]=0,i=c[r+140>>2]){if(i>>>0>=4)for(p=-4&i;c[(a=t<<2)+c[r+136>>2]>>2]=0,c[c[r+136>>2]+(4|a)>>2]=0,c[c[r+136>>2]+(8|a)>>2]=0,c[c[r+136>>2]+(12|a)>>2]=0,t=t+4|0,(0|p)!=(0|(u=u+4|0)););if(i&=3)for(u=0;c[c[r+136>>2]+(t<<2)>>2]=0,t=t+1|0,(0|i)!=(0|(u=u+1|0)););}c[r+148>>2]=0}if(Ie(1,e+60|0,c[c[r+4>>2]+32>>2])&&(c[r+156>>2]=c[e+60>>2],Ie(1,e+56|0,c[c[r+4>>2]+32>>2])&&!((a=c[e+56>>2])>>>0>1431655765|s[r+156>>2]>y(a,3)>>>0)&&(t=c[c[r+4>>2]+32>>2],p=c[t+8>>2],!((0|(h=c[t+12>>2]))<=(0|(i=c[t+20>>2]))&(u=c[t+16>>2])>>>0>=p>>>0|(0|i)>(0|h))&&(p=k[u+c[t>>2]|0],i=(u=u+1|0)?i:i+1|0,c[t+16>>2]=u,c[t+20>>2]=i,Ie(1,e+52|0,t)&&!((R=c[e+52>>2])>>>0>a>>>0|a>>>0>R+((R>>>0)/3|0)>>>0)&&Ie(1,e+48|0,c[c[r+4>>2]+32>>2])&&!((t=c[e+48>>2])>>>0>R>>>0)&&(c[r+28>>2]=c[r+24>>2],u=Ue(vi(88)),i=c[r+8>>2],c[r+8>>2]=u,(!i||(ge(i),c[r+8>>2]))&&(c[r+164>>2]=c[r+160>>2],Le(r+160|0,a),c[r+176>>2]=c[r+172>>2],Le(r+172|0,a),c[r- -64>>2]=0,c[r+92>>2]=-1,c[r+84>>2]=-1,c[r+88>>2]=-1,c[r+40>>2]=c[r+36>>2],c[r+52>>2]=c[r+48>>2],c[r+76>>2]=c[r+72>>2],Qe(F=r+216|0),K(F,p),qr(c[r+8>>2],a,t+c[r+156>>2]|0)&&(i=c[r+156>>2],n[e+8|0]=1,xr(r+120|0,i+t|0,e+8|0),-1!=(0|kr(r,c[c[r+4>>2]+32>>2]))))))))){c[144+(t=r+232|0)>>2]=r,i=c[32+(0|Zt[c[c[r>>2]+32>>2]](r))>>2],i=c[i>>2]+c[i+16>>2]|0,u=c[32+(0|Zt[c[c[r>>2]+32>>2]](r))>>2],u=c[u+8>>2]-c[u+16>>2]|0,Q=t,g=_[c[32+(0|Zt[c[c[r>>2]+32>>2]](r))>>2]+38>>1],A[Q+38>>1]=g,c[t>>2]=i,c[t+16>>2]=0,c[t+20>>2]=0,c[t+8>>2]=u,c[t+12>>2]=0,c[r+372>>2]=p,p=J=pi(e+8|0),h=0,Z=f=Z-16|0,i=c[t+4>>2],c[t+40>>2]=c[t>>2],c[t+44>>2]=i,i=c[t+36>>2],c[t+72>>2]=c[t+32>>2],c[t+76>>2]=i,u=c[t+28>>2],c[(i=t- -64|0)>>2]=c[t+24>>2],c[i+4>>2]=u,i=c[t+20>>2],c[t+56>>2]=c[t+16>>2],c[t+60>>2]=i,i=c[t+12>>2],c[t+48>>2]=c[t+8>>2],c[t+52>>2]=i,bi(t+40|0,1,f+8|0)&&(i=c[t+44>>2],c[t>>2]=c[t+40>>2],c[t+4>>2]=i,i=c[t+76>>2],c[t+32>>2]=c[t+72>>2],c[t+36>>2]=i,i=c[t+68>>2],c[t+24>>2]=c[t+64>>2],c[t+28>>2]=i,b=u=c[t+60>>2],i=c[t+56>>2],c[t+16>>2]=i,c[t+20>>2]=u,a=c[t+52>>2],u=c[t+48>>2],c[t+8>>2]=u,c[t+12>>2]=a,(0|(m=c[f+12>>2]))==(0|(l=a-((i>>>0>u>>>0)+b|0)|0))&(a=u-i|0)>>>0>=(u=c[f+8>>2])>>>0|l>>>0>m>>>0)&&(a=b+m|0,a=(i=i+u|0)>>>0<u>>>0?a+1|0:a,c[t+16>>2]=i,c[t+20>>2]=a,Er(t+80|0,t)&&$r(t)&&(i=c[t+4>>2],c[p>>2]=c[t>>2],c[p+4>>2]=i,i=c[t+36>>2],c[p+32>>2]=c[t+32>>2],c[p+36>>2]=i,i=c[t+28>>2],c[p+24>>2]=c[t+24>>2],c[p+28>>2]=i,i=c[t+20>>2],c[p+16>>2]=c[t+16>>2],c[p+20>>2]=i,i=c[t+12>>2],c[p+8>>2]=c[t+8>>2],c[p+12>>2]=i,h=1)),Z=f+16|0;r:if(h){i=0,t=0,u=0,h=0,Z=d=Z-96|0,c[d+72>>2]=0,c[d+64>>2]=0,c[d+68>>2]=0,c[d+48>>2]=0,c[d+52>>2]=0,c[d+40>>2]=0,c[d+44>>2]=0,c[d+56>>2]=1065353216,c[d+32>>2]=0,c[d+24>>2]=0,c[d+28>>2]=0,p=r,w=c[r+124>>2];e:{i:{t:{f:{a:{n:{A:{o:if(!((0|R)<=0)){for(B=c[p+216>>2]!=c[p+220>>2],I=1;;){h=(a=h)+1|0;b:{u:{c:{k:{_:{s:{p:{l:{y:{d:{m:{v:{h:{R:{if(k[p+308|0]&&!((r=(m=c[p+296>>2])+((f=c[p+304>>2])>>>3|0)|0)>>>0>=(v=c[p+300>>2])>>>0)&&(b=k[0|r],r=f+1|0,c[p+304>>2]=r,T=b>>>(7&f)&1))switch(l=0,(U=m+(b=r>>>3|0)|0)>>>0>=v>>>0?(f=r,r=0):(U=k[0|U],f=f+2|0,c[p+304>>2]=f,b=f>>>3|0,r=U>>>(7&r)&1),(b=b+m|0)>>>0<v>>>0&&(b=k[0|b],c[p+304>>2]=f+1,l=b>>>(7&f)<<1&2),b=-1,(l=T|(r|l)<<1)-1|0){case 6:break v;case 0:break h;case 2:case 4:break R;default:break A}if((0|t)==(0|u)){b=-1;break A}if(f=-1,l=c[p+8>>2],I=c[l+24>>2],r=-1,-1!=(0|(i=c[(U=t-4|0)>>2]))&&(r=-1,-1!=(0|(m=((m=i+1|0)>>>0)%3|0?m:i-2|0))&&(r=c[c[l>>2]+(m<<2)>>2])),-1!=(0|(b=c[I+(r<<2)>>2]))&&(f=((f=b+1|0)>>>0)%3|0?f:b-2|0),(0|i)==(0|f)){b=-1;break A}if(-1!=(0|i)&&(b=-1,-1!=c[c[l+12>>2]+(i<<2)>>2]))break A;if(m=c[l+12>>2],-1!=(0|f)&&(b=-1,-1!=c[m+(f<<2)>>2]))break A;a=(v=y(a,3))+1|0,c[m+(i<<2)>>2]=a,c[(W=a<<2)+m>>2]=i,T=v+2|0,c[m+(f<<2)>>2]=T,c[(D=T<<2)+m>>2]=f,m=-1,a=-1;N:if(-1!=(0|i)){if((i>>>0)%3|0)i=i-1|0;else if(a=-1,-1==(0|(i=i+2|0)))break N;a=c[c[l>>2]+(i<<2)>>2]}if(i=a,-1!=(0|f)&&-1!=(0|(a=((a=f+1|0)>>>0)%3|0?a:f-2|0))&&(m=c[c[l>>2]+(a<<2)>>2]),b=-1,(0|r)==(0|i)|(0|r)==(0|m))break A;a=c[l>>2],c[a+(v<<2)>>2]=r,c[a+W>>2]=m,c[a+D>>2]=i,-1!=(0|i)&&(c[I+(i<<2)>>2]=T),i=c[p+120>>2]+(r>>>3&536870908)|0,a=c[i>>2],Q=i,g=Yi(r)&a,c[Q>>2]=g,c[U>>2]=v,i=u;break b}if((0|t)==(0|u))break A;if(i=c[(U=t-4|0)>>2],r=c[p+8>>2],f=c[r+12>>2],-1!=(0|i)&-1!=c[f+(i<<2)>>2])break A;if(v=5==(0|l),l=y(a,3),c[(W=(T=(v?2:1)+l|0)<<2)+f>>2]=i,c[f+(i<<2)>>2]=T,be(r+24|0,8324),f=c[p+8>>2],m=c[f+24>>2],c[f+28>>2]-m>>2>(0|w))break A;if(D=(f=c[f>>2])+W|0,W=((b=c[r+28>>2])-(r=c[r+24>>2])>>2)-1|0,c[D>>2]=W,(0|r)!=(0|b)&&(c[m+(W<<2)>>2]=T),b=v?l:l+2|0,v=f+(l+v<<2)|0,-1!=(0|i)){R:{N:{if((i>>>0)%3|0)r=i-1|0;else if(-1==(0|(r=i+2|0)))break N;if(r=c[f+(r<<2)>>2],c[f+(b<<2)>>2]=r,-1==(0|r))break R;c[m+(r<<2)>>2]=b;break R}c[f+(b<<2)>>2]=-1}r=-1,-1!=(0|(i=((b=i+1|0)>>>0)%3|0?b:i-2|0))&&(r=c[f+(i<<2)>>2])}else c[f+(b<<2)>>2]=-1,r=-1;c[v>>2]=r,c[U>>2]=l,i=u;break p}if((0|i)==(0|t))break A;m=c[(r=t-4|0)>>2],c[d+68>>2]=r;h:if(v=c[d+44>>2])if(b=c[d+40>>2],f=a&v+2147483647,(T=Ii(v)>>>0>1)&&((f=a)>>>0<v>>>0||(f=(a>>>0)%(v>>>0)|0)),f=c[b+((l=f)<<2)>>2])if(f=c[f>>2]){R:{if(!T){for(b=v-1|0;;){N:{if((0|(v=c[f+4>>2]))!=(0|a)){if((0|l)==(b&v))break N;t=r;break h}if((0|a)==c[f+8>>2])break R}if(!(f=c[f>>2]))break}t=r;break h}for(;;){N:{if((0|(b=c[f+4>>2]))!=(0|a)){if(b>>>0>=v>>>0&&(b=(b>>>0)%(v>>>0)|0),(0|b)==(0|l))break N;t=r;break h}if((0|a)==c[f+8>>2])break R}if(!(f=c[f>>2]))break}t=r;break h}if((0|r)==(0|V)){if((t=1+(u=(r=V-i|0)>>2)|0)>>>0>=1073741824)break m;if(b=r>>>1|0,b=r>>>0>=2147483644?1073741823:t>>>0<b>>>0?b:t){if(b>>>0>=1073741824)break a;r=vi(b<<2)}else r=0;if(c[(u=r+(u<<2)|0)>>2]=c[f+12>>2],t=u+4|0,(0|i)!=(0|V))for(;V=V-4|0,c[(u=u-4|0)>>2]=c[V>>2],(0|i)!=(0|V););V=r+(b<<2)|0,c[d+72>>2]=V,c[d+68>>2]=t,c[d+64>>2]=u,i&&er(i)}else c[r>>2]=c[f+12>>2],c[d+68>>2]=t}else t=r;else t=r;else t=r;if((0|t)==(0|u))break c;if((0|(i=c[(W=t-4|0)>>2]))==(0|m))break c;if(r=-1==(0|i),b=c[p+8>>2],!r&-1!=c[c[b+12>>2]+(i<<2)>>2])break c;if(v=c[b+12>>2],-1!=(0|m)&-1!=c[v+(m<<2)>>2])break c;if(U=(T=y(a,3))+2|0,c[v+(i<<2)>>2]=U,c[(a=U<<2)+v>>2]=i,f=T+1|0,c[v+(m<<2)>>2]=f,c[(D=f<<2)+v>>2]=m,r)break d;if((i>>>0)%3|0){f=i-1|0;break s}if(-1!=(0|(f=i+2|0)))break s;r=c[b>>2],f=-1;break _}if(be((l=c[p+8>>2])+24|0,8324),f=c[p+8>>2],r=y(a,3),U=(l=(T=(m=c[l+28>>2])-(v=c[l+24>>2])|0)>>2)-1|0,c[c[f>>2]+(r<<2)>>2]=U,be(f+24|0,8324),W=r+1|0,c[c[f>>2]+(W<<2)>>2]=(c[f+28>>2]-c[f+24>>2]>>2)-1,be((f=c[p+8>>2])+24|0,8324),D=r+2|0,c[c[f>>2]+(D<<2)>>2]=(c[f+28>>2]-c[f+24>>2]>>2)-1,Y=c[p+8>>2],f=c[Y+24>>2],c[Y+28>>2]-f>>2>(0|w))break A;if(((0|m)!=(0|v)&&(c[f+(U<<2)>>2]=r,b=0,-4==(0|T))||(c[f+(l<<2)>>2]=W,-1!=(0|(b=l+1|0))))&&(c[f+(b<<2)>>2]=D),(0|t)!=(0|V)){c[t>>2]=r,t=t+4|0,c[d+68>>2]=t;break p}if((f=1+(l=(u=t-i|0)>>2)|0)>>>0>=1073741824)break y;if(b=u>>>1|0,f=u>>>0>=2147483644?1073741823:f>>>0<b>>>0?b:f){if(f>>>0>=1073741824)break a;b=vi(f<<2)}else b=0;if(c[(u=b+(l<<2)|0)>>2]=r,V=b+(f<<2)|0,r=u+4|0,(0|i)!=(0|t))for(;t=t-4|0,c[(u=u-4|0)>>2]=c[t>>2],(0|i)!=(0|t););if(c[d+72>>2]=V,c[d+68>>2]=r,c[d+64>>2]=u,!i)break l;er(i);break l}mt(),o()}f=-1,r=c[b>>2],c[r+(T<<2)>>2]=-1,l=-1;break k}mt(),o()}t=r,i=u}if((0|(r=c[p+40>>2]))==c[p+36>>2])break b;if((l=c[4+(f=r-12|0)>>2])>>>0>(b=R+(-1^a)|0)>>>0)break c;if((0|b)!=(0|l))break b;if(l=k[r-4|0],a=c[f>>2],c[p+40>>2]=f,(0|a)<0)break c;r=c[(m=t-4|0)>>2],c[d+20>>2]=R+(-1^a),a=d+20|0,c[d+88>>2]=a,tr(d,d+40|0,a,d+88|0),f=c[d>>2];p:if(1&l){if(a=-1,-1==(0|r))break p;a=((a=r+1|0)>>>0)%3|0?a:r-2|0}else a=-1,-1!=(0|r)&&(a=r-1|0,(r>>>0)%3|0||(a=r+2|0));if(c[f+12>>2]=a,(0|(f=c[p+40>>2]))==c[p+36>>2])break b;for(;;){if((a=c[4+(r=f-12|0)>>2])>>>0>b>>>0)break c;if((0|b)!=(0|a))break b;if(f=k[f-4|0],a=c[r>>2],c[p+40>>2]=r,(0|a)<0)break c;r=c[m>>2],c[d+20>>2]=R+(-1^a),a=d+20|0,c[d+88>>2]=a,tr(d,d+40|0,a,d+88|0),l=c[d>>2];p:if(1&f){if(a=-1,-1==(0|r))break p;a=((a=r+1|0)>>>0)%3|0?a:r-2|0}else a=-1,-1!=(0|r)&&(a=r-1|0,(r>>>0)%3|0||(a=r+2|0));if(c[l+12>>2]=a,(0|(f=c[p+40>>2]))==c[p+36>>2])break}break b}r=c[b>>2],f=c[r+(f<<2)>>2]}c[(T<<2)+r>>2]=f,l=-1,-1!=(0|(i=((Y=i+1|0)>>>0)%3|0?Y:i-2|0))&&(l=c[(i<<2)+r>>2])}if(c[r+D>>2]=l,-1!=(0|m)){k:{_:{if((m>>>0)%3|0)i=m-1|0;else if(-1==(0|(i=m+2|0)))break _;if(i=c[(i<<2)+r>>2],c[r+a>>2]=i,-1==(0|i))break k;c[c[b+24>>2]+(i<<2)>>2]=U;break k}c[r+a>>2]=-1}l=-1,a=-1,-1!=(0|(i=((i=m+1|0)>>>0)%3|0?i:m-2|0))&&(l=c[(i<<2)+r>>2],a=i)}else c[r+a>>2]=-1,l=-1,a=-1;for(m=(i=c[b+24>>2])+(l<<2)|0,-1!=(0|f)&&(c[i+(f<<2)>>2]=c[m>>2]),i=a;;){if(-1==(0|i))break u;if(c[(i<<2)+r>>2]=f,b=-1,-1!=(0|(i=((U=i+1|0)>>>0)%3|0?U:i-2|0))&&(b=-1,-1!=(0|(i=c[v+(i<<2)>>2]))&&(b=((b=i+1|0)>>>0)%3|0?b:i-2|0)),(0|a)==(0|(i=b)))break}}if(b=-1,!I)break o;break A}if(c[m>>2]=-1,!B)if((0|G)==(0|E)){if((i=1+(f=(r=G-N|0)>>2)|0)>>>0>=1073741824)break f;if(a=r>>>1|0,a=r>>>0>=2147483644?1073741823:i>>>0<a>>>0?a:i){if(a>>>0>=1073741824)break a;r=vi(a<<2)}else r=0;if(c[(i=r+(f<<2)|0)>>2]=l,E=i+4|0,(0|N)!=(0|G))for(;G=G-4|0,c[(i=i-4|0)>>2]=c[G>>2],(0|N)!=(0|G););G=r+(a<<2)|0,c[d+32>>2]=G,c[d+28>>2]=E,c[d+24>>2]=i,N&&er(N),N=i}else c[E>>2]=l,E=E+4|0,c[d+28>>2]=E;c[W>>2]=T,i=u}if(I=(0|h)<(0|R),(0|h)==(0|R))break}h=R}if(b=-1,r=c[p+8>>2],!(c[r+28>>2]-c[r+24>>2]>>2>(0|w))){if((0|t)!=(0|u)){for(v=p+72|0,a=p+60|0,G=p+312|0;;){if(l=c[(t=t-4|0)>>2],c[d+68>>2]=t,Se(G)){if(V=c[p+8>>2],m=c[V>>2],((c[V+4>>2]-m>>2>>>0)/3|0)<=(0|h)){b=-1;break A}if(r=-1,b=-1,i=-1,E=c[V+24>>2],u=-1,-1!=(0|l)&&(u=-1,-1!=(0|(R=((R=l+1|0)>>>0)%3|0?R:l-2|0))&&(u=c[m+(R<<2)>>2])),-1!=(0|(N=c[E+((R=u)<<2)>>2]))?(f=1,u=-1,-1!=(0|(N=((T=N+1|0)>>>0)%3|0?T:N-2|0))&&(f=0,u=-1!=(0|(u=((u=(r=N)+1|0)>>>0)%3|0?u:r-2|0))?c[m+(u<<2)>>2]:-1)):(f=1,u=-1),-1!=(0|(N=c[(u<<2)+E>>2]))&&(i=((i=N+1|0)>>>0)%3|0?i:N-2|0),(0|r)==(0|l)|(0|i)==(0|l)|-1!=(0|l)&-1!=c[c[V+12>>2]+(l<<2)>>2]|(0|r)==(0|i))break A;if(!f&-1!=c[c[V+12>>2]+(r<<2)>>2])break A;if(f=-1,N=c[V+12>>2],V=-1,-1!=(0|i)){if(-1!=c[N+(i<<2)>>2])break A;V=-1,-1!=(0|(b=((b=i+1|0)>>>0)%3|0?b:i-2|0))&&(V=c[m+(b<<2)>>2])}b=y(h,3),c[d>>2]=b,c[N+(b<<2)>>2]=l,c[N+(l<<2)>>2]=b,b=c[d>>2]+1|0,c[N+(b<<2)>>2]=r,c[N+(r<<2)>>2]=b,r=c[d>>2]+2|0,c[N+(r<<2)>>2]=i,c[N+(i<<2)>>2]=r,r=c[d>>2],c[m+(r<<2)>>2]=u,c[(b=m+((i=r+1|0)<<2)|0)>>2]=V,c[(l=m+((N=r+2|0)<<2)|0)>>2]=R,r=c[p+120>>2],m=c[(R=r+((u=i?u:-1)>>>3&536870908)|0)>>2],Q=R,g=Yi(u)&m,c[Q>>2]=g,f=-1!=(0|i)?c[b>>2]:f,u=c[(i=r+(f>>>3&536870908)|0)>>2],Q=i,g=Yi(f)&u,c[Q>>2]=g,i=-1,i=-1!=(0|N)?c[l>>2]:i,u=c[(r=r+(i>>>3&536870908)|0)>>2],Q=r,g=Yi(i)&u,c[Q>>2]=g,n[d+88|0]=1,Ve(a,d+88|0),be(v,d),h=h+1|0,u=c[d+64>>2]}else{if((0|(i=c[p+64>>2]))==(r=c[p+68>>2])<<5){if((i+1|0)<0)break t;nr(a,r=i>>>0<=1073741822?(r<<=6)>>>0>(i=32+(-32&i)|0)>>>0?r:i:2147483647),i=c[p+64>>2]}if(c[p+64>>2]=i+1,r=c[p+60>>2]+(i>>>3&536870908)|0,f=c[r>>2],Q=r,g=Yi(i)&f,c[Q>>2]=g,(0|(i=c[p+76>>2]))==c[p+80>>2]){if((f=1+(N=(r=i-(b=c[v>>2])|0)>>2)|0)>>>0>=1073741824)break i;if(R=r>>>1|0,R=r>>>0>=2147483644?1073741823:f>>>0<R>>>0?R:f){if(R>>>0>=1073741824)break a;r=vi(R<<2)}else r=0;if(c[(f=r+(N<<2)|0)>>2]=l,N=f+4|0,(0|i)!=(0|b))for(;i=i-4|0,c[(f=f-4|0)>>2]=c[i>>2],(0|i)!=(0|b););c[p+80>>2]=r+(R<<2),c[p+76>>2]=N,c[p+72>>2]=f,b&&er(b)}else c[i>>2]=l,c[p+76>>2]=i+4}if((0|t)==(0|u))break}r=c[p+8>>2]}if(b=-1,((c[r+4>>2]-c[r>>2]>>2>>>0)/3|0)==(0|h)){if(b=c[r+28>>2]-c[r+24>>2]>>2,(0|(t=c[d+24>>2]))==(0|(R=c[d+28>>2])))break n;for(;;){if(u=c[t>>2],a=c[r+24>>2],-1==c[(f=a+((i=b-1|0)<<2)|0)>>2])for(;i=b-2|0,b=b-1|0,-1==c[(f=a+(i<<2)|0)>>2];);if(i>>>0>=u>>>0){if(c[d>>2]=r,f=c[f>>2],n[d+12|0]=1,c[d+8>>2]=f,c[d+4>>2]=f,-1!=(0|f)){for(;;){if(r=c[c[p+8>>2]>>2]+(f<<2)|0,c[r>>2]!=(0|i)){b=-1;break A}if(c[r>>2]=u,Mr(d),-1==(0|(f=c[d+8>>2])))break}r=c[p+8>>2]}a=(h=c[r+24>>2])+(i<<2)|0,-1!=(0|u)&&(c[h+(u<<2)>>2]=c[a>>2]),c[a>>2]=-1,a=1<<u,u=(h=c[p+120>>2])+(u>>>3&536870908)|0,i=(f=1<<i)&c[(h=h+(i>>>3&536870908)|0)>>2]?a|c[u>>2]:c[u>>2]&(-1^a),c[u>>2]=i,c[h>>2]=c[h>>2]&(-1^f),b=b-1|0}if((0|R)==(0|(t=t+4|0)))break}}}}t=c[d+24>>2]}if(t&&er(t),r=c[d+48>>2])for(;t=c[r>>2],er(r),r=t;);r=c[d+40>>2],c[d+40>>2]=0,r&&er(r),(r=c[d+64>>2])&&(c[d+68>>2]=r,er(r)),Z=d+96|0,r=b;break e}Zi(),o()}mt(),o()}mt(),o()}mt(),o()}if(i=r,-1!=(0|r)){u=(t=c[(r=J)+16>>2])+c[r>>2]|0,t=c[r+8>>2]-t|0,r=c[c[p+4>>2]+32>>2],A[r+38>>1]=_[r+38>>1],c[r>>2]=u,c[r+16>>2]=0,c[r+20>>2]=0,c[r+8>>2]=t,c[r+12>>2]=0;e:if(c[p+216>>2]!=c[p+220>>2]&&(r=c[p+8>>2],c[r+4>>2]!=c[r>>2])){for(t=0;;){if(cr(p,t)){if(t=t+3|0,r=c[p+8>>2],t>>>0<c[r+4>>2]-c[r>>2]>>2>>>0)continue;break e}break}break r}if(k[p+308|0]&&(n[p+308|0]=0,u=c[p+292>>2],r=0,t=(r=(a=c[p+304>>2]+7|0)>>>0<7?1:r)>>>3|0,r=(h=r<<29|a>>>3)+c[p+288>>2]|0,a=t+u|0,c[p+288>>2]=r,c[p+292>>2]=r>>>0<h>>>0?a+1|0:a),(0|(t=c[p+216>>2]))!=c[p+220>>2])for(r=0;;){if(Vr(4+((a=y(r,144))+t|0)|0,c[p+8>>2]),u=c[F>>2],(0|(t=c[132+(h=u+a|0)>>2]))!=(0|(h=c[h+136>>2]))){for(;Yr(4+(a+c[F>>2]|0)|0,c[t>>2]),(0|h)!=(0|(t=t+4|0)););u=c[F>>2]}if(!q(4+(u+a|0)|0))break r;if(r=r+1|0,t=c[p+216>>2],!(r>>>0<(c[p+220>>2]-t|0)/144>>>0))break}if(r=c[p+8>>2],ce(p+184|0,c[r+28>>2]-c[r+24>>2]>>2),(0|(u=c[p+216>>2]))!=c[p+220>>2])for(t=0;r=y(t,144)+u|0,u=c[r+60>>2]-c[r+56>>2]>>2,a=r+104|0,r=c[p+8>>2],ce(a,(0|(r=c[r+28>>2]-c[r+24>>2]>>2))<(0|u)?u:r),t=t+1|0,u=c[p+216>>2],t>>>0<(c[p+220>>2]-u|0)/144>>>0;);M=j(p,i)}}}return Z=e- -64|0,0|M},ct,tt,rt,Rr,function(r){er(Rr(r|=0))},Pi,fe,Ae,J,function(r){var e,i=0,t=0,f=0,a=0,b=0,u=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,E=0,I=0,Y=0,w=0,J=0,B=0,M=0,Q=0,g=0,O=0,C=0,z=0;if(Z=e=Z+-64|0,c[132+(r|=0)>>2]=0,c[r+148>>2]){if(i=c[r+144>>2])for(;b=c[i>>2],er(i),i=b;);if(i=0,c[r+144>>2]=0,b=c[r+140>>2]){if(b>>>0>=4)for(t=-4&b;c[(a=i<<2)+c[r+136>>2]>>2]=0,c[c[r+136>>2]+(4|a)>>2]=0,c[c[r+136>>2]+(8|a)>>2]=0,c[c[r+136>>2]+(12|a)>>2]=0,i=i+4|0,(0|t)!=(0|(f=f+4|0)););if(b&=3)for(f=0;c[c[r+136>>2]+(i<<2)>>2]=0,i=i+1|0,(0|b)!=(0|(f=f+1|0)););}c[r+148>>2]=0}r:{if(Ie(1,e+60|0,c[c[r+4>>2]+32>>2])&&(c[r+156>>2]=c[e+60>>2],Ie(1,e+56|0,c[c[r+4>>2]+32>>2])&&!((a=c[e+56>>2])>>>0>1431655765|s[r+156>>2]>y(a,3)>>>0)&&(i=c[c[r+4>>2]+32>>2],t=c[i+8>>2],!((0|(m=c[i+12>>2]))<=(0|(f=c[i+20>>2]))&(b=c[i+16>>2])>>>0>=t>>>0|(0|f)>(0|m))&&(m=k[b+c[i>>2]|0],f=(b=b+1|0)?f:f+1|0,c[i+16>>2]=b,c[i+20>>2]=f,Ie(1,e+52|0,i)&&!((V=c[e+52>>2])>>>0>a>>>0|a>>>0>V+((V>>>0)/3|0)>>>0)&&Ie(1,e+48|0,c[c[r+4>>2]+32>>2])&&!((f=c[e+48>>2])>>>0>V>>>0)&&(c[r+28>>2]=c[r+24>>2],b=Ue(vi(88)),i=c[r+8>>2],c[r+8>>2]=b,(!i||(ge(i),c[r+8>>2]))&&(c[r+164>>2]=c[r+160>>2],Le(r+160|0,a),c[r+176>>2]=c[r+172>>2],Le(r+172|0,a),c[r- -64>>2]=0,c[r+92>>2]=-1,c[r+84>>2]=-1,c[r+88>>2]=-1,c[r+40>>2]=c[r+36>>2],c[r+52>>2]=c[r+48>>2],c[r+76>>2]=c[r+72>>2],Qe(w=r+216|0),K(w,m),qr(c[r+8>>2],a,f+c[r+156>>2]|0)&&(i=c[r+156>>2],n[e+8|0]=1,xr(r+120|0,i+f|0,e+8|0),-1!=(0|kr(r,c[c[r+4>>2]+32>>2]))))))))){c[144+(i=t=r+232|0)>>2]=r,b=c[32+(0|Zt[c[c[r>>2]+32>>2]](r))>>2],b=c[b>>2]+c[b+16>>2]|0,a=c[32+(0|Zt[c[c[r>>2]+32>>2]](r))>>2],a=c[a+8>>2]-c[a+16>>2]|0,C=i,z=_[c[32+(0|Zt[c[c[r>>2]+32>>2]](r))>>2]+38>>1],A[C+38>>1]=z,c[i>>2]=b,c[i+16>>2]=0,c[i+20>>2]=0,c[i+8>>2]=a,c[i+12>>2]=0,C=i,z=0|Zt[c[c[r>>2]+36>>2]](r),c[C+148>>2]=z,c[r+372>>2]=m,c[r+384>>2]=f+c[r+156>>2],m=Q=pi(e+8|0),b=0,Z=d=Z-16|0;e:if(Er(i+80|0,i)&&$r(t)&&(i=c[t+4>>2],c[m>>2]=c[t>>2],c[m+4>>2]=i,i=c[t+36>>2],c[m+32>>2]=c[t+32>>2],c[m+36>>2]=i,i=c[t+28>>2],c[m+24>>2]=c[t+24>>2],c[m+28>>2]=i,i=c[t+20>>2],c[m+16>>2]=c[t+16>>2],c[m+20>>2]=i,i=c[t+12>>2],c[m+8>>2]=c[t+8>>2],c[m+12>>2]=i,c[t+176>>2]=2,c[t+180>>2]=7,!((0|(i=c[t+152>>2]))<0))){if(c[d+12>>2]=0,b=2,p=c[t+156>>2],(a=c[t+160>>2]-p>>2)>>>0<i>>>0?(Nr(t+156|0,i-a|0,d+12|0),b=c[t+176>>2],f=c[t+180>>2]):(f=7,i>>>0>=a>>>0||(c[t+160>>2]=p+(i<<2))),a=t+184|0,(f=1+(f-b|0)|0)>>>0>(W=((i=c[t+188>>2])-(b=c[t+184>>2])|0)/12|0)>>>0){if(p=0,(i=f-W|0)>>>0<=((N=c[a+8>>2])-(b=c[a+4>>2])|0)/12>>>0)i&&(b=Sr(b,0,i=12+((i=y(i,12)-12|0)-((i>>>0)%12|0)|0)|0)+i|0),c[a+4>>2]=b;else{i:{t:{f:{if((f=(u=(b-(W=c[a>>2])|0)/12|0)+i|0)>>>0<357913942){if(l=(N=(N-W|0)/12|0)<<1,N=N>>>0>=178956970?357913941:f>>>0<l>>>0?l:f){if(N>>>0>=357913942)break f;p=vi(y(N,12))}if(u=(i=Sr(f=y(u,12)+p|0,0,u=12+((i=y(i,12)-12|0)-((i>>>0)%12|0)|0)|0))+u|0,p=y(N,12)+p|0,(0|b)==(0|W))break t;for(;b=b-12|0,c[(f=f-12|0)>>2]=c[b>>2],c[f+4>>2]=c[b+4>>2],c[f+8>>2]=c[b+8>>2],c[b+8>>2]=0,c[b>>2]=0,c[b+4>>2]=0,(0|b)!=(0|W););if(c[a+8>>2]=p,i=c[a+4>>2],c[a+4>>2]=u,b=c[a>>2],c[a>>2]=f,(0|i)==(0|b))break i;for(;(p=c[(f=i-12|0)>>2])&&(c[i-8>>2]=p,er(p)),(0|b)!=(0|(i=f)););break i}break r}Zi(),o()}c[a+8>>2]=p,c[a+4>>2]=u,c[a>>2]=i}b&&er(b)}f=c[t+188>>2]}else if(f>>>0>=W>>>0)f=i;else{if((0|(f=b+y(f,12)|0))!=(0|i))for(;(p=c[(b=i-12|0)>>2])&&(c[i-8>>2]=p,er(p)),(0|f)!=(0|(i=b)););c[t+188>>2]=f}if(W=t+196|0,i=(f-(b=c[t+184>>2])|0)/12|0,N=c[t+196>>2],i>>>0>(p=c[t+200>>2]-N>>2)>>>0?(_e(W,i-p|0),b=c[t+184>>2],f=c[t+188>>2]):i>>>0>=p>>>0||(c[t+200>>2]=N+(i<<2)),(0|f)!=(0|b)){for(i=0;;){if(Ie(1,d+8|0,m)&&(b=c[d+8>>2],f=c[t+148>>2],!(b>>>0>(c[f+4>>2]-c[f>>2]>>2>>>0)/3>>>0))){if(b&&(p=(u=y(i,12))+c[a>>2]|0,f=c[p>>2],(N=c[p+4>>2]-f>>2)>>>0<b>>>0?(_e(p,b-N|0),f=c[u+c[a>>2]>>2]):b>>>0>=N>>>0||(c[p+4>>2]=(b<<2)+f),F(b,1,m,f),c[c[W>>2]+(i<<2)>>2]=b),b=1,(i=i+1|0)>>>0<(c[t+188>>2]-c[t+184>>2]|0)/12>>>0)continue;break e}break}b=0}else b=1}Z=d+16|0;e:if(b){a=0,t=0,f=0,i=0,m=0,b=0,W=0,N=0,Z=v=Z-96|0,c[v+72>>2]=0,c[v+64>>2]=0,c[v+68>>2]=0,c[v+48>>2]=0,c[v+52>>2]=0,c[v+40>>2]=0,c[v+44>>2]=0,c[v+56>>2]=1065353216,c[v+32>>2]=0,c[v+24>>2]=0,c[v+28>>2]=0,d=r,B=c[r+124>>2];i:{t:{f:{a:{n:if(!((0|V)<=0)){M=d+232|0,g=c[d+216>>2]!=c[d+220>>2],J=1;A:{for(;;){W=(p=W)+1|0;o:{b:{u:{if(-1!=(0|(u=c[d+404>>2]))){if(r=-1,l=c[d+428>>2]+(u<<2)|0,u=(h=c[l>>2])-1|0,c[l>>2]=u,(0|h)<=0)break a;if((u=c[c[c[d+416>>2]+y(c[d+404>>2],12)>>2]+(u<<2)>>2])>>>0>4)break a;if(l=c[8896+(u<<2)>>2],c[d+400>>2]=l,!u){if((0|i)==(0|t))break a;if(l=-1,h=c[d+8>>2],J=c[h+24>>2],u=-1,-1!=(0|(a=c[(D=t-4|0)>>2]))&&(u=-1,-1!=(0|(T=((T=a+1|0)>>>0)%3|0?T:a-2|0))&&(u=c[c[h>>2]+(T<<2)>>2])),-1!=(0|(R=c[J+(u<<2)>>2]))&&(l=((l=R+1|0)>>>0)%3|0?l:R-2|0),-1!=(0|a)&-1!=c[c[h+12>>2]+(a<<2)>>2]|(0|a)==(0|l))break a;if(R=c[h+12>>2],-1!=(0|l)&-1!=c[R+(l<<2)>>2])break a;p=(T=y(p,3))+1|0,c[R+(a<<2)>>2]=p,c[(E=p<<2)+R>>2]=a,U=T+2|0,c[R+(l<<2)>>2]=U,c[(G=U<<2)+R>>2]=l,R=-1,p=-1;c:if(-1!=(0|a)){if((a>>>0)%3|0)a=a-1|0;else if(p=-1,-1==(0|(a=a+2|0)))break c;p=c[c[h>>2]+(a<<2)>>2]}if(a=p,-1!=(0|l)&&-1!=(0|(p=((p=l+1|0)>>>0)%3|0?p:l-2|0))&&(R=c[c[h>>2]+(p<<2)>>2]),(0|a)==(0|u)|(0|u)==(0|R))break a;r=c[h>>2],c[r+(T<<2)>>2]=u,c[r+E>>2]=R,c[r+G>>2]=a,-1!=(0|a)&&(c[J+(a<<2)>>2]=U),r=c[d+120>>2]+(u>>>3&536870908)|0,a=c[r>>2],C=r,z=Yi(u)&a,c[C>>2]=z,c[D>>2]=T,a=i,lr(M,T);break o}c:switch(l-1|0){case 2:case 4:if((0|i)==(0|t))break a;if(a=c[(U=t-4|0)>>2],l=c[d+8>>2],h=c[l+12>>2],-1!=(0|a)&-1!=c[h+(a<<2)>>2])break a;if(t=y(p,3),c[(D=(u=t+((R=3==(0|u))?2:1)|0)<<2)+h>>2]=a,c[h+(a<<2)>>2]=u,be(l+24|0,8324),T=c[d+8>>2],h=c[T+24>>2],c[T+28>>2]-h>>2>(0|B))break a;if(G=(r=c[T>>2])+D|0,D=((T=c[l+28>>2])-(l=c[l+24>>2])>>2)-1|0,c[G>>2]=D,(0|l)!=(0|T)&&(c[h+(D<<2)>>2]=u),u=R?t:t+2|0,G=r+(t+R<<2)|0,-1!=(0|a)){k:{_:{if((a>>>0)%3|0)l=a-1|0;else if(-1==(0|(l=a+2|0)))break _;if(l=c[r+(l<<2)>>2],c[r+(u<<2)>>2]=l,-1==(0|l))break k;c[h+(l<<2)>>2]=u;break k}c[r+(u<<2)>>2]=-1}a=((l=a+1|0)>>>0)%3|0?l:a-2|0,l=-1,-1!=(0|a)&&(l=c[r+(a<<2)>>2])}else c[r+(u<<2)>>2]=-1,l=-1;c[G>>2]=l,c[U>>2]=t,a=i;break b;case 6:break u;case 0:break c;default:break a}if((0|a)==(0|t))break a;h=c[(b=t-4|0)>>2],c[v+68>>2]=b;c:if((R=c[v+44>>2])&&(u=c[v+40>>2],r=p&R+2147483647,(T=Ii(R)>>>0>1)&&((r=p)>>>0<R>>>0||(r=(p>>>0)%(R>>>0)|0)),(r=c[u+((l=r)<<2)>>2])&&(r=c[r>>2]))){k:{if(!T){for(u=R-1|0;;){_:{if((0|(R=c[r+4>>2]))!=(0|p)){if((0|l)==(u&R))break _;break c}if((0|p)==c[r+8>>2])break k}if(!(r=c[r>>2]))break}break c}for(;;){_:{if((0|(u=c[r+4>>2]))!=(0|p)){if(u>>>0>=R>>>0&&(u=(u>>>0)%(R>>>0)|0),(0|u)==(0|l))break _;break c}if((0|p)==c[r+8>>2])break k}if(!(r=c[r>>2]))break}break c}if((0|b)==(0|m)){if((f=1+(t=(i=m-a|0)>>2)|0)>>>0>=1073741824)break r;if(b=i>>>1|0,f=i>>>0>=2147483644?1073741823:f>>>0<b>>>0?b:f){if(f>>>0>=1073741824)break t;b=vi(f<<2)}else b=0;if(c[(i=b+(t<<2)|0)>>2]=c[r+12>>2],f=b+(f<<2)|0,b=i+4|0,(0|a)!=(0|m))for(;m=m-4|0,c[(i=i-4|0)>>2]=c[m>>2],(0|a)!=(0|m););c[v+72>>2]=f,c[v+68>>2]=b,c[v+64>>2]=i,a&&er(a),a=i,m=f}else c[b>>2]=c[r+12>>2],c[v+68>>2]=t,b=t}if((0|a)==(0|b))break A;if((0|(r=c[(E=b-4|0)>>2]))==(0|h))break A;if(l=-1==(0|r),u=c[d+8>>2],!l&-1!=c[c[u+12>>2]+(r<<2)>>2])break A;if(R=c[u+12>>2],-1!=(0|h)&-1!=c[R+(h<<2)>>2])break A;D=(T=y(p,3))+2|0,c[R+(r<<2)>>2]=D,c[(t=D<<2)+R>>2]=r,p=T+1|0,c[R+(h<<2)>>2]=p,c[(G=p<<2)+R>>2]=h;c:{k:{_:{if(!l){if((r>>>0)%3|0){p=r-1|0;break _}if(-1!=(0|(p=r+2|0)))break _;l=c[u>>2],p=-1;break k}p=-1,l=c[u>>2],c[l+(T<<2)>>2]=-1,U=-1;break c}l=c[u>>2],p=c[l+(p<<2)>>2]}c[(T<<2)+l>>2]=p,r=((U=r+1|0)>>>0)%3|0?U:r-2|0,U=-1,-1!=(0|r)&&(U=c[(r<<2)+l>>2])}if(c[l+G>>2]=U,-1!=(0|h)){c:{k:{if((h>>>0)%3|0)r=h-1|0;else if(-1==(0|(r=h+2|0)))break k;if(r=c[(r<<2)+l>>2],c[t+l>>2]=r,-1==(0|r))break c;c[c[u+24>>2]+(r<<2)>>2]=D;break c}c[t+l>>2]=-1}U=-1,t=-1,-1!=(0|(r=((r=h+1|0)>>>0)%3|0?r:h-2|0))&&(U=c[(r<<2)+l>>2],t=r)}else c[t+l>>2]=-1,U=-1,t=-1;for(h=(r=c[d+388>>2])+(D=p<<2)|0,G=r,r=U<<2,c[h>>2]=c[h>>2]+c[G+r>>2],h=(G=r)+(r=c[u+24>>2])|0,-1!=(0|p)&&(c[r+D>>2]=c[h>>2]),r=t;;){if(-1!=(0|r)){if(c[(r<<2)+l>>2]=p,u=-1,-1!=(0|(r=((D=r+1|0)>>>0)%3|0?D:r-2|0))&&(u=-1,-1!=(0|(r=c[R+(r<<2)>>2]))&&(u=((u=r+1|0)>>>0)%3|0?u:r-2|0)),(0|t)!=(0|(r=u)))continue;break A}break}c[h>>2]=-1;c:{if(!g)if((0|I)==(0|Y)){if((t=1+(u=(r=I-N|0)>>2)|0)>>>0>=1073741824)break c;if(p=r>>>1|0,p=r>>>0>=2147483644?1073741823:t>>>0<p>>>0?p:t){if(p>>>0>=1073741824)break t;t=vi(p<<2)}else t=0;if(c[(r=t+(u<<2)|0)>>2]=U,Y=r+4|0,(0|N)!=(0|I))for(;I=I-4|0,c[(r=r-4|0)>>2]=c[I>>2],(0|N)!=(0|I););I=t+(p<<2)|0,c[v+32>>2]=I,c[v+28>>2]=Y,c[v+24>>2]=r,N&&er(N),N=r}else c[Y>>2]=U,Y=Y+4|0,c[v+28>>2]=Y;c[E>>2]=T,t=b,lr(M,T);break o}break r}c[d+400>>2]=7}if(be((u=c[d+8>>2])+24|0,8324),r=-1,m=c[d+8>>2],b=y(p,3),T=(u=(R=(l=c[u+28>>2])-(h=c[u+24>>2])|0)>>2)-1|0,c[c[m>>2]+(b<<2)>>2]=T,be(m+24|0,8324),U=b+1|0,c[c[m>>2]+(U<<2)>>2]=(c[m+28>>2]-c[m+24>>2]>>2)-1,be((m=c[d+8>>2])+24|0,8324),D=b+2|0,c[c[m>>2]+(D<<2)>>2]=(c[m+28>>2]-c[m+24>>2]>>2)-1,E=c[d+8>>2],m=c[E+24>>2],c[E+28>>2]-m>>2>(0|B))break a;if(((0|l)!=(0|h)&&(c[m+(T<<2)>>2]=b,r=0,-4==(0|R))||(c[m+(u<<2)>>2]=U,-1!=(0|(r=u+1|0))))&&(c[m+(r<<2)>>2]=D),(0|f)==(0|t)){if((a=1+(m=(r=f-i|0)>>2)|0)>>>0>=1073741824)break r;if(t=r>>>1|0,r=r>>>0>=2147483644?1073741823:a>>>0<t>>>0?t:a){if(r>>>0>=1073741824)break t;t=vi(r<<2)}else t=0;if(c[(a=t+(m<<2)|0)>>2]=b,m=t+(r<<2)|0,b=a+4|0,(0|i)!=(0|f))for(;f=f-4|0,c[(a=a-4|0)>>2]=c[f>>2],(0|i)!=(0|f););c[v+72>>2]=m,c[v+68>>2]=b,c[v+64>>2]=a,i&&er(i),f=m,i=a}else c[t>>2]=b,b=t+4|0,c[v+68>>2]=b,m=f}lr(M,c[b-4>>2]);b:if((0|(r=c[d+40>>2]))!=c[d+36>>2]){if((u=c[4+(t=r-12|0)>>2])>>>0>(p=V+(-1^p)|0)>>>0)break A;if((0|u)==(0|p)){if(l=k[r-4|0],u=c[t>>2],c[d+40>>2]=t,(0|u)<0)break A;r=c[(h=b-4|0)>>2],c[v+20>>2]=V+(-1^u),t=v+20|0,c[v+88>>2]=t,tr(v,v+40|0,t,v+88|0),u=c[v>>2];u:if(1&l){if(t=-1,-1==(0|r))break u;t=((t=r+1|0)>>>0)%3|0?t:r-2|0}else t=-1,-1!=(0|r)&&(t=r-1|0,(r>>>0)%3|0||(t=r+2|0));if(c[u+12>>2]=t,(0|(r=c[d+40>>2]))!=c[d+36>>2])for(;;){if((u=c[4+(t=r-12|0)>>2])>>>0>p>>>0)break A;if((0|u)!=(0|p))break b;if(l=k[r-4|0],u=c[t>>2],c[d+40>>2]=t,(0|u)<0)break A;r=c[h>>2],c[v+20>>2]=V+(-1^u),t=v+20|0,c[v+88>>2]=t,tr(v,v+40|0,t,v+88|0),u=c[v>>2];u:if(1&l){if(t=-1,-1==(0|r))break u;t=((t=r+1|0)>>>0)%3|0?t:r-2|0}else t=-1,-1!=(0|r)&&(t=r-1|0,(r>>>0)%3|0||(t=r+2|0));if(c[u+12>>2]=t,(0|(r=c[d+40>>2]))==c[d+36>>2])break}}}t=b}if(J=(0|V)>(0|W),(0|V)==(0|W))break}W=V;break n}if(r=-1,J)break a}if(r=-1,t=c[d+8>>2],!(c[t+28>>2]-c[t+24>>2]>>2>(0|B))){if((0|i)!=(0|b)){for(h=d+72|0,m=d+60|0,R=d+312|0;;){if(V=c[(b=b-4|0)>>2],c[v+68>>2]=b,Se(R)){if(u=c[d+8>>2],N=c[u>>2],((c[u+4>>2]-N>>2>>>0)/3|0)<=(0|W)){r=-1;break a}if(i=-1,l=c[u+24>>2],r=-1,-1!=(0|V)&&(r=-1,-1!=(0|(a=((a=V+1|0)>>>0)%3|0?a:V-2|0))&&(r=c[N+(a<<2)>>2])),-1!=(0|(r=c[l+((a=r)<<2)>>2]))?(p=1,t=-1,-1!=(0|(r=((f=r+1|0)>>>0)%3|0?f:r-2|0))&&(p=0,-1!=(0|(i=((i=r+1|0)>>>0)%3|0?i:r-2|0))&&(t=c[N+(i<<2)>>2]),i=r)):(p=1,t=-1),r=-1,f=-1,-1!=(0|(l=c[l+(t<<2)>>2]))&&(f=((f=l+1|0)>>>0)%3|0?f:l-2|0),(0|i)==(0|V)|(0|f)==(0|V)|-1!=(0|V)&-1!=c[c[u+12>>2]+(V<<2)>>2]|(0|i)==(0|f))break a;if(!p&-1!=c[c[u+12>>2]+(i<<2)>>2])break a;if(p=-1,u=c[u+12>>2],l=-1,-1!=(0|f)){if(-1!=c[u+(f<<2)>>2])break a;l=-1,-1!=(0|(r=((r=f+1|0)>>>0)%3|0?r:f-2|0))&&(l=c[N+(r<<2)>>2])}r=y(W,3),c[v>>2]=r,c[u+(r<<2)>>2]=V,c[u+(V<<2)>>2]=r,r=c[v>>2]+1|0,c[u+(r<<2)>>2]=i,c[u+(i<<2)>>2]=r,r=c[v>>2]+2|0,c[u+(r<<2)>>2]=f,c[u+(f<<2)>>2]=r,r=c[v>>2],c[N+(r<<2)>>2]=t,c[(f=N+((i=r+1|0)<<2)|0)>>2]=l,c[(N=N+((V=r+2|0)<<2)|0)>>2]=a,r=c[d+120>>2],u=c[(t=r+((a=i?t:-1)>>>3&536870908)|0)>>2],C=t,z=Yi(a)&u,c[C>>2]=z,p=-1!=(0|i)?c[f>>2]:p,f=c[(i=r+(p>>>3&536870908)|0)>>2],C=i,z=Yi(p)&f,c[C>>2]=z,f=-1,f=-1!=(0|V)?c[N>>2]:f,i=c[(r=r+(f>>>3&536870908)|0)>>2],C=r,z=Yi(f)&i,c[C>>2]=z,n[v+88|0]=1,Ve(m,v+88|0),be(h,v),W=W+1|0,i=c[v+64>>2]}else{if((0|(f=c[d+64>>2]))==(r=c[d+68>>2])<<5){if((f+1|0)<0)break r;nr(m,r=f>>>0<=1073741822?(r<<=6)>>>0>(f=32+(-32&f)|0)>>>0?r:f:2147483647),f=c[d+64>>2]}if(c[d+64>>2]=f+1,r=c[d+60>>2]+(f>>>3&536870908)|0,a=c[r>>2],C=r,z=Yi(f)&a,c[C>>2]=z,(0|(f=c[d+76>>2]))==c[d+80>>2]){if((a=1+(N=(r=f-(t=c[h>>2])|0)>>2)|0)>>>0>=1073741824)break r;if(p=r>>>1|0,p=r>>>0>=2147483644?1073741823:a>>>0<p>>>0?p:a){if(p>>>0>=1073741824)break t;r=vi(p<<2)}else r=0;if(c[(a=r+(N<<2)|0)>>2]=V,V=a+4|0,(0|f)!=(0|t))for(;f=f-4|0,c[(a=a-4|0)>>2]=c[f>>2],(0|f)!=(0|t););c[d+80>>2]=r+(p<<2),c[d+76>>2]=V,c[d+72>>2]=a,t&&er(t)}else c[f>>2]=V,c[d+76>>2]=f+4}if((0|i)==(0|b))break}t=c[d+8>>2]}if(r=-1,((c[t+4>>2]-c[t>>2]>>2>>>0)/3|0)==(0|W)){if(r=c[t+28>>2]-c[t+24>>2]>>2,(0|(b=c[v+24>>2]))==(0|(p=c[v+28>>2])))break f;for(;;){if(i=c[b>>2],m=c[t+24>>2],-1==c[(a=m+((f=r-1|0)<<2)|0)>>2])for(;f=r-2|0,r=r-1|0,-1==c[(a=m+(f<<2)|0)>>2];);if(i>>>0<=f>>>0){if(c[v>>2]=t,a=c[a>>2],n[v+12|0]=1,c[v+8>>2]=a,c[v+4>>2]=a,-1!=(0|a)){for(;;){if(a=c[c[d+8>>2]>>2]+(a<<2)|0,c[a>>2]!=(0|f)){r=-1;break a}if(c[a>>2]=i,Mr(v),-1==(0|(a=c[v+8>>2])))break}t=c[d+8>>2]}a=(m=c[t+24>>2])+(f<<2)|0,-1!=(0|i)&&(c[m+(i<<2)>>2]=c[a>>2]),c[a>>2]=-1,a=1<<i,i=(m=c[d+120>>2])+(i>>>3&536870908)|0,m=m+(f>>>3&536870908)|0,f=1<<f,c[m>>2]&f?a|=c[i>>2]:a=c[i>>2]&(-1^a),c[i>>2]=a,c[m>>2]=c[m>>2]&(-1^f),r=r-1|0}if((0|p)==(0|(b=b+4|0)))break}}}}b=c[v+24>>2]}if(b&&er(b),i=c[v+48>>2])for(;f=c[i>>2],er(i),i=f;);i=c[v+40>>2],c[v+40>>2]=0,i&&er(i),(i=c[v+64>>2])&&(c[v+68>>2]=i,er(i)),Z=v+96|0;break i}Zi(),o()}if(b=r,-1!=(0|r)){f=(i=c[(r=Q)+16>>2])+c[r>>2]|0,i=c[r+8>>2]-i|0,r=c[c[d+4>>2]+32>>2],A[r+38>>1]=_[r+38>>1],c[r>>2]=f,c[r+16>>2]=0,c[r+20>>2]=0,c[r+8>>2]=i,c[r+12>>2]=0;i:if(c[d+216>>2]!=c[d+220>>2]&&(r=c[d+8>>2],c[r+4>>2]!=c[r>>2])){for(i=0;;){if(cr(d,i)){if(i=i+3|0,r=c[d+8>>2],i>>>0<c[r+4>>2]-c[r>>2]>>2>>>0)continue;break i}break}break e}if(k[d+308|0]&&(n[d+308|0]=0,f=c[d+292>>2],r=0,i=(a=(r=(a=c[d+304>>2]+7|0)>>>0<7?1:r)<<29|a>>>3)+c[d+288>>2]|0,r=(r>>>3|0)+f|0,c[d+288>>2]=i,c[d+292>>2]=i>>>0<a>>>0?r+1|0:r),(0|(i=c[d+216>>2]))!=c[d+220>>2])for(r=0;;){if(Vr(4+((a=y(r,144))+i|0)|0,c[d+8>>2]),f=c[w>>2],(0|(i=c[132+(t=f+a|0)>>2]))!=(0|(t=c[t+136>>2]))){for(;Yr(4+(a+c[w>>2]|0)|0,c[i>>2]),(0|t)!=(0|(i=i+4|0)););f=c[w>>2]}if(!q(4+(f+a|0)|0))break e;if(r=r+1|0,i=c[d+216>>2],!(r>>>0<(c[d+220>>2]-i|0)/144>>>0))break}if(r=c[d+8>>2],ce(d+184|0,c[r+28>>2]-c[r+24>>2]>>2),(0|(f=c[d+216>>2]))!=c[d+220>>2])for(i=0;r=y(i,144)+f|0,f=c[r+60>>2]-c[r+56>>2]>>2,t=r+104|0,r=c[d+8>>2],ce(t,(0|(r=c[r+28>>2]-c[r+24>>2]>>2))<(0|f)?f:r),i=i+1|0,f=c[d+216>>2],i>>>0<(c[d+220>>2]-f|0)/144>>>0;);O=j(d,b)}}}return Z=e- -64|0,0|O}mt(),o()},ct,tt,rt,_i,function(r){var e=0;c[(r|=0)>>2]=8624,(e=c[r+48>>2])&&(c[r+52>>2]=e,er(e)),c[r>>2]=8876,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),er(r)},Te,function(r){var e=0;return c[(r|=0)>>2]=8876,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),0|r},function(r){var e=0;c[(r|=0)>>2]=8876,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),er(r)},function(r){var e=0;return c[8+(r|=0)>>2]=9136,c[r>>2]=8924,(e=c[r+96>>2])&&(c[r+100>>2]=e,er(e)),(e=c[r+80>>2])&&(c[r+84>>2]=e,er(e)),(e=c[r+68>>2])&&(c[r+72>>2]=e,er(e)),(e=c[r+56>>2])&&(c[r+60>>2]=e,er(e)),c[r+8>>2]=9372,(e=c[r+44>>2])&&er(e),(e=c[r+32>>2])&&er(e),0|r},function(r){var e=0;c[8+(r|=0)>>2]=9136,c[r>>2]=8924,(e=c[r+96>>2])&&(c[r+100>>2]=e,er(e)),(e=c[r+80>>2])&&(c[r+84>>2]=e,er(e)),(e=c[r+68>>2])&&(c[r+72>>2]=e,er(e)),(e=c[r+56>>2])&&(c[r+60>>2]=e,er(e)),c[r+8>>2]=9372,(e=c[r+44>>2])&&er(e),(e=c[r+32>>2])&&er(e),er(r)},function(r,e){e|=0;var i,t=0,f=0,a=0,A=0,o=0,b=0,u=0,k=0,_=0,s=0,p=0,l=0;t=c[12+(r|=0)>>2],f=c[r+108>>2],a=c[f+80>>2],n[e+84|0]=0,i=e+68|0,b=c[e+68>>2],(A=c[e+72>>2]-b>>2)>>>0<a>>>0?(yr(i,a-A|0,9124),f=c[r+108>>2],a=c[f+80>>2]):a>>>0>=A>>>0||(c[e+72>>2]=b+(a<<2)),b=((e=c[f+100>>2])-(A=c[f+96>>2])|0)/12|0,_=1;r:if((0|e)!=(0|A))for(p=b>>>0<=1?1:b,l=c[t>>2],t=0,f=A,e=0,_=0;;){if(-1==(0|(u=c[(t=(t<<2)+l|0)>>2])))break r;if((o=c[f>>2])>>>0>=a>>>0)break r;if(s=c[c[r+112>>2]+12>>2],(k=c[s+(u<<2)>>2])>>>0>=a>>>0)break r;if(u=c[i>>2],c[u+(o<<2)>>2]=k,-1==(0|(o=c[t+4>>2])))break r;if((k=c[f+4>>2])>>>0>=a>>>0)break r;if((o=c[(o<<2)+s>>2])>>>0>=a>>>0)break r;if(c[u+(k<<2)>>2]=o,-1==(0|(t=c[t+8>>2])))break r;if((f=c[f+8>>2])>>>0>=a>>>0)break r;if((t=c[(t<<2)+s>>2])>>>0>=a>>>0)break r;if(c[u+(f<<2)>>2]=t,_=b>>>0<=(e=e+1|0)>>>0,(0|e)==(0|p))break r;if(t=y(e,3),f=A+y(e,12)|0,1431655765==(0|e))break}return 0|_},function(r){var e,i=0,t=0,f=0,a=0,n=0,A=0;Z=e=Z-16|0,a=c[4+(r|=0)>>2],f=c[a>>2];r:{if(i=c[r+12>>2],!((n=(t=c[i+28>>2]-c[i+24>>2]|0)>>2)>>>0<=c[a+8>>2]-f>>2>>>0)){if((0|t)<0)break r;if(i=c[a+4>>2],n=(t=vi(t))+(n<<2)|0,t=A=t+(i-f&-4)|0,(0|i)!=(0|f))for(;i=i-4|0,c[(t=t-4|0)>>2]=c[i>>2],(0|i)!=(0|f););c[a+8>>2]=n,c[a+4>>2]=A,c[a>>2]=t,f&&er(f)}i=c[r+12>>2],t=c[i+28>>2],i=c[i+24>>2],c[e+12>>2]=0,i=t-i>>2,a=c[(f=r+96|0)>>2],i>>>0>(t=c[r+100>>2]-a>>2)>>>0?Nr(f,i-t|0,e+12|0):i>>>0>=t>>>0||(c[r+100>>2]=a+(i<<2)),a=r+8|0;e:if(i=c[r+116>>2]){if((0|(f=c[i>>2]))==c[i+4>>2]){t=1;break e}for(i=0;;){if(!(t=C(a,c[(i<<2)+f>>2])))break e;if(n=c[r+116>>2],f=c[n>>2],!((i=i+1|0)>>>0<c[n+4>>2]-f>>2>>>0))break}}else if(t=1,r=c[r+12>>2],!((r=c[r+4>>2]-c[r>>2]|0)>>>0<12))for(r=(r>>2>>>0)/3|0,i=0;;){if(!(t=C(a,y(i,3))))break e;if((0|r)==(0|(i=i+1|0)))break}return Z=e+16|0,0|t}mt(),o()},function(r){var e=0;return c[(r|=0)>>2]=9136,(e=c[r+88>>2])&&(c[r+92>>2]=e,er(e)),(e=c[r+72>>2])&&(c[r+76>>2]=e,er(e)),(e=c[r+60>>2])&&(c[r- -64>>2]=e,er(e)),(e=c[r+48>>2])&&(c[r+52>>2]=e,er(e)),c[r>>2]=9372,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),0|r},function(r){var e=0;c[(r|=0)>>2]=9136,(e=c[r+88>>2])&&(c[r+92>>2]=e,er(e)),(e=c[r+72>>2])&&(c[r+76>>2]=e,er(e)),(e=c[r+60>>2])&&(c[r- -64>>2]=e,er(e)),(e=c[r+48>>2])&&(c[r+52>>2]=e,er(e)),c[r>>2]=9372,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),er(r)},function(r,e,i){e|=0,i|=0;var t,f=0;Z=t=Z-16|0,c[4+(r|=0)>>2]=e,f=c[e>>2],e=c[e+4>>2],n[t+15|0]=0,xr(r+24|0,(e-f>>2>>>0)/3|0,t+15|0),e=c[r+4>>2],f=c[e+28>>2],e=c[e+24>>2],n[t+14|0]=0,xr(r+36|0,f-e>>2,t+14|0),e=c[i+12>>2],c[r+16>>2]=c[i+8>>2],c[r+20>>2]=e,e=c[i+4>>2],c[r+8>>2]=c[i>>2],c[r+12>>2]=e,Z=t+16|0},function(r){var e=0;return c[(r|=0)>>2]=9372,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),0|r},function(r){var e=0;c[(r|=0)>>2]=9372,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),er(r)},function(r){var e=0;return c[8+(r|=0)>>2]=9556,c[r>>2]=9392,(e=c[r+56>>2])&&(c[r+60>>2]=e,er(e)),c[r+8>>2]=9372,(e=c[r+44>>2])&&er(e),(e=c[r+32>>2])&&er(e),0|r},function(r){var e=0;c[8+(r|=0)>>2]=9556,c[r>>2]=9392,(e=c[r+56>>2])&&(c[r+60>>2]=e,er(e)),c[r+8>>2]=9372,(e=c[r+44>>2])&&er(e),(e=c[r+32>>2])&&er(e),er(r)},function(r,e){e|=0;var i,t=0,f=0,a=0,A=0,o=0,b=0,u=0,k=0,_=0,s=0,p=0,l=0;t=c[12+(r|=0)>>2],f=c[r+68>>2],a=c[f+80>>2],n[e+84|0]=0,i=e+68|0,b=c[e+68>>2],(A=c[e+72>>2]-b>>2)>>>0<a>>>0?(yr(i,a-A|0,9124),f=c[r+68>>2],a=c[f+80>>2]):a>>>0>=A>>>0||(c[e+72>>2]=b+(a<<2)),b=((e=c[f+100>>2])-(A=c[f+96>>2])|0)/12|0,_=1;r:if((0|e)!=(0|A))for(p=b>>>0<=1?1:b,l=c[t>>2],t=0,f=A,e=0,_=0;;){if(-1==(0|(u=c[(t=(t<<2)+l|0)>>2])))break r;if((o=c[f>>2])>>>0>=a>>>0)break r;if(s=c[c[r+72>>2]+12>>2],(k=c[s+(u<<2)>>2])>>>0>=a>>>0)break r;if(u=c[i>>2],c[u+(o<<2)>>2]=k,-1==(0|(o=c[t+4>>2])))break r;if((k=c[f+4>>2])>>>0>=a>>>0)break r;if((o=c[(o<<2)+s>>2])>>>0>=a>>>0)break r;if(c[u+(k<<2)>>2]=o,-1==(0|(t=c[t+8>>2])))break r;if((f=c[f+8>>2])>>>0>=a>>>0)break r;if((t=c[(t<<2)+s>>2])>>>0>=a>>>0)break r;if(c[u+(f<<2)>>2]=t,_=b>>>0<=(e=e+1|0)>>>0,(0|e)==(0|p))break r;if(t=y(e,3),f=A+y(e,12)|0,1431655765==(0|e))break}return 0|_},function(r){var e=0,i=0,t=0,f=0,a=0,n=0;f=c[4+(r|=0)>>2],t=c[f>>2];r:{if(e=c[r+12>>2],!((a=(i=c[e+28>>2]-c[e+24>>2]|0)>>2)>>>0<=c[f+8>>2]-t>>2>>>0)){if((0|i)<0)break r;if(e=c[f+4>>2],a=(i=vi(i))+(a<<2)|0,i=n=i+(e-t&-4)|0,(0|e)!=(0|t))for(;e=e-4|0,c[(i=i-4|0)>>2]=c[e>>2],(0|e)!=(0|t););c[f+8>>2]=a,c[f+4>>2]=n,c[f>>2]=i,t&&er(t)}f=r+8|0;e:if(e=c[r+76>>2]){if((0|(t=c[e>>2]))==c[e+4>>2])return 1;for(e=0;;){if(!(i=ar(f,c[(e<<2)+t>>2])))break e;if(a=c[r+76>>2],t=c[a>>2],!((e=e+1|0)>>>0<c[a+4>>2]-t>>2>>>0))break}}else if(i=1,r=c[r+12>>2],!((r=c[r+4>>2]-c[r>>2]|0)>>>0<12))for(r=(r>>2>>>0)/3|0,e=0;;){if(!(i=ar(f,y(e,3))))break e;if((0|r)==(0|(e=e+1|0)))break}return 0|i}mt(),o()},function(r){var e=0;return c[(r|=0)>>2]=9556,(e=c[r+48>>2])&&(c[r+52>>2]=e,er(e)),c[r>>2]=9372,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),0|r},function(r){var e=0;c[(r|=0)>>2]=9556,(e=c[r+48>>2])&&(c[r+52>>2]=e,er(e)),c[r>>2]=9372,(e=c[r+36>>2])&&er(e),(e=c[r+24>>2])&&er(e),er(r)},function(r){var e=0;return c[8+(r|=0)>>2]=8624,c[r>>2]=9684,(e=c[r+56>>2])&&(c[r+60>>2]=e,er(e)),c[r+8>>2]=8876,(e=c[r+44>>2])&&er(e),(e=c[r+32>>2])&&er(e),0|r},function(r){var e=0;c[8+(r|=0)>>2]=8624,c[r>>2]=9684,(e=c[r+56>>2])&&(c[r+60>>2]=e,er(e)),c[r+8>>2]=8876,(e=c[r+44>>2])&&er(e),(e=c[r+32>>2])&&er(e),er(r)},function(r,e){e|=0;var i,t=0,f=0,a=0,A=0,o=0,b=0,u=0,k=0,_=0,s=0,p=0,l=0;_=c[12+(r|=0)>>2],t=c[r+68>>2],f=c[t+80>>2],n[e+84|0]=0,i=e+68|0,u=c[e+68>>2],(a=c[e+72>>2]-u>>2)>>>0<f>>>0?(yr(i,f-a|0,9124),t=c[r+68>>2],f=c[t+80>>2]):f>>>0>=a>>>0||(c[e+72>>2]=u+(f<<2)),u=((e=c[t+100>>2])-(a=c[t+96>>2])|0)/12|0,p=1;r:if((0|e)!=(0|a)){if(_=c[_+28>>2],-1==(0|(A=c[_>>2])))return 0;for(l=u>>>0<=1?1:u,t=a,e=0,p=0;;){if((o=c[t>>2])>>>0>=f>>>0)break r;if(k=c[c[r+72>>2]+12>>2],(b=c[k+(A<<2)>>2])>>>0>=f>>>0)break r;if(A=c[i>>2],c[A+(o<<2)>>2]=b,-1==(0|(b=c[4+(o=_+(s<<2)|0)>>2])))break r;if((s=c[t+4>>2])>>>0>=f>>>0)break r;if((b=c[(b<<2)+k>>2])>>>0>=f>>>0)break r;if(c[A+(s<<2)>>2]=b,-1==(0|(o=c[o+8>>2])))break r;if((t=c[t+8>>2])>>>0>=f>>>0)break r;if((k=c[(o<<2)+k>>2])>>>0>=f>>>0)break r;if(c[A+(t<<2)>>2]=k,p=u>>>0<=(e=e+1|0)>>>0,(0|e)==(0|l))break r;if(t=a+y(e,12)|0,s=y(e,3),-1==(0|(A=c[_+(s<<2)>>2])))break}}return 0|p},function(r){var e=0,i=0,t=0,f=0,a=0,n=0;f=c[4+(r|=0)>>2],t=c[f>>2];r:{if(e=c[r+12>>2],!((a=(i=c[e+56>>2]-c[e+52>>2]|0)>>2)>>>0<=c[f+8>>2]-t>>2>>>0)){if((0|i)<0)break r;if(e=c[f+4>>2],a=(i=vi(i))+(a<<2)|0,i=n=i+(e-t&-4)|0,(0|e)!=(0|t))for(;e=e-4|0,c[(i=i-4|0)>>2]=c[e>>2],(0|e)!=(0|t););c[f+8>>2]=a,c[f+4>>2]=n,c[f>>2]=i,t&&er(t)}f=r+8|0;e:if(e=c[r+76>>2]){if((0|(t=c[e>>2]))==c[e+4>>2])return 1;for(e=0;;){if(!(i=ir(f,c[(e<<2)+t>>2])))break e;if(a=c[r+76>>2],t=c[a>>2],!((e=e+1|0)>>>0<c[a+4>>2]-t>>2>>>0))break}}else if(i=1,r=c[c[r+12>>2]+64>>2],!((r=c[r+4>>2]-c[r>>2]|0)>>>0<12))for(r=(r>>2>>>0)/3|0,e=0;;){if(!(i=ir(f,y(e,3))))break e;if((0|r)==(0|(e=e+1|0)))break}return 0|i}mt(),o()},function(r){var e,i=0,t=0;if(c[(r|=0)>>2]=10032,(i=c[r+20>>2])&&(c[r+24>>2]=i,er(i)),e=c[r+8>>2]){if((0|(t=c[r+12>>2]))!=(0|(i=e))){for(;i=c[(t=t-4|0)>>2],c[t>>2]=0,i&&Zt[c[c[i>>2]+4>>2]](i),(0|t)!=(0|e););i=c[r+8>>2]}c[r+12>>2]=e,er(i)}er(r)},function(r,e){r|=0,e|=0;var i=0,t=0,f=0,a=0,n=0,A=0;a=vi(64),i=vi(12),c[i+8>>2]=c[c[r+4>>2]+80>>2],c[i>>2]=9968,c[i+4>>2]=0,a=He(a,i);r:{if((0|e)<0)i=a;else{if(A=r+8|0,!((0|(n=(i=c[r+12>>2])-(f=c[r+8>>2])>>2))>(0|e)))if(t=e+1|0,e>>>0>=n>>>0)Or(A,t-n|0);else if(!(t>>>0>=n>>>0)){if((0|(f=f+(t<<2)|0))!=(0|i))for(;t=c[(i=i-4|0)>>2],c[i>>2]=0,t&&Zt[c[c[t>>2]+4>>2]](t),(0|i)!=(0|f););c[r+12>>2]=f}if(r=c[A>>2]+(e<<2)|0,i=c[r>>2],c[r>>2]=a,!i)break r}Zt[c[c[i>>2]+4>>2]](i)}return(-1^e)>>>31|0},function(r){var e,i=0,t=0,f=0,a=0,n=0,A=0,b=0,u=0,s=0,p=0,l=0,d=0;Z=e=Z-32|0;r:if(We(1,e+28|0,c[32+(r|=0)>>2])&&We(1,e+24|0,c[r+32>>2])&&!((p=c[e+28>>2])>>>0>1431655765||(f=c[r+32>>2],n=Ke((b=c[(t=f)+8>>2])-(i=c[t+16>>2])|0,(A=c[t+12>>2])-((t=c[t+20>>2])+(i>>>0>b>>>0)|0)|0,3,0),!E&n>>>0<p>>>0||(d=c[e+24>>2],n=$e(p,0,3,0),!E&n>>>0<d>>>0|(0|t)>=(0|A)&i>>>0>=b>>>0|(0|t)>(0|A))))){b=k[i+c[f>>2]|0],t=(i=i+1|0)?t:t+1|0,c[f+16>>2]=i,c[f+20>>2]=t;e:{i:{if(!b){f=0,Z=t=Z-32|0,c[t+24>>2]=0,c[t+16>>2]=0,c[t+20>>2]=0;t:{f:{if(i=y(p,3)){if(i>>>0>=1073741824)break f;Sr(f=vi(b=y(p,12)),0,b)}a:{n:if(!p|!(i=F(i,1,c[r+32>>2],f))){if(!f)break a}else{for(b=0;;){if(n=a,a=(A=c[(i=(b<<2)+f|0)>>2])>>>1|0,!((0|(A=n+(1&A?0-a|0:a)|0))<0||(c[t>>2]=A,n=(a=c[i+4>>2])>>>1|0,(0|(A=A+(1&a?0-n|0:n)|0))<0||(c[t+4>>2]=A,a=(i=c[i+8>>2])>>>1|0,(0|(a=A+(1&i?0-a|0:a)|0))<0)))){if(c[t+8>>2]=a,Lr(c[r+44>>2]+96|0,t),b=b+3|0,i=1,(0|(u=u+1|0))!=(0|p))continue;break n}break}i=0}er(f)}Z=t+32|0;break t}mt(),o()}if(i)break i;break r}if(d>>>0<=255){if(!p)break i;for(;;){if(c[e+16>>2]=0,c[e+8>>2]=0,c[e+12>>2]=0,f=c[r+32>>2],b=c[(i=f)+16>>2],!((a=c[i+8>>2])>>>0<=b>>>0&(0|(t=c[i+20>>2]))>=(0|(i=n=c[i+12>>2]))|(0|i)<(0|t)||(u=c[f>>2],l=k[u+b|0],i=t,i=(A=b+1|0)?i:i+1|0,c[f+16>>2]=A,c[f+20>>2]=i,c[e+8>>2]=l,(0|(a=(l=a>>>0<b>>>0&(0|t)>=(0|n)|(0|t)>(0|n))?b:a))==(0|A)&(0|(n=l?t:n))==(0|i)||(l=k[A+u|0],i=t,i=(A=b+2|0)>>>0<2?i+1|0:i,c[f+16>>2]=A,c[f+20>>2]=i,c[e+12>>2]=l,(0|a)==(0|A)&(0|i)==(0|n))))){if(A=k[A+u|0],i=t,i=(t=b+3|0)>>>0<3?i+1|0:i,c[f+16>>2]=t,c[f+20>>2]=i,c[e+16>>2]=A,Lr(c[r+44>>2]+96|0,e+8|0),(0|p)!=(0|(s=s+1|0)))continue;break i}break}s=0;break r}if(d>>>0<=65535){if(!p)break i;for(;;){if(c[e+16>>2]=0,c[e+8>>2]=0,c[e+12>>2]=0,u=c[r+32>>2],t=c[(i=u)+8>>2],f=c[i+12>>2],A=c[i+16>>2],b=i=c[i+20>>2],!(t>>>0<(a=A+2|0)>>>0&(0|(i=a>>>0<2?i+1|0:i))>=(0|f)|(0|i)>(0|f)||(l=c[u>>2],n=k[0|(n=l+A|0)]|k[n+1|0]<<8,c[u+16>>2]=a,c[u+20>>2]=i,c[e+8>>2]=n,i=b,t>>>0<(n=A+4|0)>>>0&(0|(i=n>>>0<4?i+1|0:i))>=(0|f)|(0|i)>(0|f)||(a=k[0|(a=a+l|0)]|k[a+1|0]<<8,c[u+16>>2]=n,c[u+20>>2]=i,c[e+12>>2]=a,a=t,i=b,(t=A+6|0)>>>0>a>>>0&(0|(i=t>>>0<6?i+1|0:i))>=(0|f)|(0|i)>(0|f))))){if(f=k[0|(f=n+l|0)]|k[f+1|0]<<8,c[u+16>>2]=t,c[u+20>>2]=i,c[e+16>>2]=f,Lr(c[r+44>>2]+96|0,e+8|0),(0|p)!=(0|(s=s+1|0)))continue;break i}break}s=0;break r}if(!(d>>>0>2097151||(65535&((i=_[r+36>>1])<<8|i>>>8))>>>0<514)){if(!p)break i;for(;;){if(c[e+16>>2]=0,c[e+8>>2]=0,c[e+12>>2]=0,We(1,e+4|0,c[r+32>>2])&&(c[e+8>>2]=c[e+4>>2],We(1,e+4|0,c[r+32>>2])&&(c[e+12>>2]=c[e+4>>2],We(1,e+4|0,c[r+32>>2])))){if(c[e+16>>2]=c[e+4>>2],Lr(c[r+44>>2]+96|0,e+8|0),(0|p)!=(0|(s=s+1|0)))continue;break i}break}s=0;break r}if(p)for(;;){if(c[e+16>>2]=0,c[e+8>>2]=0,c[e+12>>2]=0,u=c[r+32>>2],t=c[(i=u)+8>>2],f=c[i+12>>2],A=c[i+16>>2],b=i=c[i+20>>2],t>>>0<(a=A+4|0)>>>0&(0|(i=a>>>0<4?i+1|0:i))>=(0|f)|(0|i)>(0|f))break e;if(l=c[u>>2],n=k[0|(n=l+A|0)]|k[n+1|0]<<8|k[n+2|0]<<16|k[n+3|0]<<24,c[u+16>>2]=a,c[u+20>>2]=i,c[e+8>>2]=n,i=b,t>>>0<(n=A+8|0)>>>0&(0|(i=n>>>0<8?i+1|0:i))>=(0|f)|(0|i)>(0|f))break e;if(a=k[0|(a=a+l|0)]|k[a+1|0]<<8|k[a+2|0]<<16|k[a+3|0]<<24,c[u+16>>2]=n,c[u+20>>2]=i,c[e+12>>2]=a,a=t,i=b,(t=A+12|0)>>>0>a>>>0&(0|(i=t>>>0<12?i+1|0:i))>=(0|f)|(0|i)>(0|f))break e;if(f=k[0|(f=n+l|0)]|k[f+1|0]<<8|k[f+2|0]<<16|k[f+3|0]<<24,c[u+16>>2]=t,c[u+20>>2]=i,c[e+16>>2]=f,Lr(c[r+44>>2]+96|0,e+8|0),(0|p)==(0|(s=s+1|0)))break}}c[c[r+4>>2]+80>>2]=d,s=1;break r}s=0}return Z=e+32|0,0|s},ut,vt,function(r,e){return n[84+(e|=0)|0]=1,c[e+72>>2]=c[e+68>>2],1},function(r){var e=0,i=0,t=0,f=0,a=0;r:{if(!((0|(e=c[8+(r|=0)>>2]))<0||(i=c[r+4>>2],f=c[i>>2],(t=c[i+4>>2]-f>>2)>>>0<e>>>0?(ae(i,e-t|0),a=c[r+8>>2]):(a=e,e>>>0>=t>>>0||(c[i+4>>2]=f+(e<<2),a=e)),(0|(t=a))<=0)))for(r=c[r+4>>2],i=c[r>>2],f=c[r+4>>2]-i>>2,r=0;;){if((0|r)==(0|f))break r;if(c[i+(r<<2)>>2]=r,(0|t)==(0|(r=r+1|0)))break}return(-1^e)>>>31|0}dt(),o()},ht,pt,ct,function(r){var e=0;return c[(r|=0)>>2]=10240,(e=c[r+96>>2])&&(c[r+100>>2]=e,er(e)),(e=c[r+84>>2])&&(c[r+88>>2]=e,er(e)),0|ke(r)},function(r){var e=0;c[(r|=0)>>2]=10240,(e=c[r+96>>2])&&(c[r+100>>2]=e,er(e)),(e=c[r+84>>2])&&(c[r+88>>2]=e,er(e)),er(ke(r))},function(r,e,i){var t=0,f=0,a=0,n=0,A=0,b=0,u=0;if(ur(r|=0,e|=0,i|=0),i=c[r+84>>2],!((0|(t=c[r+88>>2]-i>>2))>(0|e))){if((e=e+1|0)>>>0>t>>>0){r:if((t=e-t|0)>>>0<=(f=c[r+92>>2])-(i=c[r+88>>2])>>2>>>0){if(t){if(e=i,f=7&t)for(;c[e>>2]=1,e=e+4|0,(0|f)!=(0|(a=a+1|0)););if(i=(t<<2)+i|0,!((t-1&1073741823)>>>0<7))for(;c[e+24>>2]=1,c[e+28>>2]=1,c[e+16>>2]=1,c[e+20>>2]=1,c[e+8>>2]=1,c[e+12>>2]=1,c[e>>2]=1,c[e+4>>2]=1,(0|i)!=(0|(e=e+32|0)););}c[r+88>>2]=i}else{e:{if((e=(n=(b=(e=i)-(i=c[r+84>>2])|0)>>2)+t|0)>>>0<1073741824){if(A=(f=f-i|0)>>>1|0,f=f>>>0>=2147483644?1073741823:e>>>0<A>>>0?A:e){if(f>>>0>=1073741824)break e;u=vi(f<<2)}if(e=n=(n<<2)+u|0,A=7&t)for(;c[e>>2]=1,e=e+4|0,(0|A)!=(0|(a=a+1|0)););if(a=n+(t<<2)|0,(t-1&1073741823)>>>0>=7)for(;c[e+24>>2]=1,c[e+28>>2]=1,c[e+16>>2]=1,c[e+20>>2]=1,c[e+8>>2]=1,c[e+12>>2]=1,c[e>>2]=1,c[e+4>>2]=1,(0|a)!=(0|(e=e+32|0)););e=gr(u,i,b),c[r+88>>2]=a,c[r+84>>2]=e,c[r+92>>2]=e+(f<<2),i&&er(i);break r}mt(),o()}Zi(),o()}return}e>>>0>=t>>>0||(c[r+88>>2]=i+(e<<2))}},function(r,e){var i=0,t=0;rr(r|=0,e|=0),(0|e)<0||(t=c[r+88>>2])-(i=c[r+84>>2])>>2<=(0|e)||(gr(i=(e<<2)+i|0,e=i+4|0,t-e|0),c[r+88>>2]=t-4)},ke,function(r){er(ke(r|=0))},ur,rr,ut,vt,Dt,Dt,function(r,e,i){i|=0;var t,f=0;return Z=t=Z+-64|0,f=1,Be(r|=0,e|=0,0)||(f=0,e&&(f=0,(e=Xr(e,11068))&&(Sr(4|(f=t+8|0),0,52),c[t+56>>2]=1,c[t+20>>2]=-1,c[t+16>>2]=r,c[t+8>>2]=e,Zt[c[c[e>>2]+28>>2]](e,f,c[i>>2],1),1==(0|(r=c[t+32>>2]))&&(c[i>>2]=c[t+24>>2]),f=1==(0|r)))),Z=t- -64|0,0|f},function(r,e,i,t,f,a){i|=0,t|=0,f|=0,a|=0,Be(r|=0,c[8+(e|=0)>>2],a)&&Ne(e,i,t,f)},function(r,e,i,t,f){if(i|=0,t|=0,f|=0,Be(r|=0,c[8+(e|=0)>>2],f))1==c[e+28>>2]|c[e+4>>2]!=(0|i)||(c[e+28>>2]=t);else r:if(Be(r,c[e>>2],f)){if(!(c[e+16>>2]!=(0|i)&c[e+20>>2]!=(0|i))){if(1!=(0|t))break r;return void(c[e+32>>2]=1)}c[e+20>>2]=i,c[e+32>>2]=t,c[e+40>>2]=c[e+40>>2]+1,1!=c[e+36>>2]|2!=c[e+24>>2]||(n[e+54|0]=1),c[e+44>>2]=4}},function(r,e,i,t){i|=0,t|=0,Be(r|=0,c[8+(e|=0)>>2],0)&&ei(e,i,t)},vt,function(r,e,i,t,f,a){i|=0,t|=0,f|=0,a|=0,Be(r|=0,c[8+(e|=0)>>2],a)?Ne(e,i,t,f):(r=c[r+8>>2],Zt[c[c[r>>2]+20>>2]](r,e,i,t,f,a))},function(r,e,i,t,f){if(i|=0,t|=0,f|=0,Be(r|=0,c[8+(e|=0)>>2],f))1==c[e+28>>2]|c[e+4>>2]!=(0|i)||(c[e+28>>2]=t);else r:{if(Be(r,c[e>>2],f)){if(!(c[e+16>>2]!=(0|i)&c[e+20>>2]!=(0|i))){if(1!=(0|t))break r;return void(c[e+32>>2]=1)}c[e+32>>2]=t;e:if(4!=c[e+44>>2]){if(A[e+52>>1]=0,r=c[r+8>>2],Zt[c[c[r>>2]+20>>2]](r,e,i,i,1,f),k[e+53|0]){if(c[e+44>>2]=3,!k[e+52|0])break e;break r}c[e+44>>2]=4}if(c[e+20>>2]=i,c[e+40>>2]=c[e+40>>2]+1,1!=c[e+36>>2]|2!=c[e+24>>2])break r;return void(n[e+54|0]=1)}r=c[r+8>>2],Zt[c[c[r>>2]+24>>2]](r,e,i,t,f)}},function(r,e,i,t){i|=0,t|=0,Be(r|=0,c[8+(e|=0)>>2],0)?ei(e,i,t):(r=c[r+8>>2],Zt[c[c[r>>2]+28>>2]](r,e,i,t))},vt,function(r){return 1235},vt,function(r){return 1201},vt,function(r){return 1161},bt,tt,bt,bt]).set=function(r,e){this[r]=e},Gt.get=function(r){return this[r]},Gt);function Et(){return a.byteLength/65536|0}return{f:function(){},g:Zt,h:ot,i:function(){return 0|pi(vi(40))},j:function(r,e,i){e|=0,i|=0,c[(r|=0)+16>>2]=0,c[r+20>>2]=0,c[r>>2]=e,c[r+8>>2]=i,c[r+12>>2]=0},k:ot,l:function(){var r,e;return e=vi(40),c[e>>2]=-1,c[(r=e+8|0)+16>>2]=0,c[r+20>>2]=0,c[r+8>>2]=0,c[r>>2]=0,c[r+4>>2]=0,c[r+24>>2]=0,c[r+28>>2]=0,0|e},m:At,n:function(r){var e=0;(r|=0)&&((e=c[r+8>>2])&&(c[r+12>>2]=e,er(e)),er(r))},o:function(){return 0|ai(vi(64))},p:ot,q:function(){var r;return r=ai(vi(96)),c[r+64>>2]=0,c[r+68>>2]=0,c[r+88>>2]=0,c[r+72>>2]=0,c[r+76>>2]=0,n[r+77|0]=0,n[r+78|0]=0,n[r+79|0]=0,n[r+80|0]=0,n[r+81|0]=0,n[r+82|0]=0,n[r+83|0]=0,n[r+84|0]=0,0|r},r:qi,s:function(r){return c[(r|=0)+88>>2]},t:function(r){return c[(r|=0)+56>>2]},u:$i,v:function(r){return n[(r|=0)+24|0]},w:function(r){return k[(r|=0)+32|0]},x:function(r){return c[(r|=0)+40>>2]},y:function(r){return c[(r|=0)+48>>2]},z:function(r){return c[(r|=0)+60>>2]},A:xe,B:function(){var r;return r=vi(24),c[r+8>>2]=0,c[r+12>>2]=0,c[r+4>>2]=-1,c[r>>2]=1624,c[r+16>>2]=0,c[r+20>>2]=0,0|r},C:Bi,D:tt,E:function(r,e){return e|=0,d(p[c[(r|=0)+8>>2]+(e<<2)>>2])},F:function(r){return d(p[(r|=0)+20>>2])},G:Hi,H:function(){var r;return r=vi(8),c[r+4>>2]=-1,c[r>>2]=1032,0|r},I:Bi,J:tt,K:Hi,L:function(){return 0|zi(vi(84))},M:Si,N:qi,O:Hi,P:function(){var r;return r=zi(vi(108)),c[r+84>>2]=0,c[r+88>>2]=0,c[r>>2]=10240,c[r+92>>2]=0,c[r+96>>2]=0,c[r+100>>2]=0,c[r+104>>2]=0,0|r},Q:function(r){return(c[(r|=0)+100>>2]-c[r+96>>2]|0)/12|0},R:Si,S:qi,T:Hi,U:function(){var r,e;return r=vi(24),c[r+4>>2]=0,c[r+8>>2]=0,c[(e=r+16|0)>>2]=0,c[e+4>>2]=0,c[r>>2]=r+4,c[r+12>>2]=e,0|r},V:function(r){(r|=0)&&(Ai(r+12|0,c[r+16>>2]),mi(r,c[r+4>>2]),er(r))},W:At,X:function(r){return 0|!c[(r|=0)>>2]},Y:function(r){return 0|(n[(r|=0)+15|0]<0?c[r+4>>2]:r+4|0)},Z:function(r){(r|=0)&&(n[r+15|0]<0&&er(c[r+4>>2]),er(r))},_:wi,$:function(r,e){return e|=0,d(p[c[(r|=0)>>2]+(e<<2)>>2])},aa:Li,ba:ji,ca:wi,da:function(r,e){return e|=0,n[c[(r|=0)>>2]+e|0]},ea:Ki,fa:ji,ga:wi,ha:function(r,e){return e|=0,k[c[(r|=0)>>2]+e|0]},ia:Ki,ja:ji,ka:wi,la:function(r,e){return e|=0,A[c[(r|=0)>>2]+(e<<1)>>1]},ma:xi,na:ji,oa:wi,pa:function(r,e){return e|=0,_[c[(r|=0)>>2]+(e<<1)>>1]},qa:xi,ra:ji,sa:wi,ta:gi,ua:Li,va:ji,wa:wi,xa:gi,ya:Li,za:ji,Aa:function(){var r;return r=vi(28),c[r>>2]=0,c[r+4>>2]=0,c[r+24>>2]=0,c[r+16>>2]=0,c[r+20>>2]=0,c[r+8>>2]=0,c[r+12>>2]=0,0|r},Ba:function(r,e,i){r|=0,e|=0;var t,f=0,a=0,A=0,b=0,u=0,_=0;if(Z=t=Z-16|0,(f=Xe(i|=0))>>>0<2147483632){r:{if(f>>>0>=11)r=vi(a=1+(15|f)|0),c[t+8>>2]=-2147483648|a,c[t>>2]=r,c[t+4>>2]=f,a=r+f|0;else if(n[t+11|0]=f,a=t+f|0,r=t,!f)break r;hr(r,i,f)}n[0|a]=0,f=(i=k[t+11|0])<<24>>24,r=0;r:if(e=c[e+4>>2]){for(r=i,r=(i=(0|f)<0)?c[t+4>>2]:r,a=i?c[t>>2]:t;;){e:{i:{t:{f:{a:{n:{if(b=(u=(i=(A=(i=k[e+27|0])<<24>>24<0)?c[e+20>>2]:i)>>>0<r>>>0)?i:r){if(_=Ye(a,A=A?c[e+16>>2]:e+16|0,b))break n;if(r>>>0>=i>>>0)break a;break e}if(r>>>0>=i>>>0)break f;break e}if((0|_)<0)break e}if(i=Ye(A,a,b))break t}if(u)break i;r=1;break r}if(!((0|i)<0)){r=1;break r}}e=e+4|0}if(!(e=c[e>>2]))break}r=0}(0|f)<0&&er(c[t>>2]),Z=t+16|0}else yt(),o();return 0|r},Ca:function(r,e,i){r|=0,e|=0,i|=0;var t,f=0,a=0;if(Z=t=Z-16|0,c[t+12>>2]=0,(f=Xe(i))>>>0<2147483632){r:{if(f>>>0>=11)r=vi(a=1+(15|f)|0),c[t+8>>2]=-2147483648|a,c[t>>2]=r,c[t+4>>2]=f,a=r+f|0;else if(n[t+11|0]=f,a=t+f|0,r=t,!f)break r;hr(r,i,f)}n[0|a]=0,(0|(r=te(e,t)))!=(e+4|0)&&4==((e=c[r+32>>2])-(r=c[r+28>>2])|0)&&(c[t+12>>2]=k[0|r]|k[r+1|0]<<8|k[r+2|0]<<16|k[r+3|0]<<24),r=c[t+12>>2],n[t+11|0]<0&&er(c[t>>2]),Z=t+16|0}else yt(),o();return 0|r},Da:function(r,e,i,t){r|=0,e|=0,t|=0;var f=0,a=0,A=0;if(Z=r=Z-32|0,(f=Xe(i|=0))>>>0<2147483632){r:{if(f>>>0>=11)a=vi(A=1+(15|f)|0),c[r+24>>2]=-2147483648|A,c[r+16>>2]=a,c[r+20>>2]=f,A=f+a|0;else if(n[r+27|0]=f,A=f+(a=r+16|0)|0,!f)break r;hr(a,i,f)}n[0|A]=0,c[r+8>>2]=0,c[r>>2]=0,c[r+4>>2]=0,(0|(i=te(e,r+16|0)))!=(e+4|0)&&(0|(e=c[i+28>>2]))!=(0|(f=c[i+32>>2]))&&(3&(e=f-e|0)||((f=e>>>2|0)>>>0>(A=(a=c[r+4>>2])-(e=c[r>>2])>>2)>>>0?(_e(r,f-A|0),e=c[r>>2],a=c[r+4>>2]):f>>>0>=A>>>0||(a=(f<<2)+e|0,c[r+4>>2]=a),(0|e)==(0|a)?(dt(),o()):hr(f=e,e=c[i+28>>2],c[i+32>>2]-e|0))),(e=c[t>>2])&&(c[t+4>>2]=e,er(e)),c[t>>2]=c[r>>2],c[t+4>>2]=c[r+4>>2],c[t+8>>2]=c[r+8>>2],n[r+27|0]<0&&er(c[r+16>>2]),Z=r+32|0}else yt(),o()},Ea:function(r,e,i){r|=0,e|=0,i|=0;var t=0,f=0,a=0,A=0;if(Z=r=Z-32|0,c[r+24>>2]=0,c[r+28>>2]=0,(t=Xe(i))>>>0<2147483632){r:{if(t>>>0>=11)a=vi(f=1+(15|t)|0),c[r+16>>2]=-2147483648|f,c[r+8>>2]=a,c[r+12>>2]=t,f=t+a|0;else if(n[r+19|0]=t,f=(a=r+8|0)+t|0,!t)break r;hr(a,i,t)}n[0|f]=0,(0|(i=e+4|0))!=(0|(e=te(e,r+8|0)))&&8==((i=c[e+32>>2])-(e=c[e+28>>2])|0)&&(i=k[e+4|0]|k[e+5|0]<<8|k[e+6|0]<<16|k[e+7|0]<<24,c[r+24>>2]=k[0|e]|k[e+1|0]<<8|k[e+2|0]<<16|k[e+3|0]<<24,c[r+28>>2]=i),A=l[r+24>>3],n[r+19|0]<0&&er(c[r+8>>2]),Z=r+32|0}else yt(),o();return+A},Fa:function(r,e,i){r|=0,e|=0;var t,f=0,a=0,A=0;if(Z=t=Z-16|0,(f=Xe(i|=0))>>>0<2147483632){r:{if(f>>>0>=11)a=vi(A=1+(15|f)|0),c[t+8>>2]=-2147483648|A,c[t>>2]=a,c[t+4>>2]=f,A=f+a|0;else if(n[t+11|0]=f,A=t+f|0,a=t,!f)break r;hr(a,i,f)}n[0|A]=0,i=ti(e,t,a=r+16|0),e=c[r+16>>2],r=n[r+27|0],n[t+11|0]<0&&er(c[t>>2]),Z=t+16|0,r=i?(0|r)<0?e:a:0}else yt(),o();return 0|r},Ga:function(r,e){return 0,c[(e|=0)+8>>2]},Ha:function(r,e,i){e|=0;var t,f,a=0,A=0,b=0,u=0,k=0,_=0;if(f=i|=0,t=r|=0,c[r+12>>2]!=(0|e)){if(r=e,(0|(e=c[t+4>>2]))!=(0|(a=c[t>>2])))for(;i=e-12|0,n[e-1|0]<0&&er(c[i>>2]),(0|a)!=(0|(e=i)););if(c[t+12>>2]=r,c[t+4>>2]=a,(0|(i=c[r>>2]))!=(0|(k=r+4|0)))for(;;){if((0|(r=c[t+4>>2]))==c[t+8>>2]){b=0;r:{e:{i:{if((e=(A=((r=c[t+4>>2])-(a=c[t>>2])|0)/12|0)+1|0)>>>0<357913942){if(_=(u=(c[t+8>>2]-a|0)/12|0)<<1,e=u>>>0>=178956970?357913941:e>>>0<_>>>0?_:e){if(e>>>0>=357913942)break i;b=vi(y(e,12))}if(u=y(e,12),e=y(A,12)+b|0,n[i+27|0]>=0?(A=c[i+20>>2],c[e>>2]=c[i+16>>2],c[e+4>>2]=A,c[e+8>>2]=c[i+24>>2]):(me(e,c[i+16>>2],c[i+20>>2]),a=c[t>>2],r=c[t+4>>2]),b=b+u|0,A=e+12|0,(0|r)==(0|a))break e;for(;u=c[(r=r-12|0)+4>>2],c[(e=e-12|0)>>2]=c[r>>2],c[e+4>>2]=u,c[e+8>>2]=c[r+8>>2],c[r>>2]=0,c[r+4>>2]=0,c[r+8>>2]=0,(0|r)!=(0|a););if(c[t+8>>2]=b,r=c[t+4>>2],c[t+4>>2]=A,a=c[t>>2],c[t>>2]=e,(0|r)==(0|a))break r;for(;e=r-12|0,n[r-1|0]<0&&er(c[e>>2]),r=e,(0|a)!=(0|e););break r}mt(),o()}Zi(),o()}c[t+8>>2]=b,c[t+4>>2]=A,c[t>>2]=e}a&&er(a)}else n[i+27|0]>=0?(e=c[i+20>>2],c[r>>2]=c[i+16>>2],c[r+4>>2]=e,c[r+8>>2]=c[i+24>>2]):me(r,c[i+16>>2],c[i+20>>2]),c[t+4>>2]=r+12;r:{if(e=c[i+4>>2])for(;;)if(r=e,!(e=c[e>>2]))break r;for(;r=c[i+8>>2],e=c[r>>2]!=(0|i),i=r,e;);}if(i=r,(0|k)==(0|r))break}}return r=0,(0|f)<0||(e=c[t>>2],(c[t+4>>2]-e|0)/12>>>0<=f>>>0||(r=e+y(f,12)|0,r=n[r+11|0]<0?c[r>>2]:r)),0|r},Ia:function(r){var e=0,i=0,t=0;if(r|=0){if(n[r+27|0]<0&&er(c[r+16>>2]),e=c[r>>2]){if(i=e,(0|e)!=(0|(t=c[r+4>>2]))){for(;i=t-12|0,n[t-1|0]<0&&er(c[i>>2]),(0|(t=i))!=(0|e););i=c[r>>2]}c[r+4>>2]=e,er(i)}er(r)}},Ja:function(){var r,e;return r=vi(40),c[r+4>>2]=0,c[r+8>>2]=0,c[r+24>>2]=0,c[r+28>>2]=0,c[(e=r+16|0)>>2]=0,c[e+4>>2]=0,c[r>>2]=r+4,c[r+12>>2]=e,c[r+32>>2]=0,c[r+36>>2]=0,0|r},Ka:function(r,e,i,t){r|=0,e|=0,i|=0,t|=0;var f,a=0,A=0;Z=t=Z+-64|0,a=pi(t+8|0),c[a+16>>2]=0,c[a+20>>2]=0,c[a>>2]=e,c[a+8>>2]=i,c[a+12>>2]=0,Qr(e=t+48|0),c[r+24>>2]=c[t+48>>2];r:if((0|e)!=(0|(f=r+24|0)))if(e=r+28|0,i=t+48|4,a=(A=k[t+63|0])<<24>>24,n[r+39|0]>=0){if((0|a)>=0){r=c[i+4>>2],c[e>>2]=c[i>>2],c[e+4>>2]=r,c[e+8>>2]=c[i+8>>2];break r}ri(e,c[t+52>>2],c[t+56>>2])}else qe(e,(r=(0|a)<0)?c[t+52>>2]:i,r?c[t+56>>2]:A);return n[t+63|0]<0&&er(c[t+52>>2]),Z=t- -64|0,0|f},La:function(r,e,i,t){r|=0,e|=0,i|=0,t|=0;var f,a=0,A=0;Z=f=Z+-64|0,a=pi(f+8|0),c[a+16>>2]=0,c[a+20>>2]=0,c[a>>2]=e,c[a+8>>2]=i,c[a+12>>2]=0,I(e=f+48|0,r,a,t),c[r+24>>2]=c[f+48>>2];r:if((0|(a=r+24|0))!=(0|e))if(e=r+28|0,i=f+48|4,t=(A=k[f+63|0])<<24>>24,n[r+39|0]>=0){if((0|t)>=0){r=c[i+4>>2],c[e>>2]=c[i>>2],c[e+4>>2]=r,c[e+8>>2]=c[i+8>>2];break r}ri(e,c[f+52>>2],c[f+56>>2])}else qe(e,(r=(0|t)<0)?c[f+52>>2]:i,r?c[f+56>>2]:A);return n[f+63|0]<0&&er(c[f+52>>2]),Z=f- -64|0,0|a},Ma:function(r,e,i){return 0,0|li(e|=0,i|=0)},Na:function(r,e,i){r|=0,e|=0;var t=0,f=0,a=0;if(Z=r=Z-32|0,(t=Xe(i|=0))>>>0<2147483632){r:{if(t>>>0>=11)a=vi(f=1+(15|t)|0),c[r+24>>2]=-2147483648|f,c[r+16>>2]=a,c[r+20>>2]=t,f=t+a|0;else if(n[r+27|0]=t,f=(a=r+16|0)+t|0,!t)break r;hr(a,i,t)}n[0|f]=0,n[r+4|0]=0,c[r>>2]=1701667182,n[r+11|0]=4,i=-1,(t=c[e+4>>2])&&(i=-1,(t=Hr(t,r,r+16|0))&&(i=ii(e,c[t+24>>2]))),e=i,n[r+11|0]<0&&er(c[r>>2]),n[r+27|0]<0&&er(c[r+16>>2]),Z=r+32|0}else yt(),o();return 0|e},Oa:function(r,e,i,t){r|=0,e|=0,t|=0;var f,a=0,A=0;Z=f=Z-32|0;r:{e:{if((a=Xe(i|=0))>>>0<2147483632){i:{if(a>>>0>=11)A=vi(r=1+(15|a)|0),c[f+24>>2]=-2147483648|r,c[f+16>>2]=A,c[f+20>>2]=a,r=a+A|0;else if(n[f+27|0]=a,r=a+(A=f+16|0)|0,!a)break i;hr(A,i,a)}if(n[0|r]=0,(i=Xe(t))>>>0>=2147483632)break e;i:{if(i>>>0>=11)r=vi(a=1+(15|i)|0),c[f+8>>2]=-2147483648|a,c[f>>2]=r,c[f+4>>2]=i,A=r+i|0;else if(n[f+11|0]=i,A=i+f|0,r=f,!i)break i;hr(r,t,i)}n[0|A]=0,r=-1,(i=c[e+4>>2])&&(r=-1,(i=Hr(i,f+16|0,f))&&(r=ii(e,c[i+24>>2]))),n[f+11|0]<0&&er(c[f>>2]),n[f+27|0]<0&&er(c[f+16>>2]),Z=f+32|0;break r}yt(),o()}yt(),o()}return 0|r},Pa:function(r,e,i){return 0,i|=0,c[c[(e|=0)+8>>2]+(i<<2)>>2]},Qa:function(r,e,i){r|=0,i|=0;var t=0,f=0;r=0;r:if((0|(t=c[(e|=0)+12>>2]))!=(0|(e=c[e+8>>2]))){t=(r=t-e>>2)>>>0<=1?1:r,r=0;e:{for(;;){if(f=c[e+(r<<2)>>2],c[f+60>>2]==(0|i))break e;if((0|t)==(0|(r=r+1|0)))break}r=0;break r}r=-1!=(0|r)?f:0}return 0|r},Ra:function(r,e){return 0,c[(e|=0)+4>>2]},Sa:function(r,e,i){r|=0,i|=0;var t,f=0;if((t=c[(e|=0)+4>>2])&&!((0|(e=c[c[c[e+8>>2]+(i<<2)>>2]+60>>2]))<0)&&(0|(r=c[t+24>>2]))!=(0|(i=c[t+28>>2])))r:{for(;;){if(f=c[r>>2],(0|e)==c[f+24>>2])break r;if((0|i)==(0|(r=r+4|0)))break}f=0}return 0|f},Ta:function(r,e,i,t){return r|=0,i|=0,t|=0,e=c[(e|=0)+96>>2],r=vi(12),e=e+y(i,12)|0,i=c[e+4>>2],c[r>>2]=c[e>>2],c[r+4>>2]=i,c[r+8>>2]=c[e+8>>2],(e=c[t>>2])&&(c[t+4>>2]=e,er(e)),c[t>>2]=r,r=r+12|0,c[t+8>>2]=r,c[t+4>>2]=r,1},Ua:function(r,e,i){r|=0,e|=0;var t,f,a,A,b,u,_=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,E=0,F=0,I=0,Y=0,w=0,j=0,J=0,B=0,M=0,Q=0,g=0;u=i|=0,i=0,Z=f=Z-96|0,Sr(t=f+16|0,0,76),c[f+92>>2]=-1,c[f+8>>2]=0,c[f>>2]=0,c[f+4>>2]=0,Z=a=Z-16|0,c[t+68>>2]=0,c[t+72>>2]=0,c[t>>2]=e,Z=A=Z-16|0,b=e,r=c[e+20>>2],(c[e+24>>2]-r|0)<=0||-1!=(0|(r=c[r>>2]))&&(i=c[c[b+8>>2]+(r<<2)>>2]);r:{e:{if(i){r=c[b+100>>2],s=c[b+96>>2],c[A+8>>2]=0,c[A>>2]=0,c[A+4>>2]=0,e=(0|(p=r-s|0))/12|0;i:if((0|r)!=(0|s)){if(e>>>0>=357913942)break e;if(_=vi(p),c[A>>2]=_,c[A+8>>2]=_+y(e,12),r=0,p=Sr(R=_,0,_=12+((p=p-12|0)-((p>>>0)%12|0)|0)|0),c[A+4>>2]=_+p,k[i+84|0]){if(d=1&(i=e>>>0<=1?1:e),e>>>0>=2)for(l=-2&i,i=0;_=y(r,12),m=c[(e=_+s|0)+4>>2],v=c[e>>2],c[(_=_+p|0)+8>>2]=c[e+8>>2],c[_>>2]=v,c[_+4>>2]=m,_=y(1|r,12),m=c[(e=_+s|0)+4>>2],v=c[e>>2],c[(_=_+p|0)+8>>2]=c[e+8>>2],c[_>>2]=v,c[_+4>>2]=m,r=r+2|0,(0|l)!=(0|(i=i+2|0)););if(!d)break i;e=y(r,12),i=c[(r=e+s|0)+4>>2],s=c[r>>2],c[(e=e+p|0)+8>>2]=c[r+8>>2],c[e>>2]=s,c[e+4>>2]=i}else for(d=e>>>0<=1?1:e,r=c[i+68>>2],i=0;_=y(i,12),l=c[r+(c[(e=_+s|0)>>2]<<2)>>2],m=c[r+(c[e+4>>2]<<2)>>2],c[(_=_+p|0)+8>>2]=c[r+(c[e+8>>2]<<2)>>2],c[_+4>>2]=m,c[_>>2]=l,(0|d)!=(0|(i=i+1|0)););}if(_=0,Z=w=Z-16|0,Ue(d=vi(88)),Z=I=Z-16|0,c[d+80>>2]=0,c[d+84>>2]=0,r=c[d+76>>2],c[d+76>>2]=0,r&&er(r),c[d+68>>2]=0,c[d+72>>2]=0,r=c[(e=d- -64|0)>>2],c[e>>2]=0,r&&er(r),l=c[A+4>>2],e=c[A>>2],r=y(i=(l-e|0)/12|0,3),p=c[d>>2],r>>>0>(s=c[d+4>>2]-p>>2)>>>0?(ae(d,r-s|0),i=((l=c[A+4>>2])-(e=c[A>>2])|0)/12|0):r>>>0>=s>>>0||(c[d+4>>2]=p+(r<<2)),(0|e)!=(0|l)){if(l=1&(s=i>>>0<=1?1:i),r=c[d>>2],i>>>0>=2)for(m=-2&s,i=0;p=e+(s=y(_,12))|0,c[(v=s+r|0)>>2]=c[p>>2],c[r+(4|s)>>2]=c[p+4>>2],c[v+8>>2]=c[p+8>>2],s=(p=y(1|_,12))+r|0,p=e+p|0,c[s>>2]=c[p>>2],c[s+4>>2]=c[p+4>>2],c[s+8>>2]=c[p+8>>2],_=_+2|0,(0|m)!=(0|(i=i+2|0)););l&&(e=e+(i=y(_,12))|0,c[(r=i+r|0)>>2]=c[e>>2],c[r+4>>2]=c[e+4>>2],c[r+8>>2]=c[e+8>>2])}c[I+12>>2]=-1,r=0,s=0,l=0,Z=p=Z-32|0;i:{t:{if(W=I+12|0){if(m=(_=(i=c[d+4>>2])-(v=c[d>>2])|0)>>2,R=c[d+12>>2],m>>>0>(e=c[d+16>>2]-R>>2)>>>0?(yr(d+12|0,m-e|0,10228),m=(_=(i=c[d+4>>2])-(v=c[d>>2])|0)>>2):e>>>0<=m>>>0||(c[d+16>>2]=R+(m<<2)),c[p+24>>2]=0,c[p+16>>2]=0,c[p+20>>2]=0,!(e=(0|i)==(0|v))){if((0|_)<0)break t;s=vi(_),c[p+20>>2]=s,c[p+16>>2]=s,c[p+24>>2]=(m<<2)+s}f:{a:{n:{A:{if(!_){if(_=0,!e)break A;break n}for(;(m=c[(r<<2)+v>>2])>>>0<(e=c[p+20>>2]-s>>2)>>>0||(c[p>>2]=0,(_=m+1|0)>>>0>e>>>0?(Nr(p+16|0,_-e|0,p),v=c[d>>2],i=c[d+4>>2],s=c[p+16>>2]):e>>>0<=_>>>0||(c[p+20>>2]=(_<<2)+s)),c[(e=(m<<2)+s|0)>>2]=c[e>>2]+1,(r=r+1|0)>>>0<(m=(_=i-v|0)>>2)>>>0;);if((0|i)==(0|v)){_=0;break n}if(_>>>0>=2147483645)break a}Sr(_=vi(_<<1),255,m<<3)}c[p+8>>2]=0,c[p>>2]=0,c[p+4>>2]=0,U=(r=(e=c[p+20>>2])-s|0)>>2;n:{if((0|e)!=(0|s)){if((0|r)<0)break n;if(V=vi(r),c[p>>2]=V,c[p+8>>2]=(U<<2)+V,e=Sr(V,0,r),c[p+4>>2]=e+r,R=3&(i=U>>>0<=1?1:U),r=0,i-1>>>0>=3)for(N=-4&i;c[(i=l<<2)+e>>2]=r,D=4|i,r=c[i+s>>2]+r|0,c[D+e>>2]=r,G=8|i,r=r+c[s+D>>2]|0,c[G+e>>2]=r,i|=12,r=r+c[s+G>>2]|0,c[i+e>>2]=r,r=r+c[i+s>>2]|0,l=l+4|0,(0|N)!=(0|(T=T+4|0)););if(R)for(;c[(i=l<<2)+e>>2]=r,l=l+1|0,r=c[i+s>>2]+r|0,(0|R)!=(0|(h=h+1|0)););}if(!m)break f;for(D=c[d+40>>2],G=c[d+12>>2],R=0;;){r=(j=R<<2)+v|0,h=-1,-1!=(0|(e=((i=R+1|0)>>>0)%3|0?i:R-2|0))&&(h=c[(e<<2)+v>>2]),e=c[r>>2];A:{o:{if(!((R>>>0)%3|0)){if(T=-1,-1!=(0|(r=R+2|0))&&(T=c[(r<<2)+v>>2]),!((0|e)==(0|h)|(0|e)==(0|T))&(0|h)!=(0|T))break o;D=D+1|0,c[d+40>>2]=D,i=R+3|0;break A}T=c[r-4>>2]}o:{b:if(!((0|(F=c[(r=T<<2)+s>>2]))<=0))for(r=c[r+V>>2],l=0;;){if(-1==(0|(E=c[(N=(r<<3)+_|0)>>2])))break b;if((0|h)==(0|E)&&(0|(E=-1!=(0|(N=c[N+4>>2]))?c[(N<<2)+v>>2]:-1))!=(0|e)){for(;e=r,!((0|F)<=(0|(l=l+1|0))||(E=c[(J=((r=e+1|0)<<3)+_|0)>>2],c[(B=(e<<3)+_|0)+4>>2]=c[J+4>>2],c[B>>2]=E,-1==(0|E))););if(c[(e<<3)+_>>2]=-1,-1==(0|N))break b;c[G+j>>2]=N,c[G+(N<<2)>>2]=R;break o}if(r=r+1|0,(0|F)==(0|(l=l+1|0)))break}if(!((0|(h=c[(r=h<<2)+s>>2]))<=0))for(r=c[r+V>>2],l=0;;){if(-1==c[(e=(r<<3)+_|0)>>2]){c[e>>2]=T,c[e+4>>2]=R;break o}if(r=r+1|0,(0|h)==(0|(l=l+1|0)))break}}}if(!((R=i)>>>0<m>>>0))break}break f}break t}mt(),o()}c[W>>2]=U,V&&er(V),_&&er(_),(r=c[p+16>>2])&&(c[p+20>>2]=r,er(r))}if(Z=p+32|0,D=0!=(0|W)){if(Z=h=Z-32|0,r=c[d>>2],l=c[d+4>>2],c[h+24>>2]=0,c[h+16>>2]=0,c[h+20>>2]=0,(0|r)==(0|l))i=l;else{if((0|(r=l-r|0))<0)break t;i=vi((e=1+((r>>=2)-1>>>5|0)|0)<<2),c[h+24>>2]=e,c[h+20>>2]=0,c[h+16>>2]=i,ve(h+16|0,r),l=c[d>>2],i=c[d+4>>2]}for(c[h+8>>2]=0,c[h>>2]=0;;){if(N=0,m=0,(0|i)!=(0|l)){for(;;){e=c[h+16>>2];f:if(!(c[e+(m>>>3&536870908)>>2]>>>m&1)){for(i=c[h>>2],c[h+4>>2]=i,s=c[d+12>>2],r=m;_=r,!(-1==(0|(r=((p=r+1|0)>>>0)%3|0?p:r-2|0))||-1==(0|(r=c[s+(r<<2)>>2]))||(0|m)==(0|(r=((p=r+1|0)>>>0)%3|0?p:r-2|0))|-1==(0|r)||c[e+(r>>>3&536870908)>>2]>>>r&1););v=_;a:{n:{A:{for(;;){if(r=c[h+16>>2]+(v>>>3&536870908)|0,c[r>>2]=c[r>>2]|1<<v,p=((r=v+1|0)>>>0)%3|0?r:v-2|0,l=c[d>>2],e=((G=(v>>>0)%3|0)?-1:2)+v|0,!(F=(0|(R=c[h>>2]))==(0|i)))if(W=c[(p<<2)+l>>2],V=c[d+12>>2],r=R,-1==(0|e))for(;;){if((0|W)==c[r>>2]&&(U=-1,s=-1,-1!=(0|(T=c[r+4>>2]))))break n;if((0|i)==(0|(r=r+8|0)))break}else for(s=V+(e<<2)|0;;){if((0|W)==c[r>>2]&&(0|(T=c[r+4>>2]))!=(0|(U=c[s>>2]))){if(s=e,i=-1,r=-1,-1==(0|T))break a;break n}if((0|i)==(0|(r=r+8|0)))break}if(e=c[(e<<2)+l>>2],c[h+8>>2]==(0|i)){if((s=(T=(r=i-R|0)>>3)+1|0)>>>0>=536870912)break t;if(l=r>>>2|0,l=r>>>0>=2147483640?536870911:s>>>0<l>>>0?l:s){if(l>>>0>=536870912)break A;s=vi(l<<3)}else s=0;if(c[(r=s+(T<<3)|0)>>2]=e,c[r+4>>2]=p,e=r+8|0,!F){for(;p=c[(i=i-8|0)+4>>2],c[(r=r-8|0)>>2]=c[i>>2],c[r+4>>2]=p,(0|i)!=(0|R););i=c[h>>2]}c[h+8>>2]=s+(l<<3),c[h+4>>2]=e,c[h>>2]=r,i&&er(i),i=e}else c[i>>2]=e,c[i+4>>2]=p,i=i+8|0,c[h+4>>2]=i;o:{if(G)r=v-1|0;else if(-1==(0|(r=v+2|0)))break o;if(-1!=(0|(r=c[c[d+12>>2]+(r<<2)>>2]))&&(0|_)!=(0|(v=r+((r>>>0)%3|0?-1:2)|0))&&-1!=(0|v))continue}break}l=c[d>>2];break f}Zi(),o()}i=c[V+(T<<2)>>2],e=s,r=T}-1!=(0|U)&&(c[V+(U<<2)>>2]=-1),-1!=(0|i)&&(c[V+(i<<2)>>2]=-1),c[V+(e<<2)>>2]=-1,c[V+(r<<2)>>2]=-1,N=1}if(!((m=m+1|0)>>>0<(i=c[d+4>>2])-l>>2>>>0))break}if(N)continue}break}(r=c[h>>2])&&er(r),(r=c[h+16>>2])&&er(r),R=0,Z=l=(Z=h+32|0)-32|0,s=c[I+12>>2],c[d+36>>2]=s,T=d+24|0,e=c[d+24>>2];f:{if((r=c[d+28>>2]-e>>2)>>>0<s>>>0)yr(T,s-r|0,10228),c[l+24>>2]=0,c[l+16>>2]=0,c[l+20>>2]=0;else if(r>>>0>s>>>0&&(c[d+28>>2]=e+(s<<2)),c[l+24>>2]=0,c[l+16>>2]=0,c[l+20>>2]=0,!s)break f;if((0|s)<0)break t;e=vi((r=1+(s-1>>>5|0)|0)<<2),c[l+24>>2]=r,c[l+20>>2]=0,c[l+16>>2]=e,ve(l+16|0,s)}r=c[d>>2],e=c[d+4>>2],c[l+8>>2]=0,c[l>>2]=0,c[l+4>>2]=0;f:{if((0|r)==(0|e))r=e;else{if((0|(r=e-r|0))<0)break t;i=vi((e=1+((r>>=2)-1>>>5|0)|0)<<2),c[l+8>>2]=e,c[l+4>>2]=0,c[l>>2]=i,ve(l,r),e=c[d>>2],r=c[d+4>>2]}if(!(r-e>>>0<12)){a:{for(;;){if(V=y(R,3),p=c[(_=(V<<2)+e|0)>>2],i=-1,-1!=(0|(m=V+1|0))&&(i=c[(m<<2)+e>>2]),(0|i)!=(0|p)&&!((0|(m=p))==(0|(p=c[_+8>>2]))|(0|i)==(0|p))){for(h=0,m=c[l>>2];;){if(!(c[((p=h+V|0)>>>3&536870908)+m>>2]>>>p&1)){if(i=1<<(r=c[(p<<2)+e>>2]),_=c[l+16>>2],U=i&(m=c[_+((e=r>>>5|0)<<2)>>2])){if((0|(i=c[d+28>>2]))==c[d+32>>2]){if((_=(N=(e=i-(m=c[T>>2])|0)>>2)+1|0)>>>0>=1073741824)break t;if(v=e>>>1|0,v=e>>>0>=2147483644?1073741823:_>>>0<v>>>0?v:_){if(v>>>0>=1073741824)break a;e=vi(v<<2)}else e=0;if(c[(_=e+(N<<2)|0)>>2]=-1,N=_+4|0,(0|i)!=(0|m))for(;i=i-4|0,c[(_=_-4|0)>>2]=c[i>>2],(0|i)!=(0|m););c[d+32>>2]=e+(v<<2),c[d+28>>2]=N,c[d+24>>2]=_,m&&er(m)}else c[i>>2]=-1,c[d+28>>2]=i+4;if((0|(i=c[d+52>>2]))==c[d+56>>2]){if((_=(N=(e=i-(m=c[d+48>>2])|0)>>2)+1|0)>>>0>=1073741824)break t;if(v=e>>>1|0,v=e>>>0>=2147483644?1073741823:_>>>0<v>>>0?v:_){if(v>>>0>=1073741824)break a;e=vi(v<<2)}else e=0;if(c[(_=e+(N<<2)|0)>>2]=r,r=_+4|0,(0|i)!=(0|m))for(;i=i-4|0,c[(_=_-4|0)>>2]=c[i>>2],(0|i)!=(0|m););c[d+56>>2]=e+(v<<2),c[d+52>>2]=r,c[d+48>>2]=_,m&&er(m)}else c[i>>2]=r,c[d+52>>2]=i+4;if((0|(i=c[l+20>>2]))==(r=c[l+24>>2])<<5){if((i+1|0)<0)break t;nr(e=l+16|0,r=i>>>0<=1073741822?(r<<=6)>>>0>(i=32+(-32&i)|0)>>>0?r:i:2147483647),i=c[l+20>>2]}c[l+20>>2]=i+1,_=c[l+16>>2],e=c[(r=_+(i>>>3&536870908)|0)>>2],Q=r,g=Yi(i)&e,c[Q>>2]=g,i=1<<s,m=c[((e=s>>>5|0)<<2)+_>>2],s=(r=s)+1|0}c[(e<<2)+_>>2]=i|m,N=c[d+24>>2]+(r<<2)|0,v=c[d+12>>2],e=c[d>>2],m=c[l>>2],i=p;n:{A:{o:{for(;;){if(-1==(0|i))break o;if(c[(_=(i>>>3&536870908)+m|0)>>2]=c[_>>2]|1<<i,c[N>>2]=i,U&&(c[(i<<2)+e>>2]=r),_=-1,-1!=(0|(i=((W=i+1|0)>>>0)%3|0?W:i-2|0))&&(_=-1,-1!=(0|(i=c[v+(i<<2)>>2]))&&(_=((_=i+1|0)>>>0)%3|0?_:i-2|0)),(0|p)==(0|(i=_)))break}if(-1!=(0|p))break n;i=1;break A}if((p>>>0)%3|0)i=p-1|0;else if(-1==(0|(i=p+2|0)))break n}if(-1!=(0|(i=c[v+(i<<2)>>2]))){if((i>>>0)%3|0)i=i-1|0;else if(-1==(0|(i=i+2|0)))break n;for(p=c[d+12>>2],e=c[d>>2];;){if(c[(_=(i>>>3&536870908)+m|0)>>2]=c[_>>2]|1<<i,U&&(c[(i<<2)+e>>2]=r),(i>>>0)%3|0)i=i-1|0;else if(-1==(0|(i=i+2|0)))break n;if(-1==(0|(i=c[p+(i<<2)>>2])))break n;if(-1==(0|(i=i+((i>>>0)%3|0?-1:2)|0)))break}}}}if(3==(0|(h=h+1|0)))break}e=c[d>>2],r=c[d+4>>2]}if(!((R=R+1|0)>>>0<(r-e>>2>>>0)/3>>>0))break}break f}Zi(),o()}}if(i=0,c[d+44>>2]=0,r=c[l+16>>2],e=c[l+20>>2])for(s=31&e,e=(e>>>3&536870908)+r|0,_=r,m=0;c[_>>2]>>>i&1||(m=m+1|0,c[d+44>>2]=m),(0|e)!=(0|(_=((p=31==(0|i))<<2)+_|0))|(0|(i=p?0:i+1|0))!=(0|s););(e=c[l>>2])&&(er(e),r=c[l+16>>2]),r&&er(r),Z=l+32|0}Z=I+16|0,D||(c[w+8>>2]=0,ge(d),d=0),Z=w+16|0,r=d;break i}mt(),o()}(e=c[A>>2])&&(c[A+4>>2]=e,er(e))}else r=0;Z=A+16|0;break r}mt(),o()}if(i=c[t+4>>2],e=r,c[t+4>>2]=r,i&&(ge(i),e=c[t+4>>2]),e&&(r=c[b+100>>2],i=c[b+96>>2],n[a+12|0]=0,xr(t+56|0,(r-i|0)/12|0,a+12|0),(0|(r=c[b+100>>2]))!=(0|(i=c[b+96>>2]))))for(;;){if(!(c[c[t+56>>2]+(Y>>>3&536870908)>>2]>>>Y&1)){if(fr(t,0,r=y(Y,3)),i=c[t+8>>2],s=c[t+12>>2],fr(t,1,r+1|0),p=c[t+20>>2],_=c[t+24>>2],fr(t,2,r+2|0),R=(0|i)==(0|s)?-1:0,s=(r=_-p>>2)>>>0>(i=s-i>>2)>>>0,i=c[t+36>>2]-c[t+32>>2]>>2>>>0>(s?r:i)>>>0?2:s?1:R,c[t+68>>2]<=0||(c[a+12>>2]=c[t+76>>2],c[a+8>>2]=f,pe(a+8|0,a+12|0),(0|(r=c[44+((i<<2)+t|0)>>2]))<0?r=-1:(s=(r>>>0)/3|0,r=c[(c[c[t>>2]+96>>2]+y(s,12)|0)+(r-y(s,3)<<2)>>2]),c[a+12>>2]=r,c[a+8>>2]=f,pe(a+8|0,a+12|0),s=c[t+72>>2],c[t+72>>2]=s+2,1&s&&(c[a+12>>2]=r,c[a+8>>2]=f,pe(a+8|0,a+12|0),c[t+72>>2]=c[t+72>>2]+1)),_=0,Z=s=Z-16|0,c[t+68>>2]=c[t+68>>2]+1,r=y(i,12)+t|0,(0|(r=c[r+12>>2]-c[r+8>>2]|0))>0)for(d=(r=r>>>2|0)>>>0<=1?1:r,i=c[44+((i<<2)+t|0)>>2];;){p=((r=i)>>>0)/3|0,l=(i=-1==(0|r))?-1:p,m=c[t+56>>2]+(l>>>3&536870908)|0,c[m>>2]=c[m>>2]|1<<l,c[t+72>>2]=c[t+72>>2]+1;r:{e:{i:{t:{f:{if(!_){if((0|r)>=0)c[s+12>>2]=c[(c[c[t>>2]+96>>2]+y(p,12)|0)+((r>>>0)%3<<2)>>2],c[s+8>>2]=f,pe(s+8|0,s+12|0);else if(c[s+12>>2]=-1,c[s+8>>2]=f,pe(s+8|0,s+12|0),i)break f;if(i=-1,(0|(p=((p=r+1|0)>>>0)%3|0?p:r-2|0))>=0?(l=(p>>>0)/3|0,p=c[(c[c[t>>2]+96>>2]+y(l,12)|0)+(p-y(l,3)<<2)>>2]):p=-1,c[s+12>>2]=p,c[s+8>>2]=f,pe(s+8|0,s+12|0),(0|(p=((r>>>0)%3|0?-1:2)+r|0))<0)break t;i=(p>>>0)/3|0,i=c[(c[c[t>>2]+96>>2]+y(i,12)|0)+(p-y(i,3)<<2)>>2];break t}if(i=(0|r)<0?-1:c[(c[c[t>>2]+96>>2]+y(p,12)|0)+((r>>>0)%3<<2)>>2],c[t+76>>2]=i,c[s+12>>2]=i,c[s+8>>2]=f,pe(s+8|0,s+12|0),1&_){if(i=-1,-1==(0|r))break r;if((0|y(p,3))!=(0|r)){r=r-1|0;break e}r=r+2|0;break i}if(i=-1,-1==(0|r))break r;r=((i=r+1|0)>>>0)%3|0?i:r-2|0;break i}i=-1,c[s+12>>2]=-1,c[s+8>>2]=f,pe(s+8|0,s+12|0)}c[t+76>>2]=i,c[s+12>>2]=i,c[s+8>>2]=f,pe(s+8|0,s+12|0)}if(i=-1,-1==(0|r))break r}i=c[c[c[t+4>>2]+12>>2]+(r<<2)>>2]}if((0|d)==(0|(_=_+1|0)))break}Z=s+16|0,i=c[b+96>>2],r=c[b+100>>2]}if(!((Y=Y+1|0)>>>0<(r-i|0)/12>>>0))break}return Z=a+16|0,e?((r=c[u>>2])&&(c[u+4>>2]=r,er(r)),c[u>>2]=c[f>>2],c[u+4>>2]=c[f+4>>2],c[u+8>>2]=c[f+8>>2],M=c[f+84>>2]):(r=c[f>>2])&&(c[f+4>>2]=r,er(r)),(r=c[f+72>>2])&&er(r),(r=c[f+48>>2])&&(c[f+52>>2]=r,er(r)),(r=c[f+36>>2])&&(c[f+40>>2]=r,er(r)),(r=c[f+24>>2])&&(c[f+28>>2]=r,er(r)),r=c[f+20>>2],c[f+20>>2]=0,r&&ge(r),Z=f+96|0,0|M},Va:function(r,e,i,t){r|=0,i|=0,t|=0;var f=0,a=0,n=0,o=0,b=0,u=0;if(!(s[(e|=0)+80>>2]>65535||(r=c[e+100>>2],e=c[e+96>>2],n=(0|(a=y(f=(r-e|0)/12|0,6)))==(0|i),(0|r)==(0|e)|(0|i)!=(0|a)))){if(n=1,b=1&(i=f>>>0<=1?1:f),r=0,f>>>0>=2)for(u=-2&i,i=0;o=(a=y(r,6))+t|0,f=e+y(r,12)|0,A[o>>1]=c[f>>2],A[(2|a)+t>>1]=c[f+4>>2],A[o+4>>1]=c[f+8>>2],f=y(a=1|r,6)+t|0,a=e+y(a,12)|0,A[f>>1]=c[a>>2],A[f+2>>1]=c[a+4>>2],A[f+4>>1]=c[a+8>>2],r=r+2|0,(0|u)!=(0|(i=i+2|0)););b&&(i=y(r,6)+t|0,r=e+y(r,12)|0,A[i>>1]=c[r>>2],A[i+2>>1]=c[r+4>>2],A[i+4>>1]=c[r+8>>2])}return 0|n},Wa:function(r,e,i,t){r|=0,i|=0,t|=0;var f,a=0,n=0,A=0,o=0,b=0,u=0;if(!((0|(f=(r=c[(e|=0)+100>>2])-(e=c[e+96>>2])|0))!=(0|i)|(0|r)==(0|e))){if(b=1&(a=(A=(0|i)/12|0)>>>0<=1?1:A),r=0,A>>>0>=2)for(u=-2&a,A=0;n=e+(a=y(r,12))|0,c[(o=a+t|0)>>2]=c[n>>2],c[(4|a)+t>>2]=c[n+4>>2],c[o+8>>2]=c[n+8>>2],a=(n=y(1|r,12))+t|0,n=e+n|0,c[a>>2]=c[n>>2],c[a+4>>2]=c[n+4>>2],c[a+8>>2]=c[n+8>>2],r=r+2|0,(0|u)!=(0|(A=A+2|0)););b&&(a=t,e=e+(t=y(r,12))|0,c[(r=a+t|0)>>2]=c[e>>2],c[r+4>>2]=c[e+4>>2],c[r+8>>2]=c[e+8>>2])}return(0|i)==(0|f)|0},Xa:function(r,e,i,t){r|=0,i|=0,t|=0;var f,a=0,A=0;return Z=r=Z-16|0,f=n[(e|=0)+24|0],a=c[2555],c[r+8>>2]=c[2554],c[r+12>>2]=a,a=c[2553],c[r>>2]=c[2552],c[r+4>>2]=a,(a=X(e,i,f,r))&&(e=0,f&&(A=hr(e=vi(i=(255&f)<<2),r,i)+i|0),(i=c[t>>2])&&(c[t+4>>2]=i,er(i)),c[t+8>>2]=A,c[t+4>>2]=A,c[t>>2]=e),Z=r+16|0,0|a},Ya:function(r,e,i,t){r|=0,i|=0,t|=0;var f,a=0,A=0,o=0,b=0,u=0,_=0,s=0,l=0,d=0,m=0;if(f=c[(e|=0)+80>>2],e=k[i+24|0],o=y(f,e),e){if(r=A=vi(b=e<<2),_=7&e)for(;c[r>>2]=-1073741824,r=r+4|0,(0|_)!=(0|(a=a+1|0)););if(!((e-1&1073741823)>>>0<7))for(a=A+b|0;c[r+24>>2]=-1073741824,c[r+28>>2]=-1073741824,c[r+16>>2]=-1073741824,c[r+20>>2]=-1073741824,c[r+8>>2]=-1073741824,c[r+12>>2]=-1073741824,c[r>>2]=-1073741824,c[r+4>>2]=-1073741824,(0|a)!=(0|(r=r+32|0)););}a=c[t>>2],(r=c[t+4>>2]-a>>2)>>>0<o>>>0?_e(t,o-r|0):r>>>0<=o>>>0||(c[t+4>>2]=a+(o<<2));r:{e:{i:if(f){if(e){for(d=252&e,_=3&e,m=e>>>0<4,a=0,e=0;;){if(!X(i,k[i+84|0]?e:c[c[i+68>>2]+(e<<2)>>2],n[i+24|0],A))break i;if(l=c[t>>2],u=0,r=0,s=0,!m)for(;b=r<<2,p[(o=(a<<2)+l|0)>>2]=p[b+A>>2],p[o+4>>2]=p[(4|b)+A>>2],p[o+8>>2]=p[(8|b)+A>>2],p[o+12>>2]=p[(12|b)+A>>2],r=r+4|0,a=a+4|0,(0|d)!=(0|(s=s+4|0)););if(_)for(;p[(a<<2)+l>>2]=p[(r<<2)+A>>2],r=r+1|0,a=a+1|0,(0|_)!=(0|(u=u+1|0)););if(u=f>>>0<=(e=e+1|0)>>>0,(0|e)==(0|f))break}break e}for(r=0;;){if(!X(i,k[i+84|0]?r:c[c[i+68>>2]+(r<<2)>>2],n[i+24|0],A))break i;if(u=f>>>0<=(r=r+1|0)>>>0,(0|r)==(0|f))break}}else u=1;if(!A)break r}er(A)}return 0|u},Za:pr,_a:function(r,e,i,t){r|=0,i|=0,t|=0;var f,a,A=0,b=0,u=0,_=0,s=0,p=0,l=0,d=0,m=0;Z=a=Z-16|0,f=c[(e|=0)+80>>2],A=k[i+24|0],r=y(f,A);r:{e:{i:{t:{if(e=c[i+28>>2],!(!k[i+84|0]|1!=(0|e)&2!=(0|e))){if(e=c[i+48>>2],i=c[c[i>>2]>>2],c[a+8>>2]=0,c[a>>2]=0,c[a+4>>2]=0,r){if((0|r)<0)break t;_=hr(b=vi(r),e+i|0,r)+r|0}(r=c[t>>2])&&(c[t+4>>2]=r,er(r)),c[t+8>>2]=_,c[t+4>>2]=_,c[t>>2]=b,e=1;break r}A&&Sr(b=vi(A),0,A);f:{a:if((u=(s=c[t+4>>2])-(e=c[t>>2])|0)>>>0<r>>>0){if((l=r-u|0)>>>0<=(p=c[t+8>>2])-s>>>0){d=t,m=Sr(s,0,l)+l|0,c[d+4>>2]=m;break a}if((0|r)<0)break f;if(p=(s=p-e|0)<<1,Sr((p=vi(s=s>>>0>=1073741823?2147483647:r>>>0<p>>>0?p:r))+u|0,0,l),u=gr(p,e,u),c[t+8>>2]=u+s,c[t+4>>2]=r+u,c[t>>2]=u,!e)break a;er(e)}else r>>>0>=u>>>0||(c[t+4>>2]=r+e);if(!f){e=1;break i}if(!A){for(e=0,r=0;;){if(!S(i,k[i+84|0]?r:c[c[i+68>>2]+(r<<2)>>2],n[i+24|0],b))break i;if(e=f>>>0<=(r=r+1|0)>>>0,(0|r)==(0|f))break}break i}for(s=252&A,u=3&A,e=0,p=A>>>0<4,A=0;;){if(!S(i,k[i+84|0]?A:c[c[i+68>>2]+(A<<2)>>2],n[i+24|0],b))break i;if(e=0,r=0,l=0,!p)for(;n[c[t>>2]+_|0]=k[r+b|0],n[1+(c[t>>2]+_|0)|0]=k[(1|r)+b|0],n[2+(c[t>>2]+_|0)|0]=k[(2|r)+b|0],n[3+(c[t>>2]+_|0)|0]=k[(3|r)+b|0],r=r+4|0,_=_+4|0,(0|s)!=(0|(l=l+4|0)););if(u)for(;n[c[t>>2]+_|0]=k[r+b|0],r=r+1|0,_=_+1|0,(0|u)!=(0|(e=e+1|0)););if(e=f>>>0<=(A=A+1|0)>>>0,(0|A)==(0|f))break}break e}mt(),o()}mt(),o()}if(!b)break r}er(b)}return Z=a+16|0,1&e},$a:function(r,e,i,t){r|=0,i|=0,t|=0;var f,a,A=0,b=0,u=0,_=0,s=0,p=0,l=0,d=0,m=0;Z=a=Z-16|0,f=c[(e|=0)+80>>2],A=k[i+24|0],r=y(f,A);r:{e:{i:{t:{if(e=c[i+28>>2],!(!k[i+84|0]|1!=(0|e)&2!=(0|e))){if(e=c[i+48>>2],i=c[c[i>>2]>>2],c[a+8>>2]=0,c[a>>2]=0,c[a+4>>2]=0,r){if((0|r)<0)break t;_=hr(b=vi(r),e+i|0,r)+r|0}(r=c[t>>2])&&(c[t+4>>2]=r,er(r)),c[t+8>>2]=_,c[t+4>>2]=_,c[t>>2]=b,e=1;break r}A&&Sr(b=vi(A),0,A);f:{a:if((u=(s=c[t+4>>2])-(e=c[t>>2])|0)>>>0<r>>>0){if((l=r-u|0)>>>0<=(p=c[t+8>>2])-s>>>0){d=t,m=Sr(s,0,l)+l|0,c[d+4>>2]=m;break a}if((0|r)<0)break f;if(p=(s=p-e|0)<<1,Sr((p=vi(s=s>>>0>=1073741823?2147483647:r>>>0<p>>>0?p:r))+u|0,0,l),u=gr(p,e,u),c[t+8>>2]=u+s,c[t+4>>2]=r+u,c[t>>2]=u,!e)break a;er(e)}else r>>>0>=u>>>0||(c[t+4>>2]=r+e);if(!f){e=1;break i}if(!A){for(e=0,r=0;;){if(!H(i,k[i+84|0]?r:c[c[i+68>>2]+(r<<2)>>2],n[i+24|0],b))break i;if(e=f>>>0<=(r=r+1|0)>>>0,(0|r)==(0|f))break}break i}for(s=252&A,u=3&A,e=0,p=A>>>0<4,A=0;;){if(!H(i,k[i+84|0]?A:c[c[i+68>>2]+(A<<2)>>2],n[i+24|0],b))break i;if(e=0,r=0,l=0,!p)for(;n[c[t>>2]+_|0]=k[r+b|0],n[1+(c[t>>2]+_|0)|0]=k[(1|r)+b|0],n[2+(c[t>>2]+_|0)|0]=k[(2|r)+b|0],n[3+(c[t>>2]+_|0)|0]=k[(3|r)+b|0],r=r+4|0,_=_+4|0,(0|s)!=(0|(l=l+4|0)););if(u)for(;n[c[t>>2]+_|0]=k[r+b|0],r=r+1|0,_=_+1|0,(0|u)!=(0|(e=e+1|0)););if(e=f>>>0<=(A=A+1|0)>>>0,(0|A)==(0|f))break}break e}mt(),o()}mt(),o()}if(!b)break r}er(b)}return Z=a+16|0,1&e},ab:function(r,e,i,t){r|=0,i|=0,t|=0;var f,a,b=0,u=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0;r=0,Z=a=Z-16|0,f=c[(e|=0)+80>>2],b=k[i+24|0],e=y(f,b);r:{e:{i:{t:{if(u=c[i+28>>2],!(!k[i+84|0]|3!=(0|u)&4!=(0|u))){if(b=c[i+48>>2],i=c[c[i>>2]>>2],c[a+8>>2]=0,c[a>>2]=0,c[a+4>>2]=0,e){if((0|e)<0)break t;s=hr(r=vi(e<<=1),i+b|0,e)+e|0}(e=c[t>>2])&&(c[t+4>>2]=e,er(e)),c[t+8>>2]=s,c[t+4>>2]=s,c[t>>2]=r,p=1;break r}if(b&&Sr(r=vi(u=b<<1),0,u),l=c[t>>2],(u=c[t+4>>2]-l>>1)>>>0<e>>>0?de(t,e-u|0):e>>>0>=u>>>0||(c[t+4>>2]=l+(e<<1)),!f){p=1;break i}if(!b){for(e=0;;){if(!B(i,k[i+84|0]?e:c[c[i+68>>2]+(e<<2)>>2],n[i+24|0],r))break i;if(p=f>>>0<=(e=e+1|0)>>>0,(0|e)==(0|f))break}break i}for(h=252&b,m=3&b,R=b>>>0<4,b=0;;){if(!B(i,k[i+84|0]?b:c[c[i+68>>2]+(b<<2)>>2],n[i+24|0],r))break i;if(v=c[t>>2],d=0,e=0,p=0,!R)for(;l=e<<1,A[(u=(s<<1)+v|0)>>1]=_[l+r>>1],A[u+2>>1]=_[(2|l)+r>>1],A[u+4>>1]=_[(4|l)+r>>1],A[u+6>>1]=_[(6|l)+r>>1],e=e+4|0,s=s+4|0,(0|h)!=(0|(p=p+4|0)););if(m)for(;A[(s<<1)+v>>1]=_[(e<<1)+r>>1],e=e+1|0,s=s+1|0,(0|(d=d+1|0))!=(0|m););if(p=f>>>0<=(b=b+1|0)>>>0,(0|b)==(0|f))break}break e}mt(),o()}if(!r)break r}er(r)}return Z=a+16|0,0|p},bb:function(r,e,i,t){r|=0,i|=0,t|=0;var f,a,b=0,u=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0;r=0,Z=a=Z-16|0,f=c[(e|=0)+80>>2],b=k[i+24|0],e=y(f,b);r:{e:{i:{t:{if(u=c[i+28>>2],!(!k[i+84|0]|3!=(0|u)&4!=(0|u))){if(b=c[i+48>>2],i=c[c[i>>2]>>2],c[a+8>>2]=0,c[a>>2]=0,c[a+4>>2]=0,e){if((0|e)<0)break t;s=hr(r=vi(e<<=1),i+b|0,e)+e|0}(e=c[t>>2])&&(c[t+4>>2]=e,er(e)),c[t+8>>2]=s,c[t+4>>2]=s,c[t>>2]=r,p=1;break r}if(b&&Sr(r=vi(u=b<<1),0,u),l=c[t>>2],(u=c[t+4>>2]-l>>1)>>>0<e>>>0?de(t,e-u|0):e>>>0>=u>>>0||(c[t+4>>2]=l+(e<<1)),!f){p=1;break i}if(!b){for(e=0;;){if(!Q(i,k[i+84|0]?e:c[c[i+68>>2]+(e<<2)>>2],n[i+24|0],r))break i;if(p=f>>>0<=(e=e+1|0)>>>0,(0|e)==(0|f))break}break i}for(h=252&b,m=3&b,R=b>>>0<4,b=0;;){if(!Q(i,k[i+84|0]?b:c[c[i+68>>2]+(b<<2)>>2],n[i+24|0],r))break i;if(v=c[t>>2],d=0,e=0,p=0,!R)for(;l=e<<1,A[(u=(s<<1)+v|0)>>1]=_[l+r>>1],A[u+2>>1]=_[(2|l)+r>>1],A[u+4>>1]=_[(4|l)+r>>1],A[u+6>>1]=_[(6|l)+r>>1],e=e+4|0,s=s+4|0,(0|h)!=(0|(p=p+4|0)););if(m)for(;A[(s<<1)+v>>1]=_[(e<<1)+r>>1],e=e+1|0,s=s+1|0,(0|(d=d+1|0))!=(0|m););if(p=f>>>0<=(b=b+1|0)>>>0,(0|b)==(0|f))break}break e}mt(),o()}if(!r)break r}er(r)}return Z=a+16|0,0|p},cb:pr,db:function(r,e,i,t){r|=0,i|=0,t|=0;var f,a,A=0,b=0,u=0,_=0,s=0,p=0,l=0,d=0,m=0,v=0;r=0,Z=a=Z-16|0,f=c[(e|=0)+80>>2],A=k[i+24|0],e=y(f,A);r:{e:{i:{t:{if(b=c[i+28>>2],!(!k[i+84|0]|5!=(0|b)&6!=(0|b))){if(A=c[i+48>>2],i=c[c[i>>2]>>2],c[a+8>>2]=0,c[a>>2]=0,c[a+4>>2]=0,e){if((0|e)<0)break t;u=hr(r=vi(e<<=2),i+A|0,e)+e|0}(e=c[t>>2])&&(c[t+4>>2]=e,er(e)),c[t+8>>2]=u,c[t+4>>2]=u,c[t>>2]=r,_=1;break r}if(A&&Sr(r=vi(b=A<<2),0,b),s=c[t>>2],(b=c[t+4>>2]-s>>2)>>>0<e>>>0?_e(t,e-b|0):e>>>0>=b>>>0||(c[t+4>>2]=s+(e<<2)),!f){_=1;break i}if(!A){for(e=0;;){if(!O(i,k[i+84|0]?e:c[c[i+68>>2]+(e<<2)>>2],n[i+24|0],r))break i;if(_=f>>>0<=(e=e+1|0)>>>0,(0|e)==(0|f))break}break i}for(m=252&A,l=3&A,v=A>>>0<4,A=0;;){if(!O(i,k[i+84|0]?A:c[c[i+68>>2]+(A<<2)>>2],n[i+24|0],r))break i;if(d=c[t>>2],p=0,e=0,_=0,!v)for(;s=e<<2,c[(b=(u<<2)+d|0)>>2]=c[s+r>>2],c[b+4>>2]=c[(4|s)+r>>2],c[b+8>>2]=c[(8|s)+r>>2],c[b+12>>2]=c[(12|s)+r>>2],e=e+4|0,u=u+4|0,(0|m)!=(0|(_=_+4|0)););if(l)for(;c[(u<<2)+d>>2]=c[(e<<2)+r>>2],e=e+1|0,u=u+1|0,(0|(p=p+1|0))!=(0|l););if(_=f>>>0<=(A=A+1|0)>>>0,(0|A)==(0|f))break}break e}mt(),o()}if(!r)break r}er(r)}return Z=a+16|0,0|_},eb:function(r,e,i,t,f,a){r|=0,i|=0,f|=0,a|=0;var A=0,o=0,b=0,u=0,_=0,s=0,p=0,l=0,d=0,m=0,v=0,h=0,R=0;b=e|=0,r=0,e=0;r:{e:switch((t|=0)-1|0){case 0:if(u=c[b+80>>2],o=k[i+24|0],(0|y(u,o))==(0|f))if((t=1!=c[i+28>>2])|!(e=k[i+84|0])){o&&Sr(r=vi(o),0,o);i:if(u)if(t)if(e=0,o)for(t=0,f=0;;){if(!S(i,k[i+84|0]?f:c[c[i+68>>2]+(f<<2)>>2],n[i+24|0],r))break i;if(hr(t+a|0,r,o),t=t+o|0,e=u>>>0<=(f=f+1|0)>>>0,(0|f)==(0|u))break}else for(t=0;;){if(!S(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],r))break i;if(e=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else{if(o){for(t=0,f=0;b=t+a|0,_=c[c[i>>2]>>2],p=c[i+48>>2],l=e=$e(A=c[i+40>>2],c[i+44>>2],k[i+84|0]?f:c[c[i+68>>2]+(f<<2)>>2],0),hr(b,hr(r,(e=e+p|0)+_|0,A),o),t=t+o|0,e=1,(0|u)!=(0|(f=f+1|0)););break i}if(e){if(e=1,o=c[i>>2],f=c[i+48>>2],a=c[i+40>>2],b=c[i+44>>2],1!=(0|u)){for(A=-2&u,i=0,t=0;_=hr(r,(_=c[o>>2])+(p=$e(a,b,i,0)+f|0)|0,a),hr(_,(p=c[o>>2])+(l=$e(a,b,1|i,0)+f|0)|0,a),i=i+2|0,(0|A)!=(0|(t=t+2|0)););A=i}if(!(1&u))break i;hr(r,(i=c[o>>2])+(t=$e(A,0,a,b)+f|0)|0,a);break i}if(e=1,o=c[i>>2],f=c[i+48>>2],A=c[i+68>>2],a=c[i+40>>2],b=c[i+44>>2],i=0,1!=(0|u))for(_=-2&u,t=0;p=hr(r,(p=c[o>>2])+(s=$e(a,b,c[(l=i<<2)+A>>2],0)+f|0)|0,a),hr(p,(s=c[o>>2])+(l=$e(a,b,c[A+(4|l)>>2],0)+f|0)|0,a),i=i+2|0,(0|_)!=(0|(t=t+2|0)););if(!(1&u))break i;t=c[o>>2],hr(r,(i=$e(a,b,c[A+(i<<2)>>2],0)+f|0)+t|0,a)}else e=1;r&&er(r)}else hr(a,c[c[i>>2]>>2]+c[i+48>>2]|0,f),e=1;break r;case 2:if(s=(l=k[i+24|0])<<1,u=c[b+80>>2],(0|y(s,u))==(0|f))if((b=3!=c[i+28>>2])|!(t=k[i+84|0])){l?Sr(f=vi(s),0,s):f=0;i:if(u)if(b)if(l)for(t=0;;){if(!B(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],f))break i;if(hr((e<<1)+a|0,f,s),e=e+l|0,r=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else for(t=0;;){if(!B(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],f))break i;if(r=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else{if(d=c[i+68>>2],_=c[i>>2],e=c[i+48>>2],b=c[i+40>>2],p=c[i+44>>2],l){if(!t){for(i=0,t=0;r=1,hr((i<<1)+a|0,hr(f,(A=c[_>>2])+(m=$e(b,p,c[d+(t<<2)>>2],0)+e|0)|0,b),s),i=i+l|0,(0|u)!=(0|(t=t+1|0)););break i}for(i=0;r=1,hr((i<<1)+a|0,hr(f,(d=c[_>>2])+(m=$e(A,o,b,p)+e|0)|0,b),s),i=i+l|0,t=o,o=t=(A=A+1|0)?t:t+1|0,(0|u)!=(0|A)|t;);break i}if(!t){if(r=1,i=0,1!=(0|u))for(a=-2&u,t=0;o=hr(f,(o=c[_>>2])+(l=$e(b,p,c[(A=i<<2)+d>>2],0)+e|0)|0,b),l=c[_>>2],hr(o,(A=$e(b,p,c[d+(4|A)>>2],0)+e|0)+l|0,b),i=i+2|0,(0|a)!=(0|(t=t+2|0)););if(!(1&u))break i;t=c[_>>2],hr(f,(e=$e(b,p,c[d+(i<<2)>>2],0)+e|0)+t|0,b);break i}if(l=1&u,r=1,1!=(0|u))for(u&=-2,a=0,i=0;t=hr(f,(t=c[_>>2])+(s=$e(A,o,b,p)+e|0)|0,b),hr(t,(s=c[_>>2])+(d=$e(b,p,1|A,o)+e|0)|0,b),o=(A=A+2|0)>>>0<2?o+1|0:o,(0|(a=a+2|0))!=(0|u)|(i=t=a>>>0<2?i+1|0:i););if(!l)break i;i=c[_>>2],hr(f,(e=$e(A,o,b,p)+e|0)+i|0,b)}else r=1;f&&er(f)}else hr(a,c[c[i>>2]>>2]+c[i+48>>2]|0,f),r=1;e=r;break r;case 4:if(d=(s=k[i+24|0])<<2,u=c[b+80>>2],(0|y(d,u))==(0|f))if((b=5!=c[i+28>>2])|!(t=k[i+84|0])){s?Sr(f=vi(d),0,d):f=0,e=1;i:if(u)if(b)if(e=0,s)for(t=0;;){if(!M(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],f))break i;if(hr((r<<2)+a|0,f,d),r=r+s|0,e=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else for(t=0;;){if(!M(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],f))break i;if(e=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else{if(r=c[i+68>>2],p=c[i>>2],b=c[i+48>>2],_=c[i+40>>2],l=c[i+44>>2],s){if(!t){for(i=0,t=0;hr((i<<2)+a|0,hr(f,(A=c[p>>2])+(m=$e(_,l,c[r+(t<<2)>>2],0)+b|0)|0,_),d),i=i+s|0,(0|u)!=(0|(t=t+1|0)););break i}for(i=0;hr((i<<2)+a|0,hr(f,(t=c[p>>2])+(m=$e(A,o,_,l)+b|0)|0,_),d),i=i+s|0,(0|u)!=(0|(A=A+1|0))|(o=r=A?o:o+1|0););break i}if(!t){if(i=0,1!=(0|u))for(a=-2&u,t=0;o=hr(f,(o=c[p>>2])+(s=$e(_,l,c[(A=i<<2)+r>>2],0)+b|0)|0,_),s=c[p>>2],hr(o,(A=$e(_,l,c[r+(4|A)>>2],0)+b|0)+s|0,_),i=i+2|0,(0|a)!=(0|(t=t+2|0)););if(!(1&u))break i;t=c[p>>2],hr(f,(r=$e(_,l,c[r+(i<<2)>>2],0)+b|0)+t|0,_);break i}if(s=1&u,1!=(0|u))for(u&=-2,a=0,i=0;r=hr(f,(r=c[p>>2])+(t=$e(A,o,_,l)+b|0)|0,_),hr(r,(t=c[p>>2])+(d=$e(_,l,1|A,o)+b|0)|0,_),t=o,o=(A=A+2|0)>>>0<2?t+1|0:t,(0|(a=a+2|0))!=(0|u)|(i=r=a>>>0<2?i+1|0:i););if(!s)break i;hr(f,(r=c[p>>2])+(i=$e(A,o,_,l)+b|0)|0,_)}f&&er(f)}else hr(a,c[c[i>>2]>>2]+c[i+48>>2]|0,f),e=1;break r;case 1:if(u=c[b+80>>2],o=k[i+24|0],(0|y(u,o))==(0|f))if((t=2!=c[i+28>>2])|!(e=k[i+84|0])){o&&Sr(r=vi(o),0,o);i:if(u)if(t)if(e=0,o)for(t=0,f=0;;){if(!H(i,k[i+84|0]?f:c[c[i+68>>2]+(f<<2)>>2],n[i+24|0],r))break i;if(hr(t+a|0,r,o),t=t+o|0,e=u>>>0<=(f=f+1|0)>>>0,(0|f)==(0|u))break}else for(t=0;;){if(!H(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],r))break i;if(e=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else{if(o){for(t=0,f=0;b=t+a|0,_=c[c[i>>2]>>2],p=c[i+48>>2],l=e=$e(A=c[i+40>>2],c[i+44>>2],k[i+84|0]?f:c[c[i+68>>2]+(f<<2)>>2],0),hr(b,hr(r,(e=e+p|0)+_|0,A),o),t=t+o|0,e=1,(0|u)!=(0|(f=f+1|0)););break i}if(e){if(e=1,o=c[i>>2],f=c[i+48>>2],a=c[i+40>>2],b=c[i+44>>2],1!=(0|u)){for(A=-2&u,i=0,t=0;_=hr(r,(_=c[o>>2])+(p=$e(a,b,i,0)+f|0)|0,a),hr(_,(p=c[o>>2])+(l=$e(a,b,1|i,0)+f|0)|0,a),i=i+2|0,(0|A)!=(0|(t=t+2|0)););A=i}if(!(1&u))break i;hr(r,(i=c[o>>2])+(t=$e(A,0,a,b)+f|0)|0,a);break i}if(e=1,o=c[i>>2],f=c[i+48>>2],A=c[i+68>>2],a=c[i+40>>2],b=c[i+44>>2],i=0,1!=(0|u))for(_=-2&u,t=0;p=hr(r,(p=c[o>>2])+(s=$e(a,b,c[(l=i<<2)+A>>2],0)+f|0)|0,a),hr(p,(s=c[o>>2])+(l=$e(a,b,c[A+(4|l)>>2],0)+f|0)|0,a),i=i+2|0,(0|_)!=(0|(t=t+2|0)););if(!(1&u))break i;t=c[o>>2],hr(r,(i=$e(a,b,c[A+(i<<2)>>2],0)+f|0)+t|0,a)}else e=1;r&&er(r)}else hr(a,c[c[i>>2]>>2]+c[i+48>>2]|0,f),e=1;break r;case 3:if(s=(l=k[i+24|0])<<1,u=c[b+80>>2],(0|y(s,u))==(0|f))if((b=4!=c[i+28>>2])|!(t=k[i+84|0])){l?Sr(f=vi(s),0,s):f=0;i:if(u)if(b)if(l)for(t=0;;){if(!Q(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],f))break i;if(hr((e<<1)+a|0,f,s),e=e+l|0,r=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else for(t=0;;){if(!Q(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],f))break i;if(r=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else{if(d=c[i+68>>2],_=c[i>>2],e=c[i+48>>2],b=c[i+40>>2],p=c[i+44>>2],l){if(!t){for(i=0,t=0;r=1,hr((i<<1)+a|0,hr(f,(A=c[_>>2])+(m=$e(b,p,c[d+(t<<2)>>2],0)+e|0)|0,b),s),i=i+l|0,(0|u)!=(0|(t=t+1|0)););break i}for(i=0;r=1,hr((i<<1)+a|0,hr(f,(d=c[_>>2])+(m=$e(A,o,b,p)+e|0)|0,b),s),i=i+l|0,t=o,o=t=(A=A+1|0)?t:t+1|0,(0|u)!=(0|A)|t;);break i}if(!t){if(r=1,i=0,1!=(0|u))for(a=-2&u,t=0;o=hr(f,(o=c[_>>2])+(l=$e(b,p,c[(A=i<<2)+d>>2],0)+e|0)|0,b),l=c[_>>2],hr(o,(A=$e(b,p,c[d+(4|A)>>2],0)+e|0)+l|0,b),i=i+2|0,(0|a)!=(0|(t=t+2|0)););if(!(1&u))break i;t=c[_>>2],hr(f,(e=$e(b,p,c[d+(i<<2)>>2],0)+e|0)+t|0,b);break i}if(l=1&u,r=1,1!=(0|u))for(u&=-2,a=0,i=0;t=hr(f,(t=c[_>>2])+(s=$e(A,o,b,p)+e|0)|0,b),hr(t,(s=c[_>>2])+(d=$e(b,p,1|A,o)+e|0)|0,b),o=(A=A+2|0)>>>0<2?o+1|0:o,(0|(a=a+2|0))!=(0|u)|(i=t=a>>>0<2?i+1|0:i););if(!l)break i;i=c[_>>2],hr(f,(e=$e(A,o,b,p)+e|0)+i|0,b)}else r=1;f&&er(f)}else hr(a,c[c[i>>2]>>2]+c[i+48>>2]|0,f),r=1;e=r;break r;case 5:if(d=(s=k[i+24|0])<<2,u=c[b+80>>2],(0|y(d,u))==(0|f))if((b=6!=c[i+28>>2])|!(t=k[i+84|0])){s?Sr(f=vi(d),0,d):f=0,e=1;i:if(u)if(b)if(e=0,s)for(t=0;;){if(!O(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],f))break i;if(hr((r<<2)+a|0,f,d),r=r+s|0,e=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else for(t=0;;){if(!O(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],f))break i;if(e=u>>>0<=(t=t+1|0)>>>0,(0|t)==(0|u))break}else{if(r=c[i+68>>2],p=c[i>>2],b=c[i+48>>2],_=c[i+40>>2],l=c[i+44>>2],s){if(!t){for(i=0,t=0;hr((i<<2)+a|0,hr(f,(A=c[p>>2])+(m=$e(_,l,c[r+(t<<2)>>2],0)+b|0)|0,_),d),i=i+s|0,(0|u)!=(0|(t=t+1|0)););break i}for(i=0;hr((i<<2)+a|0,hr(f,(t=c[p>>2])+(m=$e(A,o,_,l)+b|0)|0,_),d),i=i+s|0,(0|u)!=(0|(A=A+1|0))|(o=r=A?o:o+1|0););break i}if(!t){if(i=0,1!=(0|u))for(a=-2&u,t=0;o=hr(f,(o=c[p>>2])+(s=$e(_,l,c[(A=i<<2)+r>>2],0)+b|0)|0,_),s=c[p>>2],hr(o,(A=$e(_,l,c[r+(4|A)>>2],0)+b|0)+s|0,_),i=i+2|0,(0|a)!=(0|(t=t+2|0)););if(!(1&u))break i;t=c[p>>2],hr(f,(r=$e(_,l,c[r+(i<<2)>>2],0)+b|0)+t|0,_);break i}if(s=1&u,1!=(0|u))for(u&=-2,a=0,i=0;r=hr(f,(r=c[p>>2])+(t=$e(A,o,_,l)+b|0)|0,_),hr(r,(t=c[p>>2])+(d=$e(_,l,1|A,o)+b|0)|0,_),t=o,o=(A=A+2|0)>>>0<2?t+1|0:t,(0|(a=a+2|0))!=(0|u)|(i=r=a>>>0<2?i+1|0:i););if(!s)break i;hr(f,(r=c[p>>2])+(i=$e(A,o,_,l)+b|0)|0,_)}f&&er(f)}else hr(a,c[c[i>>2]>>2]+c[i+48>>2]|0,f),e=1;break r;case 8:if(v=(m=k[i+24|0])<<2,_=c[b+80>>2],(0|y(v,_))==(0|f)){if(b=c[i+28>>2],m){if(t=r=vi(v),s=1+((p=v-4|0)>>>2|0)&7)for(f=0;c[t>>2]=-1073741824,t=t+4|0,(0|s)!=(0|(f=f+1|0)););if(!(p>>>0<28))for(f=(m<<2)+r|0;c[t+24>>2]=-1073741824,c[t+28>>2]=-1073741824,c[t+16>>2]=-1073741824,c[t+20>>2]=-1073741824,c[t+8>>2]=-1073741824,c[t+12>>2]=-1073741824,c[t>>2]=-1073741824,c[t+4>>2]=-1073741824,(0|f)!=(0|(t=t+32|0)););}i:if(_)if(9!=(0|b))if(m)for(f=0,t=0;;){if(!X(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],r))break i;if(hr((f<<2)+a|0,r,v),f=f+m|0,e=_>>>0<=(t=t+1|0)>>>0,(0|t)==(0|_))break}else for(t=0;;){if(!X(i,k[i+84|0]?t:c[c[i+68>>2]+(t<<2)>>2],n[i+24|0],r))break i;if(e=_>>>0<=(t=t+1|0)>>>0,(0|t)==(0|_))break}else{if(h=c[i+68>>2],s=c[i>>2],b=c[i+48>>2],R=k[i+84|0],p=c[i+44>>2],d=i=c[i+40>>2],m){for(f=0,t=0;o=(f<<2)+a|0,A=c[s>>2],hr(o,hr(r,(e=$e(i,p,R?t:c[h+(t<<2)>>2],0)+b|0)+A|0,d),v),f=f+m|0,e=1,(0|_)!=(0|(t=t+1|0)););break i}if(!R){if(e=1,t=0,1!=(0|_))for(a=-2&_,f=0;o=hr(r,(o=c[s>>2])+(u=$e(i,p,c[(A=t<<2)+h>>2],0)+b|0)|0,d),hr(o,(u=c[s>>2])+(A=$e(i,p,c[h+(4|A)>>2],0)+b|0)|0,d),t=t+2|0,(0|a)!=(0|(f=f+2|0)););if(!(1&_))break i;f=c[s>>2],hr(r,(i=$e(i,p,c[h+(t<<2)>>2],0)+b|0)+f|0,d);break i}if(a=1&_,e=1,1!=(0|_))for(_&=-2;t=hr(r,(t=c[s>>2])+(f=$e(A,o,i,p)+b|0)|0,d),hr(t,(f=c[s>>2])+(m=$e(i,p,1|A,o)+b|0)|0,d),o=(A=A+2|0)>>>0<2?o+1|0:o,t=u,l=f=l+2|0,u=t=f>>>0<2?t+1|0:t,(0|f)!=(0|_)|t;);if(!a)break i;t=c[s>>2],hr(r,(i=$e(A,o,i,p)+b|0)+t|0,d)}else e=1;r&&er(r)}r=e;break;default:break e}e=r}return 0|e},fb:function(r,e){r|=0,e|=0;var i,t,f=0,a=0,A=0,b=0,u=0,_=0,p=0,l=0,y=0,d=0,m=0,v=0,h=0,R=0,N=0,T=0,V=0,U=0,W=0,D=0,G=0,E=0;Z=t=Z-16|0,c[t+12>>2]=e,e=vi(32),c[t>>2]=e,c[t+4>>2]=24,c[t+8>>2]=-2147483616,f=k[1196]|k[1197]<<8|k[1198]<<16|k[1199]<<24,a=k[1192]|k[1193]<<8|k[1194]<<16|k[1195]<<24,n[e+16|0]=a,n[e+17|0]=a>>>8,n[e+18|0]=a>>>16,n[e+19|0]=a>>>24,n[e+20|0]=f,n[e+21|0]=f>>>8,n[e+22|0]=f>>>16,n[e+23|0]=f>>>24,f=k[1188]|k[1189]<<8|k[1190]<<16|k[1191]<<24,a=k[1184]|k[1185]<<8|k[1186]<<16|k[1187]<<24,n[e+8|0]=a,n[e+9|0]=a>>>8,n[e+10|0]=a>>>16,n[e+11|0]=a>>>24,n[e+12|0]=f,n[e+13|0]=f>>>8,n[e+14|0]=f>>>16,n[e+15|0]=f>>>24,f=k[1180]|k[1181]<<8|k[1182]<<16|k[1183]<<24,a=k[1176]|k[1177]<<8|k[1178]<<16|k[1179]<<24,n[0|e]=a,n[e+1|0]=a>>>8,n[e+2|0]=a>>>16,n[e+3|0]=a>>>24,n[e+4|0]=f,n[e+5|0]=f>>>8,n[e+6|0]=f>>>16,n[e+7|0]=f>>>24,n[e+24|0]=0,Z=i=Z-48|0,b=c[t+12>>2],a=r;r:{if(e=c[(r=r+16|0)>>2]){for(f=r;f=(A=(0|b)>c[e+16>>2])?f:e,e=c[(A?e+4|0:e)>>2];);if((0|r)!=(0|f)&&(0|b)>=c[f+16>>2])break r}c[i+28>>2]=0,c[i+32>>2]=0,D=i+24|0,c[i+24>>2]=4|D,c[(r=i+16|0)>>2]=0,c[r+4>>2]=0,c[i+8>>2]=b,c[i+12>>2]=r,r=T=i+8|0,Z=W=Z-16|0;e:{i:if(f=c[(V=a+12|0)+4>>2]){for(r=c[r>>2];;){if(a=f,(0|(e=c[f+16>>2]))>(0|r)){if(m=a,f=c[a>>2])continue;break i}if((0|r)<=(0|e)){u=a,r=0;break e}if(!(f=c[a+4>>2]))break}m=a+4|0}else a=m=V+4|0;if(u=vi(32),e=c[T>>2],c[(r=h=u+24|0)>>2]=0,c[r+4>>2]=0,c[u+16>>2]=e,c[(R=u+20|0)>>2]=r,(0|(f=c[T+4>>2]))!=(0|(G=T+8|0)))for(;;){Z=v=Z-16|0,r=v+8|0,y=f+16|0;i:{t:{f:{a:{n:{A:{o:{b:if((0|(b=h))!=(0|(A=R+4|0)))if(_=(e=k[b+27|0])<<24>>24<0,U=(N=(p=(l=(0|(d=(p=k[y+11|0])<<24>>24))<0)?c[y+4>>2]:p)>>>0>(e=_?c[b+20>>2]:e)>>>0)?e:p){if(!(E=Ye(l=l?c[y>>2]:y,_=_?c[b+16>>2]:b+16|0,U))){if(e>>>0>p>>>0)break b;break o}if((0|E)>=0)break o}else if(e>>>0<=p>>>0)break A;_=c[b>>2];b:{u:if((0|(r=b))!=c[R>>2]){if(_)for(e=_;r=e,e=c[e+4>>2];);else for(e=b;r=c[e+8>>2],p=c[r>>2]==(0|e),e=r,p;);if(e=(0|(N=(p=k[y+11|0])<<24>>24))<0,d=(l=k[r+27|0])<<24>>24<0,!(U=(p=e?c[y+4>>2]:p)>>>0<(l=d?c[r+20>>2]:l)>>>0?p:l)||!(e=Ye(d?c[r+16>>2]:r+16|0,e?c[y>>2]:y,U))){if(p>>>0>l>>>0)break u;break b}if((0|e)>=0)break b}if(!_){c[v+12>>2]=b,r=b;break i}c[v+12>>2]=r,r=r+4|0;break i}if(!(e=c[A>>2])){c[v+12>>2]=A,r=A;break i}for(_=(0|N)<0?c[y>>2]:y,b=A;;){r=e;b:{u:{c:{k:{_:if(d=(y=(e=(A=(e=k[e+27|0])<<24>>24<0)?c[r+20>>2]:e)>>>0<p>>>0)?e:p){if(!(l=Ye(_,A=A?c[r+16>>2]:r+16|0,d))){if(e>>>0>p>>>0)break _;break k}if((0|l)>=0)break k}else if(e>>>0<=p>>>0)break c;if(b=r,e=c[r>>2])continue;break f}if(e=Ye(A,_,d))break u}if(y)break b;break f}if((0|e)>=0)break f}if(b=r+4|0,!(e=c[r+4>>2]))break}break f}if(e=Ye(_,l,U))break n}if(N)break a;break t}if((0|e)>=0)break t}if(_=c[b+4>>2])for(e=_;r=e,e=c[e>>2];);else for(e=b;r=c[e+8>>2],l=c[r>>2]!=(0|e),e=r,l;);a:{n:if((0|r)!=(0|A)){if(!(N=p>>>0>(l=(e=(l=k[r+27|0])<<24>>24<0)?c[r+20>>2]:l)>>>0?l:p)||!(e=Ye((0|d)<0?c[y>>2]:y,e?c[r+16>>2]:r+16|0,N))){if(p>>>0<l>>>0)break n;break a}if((0|e)>=0)break a}if(!_){c[v+12>>2]=b,r=b+4|0;break i}c[v+12>>2]=r;break i}if(!(e=c[A>>2])){c[v+12>>2]=A,r=A;break i}for(_=(0|d)<0?c[y>>2]:y,b=A;;){r=e;a:{n:{A:{o:{b:if(d=(y=(e=(A=(e=k[e+27|0])<<24>>24<0)?c[r+20>>2]:e)>>>0<p>>>0)?e:p){if(!(l=Ye(_,A=A?c[r+16>>2]:r+16|0,d))){if(e>>>0>p>>>0)break b;break o}if((0|l)>=0)break o}else if(e>>>0<=p>>>0)break A;if(b=r,e=c[r>>2])continue;break f}if(e=Ye(A,_,d))break n}if(y)break a;break f}if((0|e)>=0)break f}if(b=r+4|0,!(e=c[r+4>>2]))break}}c[v+12>>2]=r,r=b;break i}c[v+12>>2]=b,c[r>>2]=b}b=r,(r=c[r>>2])?e=0:(e=(r=vi(40))+16|0,n[f+27|0]>=0?(A=c[f+20>>2],c[e>>2]=c[f+16>>2],c[e+4>>2]=A,c[e+8>>2]=c[f+24>>2]):me(e,c[f+16>>2],c[f+20>>2]),e=r+28|0,n[f+39|0]>=0?(A=c[f+32>>2],c[e>>2]=c[f+28>>2],c[e+4>>2]=A,c[e+8>>2]=c[f+36>>2]):me(e,c[f+28>>2],c[f+32>>2]),c[r+8>>2]=c[v+12>>2],c[r>>2]=0,c[r+4>>2]=0,c[b>>2]=r,e=r,(A=c[c[R>>2]>>2])&&(c[R>>2]=A,e=c[b>>2]),Ur(c[R+4>>2],e),c[R+8>>2]=c[R+8>>2]+1,e=1),n[W+12|0]=e,c[W+8>>2]=r,Z=v+16|0;i:{if(e=c[f+4>>2])for(;;)if(f=e,!(e=c[e>>2]))break i;for(;r=f,f=c[f+8>>2],(0|r)!=c[f>>2];);}if((0|f)==(0|G))break}c[u+8>>2]=a,c[u>>2]=0,c[u+4>>2]=0,c[m>>2]=u,f=u,(r=c[c[V>>2]>>2])&&(c[V>>2]=r,f=c[m>>2]),Ur(c[V+4>>2],f),c[V+8>>2]=c[V+8>>2]+1,r=1}n[i+44|0]=r,c[i+40>>2]=u,Z=W+16|0,f=c[i+40>>2],hi(4|T,c[i+16>>2]),hi(D,c[i+28>>2])}if(a=(b=Z-48|0)+8|0,Z=u=(Z=b)-32|0,(0|(A=(e=m=u+32|0)-(r=u+21|0)|0))<=9&&(_=61,(0|A)<(s[2684]<=1|0))||(n[0|r]=49,e=r+1|0,_=0),c[u+12>>2]=_,c[u+8>>2]=e,Z=A=(Z=_=Z-16|0)-16|0,(u=(h=c[u+8>>2])-r|0)>>>0<=2147483631){for(u>>>0<11?(n[a+11|0]=u|128&k[a+11|0],n[a+11|0]=127&k[a+11|0],e=a):(Ci(T=A+8|0,(e=u>>>0>=11?11==(0|(e=(y=u+16&-16)-1|0))?y:e:10)+1|0),e=c[A+8>>2],c[a>>2]=e,c[a+8>>2]=-2147483648&c[a+8>>2]|2147483647&c[A+12>>2],c[a+8>>2]=-2147483648|c[a+8>>2],c[a+4>>2]=u);(0|r)!=(0|h);)n[0|e]=k[0|r],e=e+1|0,r=r+1|0;n[A+7|0]=0,n[0|e]=k[A+7|0],Z=A+16|0}else yt(),o();Z=_+16|0,Z=m,c[b+32>>2]=t;r:{e:{i:if(a=c[(r=f+20|0)+4>>2]){for(A=(f=(e=k[t+11|0])<<24>>24<0)?c[t>>2]:t,e=f?c[t+4>>2]:e;;){t:{f:{a:{n:{A:if(_=(m=(a=(u=(a=k[(f=a)+27|0])<<24>>24<0)?c[f+20>>2]:a)>>>0<e>>>0)?a:e){if(!(h=Ye(A,u=u?c[f+16>>2]:f+16|0,_))){if(e>>>0<a>>>0)break A;break n}if((0|h)>=0)break n}else if(e>>>0>=a>>>0)break a;if(u=f,a=c[f>>2])continue;break i}if(a=Ye(u,A,_))break f}if(m)break t;break e}if((0|a)>=0)break e}if(!(a=c[f+4>>2]))break}u=f+4|0}else f=u=r+4|0;A=(a=vi(40))+16|0,e=c[b+32>>2],n[e+11|0]>=0?(m=c[e+4>>2],c[A>>2]=c[e>>2],c[A+4>>2]=m,c[A+8>>2]=c[e+8>>2]):me(A,c[e>>2],c[e+4>>2]),c[a+8>>2]=f,c[a>>2]=0,c[a+4>>2]=0,c[a+36>>2]=0,c[a+28>>2]=0,c[a+32>>2]=0,c[u>>2]=a,f=a,(e=c[c[r>>2]>>2])&&(c[r>>2]=e,f=c[u>>2]),Ur(c[r+4>>2],f),c[r+8>>2]=c[r+8>>2]+1,r=1;break r}a=f,r=0}n[b+44|0]=r,c[b+40>>2]=a,r=c[b+40>>2],n[r+39|0]<0&&er(c[r+28>>2]),e=c[b+12>>2],c[r+28>>2]=c[b+8>>2],c[r+32>>2]=e,c[r+36>>2]=c[b+16>>2],Z=b+48|0,Z=i+48|0,n[t+11|0]<0&&er(c[t>>2]),Z=t+16|0},gb:function(r,e){r|=0;var i,t,f=0,a=0;a=(t=Z-32|0)+8|0,Z=i=(Z=t)-80|0,r=c[(e|=0)+36>>2],c[i+72>>2]=c[e+32>>2],c[i+76>>2]=r,f=c[e+28>>2],c[(r=i- -64|0)>>2]=c[e+24>>2],c[r+4>>2]=f,r=c[e+20>>2],c[i+56>>2]=c[e+16>>2],c[i+60>>2]=r,r=c[e+12>>2],c[i+48>>2]=c[e+8>>2],c[i+52>>2]=r,r=c[e+4>>2],c[i+40>>2]=c[e>>2],c[i+44>>2]=r,P(i+8|0,i+40|0,i+24|0);r:if(r=c[i+8>>2]){if(c[a>>2]=r,r=a+4|0,n[i+23|0]>=0){a=c[(e=i+8|4)+4>>2],c[r>>2]=c[e>>2],c[r+4>>2]=a,c[r+8>>2]=c[e+8>>2];break r}if(me(r,c[i+12>>2],c[i+16>>2]),n[i+23|0]>=0)break r;er(c[i+12>>2])}else if(n[i+23|0]<0&&er(c[i+12>>2]),(r=k[i+31|0])>>>0>=2){if(e=vi(32),n[e+26|0]=0,r=k[1475]|k[1476]<<8,n[e+24|0]=r,n[e+25|0]=r>>>8,r=k[1471]|k[1472]<<8|k[1473]<<16|k[1474]<<24,f=k[1467]|k[1468]<<8|k[1469]<<16|k[1470]<<24,n[e+16|0]=f,n[e+17|0]=f>>>8,n[e+18|0]=f>>>16,n[e+19|0]=f>>>24,n[e+20|0]=r,n[e+21|0]=r>>>8,n[e+22|0]=r>>>16,n[e+23|0]=r>>>24,r=k[1463]|k[1464]<<8|k[1465]<<16|k[1466]<<24,f=k[1459]|k[1460]<<8|k[1461]<<16|k[1462]<<24,n[e+8|0]=f,n[e+9|0]=f>>>8,n[e+10|0]=f>>>16,n[e+11|0]=f>>>24,n[e+12|0]=r,n[e+13|0]=r>>>8,n[e+14|0]=r>>>16,n[e+15|0]=r>>>24,r=k[1455]|k[1456]<<8|k[1457]<<16|k[1458]<<24,f=k[1451]|k[1452]<<8|k[1453]<<16|k[1454]<<24,n[0|e]=f,n[e+1|0]=f>>>8,n[e+2|0]=f>>>16,n[e+3|0]=f>>>24,n[e+4|0]=r,n[e+5|0]=r>>>8,n[e+6|0]=r>>>16,n[e+7|0]=r>>>24,c[i+8>>2]=-1,me(r=i+8|4,e,26),f=n[i+23|0],c[a>>2]=c[i+8>>2],a=a+4|0,(0|f)>=0){f=c[r+4>>2],c[a>>2]=c[r>>2],c[a+4>>2]=f,c[a+8>>2]=c[r+8>>2],er(e);break r}me(a,c[i+12>>2],c[i+16>>2]),n[i+23|0]<0&&er(c[i+12>>2]),er(e)}else c[a>>2]=0,c[a+4>>2]=0,c[a+16>>2]=r,c[a+8>>2]=0,c[a+12>>2]=0;return Z=i+80|0,r=c[t+24>>2],n[t+23|0]<0&&er(c[t+12>>2]),Z=t+32|0,0|r},hb:function(r,e,i){r|=0,e|=0,i|=0;var t,f=0,a=0,A=0;Z=e=Z-16|0,Qr(e),c[r+24>>2]=c[e>>2];r:if((0|(t=r+24|0))!=(0|e))if(i=r+28|0,f=4|e,A=(a=k[e+15|0])<<24>>24,n[r+39|0]>=0){if((0|A)>=0){r=c[f+4>>2],c[i>>2]=c[f>>2],c[i+4>>2]=r,c[i+8>>2]=c[f+8>>2];break r}ri(i,c[e+4>>2],c[e+8>>2])}else qe(i,(r=(0|A)<0)?c[e+4>>2]:f,r?c[e+8>>2]:a);return n[e+15|0]<0&&er(c[e+4>>2]),Z=e+16|0,0|t},ib:function(r,e,i){var t,f,a=0,A=0;Z=t=Z-16|0,I(t,r|=0,e|=0,i|=0),c[r+24>>2]=c[t>>2];r:if((0|(f=r+24|0))!=(0|t))if(e=r+28|0,i=4|t,A=(a=k[t+15|0])<<24>>24,n[r+39|0]>=0){if((0|A)>=0){r=c[i+4>>2],c[e>>2]=c[i>>2],c[e+4>>2]=r,c[e+8>>2]=c[i+8>>2];break r}ri(e,c[t+4>>2],c[t+8>>2])}else qe(e,(r=(0|A)<0)?c[t+4>>2]:i,r?c[t+8>>2]:a);return n[t+15|0]<0&&er(c[t+4>>2]),Z=t+16|0,0|f},jb:function(r){(r|=0)&&(n[r+39|0]<0&&er(c[r+28>>2]),Fi(r+12|0,c[r+16>>2]),hi(r,c[r+4>>2]),er(r))},kb:Rt,lb:Tt,mb:Nt,nb:Wt,ob:Rt,pb:Tt,qb:Nt,rb:Wt,sb:Vt,tb:Ut,ub:Rt,vb:Tt,wb:Nt,xb:Tt,yb:Nt,zb:Wt,Ab:Vt,Bb:Ut,Cb:function(){return 5},Db:function(){return 6},Eb:function(){return 7},Fb:function(){return 8},Gb:function(){return 9},Hb:function(){return 10},Ib:function(){return 11},Jb:function(){return 12},Kb:Tt,Lb:Rt,Mb:function(){return-2},Nb:function(){return-3},Ob:function(){return-4},Pb:function(){return-5},Qb:Y,Rb:er,Sb:function(r){return(r|=0)?0!=(0|Xr(r,11164))|0:0}}}(r)}(e)},instantiate:function(r,e){return{then:function(i){var t=new h.Module(r);i({instance:new h.Instance(t,e)})}}},RuntimeError:Error};y=[],"object"!=typeof h&&Q("no native wasm support detected");var R=!1;function N(r,e){r||Q(e)}var T,V,U,W,D="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function G(r,e){return r?function(r,e,i){for(var t=e+i,f=e;r[f]&&!(f>=t);)++f;if(f-e>16&&r.buffer&&D)return D.decode(r.subarray(e,f));for(var a="";e<f;){var n=r[e++];if(128&n){var A=63&r[e++];if(192!=(224&n)){var o=63&r[e++];if((n=224==(240&n)?(15&n)<<12|A<<6|o:(7&n)<<18|A<<12|o<<6|63&r[e++])<65536)a+=String.fromCharCode(n);else{var b=n-65536;a+=String.fromCharCode(55296|b>>10,56320|1023&b)}}else a+=String.fromCharCode((31&n)<<6|A)}else a+=String.fromCharCode(n)}return a}(V,r,e):""}function Z(){var r=m.buffer;f.HEAP8=T=new Int8Array(r),f.HEAP16=new Int16Array(r),f.HEAP32=U=new Int32Array(r),f.HEAPU8=V=new Uint8Array(r),f.HEAPU16=new Uint16Array(r),f.HEAPU32=W=new Uint32Array(r),f.HEAPF32=new Float32Array(r),f.HEAPF64=new Float64Array(r)}var E=f.INITIAL_MEMORY||16777216;N(E>=65536,"INITIAL_MEMORY should be larger than STACK_SIZE, was "+E+"! (STACK_SIZE=65536)"),m=f.wasmMemory?f.wasmMemory:new h.Memory({initial:E/65536,maximum:32768}),Z(),E=m.buffer.byteLength;var F=[],I=[],Y=[],w=!1;function j(r){I.unshift(r)}var J=0,B=null,M=null;function Q(r){f.onAbort&&f.onAbort(r),d(r="Aborted("+r+")"),R=!0,1,r+=". Build with -sASSERTIONS for more info.";var e=new h.RuntimeError(r);throw t(e),e}var g,O,C="data:application/octet-stream;base64,";function z(r){return r.startsWith(C)}function X(r){return r.startsWith("file://")}function S(r){try{if(r==g&&y)return new Uint8Array(y);var e=q(r);if(e)return e;if(b)return b(r);throw"both async and sync fetching of the wasm failed"}catch(r){Q(r)}}function H(r){this.name="ExitStatus",this.message="Program terminated with exit("+r+")",this.status=r}function P(r){for(;r.length>0;)r.shift()(f)}function x(r){this.excPtr=r,this.ptr=r-24,this.set_type=function(r){W[this.ptr+4>>2]=r},this.get_type=function(){return W[this.ptr+4>>2]},this.set_destructor=function(r){W[this.ptr+8>>2]=r},this.get_destructor=function(){return W[this.ptr+8>>2]},this.set_refcount=function(r){U[this.ptr>>2]=r},this.set_caught=function(r){r=r?1:0,T[this.ptr+12>>0]=r},this.get_caught=function(){return 0!=T[this.ptr+12>>0]},this.set_rethrown=function(r){r=r?1:0,T[this.ptr+13>>0]=r},this.get_rethrown=function(){return 0!=T[this.ptr+13>>0]},this.init=function(r,e){this.set_adjusted_ptr(0),this.set_type(r),this.set_destructor(e),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var r=U[this.ptr>>2];U[this.ptr>>2]=r+1},this.release_ref=function(){var r=U[this.ptr>>2];return U[this.ptr>>2]=r-1,1===r},this.set_adjusted_ptr=function(r){W[this.ptr+16>>2]=r},this.get_adjusted_ptr=function(){return W[this.ptr+16>>2]},this.get_exception_ptr=function(){if(ji(this.get_type()))return W[this.excPtr>>2];var r=this.get_adjusted_ptr();return 0!==r?r:this.excPtr}}z(g="draco_decoder_gltf.wasm")||(O=g,g=f.locateFile?f.locateFile(O,s):s+O);function L(r){var e=m.buffer;try{return m.grow(r-e.byteLength+65535>>>16),Z(),1}catch(r){}}var K="function"==typeof atob?atob:function(r){var e,i,t,f,a,n,A="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",o="",b=0;r=r.replace(/[^A-Za-z0-9\+\/\=]/g,"");do{e=A.indexOf(r.charAt(b++))<<2|(f=A.indexOf(r.charAt(b++)))>>4,i=(15&f)<<4|(a=A.indexOf(r.charAt(b++)))>>2,t=(3&a)<<6|(n=A.indexOf(r.charAt(b++))),o+=String.fromCharCode(e),64!==a&&(o+=String.fromCharCode(i)),64!==n&&(o+=String.fromCharCode(t))}while(b<r.length);return o};function q(r){if(z(r))return function(r){if("boolean"==typeof _&&_){var e=Buffer.from(r,"base64");return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}try{for(var i=K(r),t=new Uint8Array(i.length),f=0;f<i.length;++f)t[f]=i.charCodeAt(f);return t}catch(r){throw new Error("Converting base64 string to bytes failed.")}}(r.slice(C.length))}var $,rr={c:function(r,e,i){throw new x(r).init(e,i),r,r},b:function(){Q("")},e:function(r,e,i){V.copyWithin(r,e,e+i)},d:function(r){var e,i,t=V.length,f=2147483648;if((r>>>=0)>f)return!1;for(var a=1;a<=4;a*=2){var n=t*(1+.2/a);if(n=Math.min(n,r+100663296),L(Math.min(f,(e=Math.max(r,n))+((i=65536)-e%i)%i)))return!0}return!1},a:m},er=(function(){var r={a:rr};function e(r,e){var i=r.exports;f.asm=i,f.asm.g,j(f.asm.f),function(r){if(J--,f.monitorRunDependencies&&f.monitorRunDependencies(J),0==J&&(null!==B&&(clearInterval(B),B=null),M)){var e=M;M=null,e()}}()}function i(r){e(r.instance)}function a(e){return function(){if(!y&&(c||k)){if("function"==typeof fetch&&!X(g))return fetch(g,{credentials:"same-origin"}).then((function(r){if(!r.ok)throw"failed to load wasm binary file at '"+g+"'";return r.arrayBuffer()})).catch((function(){return S(g)}));if(o)return new Promise((function(r,e){o(g,(function(e){r(new Uint8Array(e))}),e)}))}return Promise.resolve().then((function(){return S(g)}))}().then((function(e){return h.instantiate(e,r)})).then((function(r){return r})).then(e,(function(r){d("failed to asynchronously prepare wasm: "+r),Q(r)}))}if(J++,f.monitorRunDependencies&&f.monitorRunDependencies(J),f.instantiateWasm)try{return f.instantiateWasm(r,e)}catch(r){d("Module.instantiateWasm callback failed with error: "+r),t(r)}(y||"function"!=typeof h.instantiateStreaming||z(g)||X(g)||_||"function"!=typeof fetch?a(i):fetch(g,{credentials:"same-origin"}).then((function(e){return h.instantiateStreaming(e,r).then(i,(function(r){return d("wasm streaming compile failed: "+r),d("falling back to ArrayBuffer instantiation"),a(i)}))}))).catch(t)}(),f._emscripten_bind_VoidPtr___destroy___0=function(){return(er=f._emscripten_bind_VoidPtr___destroy___0=f.asm.h).apply(null,arguments)}),ir=f._emscripten_bind_DecoderBuffer_DecoderBuffer_0=function(){return(ir=f._emscripten_bind_DecoderBuffer_DecoderBuffer_0=f.asm.i).apply(null,arguments)},tr=f._emscripten_bind_DecoderBuffer_Init_2=function(){return(tr=f._emscripten_bind_DecoderBuffer_Init_2=f.asm.j).apply(null,arguments)},fr=f._emscripten_bind_DecoderBuffer___destroy___0=function(){return(fr=f._emscripten_bind_DecoderBuffer___destroy___0=f.asm.k).apply(null,arguments)},ar=f._emscripten_bind_AttributeTransformData_AttributeTransformData_0=function(){return(ar=f._emscripten_bind_AttributeTransformData_AttributeTransformData_0=f.asm.l).apply(null,arguments)},nr=f._emscripten_bind_AttributeTransformData_transform_type_0=function(){return(nr=f._emscripten_bind_AttributeTransformData_transform_type_0=f.asm.m).apply(null,arguments)},Ar=f._emscripten_bind_AttributeTransformData___destroy___0=function(){return(Ar=f._emscripten_bind_AttributeTransformData___destroy___0=f.asm.n).apply(null,arguments)},or=f._emscripten_bind_GeometryAttribute_GeometryAttribute_0=function(){return(or=f._emscripten_bind_GeometryAttribute_GeometryAttribute_0=f.asm.o).apply(null,arguments)},br=f._emscripten_bind_GeometryAttribute___destroy___0=function(){return(br=f._emscripten_bind_GeometryAttribute___destroy___0=f.asm.p).apply(null,arguments)},ur=f._emscripten_bind_PointAttribute_PointAttribute_0=function(){return(ur=f._emscripten_bind_PointAttribute_PointAttribute_0=f.asm.q).apply(null,arguments)},cr=f._emscripten_bind_PointAttribute_size_0=function(){return(cr=f._emscripten_bind_PointAttribute_size_0=f.asm.r).apply(null,arguments)},kr=f._emscripten_bind_PointAttribute_GetAttributeTransformData_0=function(){return(kr=f._emscripten_bind_PointAttribute_GetAttributeTransformData_0=f.asm.s).apply(null,arguments)},_r=f._emscripten_bind_PointAttribute_attribute_type_0=function(){return(_r=f._emscripten_bind_PointAttribute_attribute_type_0=f.asm.t).apply(null,arguments)},sr=f._emscripten_bind_PointAttribute_data_type_0=function(){return(sr=f._emscripten_bind_PointAttribute_data_type_0=f.asm.u).apply(null,arguments)},pr=f._emscripten_bind_PointAttribute_num_components_0=function(){return(pr=f._emscripten_bind_PointAttribute_num_components_0=f.asm.v).apply(null,arguments)},lr=f._emscripten_bind_PointAttribute_normalized_0=function(){return(lr=f._emscripten_bind_PointAttribute_normalized_0=f.asm.w).apply(null,arguments)},yr=f._emscripten_bind_PointAttribute_byte_stride_0=function(){return(yr=f._emscripten_bind_PointAttribute_byte_stride_0=f.asm.x).apply(null,arguments)},dr=f._emscripten_bind_PointAttribute_byte_offset_0=function(){return(dr=f._emscripten_bind_PointAttribute_byte_offset_0=f.asm.y).apply(null,arguments)},mr=f._emscripten_bind_PointAttribute_unique_id_0=function(){return(mr=f._emscripten_bind_PointAttribute_unique_id_0=f.asm.z).apply(null,arguments)},vr=f._emscripten_bind_PointAttribute___destroy___0=function(){return(vr=f._emscripten_bind_PointAttribute___destroy___0=f.asm.A).apply(null,arguments)},hr=f._emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=function(){return(hr=f._emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=f.asm.B).apply(null,arguments)},Rr=f._emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=function(){return(Rr=f._emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=f.asm.C).apply(null,arguments)},Nr=f._emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=function(){return(Nr=f._emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=f.asm.D).apply(null,arguments)},Tr=f._emscripten_bind_AttributeQuantizationTransform_min_value_1=function(){return(Tr=f._emscripten_bind_AttributeQuantizationTransform_min_value_1=f.asm.E).apply(null,arguments)},Vr=f._emscripten_bind_AttributeQuantizationTransform_range_0=function(){return(Vr=f._emscripten_bind_AttributeQuantizationTransform_range_0=f.asm.F).apply(null,arguments)},Ur=f._emscripten_bind_AttributeQuantizationTransform___destroy___0=function(){return(Ur=f._emscripten_bind_AttributeQuantizationTransform___destroy___0=f.asm.G).apply(null,arguments)},Wr=f._emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=function(){return(Wr=f._emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=f.asm.H).apply(null,arguments)},Dr=f._emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=function(){return(Dr=f._emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=f.asm.I).apply(null,arguments)},Gr=f._emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=function(){return(Gr=f._emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=f.asm.J).apply(null,arguments)},Zr=f._emscripten_bind_AttributeOctahedronTransform___destroy___0=function(){return(Zr=f._emscripten_bind_AttributeOctahedronTransform___destroy___0=f.asm.K).apply(null,arguments)},Er=f._emscripten_bind_PointCloud_PointCloud_0=function(){return(Er=f._emscripten_bind_PointCloud_PointCloud_0=f.asm.L).apply(null,arguments)},Fr=f._emscripten_bind_PointCloud_num_attributes_0=function(){return(Fr=f._emscripten_bind_PointCloud_num_attributes_0=f.asm.M).apply(null,arguments)},Ir=f._emscripten_bind_PointCloud_num_points_0=function(){return(Ir=f._emscripten_bind_PointCloud_num_points_0=f.asm.N).apply(null,arguments)},Yr=f._emscripten_bind_PointCloud___destroy___0=function(){return(Yr=f._emscripten_bind_PointCloud___destroy___0=f.asm.O).apply(null,arguments)},wr=f._emscripten_bind_Mesh_Mesh_0=function(){return(wr=f._emscripten_bind_Mesh_Mesh_0=f.asm.P).apply(null,arguments)},jr=f._emscripten_bind_Mesh_num_faces_0=function(){return(jr=f._emscripten_bind_Mesh_num_faces_0=f.asm.Q).apply(null,arguments)},Jr=f._emscripten_bind_Mesh_num_attributes_0=function(){return(Jr=f._emscripten_bind_Mesh_num_attributes_0=f.asm.R).apply(null,arguments)},Br=f._emscripten_bind_Mesh_num_points_0=function(){return(Br=f._emscripten_bind_Mesh_num_points_0=f.asm.S).apply(null,arguments)},Mr=f._emscripten_bind_Mesh___destroy___0=function(){return(Mr=f._emscripten_bind_Mesh___destroy___0=f.asm.T).apply(null,arguments)},Qr=f._emscripten_bind_Metadata_Metadata_0=function(){return(Qr=f._emscripten_bind_Metadata_Metadata_0=f.asm.U).apply(null,arguments)},gr=f._emscripten_bind_Metadata___destroy___0=function(){return(gr=f._emscripten_bind_Metadata___destroy___0=f.asm.V).apply(null,arguments)},Or=f._emscripten_bind_Status_code_0=function(){return(Or=f._emscripten_bind_Status_code_0=f.asm.W).apply(null,arguments)},Cr=f._emscripten_bind_Status_ok_0=function(){return(Cr=f._emscripten_bind_Status_ok_0=f.asm.X).apply(null,arguments)},zr=f._emscripten_bind_Status_error_msg_0=function(){return(zr=f._emscripten_bind_Status_error_msg_0=f.asm.Y).apply(null,arguments)},Xr=f._emscripten_bind_Status___destroy___0=function(){return(Xr=f._emscripten_bind_Status___destroy___0=f.asm.Z).apply(null,arguments)},Sr=f._emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=function(){return(Sr=f._emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=f.asm._).apply(null,arguments)},Hr=f._emscripten_bind_DracoFloat32Array_GetValue_1=function(){return(Hr=f._emscripten_bind_DracoFloat32Array_GetValue_1=f.asm.$).apply(null,arguments)},Pr=f._emscripten_bind_DracoFloat32Array_size_0=function(){return(Pr=f._emscripten_bind_DracoFloat32Array_size_0=f.asm.aa).apply(null,arguments)},xr=f._emscripten_bind_DracoFloat32Array___destroy___0=function(){return(xr=f._emscripten_bind_DracoFloat32Array___destroy___0=f.asm.ba).apply(null,arguments)},Lr=f._emscripten_bind_DracoInt8Array_DracoInt8Array_0=function(){return(Lr=f._emscripten_bind_DracoInt8Array_DracoInt8Array_0=f.asm.ca).apply(null,arguments)},Kr=f._emscripten_bind_DracoInt8Array_GetValue_1=function(){return(Kr=f._emscripten_bind_DracoInt8Array_GetValue_1=f.asm.da).apply(null,arguments)},qr=f._emscripten_bind_DracoInt8Array_size_0=function(){return(qr=f._emscripten_bind_DracoInt8Array_size_0=f.asm.ea).apply(null,arguments)},$r=f._emscripten_bind_DracoInt8Array___destroy___0=function(){return($r=f._emscripten_bind_DracoInt8Array___destroy___0=f.asm.fa).apply(null,arguments)},re=f._emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=function(){return(re=f._emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=f.asm.ga).apply(null,arguments)},ee=f._emscripten_bind_DracoUInt8Array_GetValue_1=function(){return(ee=f._emscripten_bind_DracoUInt8Array_GetValue_1=f.asm.ha).apply(null,arguments)},ie=f._emscripten_bind_DracoUInt8Array_size_0=function(){return(ie=f._emscripten_bind_DracoUInt8Array_size_0=f.asm.ia).apply(null,arguments)},te=f._emscripten_bind_DracoUInt8Array___destroy___0=function(){return(te=f._emscripten_bind_DracoUInt8Array___destroy___0=f.asm.ja).apply(null,arguments)},fe=f._emscripten_bind_DracoInt16Array_DracoInt16Array_0=function(){return(fe=f._emscripten_bind_DracoInt16Array_DracoInt16Array_0=f.asm.ka).apply(null,arguments)},ae=f._emscripten_bind_DracoInt16Array_GetValue_1=function(){return(ae=f._emscripten_bind_DracoInt16Array_GetValue_1=f.asm.la).apply(null,arguments)},ne=f._emscripten_bind_DracoInt16Array_size_0=function(){return(ne=f._emscripten_bind_DracoInt16Array_size_0=f.asm.ma).apply(null,arguments)},Ae=f._emscripten_bind_DracoInt16Array___destroy___0=function(){return(Ae=f._emscripten_bind_DracoInt16Array___destroy___0=f.asm.na).apply(null,arguments)},oe=f._emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=function(){return(oe=f._emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=f.asm.oa).apply(null,arguments)},be=f._emscripten_bind_DracoUInt16Array_GetValue_1=function(){return(be=f._emscripten_bind_DracoUInt16Array_GetValue_1=f.asm.pa).apply(null,arguments)},ue=f._emscripten_bind_DracoUInt16Array_size_0=function(){return(ue=f._emscripten_bind_DracoUInt16Array_size_0=f.asm.qa).apply(null,arguments)},ce=f._emscripten_bind_DracoUInt16Array___destroy___0=function(){return(ce=f._emscripten_bind_DracoUInt16Array___destroy___0=f.asm.ra).apply(null,arguments)},ke=f._emscripten_bind_DracoInt32Array_DracoInt32Array_0=function(){return(ke=f._emscripten_bind_DracoInt32Array_DracoInt32Array_0=f.asm.sa).apply(null,arguments)},_e=f._emscripten_bind_DracoInt32Array_GetValue_1=function(){return(_e=f._emscripten_bind_DracoInt32Array_GetValue_1=f.asm.ta).apply(null,arguments)},se=f._emscripten_bind_DracoInt32Array_size_0=function(){return(se=f._emscripten_bind_DracoInt32Array_size_0=f.asm.ua).apply(null,arguments)},pe=f._emscripten_bind_DracoInt32Array___destroy___0=function(){return(pe=f._emscripten_bind_DracoInt32Array___destroy___0=f.asm.va).apply(null,arguments)},le=f._emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=function(){return(le=f._emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=f.asm.wa).apply(null,arguments)},ye=f._emscripten_bind_DracoUInt32Array_GetValue_1=function(){return(ye=f._emscripten_bind_DracoUInt32Array_GetValue_1=f.asm.xa).apply(null,arguments)},de=f._emscripten_bind_DracoUInt32Array_size_0=function(){return(de=f._emscripten_bind_DracoUInt32Array_size_0=f.asm.ya).apply(null,arguments)},me=f._emscripten_bind_DracoUInt32Array___destroy___0=function(){return(me=f._emscripten_bind_DracoUInt32Array___destroy___0=f.asm.za).apply(null,arguments)},ve=f._emscripten_bind_MetadataQuerier_MetadataQuerier_0=function(){return(ve=f._emscripten_bind_MetadataQuerier_MetadataQuerier_0=f.asm.Aa).apply(null,arguments)},he=f._emscripten_bind_MetadataQuerier_HasEntry_2=function(){return(he=f._emscripten_bind_MetadataQuerier_HasEntry_2=f.asm.Ba).apply(null,arguments)},Re=f._emscripten_bind_MetadataQuerier_GetIntEntry_2=function(){return(Re=f._emscripten_bind_MetadataQuerier_GetIntEntry_2=f.asm.Ca).apply(null,arguments)},Ne=f._emscripten_bind_MetadataQuerier_GetIntEntryArray_3=function(){return(Ne=f._emscripten_bind_MetadataQuerier_GetIntEntryArray_3=f.asm.Da).apply(null,arguments)},Te=f._emscripten_bind_MetadataQuerier_GetDoubleEntry_2=function(){return(Te=f._emscripten_bind_MetadataQuerier_GetDoubleEntry_2=f.asm.Ea).apply(null,arguments)},Ve=f._emscripten_bind_MetadataQuerier_GetStringEntry_2=function(){return(Ve=f._emscripten_bind_MetadataQuerier_GetStringEntry_2=f.asm.Fa).apply(null,arguments)},Ue=f._emscripten_bind_MetadataQuerier_NumEntries_1=function(){return(Ue=f._emscripten_bind_MetadataQuerier_NumEntries_1=f.asm.Ga).apply(null,arguments)},We=f._emscripten_bind_MetadataQuerier_GetEntryName_2=function(){return(We=f._emscripten_bind_MetadataQuerier_GetEntryName_2=f.asm.Ha).apply(null,arguments)},De=f._emscripten_bind_MetadataQuerier___destroy___0=function(){return(De=f._emscripten_bind_MetadataQuerier___destroy___0=f.asm.Ia).apply(null,arguments)},Ge=f._emscripten_bind_Decoder_Decoder_0=function(){return(Ge=f._emscripten_bind_Decoder_Decoder_0=f.asm.Ja).apply(null,arguments)},Ze=f._emscripten_bind_Decoder_DecodeArrayToPointCloud_3=function(){return(Ze=f._emscripten_bind_Decoder_DecodeArrayToPointCloud_3=f.asm.Ka).apply(null,arguments)},Ee=f._emscripten_bind_Decoder_DecodeArrayToMesh_3=function(){return(Ee=f._emscripten_bind_Decoder_DecodeArrayToMesh_3=f.asm.La).apply(null,arguments)},Fe=f._emscripten_bind_Decoder_GetAttributeId_2=function(){return(Fe=f._emscripten_bind_Decoder_GetAttributeId_2=f.asm.Ma).apply(null,arguments)},Ie=f._emscripten_bind_Decoder_GetAttributeIdByName_2=function(){return(Ie=f._emscripten_bind_Decoder_GetAttributeIdByName_2=f.asm.Na).apply(null,arguments)},Ye=f._emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=function(){return(Ye=f._emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=f.asm.Oa).apply(null,arguments)},we=f._emscripten_bind_Decoder_GetAttribute_2=function(){return(we=f._emscripten_bind_Decoder_GetAttribute_2=f.asm.Pa).apply(null,arguments)},je=f._emscripten_bind_Decoder_GetAttributeByUniqueId_2=function(){return(je=f._emscripten_bind_Decoder_GetAttributeByUniqueId_2=f.asm.Qa).apply(null,arguments)},Je=f._emscripten_bind_Decoder_GetMetadata_1=function(){return(Je=f._emscripten_bind_Decoder_GetMetadata_1=f.asm.Ra).apply(null,arguments)},Be=f._emscripten_bind_Decoder_GetAttributeMetadata_2=function(){return(Be=f._emscripten_bind_Decoder_GetAttributeMetadata_2=f.asm.Sa).apply(null,arguments)},Me=f._emscripten_bind_Decoder_GetFaceFromMesh_3=function(){return(Me=f._emscripten_bind_Decoder_GetFaceFromMesh_3=f.asm.Ta).apply(null,arguments)},Qe=f._emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=function(){return(Qe=f._emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=f.asm.Ua).apply(null,arguments)},ge=f._emscripten_bind_Decoder_GetTrianglesUInt16Array_3=function(){return(ge=f._emscripten_bind_Decoder_GetTrianglesUInt16Array_3=f.asm.Va).apply(null,arguments)},Oe=f._emscripten_bind_Decoder_GetTrianglesUInt32Array_3=function(){return(Oe=f._emscripten_bind_Decoder_GetTrianglesUInt32Array_3=f.asm.Wa).apply(null,arguments)},Ce=f._emscripten_bind_Decoder_GetAttributeFloat_3=function(){return(Ce=f._emscripten_bind_Decoder_GetAttributeFloat_3=f.asm.Xa).apply(null,arguments)},ze=f._emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=function(){return(ze=f._emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=f.asm.Ya).apply(null,arguments)},Xe=f._emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=function(){return(Xe=f._emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=f.asm.Za).apply(null,arguments)},Se=f._emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=function(){return(Se=f._emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=f.asm._a).apply(null,arguments)},He=f._emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=function(){return(He=f._emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=f.asm.$a).apply(null,arguments)},Pe=f._emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=function(){return(Pe=f._emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=f.asm.ab).apply(null,arguments)},xe=f._emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=function(){return(xe=f._emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=f.asm.bb).apply(null,arguments)},Le=f._emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=function(){return(Le=f._emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=f.asm.cb).apply(null,arguments)},Ke=f._emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=function(){return(Ke=f._emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=f.asm.db).apply(null,arguments)},qe=f._emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5=function(){return(qe=f._emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5=f.asm.eb).apply(null,arguments)},$e=f._emscripten_bind_Decoder_SkipAttributeTransform_1=function(){return($e=f._emscripten_bind_Decoder_SkipAttributeTransform_1=f.asm.fb).apply(null,arguments)},ri=f._emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1=function(){return(ri=f._emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1=f.asm.gb).apply(null,arguments)},ei=f._emscripten_bind_Decoder_DecodeBufferToPointCloud_2=function(){return(ei=f._emscripten_bind_Decoder_DecodeBufferToPointCloud_2=f.asm.hb).apply(null,arguments)},ii=f._emscripten_bind_Decoder_DecodeBufferToMesh_2=function(){return(ii=f._emscripten_bind_Decoder_DecodeBufferToMesh_2=f.asm.ib).apply(null,arguments)},ti=f._emscripten_bind_Decoder___destroy___0=function(){return(ti=f._emscripten_bind_Decoder___destroy___0=f.asm.jb).apply(null,arguments)},fi=f._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=function(){return(fi=f._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=f.asm.kb).apply(null,arguments)},ai=f._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=function(){return(ai=f._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=f.asm.lb).apply(null,arguments)},ni=f._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=function(){return(ni=f._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=f.asm.mb).apply(null,arguments)},Ai=f._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=function(){return(Ai=f._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=f.asm.nb).apply(null,arguments)},oi=f._emscripten_enum_draco_GeometryAttribute_Type_INVALID=function(){return(oi=f._emscripten_enum_draco_GeometryAttribute_Type_INVALID=f.asm.ob).apply(null,arguments)},bi=f._emscripten_enum_draco_GeometryAttribute_Type_POSITION=function(){return(bi=f._emscripten_enum_draco_GeometryAttribute_Type_POSITION=f.asm.pb).apply(null,arguments)},ui=f._emscripten_enum_draco_GeometryAttribute_Type_NORMAL=function(){return(ui=f._emscripten_enum_draco_GeometryAttribute_Type_NORMAL=f.asm.qb).apply(null,arguments)},ci=f._emscripten_enum_draco_GeometryAttribute_Type_COLOR=function(){return(ci=f._emscripten_enum_draco_GeometryAttribute_Type_COLOR=f.asm.rb).apply(null,arguments)},ki=f._emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=function(){return(ki=f._emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=f.asm.sb).apply(null,arguments)},_i=f._emscripten_enum_draco_GeometryAttribute_Type_GENERIC=function(){return(_i=f._emscripten_enum_draco_GeometryAttribute_Type_GENERIC=f.asm.tb).apply(null,arguments)},si=f._emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=function(){return(si=f._emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=f.asm.ub).apply(null,arguments)},pi=f._emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=function(){return(pi=f._emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=f.asm.vb).apply(null,arguments)},li=f._emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=function(){return(li=f._emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=f.asm.wb).apply(null,arguments)},yi=f._emscripten_enum_draco_DataType_DT_INVALID=function(){return(yi=f._emscripten_enum_draco_DataType_DT_INVALID=f.asm.xb).apply(null,arguments)},di=f._emscripten_enum_draco_DataType_DT_INT8=function(){return(di=f._emscripten_enum_draco_DataType_DT_INT8=f.asm.yb).apply(null,arguments)},mi=f._emscripten_enum_draco_DataType_DT_UINT8=function(){return(mi=f._emscripten_enum_draco_DataType_DT_UINT8=f.asm.zb).apply(null,arguments)},vi=f._emscripten_enum_draco_DataType_DT_INT16=function(){return(vi=f._emscripten_enum_draco_DataType_DT_INT16=f.asm.Ab).apply(null,arguments)},hi=f._emscripten_enum_draco_DataType_DT_UINT16=function(){return(hi=f._emscripten_enum_draco_DataType_DT_UINT16=f.asm.Bb).apply(null,arguments)},Ri=f._emscripten_enum_draco_DataType_DT_INT32=function(){return(Ri=f._emscripten_enum_draco_DataType_DT_INT32=f.asm.Cb).apply(null,arguments)},Ni=f._emscripten_enum_draco_DataType_DT_UINT32=function(){return(Ni=f._emscripten_enum_draco_DataType_DT_UINT32=f.asm.Db).apply(null,arguments)},Ti=f._emscripten_enum_draco_DataType_DT_INT64=function(){return(Ti=f._emscripten_enum_draco_DataType_DT_INT64=f.asm.Eb).apply(null,arguments)},Vi=f._emscripten_enum_draco_DataType_DT_UINT64=function(){return(Vi=f._emscripten_enum_draco_DataType_DT_UINT64=f.asm.Fb).apply(null,arguments)},Ui=f._emscripten_enum_draco_DataType_DT_FLOAT32=function(){return(Ui=f._emscripten_enum_draco_DataType_DT_FLOAT32=f.asm.Gb).apply(null,arguments)},Wi=f._emscripten_enum_draco_DataType_DT_FLOAT64=function(){return(Wi=f._emscripten_enum_draco_DataType_DT_FLOAT64=f.asm.Hb).apply(null,arguments)},Di=f._emscripten_enum_draco_DataType_DT_BOOL=function(){return(Di=f._emscripten_enum_draco_DataType_DT_BOOL=f.asm.Ib).apply(null,arguments)},Gi=f._emscripten_enum_draco_DataType_DT_TYPES_COUNT=function(){return(Gi=f._emscripten_enum_draco_DataType_DT_TYPES_COUNT=f.asm.Jb).apply(null,arguments)},Zi=f._emscripten_enum_draco_StatusCode_OK=function(){return(Zi=f._emscripten_enum_draco_StatusCode_OK=f.asm.Kb).apply(null,arguments)},Ei=f._emscripten_enum_draco_StatusCode_DRACO_ERROR=function(){return(Ei=f._emscripten_enum_draco_StatusCode_DRACO_ERROR=f.asm.Lb).apply(null,arguments)},Fi=f._emscripten_enum_draco_StatusCode_IO_ERROR=function(){return(Fi=f._emscripten_enum_draco_StatusCode_IO_ERROR=f.asm.Mb).apply(null,arguments)},Ii=f._emscripten_enum_draco_StatusCode_INVALID_PARAMETER=function(){return(Ii=f._emscripten_enum_draco_StatusCode_INVALID_PARAMETER=f.asm.Nb).apply(null,arguments)},Yi=f._emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=function(){return(Yi=f._emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=f.asm.Ob).apply(null,arguments)},wi=f._emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=function(){return(wi=f._emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=f.asm.Pb).apply(null,arguments)},ji=(f._malloc=function(){return(f._malloc=f.asm.Qb).apply(null,arguments)},f._free=function(){return(f._free=f.asm.Rb).apply(null,arguments)},function(){return(ji=f.asm.Sb).apply(null,arguments)});f.___start_em_js=11660,f.___stop_em_js=11758;function Ji(){function r(){$||($=!0,f.calledRun=!0,R||(w=!0,P(I),i(f),f.onRuntimeInitialized&&f.onRuntimeInitialized(),function(){if(f.postRun)for("function"==typeof f.postRun&&(f.postRun=[f.postRun]);f.postRun.length;)r=f.postRun.shift(),Y.unshift(r);var r;P(Y)}()))}J>0||(!function(){if(f.preRun)for("function"==typeof f.preRun&&(f.preRun=[f.preRun]);f.preRun.length;)r=f.preRun.shift(),F.unshift(r);var r;P(F)}(),J>0||(f.setStatus?(f.setStatus("Running..."),setTimeout((function(){setTimeout((function(){f.setStatus("")}),1),r()}),1)):r()))}if(M=function r(){$||Ji(),$||(M=r)},f.preInit)for("function"==typeof f.preInit&&(f.preInit=[f.preInit]);f.preInit.length>0;)f.preInit.pop()();function Bi(){}function Mi(r){return(r||Bi).__cache__}function Qi(r,e){var i=Mi(e),t=i[r];return t||((t=Object.create((e||Bi).prototype)).ptr=r,i[r]=t)}Ji(),Bi.prototype=Object.create(Bi.prototype),Bi.prototype.constructor=Bi,Bi.prototype.__class__=Bi,Bi.__cache__={},f.WrapperObject=Bi,f.getCache=Mi,f.wrapPointer=Qi,f.castObject=function(r,e){return Qi(r.ptr,e)},f.NULL=Qi(0),f.destroy=function(r){if(!r.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";r.__destroy__(),delete Mi(r.__class__)[r.ptr]},f.compare=function(r,e){return r.ptr===e.ptr},f.getPointer=function(r){return r.ptr},f.getClass=function(r){return r.__class__};var gi={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(gi.needed){for(var r=0;r<gi.temps.length;r++)f._free(gi.temps[r]);gi.temps.length=0,f._free(gi.buffer),gi.buffer=0,gi.size+=gi.needed,gi.needed=0}gi.buffer||(gi.size+=128,gi.buffer=f._malloc(gi.size),N(gi.buffer)),gi.pos=0},alloc:function(r,e){N(gi.buffer);var i,t=e.BYTES_PER_ELEMENT,a=r.length*t;return a=a+7&-8,gi.pos+a>=gi.size?(N(a>0),gi.needed+=a,i=f._malloc(a),gi.temps.push(i)):(i=gi.buffer+gi.pos,gi.pos+=a),i},copy:function(r,e,i){switch(i>>>=0,e.BYTES_PER_ELEMENT){case 2:i>>>=1;break;case 4:i>>>=2;break;case 8:i>>>=3}for(var t=0;t<r.length;t++)e[i+t]=r[t]}};function Oi(r){if("string"==typeof r){var e=(t=r,n=a>0?a:function(r){for(var e=0,i=0;i<r.length;++i){var t=r.charCodeAt(i);t<=127?e++:t<=2047?e+=2:t>=55296&&t<=57343?(e+=4,++i):e+=3}return e}(t)+1,A=new Array(n),o=function(r,e,i,t){if(!(t>0))return 0;for(var f=i,a=i+t-1,n=0;n<r.length;++n){var A=r.charCodeAt(n);if(A>=55296&&A<=57343&&(A=65536+((1023&A)<<10)|1023&r.charCodeAt(++n)),A<=127){if(i>=a)break;e[i++]=A}else if(A<=2047){if(i+1>=a)break;e[i++]=192|A>>6,e[i++]=128|63&A}else if(A<=65535){if(i+2>=a)break;e[i++]=224|A>>12,e[i++]=128|A>>6&63,e[i++]=128|63&A}else{if(i+3>=a)break;e[i++]=240|A>>18,e[i++]=128|A>>12&63,e[i++]=128|A>>6&63,e[i++]=128|63&A}}return e[i]=0,i-f}(t,A,0,A.length),f&&(A.length=o),A),i=gi.alloc(e,T);return gi.copy(e,T,i),i}var t,f,a,n,A,o;return r}function Ci(r){if("object"==typeof r){var e=gi.alloc(r,T);return gi.copy(r,T,e),e}return r}function zi(){throw"cannot construct a VoidPtr, no constructor in IDL"}function Xi(){this.ptr=ir(),Mi(Xi)[this.ptr]=this}function Si(){this.ptr=ar(),Mi(Si)[this.ptr]=this}function Hi(){this.ptr=or(),Mi(Hi)[this.ptr]=this}function Pi(){this.ptr=ur(),Mi(Pi)[this.ptr]=this}function xi(){this.ptr=hr(),Mi(xi)[this.ptr]=this}function Li(){this.ptr=Wr(),Mi(Li)[this.ptr]=this}function Ki(){this.ptr=Er(),Mi(Ki)[this.ptr]=this}function qi(){this.ptr=wr(),Mi(qi)[this.ptr]=this}function $i(){this.ptr=Qr(),Mi($i)[this.ptr]=this}function rt(){throw"cannot construct a Status, no constructor in IDL"}function et(){this.ptr=Sr(),Mi(et)[this.ptr]=this}function it(){this.ptr=Lr(),Mi(it)[this.ptr]=this}function tt(){this.ptr=re(),Mi(tt)[this.ptr]=this}function ft(){this.ptr=fe(),Mi(ft)[this.ptr]=this}function at(){this.ptr=oe(),Mi(at)[this.ptr]=this}function nt(){this.ptr=ke(),Mi(nt)[this.ptr]=this}function At(){this.ptr=le(),Mi(At)[this.ptr]=this}function ot(){this.ptr=ve(),Mi(ot)[this.ptr]=this}function bt(){this.ptr=Ge(),Mi(bt)[this.ptr]=this}return zi.prototype=Object.create(Bi.prototype),zi.prototype.constructor=zi,zi.prototype.__class__=zi,zi.__cache__={},f.VoidPtr=zi,zi.prototype.__destroy__=zi.prototype.__destroy__=function(){var r=this.ptr;er(r)},Xi.prototype=Object.create(Bi.prototype),Xi.prototype.constructor=Xi,Xi.prototype.__class__=Xi,Xi.__cache__={},f.DecoderBuffer=Xi,Xi.prototype.Init=Xi.prototype.Init=function(r,e){var i=this.ptr;gi.prepare(),"object"==typeof r&&(r=Ci(r)),e&&"object"==typeof e&&(e=e.ptr),tr(i,r,e)},Xi.prototype.__destroy__=Xi.prototype.__destroy__=function(){var r=this.ptr;fr(r)},Si.prototype=Object.create(Bi.prototype),Si.prototype.constructor=Si,Si.prototype.__class__=Si,Si.__cache__={},f.AttributeTransformData=Si,Si.prototype.transform_type=Si.prototype.transform_type=function(){var r=this.ptr;return nr(r)},Si.prototype.__destroy__=Si.prototype.__destroy__=function(){var r=this.ptr;Ar(r)},Hi.prototype=Object.create(Bi.prototype),Hi.prototype.constructor=Hi,Hi.prototype.__class__=Hi,Hi.__cache__={},f.GeometryAttribute=Hi,Hi.prototype.__destroy__=Hi.prototype.__destroy__=function(){var r=this.ptr;br(r)},Pi.prototype=Object.create(Bi.prototype),Pi.prototype.constructor=Pi,Pi.prototype.__class__=Pi,Pi.__cache__={},f.PointAttribute=Pi,Pi.prototype.size=Pi.prototype.size=function(){var r=this.ptr;return cr(r)},Pi.prototype.GetAttributeTransformData=Pi.prototype.GetAttributeTransformData=function(){var r=this.ptr;return Qi(kr(r),Si)},Pi.prototype.attribute_type=Pi.prototype.attribute_type=function(){var r=this.ptr;return _r(r)},Pi.prototype.data_type=Pi.prototype.data_type=function(){var r=this.ptr;return sr(r)},Pi.prototype.num_components=Pi.prototype.num_components=function(){var r=this.ptr;return pr(r)},Pi.prototype.normalized=Pi.prototype.normalized=function(){var r=this.ptr;return!!lr(r)},Pi.prototype.byte_stride=Pi.prototype.byte_stride=function(){var r=this.ptr;return yr(r)},Pi.prototype.byte_offset=Pi.prototype.byte_offset=function(){var r=this.ptr;return dr(r)},Pi.prototype.unique_id=Pi.prototype.unique_id=function(){var r=this.ptr;return mr(r)},Pi.prototype.__destroy__=Pi.prototype.__destroy__=function(){var r=this.ptr;vr(r)},xi.prototype=Object.create(Bi.prototype),xi.prototype.constructor=xi,xi.prototype.__class__=xi,xi.__cache__={},f.AttributeQuantizationTransform=xi,xi.prototype.InitFromAttribute=xi.prototype.InitFromAttribute=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),!!Rr(e,r)},xi.prototype.quantization_bits=xi.prototype.quantization_bits=function(){var r=this.ptr;return Nr(r)},xi.prototype.min_value=xi.prototype.min_value=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),Tr(e,r)},xi.prototype.range=xi.prototype.range=function(){var r=this.ptr;return Vr(r)},xi.prototype.__destroy__=xi.prototype.__destroy__=function(){var r=this.ptr;Ur(r)},Li.prototype=Object.create(Bi.prototype),Li.prototype.constructor=Li,Li.prototype.__class__=Li,Li.__cache__={},f.AttributeOctahedronTransform=Li,Li.prototype.InitFromAttribute=Li.prototype.InitFromAttribute=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),!!Dr(e,r)},Li.prototype.quantization_bits=Li.prototype.quantization_bits=function(){var r=this.ptr;return Gr(r)},Li.prototype.__destroy__=Li.prototype.__destroy__=function(){var r=this.ptr;Zr(r)},Ki.prototype=Object.create(Bi.prototype),Ki.prototype.constructor=Ki,Ki.prototype.__class__=Ki,Ki.__cache__={},f.PointCloud=Ki,Ki.prototype.num_attributes=Ki.prototype.num_attributes=function(){var r=this.ptr;return Fr(r)},Ki.prototype.num_points=Ki.prototype.num_points=function(){var r=this.ptr;return Ir(r)},Ki.prototype.__destroy__=Ki.prototype.__destroy__=function(){var r=this.ptr;Yr(r)},qi.prototype=Object.create(Bi.prototype),qi.prototype.constructor=qi,qi.prototype.__class__=qi,qi.__cache__={},f.Mesh=qi,qi.prototype.num_faces=qi.prototype.num_faces=function(){var r=this.ptr;return jr(r)},qi.prototype.num_attributes=qi.prototype.num_attributes=function(){var r=this.ptr;return Jr(r)},qi.prototype.num_points=qi.prototype.num_points=function(){var r=this.ptr;return Br(r)},qi.prototype.__destroy__=qi.prototype.__destroy__=function(){var r=this.ptr;Mr(r)},$i.prototype=Object.create(Bi.prototype),$i.prototype.constructor=$i,$i.prototype.__class__=$i,$i.__cache__={},f.Metadata=$i,$i.prototype.__destroy__=$i.prototype.__destroy__=function(){var r=this.ptr;gr(r)},rt.prototype=Object.create(Bi.prototype),rt.prototype.constructor=rt,rt.prototype.__class__=rt,rt.__cache__={},f.Status=rt,rt.prototype.code=rt.prototype.code=function(){var r=this.ptr;return Or(r)},rt.prototype.ok=rt.prototype.ok=function(){var r=this.ptr;return!!Cr(r)},rt.prototype.error_msg=rt.prototype.error_msg=function(){var r=this.ptr;return G(zr(r))},rt.prototype.__destroy__=rt.prototype.__destroy__=function(){var r=this.ptr;Xr(r)},et.prototype=Object.create(Bi.prototype),et.prototype.constructor=et,et.prototype.__class__=et,et.__cache__={},f.DracoFloat32Array=et,et.prototype.GetValue=et.prototype.GetValue=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),Hr(e,r)},et.prototype.size=et.prototype.size=function(){var r=this.ptr;return Pr(r)},et.prototype.__destroy__=et.prototype.__destroy__=function(){var r=this.ptr;xr(r)},it.prototype=Object.create(Bi.prototype),it.prototype.constructor=it,it.prototype.__class__=it,it.__cache__={},f.DracoInt8Array=it,it.prototype.GetValue=it.prototype.GetValue=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),Kr(e,r)},it.prototype.size=it.prototype.size=function(){var r=this.ptr;return qr(r)},it.prototype.__destroy__=it.prototype.__destroy__=function(){var r=this.ptr;$r(r)},tt.prototype=Object.create(Bi.prototype),tt.prototype.constructor=tt,tt.prototype.__class__=tt,tt.__cache__={},f.DracoUInt8Array=tt,tt.prototype.GetValue=tt.prototype.GetValue=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),ee(e,r)},tt.prototype.size=tt.prototype.size=function(){var r=this.ptr;return ie(r)},tt.prototype.__destroy__=tt.prototype.__destroy__=function(){var r=this.ptr;te(r)},ft.prototype=Object.create(Bi.prototype),ft.prototype.constructor=ft,ft.prototype.__class__=ft,ft.__cache__={},f.DracoInt16Array=ft,ft.prototype.GetValue=ft.prototype.GetValue=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),ae(e,r)},ft.prototype.size=ft.prototype.size=function(){var r=this.ptr;return ne(r)},ft.prototype.__destroy__=ft.prototype.__destroy__=function(){var r=this.ptr;Ae(r)},at.prototype=Object.create(Bi.prototype),at.prototype.constructor=at,at.prototype.__class__=at,at.__cache__={},f.DracoUInt16Array=at,at.prototype.GetValue=at.prototype.GetValue=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),be(e,r)},at.prototype.size=at.prototype.size=function(){var r=this.ptr;return ue(r)},at.prototype.__destroy__=at.prototype.__destroy__=function(){var r=this.ptr;ce(r)},nt.prototype=Object.create(Bi.prototype),nt.prototype.constructor=nt,nt.prototype.__class__=nt,nt.__cache__={},f.DracoInt32Array=nt,nt.prototype.GetValue=nt.prototype.GetValue=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),_e(e,r)},nt.prototype.size=nt.prototype.size=function(){var r=this.ptr;return se(r)},nt.prototype.__destroy__=nt.prototype.__destroy__=function(){var r=this.ptr;pe(r)},At.prototype=Object.create(Bi.prototype),At.prototype.constructor=At,At.prototype.__class__=At,At.__cache__={},f.DracoUInt32Array=At,At.prototype.GetValue=At.prototype.GetValue=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),ye(e,r)},At.prototype.size=At.prototype.size=function(){var r=this.ptr;return de(r)},At.prototype.__destroy__=At.prototype.__destroy__=function(){var r=this.ptr;me(r)},ot.prototype=Object.create(Bi.prototype),ot.prototype.constructor=ot,ot.prototype.__class__=ot,ot.__cache__={},f.MetadataQuerier=ot,ot.prototype.HasEntry=ot.prototype.HasEntry=function(r,e){var i=this.ptr;return gi.prepare(),r&&"object"==typeof r&&(r=r.ptr),e=e&&"object"==typeof e?e.ptr:Oi(e),!!he(i,r,e)},ot.prototype.GetIntEntry=ot.prototype.GetIntEntry=function(r,e){var i=this.ptr;return gi.prepare(),r&&"object"==typeof r&&(r=r.ptr),e=e&&"object"==typeof e?e.ptr:Oi(e),Re(i,r,e)},ot.prototype.GetIntEntryArray=ot.prototype.GetIntEntryArray=function(r,e,i){var t=this.ptr;gi.prepare(),r&&"object"==typeof r&&(r=r.ptr),e=e&&"object"==typeof e?e.ptr:Oi(e),i&&"object"==typeof i&&(i=i.ptr),Ne(t,r,e,i)},ot.prototype.GetDoubleEntry=ot.prototype.GetDoubleEntry=function(r,e){var i=this.ptr;return gi.prepare(),r&&"object"==typeof r&&(r=r.ptr),e=e&&"object"==typeof e?e.ptr:Oi(e),Te(i,r,e)},ot.prototype.GetStringEntry=ot.prototype.GetStringEntry=function(r,e){var i=this.ptr;return gi.prepare(),r&&"object"==typeof r&&(r=r.ptr),e=e&&"object"==typeof e?e.ptr:Oi(e),G(Ve(i,r,e))},ot.prototype.NumEntries=ot.prototype.NumEntries=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),Ue(e,r)},ot.prototype.GetEntryName=ot.prototype.GetEntryName=function(r,e){var i=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),G(We(i,r,e))},ot.prototype.__destroy__=ot.prototype.__destroy__=function(){var r=this.ptr;De(r)},bt.prototype=Object.create(Bi.prototype),bt.prototype.constructor=bt,bt.prototype.__class__=bt,bt.__cache__={},f.Decoder=bt,bt.prototype.DecodeArrayToPointCloud=bt.prototype.DecodeArrayToPointCloud=function(r,e,i){var t=this.ptr;return gi.prepare(),"object"==typeof r&&(r=Ci(r)),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),Qi(Ze(t,r,e,i),rt)},bt.prototype.DecodeArrayToMesh=bt.prototype.DecodeArrayToMesh=function(r,e,i){var t=this.ptr;return gi.prepare(),"object"==typeof r&&(r=Ci(r)),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),Qi(Ee(t,r,e,i),rt)},bt.prototype.GetAttributeId=bt.prototype.GetAttributeId=function(r,e){var i=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),Fe(i,r,e)},bt.prototype.GetAttributeIdByName=bt.prototype.GetAttributeIdByName=function(r,e){var i=this.ptr;return gi.prepare(),r&&"object"==typeof r&&(r=r.ptr),e=e&&"object"==typeof e?e.ptr:Oi(e),Ie(i,r,e)},bt.prototype.GetAttributeIdByMetadataEntry=bt.prototype.GetAttributeIdByMetadataEntry=function(r,e,i){var t=this.ptr;return gi.prepare(),r&&"object"==typeof r&&(r=r.ptr),e=e&&"object"==typeof e?e.ptr:Oi(e),i=i&&"object"==typeof i?i.ptr:Oi(i),Ye(t,r,e,i)},bt.prototype.GetAttribute=bt.prototype.GetAttribute=function(r,e){var i=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),Qi(we(i,r,e),Pi)},bt.prototype.GetAttributeByUniqueId=bt.prototype.GetAttributeByUniqueId=function(r,e){var i=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),Qi(je(i,r,e),Pi)},bt.prototype.GetMetadata=bt.prototype.GetMetadata=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),Qi(Je(e,r),$i)},bt.prototype.GetAttributeMetadata=bt.prototype.GetAttributeMetadata=function(r,e){var i=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),Qi(Be(i,r,e),$i)},bt.prototype.GetFaceFromMesh=bt.prototype.GetFaceFromMesh=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!Me(t,r,e,i)},bt.prototype.GetTriangleStripsFromMesh=bt.prototype.GetTriangleStripsFromMesh=function(r,e){var i=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),Qe(i,r,e)},bt.prototype.GetTrianglesUInt16Array=bt.prototype.GetTrianglesUInt16Array=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!ge(t,r,e,i)},bt.prototype.GetTrianglesUInt32Array=bt.prototype.GetTrianglesUInt32Array=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!Oe(t,r,e,i)},bt.prototype.GetAttributeFloat=bt.prototype.GetAttributeFloat=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!Ce(t,r,e,i)},bt.prototype.GetAttributeFloatForAllPoints=bt.prototype.GetAttributeFloatForAllPoints=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!ze(t,r,e,i)},bt.prototype.GetAttributeIntForAllPoints=bt.prototype.GetAttributeIntForAllPoints=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!Xe(t,r,e,i)},bt.prototype.GetAttributeInt8ForAllPoints=bt.prototype.GetAttributeInt8ForAllPoints=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!Se(t,r,e,i)},bt.prototype.GetAttributeUInt8ForAllPoints=bt.prototype.GetAttributeUInt8ForAllPoints=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!He(t,r,e,i)},bt.prototype.GetAttributeInt16ForAllPoints=bt.prototype.GetAttributeInt16ForAllPoints=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!Pe(t,r,e,i)},bt.prototype.GetAttributeUInt16ForAllPoints=bt.prototype.GetAttributeUInt16ForAllPoints=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!xe(t,r,e,i)},bt.prototype.GetAttributeInt32ForAllPoints=bt.prototype.GetAttributeInt32ForAllPoints=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!Le(t,r,e,i)},bt.prototype.GetAttributeUInt32ForAllPoints=bt.prototype.GetAttributeUInt32ForAllPoints=function(r,e,i){var t=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),!!Ke(t,r,e,i)},bt.prototype.GetAttributeDataArrayForAllPoints=bt.prototype.GetAttributeDataArrayForAllPoints=function(r,e,i,t,f){var a=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),i&&"object"==typeof i&&(i=i.ptr),t&&"object"==typeof t&&(t=t.ptr),f&&"object"==typeof f&&(f=f.ptr),!!qe(a,r,e,i,t,f)},bt.prototype.SkipAttributeTransform=bt.prototype.SkipAttributeTransform=function(r){var e=this.ptr;r&&"object"==typeof r&&(r=r.ptr),$e(e,r)},bt.prototype.GetEncodedGeometryType_Deprecated=bt.prototype.GetEncodedGeometryType_Deprecated=function(r){var e=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),ri(e,r)},bt.prototype.DecodeBufferToPointCloud=bt.prototype.DecodeBufferToPointCloud=function(r,e){var i=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),Qi(ei(i,r,e),rt)},bt.prototype.DecodeBufferToMesh=bt.prototype.DecodeBufferToMesh=function(r,e){var i=this.ptr;return r&&"object"==typeof r&&(r=r.ptr),e&&"object"==typeof e&&(e=e.ptr),Qi(ii(i,r,e),rt)},bt.prototype.__destroy__=bt.prototype.__destroy__=function(){var r=this.ptr;ti(r)},function(){function r(){f.ATTRIBUTE_INVALID_TRANSFORM=fi(),f.ATTRIBUTE_NO_TRANSFORM=ai(),f.ATTRIBUTE_QUANTIZATION_TRANSFORM=ni(),f.ATTRIBUTE_OCTAHEDRON_TRANSFORM=Ai(),f.INVALID=oi(),f.POSITION=bi(),f.NORMAL=ui(),f.COLOR=ci(),f.TEX_COORD=ki(),f.GENERIC=_i(),f.INVALID_GEOMETRY_TYPE=si(),f.POINT_CLOUD=pi(),f.TRIANGULAR_MESH=li(),f.DT_INVALID=yi(),f.DT_INT8=di(),f.DT_UINT8=mi(),f.DT_INT16=vi(),f.DT_UINT16=hi(),f.DT_INT32=Ri(),f.DT_UINT32=Ni(),f.DT_INT64=Ti(),f.DT_UINT64=Vi(),f.DT_FLOAT32=Ui(),f.DT_FLOAT64=Wi(),f.DT_BOOL=Di(),f.DT_TYPES_COUNT=Gi(),f.OK=Zi(),f.DRACO_ERROR=Ei(),f.IO_ERROR=Fi(),f.INVALID_PARAMETER=Ii(),f.UNSUPPORTED_VERSION=Yi(),f.UNKNOWN_VERSION=wi()}w?r():j(r)}(),"function"==typeof f.onModuleParsed&&f.onModuleParsed(),f.Decoder.prototype.GetEncodedGeometryType=function(r){if(r.__class__&&r.__class__===f.DecoderBuffer)return f.Decoder.prototype.GetEncodedGeometryType_Deprecated(r);if(r.byteLength<8)return f.INVALID_GEOMETRY_TYPE;switch(r[7]){case 0:return f.POINT_CLOUD;case 1:return f.TRIANGULAR_MESH;default:return f.INVALID_GEOMETRY_TYPE}},e.ready}})();"object"==typeof exports&&"object"==typeof module?module.exports=DracoDecoderModule:"function"==typeof define&&define.amd?define([],(function(){return DracoDecoderModule})):"object"==typeof exports&&(exports.DracoDecoderModule=DracoDecoderModule);