diff --git a/webgl/transcoder/CMakeLists.txt b/webgl/transcoder/CMakeLists.txt
index 372653d..f1829d8 100644
--- a/webgl/transcoder/CMakeLists.txt
+++ b/webgl/transcoder/CMakeLists.txt
@@ -48,5 +48,5 @@ if (EMSCRIPTEN)
   set_target_properties(basis_transcoder.js PROPERTIES
       OUTPUT_NAME "basis_transcoder"
       SUFFIX ".js"
-      LINK_FLAGS "--bind -s ALLOW_MEMORY_GROWTH=1 -O3 -s ASSERTIONS=0 -s MALLOC=emmalloc -s MODULARIZE=1 -s EXPORT_NAME=BASIS ")
+      LINK_FLAGS "--bind -s ALLOW_MEMORY_GROWTH=1 -O3 -s ASSERTIONS=0 -s MALLOC=emmalloc -s MODULARIZE=1 -s EXPORT_NAME=BASIS -s NO_DYNAMIC_EXECUTION=1")
 endif()
